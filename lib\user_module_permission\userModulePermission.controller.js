const { PUBLISHED, SURVEY_CREATOR_ROLE_NAME } = require('../utility/constants');
const {
    USER_PERMISSION_MODULE_NAMES: { ANNOUNCEMENT_MANAGEMENT, SURVEY },
} = require('../utility/util_keys');
const roleSchema = require('../models/role');
const roleAssignSchema = require('../models/role_assign');
const userModulePermissionSchema = require('./userModulePermission.model');
const programCalendarSchema = require('../models/program_calendar');
const moduleSchema = require('../models/module');
const announcementUserSettingSchema = require('../announcement/announcement_user_setting_model');
const { convertToMongoObjectId } = require('../utility/common');
const { getUserPermissionModuleList } = require('../utility/utility.service');
exports.getUserPermissionModuleList = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const modules = await getUserPermissionModuleList();
        const userPermissionModuleList = await moduleSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    name: { $in: modules },
                },
                {
                    name: 1,
                    'pages.name': 1,
                    'pages.actions.name': 1,
                    'pages.tabs.name': 1,
                    'pages.tabs.actions.name': 1,
                    'pages.tabs.subTabs.name': 1,
                    'pages.tabs.subTabs.actions.name': 1,
                },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'Module List',
            data: userPermissionModuleList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getModuleUsers = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { moduleName } = query;
        const findQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            'modules.name': moduleName,
            ...(moduleName === SURVEY && { name: { $ne: SURVEY_CREATOR_ROLE_NAME } }),
        };
        const rolesTaggedForCurrentModule = await roleSchema
            .find(findQuery, {
                _id: 1,
            })
            .lean();
        if (!rolesTaggedForCurrentModule || !rolesTaggedForCurrentModule.length) {
            return { statusCode: 404, message: 'No Roles Tagged For This Selected Module' };
        }
        const usersTaggedForCurrentRoles = await roleAssignSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'roles._role_id': [...rolesTaggedForCurrentModule],
                },
                {
                    _user_id: 1,
                    'roles._role_id.$': 1,
                },
            )
            .lean()
            .populate({ path: '_user_id', select: { name: 1, user_id: 1 } })
            .populate({ path: 'roles._role_id', select: { name: 1, _id: 0 } });
        if (!usersTaggedForCurrentRoles || !usersTaggedForCurrentRoles.length) {
            return {
                statusCode: 404,
                message: 'No Users Tagged In The Role For This Selected Module ',
            };
        }
        return { statusCode: 200, message: 'Users List', data: usersTaggedForCurrentRoles };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserModuleExistingPermissions = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { userId, moduleName } = query;
        let userExistingModulePermissions = [];
        if (moduleName === ANNOUNCEMENT_MANAGEMENT) {
            userExistingModulePermissions = await announcementUserSettingSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        userId: convertToMongoObjectId(userId),
                    },
                    {
                        selectedCourses: 1,
                        selectedPrograms: 1,
                    },
                )
                .populate([
                    { path: 'selectedCourses._program_id', select: { name: 1, code: 1 } },
                    {
                        path: 'selectedCourses._institution_calendar_id',
                        select: { calendar_name: 1 },
                    },
                    { path: 'selectedPrograms', select: { name: 1, code: 1 } },
                ])
                .sort({ _id: -1 })
                .lean();
        } else {
            userExistingModulePermissions = await userModulePermissionSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        userId: convertToMongoObjectId(userId),
                        moduleName,
                    },
                    {
                        selectedCourses: 1,
                        selectedPrograms: 1,
                    },
                )
                .populate([
                    { path: 'selectedCourses._program_id', select: { name: 1, code: 1 } },
                    {
                        path: 'selectedCourses._institution_calendar_id',
                        select: { calendar_name: 1 },
                    },
                    { path: 'selectedPrograms', select: { name: 1, code: 1 } },
                ])
                .sort({ _id: -1 })
                .lean();
        }
        if (!userExistingModulePermissions) {
            return {
                statusCode: 200,
                message: `User Has No Existing ${moduleName} Module Permissions`,
                data: { selectedCourses: [], selectedPrograms: [] },
            };
        }
        return {
            statusCode: 200,
            message: `User Existing ${moduleName} Module Permissions`,
            data: userExistingModulePermissions,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createUserModulePermissions = async ({ headers = {}, body = {} }) => {
    try {
        const { selectedUserProgram = [], manageUser, moduleName = '' } = body;
        const { _institution_id } = headers;
        const userModulePermissionBulkWrite = selectedUserProgram.map(
            (selectedUserProgramElement) => {
                const {
                    type = 'all',
                    userId = '',
                    selectedCourses = [],
                    selectedPrograms = [],
                } = selectedUserProgramElement;
                return {
                    updateOne: {
                        filter: {
                            _institution_id: convertToMongoObjectId(_institution_id),
                            ...(moduleName !== ANNOUNCEMENT_MANAGEMENT && { moduleName }),
                            userId: convertToMongoObjectId(userId),
                        },
                        update: {
                            $set: {
                                ...(moduleName !== ANNOUNCEMENT_MANAGEMENT && { moduleName }),
                                manageUser: convertToMongoObjectId(manageUser),
                                _institution_id: convertToMongoObjectId(_institution_id),
                                userId: convertToMongoObjectId(userId),
                                selectedPrograms,
                                ...(type && type === 'mixed', { selectedCourses }),
                            },
                        },
                        upsert: true,
                    },
                };
            },
        );
        if (userModulePermissionBulkWrite.length) {
            let createdUserModulePermissions = {};
            if (moduleName === ANNOUNCEMENT_MANAGEMENT) {
                createdUserModulePermissions = await announcementUserSettingSchema.bulkWrite(
                    userModulePermissionBulkWrite,
                );
            } else {
                createdUserModulePermissions = await userModulePermissionSchema.bulkWrite(
                    userModulePermissionBulkWrite,
                );
            }

            if (
                createdUserModulePermissions &&
                (createdUserModulePermissions.modifiedCount ||
                    createdUserModulePermissions.upsertedCount)
            ) {
                return {
                    statusCode: 201,
                    message: `${moduleName} Module Permissions Created Successfully`,
                };
            }
        }
        return { status: 400, message: `Unable To Create ${moduleName} Module Permissions` };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getSingleProgramCalendarDetails = async ({ params = {} }) => {
    try {
        const { programId } = params;
        const singleProgramCalendarDetails = await programCalendarSchema
            .find(
                {
                    _program_id: convertToMongoObjectId(programId),
                    status: PUBLISHED,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _institution_calendar_id: 1,
                    'level.curriculum': 1,
                    'level.course._id': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course._course_id': 1,
                },
            )
            .sort({ _id: -1 })
            .populate({
                path: '_institution_calendar_id',
                select: { calendar_name: 1 },
            })
            .lean();
        return {
            statusCode: 200,
            message: 'Single Program Calendar Details',
            data:
                singleProgramCalendarDetails && singleProgramCalendarDetails.length
                    ? singleProgramCalendarDetails
                    : [],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getSingleProgramCourseDetails = async ({ query = {} }) => {
    try {
        const { programId, institutionCalendarId } = query;
        const singleProgramCourseDetails = await programCalendarSchema
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    status: PUBLISHED,
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'level.curriculum': 1,
                    'level.course._id': 1,
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.rotation_course.rotation_count': 1,
                    'level.rotation_course.course.courses_name': 1,
                    'level.rotation_course.course.courses_number': 1,
                    'level.rotation_course.course._course_id': 1,
                },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'Single Program Course Details',
            data:
                singleProgramCourseDetails && singleProgramCourseDetails.level
                    ? singleProgramCourseDetails.level
                    : [],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
