const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const keys = require('./util_keys');
const { ensureDir } = require('fs-extra');
const { logger } = require('./util_keys');
const { commonMulterUpload } = require('../../commonService/utility/file-upload');

const maxFileSize = keys.MAX_FILE_SIZE;
const s3 = new AWS.S3({
    accessKeyId: keys.AWS_ACCESS_KEY,
    secretAccessKey: keys.AWS_SECRET_KEY,
});

const s3Oci = new AWS.S3({
    region: keys.OCI_REGION,
    accessKeyId: keys.OCI_ACCESS_KEY_ID,
    secretAccessKey: keys.OCI_SECRET_ACCESS_KEY,
    endpoint: keys.OCI_AWS_S3_API,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
});
const s3Config = keys.DIGIVAL_CLOUD_PROVIDER === 'OCI' ? s3Oci : s3;

const s3FileURL = ({ filePath, fileName }) => {
    return keys.DIGIVAL_CLOUD_PROVIDER === 'OCI'
        ? `${keys.OCI_AWS_S3_API}/${filePath}/${fileName}`
        : `https://s3-${keys.AWS_REGION}.amazonaws.com/${filePath}/${fileName}`;
};

const uploadfile = commonMulterUpload({
    bucketName: keys.BUCKET_NAME_DOC,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const uploadfile3 = commonMulterUpload({
    bucketName: keys.BUCKET_NAME_USER_DATA,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const uploadfile2 = commonMulterUpload({
    bucketName: keys.BUCKET_NAME_DOC,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const uploadfile5 = commonMulterUpload({
    bucketName: keys.BUCKET_DOCUMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const uploadDocumentFile = commonMulterUpload({
    bucketName: keys.BUCKET_NAME_DOC,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
    folderPath: 'digiclass',
});

const scheduleUploadDocument = commonMulterUpload({
    bucketName: keys.BUCKET_DOCUMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const multipleFileUpload = commonMulterUpload({
    bucketName: keys.BUCKET_DOCUMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 2,
}).array('file', 5);

const multipleFileUploadForUserRegistration = commonMulterUpload({
    bucketName: keys.BUCKET_NAME_USER_DATA,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const uploadSessionDocumentFile = commonMulterUpload({
    bucketName: keys.BUCKET_SESSION_ORDER_DOCUMENTS,
    fileSizeLimit: maxFileSize,
    maxFiles: 1,
});

const multipleFileUploadWithRestrictions = commonMulterUpload({
    bucketName: keys.BUCKET_DOCUMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 2,
    urlFieldName: 'urls',
}).array('file', 5);

const qapcMultipleFileUpload = commonMulterUpload({
    bucketName: keys.BUCKET_QAPC_DOCUMENT,
    fileSizeLimit: maxFileSize,
    maxFiles: 2,
    urlFieldName: 'urls',
    storeOriginalName: true,
}).array('file', 5);

/**
 * Local Image storing
 */
const storage = multer.diskStorage({
    destination(req, file, cb) {
        const dir = `public/facial/images/${req.body.email}`;
        ensureDir(dir)
            .then(() => cb(null, dir))
            .catch((err) => {
                logger.error(err, 'multerConfig -> storage -> destination:');
                cb(null, dir);
            });
        // cb(null, 'public/facial/images');
    },
    filename(req, file, cb) {
        cb(null, file.originalname);
    },
});

// const path = require("path");
const uploadfile4 = multer({
    storage,
    limits: {
        fileSize: 1024 * 1024 * 40, // limit size for files
    },
    fileFilter(req, file, cb) {
        // var filetypes = /png/;
        // var mimetype = filetypes.test(file.mimetype);
        // var extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        // if (mimetype && extname) {
        const fileName = file.originalname;
        req.body[file.fieldname] = fileName;
        console.log(file.fieldname, ' ', file.originalname);
        return cb(null, true);
        // }
        // cb("Error: Supported filetypes - " + filetypes);
    },
});

exports.remove = async (bucket, file_name) => {
    const params = { Bucket: bucket, Key: file_name };
    console.log(params);
    s3Config.deleteObject(params, function (err, data) {
        if (err) {
            console.log(err);
        } else {
            console.log('Removed');
        }
    });
};

exports.moveFile = async (oldName, newName, bucketUrls) => {
    console.log('move called', oldName, newName);
    // const BUCKET_NAME = keys.BUCKET_DOCUMENT;
    // Copy the object to a new location
    s3Config
        .copyObject({
            Bucket: bucketUrls,
            CopySource: `${bucketUrls}${oldName}`,
            Key: newName,
        })
        .promise()
        .then(() => {
            console.log('New File Name ', newName);
        })
        // Error handling is left up to reader
        .catch((e) => console.error(e));
};

const uploadOutsideFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_OUTSIDE_CAMPUS,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, `outside/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_OUTSIDE_CAMPUS,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

//handout
const handoutMultipleFileUpload = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.BUCKET_HANDOUT_DOCUMENT, // bucket path
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.BUCKET_HANDOUT_DOCUMENT,
                fileName,
            });
            if (!req.body.urls) {
                req.body.urls = [{ url: fileName, name: originalname }];
            } else {
                req.body.urls.push({ url: fileName, name: originalname });
            }
        },
    }),
    fileFilter: (req, file, cb) => {
        const allowedMimes = [
            'image/jpg',
            'image/jpeg',
            'image/png',
            'video/x-flv',
            'video/mp4',
            'application/pdf',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
        ];
        if (allowedMimes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
}).array('file', Infinity);

exports.uploadfile5 = uploadfile5;
exports.uploadfile4 = uploadfile4;
exports.uploadfile3 = uploadfile3;
exports.uploadfile2 = uploadfile2;
exports.uploadfile = uploadfile;
exports.uploadDocumentFile = uploadDocumentFile;
exports.scheduleUploadDocument = scheduleUploadDocument;
exports.uploadOutsideFile = uploadOutsideFile;
exports.multipleFileUpload = multipleFileUpload;
exports.qapcMultipleFileUpload = qapcMultipleFileUpload;
exports.handoutMultipleFileUpload = handoutMultipleFileUpload;
exports.s3FileURL = s3FileURL;
exports.uploadSessionDocumentFile = uploadSessionDocumentFile;
exports.multipleFileUploadForUserRegistration = multipleFileUploadForUserRegistration;
exports.multipleFileUploadWithRestrictions = multipleFileUploadWithRestrictions;
