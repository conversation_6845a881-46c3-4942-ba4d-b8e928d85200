const UserModel = require('../../../lib/models/user');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const ProgramModel = require('../../../lib/models/digi_programs');
const CourseModel = require('../../../lib/models/digi_course');
const PortfolioModel = require('../portfolio/portfolio.model');

const { convertToMongoObjectId } = require('../../common/utils/common.util');
const {
    getUserCourseLists,
    getStudentListFromStudentGroup,
} = require('../../../lib/digi_class/course_session/course_session_service');
const { STAFF, MALE, FEMALE, MALE_LABEL, PROGRAM } = require('../../common/utils/constants');
const { PUBLISHED, LB } = require('../../common/utils/enums');

const getListOfCoursesByUser = async ({ userId, type, institutionCalendarId }) => {
    const courseLists = await getUserCourseLists({ userId, type, institutionCalendarId });

    return courseLists;
};

const getStudentGroupsByCourse = async ({
    programId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    courseId,
    institutionCalendarId,
    institutionId,
}) => {
    const studentGroups = await getStudentListFromStudentGroup({
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
        institutionId,
    });

    const {
        femaleStudentCount = 0,
        maleStudentCount = 0,
        masterGroup: groups = [],
        sgStudentList: students = [],
        totalStudentCount = 0,
    } = studentGroups;

    if (students.length) {
        const inActiveUsers = await UserModel.find(
            {
                _id: {
                    $in: students.map(({ _student_id }) => convertToMongoObjectId(_student_id)),
                },
                isActive: false,
            },
            { _id: 1 },
        ).lean();
        if (inActiveUsers.length) {
            const inActiveSet = new Set(inActiveUsers.map((user) => user._id.toString()));

            for (const student of students) {
                if (inActiveSet.has(student._student_id.toString())) {
                    student.isActive = false;
                }
            }
        }
    }

    return {
        count: { male: maleStudentCount, female: femaleStudentCount, total: totalStudentCount },
        groups,
        students,
    };
};

const getFacultyListByCourse = async ({
    programId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    courseId,
    institutionCalendarId,
    institutionId,
}) => {
    const faculties = await CourseScheduleModel.find(
        {
            ...(institutionId && { institutionId }),
            _institution_calendar_id: institutionCalendarId,
            _program_id: programId,
            _course_id: courseId,
            ...(year && { year_no: year }),
            ...(level && { level_no: level }),
            ...(term && { term }),
            ...(rotation && { rotation }),
            ...(rotationCount && { rotation_count: rotationCount }),
        },
        { 'staffs._staff_id': 1 },
    ).lean();

    if (faculties.length) {
        const staffIds = faculties.reduce((acc, { staffs }) => {
            if (staffs.length) {
                acc.push(...staffs.map(({ _staff_id }) => convertToMongoObjectId(_staff_id)));
            }
            return acc;
        }, []);

        const users = await UserModel.find(
            {
                _id: { $in: staffIds },
                user_type: STAFF,
            },
            { _id: 1, name: 1, employeeId: '$user_id', gender: 1, email: 1 },
        ).lean();

        const formattedUsers = users.map(({ name, _id, employeeId, gender, email }) => {
            return {
                userId: _id,
                name,
                employeeId,
                gender: gender === MALE_LABEL ? MALE : FEMALE,
                email,
            };
        });

        return formattedUsers;
    }

    return faculties;
};

const getPrograms = async () => {
    const programs = await ProgramModel.find(
        {
            program_type: PROGRAM,
            isActive: true,
            isDeleted: false,
        },
        { name: 1, code: 1 },
    ).lean();

    return programs;
};

const getCoursesByProgram = async ({ programId }) => {
    const courses = await CourseModel.find(
        {
            _program_id: programId,
            isActive: true,
            isDeleted: false,
        },
        { name: '$course_name', code: '$course_code', type: '$course_type' },
    ).lean();

    return courses;
};

const getDeliveryTypesByCourse = async ({ courseId }) => {
    const course = await CourseModel.findOne(
        { _id: courseId },
        { 'credit_hours._session_id': 1, 'credit_hours.delivery_type': 1 },
    ).lean();
    if (course?.credit_hours?.length) {
        const deliveryTypes = [];

        course.credit_hours.forEach(({ _session_id, delivery_type = [] }) => {
            delivery_type.forEach((deliveryType) => {
                deliveryTypes.push({
                    sessionId: _session_id,
                    deliveryTypeId: deliveryType?._delivery_id,
                    deliveryTypeName: deliveryType?.delivery_type,
                    deliveryTypeSymbol: deliveryType?.delivery_symbol,
                });
            });
        });

        return deliveryTypes;
    }

    return [];
};

const getPortfolio = async ({
    programId,
    courseId,
    institutionCalendarId,
    term,
    year,
    level,
    rotation,
    rotationCount,
}) => {
    const portfolio = await PortfolioModel.findOne(
        {
            programId,
            courseId,
            institutionCalendarId,
            status: PUBLISHED,
            term,
            year,
            level,
            ...(rotation && { rotation }),
            ...(rotationCount && { rotationCount }),
        },
        {
            'components.name': 1,
            'components.code': 1,
            'components._id': 1,
            'components.children.name': 1,
            'components.children._id': 1,
        },
    ).lean();

    if (!portfolio) {
        return { components: [] };
    }

    portfolio.components = portfolio.components.filter((component) => {
        return component.code !== LB;
    });

    return portfolio;
};

module.exports = {
    getListOfCoursesByUser,
    getStudentGroupsByCourse,
    getFacultyListByCourse,
    getPrograms,
    getCoursesByProgram,
    getDeliveryTypesByCourse,
    getPortfolio,
};
