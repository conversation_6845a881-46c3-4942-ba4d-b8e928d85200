const express = require('express');
const router = express.Router();
const {
    registerGuestUser,
    getAllGuestUsers,
    getGuestUserById,
    updateGuestUserById,
    deleteGuestUserById,
    loginGuestUser,
    sendEmailToGuestUser,
} = require('./guestUserRegister.controller');
const catchAsync = require('../utility/catch-async');
const {
    getGuestUserRegisterValidate,
    getAllGuestUsersValidate,
    getGuestUserByIdValidate,
    updateGuestUserByIdValidate,
    deleteGuestUserByIdValidate,
    loginGuestUserValidate,
    sendEmailToGuestUserValidate,
} = require('./guestUserRegister.validate');
const { validate } = require('../../middleware/validation');

router.post('/user', validate(getGuestUserRegisterValidate), catchAsync(registerGuestUser));
router.get('/all-users', validate(getAllGuestUsersValidate), catchAsync(getAllGuestUsers));
router.get('/user', validate(getGuestUserByIdValidate), catchAsync(getGuestUserById));
router.put('/user', validate(updateGuestUserByIdValidate), catchAsync(updateGuestUserById));
router.delete('/user', validate(deleteGuestUserByIdValidate), catchAsync(deleteGuestUserById));
router.post(
    '/user/send-mail',
    validate(sendEmailToGuestUserValidate),
    catchAsync(sendEmailToGuestUser),
);

module.exports = router;
