const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();

exports.manageTagMasterSettingsValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            type: Joi.string()
                .valid('tags', 'groups', 'families')
                .error(() => 'INVALID TYPE'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.updatedTagMasterSettingsValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            updateId: objectId.error(() => 'UPDATE ID REQUIRED'),
            type: Joi.string()
                .valid('tags', 'groups', 'families')
                .error(() => 'INVALID TYPE'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.deleteTagMasterSettingsValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            tagGroupId: objectId.error(() => 'DELETE ID REQUIRED'),
            type: Joi.string()
                .valid('tags', 'groups', 'families')
                .required()
                .error(() => 'INVALID TYPE'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.updatedOverAllTagMasterSettingsValidator = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            _id: objectId.error(() => '_ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
