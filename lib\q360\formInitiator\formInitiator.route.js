const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    getCreateForm,
    createNewForm,
    updateCreateNewForm,
    singleFormList,
    formAttachment,
    getFormAttachment,
    updateFormStatus,
    settingTag,
    referenceDocumentAcademicYear,
    conclusionPhase,
    searchReferenceDocument,
    qapcLogUserList,
    referenceFormAttachment,
    getCategoryAndForm,
    formProgramList,
    createIncorporate,
    getIncorporateSection,
    getTermAndAttempt,
    getIncorporate,
    getTodoMissed,
    getCategoryList,
    getFormViewAttachment,
    searchDocumentList,
    createDuplicateForm,
    toDoCreatedFormList,
    publishedFormInitiator,
    selectedFormList,
    formInitiatedAttachments,
} = require('./formInitiator.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

//qapc log user list
router.get(
    '/qapcLogUserList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(qapcLogUserList),
);
router.get(
    '/getCreateForm',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getCreateForm),
);
router.get(
    '/getFormViewAttachment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getFormViewAttachment),
);
//createNewForm
router.post(
    '/createNewForm',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(createNewForm),
);
router.put(
    '/updateCreateNewForm',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateCreateNewForm),
);
router.get(
    '/singleFormList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(singleFormList),
);
//step 2 form attachment
router.put(
    '/formAttachment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(formAttachment),
);
router.get(
    '/getFormAttachment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getFormAttachment),
);
//update delete & archive
router.put(
    '/updateFormStatus',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateFormStatus),
);
router.get(
    '/settingTag',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(settingTag),
);
//reference Document
router.get(
    '/referenceDocumentAcademicYear',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(referenceDocumentAcademicYear),
);
router.get(
    '/conclusionPhase',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(conclusionPhase),
);
router.get(
    '/searchReferenceDocument',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(searchReferenceDocument),
);
router.get(
    '/searchDocumentList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(searchDocumentList),
);
router.get(
    '/referenceFormAttachment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(referenceFormAttachment),
);
//incorporate sections
router.get(
    '/getCategoryAndForm',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getCategoryAndForm),
);
router.get(
    '/getTermAndAttempt',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getTermAndAttempt),
);
router.get(
    '/formProgramList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(formProgramList),
);
router.get(
    '/getIncorporateSection',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getIncorporateSection),
);
router.put(
    '/createIncorporate',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(createIncorporate),
);
router.get(
    '/getIncorporate',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getIncorporate),
);
//missed todo list
router.get(
    '/todoMissed',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getTodoMissed),
);
//category list
router.get(
    '/categoryList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getCategoryList),
);
//create duplicate
router.get(
    '/createDuplicateForm',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(createDuplicateForm),
);
router.get(
    '/toDoCreatedFormList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(toDoCreatedFormList),
);
//HABA
router.get(
    '/publishedFormInitiator',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(publishedFormInitiator),
);
router.get(
    '/selectedFormList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(selectedFormList),
);
router.get(
    '/formInitiatedAttachments',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(formInitiatedAttachments),
);
module.exports = router;
