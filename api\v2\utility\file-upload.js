const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const keys = require('./util_keys');

const s3 = new AWS.S3({
    accessKeyId: keys.AWS_ACCESS_KEY_V2,
    secretAccessKey: keys.AWS_SECRET_KEY_V2,
});

const s3Oci = new AWS.S3({
    region: keys.OCI_REGION,
    accessKeyId: keys.OCI_ACCESS_KEY_ID,
    secretAccessKey: keys.OCI_SECRET_ACCESS_KEY,
    endpoint: keys.OCI_AWS_S3_API,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
});
const s3Config = keys.DIGIVAL_CLOUD_PROVIDER === 'OCI' ? s3Oci : s3;

const s3FileURL = ({ filePath, fileName }) => {
    return keys.DIGIVAL_CLOUD_PROVIDER === 'OCI'
        ? `${keys.OCI_AWS_S3_API}/${filePath}/${fileName}`
        : `https://s3-${keys.AWS_REGION}.amazonaws.com/${filePath}/${fileName}`;
};

const uploadProgramMedia = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const { _institution_id } = req.body;
            if (!_institution_id) {
                return cb(new Error('_institution_id is required'));
            }
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, `institute_${_institution_id}/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.AWS_SECRET_BUCKET_NAME_V2,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'video/mp2t'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

const uploadInstituteMedia = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const { id } = req.params;
            if (!id) {
                return cb(new Error('Institute id is required'));
            }
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, `institute_${req.params.id}/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.AWS_SECRET_BUCKET_NAME_V2,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'video/mp2t'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

const uploadInstituteLogo = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/png') {
                let fileName = Date.now() + '-' + file.originalname;
                cb(null, `logos/` + fileName);
                const fieldName = file.fieldname;
                fileName = s3FileURL({
                    filePath: keys.AWS_SECRET_BUCKET_NAME_V2,
                    fileName,
                });
                req.body[fieldName] = fileName;
            } else {
                cb(null, false);
            }
        },
    }),
    limits: {
        fileSize: 1024 * 1024 * 1,
    },
});

const uploadFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, `courses/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.AWS_SECRET_BUCKET_NAME_V2,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'video/mp2t'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

const uploadDocumentFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const { payload } = req.headers;
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            if (payload && payload.user_id) {
                fileName = payload.user_id + '/' + fileName;
            }
            cb(null, `users/documentation/` + fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.AWS_SECRET_BUCKET_NAME_V2,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'video/mp2t' ||
            file.mimetype === 'application/msword' ||
            file.mimetype ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.mimetype === 'application/pdf' ||
            file.mimetype === 'image/svg+xml' ||
            file.mimetype === 'application/pdf'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        }
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

module.exports = {
    uploadProgramMedia,
    uploadInstituteMedia,
    uploadInstituteLogo,
    uploadFile,
    uploadDocumentFile,
};
