const program_formate = require('../program/program_formate');
const credit_calc_formate = require('../credit_hours_calc/credit_hours_calc_formate');

exports.session_type_ID_Only = (doc) => {
    //console.log(doc);
    let obj = {
        _id: doc._id,
        session_type: doc.session_type,
        delivery_type: doc.delivery_type,
        delivery_mode: doc.delivery_mode,
        delivery_symbol: doc.delivery_symbol,
        credit_calc: doc._credit_calc_id,
        program: doc._program_id,
        isActive: doc.isActive,
        isDeleted: doc.isDeleted
    }
    return obj;
}

module.exports = {
    session_type: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                session_type: element.session_type,
                delivery_type: element.delivery_type,
                delivery_mode: element.delivery_mode,
                delivery_symbol: element.delivery_symbol,
                credit_calc: credit_calc_formate.credit_hours_calc_ID_Only(element.credit_calc),
                program: program_formate.program_ID_Only(element.program),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    session_type_ID: (doc) => {
        let obj = {
            _id: doc._id,
            session_type: doc.session_type,
            delivery_type: doc.delivery_type,
            delivery_mode: doc.delivery_mode,
            delivery_symbol: doc.delivery_symbol,
            credit_calc: credit_calc_formate.credit_hours_calc_ID_Only(doc.credit_calc),
            program: program_formate.program_ID_Only(doc.program),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    session_type_ID_Onlys: function (doc) {
        let obj = {
            _id: doc._id,
            session_type: doc.session_type,
            delivery_type: doc.delivery_type,
            delivery_mode: doc.delivery_mode,
            delivery_symbol: doc.delivery_symbol,
            credit_calc: doc._credit_calc_id,
            program: doc._program_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    session_type_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                session_type: element.session_type,
                delivery_type: element.delivery_type,
                delivery_mode: element.delivery_mode,
                delivery_symbol: element.delivery_symbol,
                credit_calc: element._credit_calc_id,
                program: element._program_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}