const constant = require('../utility/constants');
const role = require('mongoose').model(constant.ROLE);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;

exports.list = async (req, res) => {
    const role_list = await base_control.get_list(
        role,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        {},
    );
    if (role_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('ROLE_LIST'),
                    role_list.data,
                ),
            );
    return res
        .status(200)
        .send(common_files.responseFunctionWithRequest(req, 200, true, req.t('ROLE_LIST'), []));
};

exports.role_list = async (req, res) => {
    const role_list = await base_control.get_list(
        role,
        { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
        { name: 1, isActive: 1 },
    );
    if (role_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('ROLE_LIST'),
                    role_list.data,
                ),
            );
    return res
        .status(200)
        .send(common_files.responseFunctionWithRequest(req, 200, true, req.t('ROLE_LIST'), []));
};

exports.list_id = async (req, res) => {
    const role_list = await base_control.get(
        role,
        {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params.id),
            isDeleted: false,
        },
        {},
    );
    if (role_list.status)
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('ROLE_SINGLE_GET'),
                    role_list.data,
                ),
            );
    return res
        .status(200)
        .send(common_files.response_function(res, 200, true, req.t('ROLE_SINGLE_GET'), {}));
};

exports.insert = async (req, res) => {
    const institution_check = await base_control.get(institution, {
        _id: req.headers._institution_id,
        isDeleted: false,
    });
    if (!institution_check.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const regex = new RegExp(['^', req.body.name, '$'].join(''), 'i');
    const role_list = await base_control.get(
        role,
        { _institution_id: ObjectId(req.headers._institution_id), name: regex, isDeleted: false },
        {},
    );
    if (role_list.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DUPLICATE_ROLE'),
                    req.t('DUPLICATE_ROLE'),
                ),
            );
    const objs = {
        _institution_id: req.headers._institution_id,
        name: req.body.name,
    };
    const doc = await base_control.insert(role, objs);
    if (doc.status)
        return res
            .status(201)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    201,
                    true,
                    req.t('ROLE_CREATED'),
                    doc.data,
                ),
            );
    return res
        .status(410)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                410,
                false,
                req.t('UNABLE_TO_CREATE_ROLE'),
                doc.data,
            ),
        );
};

exports.update = async (req, res) => {
    if (req.body.name) {
        const regex = new RegExp(['^', req.body.name, '$'].join(''), 'i');
        const role_list = await base_control.get(
            role,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                name: regex,
                isDeleted: false,
            },
            {},
        );
        if (role_list.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DUPLICATE_ROLE'),
                        req.t('DUPLICATE_ROLE'),
                    ),
                );
    }
    const modulePolicy = [];
    if (req.body.modules?.length) {
        req.body.modules.forEach((moduleElement) => {
            moduleElement.pages?.forEach((pageElement) => {
                modulePolicy.push(
                    ...(pageElement.actions?.map((actionElement) => actionElement.policy) || []),
                );
                pageElement.tabs?.forEach((tabElement) => {
                    modulePolicy.push(
                        ...(tabElement.actions?.map((actionElement) => actionElement.policy) || []),
                    );
                    tabElement.subTabs?.forEach((subTabElement) => {
                        modulePolicy.push(
                            ...(subTabElement.actions?.map(
                                (actionElement) => actionElement.policy,
                            ) || []),
                        );
                    });
                });
            });
        });
    }
    req.body.policy = modulePolicy;
    const doc = await base_control.update(role, ObjectId(req.params.id), req.body);
    if (doc.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('ROLE_UPDATED'),
                    doc.data,
                ),
            );
    return res
        .status(410)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                410,
                false,
                req.t('UNABLE_TO_UPDATE_ROLE'),
                doc.data,
            ),
        );
};

exports.delete = async (req, res) => {
    //Check is Role is Assigned to User / If it not assigned only able to delete
    const role_assign_check = await base_control.get(
        role_assign,
        { roles: { $elemMatch: { _role_id: ObjectId(req.params.id) } } },
        { _id: 1 },
    );
    if (role_assign_check.status)
        return res
            .status(405)
            .send(
                common_files.response_function(
                    res,
                    405,
                    false,
                    req.t('ROLE_IS_ASSIGNED_TO_USER_UNABLE_TO_REMOVE'),
                    req.t('ROLE_IS_ASSIGNED_TO_USER_UNABLE_TO_REMOVE'),
                ),
            );
    const doc = await base_control.delete(role, ObjectId(req.params.id));
    if (doc.status)
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('ROLE_REMOVED'), doc.data));
    return res
        .status(410)
        .send(
            common_files.response_function(
                res,
                410,
                false,
                req.t('UNABLE_TO_ROLE_REMOVED'),
                doc.data,
            ),
        );
};
exports.listRoleBasedOnTheRoleAndPermission = async (req, res) => {
    const role_list = await role.find(
        {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            'modules.name': 'Leave Management',
            'modules.pages': {
                $elemMatch: {
                    name: 'Leave Authority',
                    'actions.name': 'View',
                },
            },
        },
        {
            name: 1,
        },
    );
    if (role_list)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('role_list'),
                    role_list,
                ),
            );
    return res
        .status(200)
        .send(common_files.responseFunctionWithRequest(req, 200, true, req.t('role_list'), []));
};
exports.listOfRoleUsers = async (req, res) => {
    try {
        const roleUserList = await role_assign
            .find(
                {
                    'roles._role_id': ObjectId(req.params.roleId),
                },
                {
                    // user_name: 1,
                    _user_id: 1,
                },
            )
            .populate({ path: '_user_id', select: { email: 1, user_id: 1, name: 1, gender: 1 } })
            .lean();
        if (roleUserList)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('role_user_list'),
                        roleUserList,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('role_user_list'),
                    [],
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};
