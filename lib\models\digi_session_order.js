const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const digi_session_order = new Schema(
    {
        session_flow_data: [
            {
                _session_id: {
                    type: Schema.Types.ObjectId,
                },
                sessionData: String,
                sessionDocumentDetails: [
                    {
                        url: String,
                        fileName: String,
                        uploadedAt: {
                            type: Date,
                        },
                    },
                ],
                s_no: Number,
                delivery_type: String,
                _delivery_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DEPARTMENT_SUBJECT,
                },
                delivery_symbol: {
                    type: String,
                    required: true,
                },
                delivery_no: {
                    type: Number,
                    required: true,
                },
                delivery_topic: {
                    type: String,
                    required: true,
                },
                subjects: [
                    {
                        subject_name: String,
                        _subject_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DEPARTMENT_SUBJECT,
                        },
                    },
                ],
                duration: Number,

                week: [
                    {
                        level_id: {
                            type: Schema.Types.ObjectId,
                        },
                        level_no: String,
                        week_no: Number,
                    },
                ],
                slo: [
                    {
                        no: String,
                        name: String,
                        delivery_type_id: String,
                        delivery_symbol: String,
                        delivery_no: Number,
                    },
                ],
                _module_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DIGI_SESSION_ORDER_MODULE,
                },
            },
        ],
        additional_session_flow_data: [
            {
                _session_id: {
                    type: Schema.Types.ObjectId,
                },
                s_no: Number,
                delivery_type: String,
                _delivery_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DEPARTMENT_SUBJECT,
                },
                delivery_symbol: {
                    type: String,
                    required: true,
                },
                delivery_no: {
                    type: Number,
                    required: true,
                },
                delivery_topic: {
                    type: String,
                    required: true,
                },
                subjects: [
                    {
                        subject_name: String,
                        _subject_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DEPARTMENT_SUBJECT,
                        },
                    },
                ],
                duration: Number,

                week: [
                    {
                        level_id: {
                            type: Schema.Types.ObjectId,
                        },
                        level_no: String,
                        week_no: Number,
                    },
                ],
            },
        ],
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: constant.COURSE,
            required: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        max_hours: {
            type: Number,
        },
        synced: { type: Boolean, default: false },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_SESSION_ORDER, digi_session_order);
