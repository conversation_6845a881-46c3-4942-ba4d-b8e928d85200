let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let role_set = new Schema({
    _role_id: {
        type: Schema.Types.ObjectId,
        ref: constant.ROLE,
        required: true
    },
    _permission_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.PERMISSION,
        required: true
    }],
    // description: {
    //     type: String
    // },
    // code: {
    //     type: String
    // },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: <PERSON>olean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.ROLE_SET, role_set);