const { Joi } = require('../../common/middlewares/validation');
const { STUDENT, STAFF } = require('../../common/utils/constants');

// Common schema
const objectId = Joi.string().hex().length(24);

const createRoleSchema = Joi.object({
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().valid(STUDENT, STAFF).required(),
    }),
}).unknown(true);

const updateRoleSchema = Joi.object({
    query: Joi.object({
        roleId: objectId.required(),
    }),
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().valid(STUDENT, STAFF).required(),
    }),
}).unknown(true);

const deleteRoleSchema = Joi.object({
    query: Joi.object({
        roleId: objectId.required(),
    }),
}).unknown(true);

module.exports = {
    createRoleSchema,
    updateRoleSchema,
    deleteRoleSchema,
};
