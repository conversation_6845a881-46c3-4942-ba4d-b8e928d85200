const constant = require('../utility/constants');

// var institution = require('mongoose').model(constant.DIGI_INSTITUTE);
const institution = require('mongoose').model(constant.INSTITUTION);
const digi_course_group = require('mongoose').model(constant.DIGI_COURSE_GROUP);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;

async function insert(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        'Institution not found',
                        'Institution not found',
                    ),
                );

        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _curriculum_id: ObjectId(req.body._curriculum_id),
            _level_id: ObjectId(req.body._level_id),
            _program_id: ObjectId(req.body._program_id),
            _year_id: ObjectId(req.body._year_id),
            isDeleted: false,
            isActive: true,
        };
        const course_group_data = await base_control.get_list(digi_course_group, query, {});
        let group_no = 1;
        if (course_group_data.status)
            group_no = course_group_data.data[course_group_data.data.length - 1].group_no + 1;
        let obj = {};
        let doc = '';
        for (let i = 0; i < req.body.course_group.length; i++) {
            obj = {
                _institution_id: ObjectId(req.headers._institution_id),
                _curriculum_id: ObjectId(req.body._curriculum_id),
                _level_id: ObjectId(req.body._level_id),
                _program_id: ObjectId(req.body._program_id),
                _year_id: ObjectId(req.body._year_id),
                group_no,
                group_name: 'GROUP ' + group_no,
                course_id: req.body.course_group[i].course_id,
                course_name: req.body.course_group[i].course_name,
            };
            doc = await base_control.insert(digi_course_group, obj);
        }
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        'Course added to group successfully',
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    'Unable to add course to group',
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const query = { isDeleted: false, isActive: true };
        const doc = await base_control.get_list(digi_course_group, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        'Course group not found',
                        [],
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    'Course Group List',
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function ungroup(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );

        // let query = { group_name: req.body.group_name }
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _curriculum_id: ObjectId(req.body._curriculum_id),
            _level_id: ObjectId(req.body._level_id),
            _program_id: ObjectId(req.body._program_id),
            _year_id: ObjectId(req.body._year_id),
            group_name: req.body.group_name,
        };
        const obj = {
            isDeleted: true,
            isActive: false,
        };
        const doc = await digi_course_group.updateMany(query, obj);
        response_obj = {};
        if (!doc) {
            response_obj = {
                status: false,
                data: 'Updating error',
            };
        } else {
            if (doc.modifiedCount > 0) {
                response_obj = {
                    status: true,
                    data: 'Updating successfully',
                    responses: doc,
                };
            } else {
                response_obj = {
                    status: false,
                    data: 'Updating error check parsing data',
                };
            }
        }
        if (response_obj.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        'Ungrouped successfully',
                        response_obj.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    'Unable to update course',
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

module.exports = {
    insert,
    list,
    ungroup,
};
