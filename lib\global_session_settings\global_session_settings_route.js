const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    createInstitutionSessionSetting,
    getInstitutionSessionSetting,
    deleteInstitutionSessionSetting,
    lateConfiguration,
    getLateConfig,
    getGlobalSessionStatusDetails,
    updateGlobalSessionStatusDetails,
    getGlobalScheduleStaffStudentCaptchaControl,
    updateGlobalScheduleStaffStudentCaptchaControl,
    getGlobalSessionDocumentDetails,
    listGlobalSessionDocumentDetails,
    createGlobalSessionDocumentDetails,
    updateGlobalSessionDocumentDetails,
    deleteGlobalSessionDocumentDetails,
    getDisciplinaryRemarksVisibility,
    updateDisciplinaryRemarkVisibility,
    updateStaffFacial,
    updateStudentFacial,
    updateStudentSelfRegistrationDocument,
    updateSchedulePermission,
    updateHandout,
    getHandOut,
    updateUserFacialRegister,
    getUserDeviceControl,
    updateUserDeviceControl,
} = require('./global_session_settings_controller');
const {
    createDaySessionValidator,
    getSessionValidator,
    updateGlobalSessionStatusValidator,
    schedulePermissionValidator,
    updateHandoutValidation,
    getHandoutValidation,
} = require('./global_session_settings_validator');
const { getTardis, createTardis, deleteTardis } = require('./global_session_settings_controller');
const {
    getTardisValidator,
    createTardisValidator,
    deleteTardisValidator,
} = require('./global_session_settings_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.post(
    '/create-institution-session-setting',
    [
        userPolicyAuthentication([
            'global_configuration:institution:view',
            'global_configuration:institution:basic_details:view',
            'global_configuration:institution:basic_details:edit',
        ]),
    ],
    createDaySessionValidator,
    catchAsync(createInstitutionSessionSetting),
);
router.get(
    '/get-institution-session-setting',
    [
        userPolicyAuthentication([
            'global_configuration:institution:view',
            'global_configuration:institution:basic_details:view',
        ]),
    ],
    getSessionValidator,
    catchAsync(getInstitutionSessionSetting),
);
router.put('/updateStaffFacial', catchAsync(updateStaffFacial));

router.put(
    '/updateSchedulePermission',
    [
        userPolicyAuthentication([
            'global_configuration:institution:view',
            'global_configuration:institution:basic_details:view',
        ]),
    ],
    schedulePermissionValidator,
    catchAsync(updateSchedulePermission),
);
router.delete(
    '/delete-institution-session-setting',
    getSessionValidator,
    catchAsync(deleteInstitutionSessionSetting),
);

//----------------tardis-enable-routes----------------*
router.get(
    '/get-tardis',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, 'global_search:dashboard:view'])],
    getTardisValidator,
    catchAsync(getTardis),
);
router.post(
    '/update-tardis',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    createTardisValidator,
    catchAsync(createTardis),
);
router.delete(
    '/delete-tardis',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    deleteTardisValidator,
    catchAsync(deleteTardis),
);

//late config
router.put(
    '/lateConfig',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(lateConfiguration),
);
router.get(
    '/lateConfig',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getLateConfig),
);

//Session status management - Global setting
router.get(
    '/getGlobalSessionStatusDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getGlobalSessionStatusDetails),
);
router.put(
    '/updateGlobalSessionStatusDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateGlobalSessionStatusValidator,
    catchAsync(updateGlobalSessionStatusDetails),
);

// Schedule Staff & Student Attendance Captcha Control
router.get(
    '/getGlobalScheduleStaffStudentCaptchaControl',
    catchAsync(getGlobalScheduleStaffStudentCaptchaControl),
);
router.put(
    '/updateGlobalScheduleStaffStudentCaptchaControl',
    catchAsync(updateGlobalScheduleStaffStudentCaptchaControl),
);
//sessionContent and document details
router.post('/createGlobalSessionDocumentDetails', catchAsync(createGlobalSessionDocumentDetails));
router.get('/getGlobalSessionDocumentDetails', catchAsync(getGlobalSessionDocumentDetails));
router.get('/listGlobalSessionDocumentDetails', catchAsync(listGlobalSessionDocumentDetails));
router.put('/updateGlobalSessionDocumentDetails', catchAsync(updateGlobalSessionDocumentDetails));
router.delete(
    '/deleteGlobalSessionDocumentDetails',
    catchAsync(deleteGlobalSessionDocumentDetails),
);
// Disciplinary Remarks
router.get(
    '/disciplinary_remarks_visibility',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getDisciplinaryRemarksVisibility),
);
router.put(
    '/disciplinary_remarks_visibility',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(updateDisciplinaryRemarkVisibility),
);
router.put('/student-facial', catchAsync(updateStudentFacial));
router.put('/student-self-registration', catchAsync(updateStudentSelfRegistrationDocument));

//course handout
router.put('/updateHandout', updateHandoutValidation, catchAsync(updateHandout));
router.get('/getHandOut', getHandoutValidation, catchAsync(getHandOut));
router.put('/user-facial-register', catchAsync(updateUserFacialRegister));
router.get('/getDeviceControl', catchAsync(getUserDeviceControl));
router.put('/setDeviceControl', catchAsync(updateUserDeviceControl));

module.exports = router;
