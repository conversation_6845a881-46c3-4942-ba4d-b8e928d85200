const express = require('express');
const route = express.Router();
const student = require('./student_controller');
const validater = require('./student_validator');
route.post('/list', student.list_values);
route.get('/:id', validater.student_id, student.list_id);
route.get('/', student.list);
route.post('/', validater.student, student.insert);
route.put('/:id', validater.student_id, validater.student, student.update);
route.delete('/:id', validater.student_id, student.delete);

module.exports = route;