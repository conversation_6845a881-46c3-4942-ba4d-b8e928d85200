const Joi = require('joi');
const { DS_UPDATE, DS_DELETE, DS_CREATE } = require('../../utility/constants');
const objectId = Joi.string().alphanum().length(24).required();
const {
    USER_MODULE_PERMISSION_CATEGORY_KEYS: { TAGS, GROUPS, FAMILIES },
} = require('../../utility/constants');
const optionalObjectId = Joi.string().alphanum().length(24);
const commonQuery = {
    tagId: objectId.error((error) => error),
    institutionCalendarId: objectId.error((error) => error),
    reportId: objectId.error((error) => error),
};

exports.getExistingTagReportValidator = Joi.object({
    query: Joi.object({
        createdBy: objectId.error((error) => error),
    }).unknown(true),
});

exports.getTagBasedQuestionsValidator = Joi.object({
    body: Joi.object({
        ...commonQuery,
    }).unknown(true),
});

exports.getQuestionBasedSurveyDetailValidator = Joi.object({
    query: Joi.object({
        ...commonQuery,
        questionId: Joi.string()
            .required()
            .error((error) => error),
    }).unknown(true),
});

exports.getSingleSurveyDetailValidator = Joi.object({
    query: Joi.object({
        surveyId: objectId.error((error) => error),
    }).unknown(true),
});

exports.excludeSurveyValidator = Joi.object({
    body: Joi.object({
        reportId: objectId.error((error) => error),
        tagId: objectId.error((error) => error),
        questionId: Joi.string()
            .required()
            .error((error) => error),
    }).unknown(true),
});

exports.createTagReportValidator = Joi.object({
    body: Joi.object({
        createdBy: objectId.error((error) => error),
        tagReportName: Joi.string()
            .required()
            .error((error) => error),
        viewBy: Joi.string()
            .required()
            .error((error) => error),
        noOfTags: Joi.number()
            .required()
            .error((error) => error),
        reportId: objectId.error((error) => error),
    }).unknown(true),
});

exports.getTagReportListValidator = Joi.object({
    query: Joi.object({
        createdBy: objectId.error((error) => error),
    }).unknown(true),
});

exports.getTagReportHeaderFilterValidator = Joi.object({
    body: Joi.object({
        institutionCalendarId: objectId.error((error) => error),
        reportId: objectId.error((error) => error),
        requiredField: Joi.string()
            .required()
            .error((error) => error),
    }).unknown(true),
});

exports.getTagReportResponseRateAnalysisValidator = Joi.object({
    body: Joi.object({
        reportId: objectId.error((error) => error),
        viewBy: Joi.string()
            .valid(FAMILIES, TAGS, GROUPS)
            .required()
            .error((error) => error),
    }).unknown(true),
});

exports.deleteTagReportValidator = Joi.object({
    body: Joi.object({
        reportId: objectId.error((error) => error),
    }).unknown(true),
});

exports.getExternalUserValidator = Joi.object({
    query: Joi.object({
        isTotalCountNeeded: Joi.string()
            .valid('true', 'false')
            .error((error) => error),
    }).unknown(true),
});

exports.getExternalUserValidator = Joi.object({
    query: Joi.object({
        isTotalCountNeeded: Joi.string()
            .valid('true', 'false')
            .error((error) => error),
    }).unknown(true),
});

exports.updateExternalUserListValidator = Joi.object({
    body: Joi.object({
        updateId: optionalObjectId.error((error) => error),
        updateType: Joi.string()
            .valid(DS_UPDATE, DS_DELETE, DS_CREATE)
            .error((error) => error),
        newUserList: Joi.object({
            email: Joi.string()
                .email()
                .required()
                .error((error) => error),
            name: Joi.string()
                .required()
                .error((error) => error),
        }).unknown(true),
    }).unknown(true),
});

exports.checkIsEmailDuplicateValidator = Joi.object({
    query: Joi.object({
        email: Joi.string().email().required(),
    }).unknown(true),
});
