/**
 * Expose routes
 *
 * @param {Express} app `Express` instance.
 * @api public
 */

const { authMiddleware } = require('../../middleware');

module.exports = function routes(app) {
    const version = { v1: '/api/v1', v2: '/api/v2' };
    const sisProgramInput = require('../api/sisIntegration/programInput/programInput.route');
    const assessmentManagement = require('../api/attainment_module/assessment-management/assessment-management.route');
    const attainmentManagement = require('../api/attainment_module/attainment-management/attainment-management.route');
    const attainmentReport = require('../api/attainment_module/attainment-report/attainment-report.route');
    const assignment = require('../api/assignment_module/assignment/assignment.route');
    const assignmentCategory = require('../api/assignment_module/assignment_category/assignment_category.route');
    const assignmentSettings = require('../api/assignment_module/assignment-settings/assignment-settings.route');
    const assignmentPrompt = require('../api/assignment_module/assignment-prompt/assignment-prompt.route');
    const assignmentPromptAnswer = require('../api/assignment_module/assignment_prompt_answer/assignment_prompt_answer.route');
    const assignmentAnswer = require('../api/assignment_module/assignment_answer/assignment_answer.route');
    const assignmentComments = require('../api/assignment_module/assignment_comments/assignment_comments.route');
    const course = require('../api/assignment_module/course/course.route');
    const programInput = require('../api/assignment_module/program-input/program-input.route');
    const assignmentGroup = require('../api/assignment_module/assignment_group/assignment_group.route');
    const assignmentRubrics = require('../api/assignment_module/assignment_rubrics/assignment-rubrics.route');
    const assignmentReport = require('../api/assignment_module/assignment_report/assignment.report.route');

    app.use(version.v1 + '/sis/programInput', authMiddleware, sisProgramInput);
    app.use(version.v1 + '/assessment-management', authMiddleware, assessmentManagement);
    app.use(version.v1 + '/attainment-management', authMiddleware, attainmentManagement);
    app.use(version.v1 + '/attainment-report', authMiddleware, attainmentReport);
    app.use(version.v1 + '/assignment-settings', authMiddleware, assignmentSettings);
    app.use(version.v1 + '/assignment-prompt', authMiddleware, assignmentPrompt);
    app.use(version.v1 + '/assignment-course', authMiddleware, course);
    app.use(version.v1 + '/assignment-program', authMiddleware, programInput);
    app.use(version.v1 + '/assignment', authMiddleware, assignment);
    app.use(version.v1 + '/assignment-category', authMiddleware, assignmentCategory);
    app.use(version.v1 + '/assignment-group', authMiddleware, assignmentGroup);
    app.use(version.v1 + '/assignment-prompt-answer', authMiddleware, assignmentPromptAnswer);
    app.use(version.v1 + '/assignment-answer', authMiddleware, assignmentAnswer);
    app.use(version.v1 + '/assignment-comments', authMiddleware, assignmentComments);
    app.use(version.v1 + '/assignment-rubrics', authMiddleware, assignmentRubrics);
    app.use(version.v1 + '/assignment-report', authMiddleware, assignmentReport);
};
