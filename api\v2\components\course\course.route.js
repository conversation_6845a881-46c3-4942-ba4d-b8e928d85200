const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    courseAdd,
    courseConfigAdd,
    portfolioAdd,
    portfolioDelete,
    portfolioUpdate,
    portfolioGet,
    portfolioIdGet,
    requisiteCoursesAdd,
    addCourseTheme,
    getCourseTheme,
    getSubCourseTheme,
    listCourseTheme,
    deleteCourseTheme,
    getlinkedSessions,
    getUnlinkedSessions,
    linkSessions,
    editLinkSession,
    courseGet,
    courseCurriculamWiseGet,
    courseProgramWiseGet,
    courseDelete,
    courseParamWiseGet,
    courseAssigning,
    assignDelete,
    addIndependentCourse,
    getIndependentCourses,
    getIndependentCourse,
    courseEdit,
    GetProgramCurriculumList,
    getIndependentCourseOverview,
    independentCourseEdit,
    independentCourseConfigAdd,
    independentCourseConfigGet,
    independentCourseAssign,
    courseConfigAddIndianSystem,
    toggleIndianSystemAdvanceCreditConfiguration,
} = require('./course.controller');
const {
    courseAddSchema,
    courseConfigAddSchema,
    portfolioDeleteSchema,
    requisiteCourseSchema,
    courseGetSchema,
    courseGetCurriculamWiseSchema,
    courseGetProgramWiseSchema,
    addCourseThemeValidator,
    getCourseThemeValidator,
    getSubCourseThemeValidator,
    listCourseThemeValidator,
    getUnlinkedSessionsValidator,
    linkSessionValidator,
    editLinkSessionValidator,
    courseDeleteSchema,
    courseGetListSchema,
    courseAssigningValidation,
    assignDeleteSchema,
    addIndependentCourseValidator,
    getIndependentCoursesValidator,
    getIndependentCourseValidator,
    courseEditSchema,
    GetProgramCurriculumListValidator,
    editIndependentCourseValidator,
    getIndependentCourseConfigValidator,
    independentCourseConfigAddSchema,
    independentCourseAssignUpdateValidator,
} = require('./course.validator');

const { uploadcourseFile } = require('./course.util');
const { validate } = require('../../utility/input-validation');
const {
    toggleDeliveryTypeIndianCreditSystem,
} = require('../session-delivery-types/session-delivery-types.controller');

router.get(
    '/overview-independent-course/:_institution_id',
    validate(GetProgramCurriculumListValidator),
    catchAsync(getIndependentCourseOverview),
);
router.post('/', validate(courseAddSchema), catchAsync(courseAdd));
router.put('/configuration', validate(courseConfigAddSchema), catchAsync(courseConfigAdd));
router.put('/course-edit/:_course_id', validate(courseEditSchema), catchAsync(courseEdit));
router.post(
    '/programs/:_program_id/:_curriculum_id/:_course_id',
    validate(courseGetProgramWiseSchema),
    catchAsync(courseProgramWiseGet),
);
router.get('/:_course_id/:type', validate(courseGetSchema), catchAsync(courseGet));
router.get(
    '/:_curriculum_id/',
    validate(courseGetCurriculamWiseSchema),
    catchAsync(courseCurriculamWiseGet),
);
router.get(
    '/course-list/:_program_id/:_curriculum_id/:_year_id/:_level_id/',
    validate(courseGetListSchema),
    catchAsync(courseParamWiseGet),
);
router.put(
    '/course-assigning-details/:_course_id',
    validate(courseAssigningValidation),
    catchAsync(courseAssigning),
);
router.post('/portfolio', uploadcourseFile, catchAsync(portfolioAdd));
router.delete(
    '/portfolio/:_course_id/:_portfolio_id',
    validate(portfolioDeleteSchema),
    catchAsync(portfolioDelete),
);
router.delete(
    '/course-assigning-details/:_course_id/:_assign_id',
    validate(assignDeleteSchema),
    catchAsync(assignDelete),
);
router.delete('/:_course_id', validate(courseDeleteSchema), catchAsync(courseDelete));
router.put('/portfolio/:_course_id/:_portfolio_id', uploadcourseFile, catchAsync(portfolioUpdate));
router.get('/portfolio/course/:_course_id', catchAsync(portfolioGet));
router.get('/portfolio/:_course_id/:_portfolio_id', catchAsync(portfolioIdGet));
router.post(
    '/requisiteCourses/:_course_id/:type',
    validate(requisiteCourseSchema),
    catchAsync(requisiteCoursesAdd),
);

router.post('/add-course-theme', validate(addCourseThemeValidator), catchAsync(addCourseTheme));
router.get(
    '/get-course-theme/:_institution_id/:courseId/:themeId',
    validate(getCourseThemeValidator),
    catchAsync(getCourseTheme),
);
router.get(
    '/get-sub-course-theme/:_institution_id/:courseId/:subThemeId',
    validate(getSubCourseThemeValidator),
    catchAsync(getSubCourseTheme),
);
router.get(
    '/list-course-theme/:_institution_id/:courseId',
    validate(listCourseThemeValidator),
    catchAsync(listCourseTheme),
);
router.delete(
    '/delete-course-theme/:_institution_id/:courseId/:themeId',
    validate(getCourseThemeValidator),
    catchAsync(deleteCourseTheme),
);
router.get(
    '/get-unlinked-sessions/:_course_id/:_institution_id',
    validate(getUnlinkedSessionsValidator),
    catchAsync(getUnlinkedSessions),
);

router.get(
    '/get-linked-sessions/:_course_id/:_institution_id',
    validate(getUnlinkedSessionsValidator),
    catchAsync(getlinkedSessions),
);
router.put('/link-session', validate(linkSessionValidator), catchAsync(linkSessions));
router.put(
    '/edit-link-session/:id/:linkedSessionId/:_institution_id',
    validate(editLinkSessionValidator),
    catchAsync(editLinkSession),
);
router.post(
    '/add-independent-course',
    validate(addIndependentCourseValidator),
    catchAsync(addIndependentCourse),
);
router.get(
    '/list/independent/course',
    validate(getIndependentCoursesValidator),
    catchAsync(getIndependentCourses),
);
router.get(
    '/get-independent-course/:id',
    validate(getIndependentCourseValidator),
    catchAsync(getIndependentCourse),
);

router.get(
    '/program/curriculum-year-level-list/:_institution_id',
    validate(GetProgramCurriculumListValidator),
    catchAsync(GetProgramCurriculumList),
);

router.put(
    '/independent-course/edit/:_course_id',
    validate(editIndependentCourseValidator),
    catchAsync(independentCourseEdit),
);

router.post(
    '/add-independent-course-config',
    validate(independentCourseConfigAddSchema),
    catchAsync(independentCourseConfigAdd),
);
router.get(
    '/independent-course/get-independent-course-config/:_course_id',
    validate(getIndependentCourseConfigValidator),
    catchAsync(independentCourseConfigGet),
);
router.put(
    '/assigndetails-independent-course-config/:_course_id',
    validate(independentCourseAssignUpdateValidator),
    catchAsync(independentCourseAssign),
);

router.post('/course-config-indian-system', catchAsync(courseConfigAddIndianSystem));
router.post(
    '/toggle-indian-advanced-credit-system-config',
    catchAsync(toggleIndianSystemAdvanceCreditConfiguration),
);

module.exports = router;
