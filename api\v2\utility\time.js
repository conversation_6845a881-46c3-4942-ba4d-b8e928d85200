// format One Digit To Two Digit Number
const formatHourMinuteOneDigitToTwoDigitNumber = (session) => {
    const startTimeHour = String(session.start.hour).padStart(2, '0');
    const startTimeMinute = String(session.start.minute).padStart(2, '0');
    const startTimeSession = {
        hour: startTimeHour,
        minute: startTimeMinute,
        format: session.start.format,
    };

    const endTimeHour = String(session.end.hour).padStart(2, '0');
    const endTimeMinute = String(session.end.minute).padStart(2, '0');

    const endTimeSession = {
        hour: endTimeHour,
        minute: endTimeMinute,
        format: session.end.format,
    };

    return { start: startTimeSession, end: endTimeSession };
};

module.exports = {
    formatHourMinuteOneDigitToTwoDigitNumber,
};
