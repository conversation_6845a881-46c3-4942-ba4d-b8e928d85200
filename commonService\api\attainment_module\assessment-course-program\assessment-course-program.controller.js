const assessmentPlanningSchema = require('./assessment-course-program.model');
const assessmentManagementSchema = require('../assessment-planning/assessment-planning.model');
const assessmentLibrarySchema = require('../assessment-library/assessment-library.model');
const { convertToMongoObjectId, clone } = require('../../../utility/common');

const getAssessmentCourseProgramPlanning = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, institutionCalendarId, term, level, courseId, type } = query;
        let assessmentPlanning = await assessmentPlanningSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                    level,
                    _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                    type,
                },
                {
                    types: 1,
                },
            )
            .lean();

        if (assessmentPlanning) {
            // Adding Assessment Feed Datas
            const assessmentPlanned = await assessmentLibrarySchema.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                    level,
                    _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                    type,
                },
                {
                    _assessment_id: 1,
                    'studentDetails._id': 1,
                    noQuestions: 1,
                    'questionMarks.outComeIds': 1,
                },
            );
            const typeData = [];
            for (typeElement of assessmentPlanning.types) {
                const subTypeData = [];
                for (subtypeElement of typeElement.subTypes) {
                    const assessmentTypeData = [];
                    if (subtypeElement.isActive)
                        for (assessmentTypeElement of subtypeElement.assessmentTypes) {
                            const planningData = [];
                            // if (assessmentTypeElement.isActive)
                            for (planningElement of assessmentTypeElement.planning) {
                                // if (planningElement.isActive) {
                                const assessmentFind = assessmentPlanned.find(
                                    (assessmentElement) =>
                                        assessmentElement._assessment_id.toString() ===
                                        planningElement._id.toString(),
                                );
                                if (assessmentFind) {
                                    planningElement.studentCount =
                                        assessmentFind.studentDetails.length;
                                    planningElement.noQuestions = assessmentFind.noQuestions || 0;
                                    planningElement.assessmentOutComes = [
                                        ...new Set(
                                            assessmentFind.questionMarks
                                                .map((questionElement) =>
                                                    questionElement.outComeIds
                                                        .map((outComeElement) =>
                                                            outComeElement.toString(),
                                                        )
                                                        .flat(),
                                                )
                                                .flat(),
                                        ),
                                    ].length;
                                }
                                planningData.push(planningElement);
                                // }
                            }
                            if (planningData.length) {
                                assessmentTypeElement.planning = planningData;
                                assessmentTypeData.push(assessmentTypeElement);
                            }
                        }
                    if (assessmentTypeData.length) {
                        subtypeElement.assessmentTypes = assessmentTypeData;
                        subTypeData.push(subtypeElement);
                    }
                }
                if (subTypeData.length) {
                    typeElement.subTypes = subTypeData;
                    typeData.push(typeElement);
                }
            }
            assessmentPlanning.types = typeData;
        }
        if (!assessmentPlanning) {
            const assessmentTypes = await assessmentManagementSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        term,
                    },
                    {
                        types: 1,
                    },
                )
                .lean();
            if (!assessmentTypes)
                return { statusCode: 404, message: 'Assessment Setting Not Found' };
            const typeData = [];
            for (const typeElement of clone(assessmentTypes.types)) {
                const subTypeData = [];
                for (const subTypeElement of typeElement.subTypes) {
                    const assessmentTypeData = [];
                    // if (subTypeElement.isActive) {
                    for (const assessmentTypeElement of subTypeElement.assessmentTypes) {
                        const planningData = [];
                        // if (assessmentTypeElement.isActive) {
                        for (const planningElement of assessmentTypeElement.planning) {
                            if (courseId && level && term) {
                                if (
                                    planningElement.isActive &&
                                    planningElement.occurring === 'course' &&
                                    planningElement.courses.find(
                                        (courseElement) =>
                                            courseElement.courseId.toString() ===
                                            courseId.toString(),
                                    )
                                ) {
                                    planningData.push(clone(planningElement));
                                }
                            } else {
                                if (
                                    planningElement.isActive &&
                                    planningElement.occurring === 'program'
                                ) {
                                    planningData.push(clone(planningElement));
                                }
                            }
                        }
                        // if (planningData.length) {
                        assessmentTypeElement.planning = planningData;
                        assessmentTypeData.push(clone(assessmentTypeElement));
                        // }
                        // }
                    }
                    // if (assessmentTypeData.length) {
                    subTypeElement.assessmentTypes = assessmentTypeData;
                    subTypeData.push(clone(subTypeElement));
                    // }
                    // }
                }
                // if (subTypeData.length) {
                typeElement.subTypes = subTypeData;
                typeData.push(clone(typeElement));
                // }
            }
            if (!typeData.length)
                return { statusCode: 404, message: 'Assessment Type Not Found', data: [] };
            await assessmentPlanningSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                term,
                level,
                _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                types: typeData,
                type,
            });
            assessmentPlanning = await assessmentPlanningSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        term,
                        level,
                        _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                        type,
                    },
                    {
                        types: 1,
                    },
                )
                .lean();
            const alteredTypeData = [];
            for (typeElement of assessmentPlanning.types) {
                const subTypeData = [];
                for (subtypeElement of typeElement.subTypes) {
                    const assessmentTypeData = [];
                    if (subtypeElement.isActive)
                        for (assessmentTypeElement of subtypeElement.assessmentTypes) {
                            const planningData = [];
                            // if (assessmentTypeElement.isActive)
                            for (planningElement of assessmentTypeElement.planning) {
                                // if (planningElement.isActive) {
                                planningData.push(planningElement);
                                // }
                            }
                            if (planningData.length) {
                                assessmentTypeElement.planning = planningData;
                                assessmentTypeData.push(assessmentTypeElement);
                            }
                        }
                    if (assessmentTypeData.length) {
                        subtypeElement.assessmentTypes = assessmentTypeData;
                        subTypeData.push(subtypeElement);
                    }
                }
                if (subTypeData.length) {
                    typeElement.subTypes = subTypeData;
                    alteredTypeData.push(typeElement);
                }
            }
            assessmentPlanning.types = alteredTypeData;
        }
        return { statusCode: 200, data: assessmentPlanning };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleAssessmentCourseProgramPlanning = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, assessments } = body;
        const assessmentPlanning = await assessmentPlanningSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    types: 1,
                    _institution_calendar_id: 1,
                    _program_id: 1,
                    term: 1,
                    level: 1,
                    _course_id: 1,
                    type: 1,
                },
            )
            .lean();
        if (!assessmentPlanning) return { statusCode: 404, message: 'NOT_FOUND' };
        // Need to check with Assessment Planning & Course-Program Area
        const assessmentIds = [];
        for (assessmentElement of assessments)
            if (!assessmentElement.status)
                assessmentIds.push(convertToMongoObjectId(assessmentElement.assessmentId));
        const assessmentPlanned = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(
                    assessmentPlanning._institution_calendar_id,
                ),
                _program_id: convertToMongoObjectId(assessmentPlanning._program_id),
                term: assessmentPlanning.term,
                level: assessmentPlanning.level,
                _course_id: assessmentPlanning._course_id
                    ? convertToMongoObjectId(assessmentPlanning._course_id)
                    : undefined,
                type: assessmentPlanning.type,
                noQuestions: { $exists: true, $ne: 0 },
                assessmentTypeId: { $in: assessmentIds },
            },
            {
                _id: 1,
            },
        );
        if (assessmentPlanned && assessmentPlanned.length)
            return {
                statusCode: 410,
                message: 'Assessment Has Mapped',
            };

        for (typeElement of assessmentPlanning.types) {
            for (subTypeElement of typeElement.subTypes) {
                for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                    const assessmentElement = assessments.find(
                        (assessmentIdElement) =>
                            assessmentIdElement.assessmentId.toString() ===
                            assessmentTypeElement._id.toString(),
                    );
                    if (assessmentElement) {
                        assessmentTypeElement.isActive = assessmentElement.status;
                    }
                }
            }
        }
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(_id),
            },
            { $set: { types: assessmentPlanning.types } },
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'NOT_UPDATED' };
        return { statusCode: 200, message: 'UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAssessmentCourseProgram = async ({ body = {}, headers = {} }) => {
    try {
        const { _id, typeId, subTypeId, assessmentTypeId, assessmentName, totalMark } = body;
        const dbUpdate = {
            $push: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning': {
                    name: assessmentName,
                    totalMark,
                    isActive: true,
                },
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [
                { 'i._id': convertToMongoObjectId(typeId) },
                { 'j._id': convertToMongoObjectId(subTypeId) },
                { 'k._id': convertToMongoObjectId(assessmentTypeId) },
            ],
        };
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT ADDED' };
        return { statusCode: 200, message: 'ASSESSMENT ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateAssessmentCourseProgram = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _id,
            typeId,
            subTypeId,
            assessmentTypeId,
            assessmentId,
            assessmentName,
            totalMark,
        } = body;

        // Check Is Total Mark Is Greater Than Assessment Library
        if (totalMark && totalMark !== 0) {
            const assessmentLibraryDatas = await assessmentLibrarySchema
                .find(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _assessment_id: convertToMongoObjectId(assessmentId),
                    },
                    { 'questionMarks.totalMark': 1 },
                )
                .lean();
            for (const assessmentLibraryElement of assessmentLibraryDatas) {
                const assessmentMark = assessmentLibraryElement.questionMarks.reduce(
                    (n, markElement) => n + markElement.totalMark,
                    0,
                );
                if (assessmentMark > totalMark)
                    return { statusCode: 410, message: 'Assessment Total Mark is Too Low' };
            }
        }
        const dbUpdate = {
            $set: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning.$[l].name': assessmentName,
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning.$[l].totalMark': totalMark,
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning.$[l].isActive': true,
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [
                { 'i._id': convertToMongoObjectId(typeId) },
                { 'j._id': convertToMongoObjectId(subTypeId) },
                { 'k._id': convertToMongoObjectId(assessmentTypeId) },
                { 'l._id': convertToMongoObjectId(assessmentId) },
            ],
        };
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT UPDATED' };
        await assessmentLibrarySchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _assessment_id: convertToMongoObjectId(assessmentId),
            },
            {
                $set: {
                    assessmentMark: totalMark,
                    assessmentName,
                },
            },
        );
        return { statusCode: 200, message: 'ASSESSMENT UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeAssessmentCourseProgram = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, typeId, subTypeId, assessmentTypeId, assessmentId } = body;
        // Check Details In Library Area
        const libraryData = await assessmentLibrarySchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _assessment_id: convertToMongoObjectId(assessmentId),
                questionMarks: { $exists: true, $ne: [] },
            },
            { _id: 1 },
        );
        if (libraryData)
            return {
                statusCode: 410,
                message: 'Assessment Has Planned in Library',
            };
        const dbUpdate = {
            $pull: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning': {
                    _id: convertToMongoObjectId(assessmentId),
                },
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [
                { 'i._id': convertToMongoObjectId(typeId) },
                { 'j._id': convertToMongoObjectId(subTypeId) },
                { 'k._id': convertToMongoObjectId(assessmentTypeId) },
            ],
        };
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT REMOVED' };
        return { statusCode: 200, message: 'ASSESSMENT REMOVED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getAssessmentCourseProgramPlanning,
    toggleAssessmentCourseProgramPlanning,
    addAssessmentCourseProgram,
    updateAssessmentCourseProgram,
    removeAssessmentCourseProgram,
};
