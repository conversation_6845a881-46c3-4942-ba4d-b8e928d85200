const joi = require('joi');
const objectIdValidator = joi
    .string()
    .alphanum()
    .length(24)
    .required()
    .error((error) => {
        return error;
    });
const stringValidator = joi
    .string()
    .required()
    .error((error) => {
        return error;
    });
exports.getCourseSessionStatusValidator = (req, res, next) => {
    const schema = joi.object({
        headers: joi
            .object({
                _institution_id: objectIdValidator,
            })
            .unknown(true),
        query: joi
            .object({
                institutionCalendarId: objectIdValidator,
                programId: objectIdValidator,
                curriculumId: objectIdValidator,
                courseId: objectIdValidator,
                term: stringValidator,
                year: stringValidator,
                level: stringValidator,
            })
            .unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const details = error.details.map((errorElement) => errorElement.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};
exports.updateCourseSessionStatusValidator = (req, res, next) => {
    const schema = joi.object({
        params: joi.object({
            sessionStatusId: objectIdValidator,
        }),
        body: joi
            .object({
                isEnabled: joi
                    .boolean()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                activeToInactive: joi
                    .string()
                    .valid('present', 'absent', 'exclude')
                    .required()
                    .error((error) => {
                        return error;
                    }),
                inactiveToActive: joi
                    .string()
                    .valid('present', 'absent', 'exclude')
                    .required()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    });
    const { error } = schema.validate({
        params: req.params,
        body: req.body,
    });
    if (error) {
        const details = error.details.map((errorElement) => errorElement.message).join(', ');
        return res.status(400).json({ error: details });
    }
    next();
};
