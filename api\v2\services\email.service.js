require('dotenv').config();
const nodemailer = require('nodemailer');
const { logger } = require('../utility/util_keys');
const { sendMessage } = require('./slack.service');

async function sendEmail({ displayName, emailId, password, smtpClient, portNumber }) {
    const email = {
        aws: {
            host: smtpClient,
            port: portNumber,
            auth: {
                user: smtpClient.includes('amazon')
                    ? process.env.SMTP_USERNAME_AWS
                    : process.env.SMTP_USERNAME_GMAIL,
                pass: smtpClient.includes('amazon')
                    ? process.env.SMTP_PASSWORD_AWS
                    : process.env.SMTP_PASSWORD_GMAIL,
            },
        },
        from: displayName + '<' + emailId + '>',
    };
    const msg = { from: email.from, to: emailId, subject: 'TEST MAIL', text: 'TEST MAIL CONTENT' };
    const awsTransport = nodemailer.createTransport(email.aws);
    return awsTransport.sendMail(msg).catch((awsErr) => {
        throw new Error(awsErr);
    });
}

async function sendStaffEmail({
    displayName,
    emailId,
    userName,
    password,
    smtpClient,
    portNumber,
    toEmail,
    subject,
    text,
}) {
    const msg = { from: emailId, to: toEmail, subject, text };
    await sendMessage(msg);
    const email = {
        aws: {
            host: smtpClient,
            port: portNumber,
            auth: {
                user: userName || emailId,
                pass: password,
            },
        },
        from: displayName + '<' + emailId + '>',
    };
    const awsTransport = nodemailer.createTransport(email.aws);
    return awsTransport.sendMail(msg).catch((awsErr) => {
        logger.error({ awsErr }, 'email -> sendEmailService -> err:');
    });
}

async function sendEmailCommon({
    displayName,
    fromEmailId,
    userName,
    password,
    smtpClient,
    portNumber,
    toEmailId,
    ttl_ssl,
    subject,
    text,
}) {
    const email = {
        host: smtpClient,
        port: portNumber,
        auth: {
            user: userName || fromEmailId,
            pass: password,
        },
        // secure: ttl_ssl,
    };
    const msg = {
        from: displayName + '<' + fromEmailId + '>',
        to: toEmailId,
        subject,
        text,
    };
    const awsTransport = nodemailer.createTransport(email);
    return awsTransport.sendMail(msg).catch((awsErr) => {
        console.log(awsErr);
        return undefined;
        // throw new Error(awsErr);
    });
}

module.exports = {
    sendEmail,
    sendStaffEmail,
    sendEmailCommon,
};
