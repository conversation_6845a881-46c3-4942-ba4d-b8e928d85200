const express = require('express');
const catchAsync = require('../utility/catch-async');
const {
    getProgramYearLevelList,
    upsertYearLevelAuthor,
    userAuthorProgramList,
    userAuthorProgramYearLevelList,
} = require('./yearLevelAuthor.controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const { getUserPrograms } = require('../session-report/session-report.controller');
const { staffList } = require('../courseInput/courseInput.controller');
const { validate } = require('../../middleware/validation');
const {
    programYearLevelListValidation,
    upsertYearLevelAuthorValidation,
    userProgramListValidation,
    userAuthorProgramListValidation,
    staffListValidation,
} = require('./yearLevelAuthor.validator');

const route = express.Router();

route.get(
    '/userProgramList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF]), validate([userProgramListValidation])],
    catchAsync(getUserPrograms),
);

route.get(
    '/staffList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF]), validate([staffListValidation])],
    catchAsync(staffList),
);

route.get(
    '/programYearLevelList',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF]),
        validate([programYearLevelListValidation]),
    ],
    catchAsync(getProgramYearLevelList),
);

route.post(
    '/',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF]),
        validate([upsertYearLevelAuthorValidation]),
    ],
    catchAsync(upsertYearLevelAuthor),
);

route.get(
    '/userAuthorProgramList',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF]),
        validate([userAuthorProgramListValidation]),
    ],
    catchAsync(userAuthorProgramList),
);

route.get(
    '/userAuthorProgramYearLevelList',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF]),
        // validate([userAuthorProgramListValidation]),
    ],
    catchAsync(userAuthorProgramYearLevelList),
);

module.exports = route;
