const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { TAG_MASTER } = require('../utility/constants');
const tagSchema = new Schema({
    code: { type: String },
    name: { type: String },
    description: { type: String },
});
const groupSchema = new Schema({
    code: { type: String },
    name: { type: String },
    description: { type: String },
    isAssigned: { type: Boolean, default: false },
    tags: [{ type: ObjectId }],
});
const familySchema = new Schema({
    code: { type: String },
    name: { type: String },
    description: { type: String },
    groups: [{ type: ObjectId }],
});
const tagMasterSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        tags: [tagSchema],
        groups: [groupSchema],
        families: [familySchema],
    },
    { timestamps: true },
);

module.exports = model(TAG_MASTER, tagMasterSchema);
