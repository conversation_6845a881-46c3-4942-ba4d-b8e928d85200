const constant = require('../utility/constants');
const { convertToMongoObjectId } = require('../utility/common');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const digiSessionOrderModuleCollection = require('mongoose').model(
    constant.DIGI_SESSION_ORDER_MODULE,
);
const digiSessionOrderCollection = require('mongoose').model(constant.DIGI_SESSION_ORDER);

async function insert(req, res) {
    try {
        req.body._institution_id = req.headers._institution_id;
        const doc = await base_control.insert(digiSessionOrderModuleCollection, req.body);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('ADDED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ADD'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function update(req, res) {
    try {
        const query = { _id: convertToMongoObjectId(req.params.id) };
        const doc = await base_control.update(digiSessionOrderModuleCollection, query, req.body);
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('UNABLE_TO_UPDATE'),
                        [],
                    ),
                );
        return res
            .status(201)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    201,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function deleteSessionOrderModule(req, res) {
    try {
        const courseSessionOrderCheck = await digiSessionOrderCollection.findOne(
            { 'session_flow_data._module_id': convertToMongoObjectId(req.params.id) },
            { _id: 1 },
        );
        if (courseSessionOrderCheck)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('UNABLE_TO_DELETE'),
                        req.t('UNABLE_TO_DELETE'),
                    ),
                );
        const query = { _id: convertToMongoObjectId(req.params.id) };
        const obj = { isDeleted: true };
        const doc = await base_control.update(digiSessionOrderModuleCollection, query, obj);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('COURSE_DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_DELETE_COURSE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function list(req, res) {
    try {
        const { courseId, programId } = req.params;
        const query = {
            isDeleted: false,
            _course_id: convertToMongoObjectId(courseId),
            _program_id: convertToMongoObjectId(programId),
        };
        const doc = await base_control.get_list(digiSessionOrderModuleCollection, query, {
            _course_id: 1,
            _program_id: 1,
            moduleName: 1,
        });
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(req, 200, true, req.t('LIST'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function getById(req, res) {
    try {
        const { id } = req.params;
        const query = { isDeleted: false, _id: convertToMongoObjectId(id) };
        const doc = await base_control.get_list(digiSessionOrderModuleCollection, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('LIST'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    res,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

module.exports = {
    insert,
    update,
    list,
    deleteSessionOrderModule,
    getById,
};
