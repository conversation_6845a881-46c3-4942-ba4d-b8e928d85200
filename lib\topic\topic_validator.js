const Joi = require('joi');
const common_files = require('../../utility/common');

exports.topic = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            label: Joi.string().alphanum().min(1).max(4).trim().error(error => {
                return error;
            }),
            content: Joi.string().allow(' ').min(3).max(250).error(error => {
                return error;
            }),
            contact_hours: Joi.number().min(0).max(12).error(error => {
                return error;
            }),
            _course_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.topic_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}