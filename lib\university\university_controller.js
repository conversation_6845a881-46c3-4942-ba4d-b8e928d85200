const university = require('./university_model');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const university_formate = require('./university_formate');
const ObjectId = require('mongodb').ObjectID;
const country = require('../models/country');
const constant = require('../utility/constants');

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COUNTRY, localField: '_country_id', foreignField: '_id', as: 'country' } },
        { $unwind: '$country' },
        { $skip: skips },
        { $limit: limits },
        { $project: { _id: 1, isDeleted: 1, name: 1, university_code: 1, location: 1, 'country._id': 1, 'country.isDeleted': 1, 'country.name': 1, 'country.code': 1, 'country.currency': 1 } }

    ];
    let doc = await base_control.get_aggregate(university, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "University list", doc.totalDoc, totalPages, Number(req.query.pageNo),/*  doc.data  */university_formate.university(doc.data));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.COUNTRY, localField: '_country_id', foreignField: '_id', as: 'country' } },
        { $unwind: '$country' },
        { $project: { _id: 1, isDeleted: 1, name: 1, university_code: 1, location: 1, 'country._id': 1, 'country.isDeleted': 1, 'country.name': 1, 'country.code': 1, 'country.currency': 1 } }
    ];
    let doc = await base_control.get_aggregate(university, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "University details", /* doc.data */university_formate.university_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let checks = await base_control.check_id(country, { _id: req.body._country_id, 'isDeleted': false });
    if (checks.status) {
        let doc = await base_control.insert(university, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "University Added successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", checks.data);
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._country_id != undefined) {
        checks = await base_control.check_id(country, { _id: { $in: req.body._country_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(university, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "University update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(university, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "University deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(university, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "university List", university_formate.university_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};