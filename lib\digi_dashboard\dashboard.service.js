const { convertToMongoObjectId, clone } = require('../utility/common');
const {
    allRoleAssignDatas,
    allCourseScheduleTillYesterday,
} = require('../../service/cache.service');
const { logger } = require('../utility/util_keys');
const { scheduleDateFormateChange } = require('../utility/common_functions');
const {
    SCHEDULE_TYPES,
    PRESENT,
    LEAVE_TYPE: { ONDUTY },
    ABSENT,
    PERMISSION,
    LEAVE,
    COMPLETED,
    ROLE_ASSIGN,
    STUDENT_CRITERIA_MANIPULATION,
    COURSE_SCHEDULE,
} = require('../utility/constants');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const courseScheduleCollection = require('mongoose').model(COURSE_SCHEDULE);
const constant = require('../utility/constants');
const lms = require('mongoose').model(constant.LMS);
const { get, get_list } = require('../base/base_controller');
// const { COMPLETED } = require('../utility/enums');
const roleAssignCollection = require('mongoose').model(ROLE_ASSIGN);

// Leave Setting Data
const lmsSettings = async () => {
    try {
        let warningAbsenceData = [
            { warning: 'Denial', absence_percentage: 101, warning_message: 'Denial' },
        ];
        const leave_category = await get(
            lms,
            {
                isDeleted: false,
            },
            { _id: 1, category: 1, student_warning_absence_calculation: 1 },
        );
        if (
            leave_category &&
            leave_category &&
            leave_category.data.student_warning_absence_calculation &&
            leave_category.data.student_warning_absence_calculation.length !== 0 &&
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ).length !== 0
        )
            warningAbsenceData = clone(
                leave_category.data.student_warning_absence_calculation.filter(
                    (ele) => ele.isDeleted === false,
                ),
            );
        warningAbsenceData = clone(
            warningAbsenceData.sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                    comparison = -1;
                } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                    comparison = 1;
                }
                return comparison;
            }),
        );
        const finaleWarning =
            warningAbsenceData.length !== 1
                ? warningAbsenceData[1] && warningAbsenceData[1].warning
                    ? warningAbsenceData[1].warning
                    : undefined
                : undefined;
        const denialWarning = warningAbsenceData[0].warning;
        return {
            warningAbsenceData,
            finaleWarning,
            denialWarning,
        };
    } catch (error) {
        logger.error(error, 'Internal Server Issue');
        throw new Error(error);
    }
};

const userRoleData = async (userId, roleId) => {
    try {
        console.time('roleAssign');
        const roleAssign = await roleAssignCollection
            .findOne({ isDeleted: false, _user_id: convertToMongoObjectId(userId) }, {})
            .lean();
        console.timeEnd('roleAssign');
        if (!roleAssign) return {};
        const userRoleData = roleAssign.roles.find(
            (ele) => ele._role_id.toString() === roleId.toString(),
        );
        const roleProgramIds =
            userRoleData && userRoleData.program && userRoleData.program.length !== 0
                ? userRoleData.program.map((ele) => ele && ele._program_id.toString())
                : [];
        return {
            status: true,
            isAdmin: userRoleData?.isAdmin ?? false,
            roleName: userRoleData?.role_name ?? '',
            data: roleProgramIds,
        };
    } catch (error) {
        logger.error(error, 'Internal Server Issue');
        throw new Error(error);
    }
};

const courseCoordinatorBasedIds = async (courseList, userId, institutionCalendar) => {
    const userCourseList = courseList.filter((ele) =>
        ele.coordinators.find(
            (ele2) =>
                ele2._user_id.toString() === userId.toString() &&
                ele2._institution_calendar_id.toString() === institutionCalendar.toString(),
        ),
    );
    let programIds = userCourseList.map((ele) => ele._program_id.toString());
    const courseIds = userCourseList.map((ele) => ele._id.toString());
    const courseWithTerm = [];
    for (eleUCL of userCourseList) {
        const coordinator = eleUCL.coordinators.filter(
            (ele2) =>
                ele2._user_id.toString() === userId.toString() &&
                ele2._institution_calendar_id.toString() === institutionCalendar.toString(),
        );
        if (coordinator) {
            for (eleC of coordinator) {
                courseWithTerm.push({
                    _program_id: eleUCL._program_id.toString(),
                    _course_id: eleUCL._id.toString(),
                    term: eleC.term,
                    level_no: eleC.level_no,
                    year: eleC.year,
                    versionNo: eleC.versionNo || 1,
                    versioned: eleC.versioned || false,
                    versionName: eleC.versionName || '',
                    versionedFrom: eleC.versionedFrom || null,
                    versionedCourseIds: eleC.versionedCourseIds || [],
                });
            }
        }
    }

    for (courseData of userCourseList) {
        for (courseAssigned of courseData.course_assigned_details) {
            for (courseShared of courseAssigned.course_shared_with) {
                programIds.push(courseShared._program_id.toString());
            }
        }
    }
    programIds = [...new Set(programIds)];
    return { programIds, courseIds, courseWithTerm };
};

const userCourseScheduleList = async (
    userId,
    institutionCalendarId,
    isAdmin,
    programIds,
    courseIds,
    programIdResult,
) => {
    const courseScheduleFilteredData =
        programIdResult &&
        (programIdResult.isAdmin === false ||
            (programIdResult.isAdmin === false &&
                programIdResult.roleName !== 'Course Coordinator'))
            ? await courseScheduleCollection.find(
                  {
                      isDeleted: false,
                      isActive: true,
                      _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                      type: SCHEDULE_TYPES.REGULAR,
                      'staffs._staff_id': convertToMongoObjectId(userId),
                  },
                  {
                      _course_id: 1,
                      _program_id: 1,
                      'staffs._staff_id': 1,
                      'staffs.staff_name': 1,
                      term: 1,
                      rotation: 1,
                      rotation_count: 1,
                  },
              )
            : (
                  await allCourseScheduleTillYesterday(
                      institutionCalendarId,
                      scheduleDateFormateChange(new Date()),
                      true,
                  )
              )
                  .filter(
                      (courseScheduleElement) =>
                          courseScheduleElement &&
                          courseScheduleElement._institution_calendar_id &&
                          courseScheduleElement._institution_calendar_id.toString() ===
                              institutionCalendarId.toString() &&
                          courseScheduleElement.type === SCHEDULE_TYPES.REGULAR &&
                          courseScheduleElement.isActive === true &&
                          courseScheduleElement.isDeleted === false &&
                          (isAdmin === true
                              ? programIds.find(
                                    (programIdElement) =>
                                        programIdElement.toString() ===
                                        courseScheduleElement._program_id.toString(),
                                )
                              : courseIds && courseIds.length !== 0
                              ? courseIds.find(
                                    (courseIdElement) =>
                                        courseIdElement.toString() ===
                                        courseScheduleElement._course_id,
                                ) ||
                                courseScheduleElement.staffs.find(
                                    (staffElement) =>
                                        staffElement._staff_id.toString() === userId.toString(),
                                )
                              : courseScheduleElement.staffs.find(
                                    (staffElement) =>
                                        staffElement._staff_id.toString() === userId.toString(),
                                )),
                  )
                  .map((courseScheduleElement) => {
                      return {
                          _id: courseScheduleElement._id,
                          _subject_id: courseScheduleElement.subjects.map((ele) => ele._subject_id),
                          _program_id: courseScheduleElement._program_id,
                          term: courseScheduleElement.term,
                          level_no: courseScheduleElement.level_no,
                          rotation: courseScheduleElement.rotation,
                          rotation_count: courseScheduleElement.rotation_count,
                          students: courseScheduleElement.students,
                          session: courseScheduleElement.session,
                          status: courseScheduleElement.status,
                          _course_id: courseScheduleElement._course_id,
                          _session_id:
                              courseScheduleElement.session &&
                              courseScheduleElement.session._session_id
                                  ? courseScheduleElement.session._session_id
                                  : undefined,
                          staffs: courseScheduleElement.staffs,
                      };
                  });
    const courseStaff = [];
    const scheduleCourseTerm = [];
    for (scheduleElement of courseScheduleFilteredData) {
        const ind = scheduleCourseTerm.findIndex(
            (ele) =>
                ele._course_id &&
                ele._course_id.toString() === scheduleElement._course_id.toString() &&
                ele.term === scheduleElement.term &&
                (ele.rotation && ele.rotation === 'yes'
                    ? ele.rotation_count === scheduleElement.rotation_count
                    : true),
        );
        if (ind == -1) {
            scheduleCourseTerm.push({
                _course_id: scheduleElement._course_id.toString(),
                term: scheduleElement.term,
                rotation: scheduleElement.rotation,
                rotation_count: scheduleElement.rotation_count,
            });
        }
        const loc = courseStaff.findIndex(
            (ele) =>
                ele &&
                ele._course_id &&
                ele._course_id.toString() === scheduleElement._course_id.toString(),
        );
        if (loc === -1) {
            courseStaff.push(scheduleElement);
        } else {
            courseStaff[loc].staffs = [...courseStaff[loc].staffs, ...scheduleElement.staffs];
        }
        for (cElement of courseStaff) {
            cElement.staffs = cElement.staffs.filter(
                (item, index) =>
                    cElement.staffs.findIndex(
                        (ele) => ele._staff_id.toString() === item._staff_id.toString(),
                    ) === index,
            );
        }
    }
    return { scheduleCourseTerm, courseScheduleFilteredData, courseStaff };
};

const courseCreditContactHoursCalculation = (
    sessionDeliveryTypes,
    _program_id,
    courseScheduled,
    sessionOrderData,
    credit_hours,
    studentId,
) => {
    const credit_hoursData = [];
    const sessionDeliveryTypesData = sessionDeliveryTypes.filter(
        (ele) => ele._program_id.toString() === _program_id.toString(),
    );
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        const deliveryCredit = credit_hours.find(
            (ele) =>
                ele.type_name === sessionTypeElement.session_name &&
                ele.type_symbol === sessionTypeElement.session_symbol,
        );
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        let sessionCompletedHours = 0;
        let endSchedule = 0;
        let scheduleSessionCount = 0;
        let attendanceCount = 0;
        // let endSchedule = [];
        if (deliveryTypeData.length) {
            // Altering Based on Scheduled Session SG wise
            for (sessionOrderElement of sessionOrderData) {
                const sessionSchedule = deliveryTypeData.filter(
                    (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
                );
                if (sessionSchedule.length > 0) scheduleSessionCount++;
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                ) {
                    if (studentId) {
                        attendanceCount += sessionSchedule.filter(
                            (ele2) =>
                                ele2 &&
                                ele2.students &&
                                ele2.students.find(
                                    (ele) =>
                                        ele &&
                                        ele._id.toString() === studentId.toString() &&
                                        (ele.status === PRESENT || ele.status === ONDUTY),
                                ),
                        ).length;
                    }
                    endSchedule++;
                    sessionCompletedHours += sessionOrderElement.duration;
                }
            }
        }
        const sessionCompletedCredit = sessionCompletedHours !== 0 ? sessionCompletedHours / 60 : 0;
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            credit_hours:
                deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
            completed_credit_hours:
                sessionCompletedCredit /
                    parseInt(sessionTypeElement.contact_hour_per_credit_hour) || 0,
            completed_contact_hours: sessionCompletedCredit || 0,
            contact_hours:
                parseInt(
                    deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
                ) * parseInt(sessionTypeElement.contact_hour_per_credit_hour),
            // no_of_sessions: deliveryTypeData.length,
            no_of_sessions: scheduleSessionCount,
            completed_sessions: endSchedule,
            present_count: attendanceCount,
            // completed_sessions: endSchedule.length,
        });
    }
    return credit_hoursData;
};

const studentAttendanceReport = (
    courseStudentIds,
    courseScheduled,
    warningAbsenceData,
    studentCriteriaData,
) => {
    const studentData = [];
    for (studentElement of courseStudentIds) {
        const studentSchedule = courseScheduled.filter(
            (ele) =>
                ele &&
                ele.students.find((ele2) => ele2._id.toString() === studentElement.toString()),
        );
        // const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
        const absentSchedules = studentSchedule.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === PERMISSION ||
                            ele2.status === LEAVE),
                ),
        );
        const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
        studentElement.absence = denialPercentage.toFixed(2);
        const studentWarningAbsence = clone(warningAbsenceData);
        if (studentCriteriaData.status != false) {
            const studentManipulation = studentCriteriaData.data.find(
                (absenceElement) =>
                    absenceElement.studentId.toString() === studentElement.toString(),
            );
            studentElement.manipulationStatus = false;
            studentElement.manipulationPercentage = 0;
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].absence_percentage &&
                studentWarningAbsence[0].absence_percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].absence_percentage = studentManipulation.absencePercentage;
                studentElement.manipulationStatus = true;
                studentElement.manipulationPercentage = studentManipulation.absencePercentage;
            }
        }
        const warningData = studentWarningAbsence.find(
            (ele) =>
                ele.absence_percentage &&
                parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
        );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.warning : '',
        });
    }
    return studentData;
};

const programWiseYearLevelData = async ({
    programCalendarData,
    programCourseData,
    programId,
    courseIds,
    isAdmin,
    roleName,
    scheduleCourseTerm,
    courseWithTerm,
}) => {
    let completeCourseLists = [];
    let courseCount = 0;
    for (pcData of programCalendarData) {
        for (pcLevel of pcData.level) {
            delete pcLevel.events;
            const courseLists = [];
            if (pcLevel.rotation === 'yes') {
                for (rotationCourse of pcLevel.rotation_course) {
                    delete rotationCourse.events;
                    for (pcCourse of rotationCourse.course) {
                        if (
                            (isAdmin === false &&
                                courseIds.find(
                                    (courseIdElement) =>
                                        courseIdElement.toString() ===
                                        pcCourse._course_id.toString(),
                                )) ||
                            isAdmin === true
                        ) {
                            delete pcCourse.courses_events;
                            delete pcCourse.credit_hours;
                            delete pcCourse.color_code;
                            // Shared Details Adding
                            const sCourse = clone(
                                programCourseData.find(
                                    (uCourse) =>
                                        uCourse._id.toString() === pcCourse._course_id.toString(),
                                ),
                            );
                            pcCourse.course_shared =
                                sCourse._program_id.toString() !== pcData._program_id.toString();

                            pcCourse.course_shared_program = pcCourse.course_shared
                                ? sCourse.course_assigned_details.find(
                                      (cad) =>
                                          cad._program_id.toString() ===
                                          sCourse._program_id.toString(),
                                  ).program_name
                                : '' /* userProgram.name */;
                            // Adding Admin course key
                            pcCourse.isAdmin = isAdmin
                                ? true
                                : !!(
                                      roleName === 'Course Coordinator' &&
                                      courseWithTerm.find(
                                          (courseWithTermElement) =>
                                              courseWithTermElement._course_id.toString() ===
                                                  pcCourse._course_id.toString() &&
                                              courseWithTermElement.term === pcLevel.term,
                                      )
                                  );
                            pcCourse._program_id = pcData._program_id.toString();
                            pcCourse.level = pcLevel.level_no;
                            pcCourse.year = pcLevel.year;
                            pcCourse.term = pcLevel.term;
                            pcCourse.curriculum = pcLevel.curriculum;
                            pcCourse.rotation = 'yes';
                            pcCourse.rotation_count = rotationCourse.rotation_count;
                            //version keys added
                            pcCourse.versionNo = sCourse.versionNo || 1;
                            pcCourse.versioned = sCourse.versioned || false;
                            pcCourse.versionName = sCourse.versionName || '';
                            pcCourse.versionedFrom = sCourse.versionedFrom || null;
                            pcCourse.versionedCourseIds = sCourse.versionedCourseIds || [];
                            courseLists.push(pcCourse);
                        }
                    }
                    // rotationCourse.course = courseLists;
                    // rotationCourses.push(rotationCourse);
                }
                delete pcLevel.rotation_course;
            } else {
                for (pcCourse of pcLevel.course) {
                    if (
                        (isAdmin === false &&
                            courseIds.find(
                                (courseIdElement) =>
                                    courseIdElement.toString() === pcCourse._course_id.toString(),
                            )) ||
                        isAdmin === true
                    ) {
                        delete pcCourse.courses_events;
                        delete pcCourse.credit_hours;
                        delete pcCourse.color_code;
                        // Shared Details Adding
                        const sCourse = clone(
                            programCourseData.find(
                                (uCourse) =>
                                    uCourse._id.toString() === pcCourse._course_id.toString(),
                            ),
                        );
                        pcCourse.course_shared = sCourse
                            ? sCourse._program_id.toString() !== pcData._program_id.toString()
                            : false;
                        pcCourse.course_shared_program = sCourse
                            ? pcCourse.course_shared
                                ? sCourse.course_assigned_details.find(
                                      (cad) =>
                                          cad._program_id.toString() ===
                                          sCourse._program_id.toString(),
                                  ).program_name
                                : '' /* userProgram.name */
                            : '';
                        // Adding Admin course key
                        pcCourse.isAdmin = isAdmin
                            ? true
                            : !!(
                                  roleName === 'Course Coordinator' &&
                                  courseWithTerm.find(
                                      (courseWithTermElement) =>
                                          courseWithTermElement._course_id.toString() ===
                                              pcCourse._course_id.toString() &&
                                          courseWithTermElement.term === pcLevel.term,
                                  )
                              );
                        pcCourse._program_id = pcData._program_id.toString();
                        pcCourse.level = pcLevel.level_no;
                        pcCourse.year = pcLevel.year;
                        pcCourse.term = pcLevel.term;
                        pcCourse.curriculum = pcLevel.curriculum;
                        //version keys added
                        pcCourse.versionNo = sCourse.versionNo || 1;
                        pcCourse.versioned = sCourse.versioned || false;
                        pcCourse.versionName = sCourse.versionName || '';
                        pcCourse.versionedFrom = sCourse.versionedFrom || null;
                        pcCourse.versionedCourseIds = sCourse.versionedCourseIds || [];
                        courseLists.push(pcCourse);
                        // }
                    }
                }
            }

            pcLevel.course = courseLists.filter(
                (courseElement) =>
                    (isAdmin === false &&
                        (roleName === 'Course Coordinator'
                            ? courseWithTerm.find(
                                  (courseTermElement) =>
                                      courseTermElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseTermElement.term === pcLevel.term &&
                                      courseTermElement.level_no === pcLevel.level_no,
                              ) /* courseIds.find(
                                  (courseIdElement) =>
                                      courseIdElement.toString() ===
                                      courseElement._course_id.toString(),
                              ) */ ||
                              scheduleCourseTerm.find(
                                  (courseIdElement) =>
                                      courseIdElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseIdElement.term === courseElement.term &&
                                      (courseElement.rotation === 'yes'
                                          ? courseElement.rotation_count.toString() ===
                                            courseIdElement.rotation_count.toString()
                                          : true),
                              )
                            : scheduleCourseTerm.find(
                                  (courseIdElement) =>
                                      courseIdElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseIdElement.term === courseElement.term &&
                                      (courseElement.rotation === 'yes'
                                          ? courseElement.rotation_count.toString() ===
                                            courseIdElement.rotation_count.toString()
                                          : true),
                              ))) ||
                    isAdmin === true,
            );
            completeCourseLists = [...completeCourseLists, ...pcLevel.course];
            if (pcData._program_id.toString() === programId.toString())
                courseCount += pcLevel.course.length;
        }
    }
    const programPC = programCalendarData.find(
        (pcElement) => pcElement._program_id.toString() === programId.toString(),
    );
    const curriculumDatas =
        programPC && programPC.level
            ? [
                  ...new Set(
                      programPC.level.map((levelElement) => levelElement.curriculum.toString()),
                  ),
              ]
            : [];
    const response = {
        response: {
            curriculumCount: curriculumDatas.length,
            yearCount:
                programPC && programPC.level
                    ? Math.max(
                          ...programPC.level
                              .map((levelElement) => levelElement.year.toString())
                              .map((year) => parseInt(year.split('year')[1])),
                      )
                    : '0',
            levelCount:
                programPC && programPC.level
                    ? Math.max(
                          ...programPC.level
                              .map((levelElement) => levelElement.level_no.toString())
                              .map((level) => parseInt(level.split('Level ')[1])),
                      )
                    : '0',
            courseCount,
            yearLevelCourses: programPC && programPC.level ? programPC.level : [],
        },
        completeCourseLists,
    };
    return response;
};

const programWiseDepartmentSubjectData = async ({
    programId,
    courseStaff,
    departmentSubjectDatas,
    courseWithResponseData,
    programDepartmentSubjectDatas,
    programCourseData,
    userData,
    programList,
}) => {
    const sharedSubject = [];
    const sharedDepart = [];
    for (departmentElement of departmentSubjectDatas) {
        departmentElement.shared = false;
        const subjectData = departmentElement.subject.filter(
            (subjectElement) => subjectElement.isDeleted === false,
        );
        for (subjectElement of subjectData) {
            if (subjectElement.shared_with && subjectElement.shared_with.length !== 0) {
                sharedSubject.push({
                    program_id: departmentElement.program_id,
                    program_name: departmentElement.program_name,
                    department_id: departmentElement._id,
                    department_name: departmentElement.department_name,
                    subject_id: subjectElement._id,
                    subject_name: subjectElement.subject_name,
                    shared_with: subjectElement.shared_with,
                    shared: false,
                    isDeleted: subjectElement.isDeleted,
                });
            }
        }
        // if (
        // programIds.findIndex(
        //     (ele) => ele && ele.toString() === departmentElement.program_id.toString(),
        // ) !== -1
        // ) {
        departmentElement.subject.forEach((ele2) => {
            ele2.shared = false;
        });
        departmentElement.shared_with.forEach((ele3) => {
            if (
                departmentElement.program_id !== programId
                    ? programId === ele3.program_id.toString()
                    : true
            )
                sharedDepart.push({
                    _id: departmentElement._id,
                    isActive: departmentElement.isActive,
                    program_id: ele3.program_id,
                    program_name: ele3.program_name,
                    department_share: true,
                    shared_from_program_id: departmentElement.program_id,
                    shared_from_program_name: departmentElement.program_name,
                    department_name: departmentElement.department_name,
                    subject: departmentElement.subject,
                    shared_with: departmentElement.shared_with,
                    shared: true,
                });
        });
        // }
    }
    let programDepartmentSubjectDatasWithShare = [
        ...programDepartmentSubjectDatas,
        ...sharedDepart,
    ];
    programDepartmentSubjectDatasWithShare = clone(programDepartmentSubjectDatasWithShare);
    for (departmentElement of programDepartmentSubjectDatasWithShare) {
        for (sharedSubjectElement of sharedSubject) {
            const datas = clone(departmentElement);
            if (
                !datas.department_share &&
                sharedSubjectElement.shared_with.findIndex(
                    (i) =>
                        i.program_id.toString() == datas.program_id.toString() &&
                        i.department_id.toString() == datas._id.toString(),
                ) != -1
            ) {
                departmentElement.subject.push({
                    _id: sharedSubjectElement.subject_id,
                    subject_name: sharedSubjectElement.subject_name,
                    subject_shared_from_program_id: sharedSubjectElement.program_id,
                    subject_shared_from_program_name: sharedSubjectElement.program_name,
                    isDeleted: sharedSubjectElement.isDeleted,
                    shared: true,
                });
            }
        }
    }

    // Department Subject Course Listing
    const mergeUserCourse = (course) => {
        const reqCourse = clone(course);
        delete reqCourse.course_assigned_details;
        // delete reqCourse.administration;
        delete reqCourse.participating;
        delete reqCourse.coordinators;
        const sCourse = clone(
            courseWithResponseData.completeCourseLists.filter(
                (uCourse) => uCourse._course_id.toString() === reqCourse._id.toString(),
            ),
        );
        for (courseElements of sCourse) {
            const courseProgramName = departmentSubjectDatas.find(
                (deptElement) =>
                    courseElements.courseProgramId &&
                    deptElement.program_id.toString() === courseElements.courseProgramId.toString(),
            );
            if (courseProgramName)
                reqCourse.administration.program_name = courseProgramName.program_name;
        }
        if (sCourse.length !== 1) {
            const rotationCourse = [];
            for (crs of sCourse) rotationCourse.push({ ...crs, ...reqCourse });
            return rotationCourse;
        }
        const courseElement = { ...sCourse[0], ...reqCourse };
        return courseElement;
    };
    const subjectCourses = JSON.parse(JSON.stringify(programDepartmentSubjectDatasWithShare));
    let adminCourseIds = [];
    for (const [deptIndex, DeptSub] of programDepartmentSubjectDatasWithShare.entries()) {
        adminCourseIds = [];
        for (const [subIndex, subject] of DeptSub.subject.entries()) {
            SelectedCourses = [];
            for (course of programCourseData) {
                if (course && course.administration && course.administration._program_id)
                    course.administration.program_name = programList.find(
                        (programListElement) =>
                            programListElement._id.toString() === course._program_id.toString(),
                    )
                        ? programList.find(
                              (programListElement) =>
                                  programListElement._id.toString() ===
                                  course._program_id.toString(),
                          ).name
                        : course.administration.program_name;
                if (
                    programId.toString() !== DeptSub.program_id.toString()
                        ? true
                        : course._program_id.toString() === DeptSub.program_id.toString()
                ) {
                    const staffLoc = courseStaff.findIndex(
                        (ele) =>
                            ele._program_id.toString() === DeptSub.program_id.toString() &&
                            ele._course_id.toString() === course._id.toString(),
                    );
                    course.staffs = staffLoc !== -1 ? courseStaff[staffLoc].staffs : [];
                    if (
                        course.administration &&
                        course.administration._subject_id.toString() === subject._id.toString()
                    ) {
                        course.AdminCourse = true;
                        course.participatingCourse = false;
                        const mergedCourse = mergeUserCourse(course);
                        if (Array.isArray(mergedCourse)) {
                            for (mergedCourseElement of mergedCourse) {
                                adminCourseIds.push(mergedCourseElement._course_id.toString());
                                SelectedCourses.push(mergedCourseElement);
                            }
                        } else {
                            adminCourseIds.push(mergedCourse._course_id.toString());
                            SelectedCourses.push(mergedCourse);
                        }
                    } else {
                        if (course.participating) {
                            for (participating of course.participating) {
                                if (
                                    participating &&
                                    participating._subject_id.toString() === subject._id.toString()
                                ) {
                                    course.AdminCourse = false;
                                    course.participatingCourse = true;
                                    const mergedCourse = mergeUserCourse(course);
                                    if (Array.isArray(mergedCourse)) {
                                        for (mergedCourseElement of mergedCourse) {
                                            SelectedCourses.push(mergedCourseElement);
                                        }
                                    } else {
                                        SelectedCourses.push(mergedCourse);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            subjectCourses[deptIndex].subject[subIndex].courses = SelectedCourses;
        }
    }
    // return { departmentSubjectCourses: subjectCourses };
    const subjectShared = [];
    for (DeptSub of subjectCourses) {
        const sharedSubjects = JSON.parse(
            JSON.stringify(DeptSub.subject.filter((ele) => ele.subject_shared_from_program_id)),
        );
        if (sharedSubjects.length !== 0)
            subjectShared.push({
                _id: DeptSub._id,
                isActive: DeptSub.isActive,
                program_id: DeptSub.program_id,
                program_name: DeptSub.program_name,
                department_name: DeptSub.department_name,
                subject: sharedSubjects,
                shared_with: DeptSub.shared_with,
                shared: DeptSub.shared,
            });
    }
    for (sharedElement of subjectShared) {
        for (sharedSubjectElement of sharedElement.subject) {
            const sharedPrograms = JSON.parse(
                JSON.stringify(
                    subjectCourses.filter(
                        (ele) =>
                            ele.program_id.toString() ===
                                sharedSubjectElement.subject_shared_from_program_id.toString() &&
                            ele.subject.some(
                                (subjectShareElement) =>
                                    subjectShareElement._id.toString() ===
                                    sharedSubjectElement._id.toString(),
                            ),
                    ),
                ),
            );
            if (sharedPrograms && sharedPrograms.subject)
                for (ele1 of sharedPrograms.subject) {
                    const deptLoc = subjectCourses.findIndex(
                        (ele2) => ele2._id.toString() === ele1._id.toString(),
                    );
                    const subLoc = subjectCourses[deptLoc].subject.findIndex(
                        (ele3) => ele3._id.toString() === sharedSubjectElement._id.toString(),
                    );
                    subjectCourses[deptLoc].subject[subLoc].courses = [
                        ...subjectCourses[deptLoc].subject[subLoc].courses,
                        ...sharedSubjectElement.courses,
                    ];
                }
        }
    }
    // return { departmentSubjectCourses: subjectCourses };
    const sharedWith = JSON.parse(
        JSON.stringify(subjectCourses.filter((ele) => ele.shared_from_program_name)),
    );
    for (departmentElement of sharedWith) {
        const shareInd = subjectCourses.findIndex(
            (ele2) => ele2._id.toString() === departmentElement._id.toString(),
        );
        if (shareInd !== -1) {
            for (subElement of departmentElement.subject) {
                if (subElement.courses.length !== 0) {
                    const subShareInd = subjectCourses[shareInd].subject.findIndex(
                        (ele3) => ele3._id.toString() === subElement._id.toString(),
                    );
                    if (subShareInd !== -1) {
                        for (element of subElement.courses) {
                            // element._program_id = subjectCourses[shareInd].program_id;
                            if (
                                !subjectCourses[shareInd].subject[subShareInd].courses.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() ===
                                            element._course_id.toString() &&
                                        courseElement.term === element.term,
                                )
                            )
                                subjectCourses[shareInd].subject[subShareInd].courses.push(element);
                        }
                    }
                }
            }
        }
    }
    if (userData) {
        const subjectIds = userData.academic_allocation
            .map((subElement) => subElement._department_subject_id)
            .flat();
        const departmentCourses = [];
        for (departmentSubject of subjectCourses) {
            const subjectCourses = [];
            for (departmentSubjectElement of departmentSubject.subject) {
                if (
                    subjectIds.find(
                        (idsElement) =>
                            idsElement.toString() === departmentSubjectElement._id.toString(),
                    ) &&
                    departmentSubjectElement.courses.length
                )
                    subjectCourses.push(departmentSubjectElement);
            }
            if (subjectCourses.length) {
                departmentSubject.subject = subjectCourses;
                departmentCourses.push(departmentSubject);
            }
        }
        return departmentCourses;
    }
    return subjectCourses;
};

const getIsConfigured = ({
    DSettings,
    _level_id,
    _program_id,
    _course_id,
    term,
    rotation_count,
}) => {
    let returnable = false;
    for (DSettingElement of DSettings) {
        if (
            DSettingElement.settings._program_id.toString() === _program_id.toString() &&
            DSettingElement.settings._level_id.toString() === _level_id.toString() &&
            DSettingElement.settings._course_id.toString() === _course_id.toString() &&
            DSettingElement.settings.term.toString() === term.toString() &&
            (rotation_count && DSettingElement.settings.rotation_count
                ? DSettingElement.settings.rotation_count.toString() === rotation_count.toString()
                : true)
        )
            returnable = DSettingElement.settings.isConfigured;
    }
    return returnable;
};

const getIsConfiguredDepartment = ({
    DeptSettings,
    _department_id,
    _subject_id,
    _program_id,
    _course_id,
    term,
    rotation_count,
}) => {
    let returnable = false;
    DeptSettings.forEach((DeptSettingElement) => {
        DeptSettingElement.settings.forEach((setting) => {
            if (
                setting._department_id &&
                setting._department_id.toString() === _department_id.toString() &&
                setting._program_id.toString() === _program_id.toString() &&
                setting._subject_id.toString() === _subject_id.toString() &&
                setting._course_id.toString() === _course_id.toString() &&
                setting.term.toString() === term.toString() &&
                (rotation_count && setting.rotation_count
                    ? setting.rotation_count.toString() === rotation_count.toString()
                    : true)
            ) {
                returnable = setting.isConfigured;
            }
        });
    });
    return returnable;
};

const yearLevelData = async ({
    programCalendarData,
    programCourseData,
    DSettings,
    courseIds,

    // Department Subject Datas
    programIds,
    departmentSubjectDatas,
    programDepartmentSubjectDatas,
    DeptSettings,
    isAdmin,
    roleName,
    scheduleCourseTerm,
    courseWithTerm,
    userData,
    programList,
}) => {
    let completeCourseLists = [];
    const programCourseDatas = [];
    for (pcElement of programCalendarData) {
        const programId = pcElement._program_id.toString();
        for (pcLevel of pcElement.level) {
            delete pcLevel.events;
            const courseLists = [];
            if (pcLevel.rotation === 'yes') {
                for (rotationCourse of pcLevel.rotation_course) {
                    delete rotationCourse.events;
                    for (pcCourse of rotationCourse.course) {
                        if (
                            (isAdmin === false &&
                                courseIds.find(
                                    (courseIdElement) =>
                                        courseIdElement.toString() ===
                                        pcCourse._course_id.toString(),
                                )) ||
                            isAdmin === true
                        ) {
                            pcCourse.courseProgramId = pcCourse._program_id;
                            pcCourse._program_id = programId;
                        }
                        pcCourse.isConfigured = getIsConfigured({
                            DSettings,
                            _level_id: pcLevel._id,
                            _program_id: pcLevel._program_id,
                            _course_id: pcCourse._course_id,
                            term: pcLevel.term,
                            rotation_count: rotationCourse.rotation_count,
                        });
                        delete pcCourse.color_code;
                        delete pcCourse.courses_events;
                        delete pcCourse.credit_hours;
                        delete pcCourse.model;
                        delete pcCourse.start_date;
                        delete pcCourse.end_date;
                        delete pcCourse.start_week;
                        delete pcCourse.end_week;
                        delete pcCourse._batch_course_id;
                        const courseVersionedDetails = programCourseData.find(
                            (programCourseDataElement) =>
                                programCourseDataElement._id.toString() ===
                                pcCourse._course_id.toString(),
                        );
                        courseLists.push({
                            ...pcCourse,
                            versionNo:
                                (courseVersionedDetails && courseVersionedDetails.versionNo) || 1,
                            versioned:
                                (courseVersionedDetails && courseVersionedDetails.versioned) ||
                                false,
                            versionName:
                                (courseVersionedDetails && courseVersionedDetails.versionName) ||
                                '',
                            versionedFrom:
                                (courseVersionedDetails && courseVersionedDetails.versionedFrom) ||
                                null,
                            versionedCourseIds:
                                (courseVersionedDetails &&
                                    courseVersionedDetails.versionedCourseIds) ||
                                [],
                            ...{
                                term: pcLevel.term,
                                // year: pcLevel.year,
                                // level_no: pcLevel.level_no,
                                // curriculum: pcLevel.curriculum,
                                rotation: pcLevel.rotation,
                                rotation_count: rotationCourse.rotation_count,
                            },
                        });
                    }
                }
                delete pcLevel.rotation_course;
            } else {
                for (pcCourse of pcLevel.course) {
                    if (
                        (isAdmin === false &&
                            courseIds.find(
                                (courseIdElement) =>
                                    courseIdElement.toString() === pcCourse._course_id.toString(),
                            )) ||
                        isAdmin === true
                    ) {
                        pcCourse.courseProgramId = pcCourse._program_id;
                        pcCourse._program_id = programId;
                    }
                    pcCourse.isConfigured = getIsConfigured({
                        DSettings,
                        _level_id: pcLevel._id,
                        _program_id: pcLevel._program_id,
                        _course_id: pcCourse._course_id,
                        term: pcLevel.term,
                    });
                    delete pcCourse.courses_events;
                    delete pcCourse.color_code;
                    delete pcCourse.credit_hours;
                    delete pcCourse.model;
                    delete pcCourse.start_date;
                    delete pcCourse.end_date;
                    delete pcCourse._batch_course_id;
                    const courseVersionedDetails = programCourseData.find(
                        (programCourseDataElement) =>
                            programCourseDataElement._id.toString() ===
                            pcCourse._course_id.toString(),
                    );
                    courseLists.push({
                        ...pcCourse,
                        versionNo:
                            (courseVersionedDetails && courseVersionedDetails.versionNo) || 1,
                        versioned:
                            (courseVersionedDetails && courseVersionedDetails.versioned) || false,
                        versionName:
                            (courseVersionedDetails && courseVersionedDetails.versionName) || '',
                        versionedFrom:
                            (courseVersionedDetails && courseVersionedDetails.versionedFrom) ||
                            null,
                        versionedCourseIds:
                            (courseVersionedDetails && courseVersionedDetails.versionedCourseIds) ||
                            [],
                        ...{
                            term: pcLevel.term,
                            // year: pcLevel.year,
                            // level_no: pcLevel.level_no,
                            // curriculum: pcLevel.curriculum,
                        },
                    });
                }
            }
            pcLevel.course = courseLists.filter(
                (courseElement) =>
                    (isAdmin === false &&
                        (roleName === 'Course Coordinator'
                            ? courseWithTerm.find(
                                  (courseTermElement) =>
                                      courseTermElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseTermElement.term === pcLevel.term &&
                                      courseTermElement.level_no === pcLevel.level_no,
                              ) /* courseIds.find(
                                (courseIdElement) =>
                                    courseIdElement.toString() ===
                                    courseElement._course_id.toString(),
                            ) */ ||
                              scheduleCourseTerm.find(
                                  (courseIdElement) =>
                                      courseIdElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseIdElement.term === courseElement.term &&
                                      (courseElement.rotation === 'yes'
                                          ? courseElement.rotation_count.toString() ===
                                            courseIdElement.rotation_count.toString()
                                          : true),
                              )
                            : scheduleCourseTerm.find(
                                  (courseIdElement) =>
                                      courseIdElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseIdElement.term === courseElement.term &&
                                      (courseElement.rotation === 'yes'
                                          ? courseElement.rotation_count.toString() ===
                                            courseIdElement.rotation_count.toString()
                                          : true),
                              ))) ||
                    isAdmin === true,
            );
            completeCourseLists = [...completeCourseLists, ...pcLevel.course];
            pcLevel.isConfigured = !!courseLists.find(
                (courseElement) => courseElement.isConfigured === true,
            );
            delete pcLevel.curriculum;
            delete pcLevel.rotation;
            delete pcLevel.rotation_count;
            delete pcLevel.rotation_course;
            delete pcLevel.rotation_course;
            delete pcLevel.start_date;
            delete pcLevel.end_date;
        }
        if (
            programIds.find(
                (programIdElement) =>
                    programIdElement.toString() === pcElement._program_id.toString(),
            )
        )
            programCourseDatas.push({
                _id: pcElement._id,
                _program_id: pcElement._program_id,
                programName:
                    programList.find(
                        (programListElement) =>
                            programListElement._id.toString() === pcElement._program_id.toString(),
                    ).name || '',
                level: pcElement.level,
            });
    }
    // return { programCalendarData, programCourseDatas, completeCourseLists };
    // Department Subjects
    const sharedSubject = [];
    const sharedDepart = [];
    for (departmentElement of departmentSubjectDatas) {
        departmentElement.shared = false;
        const subjectData = departmentElement.subject.filter(
            (subjectElement) => subjectElement.isDeleted === false,
        );
        for (subjectElement of subjectData) {
            delete subjectElement.isDeleted;
            delete subjectElement.isActive;
            if (subjectElement.shared_with && subjectElement.shared_with.length !== 0) {
                sharedSubject.push({
                    program_id: departmentElement.program_id,
                    program_name: departmentElement.program_name,
                    department_id: departmentElement._id,
                    department_name: departmentElement.department_name,
                    subject_id: subjectElement._id,
                    subject_name: subjectElement.subject_name,
                    shared_with: subjectElement.shared_with,
                    shared: false,
                    isDeleted: subjectElement.isDeleted,
                });
            }
        }
        // if (
        //     programIds.findIndex(
        //         (ele) => ele && ele.toString() === departmentElement.program_id.toString(),
        //     ) !== -1
        // ) {
        departmentElement.subject.forEach((ele2) => {
            ele2.shared = false;
        });
        departmentElement.shared_with.forEach((ele3) => {
            // if (
            //     programIds.findIndex((programIdElement) =>
            //         programIdElement.toString() !== departmentElement.program_id
            //             ? programIdElement.toString() === ele3.program_id.toString()
            //             : true,
            //     )
            // )
            sharedDepart.push({
                _id: departmentElement._id,
                program_id: ele3.program_id,
                program_name: ele3.program_name,
                department_share: true,
                shared_from_program_id: departmentElement.program_id,
                shared_from_program_name: departmentElement.program_name,
                department_name: departmentElement.department_name,
                subject: departmentElement.subject,
                shared_with: departmentElement.shared_with,
                shared: true,
            });
        });
        // }
    }
    let programDepartmentSubjectDatasWithShare = [
        ...programDepartmentSubjectDatas,
        ...sharedDepart,
    ];
    programDepartmentSubjectDatasWithShare = clone(programDepartmentSubjectDatasWithShare);
    for (departmentElement of programDepartmentSubjectDatasWithShare) {
        for (sharedSubjectElement of sharedSubject) {
            const datas = clone(departmentElement);
            if (
                !datas.department_share &&
                sharedSubjectElement.shared_with.findIndex(
                    (i) =>
                        i.program_id.toString() == datas.program_id.toString() &&
                        i.department_id.toString() == datas._id.toString(),
                ) != -1
            ) {
                departmentElement.subject.push({
                    _id: sharedSubjectElement.subject_id,
                    subject_name: sharedSubjectElement.subject_name,
                    subject_shared_from_program_id: sharedSubjectElement.program_id,
                    subject_shared_from_program_name: sharedSubjectElement.program_name,
                    isDeleted: sharedSubjectElement.isDeleted,
                    shared: true,
                });
            }
        }
    }

    // Department Subject Course Listing
    const mergeUserCourse = (course) => {
        const reqCourse = clone(course);
        delete reqCourse.course_assigned_details;
        delete reqCourse.administration;
        delete reqCourse.participating;
        delete reqCourse.coordinators;
        delete reqCourse.course_name;
        delete reqCourse.course_code;
        delete reqCourse.course_type;
        const sCourse = clone(
            completeCourseLists.filter(
                (uCourse) => uCourse._course_id.toString() === reqCourse._id.toString(),
            ),
        );
        if (sCourse.length !== 1) {
            const rotationCourse = [];
            for (crs of sCourse) rotationCourse.push({ ...crs, ...reqCourse });
            return rotationCourse;
        }
        const courseElement = { ...sCourse[0], ...reqCourse };
        return courseElement;
    };
    const subjectCourses = JSON.parse(JSON.stringify(programDepartmentSubjectDatasWithShare));
    let adminCourseIds = [];
    for (const [deptIndex, DeptSub] of programDepartmentSubjectDatasWithShare.entries()) {
        adminCourseIds = [];
        for (const [subIndex, subject] of DeptSub.subject.entries()) {
            SelectedCourses = [];
            for (course of programCourseData) {
                if (
                    // programIds.find((programIdElement) =>
                    //     programIdElement.toString() !== DeptSub.program_id.toString()
                    //         ? true
                    //         : course._program_id.toString() === DeptSub.program_id.toString(),
                    // )
                    course &&
                    course.administration &&
                    course.administration._program_id.toString() === DeptSub.program_id.toString()
                ) {
                    if (
                        course.administration &&
                        course.administration._subject_id.toString() === subject._id.toString()
                    ) {
                        course.AdminCourse = true;
                        course.participatingCourse = false;
                        const mergedCourse = mergeUserCourse(course);
                        if (Array.isArray(mergedCourse)) {
                            for (mergedCourseElement of mergedCourse) {
                                mergedCourseElement.isConfigured = getIsConfiguredDepartment({
                                    DeptSettings,
                                    _program_id: DeptSub.program_id.toString(),
                                    _department_id: DeptSub._id.toString(),
                                    _subject_id: subject._id.toString(),
                                    _course_id: mergedCourseElement._course_id.toString(),
                                    term: mergedCourseElement.term,
                                    rotation_count: mergedCourseElement.rotation_count,
                                });
                                adminCourseIds.push(mergedCourseElement._course_id.toString());
                                SelectedCourses.push(mergedCourseElement);
                            }
                        } else {
                            mergedCourse.isConfigured = getIsConfiguredDepartment({
                                DeptSettings,
                                _program_id: DeptSub.program_id.toString(),
                                _department_id: DeptSub._id.toString(),
                                _subject_id: subject._id.toString(),
                                _course_id: mergedCourse._course_id.toString(),
                                term: mergedCourse.term,
                                rotation_count: mergedCourse.rotation_count,
                            });
                            adminCourseIds.push(mergedCourse._course_id.toString());
                            SelectedCourses.push(mergedCourse);
                        }
                    } else {
                        if (course.participating) {
                            for (participating of course.participating) {
                                if (
                                    participating &&
                                    participating._subject_id.toString() === subject._id.toString()
                                ) {
                                    course.AdminCourse = false;
                                    course.participatingCourse = true;
                                    const mergedCourse = mergeUserCourse(course);
                                    if (Array.isArray(mergedCourse)) {
                                        for (mergedCourseElement of mergedCourse) {
                                            mergedCourseElement.isConfigured =
                                                getIsConfiguredDepartment({
                                                    DeptSettings,
                                                    _program_id: DeptSub.program_id.toString(),
                                                    _department_id: DeptSub._id.toString(),
                                                    _subject_id: subject._id.toString(),
                                                    _course_id:
                                                        mergedCourseElement._course_id.toString(),
                                                    term: mergedCourseElement.term,
                                                    rotation_count:
                                                        mergedCourseElement.rotation_count,
                                                });
                                            SelectedCourses.push(mergedCourseElement);
                                        }
                                    } else {
                                        mergedCourse.isConfigured = getIsConfiguredDepartment({
                                            DeptSettings,
                                            _program_id: DeptSub.program_id.toString(),
                                            _department_id: DeptSub._id.toString(),
                                            _subject_id: subject._id.toString(),
                                            _course_id: mergedCourse._course_id.toString(),
                                            term: mergedCourse.term,
                                            rotation_count: mergedCourse.rotation_count,
                                        });
                                        SelectedCourses.push(mergedCourse);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            subjectCourses[deptIndex].subject[subIndex].courses = SelectedCourses;
            subjectCourses[deptIndex].subject[subIndex].isConfigured = !!SelectedCourses.find(
                (courseElement) => courseElement.isConfigured === true,
            );
        }
        subjectCourses[deptIndex].isConfigured = !!subjectCourses[deptIndex].subject.find(
            (courseElement) => courseElement.isConfigured === true,
        );
    }

    const subjectShared = [];
    for (DeptSub of subjectCourses) {
        const sharedSubjects = JSON.parse(
            JSON.stringify(DeptSub.subject.filter((ele) => ele.subject_shared_from_program_id)),
        );
        if (sharedSubjects.length !== 0)
            subjectShared.push({
                _id: DeptSub._id,
                program_id: DeptSub.program_id,
                program_name: DeptSub.program_name,
                department_name: DeptSub.department_name,
                subject: sharedSubjects,
                shared_with: DeptSub.shared_with,
                shared: DeptSub.shared,
            });
    }
    for (sharedElement of subjectShared) {
        for (sharedSubjectElement of sharedElement.subject) {
            const sharedPrograms = JSON.parse(
                JSON.stringify(
                    subjectCourses.filter(
                        (ele) =>
                            ele.program_id.toString() ===
                                sharedSubjectElement.subject_shared_from_program_id.toString() &&
                            ele.subject.some(
                                (subjectShareElement) =>
                                    subjectShareElement._id.toString() ===
                                    sharedSubjectElement._id.toString(),
                            ),
                    ),
                ),
            );
            if (sharedPrograms && sharedPrograms.subject)
                for (ele1 of sharedPrograms.subject) {
                    const deptLoc = subjectCourses.findIndex(
                        (ele2) => ele2._id.toString() === ele1._id.toString(),
                    );
                    const subLoc = subjectCourses[deptLoc].subject.findIndex(
                        (ele3) => ele3._id.toString() === sharedSubjectElement._id.toString(),
                    );
                    subjectCourses[deptLoc].subject[subLoc].courses = [
                        ...subjectCourses[deptLoc].subject[subLoc].courses,
                        ...sharedSubjectElement.courses,
                    ];
                }
        }
    }
    const sharedWith = JSON.parse(
        JSON.stringify(subjectCourses.filter((ele) => ele.shared_from_program_name)),
    );
    for (departmentElement of sharedWith) {
        const shareInd = subjectCourses.findIndex(
            (ele2) => ele2._id.toString() === departmentElement._id.toString(),
        );
        if (shareInd !== -1) {
            for (subElement of departmentElement.subject) {
                if (subElement.courses.length !== 0) {
                    const subShareInd = subjectCourses[shareInd].subject.findIndex(
                        (ele3) => ele3._id.toString() === subElement._id.toString(),
                    );
                    if (subShareInd !== -1) {
                        for (element of subElement.courses) {
                            // element._program_id = subjectCourses[shareInd].program_id;
                            if (
                                !subjectCourses[shareInd].subject[subShareInd].courses.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() ===
                                            element._course_id.toString() &&
                                        courseElement.term === element.term,
                                )
                            )
                                subjectCourses[shareInd].subject[subShareInd].courses.push(element);
                        }
                    }
                }
            }
        }
    }

    const subjectIds =
        userData && userData.academic_allocation
            ? userData.academic_allocation
                  .map((subElement) => subElement._department_subject_id)
                  .flat()
            : [];
    const departmentSubjects = [];
    for (departmentSubjectElement of subjectCourses) {
        const subjects = [];
        for (const subjectElement of departmentSubjectElement.subject) {
            if (
                subjectIds.length
                    ? subjectIds.find(
                          (idsElement) => idsElement.toString() === subjectElement._id.toString(),
                      ) && subjectElement.courses.length
                    : subjectElement.courses.length
            ) {
                delete subjectElement.shared_with;
                delete subjectElement.shared;
                subjects.push(subjectElement);
            }
        }
        if (subjects.length) {
            delete departmentSubjectElement.shared_with;
            delete departmentSubjectElement.shared;
            departmentSubjectElement.subject = subjects;
            departmentSubjects.push(departmentSubjectElement);
        }
    }
    return { programCalendars: programCourseDatas, departmentSubjects /* : subjectCourses */ };
};

module.exports = {
    lmsSettings,
    userRoleData,
    courseCoordinatorBasedIds,
    userCourseScheduleList,
    courseCreditContactHoursCalculation,
    programWiseYearLevelData,
    programWiseDepartmentSubjectData,
    yearLevelData,
};
