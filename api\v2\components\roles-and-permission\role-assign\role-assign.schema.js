const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { INSTITUTION, USER, ROLE, PROGRAM, DEPARTMENT } = require('../../../utility/constants');
const { convertToMongoObjectId } = require('../../../utility/common');

const roleAssignSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _parent_id: {
            type: Schema.Types.ObjectId,
            default: null,
        },
        _user_id: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        userName: {
            first: String,
            middle: String,
            last: String,
            family: String,
        },
        roles: [
            {
                _role_id: {
                    type: Schema.Types.ObjectId,
                    ref: ROLE,
                },
                roleName: {
                    type: String,
                },
                program: [
                    {
                        _program_id: {
                            type: Schema.Types.ObjectId,
                            ref: PROGRAM,
                        },
                        programName: {
                            type: String,
                        },
                    },
                ],
                department: [
                    {
                        _department_id: {
                            type: Schema.Types.ObjectId,
                            ref: DEPARTMENT,
                        },
                        departmentTitle: {
                            type: String,
                        },
                    },
                ],
                isDefault: {
                    type: Boolean,
                    default: false,
                },
                isAdmin: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        reportTo: {
            _user_id: {
                type: Schema.Types.ObjectId,
                ref: USER,
            },
            name: {
                first: String,
                middle: String,
                last: String,
                family: String,
            },
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

roleAssignSchema.query.findAllAssignRoles = function ({ _institution_id = '', _parent_id = '' }) {
    return this.find({
        ...(_institution_id && { _institution_id: convertToMongoObjectId(_institution_id) }),
        ...(_parent_id && { _parent_id: convertToMongoObjectId(_parent_id) }),
        isDeleted: false,
        isActive: true,
    });
};

module.exports = {
    roleAssignSchema,
};
