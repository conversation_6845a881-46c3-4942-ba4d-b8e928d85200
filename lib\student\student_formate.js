let program_formate = require('../program/program_formate');

module.exports = {
    student: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                _program_id: program_formate.program_ID_Only(element.program),
                name: {
                    'first': element.name.first,
                    'middle': element.name.middle,
                    'last': element.name.last,
                    'family': element.name.family,
                },
                gender: element.gender,
                mobile: element.mobile,
                mailID: element.mailID,
                nationality: element.nationality,
                nationality_id: element.nationality_id,
                deviceID: element.deviceID,
                face_reg_status: element.face_reg_status,
                device_token: element.device_token,
                os: element.os,
                verified_status: element.verified_status,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    student_ID: (doc) => {
        let obj = {
            _id: doc._id,
            program: program_formate.program_ID_Only(doc.program),
            name: {
                'first': doc.name.first,
                'middle': doc.name.middle,
                'last': doc.name.last,
                'family': doc.name.family,
            },
            reg_id: doc.reg_id,
            gender: doc.gender,
            mobile: doc.mobile,
            mailID: doc.mailID,
            nationality: doc.nationality,
            nationality_id: doc.nationality_id,
            deviceID: doc.deviceID,
            face_reg_status: doc.face_reg_status,
            device_token: doc.device_token,
            os: doc.os,
            verified_status: doc.verified_status,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    student_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            program: doc._program_id,
            name: {
                'first': doc.name.first,
                'middle': doc.name.middle,
                'last': doc.name.last,
                'family': doc.name.family,
            },
            reg_id: doc.reg_id,
            gender: doc.gender,
            mobile: doc.mobile,
            mailID: doc.mailID,
            nationality: doc.nationality,
            nationality_id: doc.nationality_id,
            deviceID: doc.deviceID,
            face_reg_status: doc.face_reg_status,
            device_token: doc.device_token,
            os: doc.os,
            verified_status: doc.verified_status,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    student_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                _program_id: element._program_id,
                name: {
                    'first': element.name.first,
                    'middle': element.name.middle,
                    'last': element.name.last,
                    'family': element.name.family,
                },
                gender: element.gender,
                mobile: element.mobile,
                mailID: element.mailID,
                nationality: element.nationality,
                nationality_id: element.nationality_id,
                deviceID: element.deviceID,
                face_reg_status: element.face_reg_status,
                device_token: element.device_token,
                os: element.os,
                verified_status: element.verified_status,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },
}