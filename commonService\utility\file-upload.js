const AWS = require('aws-sdk');
const multer = require('multer');
const multerS3 = require('multer-s3');
const keys = require('./util_keys');

const s3 = new AWS.S3({
    accessKeyId: keys.AWS_ACCESS_KEY,
    secretAccessKey: keys.AWS_SECRET_KEY,
});

const s3Oci = new AWS.S3({
    region: keys.OCI_REGION,
    accessKeyId: keys.OCI_ACCESS_KEY_ID,
    secretAccessKey: keys.OCI_SECRET_ACCESS_KEY,
    endpoint: keys.OCI_AWS_S3_API,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
});
const s3Config = keys.DIGIVAL_CLOUD_PROVIDER === 'OCI' ? s3Oci : s3;

const s3FileURL = ({ filePath, fileName }) => {
    return keys.DIGIVAL_CLOUD_PROVIDER === 'OCI'
        ? `${keys.OCI_AWS_S3_API}/${filePath}/${fileName}`
        : `https://s3-${keys.AWS_REGION}.amazonaws.com/${filePath}/${fileName}`;
};

const uploadDocumentFile = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_BUCKET_NAME_ASSIGNMENT,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const { payload } = req.headers;
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            if (payload && payload.user_id) {
                fileName = payload.user_id + '/' + fileName;
            }
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.AWS_BUCKET_NAME_ASSIGNMENT,
                fileName,
            });
            req.body[fieldName] = fileName;
        },
    }),
    fileFilter: (req, file, cb) => {
        /* if (
            file.mimetype === 'image/jpg' ||
            file.mimetype === 'audio/mp3' ||
            file.mimetype === 'image/jpeg' ||
            file.mimetype === 'image/png' ||
            file.mimetype === 'video/x-flv' ||
            file.mimetype === 'video/mp4' ||
            file.mimetype === 'application/x-mpegURL' ||
            file.mimetype === 'application/octet-stream' ||
            file.mimetype === 'vnd.apple.mpegURL' ||
            file.mimetype === 'video/MP2T' ||
            file.mimetype === 'video/3gpp' ||
            file.mimetype === 'video/quicktime' ||
            file.mimetype === 'video/x-msvideo' ||
            file.mimetype === 'video/x-ms-wmv' ||
            file.mimetype === 'audio/mpeg' ||
            file.mimetype === 'audio/wav' ||
            file.mimetype === 'application/vnd.ms-excel' ||
            file.mimetype === 'video/mp2t' ||
            file.mimetype === 'application/msword' ||
            file.mimetype ===
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
            file.mimetype === 'application/pdf' ||
            file.mimetype === 'image/svg+xml' ||
            file.mimetype === 'application/pdf' ||
            file.mimetype === 'image/heic' ||
            file.mimetype === 'audio/mp4' ||
            file.mimetype === 'audio/wave'
        ) {
            cb(null, true);
        } else {
            return cb(new Error('file type is not allowed'));
        } */
        cb(null, true);
    },
    limits: {
        fileSize: 1024 * 1024 * 250,
    },
});

const assignmentFilesUpload = multer({
    storage: multerS3({
        s3: s3Config,
        bucket: keys.AWS_BUCKET_NAME_ASSIGNMENT,
        contentType: multerS3.AUTO_CONTENT_TYPE,
        metadata(req, file, cb) {
            cb(null, { fieldName: file.fieldname });
        },
        key(req, file, cb) {
            const originalname = file.originalname;
            let fileName = Date.now() + '-' + originalname.replace(/[^0-9a-zA-Z.]/g, '_');
            cb(null, fileName);
            const fieldName = file.fieldname;
            fileName = s3FileURL({
                filePath: keys.AWS_BUCKET_NAME_ASSIGNMENT,
                fileName,
            });
            if (!req[fieldName]) {
                req[fieldName] = [];
            }
            req[fieldName].push(fileName);
        },
    }),
});

module.exports = { uploadDocumentFile, assignmentFilesUpload };
