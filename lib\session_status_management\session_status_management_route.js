const route = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    getCourseSessionStatusDetail,
    updateCourseSessionStatusDetail,
    getTotalScheduleCount,
} = require('./session_status_management_controller');
const {
    getCourseSessionStatusValidator,
    updateCourseSessionStatusValidator,
} = require('./session_status_management_validator');
route.get(
    '/getCourseSessionStatusDetail',
    getCourseSessionStatusValidator,
    catchAsync(getCourseSessionStatusDetail),
);
route.put(
    '/updateCourseSessionStatusDetail/:sessionStatusId',
    updateCourseSessionStatusValidator,
    catchAsync(updateCourseSessionStatusDetail),
);
// Student grouping
route.get('/student-grouping/get-total-scheduleCount', catchAsync(getTotalScheduleCount));
module.exports = route;
