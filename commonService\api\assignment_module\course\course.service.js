const { convertToMongoObjectId, query: commonQuery } = require('../../../utility/common');
const {
    LOCAL,
    DIGI_COURSE,
    COURSE_SCHEDULE,
    TAXONOMY,
    DIGI_SESSION_ORDER,
} = require('../../../utility/constants');
// const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const courseSchema = require('mongoose').model(DIGI_COURSE);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const digiSession = require('mongoose').model(DIGI_SESSION_ORDER);
const taxonomySchema = require('mongoose').model(TAXONOMY);
const {
    courseAssessmentFormatting,
    subjectAssessmentFormatting,
    sloPlusCloFormatting,
    getCoursesListsForStaff,
    getTaxonomyLists,
} = require('../../serviceAdapter/adapter.formatter');
const { getProgramCourseSubjectIds } = require('../assignment/assignment.controller');

const getCourseList = async ({ _institution_id, programId, staffId, isAdmin }) => {
    try {
        // let courseList;
        // if (BASIC_DATA_FROM === LOCAL) {
        const courseQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            ...(programId && {
                _program_id: programId,
            }),
        };
        if (isAdmin === 'false' || !isAdmin) {
            courseQuery['coordinators._user_id'] = convertToMongoObjectId(staffId);
        }

        const courseScheduleQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            'staffs._staff_id': convertToMongoObjectId(staffId),
            ...(programId && {
                _program_id: programId,
            }),
            isDeleted: false,
            isActive: true,
        };
        const courseScheduleProject = {
            _course_id: 1,
            course_name: 1,
            course_code: 1,
            subjects: 1,
            _program_id: 1,
            term: 1,
        };

        courseList = await courseSchema
            .find(courseQuery, {
                _id: 0,
                courseId: '$_id',
                courseName: '$course_name',
                courseCode: '$course_code',
            })
            .lean();
        courseScheduleResult = await courseScheduleSchema
            .find(courseScheduleQuery, courseScheduleProject)
            .lean();
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return courseAssessmentFormatting({
            courseList,
            courseScheduleResult,
            // dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getSubjectList = async ({ _institution_id, courseId }) => {
    try {
        // let subjectList;
        // if (BASIC_DATA_FROM === LOCAL) {
        const courseSubjectList = await courseSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    ...(courseId && {
                        _id: convertToMongoObjectId(courseId),
                    }),
                },
                {
                    _id: 0,
                    participating: 1,
                    administration: 1,
                },
            )
            .lean();
        const isDuplicate = courseSubjectList.participating.some(
            (participatingElement) =>
                participatingElement._subject_id.toString() ===
                courseSubjectList.administration._subject_id.toString(),
        );
        if (!isDuplicate) {
            courseSubjectList.participating.push(courseSubjectList.administration);
        }
        subjectList = courseSubjectList.participating;
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }

        return subjectAssessmentFormatting({
            subjectList,
            // dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSloPlusCloBySessionIdAndCourseIdForAssessment = async ({ courseId, sessionIds }) => {
    try {
        const query = {
            _id: convertToMongoObjectId(courseId),
            isDeleted: false,
            isActive: true,
        };
        const digiQuery = {
            ...commonQuery,
            _course_id: convertToMongoObjectId(courseId),
        };
        // let courseResult;
        // let digiSessions;
        // if (BASIC_DATA_FROM === LOCAL) {
        courseResult = await courseSchema.findOne(query);
        digiSessions = await digiSession.find(digiQuery);
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return sloPlusCloFormatting({
            courseList: courseResult,
            sessionIds,
            // dataFrom: BASIC_DATA_FROM,
            digiSessions,
        });
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseListForStaff = async ({
    _institution_id,
    staffId,
    _institution_calendar_id,
    roleId,
}) => {
    try {
        // let courseResult;
        // let courseScheduleResult;
        // if (BASIC_DATA_FROM === LOCAL) {
        const courseProject = {
            coordinators: 1,
            administration: 1,
            participating: 1,
            course_name: 1,
            course_code: 1,
            _program_id: 1,
            program_name: 1,
        };
        const programAndCourseIds = await getProgramCourseSubjectIds({
            _institution_id,
            staffId,
            _institution_calendar_id,
            roleId,
        });
        const { userProgramIds, userCourseIds, subjectIds, isCourseAdmin, isProgramAdmin } =
            programAndCourseIds;
        let queryBasedOnAdmins = {};
        if (isProgramAdmin && userProgramIds.length)
            queryBasedOnAdmins = { _program_id: { $in: userProgramIds } };
        else if (isCourseAdmin && userCourseIds.length)
            queryBasedOnAdmins = { _course_id: { $in: userCourseIds } };
        else if (subjectIds && subjectIds.length)
            queryBasedOnAdmins = { 'subjects._subject_id': { $in: subjectIds } };
        else
            queryBasedOnAdmins = {
                'staffs._staff_id': convertToMongoObjectId(staffId),
            };
        const courseScheduleQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            ...queryBasedOnAdmins,
            isDeleted: false,
            isActive: true,
        };
        const courseScheduleProject = {
            _course_id: 1,
            course_name: 1,
            subjects: 1,
            _program_id: 1,
            program_name: 1,
            term: 1,
        };
        courseScheduleResult = await courseScheduleSchema.aggregate([
            { $match: courseScheduleQuery },
            { $unwind: '$subjects' },
            {
                $replaceWith: {
                    subjects: [
                        {
                            subjectId: '$subjects._subject_id',
                            subjectName: '$subjects.subject_name',
                            schedule: true,
                        },
                    ],
                    courseId: '$_course_id',
                    courseName: '$course_name',
                    programId: '$_program_id',
                    programName: '$program_name',
                    term: '$term',
                },
            },
            {
                $project: {
                    courseId: {
                        $toString: '$courseId',
                    },
                    courseName: 1,
                    programId: 1,
                    programName: 1,
                    term: 1,
                    subjects: 1,
                },
            },
        ]);
        const courseScheduleResultId = courseScheduleResult.map((course) =>
            convertToMongoObjectId(course.courseId),
        );
        courseResult = await courseSchema.find(
            { _id: { $in: courseScheduleResultId } },
            courseProject,
        );
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return getCoursesListsForStaff({
            courseList: courseResult,
            courseScheduleList: courseScheduleResult,
            staffId,
            // dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        throw new Error(error);
    }
};

const getTaxonomyList = async ({ _institution_id, search }) => {
    try {
        // let taxonomyResult;
        // if (BASIC_DATA_FROM === LOCAL) {
        let searchObject;
        const taxonomyQuery = { isDeleted: false };
        if (search) {
            const searchQuery = {
                $or: [
                    {
                        name: { $regex: search, $options: 'i' },
                    },
                ],
            };
            searchObject = { $and: [taxonomyQuery, searchQuery] };
        } else {
            searchObject = { $and: [taxonomyQuery] };
        }
        taxonomyResult = await taxonomySchema.find(searchObject).lean();
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return getTaxonomyLists({
            taxonomyList: taxonomyResult,
            // dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        throw new Error(error);
    }
};
module.exports = {
    getCourseList,
    getSubjectList,
    getSloPlusCloBySessionIdAndCourseIdForAssessment,
    getCourseListForStaff,
    getCoursesListsForStaff,
    getTaxonomyList,
};
