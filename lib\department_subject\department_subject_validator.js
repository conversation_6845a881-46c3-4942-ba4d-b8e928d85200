const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.department_subject = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).required().allow('').error(error => {
                        return error;
                    }),
                    master: Joi.string().valid(constant.DEPARTMENT_MASTER.DIVISION, constant.DEPARTMENT_MASTER.DEPARTMENT).allow(' ').min(3).max(20).trim().required().error(error => {
                        return error;
                    }),
                    title: Joi.string().allow(' ').min(3).max(50).trim().required().error(error => {
                        return error;
                    }),
                    _master_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    }),
                    shared: Joi.boolean().required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.department_subject_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            master: Joi.string().valid(constant.DEPARTMENT_MASTER.DIVISION, constant.DEPARTMENT_MASTER.DEPARTMENT).allow(' ').min(3).max(20).trim().error(error => {
                return error;
            }),
            title: Joi.string().allow(' ').min(3).max(20).trim().error(error => {
                return error;
            }),
            _master_id: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            shared: Joi.boolean().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.department_subject_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}