const crypto = require('crypto');
const algorithm = { TYPE: 'aes-256-cbc', IV_SIZE: 16 };

const ENCRYPT_PRIVATE_KEY = process.env.ENCRYPT_PRIVATE_KEY;

const getPrivateKeyFromEnv = () => {
    const privateKeyPem = `-----BEGIN PRIVATE KEY-----\n${ENCRYPT_PRIVATE_KEY}\n-----END PRIVATE KEY-----`;
    return privateKeyPem;
};

const decryptSessionKey = ({ encryptedSessionKey = '' }) => {
    const buffer = Buffer.from(encryptedSessionKey, 'base64');

    const decrypted = crypto.privateDecrypt(
        {
            key: getPrivateKeyFromEnv(),
            padding: crypto.constants.RSA_PKCS1_OAEP_PADDING,
            oaepHash: 'sha256',
        },
        buffer,
    );

    return JSON.parse(decrypted.toString('utf8'));
};

const decryptPayload = ({ encryptedPayload = '', encryptedSessionKey = '' }) => {
    const { sessionKey, sessionIv } = decryptSessionKey({ encryptedSessionKey });
    if (!sessionKey || !sessionIv) return null;

    const decipher = crypto.createDecipheriv(
        algorithm.TYPE,
        Buffer.from(sessionKey, 'hex'),
        Buffer.from(sessionIv, 'hex'),
    );

    let decrypted = decipher.update(encryptedPayload, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
};

const encryptPayload = ({ data, encryptedSessionKey = '' }) => {
    const { sessionKey, sessionIv } = decryptSessionKey({ encryptedSessionKey });
    if (!sessionKey || !sessionIv) return null;

    const cipher = crypto.createCipheriv(
        algorithm.TYPE,
        Buffer.from(sessionKey, 'hex'),
        Buffer.from(sessionIv, 'hex'),
    );

    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
};

module.exports = { decryptSessionKey, decryptPayload, encryptPayload };
