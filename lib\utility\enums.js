const enums = Object.freeze({
    // assessment & item status
    NOT_STARTED: 'NOT_STARTED',
    ONGOING_WITH_ASSESSMENT_AUTHOR: 'ONGOING_WITH_ASSESSMENT_AUTHOR',
    ONGOING_WITH_ITEM_AUTHOR: 'ONGOING_WITH_ITEM_AUTHOR',
    FULLFILLED_FROM_ITEM_AUTHOR: 'FULLFILLED_FROM_ITEM_AUTHOR',
    PUBLISHED: 'PUBLISHED',

    // gender
    MALE: 'M',
    FEMALE: 'F',
    AM: 'AM',
    PM: 'PM',

    MB: 'MB',
    YES: 'YES',
    EQUAL: 'EQUAL',
    UN_EQUAL: 'UN_EQUAL',
    NONE: 'NONE',
    MINS: 'MINS',

    SUBJECT_EXPERT_REVIEWER: 'SUBJECT_EXPERT_REVIEWER',
    MEDICAL_EDUCATIONIST: 'MEDICAL_EDUCATIONIST',
    BOTH: 'BOTH',
    NO: 'NO',
    END_OF_ASSESSMENT: 'END_OF_ASSESSMENT',
    DURING_ASSESSMENT: 'DURING_ASSESSMENT',

    FREE_SEQUENCE: 'FREE_SEQUENCE',
    RESTRICTED_SEQUENCE: 'RESTRICTED_SEQUENCE',

    // formative
    MCQ: 'MCQ', // Multiple Choice question
    SAQ: 'SAQ', // Short Answer Question
    MQ: 'MQ', // Matching Question
    CQ: 'CQ', //  Comprehensive Question
    EQ: 'EQ', //  Explanations Question
    LIKERTSCALE: 'Likert Scale',
    OPEN_ENDED: 'Open-Ended',
    SCQ: 'SCQ', // Single Choice Question

    // summative - survey item type
    TF: 'TF', // True or False
    EMQ: 'EMQ', // Extended matching Question
    LES: 'LES', // Likehert Extent scale
    LAS: 'LAS', // Likehert Agree scale
    LS: 'LS', // Linear scale
    ES: 'ES', // Emotion scale
    RS: 'RS', // Rating scale

    // exam status
    NOT_SCHEDULED: 'NOT_SCHEDULED',
    SCHEDULED: 'SCHEDULED',
    COMPLETED: 'COMPLETED',
    RE_SCHEDULED: 'RE_SCHEDULED',
    EXPIRED: 'EXPIRED',
    UN_PUBLISHED: 'UN_PUBLISHED',

    // exam location & proctoring type
    ONSITE_WITH_PROCTOR: 'onsite_with_proctor',
    REMOTE_WITH_PROCTOR: 'remote_with_proctor',
    REMOTE_WITHOUT_PROCTOR: 'remote_without_proctor',

    // item status
    NEW_REQUEST: 'NEW_REQUEST',
    INVALID_REQUEST: 'INVALID_REQUEST',
    REJECTED: 'REJECTED',
    ON_GOING: 'ON_GOING',
    READY_FOR_REVIEW: 'READY_FOR_REVIEW',
    FOR_REVIEW: 'FOR_REVIEW',

    IN_REVIEW: 'IN_REVIEW',
    IN_REVIEW_SE: 'IN_REVIEW_SE',
    IN_REVIEW_ME: 'IN_REVIEW_ME',

    APPROVED: 'APPROVED',
    APPROVED_SE: 'APPROVED_SE',
    APPROVED_ME: 'APPROVED_ME',

    REVIEWED: 'REVIEWED',
    REVIEWED_AND_SENT: 'REVIEWED_AND_SENT',
    REVIEWED_SE: 'REVIEWED_SE',
    REVIEWED_ME: 'REVIEWED_ME',
    IN_CORRECTIONS_SE: 'IN_CORRECTIONS_SE',
    IN_CORRECTIONS_ME: 'IN_CORRECTIONS_ME',

    CORRECTED_ME: 'CORRECTED_ME',
    IN_APPROVAL: 'IN_APPROVAL',
    IN_APPROVAL_ME: 'IN_APPROVAL_ME',
    FINALIZED: 'FINALIZED',
    FULFILLED: 'FULFILLED',

    READY_FOR_APPROVAL: 'READY_FOR_APPROVAL',
    IN_CORRECTIONS: 'IN_CORRECTIONS',
    APPROVAL_CORRECTION: 'APPROVAL_CORRECTION',

    SUITABLE: 'SUITABLE',
    NOT_SUITABLE: 'NOT_SUITABLE',

    NORMAL: 'NORMAL',
    RETIRED: 'RETIRED',
    IMPORTED: 'IMPORTED',
    FINALIZED_WR: 'FINALIZED_WR',
    PUBLISHED_WR: 'PUBLISHED_WR',
    ARCHIVED: 'ARCHIVED',

    OLD: 'OLD',
    NEW: 'NEW',

    DONE: 'DONE',
    NOT_DONE: 'NOT_DONE',
    SELECT: 'SELECT',
    NOT_APPLICABLE: 'NOT_APPLICABLE',

    IN_PROGRESS: 'IN_PROGRESS',
    FAILED: 'FAILED',
    BUNDLED: 'BUNDLED',

    LOW_IMPACT: 'L',
    MEDIUM_IMPACT: 'M',
    HIGH_IMPACT: 'H',

    INTRODUCTORY: 'I',
    PROFICIENT: 'P',
    ADVANCED: 'A',

    // activities types
    PRE_CLASS: 'PRE_CLASS',
    IN_CLASS: 'IN_CLASS',
    POST_CLASS: 'POST_CLASS',
    TIME: 'TIME',
    ONE_BY_ONE: 'ONE_BY_ONE',
    QUIZ: 'QUIZ',
    POLL: 'POLL',
    SURVEY: 'SURVEY',
    CORRECT_ANSWER: 'CORRECT_ANSWER',
    INCORRECT_ANSWER: 'INCORRECT_ANSWER',
    GENERAL: 'GENERAL',
    Name: 'name',
    Family_Name: 'family_name',
    USER_TYPE: 'user_type',
    ACADEMIC: 'academic',
    GENDER: 'gender',
    EMAIL: 'email',
    BATCH: 'batch',
    ENROLLMENT_YEAR: 'enrollment_year',
    DOB: 'dob',
    MOBILE: 'mobile',
    NATIONALITY_ID: 'nationality_id',
    USER_STATE: 'user_state',
    STATUS: 'status',
    MANUAL: 'manual',
    SYSTEM: 'system',
    MIN: 'min',
    MAX: 'max',
    EXACTLY: 'exactly',
    CLOSELY: 'closely',
});

module.exports = enums;
