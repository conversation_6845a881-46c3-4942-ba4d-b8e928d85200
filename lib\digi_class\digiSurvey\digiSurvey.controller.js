const studentGroupSchemas = require('../../models/student_group');
const surveySchemas = require('../../models/digiSurvey');
const surveyQuestionSchemas = require('../../models/digiSurvey.question');
const surveySectionSchemas = require('../../models/digiSurvey.section');
const surveyStudentSubmissionSchemas = require('../../models/digiSurvey.studentSubmission');
const digiSurveyResponseSchema = require('../digiSurveyResponse/digiSurveyResponse.model');
const institutionCalendarSchema = require('../../models/institution_calendar');
const userOutcomeReportResponseSettingSchema = require('./digiSurveyOutcomeReportResponseSettings');
const userSchema = require('../../models/user');
const curriculumSchema = require('../../models/digi_curriculum');
const courseSchema = require('../../models/digi_course');
const {
    convertToMongoObjectId,
    clone,
    query: dbQuery,
    paginator,
} = require('../../utility/common');
const {
    DS_PROGRAM_KEY,
    DS_COURSE_KEY,
    GENDER,
    DS_UPDATE,
    DS_DELETE,
    EXPIRE,
    COMPLETED,
    UPCOMING,
    INPROGRESS,
    CLOSED,
    EDIT_DURATION,
    GENDER: { MALE, FEMALE },
    SURVEY_QUESTION_TYPE: { RATING },
    DRAFT,
    CATEGORY_SELECTED: { COMMENT },
    SORTING_ORDER: { ASC },
    SURVEY_REPORT_SATISFACTION_LEVEL: {
        HIGH_SATISFACTION,
        LOW_SATISFACTION,
        MODERATE_SATISFACTION,
    },
    SURVEY_SATISFACTION_LEVEL: {
        HIGH_SATISFACTION_KEY,
        LOW_SATISFACTION_KEY,
        MODERATE_SATISFACTION_KEY,
    },
    LEARNING_OUTCOME_SECTION,
    PUBLIC_USER,
    CORRELATION_MATRIX_FORMULA: { PEARSON_CORRELATION_COEFFICIENT },
    DS_INSTITUTION_KEY,
} = require('../../utility/constants');
const {
    getSurveyUserListFromRedis,
    getTotalPublishedSurveyUserFromRedis,
    getSurveyUserListBasedOnGender,
    getQuestionBasedReport,
    calculateMean,
    calculateStandard,
    calculateCorrelation,
    getMappedOutcomeDetails,
    getMappedTagDetail,
} = require('./digiSurvey.service');
const { getStudentGrade } = require('../../utility/digiAssess.helper');
const { mean, std, sum, variance, sqrt } = require('mathjs');
const { analyzeSentiment } = require('../../../service/nlp.service');
const {
    getExpireTimeInSeconds,
    activateRedisTimer,
    deleteRedisTimer,
} = require('../../utility/utility.service');
const {
    USER_PERMISSION_MODULE_NAMES: { SURVEY },
} = require('../../utility/util_keys');
const { getPaginationValues } = require('../../utility/pagination');
const { nameFormatter } = require('../../utility/common_functions');
const { ploDetails, cloDetails } = require('../../utility/programInput.helper');

const getSurveyList = async ({ query = {} }) => {
    try {
        const {
            institutionCalendarId,
            userId,
            isTemplate,
            surveyLevel,
            status = [],
            surveyType = [],
            programIds = [],
            courseIds = [],
            term = [],
            year = [],
            level = [],
            searchKey,
        } = query;
        const { limit, skip } = getPaginationValues(query);
        const surveyList = await surveySchemas
            .find(
                {
                    ...dbQuery,
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    createdBy: convertToMongoObjectId(userId),
                    isTemplate,
                    surveyLevel,
                    ...(searchKey &&
                        searchKey.trim() !== '' && {
                            surveyName: { $regex: searchKey, $options: 'i' },
                        }),
                    ...(status.length ? { status: { $in: status } } : { status: { $ne: DRAFT } }),
                    ...(surveyType.length && { surveyType: { $in: surveyType } }),
                    ...(programIds.length && {
                        $or: [
                            { programId: { $in: programIds } },
                            { 'multiSelectedProgramIds.programId': { $in: programIds } },
                        ],
                    }),
                    ...(term.length && {
                        $or: [
                            { term: { $in: term } },
                            { 'multiSelectedProgramIds.term': { $in: term } },
                        ],
                    }),
                    ...(year.length && {
                        $or: [
                            { yearNo: { $in: year } },
                            { 'multiSelectedProgramIds.yearNo': { $in: year } },
                        ],
                    }),
                    ...(level.length && {
                        $or: [
                            { levelNo: { $in: level } },
                            { 'multiSelectedProgramIds.levelNo': { $in: level } },
                        ],
                    }),
                    ...(courseIds.length && {
                        $or: [
                            { courseId: { $in: courseIds } },
                            { 'multiSelectedProgramIds.courseId': { $in: courseIds } },
                        ],
                    }),
                },
                {
                    surveyName: 1,
                    surveyType: 1,
                    surveyDescription: 1,
                    surveyLevel: 1,
                    noOfQuestion: 1,
                    status: 1,
                    publishDate: 1,
                    expiryDate: 1,
                    surveyUsers: 1,
                    isMandator: 1,
                    learningOutcome: 1,
                    surveyVersionName: 1,
                    programId: 1,
                    curriculumName: 1,
                    courseId: 1,
                    'multiSelectedProgramIds.programId': 1,
                    'multiSelectedProgramIds.term': 1,
                    'multiSelectedProgramIds.yearNo': 1,
                    'multiSelectedProgramIds.levelNo': 1,
                    'multiSelectedProgramIds.courseId': 1,
                    'multiSelectedProgramIds.rotationCount': 1,
                },
            )
            .skip(skip)
            .limit(limit)
            .sort({ updatedAt: -1 })
            .populate([
                { path: 'surveyBankId', select: { cloneName: 1 } },
                { path: 'programId', select: { name: 1 } },
                { path: 'courseId', select: { course_name: 1 } },
                { path: 'multiSelectedProgramIds.programId', select: { name: 1 } },
                { path: 'multiSelectedProgramIds.courseId', select: { course_name: 1 } },
            ])
            .lean();
        //response count calculation
        if (surveyList && surveyList.length) {
            const surveyResponseCount = await digiSurveyResponseSchema.aggregate([
                {
                    $match: {
                        surveyId: { $in: surveyList.map(({ _id }) => convertToMongoObjectId(_id)) },
                    },
                },
                {
                    $project: {
                        surveyId: 1,
                        userId: 1,
                    },
                },
                {
                    $group: {
                        _id: {
                            surveyId: '$surveyId',
                        },
                        uniqueUsers: { $addToSet: '$userId' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        surveyId: { $toString: '$_id.surveyId' },
                        responseCount: { $size: '$uniqueUsers' },
                    },
                },
            ]);
            const constructedSurveyResponse = new Map();
            surveyResponseCount.forEach(({ surveyId, responseCount }) =>
                constructedSurveyResponse.set(surveyId, responseCount),
            );
            surveyList.forEach((surveyListElement) => {
                const totalResponseCount = constructedSurveyResponse.get(
                    String(surveyListElement._id),
                );
                surveyListElement.responseCount = totalResponseCount || 0;
                surveyListElement.totalCount = surveyListElement.surveyUsers.length;
                delete surveyListElement.surveyUsers;
            });
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: surveyList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentSurveyList = async ({ query = {} }) => {
    try {
        const { institutionCalendarId, userId, surveyLevel } = query;
        let surveyList = await surveySchemas
            .find(
                {
                    ...dbQuery,
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    surveyUsers: convertToMongoObjectId(userId),
                    'closedSurveyReason.removeFromAllParticipants': false,
                    surveyLevel,
                    status: { $ne: DRAFT },
                },
                {
                    surveyName: 1,
                    surveyType: 1,
                    surveyDescription: 1,
                    surveyLevel: 1,
                    status: 1,
                    programId: 1,
                    courseId: 1,
                    publishDate: 1,
                    expiryDate: 1,
                    isMandator: 1,
                    closedSurveyReason: 1,
                    learningOutcome: 1,
                    multiSelectedProgramIds: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        surveyList = surveyList.map(({ multiSelectedProgramIds = [], ...rest }) => {
            const userProgramDetails = multiSelectedProgramIds.find(({ studentIds = [] }) =>
                studentIds.some(({ studentId }) => String(userId) === String(studentId)),
            );
            return {
                ...rest,
                programDetail: userProgramDetails
                    ? (({ studentIds, ...restDetails }) => restDetails)(userProgramDetails)
                    : {},
            };
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: surveyList,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPublishedSurveyCalendarList = async ({ query = {} }) => {
    try {
        const { userId } = query;
        const publishedSurveyCalendarIds = await surveySchemas
            .distinct(
                'institutionCalendarId',
                userId && {
                    surveyUsers: convertToMongoObjectId(userId),
                },
            )
            .lean();
        let publishedSurveyCalendarDetails = [];
        if (publishedSurveyCalendarIds && publishedSurveyCalendarIds.length) {
            publishedSurveyCalendarDetails = await institutionCalendarSchema
                .find(
                    {
                        _id: { $in: publishedSurveyCalendarIds },
                    },
                    {
                        calendar_name: 1,
                        start_date: 1,
                        end_date: 1,
                    },
                )
                .sort({ _id: -1 })
                .lean();
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: publishedSurveyCalendarDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSinglePublishedSurveyDetails = async ({ query = {} }) => {
    try {
        const { surveyId } = query;
        const populateQuery = [
            { path: 'programId', select: { name: 1, code: 1 } },
            { path: 'courseId', select: { course_name: 1, course_code: 1 } },
            {
                path: 'surveyBankId',
                select: {
                    status: 1,
                    learningOutcome: 1,
                    manageQuestionTags: 1,
                    mapLearningOutcomes: 1,
                    ratingScaleConfiguration: 1,
                    outcomeSectionPlacement: 1,
                    cloneName: 1,
                },
            },
        ];
        const singleSurveyDetails = await surveySchemas
            .findOne(
                {
                    ...dbQuery,
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    programId: 1,
                    curriculumName: 1,
                    term: 1,
                    yearNo: 1,
                    levelNo: 1,
                    courseId: 1,
                    surveyBankId: 1,
                    isTemplate: 1,
                    surveyType: 1,
                    surveyName: 1,
                    surveyDescription: 1,
                    surveyVersionName: 1,
                    learningOutcome: 1,
                    surveyLevel: 1,
                    publishDate: 1,
                    expiryDate: 1,
                    questions: 1,
                    isMandator: 1,
                    surveyUsers: 1,
                    outComeDetails: 1,
                    mappedOutcomeDetails: 1,
                    userResponseSettings: 1,
                    surveyPublicUserDetails: 1,
                    selectedParticipants: 1,
                    checkedParticipants: 1,
                    noOfQuestion: 1,
                    mappedTagDetails: 1,
                    existingSelectedCourseId: 1,
                },
            )
            .populate(populateQuery)
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: singleSurveyDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const extendSurveyDuration = async ({ query = {} }) => {
    try {
        const { surveyId, expireDateAndTime } = query;
        const updatedSurveyData = await surveySchemas.updateOne(
            { _id: convertToMongoObjectId(surveyId) },
            { status: INPROGRESS, expiryDate: new Date(expireDateAndTime) },
        );
        if (!updatedSurveyData.modifiedCount) {
            return { statusCode: 400, message: 'UNABLE_TO_EXTENDED_SURVEY_DURATION' };
        }
        const expireTimeInSeconds = getExpireTimeInSeconds({
            endDateAndTime: expireDateAndTime,
            startDateAndTime: new Date(),
        });
        if (expireTimeInSeconds > 0) {
            await activateRedisTimer({
                moduleName: SURVEY,
                uniqueId: surveyId,
                documentStatus: EXPIRE,
                timeInSeconds: expireTimeInSeconds,
            });
        }
        return { statusCode: 200, message: 'SURVEY_DURATION_EXTENDED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyAdd = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            rotationCount,
            term,
            createdBy,
            surveyName,
            surveyDescription,
            publishTo,
            noOfQuestion,
            responseCount,
            studentCount,
        } = body;
        const surveyCreate = await surveySchemas.create({
            _institution_id,
            institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            rotationCount,
            term,
            createdBy,
            surveyName,
            surveyDescription,
            publishTo,
            noOfQuestion,
            responseCount,
            studentCount,
        });
        return {
            statusCode: 201,
            message: 'DATA_RETRIEVED',
            data: surveyCreate,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyAddSectionQuestion = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { surveyId, sections } = body;
        const surveySections = [];
        const surveyQuestions = [];
        for (sectionElement of sections) {
            const questionIds = [];
            for (questionElement of sectionElement.questions) {
                const questionMongoId = convertToMongoObjectId();
                questionIds.push(questionMongoId);
                surveyQuestions.push({
                    surveyId,
                    _id: questionMongoId,
                    question: questionElement.question,
                    questionNo: questionElement.questionNo,
                    questionType: questionElement.questionType,
                    ratingValues: questionElement.ratingValues,
                    ratingType: questionElement.ratingType,
                    outComeType: questionElement.outComeType,
                    outCome: questionElement.outCome,
                    kpi: questionElement.kpi,
                    tagName: questionElement.tagName,
                });
            }
            surveySections.push({
                surveyId,
                sectionName: sectionElement.sectionName,
                sectionDescription: sectionElement.sectionDescription,
                sessionPosition: sectionElement.sessionPosition,
                questionIds,
            });
        }
        const surveySectionCreate = await surveySectionSchemas.create(surveySections);
        const surveyQuestionCreate = await surveyQuestionSchemas.create(surveyQuestions);
        return {
            statusCode: 201,
            message: 'DATA_RETRIEVED',
            data: { surveySections, surveyQuestions, surveySectionCreate, surveyQuestionCreate },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyStudentSubmission = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { surveyId, sections } = body;
        const surveySections = [];
        const surveyQuestions = [];
        // for (sectionElement of sections) {
        //     const questionIds = [];
        //     for (questionElement of sectionElement.questions) {
        //         const questionMongoId = convertToMongoObjectId();
        //         questionIds.push(questionMongoId);
        //         surveyQuestions.push({
        //             surveyId,
        //             _id: questionMongoId,
        //             questionNo: questionElement.questionNo,
        //             questionType: questionElement.questionType,
        //             ratingType: questionElement.ratingType,
        //             outComeType: questionElement.outComeType,
        //             outCome: questionElement.outCome,
        //             kpi: questionElement.kpi,
        //             tagName: questionElement.tagName,
        //         });
        //     }
        //     surveySections.push({
        //         surveyId,
        //         sectionName: sectionElement.sectionName,
        //         sectionDescription: sectionElement.sectionDescription,
        //         sessionPosition: sectionElement.sessionPosition,
        //         questionIds,
        //     });
        // }
        // const surveySectionCreate = await surveySectionSchemas.create(surveySections);
        // const surveyQuestionCreate = await surveyQuestionSchemas.create(surveyQuestions);
        return {
            statusCode: 201,
            message: 'DATA_RETRIEVED',
            // data: { surveySections, surveyQuestions, surveySectionCreate, surveyQuestionCreate },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyStudentAutoSubmission = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { surveyId, institutionCalendarId, programId, surveyType, dbUpdate } = body;

        const studentGroupData = await studentGroupSchemas
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'master._program_id': convertToMongoObjectId(programId),
                },
                {
                    'groups.students._student_id': 1,
                },
            )
            .lean();

        let studentIds = [];
        const genderBasedCount = {
            male: 0,
            female: 0,
        };
        switch (surveyType) {
            case DS_PROGRAM_KEY:
                for (studentGroupElement of studentGroupData) {
                    for (groupElement of studentGroupElement.groups) {
                        studentIds = [
                            ...studentIds,
                            ...groupElement.students.map((studentElement) =>
                                studentElement._student_id.toString(),
                            ),
                        ];
                        genderBasedCount.male += groupElement.students.filter(
                            (studentElement) => studentElement.gender === GENDER.MALE,
                        ).length;
                        genderBasedCount.female += groupElement.students.filter(
                            (studentElement) => studentElement.gender === GENDER.FEMALE,
                        ).length;
                    }
                }
                break;
            case DS_COURSE_KEY:
                break;
            default:
                break;
        }
        studentIds = [...new Set(studentIds)];

        const surveyQuestions = await surveyQuestionSchemas
            .find(
                {
                    surveyId,
                },
                {
                    questionType: 1,
                    ratingType: 1,
                    question: 1,
                },
            )
            .lean();

        // Array of possible responses for each question
        const studentSAQResponses = {
            liked: [
                'I enjoyed the hands-on projects the most.',
                'The clear explanations really helped me understand the material.',
                'The practical examples were very beneficial for grasping concepts, i like it.',
                'The course structure was well-organized and easy to follow.',
                'I appreciated the support from the instructor and peers.',
                'The practical exercises were incredibly helpful in reinforcing the concepts taught.',
                "The instructor's depth of knowledge and ability to explain complex topics in a simple manner.",
                'The course materials were well-organized and easy to follow.',
                'The interactive nature of the course kept me engaged throughout.',
                'The real-world examples provided valuable insights into how to apply the concepts learned.',
                'The course could have delved deeper into advanced topics.',
                'The rigid schedule made it difficult to balance with other commitments.',
                'The lack of practical application exercises for certain concepts.',
                'The outdated resources used in some parts of the course.',
                'The length of the course felt excessive for the amount of material covered.',
            ],
            disliked: [
                'I found the pace of the course to be too slow.',
                'Some topics were not covered in enough detail for my liking.',
                'The assessments felt too difficult compared to the content covered.',
                'I encountered technical issues with the course platform.',
                'The video/audio quality could be improved.',
                'The pace of the course felt too fast at times, making it challenging to absorb all the information.',
                'The lack of emphasis on certain important topics left me feeling unprepared.',
                'The course materials could have been more visually engaging.',
                "The course assessments felt too easy and didn't adequately test my understanding.",
                'The lack of opportunities for one-on-one interaction with the instructor.',
            ],
            improvements: [
                'The course could benefit from more interactive elements.',
                'Providing additional resources for further exploration would be helpful.',
                'Including more real-world case studies could enhance the learning experience.',
                'Offering live Q&A sessions with instructors would be beneficial.',
                'Creating a dedicated discussion forum for students to collaborate.',
                'The opportunity to collaborate with other students and share experiences.',
                'The flexibility of being able to access course materials online at any time.',
                'The regular feedback and constructive criticism from the instructor.',
                'The course covered a wide range of topics relevant to the subject.',
                'The practical tips and tricks shared by the instructor were invaluable.',
                'Provide more opportunities for hands-on practical exercises.',
                'Offer more supplementary resources for further exploration of topics.',
                'Enhance the interactivity of the course through more group discussions or forums.',
                'Include more real-world case studies to illustrate the application of concepts.',
                'Update course materials to reflect the latest industry trends and advancements.',
                'Provide more personalized feedback on assignments and assessments.',
                'Offer optional additional sessions for students who want to delve deeper into specific topics.',
                'Allow for more flexibility in the course schedule to accommodate different learning paces.',
                'Increase the diversity of teaching methods to cater to different learning styles.',
                'Create a more user-friendly platform/interface for accessing course materials and assignments.',
            ],
        };
        const getRandomResponse = (responseValues) => {
            const index = Math.floor(Math.random() * responseValues.length);
            return responseValues[index];
        };
        // Keywords to identify categories
        const likeKeywords = ['like', 'enjoy', 'best'];
        const dislikeKeywords = ['dislike', 'hate', 'worst'];
        const improveKeywords = ['improve', 'enhance', 'better'];
        const surveyStudentResponseData = [];
        for (studentElement of studentIds) {
            const studentSurveyQuestionData = [];
            for (questionElement of surveyQuestions) {
                if (questionElement.questionType === 'rating') {
                    studentSurveyQuestionData.push({
                        questionId: questionElement._id,
                        questionType: questionElement.questionType,
                        studentResponseRating: Math.floor(
                            Math.random() * (questionElement.ratingType - 1 + 1) + 1,
                        ),
                    });
                } else {
                    let studentQuestionResponse = '';
                    if (
                        likeKeywords.some((keyword) =>
                            questionElement.question.toLowerCase().includes(keyword),
                        )
                    ) {
                        studentQuestionResponse = getRandomResponse(studentSAQResponses.liked);
                    } else if (
                        dislikeKeywords.some((keyword) =>
                            questionElement.question.toLowerCase().includes(keyword),
                        )
                    ) {
                        studentQuestionResponse = getRandomResponse(studentSAQResponses.disliked);
                    } else if (
                        improveKeywords.some((keyword) =>
                            questionElement.question.toLowerCase().includes(keyword),
                        )
                    ) {
                        studentQuestionResponse = getRandomResponse(
                            studentSAQResponses.improvements,
                        );
                    }
                    if (studentQuestionResponse !== '')
                        studentSurveyQuestionData.push({
                            questionId: questionElement._id,
                            questionType: questionElement.questionType,
                            studentResponse: studentQuestionResponse,
                        });
                }
            }
            surveyStudentResponseData.push({
                surveyId,
                studentId: studentElement,
                studentResponse: studentSurveyQuestionData,
            });
        }
        let studentResponseCreate = [];
        if (dbUpdate) {
            studentResponseCreate = await surveyStudentSubmissionSchemas.create(
                surveyStudentResponseData,
            );
            await surveySchemas.updateOne(
                { _id: convertToMongoObjectId(surveyId) },
                { $set: { studentCount: studentIds.length } },
            );
        }

        return {
            statusCode: 201,
            message: 'DATA_RETRIEVED',
            data: { surveyQuestions, surveyStudentResponseData, studentResponseCreate },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportSummaryHeader = async ({ query = {} }) => {
    try {
        const { surveyId } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    surveyName: 1,
                    surveyDescription: 1,
                    surveyLevel: 1,
                    noOfQuestion: 1,
                    studentCount: 1,
                    institutionCalendarId: 1,
                    programId: 1,
                    questions: 1,
                    surveyUsers: 1,
                    surveyVersionName: 1,
                    mappedTagDetails: 1,
                    'surveyPublicUserDetails.maxResponse': 1,
                },
            )
            .populate({ path: 'surveyUsers', select: { gender: 1 } })
            .populate({ path: 'programId', select: { name: 1 } })
            .populate({ path: 'surveyBankId', select: { cloneName: 1 } })
            .lean();

        let maleUserCount = 0;
        let femaleUserCount = 0;
        const publicUserCount = surveyDetail.surveyPublicUserDetails.maxResponse || 0;
        const totalUserCount = surveyDetail.surveyUsers.length + publicUserCount;

        surveyDetail.surveyUsers.forEach(({ gender }) => {
            if (gender === MALE) {
                maleUserCount += 1;
            } else {
                femaleUserCount += 1;
            }
        });
        //for response summary at least 1 rating should be present
        const surveySectionList = [];
        surveyDetail.questions.pages.forEach(({ title = '', elements = [] }) => {
            const isSectionHaveRatingQuestion = elements.some(({ type = '' }) => type === RATING);
            if (isSectionHaveRatingQuestion) {
                surveySectionList.push(title);
            }
        });
        const surveyResponseCount = await digiSurveyResponseSchema.aggregate([
            {
                $match: {
                    surveyId: convertToMongoObjectId(surveyId),
                },
            },
            {
                $project: {
                    userId: 1,
                    gender: 1,
                },
            },
            {
                $group: {
                    _id: { gender: '$gender' },
                    uniqueUsers: { $addToSet: '$userId' },
                },
            },
            {
                $project: {
                    gender: '$_id.gender',
                    responseCount: { $size: '$uniqueUsers' },
                    _id: 0,
                },
            },
        ]);

        let totalMaleResponseCount = 0;
        let totalFemaleResponseCount = 0;
        let totalPublicUserResponseCount = 0;

        surveyResponseCount.forEach(({ gender, responseCount }) => {
            switch (gender) {
                case MALE:
                    totalMaleResponseCount += responseCount;
                    break;
                case FEMALE:
                    totalFemaleResponseCount += responseCount;
                    break;
                default:
                    totalPublicUserResponseCount += responseCount;
            }
        });
        const reportHeaderData = {
            surveyName: surveyDetail.surveyName,
            surveyVersionName: surveyDetail.surveyVersionName,
            cloneName: surveyDetail.surveyBankId.cloneName,
            surveyDescription: surveyDetail.surveyDescription,
            surveyLevel: surveyDetail.surveyLevel,
            maleCount: maleUserCount,
            femaleCount: femaleUserCount,
            publicUserCount,
            maleResponseCount: totalMaleResponseCount,
            femaleResponseCount: totalFemaleResponseCount,
            publicUserResponseCount: totalPublicUserResponseCount,
            totalCount: totalUserCount,
            totalResponseCount:
                totalMaleResponseCount + totalFemaleResponseCount + totalPublicUserResponseCount,
            questionCount: surveyDetail.noOfQuestion,
            surveySectionList,
            tagCount: new Set(
                surveyDetail.mappedTagDetails.map((tagElement) => String(tagElement.tagId)),
            ).size,
        };

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: reportHeaderData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportSection = async ({ query = {} }) => {
    try {
        const { surveyId, sectionName, responseStatusFilter = [] } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                    surveyUsers: 1,
                    surveyLevel: 1,
                    'surveyPublicUserDetails.maxResponse': 1,
                    'mappedOutcomeDetails.outcomeNo': 1,
                    'mappedOutcomeDetails.questionNo': 1,
                    'mappedOutcomeDetails.questionId': 1,
                    'mappedTagDetails.name': 1,
                    'mappedTagDetails.code': 1,
                    'mappedTagDetails.questionId': 1,
                },
            )
            .populate({ path: 'surveyUsers', select: { gender: 1 } })
            .lean();
        const { maleUserIds = [], femaleUserIds = [] } = getSurveyUserListBasedOnGender({
            surveyUsers: surveyDetail.surveyUsers,
        });
        const publicUserCount =
            !responseStatusFilter.length || responseStatusFilter.includes(PUBLIC_USER)
                ? surveyDetail.surveyPublicUserDetails.maxResponse || 0
                : 0;
        const totalUserCount =
            (!responseStatusFilter.length || responseStatusFilter.includes(MALE)
                ? maleUserIds.length
                : 0) +
            (!responseStatusFilter.length || responseStatusFilter.includes(FEMALE)
                ? femaleUserIds.length
                : 0) +
            publicUserCount;
        const surveyQuestionList = surveyDetail.questions.pages.find(
            ({ title = '' }) => title === sectionName,
        );
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    sectionNo: sectionName,
                    ...(responseStatusFilter.length && { gender: { $in: responseStatusFilter } }),
                    questionType: RATING,
                    questionNo: { $in: surveyQuestionList.elements.map(({ name }) => name) },
                },
                { questionNo: 1, answer: 1, gender: 1, userId: 1 },
            )
            .lean();
        const { overAllCalculation, surveyQuestionData } = getQuestionBasedReport({
            questions: surveyQuestionList.elements.filter(({ type = '' }) => type === RATING),
            userResponse: userSurveyResponses,
            maleUserIds:
                !responseStatusFilter.length || responseStatusFilter.includes(MALE)
                    ? maleUserIds
                    : [],
            femaleUserIds:
                !responseStatusFilter.length || responseStatusFilter.includes(FEMALE)
                    ? femaleUserIds
                    : [],
            publicUserCount,
            totalUserCount,
            mappedOutcomeDetails: surveyDetail.mappedOutcomeDetails,
            mappedTagDetails: surveyDetail.mappedTagDetails,
            surveyLevel: surveyDetail.surveyLevel,
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                overAllCalculation,
                surveyQuestionData,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportQuestion = async ({ query = {} }) => {
    try {
        const { surveyId, responseStatusFilter = [] } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                    surveyUsers: 1,
                    surveyLevel: 1,
                    'mappedOutcomeDetails.outcomeNo': 1,
                    'mappedOutcomeDetails.questionNo': 1,
                    'mappedOutcomeDetails.questionId': 1,
                    'surveyPublicUserDetails.maxResponse': 1,
                    'mappedTagDetails.name': 1,
                    'mappedTagDetails.code': 1,
                    'mappedTagDetails.questionId': 1,
                },
            )
            .populate({ path: 'surveyUsers', select: { gender: 1 } })
            .lean();
        const { maleUserIds = [], femaleUserIds = [] } = getSurveyUserListBasedOnGender({
            surveyUsers: surveyDetail.surveyUsers,
        });
        const publicUserCount =
            !responseStatusFilter.length || responseStatusFilter.includes(PUBLIC_USER)
                ? surveyDetail.surveyPublicUserDetails.maxResponse || 0
                : 0;
        const totalUserCount =
            (!responseStatusFilter.length || responseStatusFilter.includes(MALE)
                ? maleUserIds.length
                : 0) +
            (!responseStatusFilter.length || responseStatusFilter.includes(FEMALE)
                ? femaleUserIds.length
                : 0) +
            publicUserCount;
        const totalSections = [];
        const totalQuestions = [];

        surveyDetail.questions.pages.forEach(({ title = '', elements = [] }) => {
            totalSections.push(title);
            totalQuestions.push(...elements.map(({ name }) => name));
        });
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    sectionNo: { $in: totalSections },
                    questionType: RATING,
                    ...(responseStatusFilter.length && { gender: { $in: responseStatusFilter } }),
                    questionNo: { $in: totalQuestions },
                },
                { questionNo: 1, answer: 1, gender: 1, userId: 1 },
            )
            .lean();
        const individualQuestionReport = [];
        // calculate values for each section for each question present in individual section
        surveyDetail.questions.pages.forEach(({ title = '', elements = [], description = '' }) => {
            const ratingQuestions = elements.filter(
                (questionElement) => questionElement.type === RATING,
            );
            if (ratingQuestions.length) {
                const { overAllCalculation, surveyQuestionData } = getQuestionBasedReport({
                    questions: ratingQuestions,
                    userResponse: userSurveyResponses,
                    maleUserIds:
                        !responseStatusFilter.length || responseStatusFilter.includes(MALE)
                            ? maleUserIds
                            : [],
                    femaleUserIds:
                        !responseStatusFilter.length || responseStatusFilter.includes(FEMALE)
                            ? femaleUserIds
                            : [],
                    publicUserCount,
                    totalUserCount,
                    mappedOutcomeDetails: surveyDetail.mappedOutcomeDetails,
                    mappedTagDetails: surveyDetail.mappedTagDetails,
                    surveyLevel: surveyDetail.surveyLevel,
                });
                individualQuestionReport.push({
                    sectionName: title,
                    overAllCalculation,
                    surveyQuestionData,
                    surveyDescription: description,
                });
            }
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: individualQuestionReport,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportStudentResponse = async ({ query = {} }) => {
    try {
        const { surveyId, sort, searchKey, pageNo, limits, responseStatusFilter = [] } = query;

        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                    programId: 1,
                    term: 1,
                    curriculumName: 1,
                    yearNo: 1,
                    levelNo: 1,
                    courseId: 1,
                    institutionCalendarId: 1,
                    surveyLevel: 1,
                    userResponseSettings: 1,
                },
            )
            .populate([
                { path: 'programId', select: { code: 1 } },
                { path: 'courseId', select: { course_code: 1 } },
                { path: 'institutionCalendarId', select: { start_date: 1, end_date: 1 } },
            ])
            .lean();
        const totalSections = [];
        const totalQuestions = [];
        surveyDetail.questions.pages.forEach(({ title = '', elements = [] }) => {
            totalSections.push(title);
            totalQuestions.push(...elements.map(({ name }) => name));
        });
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    sectionNo: { $in: totalSections },
                    questionNo: { $in: totalQuestions },
                    ...(responseStatusFilter.length && { gender: { $in: responseStatusFilter } }),
                },
                { userId: 1, answer: 1, gender: 1, questionType: 1, sectionNo: 1 },
            )
            .populate({ path: 'userId', select: { user_id: 1, name: 1 } })
            .lean();
        let studentIndividualResponse = [];
        const constructedUserResponse = new Map();
        userSurveyResponses.forEach(({ userId, gender, questionType, answer = '', sectionNo }) => {
            const { user_id, name, _id } = userId;
            const existingConstructedUserResponse = constructedUserResponse.get(String(_id)) || {
                user_id,
                name,
                gender,
                commentSectionAnswers: [],
                sectionWiseMeanCalculation: {},
            };
            if (questionType === COMMENT) {
                if (answer.trim() !== '') {
                    existingConstructedUserResponse.commentSectionAnswers.push(answer);
                }
            } else {
                const userExistingSectionWiseResponse =
                    existingConstructedUserResponse.sectionWiseMeanCalculation[sectionNo];
                if (!userExistingSectionWiseResponse) {
                    existingConstructedUserResponse.sectionWiseMeanCalculation[sectionNo] = [
                        answer,
                    ];
                } else {
                    userExistingSectionWiseResponse.push(answer);
                }
            }
            constructedUserResponse.set(String(_id), existingConstructedUserResponse);
        });
        constructedUserResponse.forEach((constructedUserElement, constructedUserIndex) => {
            const { user_id, name, gender, commentSectionAnswers, sectionWiseMeanCalculation } =
                constructedUserElement;
            let sentimentLabel = '';
            if (commentSectionAnswers.length) {
                sentimentLabel = analyzeSentiment(commentSectionAnswers);
            }
            const totalSectionWiseMeanCalculation = Object.values(sectionWiseMeanCalculation).map(
                (answerElement) => calculateMean(answerElement),
            );
            studentCalculatedMetrics = {
                mean: calculateMean(totalSectionWiseMeanCalculation),
                feeling: sentimentLabel,
                testReliability: 0,
                grade: '',
            };
            studentIndividualResponse.push({
                studentId: constructedUserIndex,
                user_id,
                name,
                gender,
                studentCalculatedMetrics,
                userResponseSettings: surveyDetail?.userResponseSettings ?? {},
            });
        });
        if (sort) {
            studentIndividualResponse = studentIndividualResponse.sort(
                (firstElement, secondElement) => {
                    const formattedFirstUserName = nameFormatter(firstElement.name);
                    const formattedSecondUserName = nameFormatter(secondElement.name);
                    if (sort === ASC) {
                        return formattedFirstUserName.localeCompare(
                            formattedSecondUserName.toLocaleLowerCase(),
                        );
                    }
                    return formattedSecondUserName.localeCompare(
                        formattedFirstUserName.toLocaleLowerCase(),
                    );
                },
            );
        }
        if (searchKey) {
            const searchTerm = searchKey.toLocaleLowerCase();
            studentIndividualResponse = studentIndividualResponse.filter(
                ({ name, user_id, gender }) => {
                    const formattedUserName = nameFormatter(name);
                    return (
                        formattedUserName.toLocaleLowerCase().includes(searchTerm) ||
                        user_id.toLocaleLowerCase().includes(searchTerm) ||
                        gender.toLocaleLowerCase().includes(searchTerm)
                    );
                },
            );
        }
        const paginatedResponse = paginator(studentIndividualResponse, pageNo, limits);
        //get student grade
        const studentGrade =
            surveyDetail.surveyLevel === DS_COURSE_KEY
                ? await getStudentGrade({
                      studentDetails: paginatedResponse.data,
                      programId: surveyDetail.programId,
                      term: surveyDetail.term,
                      curriculumName: surveyDetail.curriculumName,
                      yearNo: surveyDetail.yearNo,
                      levelNo: surveyDetail.levelNo,
                      courseId: surveyDetail.courseId,
                      institutionCalendarId: surveyDetail.institutionCalendarId,
                  })
                : [];
        if (studentGrade && studentGrade.length) {
            studentGrade.forEach(({ grade = '', academicNo = '' }) => {
                const studentReportDetail = paginatedResponse.data.find(
                    ({ user_id }) => user_id === academicNo,
                );
                if (studentReportDetail && studentReportDetail.studentCalculatedMetrics) {
                    studentReportDetail.studentCalculatedMetrics.grade = grade;
                }
            });
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: paginatedResponse,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportIndividualStudentResponse = async ({ query = {} }) => {
    try {
        const { surveyId, studentId } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                    surveyLevel: 1,
                    userResponseSettings: 1,
                    'mappedOutcomeDetails.outcomeNo': 1,
                    'mappedOutcomeDetails.questionNo': 1,
                    'mappedOutcomeDetails.questionId': 1,
                    'mappedTagDetails.code': 1,
                    'mappedTagDetails.name': 1,
                    'mappedTagDetails.questionId': 1,
                    'surveyPublicUserDetails.maxResponse': 1,
                },
            )
            .lean();
        const totalSections = [];
        const totalQuestions = [];
        surveyDetail.questions.pages.forEach(({ title = '', elements = [] }) => {
            totalSections.push(title);
            totalQuestions.push(...elements.map(({ name }) => name));
        });
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    userId: convertToMongoObjectId(studentId),
                    sectionNo: { $in: totalSections },
                    questionNo: { $in: totalQuestions },
                },
                { answer: 1, sectionNo: 1, questionNo: 1 },
            )
            .lean();
        //group user response based on section wise
        const constructedUseResponse = new Map();
        userSurveyResponses.forEach(({ sectionNo, questionNo, answer }) => {
            constructedUseResponse.set(`${sectionNo}-${questionNo}`, { answer });
        });
        const totalRatingScaleResponses = [];
        const totalOpenEndedResponses = [];
        surveyDetail.questions.pages.forEach(({ title = '', elements = [], description = '' }) => {
            const ratingScaleResponse = [];
            const userAnswer = [];
            const openEndedResponse = [];
            elements.forEach((questionElement) => {
                const isStudentAnswered = constructedUseResponse.get(
                    `${title}-${questionElement.name}`,
                );

                if (isStudentAnswered) {
                    const { answer } = isStudentAnswered;
                    if (questionElement.type === RATING) {
                        questionElement.mappedOutcomeDetail = getMappedOutcomeDetails({
                            questionNo: questionElement.uniqueId,
                            mappedOutcomeDetails: surveyDetail.mappedOutcomeDetails,
                            surveyLevel: surveyDetail.surveyLevel,
                        });
                        questionElement.mappedTagDetail = getMappedTagDetail({
                            questionNo: questionElement.uniqueId,
                            mappedTagDetails: surveyDetail.mappedTagDetails,
                        });
                        userAnswer.push(answer);
                        ratingScaleResponse.push({
                            question: questionElement,
                            answer,
                        });
                    } else {
                        if (String(answer).trim() !== '') {
                            openEndedResponse.push({
                                question: questionElement,
                                answer,
                                feeling: analyzeSentiment(answer),
                            });
                        }
                    }
                }
            });
            if (ratingScaleResponse.length) {
                totalRatingScaleResponses.push({
                    sectionName: title,
                    surveyDescription: description,
                    mean: calculateMean(userAnswer),
                    standardDeviation: calculateStandard(userAnswer),
                    questions: ratingScaleResponse,
                    userResponseSettings: surveyDetail?.userResponseSettings ?? {},
                });
            }
            if (openEndedResponse.length) {
                totalOpenEndedResponses.push({
                    sectionName: title,
                    questions: openEndedResponse,
                    userResponseSettings: surveyDetail?.userResponseSettings ?? {},
                });
            }
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalRatingScaleResponses,
                totalOpenEndedResponses,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportSentimentAnalysisSection = async ({ query = {} }) => {
    try {
        const { surveyId, responseStatusFilter = [] } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                },
            )
            .lean();
        const { totalSections, totalQuestions } = surveyDetail.questions.pages.reduce(
            (acc, { title = '', elements = [] }) => {
                const openEndedQuestion = elements
                    .filter(({ type = '' }) => type === COMMENT)
                    .map(({ name }) => name);
                if (openEndedQuestion.length) {
                    acc.totalSections.push(title);
                    acc.totalQuestions.push(...openEndedQuestion);
                }
                return acc;
            },
            { totalSections: [], totalQuestions: [] },
        );
        const userSurveyResponsesSectionDetails = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    ...(responseStatusFilter.length && { gender: { $in: responseStatusFilter } }),
                    sectionNo: { $in: totalSections },
                    questionNo: { $in: totalQuestions },
                },
                { sectionNo: 1, userId: 1, questionNo: 1, answer: 1 },
            )
            .lean();
        const uniqueSections = [];
        const individualStudentSentimentCounts = {};
        userSurveyResponsesSectionDetails.forEach(
            ({ sectionNo = '', userId = '', answer = '' }) => {
                if (answer.trim() !== '') {
                    if (!uniqueSections.includes(sectionNo)) {
                        uniqueSections.push(sectionNo);
                    }
                    if (!individualStudentSentimentCounts[userId]) {
                        individualStudentSentimentCounts[userId] = {
                            positive: 0,
                            negative: 0,
                            neutral: 0,
                        };
                    }
                    const sentiment = analyzeSentiment(answer);
                    individualStudentSentimentCounts[userId][sentiment]++;
                }
            },
        );
        const overAllSentimentAnalysis = {
            totalStudent: Object.keys(individualStudentSentimentCounts).length,
            positiveStatement: 0,
            negativeStatement: 0,
            neutralStatement: 0,
        };
        Object.values(individualStudentSentimentCounts).forEach(
            ({ positive, negative, neutral }) => {
                const userOverAllSentiment =
                    (HIGH_SATISFACTION * positive +
                        MODERATE_SATISFACTION * neutral +
                        LOW_SATISFACTION * negative) /
                    (positive + neutral + negative);
                if (userOverAllSentiment <= LOW_SATISFACTION) {
                    overAllSentimentAnalysis.negativeStatement += 1;
                } else if (userOverAllSentiment <= MODERATE_SATISFACTION) {
                    overAllSentimentAnalysis.neutralStatement += 1;
                } else {
                    overAllSentimentAnalysis.positiveStatement += 1;
                }
            },
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { uniqueSections, overAllSentimentAnalysis },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sentimentAnalysisOpenEndedQuestions = async ({ query = {} }) => {
    try {
        const { surveyId, sectionName } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                },
            )
            .lean();
        let openEndedQuestions = [];
        const currentSectionDetail = surveyDetail.questions.pages.find(
            ({ title = '' }) => title === sectionName,
        );
        if (currentSectionDetail) {
            openEndedQuestions = currentSectionDetail.elements
                .filter(({ type = '' }) => type === COMMENT)
                .map(({ title = '', type = '', name = '' }) => ({
                    name: title,
                    type,
                    questionNo: name,
                    sectionName,
                }));
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: openEndedQuestions,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportSentimentAnalysisQuestion = async ({ query = {} }) => {
    try {
        const {
            surveyId,
            sectionName,
            questionNo,
            pageNo,
            limits,
            searchKey,
            responseStatusFilter = [],
        } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    surveyUsers: 1,
                    userResponseSettings: 1,
                    'surveyPublicUserDetails.maxResponse': 1,
                },
            )
            .populate({ path: 'surveyUsers', select: { gender: 1, user_id: 1, name: 1 } })
            .lean();
        const {
            maleUserIds = [],
            femaleUserIds = [],
            constructedUserDetails = new Map(),
        } = getSurveyUserListBasedOnGender({
            surveyUsers: surveyDetail.surveyUsers,
            isStudentDetailsRequired: true,
        });
        const publicUserCount =
            !responseStatusFilter.length || responseStatusFilter.includes(PUBLIC_USER)
                ? surveyDetail.surveyPublicUserDetails.maxResponse || 0
                : 0;
        const totalUserCount =
            (!responseStatusFilter.length || responseStatusFilter.includes(MALE)
                ? maleUserIds.length
                : 0) +
            (!responseStatusFilter.length || responseStatusFilter.includes(FEMALE)
                ? femaleUserIds.length
                : 0) +
            publicUserCount;

        const studentOpenEndedResponse = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    sectionNo: sectionName,
                    ...(responseStatusFilter.length && { gender: { $in: responseStatusFilter } }),
                    questionNo,
                },
                { answer: 1, userId: 1 },
            )
            .lean();

        const {
            individualStudentOpenEndedResponse,
            genderWiseSentimentCounts,
            genderWiseRespondedCount,
        } = studentOpenEndedResponse.reduce(
            (acc, { answer = '', userId = '' }) => {
                if (answer.trim() !== '') {
                    const sentiment = analyzeSentiment(answer);
                    const studentDetail = constructedUserDetails.get(String(userId));
                    acc.individualStudentOpenEndedResponse.push({
                        sentiment,
                        answer,
                        gender: studentDetail.gender,
                        studentId: userId,
                        user_id: studentDetail.user_id,
                        name: studentDetail.name,
                    });
                    if (!acc.genderWiseSentimentCounts[studentDetail.gender]) {
                        acc.genderWiseSentimentCounts[studentDetail.gender] = {
                            positive: 0,
                            negative: 0,
                            neutral: 0,
                        };
                    }
                    acc.genderWiseSentimentCounts[studentDetail.gender][sentiment]++;
                    if (!acc.genderWiseRespondedCount[studentDetail.gender]) {
                        acc.genderWiseRespondedCount[studentDetail.gender] = 0;
                    }
                    acc.genderWiseRespondedCount[studentDetail.gender] += 1;
                }
                return acc;
            },
            {
                individualStudentOpenEndedResponse: [],
                genderWiseSentimentCounts: {},
                genderWiseRespondedCount: {},
            },
        );
        let overAllStudentOpenEndedResponse = individualStudentOpenEndedResponse;
        if (searchKey) {
            const searchTerm = searchKey.toLowerCase();
            overAllStudentOpenEndedResponse = overAllStudentOpenEndedResponse.filter(
                ({ name, user_id, gender }) => {
                    const fullName =
                        `${name.first} ${name.last} ${name.middle} ${name.family}`.toLowerCase();
                    return (
                        fullName.includes(searchTerm) ||
                        (searchTerm === GENDER.MALE
                            ? gender.toLowerCase() === GENDER.MALE
                            : gender.toLowerCase().includes(searchTerm)) ||
                        user_id.toLowerCase().includes(searchTerm)
                    );
                },
            );
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalUserCount,
                totalMaleCount:
                    !responseStatusFilter.length || responseStatusFilter.includes(MALE)
                        ? maleUserIds.length
                        : 0,
                totalFemaleCount:
                    !responseStatusFilter.length || responseStatusFilter.includes(FEMALE)
                        ? femaleUserIds.length
                        : 0,
                totalPublicUserCount:
                    !responseStatusFilter.length || responseStatusFilter.includes(PUBLIC_USER)
                        ? publicUserCount
                        : 0,
                genderWiseRespondedCount,
                genderWiseSentimentCounts,
                questionResponse: paginator(overAllStudentOpenEndedResponse, pageNo, limits),
                userResponseSettings: surveyDetail?.userResponseSettings ?? {},
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const surveyReportStatisticalAnalysis = async ({ query = {} }) => {
    try {
        const {
            surveyId,
            responseStatusFilter = [],
            correlationFormula = PEARSON_CORRELATION_COEFFICIENT,
        } = query;
        const surveyDetail = await surveySchemas
            .findOne(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    questions: 1,
                },
            )
            .lean();
        const totalSections = [];
        const totalQuestions = [];
        surveyDetail.questions.pages.forEach(({ title = '', elements = [] }) => {
            //for statical analysis currently we are not taking a learning outcome questions
            if (title !== LEARNING_OUTCOME_SECTION) {
                totalSections.push(title);
                totalQuestions.push(...elements.map(({ name }) => name));
            }
        });
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    sectionNo: { $in: totalSections },
                    questionType: RATING,
                    ...(responseStatusFilter.length && { gender: { $in: responseStatusFilter } }),
                    questionNo: { $in: totalQuestions },
                },
                { questionNo: 1, answer: 1, sectionNo: 1, gender: 1, userId: 1 },
            )
            .lean();
        const sectionWiseIndividualQuestionReport = [];
        const sectionWiseReport = [];
        const sectionResponses = {};
        const genderWiseRatingCounts = {};
        let totalRespondedCount = 0;
        surveyDetail.questions.pages.forEach(({ title = '', description = '', elements = [] }) => {
            if (title !== LEARNING_OUTCOME_SECTION) {
                const sectionWiseTotalQuestions = [];
                const sectionWiseTotalResponse = [];
                const sectionWiseStandardDeviation = [];
                elements.forEach((questionELement) => {
                    if (questionELement.type === RATING) {
                        const userResponseData = userSurveyResponses.filter(
                            ({ questionNo, sectionNo }) =>
                                title === sectionNo && questionNo === questionELement.name,
                        );
                        const totalUserResponseValue = [];
                        userResponseData.forEach(({ answer = 0, gender = '', userId = '' }) => {
                            totalUserResponseValue.push(answer);
                            totalRespondedCount += 1;
                            if (!genderWiseRatingCounts[userId]) {
                                genderWiseRatingCounts[userId] = {
                                    userTotalPercentage: 0,
                                    userTotalResponseCount: 0,
                                    gender,
                                };
                            }
                            // what ever the options calculate average and check its comes in which satisfaction and increase the count
                            const totalOption =
                                questionELement.rateValues[0].value === 0
                                    ? questionELement.rateValues.length - 1
                                    : questionELement.rateValues.length;
                            const userResponseAverage = (answer / totalOption) * 100;
                            genderWiseRatingCounts[userId].userTotalPercentage +=
                                userResponseAverage;
                            genderWiseRatingCounts[userId].userTotalResponseCount += 1;
                        });
                        sectionWiseTotalQuestions.push({
                            questionNo: questionELement.name,
                            question: questionELement.title,
                            mean: calculateMean(totalUserResponseValue),
                            standardDeviation: calculateStandard(totalUserResponseValue),
                        });
                        sectionWiseTotalResponse.push(calculateMean(totalUserResponseValue));
                        sectionWiseStandardDeviation.push(
                            calculateStandard(totalUserResponseValue),
                        );
                    }
                });
                if (sectionWiseTotalQuestions.length) {
                    sectionWiseIndividualQuestionReport.push({
                        sectionName: title,
                        description,
                        questions: sectionWiseTotalQuestions,
                    });
                    sectionWiseReport.push({
                        sectionName: title,
                        description,
                        mean: calculateMean(sectionWiseTotalResponse),
                        standardDeviation: calculateMean(sectionWiseStandardDeviation),
                    });
                    sectionResponses[title] = sectionWiseTotalResponse;
                }
            }
        });
        const sectionNames = Object.keys(sectionResponses);
        const correlationMatrix = sectionNames.map((sectionX) =>
            sectionNames.map((sectionY) =>
                calculateCorrelation({
                    firstSectionResponse: sectionResponses[sectionX],
                    secondSectionResponse: sectionResponses[sectionY],
                    correlationFormula,
                }),
            ),
        );
        const overAllGenderWiseRatingCount = {
            lowSatisfaction: { male: 0, female: 0, public: 0 },
            moderateSatisfaction: { male: 0, female: 0, public: 0 },
            highSatisfaction: { male: 0, female: 0, public: 0 },
        };
        const incrementSatisfactionCount = (ratingType, gender) => {
            if (gender === MALE) {
                overAllGenderWiseRatingCount[ratingType].male += 1;
            } else if (gender === FEMALE) {
                overAllGenderWiseRatingCount[ratingType].female += 1;
            } else {
                overAllGenderWiseRatingCount[ratingType].public += 1;
            }
        };
        Object.values(genderWiseRatingCounts).forEach(
            ({ userTotalResponseCount, userTotalPercentage, gender }) => {
                const userOverAllAverage = userTotalPercentage / userTotalResponseCount;
                if (userOverAllAverage <= LOW_SATISFACTION) {
                    incrementSatisfactionCount(LOW_SATISFACTION_KEY, gender);
                } else if (userOverAllAverage <= MODERATE_SATISFACTION) {
                    incrementSatisfactionCount(MODERATE_SATISFACTION_KEY, gender);
                } else {
                    incrementSatisfactionCount(HIGH_SATISFACTION_KEY, gender);
                }
            },
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                sectionNames,
                correlationMatrix,
                sectionWiseIndividualQuestionReport,
                sectionWiseReport,
                totalRespondedCount,
                overAllGenderWiseRatingCount,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// AI Services
const surveyReportStudentFeeling = async ({ query = {} }) => {
    try {
        const { surveyId } = query;
        const surveyStudentResponseData = await surveyStudentSubmissionSchemas
            .find(
                {
                    ...dbQuery,
                    surveyId: convertToMongoObjectId(surveyId),
                    'studentResponse.questionType': 'saq',
                },
                {
                    _id: 0,
                    studentId: 1,
                    'studentResponse.questionType': 1,
                    'studentResponse.studentResponse': 1,
                },
            )
            .lean();
        for (studentResponseElement of surveyStudentResponseData) {
            studentResponseElement.studentResponse = studentResponseElement.studentResponse.filter(
                (responseQuestionElement) => responseQuestionElement.questionType === 'saq',
            );
            const sentimentLabel = analyzeSentiment(
                studentResponseElement.studentResponse.map(
                    (studentSAQResponseElement) => studentSAQResponseElement.studentResponse,
                ),
            );
            studentResponseElement.sentiment = sentimentLabel;
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: surveyStudentResponseData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updatedPublishedSurvey = async ({ body = {} }) => {
    try {
        //only status update and isDeleted is handled here..published survey over all edit is handled in publishSurvey api (digiSurveyController.js)
        const { updateType, status, updateId, expiryDate, closedSurveyReason } = body;
        let updateQuery = {};
        const findQuery = { _id: convertToMongoObjectId(updateId) };
        switch (updateType) {
            case DS_DELETE:
                updateQuery = { $set: { isDeleted: true } };
                break;
            case DS_UPDATE:
                updateQuery = { $set: { status, closedSurveyReason } };
                break;
            case EDIT_DURATION:
                updateQuery = { $set: { expiryDate } };
                break;
            default:
                break;
        }

        if (updateType === EDIT_DURATION) {
            const currentDateAndTime = new Date();
            const expireDateAndTime = new Date(expiryDate);
            if (expireDateAndTime <= currentDateAndTime) {
                return {
                    statusCode: 400,
                    message: 'EXPIRE_DATE_MESSAGE',
                };
            }
            const expireTimeInSeconds = getExpireTimeInSeconds({
                endDateAndTime: expireDateAndTime,
                startDateAndTime: currentDateAndTime,
            });
            if (expireTimeInSeconds > 0) {
                await activateRedisTimer({
                    moduleName: SURVEY,
                    uniqueId: updateId,
                    documentStatus: EXPIRE,
                    timeInSeconds: expireTimeInSeconds,
                });
            }
        }
        if (updateType === DS_UPDATE && status === CLOSED) {
            await deleteRedisTimer({
                moduleName: SURVEY,
                uniqueId: updateId,
                documentStatus: EXPIRE,
            });
        }
        const updatedPublishedSurveyData = await surveySchemas.updateOne(findQuery, updateQuery);
        let response = {
            statusCode: 200,
            message: 'PUBLISHED_SURVEY_CHANGES_UPDATED_SUCCESSFULLY',
        };
        if (!updatedPublishedSurveyData.modifiedCount) {
            response = { statusCode: 400, message: 'UNABLE_TO_UPDATE_PUBLISHED_SURVEY_CHANGES' };
        }
        return response;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getTotalPublishedSurveyCount = async ({ query = {}, headers = {} }) => {
    try {
        const { institutionCalendarId, userId, isTemplate } = query;
        const { _institution_id } = headers;
        const surveyList = await surveySchemas
            .find(
                {
                    ...dbQuery,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    createdBy: convertToMongoObjectId(userId),
                    status: { $ne: DRAFT },
                    isTemplate,
                },
                {
                    status: 1,
                },
            )
            .lean();
        const totalPublishedSurveyCount = surveyList.reduce(
            (acc, curr) => {
                switch (curr.status) {
                    case COMPLETED:
                        acc.completed += 1;
                        break;
                    case UPCOMING:
                        acc.upComing += 1;
                        break;
                    case INPROGRESS:
                        acc.inProgress += 1;
                        break;
                    default:
                        acc.closed += 1;
                        break;
                }
                return acc;
            },
            {
                inProgress: 0,
                upComing: 0,
                completed: 0,
                closed: 0,
            },
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: totalPublishedSurveyCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPublishedSurveyHeaderFilters = async ({ query = {}, headers = {} }) => {
    try {
        const {
            institutionCalendarId,
            userId,
            isTemplate,
            surveyLevel,
            status = [],
            surveyType = [],
            programIds = [],
            courseIds = [],
            term = [],
            year = [],
            level = [],
            searchKey,
        } = query;
        const { _institution_id } = headers;
        const surveyTypeList = await surveySchemas
            .find(
                {
                    ...dbQuery,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    createdBy: convertToMongoObjectId(userId),
                    surveyLevel,
                    isTemplate,
                },
                {
                    surveyType: 1,
                    status: 1,
                    surveyName: 1,
                    'multiSelectedProgramIds.programId': 1,
                    'multiSelectedProgramIds.courseId': 1,
                    'multiSelectedProgramIds.term': 1,
                    'multiSelectedProgramIds.yearNo': 1,
                    'multiSelectedProgramIds.levelNo': 1,
                },
            )
            .populate([
                { path: 'multiSelectedProgramIds.programId', select: { name: 1, code: 1 } },
                {
                    path: 'multiSelectedProgramIds.courseId',
                    select: { course_name: 1, course_code: 1 },
                },
            ])
            .lean();
        const uniqueSurveyType = new Set();
        const uniqueStatus = new Set();
        let totalSurveyLevelDocument = 0;
        let selectedProgramDetails = {};
        surveyTypeList.forEach(
            ({
                surveyType: currentSurveyType = '',
                status: currentStatus = '',
                surveyName = '',
                multiSelectedProgramIds = [],
            }) => {
                if (currentStatus !== DRAFT) {
                    uniqueStatus.add(currentStatus);
                }
                uniqueSurveyType.add(currentSurveyType);
                const matchSearchKey = searchKey
                    ? surveyName.toLowerCase().includes(searchKey.trim().toLowerCase())
                    : true;
                const matchStatus = status.length
                    ? status.includes(currentStatus)
                    : currentStatus !== DRAFT;
                const matchSurveyType = surveyType.length
                    ? surveyType.includes(currentSurveyType)
                    : true;
                const matchProgramDetails =
                    !multiSelectedProgramIds.length ||
                    multiSelectedProgramIds.some((programDetailElement) => {
                        const matchProgram =
                            !programIds.length ||
                            programIds.includes(String(programDetailElement?.programId?._id));
                        const matchCourse =
                            !courseIds.length ||
                            courseIds.includes(String(programDetailElement?.courseId?._id));
                        const matchYear =
                            !year.length || year.includes(programDetailElement?.yearNo);
                        const matchLevel =
                            !level.length || level.includes(programDetailElement?.levelNo);
                        const matchTerm = !term.length || term.includes(programDetailElement?.term);

                        return matchProgram && matchCourse && matchYear && matchLevel && matchTerm;
                    });
                if (matchSearchKey && matchStatus && matchSurveyType && matchProgramDetails) {
                    totalSurveyLevelDocument += 1;
                }
                //multi level selected program details for non-learning outcome.
                multiSelectedProgramIds.forEach((multiSelectedElement) => {
                    const groupedProgramKey = String(multiSelectedElement?.programId?._id ?? '');
                    selectedProgramDetails[groupedProgramKey] ??= [];
                    const isDuplicate = selectedProgramDetails[groupedProgramKey].some(
                        (existingItem) =>
                            existingItem.term === multiSelectedElement.term &&
                            existingItem.yearNo === multiSelectedElement.yearNo &&
                            existingItem.levelNo === multiSelectedElement.levelNo &&
                            String(existingItem?.courseId?._id ?? '') ===
                                String(multiSelectedElement?.courseId?._id ?? ''),
                    );
                    if (!isDuplicate) {
                        selectedProgramDetails[groupedProgramKey].push(multiSelectedElement);
                    }
                });
            },
        );
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalSurveyType: [...uniqueSurveyType],
                totalStatus: [...uniqueStatus],
                totalSurveyLevelDocument,
                selectedProgramDetails,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getPublishedSurveyUserList = async ({ query = {} }) => {
    try {
        const { surveyId, searchKey } = query;
        const { limit, skip } = getPaginationValues(query);
        let {
            surveyUsers = [],
            userResponseSettings = {},
            multiSelectedProgramIds = [],
            surveyLevel = '',
        } = await getSurveyUserListFromRedis({
            surveyId,
        });
        if (searchKey) {
            const regex = new RegExp(searchKey, 'i');
            surveyUsers = surveyUsers.filter(({ user_id, name }) => {
                return (
                    regex.test(user_id) ||
                    (name && (regex.test(name.first) || regex.test(name.last)))
                );
            });
        }
        // to indicate whether user attended survey or not
        const totalUserIds = surveyUsers.map(({ _id = '' }) => _id);
        const respondedUsers = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    userId: { $in: totalUserIds },
                },
                { userId: 1 },
            )
            .lean();
        const uniqueRespondedUserIds = new Set(respondedUsers.map(({ userId }) => String(userId)));
        surveyUsers = surveyUsers.map((surveyUserListElement) => {
            return {
                ...surveyUserListElement,
                isUserAnswered: uniqueRespondedUserIds.has(String(surveyUserListElement._id)),
            };
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                paginatedUserDetails: surveyUsers.slice(skip, skip + limit),
                totalUsers: surveyUsers.length,
                userResponseSettings,
                multiSelectedProgramIds,
                surveyLevel,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getTemplateRunnerUserList = async ({ query = {} }) => {
    try {
        const { templateId } = query;
        const templateRunnerUserList = await surveySchemas
            .distinct('createdBy', {
                surveyBankId: convertToMongoObjectId(templateId),
            })
            .lean();
        const templateRunnerUserDetails = templateRunnerUserList?.length
            ? await userSchema
                  .find({ _id: { $in: templateRunnerUserList } }, { name: 1, user_id: 1 })
                  .lean()
            : [];
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: templateRunnerUserDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getTotalPublishedSurveyUserList = async ({ query = {} }) => {
    try {
        const { templateId, userId } = query;
        const { limit, skip } = getPaginationValues(query);
        const publishedSurveyUserList = await surveySchemas
            .find(
                {
                    surveyBankId: convertToMongoObjectId(templateId),
                    createdBy: convertToMongoObjectId(userId),
                },
                { _id: 1 },
            )
            .lean();
        const surveyUserListFromRedis = publishedSurveyUserList?.length
            ? await getTotalPublishedSurveyUserFromRedis({
                  publishedSurveyIds: publishedSurveyUserList.map(({ _id }) => _id),
              })
            : [];
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: surveyUserListFromRedis.slice(skip, skip + limit),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createUserOutcomeReportResponseSetting = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { maxResponseSettings } = body;
        const outComeReportResponseSetting = await userOutcomeReportResponseSettingSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                userId: convertToMongoObjectId(_user_id),
            },
            { $set: { maxResponseSettings } },
            { upsert: true },
        );
        const isUpdateSuccessfully =
            outComeReportResponseSetting.modifiedCount ||
            outComeReportResponseSetting.upsertedCount ||
            outComeReportResponseSetting.matchedCount;
        return {
            statusCode: isUpdateSuccessfully ? 201 : 400,
            message: isUpdateSuccessfully
                ? 'OUTCOME_REPORT_RESPONSE_SETTINGS_UPDATED_SUCCESSFULLY'
                : 'UNABLE_TO_UPDATE_OUTCOME_REPORT_RESPONSE_SETTINGS',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserOutcomeReportResponseSetting = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const outComeReportResponseSetting = await userOutcomeReportResponseSettingSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                userId: convertToMongoObjectId(_user_id),
            },
            { maxResponseSettings: 1 },
        );
        return {
            statusCode: 200,
            message: 'OUTCOME_REPORT_RESPONSE_SETTINGS',
            data: outComeReportResponseSetting
                ? outComeReportResponseSetting.maxResponseSettings
                : [],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSurveyOutcomeReportThemes = async ({ query = {} }) => {
    try {
        const { programId, curriculumName, surveyLevel, courseId } = query;
        let themeDetails = {};
        const projectKeys = {
            'framework._id': 1,
            'framework.name': 1,
            'framework.code': 1,
            'framework.domains._id': 1,
            'framework.domains.name': 1,
            'framework.domains.no': 1,
        };
        if (surveyLevel === DS_PROGRAM_KEY) {
            const programFrameworkDetails = await curriculumSchema
                .findOne(
                    {
                        _program_id: convertToMongoObjectId(programId),
                        curriculum_name: curriculumName,
                    },
                    {
                        ...projectKeys,
                        'framework.domains.plo._id': 1,
                        'framework.domains.plo.name': 1,
                        'framework.domains.plo.no': 1,
                        'framework.domains.plo.isDeleted': 1,
                    },
                )
                .lean();
            if (programFrameworkDetails) {
                themeDetails = programFrameworkDetails.framework;
            }
        }
        if (surveyLevel === DS_COURSE_KEY) {
            const courseFrameworkDetails = await courseSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(courseId),
                    },
                    {
                        ...projectKeys,
                        'framework.domains.clo._id': 1,
                        'framework.domains.clo.name': 1,
                        'framework.domains.clo.no': 1,
                        'framework.domains.clo.isDeleted': 1,
                    },
                )
                .lean();
            if (courseFrameworkDetails) {
                themeDetails = courseFrameworkDetails.framework;
            }
        }

        return {
            statusCode: 200,
            message: 'OUTCOME_REPORT_RESPONSE_SETTINGS',
            data: themeDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getThemeBasedReports = async ({ query = {} }) => {
    try {
        const { surveyId, frameworkId, domainId } = query;
        const surveyDetails = await surveySchemas
            .find(
                {
                    _id: convertToMongoObjectId(surveyId),
                },
                { 'outComeDetails.frameWorkDetails': 1, mappedOutcomeDetails: 1, questions: 1 },
            )
            .lean();
        const totalQuestions = [];
        const constructedOutcomeDetails = new Map();
        surveyDetails.forEach(
            ({
                mappedOutcomeDetails = [],
                outComeDetails: { frameWorkDetails = [] },
                questions,
            }) => {
                mappedOutcomeDetails.forEach((mappedOutcomeDetailsElement) => {
                    if (
                        String(mappedOutcomeDetailsElement.frameWorkId) === String(frameworkId) &&
                        String(mappedOutcomeDetailsElement.domainId) === String(domainId)
                    ) {
                        totalQuestions.push(mappedOutcomeDetailsElement.questionNo);
                        constructedOutcomeDetails.set(
                            mappedOutcomeDetailsElement.questionNo.trim(),
                            {
                                questionNo: mappedOutcomeDetailsElement.questionNo,
                                outcomeId: mappedOutcomeDetailsElement.outcomeId,
                                rateValues: [],
                                students: [],
                            },
                        );
                    }
                });
                frameWorkDetails.forEach((frameWorkDetails) => {
                    if (
                        String(frameWorkDetails.frameWorkId) === String(frameworkId) &&
                        String(frameWorkDetails.domainId) === String(domainId)
                    ) {
                        totalQuestions.push(frameWorkDetails.outcomeName);
                        constructedOutcomeDetails.set(frameWorkDetails.outcomeName.trim(), {
                            questionNo: frameWorkDetails.outcomeName,
                            outcomeId: frameWorkDetails.outcomeId,
                            rateValues: [],
                            students: [],
                        });
                    }
                });
                questions.pages.forEach(({ elements = [] }) => {
                    elements.forEach(({ name = '', rateValues = [] }) => {
                        const existingConstructedOutcomeDetail = constructedOutcomeDetails.get(
                            name.trim(),
                        );
                        if (existingConstructedOutcomeDetail) {
                            existingConstructedOutcomeDetail.rateValues = rateValues;
                        }
                    });
                });
            },
        );
        const userSurveyResponses = await digiSurveyResponseSchema
            .find(
                {
                    surveyId: convertToMongoObjectId(surveyId),
                    questionNo: { $in: totalQuestions },
                },
                { answer: 1, questionNo: 1, userId: 1 },
            )
            .lean();
        userSurveyResponses.forEach(({ answer, questionNo, userId }) => {
            const existingConstructedOutcomeDetail = constructedOutcomeDetails.get(questionNo);
            if (existingConstructedOutcomeDetail) {
                existingConstructedOutcomeDetail.students.push({ userId, answer });
            }
        });
        return {
            statusCode: 200,
            message: 'OUTCOME_REPORT_RESPONSE_SETTINGS',
            data: [...constructedOutcomeDetails.values()],
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getSurveyList,
    getStudentSurveyList,
    surveyAdd,
    surveyAddSectionQuestion,
    surveyStudentSubmission,
    surveyStudentAutoSubmission,
    surveyReportSummaryHeader,
    surveyReportSection,
    surveyReportQuestion,
    surveyReportStudentResponse,
    surveyReportIndividualStudentResponse,
    surveyReportSentimentAnalysisSection,
    surveyReportSentimentAnalysisQuestion,
    surveyReportStatisticalAnalysis,
    surveyReportStudentFeeling,
    getPublishedSurveyCalendarList,
    getSinglePublishedSurveyDetails,
    updatedPublishedSurvey,
    getTotalPublishedSurveyCount,
    getPublishedSurveyHeaderFilters,
    getPublishedSurveyUserList,
    getTemplateRunnerUserList,
    getTotalPublishedSurveyUserList,
    sentimentAnalysisOpenEndedQuestions,
    createUserOutcomeReportResponseSetting,
    getUserOutcomeReportResponseSetting,
    getSurveyOutcomeReportThemes,
    getThemeBasedReports,
    extendSurveyDuration,
};
