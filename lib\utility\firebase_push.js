const firebase = require('firebase-admin');
const base_control = require('../base/base_controller');
const constant = require('./constants');
const notification = require('mongoose').model(constant.NOTIFICATIONS);

module.exports = {
    async firebase_push_notification(firebaseTokens, push_title, push_body, data) {
        const android = [];
        const ios = [];
        const notification_manager_data = [];
        firebaseTokens.forEach((element) => {
            if (element.device_type == 'android') {
                android.push(element.token);
            } else {
                ios.push(element.token);
            }
            notification_manager_data.push({
                insertOne: {
                    document: {
                        notification_type: 'push',
                        notification_priority: 'high',
                        _user_id: element._user_id,
                        notification_to: element.device_type,
                        tokens: element.token,
                        timeToLive: 60 * 60,
                        title: push_title,
                        body: push_body,
                        data: data._id.toString(),
                        status: 'pushed',
                    },
                },
            });
        });
        if (android.length != 0) {
            const message = {
                tokens: android,
                priority: 'high',
                timeToLive: 60 * 60,
                content_available: true,
                data: {
                    title: push_title,
                    body: push_body,
                    session: data._id.toString(),
                },
            };
            await firebase
                .messaging()
                .sendMulticast(message)
                .then((response) => {
                    console.log(
                        response.successCount + ' push messages were sent successfully to Android',
                    );
                })
                .catch((error) => {
                    console.log('Error sending push message:', error);
                });
        }
        if (ios.length != 0) {
            const message = {
                tokens: ios,
                priority: 'high',
                timeToLive: 60 * 60,
                content_available: true,
                data: {
                    title: push_title,
                    body: push_body,
                    session: data._id.toString(),
                },
                notification: {
                    title: push_title,
                    body: push_body,
                },
            };
            await firebase
                .messaging()
                .sendMulticast(message)
                .then((response) => {
                    console.log(
                        response.successCount + ' push messages were sent successfully to IOS',
                    );
                })
                .catch((error) => {
                    console.log('Error sending push message:', error);
                });
        }
        //Need to Push data to notification manager
        await base_control.bulk_write(notification, notification_manager_data);
    },
};

// /**
//  * Remove Program based on _id
//  * @param {array} firebaseTokens
//  * @param {object} push_title
//  * @param {object} push_body
//  * @returns {Promise<is pushed or not>}
//  */
// async function firebase_push_notification(firebaseTokens, push_title, push_body) {
//     const payload = {
//         notification: {
//             title: push_title,
//             body: push_body,
//         }
//     };
//     console.log(firebaseTokens);
//     return firebase.messaging().send(firebaseTokens[0], payload, options)
//     // .then((response) => {
//     //     console.log(response.successCount + ' push messages were sent successfully');
//     // })
//     // .catch((error) => {
//     //     console.log('Error sending push message:', error);
//     // });
// };

// module.exports = {
//     firebase_push_notification
// }
