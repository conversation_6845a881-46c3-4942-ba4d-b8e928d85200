const express = require('express');
const { reportCreditHours } = require('../../reports_analytics/reports_analytics_controller');
const {
    courseScheduleSync,
} = require('../../../commonService/api/sisIntegration/programInput/programInput.controller');
const route = express.Router();

const {
    getAllNotification,
    createNotification,
    updateNotification,
    deleteNotification,
    warningMailForStudent,
    warningMailForStaff,
    getProgramUserDetails,
} = require('./notification_controller');
const catchAsync = require('../../utility/catch-async');
const { apiLimiter } = require('../../../middleware');
const { getProgramUserDetailValidator } = require('../digiSurveyBank/digiSurveyBank.validator');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

// get, create, update,delete notifications
route.get(
    '/:userId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getAllNotification,
);
route.post(
    '/warning-mail',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    warningMailForStudent,
);
route.post(
    '/warning-mail-staff',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    warningMailForStaff,
);
route.post('/', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], createNotification);
route.put(
    '/getProgramUserDetails',
    [userPolicyAuthentication([])],
    getProgramUserDetailValidator,
    catchAsync(getProgramUserDetails),
);
route.put(
    '/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    updateNotification,
);
route.delete('/:id', [userPolicyAuthentication([])], deleteNotification);

// Redis Manual Caching
route.get('/reportCreditHours/:institutionCalendarId', apiLimiter, reportCreditHours);
route.post('/sisScheduleSync', catchAsync(courseScheduleSync));

module.exports = route;
