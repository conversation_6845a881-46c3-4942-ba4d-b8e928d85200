const {
    createTenan<PERSON><PERSON><PERSON><PERSON>,
    getTenants,
    migrateAdminCollections,
} = require('./db-management/tenant/tenant.controller');
const { tenantResolver } = require('./middleware/tenant-resolver');
const catchAsync = require('./utility/catch-async');

const router = require('express').Router();

router.use('/institution', tenantResolver, require('./components/institution/institution.route'));
router.use('/setting', tenantResolver, require('./components/setting/setting.route'));
router.use('/countries', tenantResolver, require('./components/country/country.route'));
router.use('/states', tenantResolver, require('./components/states/states.route'));
router.use('/cities', tenantResolver, require('./components/cities/cities.route'));
router.use('/roles', tenantResolver, require('./components/roles-and-permission/role/role.route'));
router.use(
    '/modules',
    tenantResolver,
    require('./components/roles-and-permission/module/module.route'),
);
router.use(
    '/role-assign',
    tenantResolver,
    require('./components/roles-and-permission/role-assign/role-assign.route'),
);
router.use(
    '/program-input',
    tenantResolver,
    require('./components/program-input/program-input.route'),
);
router.use('/users', tenantResolver, require('./components/user-management/user.route'));
router.use(
    '/accreditation',
    tenantResolver,
    require('./components/accreditation/accreditation.route'),
);
router.use(
    '/departments-and-subjects',
    tenantResolver,
    require('./components/departments-subject/department-subject.route'),
);
router.use('/curriculum', tenantResolver, require('./components/curriculum/curriculum.route'));
router.use(
    '/session-delivery-types',
    tenantResolver,
    require('./components/session-delivery-types/session-delivery-types.route'),
);
router.use('/courses', tenantResolver, require('./components/course/course.route'));
router.use(
    '/session-orders',
    tenantResolver,
    require('./components/session-order/session-order.route'),
);
router.use(
    '/course-groups',
    tenantResolver,
    require('./components/course-group/course-group.route'),
);
router.use('/elastic', require('./components/elasticSearch/elasticsSearch.route'));

router.use(
    '/department-hierarchy',
    tenantResolver,
    require('./components/department-hierarchy/department-hierarchy.route'),
);

//tenant apis
router.post('/onboard-tenant', createTenantHandler);
router.get('/get-tenants', catchAsync(getTenants));
router.post('/migrate-collections', catchAsync(migrateAdminCollections));

module.exports = router;
