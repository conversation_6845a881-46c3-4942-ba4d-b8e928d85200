const Joi = require('joi');
const {
    objectIdRQSchema,
    stringRQSchema,
    numberSchema,
    numberRQSchema,
    objectIdSchema,
    stringSchema,
} = require('../../utility/validationSchemas');

const createRatingValidator = Joi.object({
    institutionCalendarId: objectIdRQSchema,
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    year: stringRQSchema,
    level: stringRQSchema,
    term: stringRQSchema,
    scheduleId: objectIdRQSchema,
    feedBack: stringSchema.min(1).max(500),
    rating: numberRQSchema,
    studentId: objectIdRQSchema,
    staffId: objectIdRQSchema,
    rotationCount: numberSchema,
});

const updateRatingValidator = Joi.object({
    feedBack: stringSchema.min(1).max(500),
    rating: numberSchema,
});

const getRatingValidator = Joi.object({
    scheduleId: objectIdRQSchema,
});

const studentScheduleRatingValidator = Joi.object({
    scheduleId: objectIdRQSchema,
    studentId: objectIdRQSchema,
});

const getRatingsCalculationValidator = Joi.object({
    institutionCalendarId: objectIdSchema,
    courseId: objectIdSchema,
    scheduleId: objectIdSchema,
    staffId: objectIdSchema,
    rating: numberSchema,
    rotationCount: numberSchema,
    programId: objectIdSchema,
    year: stringSchema,
    level: stringSchema,
    term: stringSchema,
    pageNo: numberSchema,
    limit: numberSchema,
});

const multipleSchedulesValidator = Joi.object({
    scheduleIds: Joi.array().items(objectIdRQSchema).required(),
});

module.exports = {
    createRatingValidator,
    updateRatingValidator,
    getRatingValidator,
    getRatingsCalculationValidator,
    studentScheduleRatingValidator,
    multipleSchedulesValidator,
};
