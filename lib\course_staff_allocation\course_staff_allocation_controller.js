const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_functions = require('../utility/common_functions');
const ObjectId = require('mongodb').ObjectID;
const constant = require('../utility/constants');
const course_staff_allocation = require('mongoose').model(constant.COURSE_STAFF_ALLOCATION);
const institution = require('mongoose').model(constant.INSTITUTION);
const program = require('mongoose').model(constant.PROGRAM);
const department = require('mongoose').model(constant.DEPARTMENT);
const subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
var course = require('mongoose').model(constant.COURSE);
var user = require('mongoose').model(constant.USER);

exports.insert = async (req, res) => {
  try {
    let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
    if (!institution_check.status) return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found", 'Institution Not found'));
    //Get program details
    let program_details = await base_control.get(program, { _id: ObjectId(req.body._program_id) }, { _id: 1, name: 1, no: 1 });
    if (!program_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Program Not found", 'Program Not found'));
    let department_details = await base_control.get(department, { _id: ObjectId(req.body._department_id) }, { _id: 1, department_title: 1 });
    if (!department_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Department Not found", 'Department Not found'));
    let subject_details = await base_control.get(subject, { _id: ObjectId(req.body._subject_id) }, { _id: 1, title: 1 });
    if (!subject_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Subject Not found", 'Subject Not found'));
    let course_details = await base_control.get(course, { _id: ObjectId(req.body._course_id) }, { _id: 1, courses_name: 1 });
    if (!course_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Course Not found", 'Course Not found'));
    let course_administrator_details_regular, course_administrator_details_interim;
    if (req.body._course_administrator_regular_id && req.body._course_administrator_regular_id.length != 0) {
      course_administrator_details_regular = await base_control.get(user, { _id: ObjectId(req.body._course_administrator_regular_id) }, { _id: 1, name: 1 });
      if (!course_administrator_details_regular.status) return res.status(404).send(common_files.response_function(res, 404, false, "Regular Course Administrator Not found", 'Regular Course Administrator Not found'));
    }
    if (req.body._course_administrator_interim_id && req.body._course_administrator_interim_id.length != 0) {
      course_administrator_details_interim = await base_control.get(user, { _id: ObjectId(req.body._course_administrator_interim_id) }, { _id: 1, name: 1 });
      if (!course_administrator_details_interim.status) return res.status(404).send(common_files.response_function(res, 404, false, "Interim Course Administrator Not found", 'Interim Course Administrator Not found'));
    }
    let staff_details = await base_control.get(user, { _id: ObjectId(req.body._user_id) }, { _id: 1, name: 1 });
    if (!staff_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Staff Not found", 'Staff Not found'));

    let obj = {
      _institution_id: req.headers._institution_id,
      program: {
        _program_id: program_details.data._id,
        program_name: program_details.data.name
      },
      year: req.body.year,
      level: req.body.level,
      department: {
        _department_id: department_details.data._id,
        department_name: department_details.data.department_title
      },
      subject: {
        _subject_id: subject_details.data._id,
        subject_name: subject_details.data.title
      },
      course: {
        _course_id: course_details.data._id,
        course_name: course_details.data.courses_name
      },
      // course_administrator_regular: {
      //   _staff_id: req.body._course_administrator_regular_id,
      //   name: course_administrator_details_regular.data.name,
      // },
      // course_administrator_interim: {
      //   _staff_id: req.body._course_administrator_interim_id,
      //   name: course_administrator_details_interim.data.name,
      // },
      assigned_by: {
        _staff_id: req.body._user_id,
        name: staff_details.data.name,
      }
    }
    if (req.body._course_administrator_regular_id && req.body._course_administrator_regular_id.length != 0) {
      Object.assign(obj, {
        course_administrator_regular: {
          _staff_id: req.body._course_administrator_regular_id,
          name: course_administrator_details_regular.data.name,
        }
      });
    }
    if (req.body._course_administrator_interim_id && req.body._course_administrator_interim_id.length != 0) {
      Object.assign(obj, {
        course_administrator_interim: {
          _staff_id: req.body._course_administrator_interim_id,
          name: course_administrator_details_interim.data.name,
        }
      });
    }
    let doc = await base_control.insert(course_staff_allocation, obj);
    if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Course Staff Allocation Added successfully", doc.data));
    return res.status(404).send(common_files.response_function(res, 404, false, "Error", doc.data));
  }
  catch (error) {
    console.log(error);
    return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
  }
};

exports.update = async (req, res) => {
  try {
    let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
    if (!institution_check.status) return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found", 'Institution Not found'));
    let course_staff_allocation_details = await base_control.get(course_staff_allocation, { _id: ObjectId(req.params._id) }, {});
    if (!course_staff_allocation_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Course staff allocation Not found", 'Course staff allocation Not found'));
    let course_administrator_details = await base_control.get(user, { _id: ObjectId(req.body._course_administrator_id) }, { _id: 1, name: 1 });
    if (!course_administrator_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Course Administrator Not found", 'Course Administrator Not found'));
    let obj = {
      'course_administrator.name': course_administrator_details.data.name,
      '_staff_id': req.body._course_administrator_id
    };
    let result = await base_control.update_many(course_staff_allocation, ObjectId(req.params._id), obj);
    if (result.status)
      return res.status(200).send(common_files.response_function(res, 200, true, "Course staff allocation updated successfully", result.data));
    return res.status(404).send(common_files.response_function(res, 404, false, "Error", 'Error'));
  }
  catch (error) {
    console.log(error)
    return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
  }
};

exports.program_department_subject_course = async (req, res) => {
  try {
    let program_no_ara = [];
    let program_data = await base_control.get_list_sort(program, { 'isDeleted': false }, {}, { name: -1 });
    let department_data = await base_control.get_list(department, { 'isDeleted': false }, {});
    let subject_data = await base_control.get_list(subject, { 'isDeleted': false }, {});
    let course_data = await base_control.get_list(course, { 'isDeleted': false }, { _id: 1, courses_name: 1, courses_number: 1, study_year: 1, study_level: 1, _administration_subject_id: 1 });
    let course_admin_data = await base_control.get_list(course_staff_allocation, { 'isDeleted': false }, {});
    let user_data = await base_control.get_list(user, { isDeleted: false, user_type: 'staff', "academic_allocation.allocation_type": constant.PRIMARY }, {})
    let department_list = [], subject_list = [], program_list = [], cro = [];
    for (depart of department_data.data) {
      subject_list = [];
      if (depart._division_id.length != 0) {
        for (division_ids of depart._division_id) {
          let sub = (subject_data.data.filter(item => (division_ids).toString() == (item._master_id).toString()));
          for (element of sub) {
            let course_list = course_data.data.filter(item =>
              item._administration_subject_id ?
                ((element._id).toString() == (item._administration_subject_id).toString()) :
                ((element._id).toString() == ('').toString())
            );
            let status = 'Completed';
            if (course_list.length != 0) {
              cro = [];
              course_list.forEach(cro_element => {
                let assigned_staff = course_admin_data.status ? (course_admin_data.data.findIndex(i => (i.course._course_id).toString() == (cro_element._id).toString())) : -1;
                if (status == 'Completed') status = (assigned_staff != -1) ? 'Completed' : 'Pending'
                cro.push({
                  _id: cro_element._id,
                  courses_name: cro_element.courses_name,
                  study_year: cro_element.study_year,
                  study_level: cro_element.study_level,
                  courses_number: cro_element.courses_number,
                  regular_staff_name: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_regular.name.first) ? course_admin_data.data[assigned_staff].course_administrator_regular.name : '',
                  regular_staff_id: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_regular._staff_id) ? course_admin_data.data[assigned_staff].course_administrator_regular._staff_id : '',
                  interim_staff_name: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_interim.name.first) ? course_admin_data.data[assigned_staff].course_administrator_interim.name : '',
                  interim_staff_id: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_interim._staff_id) ? course_admin_data.data[assigned_staff].course_administrator_interim._staff_id : ''
                });
              });
              let academic_subject = [], staff_details = [];
              for (user_element of user_data.data) {
                academic_subject = [];
                for (academic_data of user_element.academic_allocation) {
                  academic_subject = academic_subject.concat(academic_data._department_subject_id);
                }
                if (academic_subject.findIndex(i => ((i).toString() == (element._id).toString())) != -1) {
                  staff_details.push({
                    _id: user_element._id,
                    name: user_element.name
                  });
                }
              }
              subject_list.push({
                subject_name: element.title,
                _subject_id: element._id,
                staffs: staff_details,
                status: status,
                courses: cro
              });
            }
          };
        }
      } else if (depart._subject_id.length != 0) {
        let sub = (subject_data.data.filter(item => (depart._id).toString() == (item._master_id).toString()));
        for (element of sub) {
          let course_list = course_data.data.filter(item =>
            item._administration_subject_id ?
              ((element._id).toString() == (item._administration_subject_id).toString()) :
              ((element._id).toString() == ('').toString())
          );
          let status = 'Completed';
          if (course_list.length != 0) {
            cro = [];
            course_list.forEach(cro_element => {
              let assigned_staff = course_admin_data.status ? (course_admin_data.data.findIndex(i => (i.course._course_id).toString() == (cro_element._id).toString())) : -1;
              if (status == 'Completed') status = (assigned_staff != -1) ? 'Completed' : 'Pending'
              cro.push({
                _id: cro_element._id,
                courses_name: cro_element.courses_name,
                study_year: cro_element.study_year,
                study_level: cro_element.study_level,
                courses_number: cro_element.courses_number,
                regular_staff_name: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_regular.name.first) ? course_admin_data.data[assigned_staff].course_administrator_regular.name : '',
                regular_staff_id: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_regular._staff_id) ? course_admin_data.data[assigned_staff].course_administrator_regular._staff_id : '',
                interim_staff_name: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_interim.name.first) ? course_admin_data.data[assigned_staff].course_administrator_interim.name : '',
                interim_staff_id: (assigned_staff != -1 && course_admin_data.data[assigned_staff].course_administrator_interim._staff_id) ? course_admin_data.data[assigned_staff].course_administrator_interim._staff_id : ''
              });
            });
            let academic_subject = [], staff_details = [];
            for (user_element of user_data.data) {
              academic_subject = [];
              for (academic_data of user_element.academic_allocation) {
                academic_subject = academic_subject.concat(academic_data._department_subject_id);
              }
              if (academic_subject.findIndex(i => ((i).toString() == (element._id).toString())) != -1) {
                staff_details.push({
                  _id: user_element._id,
                  name: user_element.name
                });
              }
            }
            subject_list.push({
              subject_name: element.title,
              _subject_id: element._id,
              staffs: staff_details,
              status: status,
              courses: cro
            });
          }
        };
      }
      if (subject_list.length != 0)
        department_list.push({
          _program_id: depart._program_id,
          _department_id: depart._id,
          department_name: depart.department_title,
          subject: subject_list
        });
    }
    for (pro of program_data.data) {
      let ver = pro.name.substring((pro.name.length - 3), (pro.name.length));
      let pro_name = pro.name.substring(0, (pro.name.length - 4));
      if (department_list.length != 0)
        program_list.push({
          _id: pro._id,
          name: pro_name,
          program_no: pro.no,
          version: ver,
          term: pro.interim,
          department: (department_list.filter(item => (item._program_id).toString() == (pro._id).toString()))
        });
    }
    let pds = [];
    for (main_element of program_list) {
      let dep = [];
      if (program_no_ara.indexOf(main_element.program_no) == -1) {
        let pros = program_list.filter(item => (item.program_no) == main_element.program_no);
        pros.forEach(element => {
          dep = dep.concat(element.department);
        });
        if (dep.length != 0)
          pds.push({
            _id: main_element._id,
            name: main_element.name,
            program_no: main_element.program_no,
            version: main_element.version,
            batch: main_element.term,
            department: dep
          });
        program_no_ara.push(main_element.program_no);
      }
    }
    //Need to add staff details in this area
    return res.status(200).send(common_files.response_function(res, 200, true, "Program Department Subject Course Staff List", pds));
  } catch (error) {
    console.log(error);
    return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
  }
};
/*
exports.admin_remove = async (req, res) => {
  try {
    let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
    if (!institution_check.status) return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found", 'Institution Not found'));
    //Get program details
    let program_details = await base_control.get(program, { _id: ObjectId(req.body._program_id) }, { _id: 1, name: 1, no: 1 });
    if (!program_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Program Not found", 'Program Not found'));
    let department_details = await base_control.get(department, { _id: ObjectId(req.body._department_id) }, { _id: 1, department_title: 1 });
    if (!department_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Department Not found", 'Department Not found'));
    let subject_details = await base_control.get(subject, { _id: ObjectId(req.body._subject_id) }, { _id: 1, title: 1 });
    if (!subject_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Subject Not found", 'Subject Not found'));
    let course_details = await base_control.get(course, { _id: ObjectId(req.body._course_id) }, { _id: 1, courses_name: 1 });
    if (!course_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Course Not found", 'Course Not found'));
    let obj = {
      _institution_id: ObjectId(req.headers._institution_id), 'program._program_id': ObjectId(req.body._program_id),
      'department._department_id': ObjectId(req.body._department_id), 'subject._subject_id': ObjectId(req.body._subject_id),
      'course._course_id': ObjectId(req.body._course_id)
    };
    let doc = await base_control.update_condition(course_staff_allocation, obj, { $set: { "isDeleted": true } });
    if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Course Staff Removed Added successfully", doc.data));
    return res.status(404).send(common_files.response_function(res, 404, false, "Error", doc.data));
  }
  catch (error) {
    console.log(error);
    return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
  }
}; */

exports.admin_remove = async (req, res) => {
  try {
    let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
    if (!institution_check.status) return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found", 'Institution Not found'));
    //Get program details
    let program_details = await base_control.get(program, { _id: ObjectId(req.body._program_id) }, { _id: 1, name: 1, no: 1 });
    if (!program_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Program Not found", 'Program Not found'));
    let department_details = await base_control.get(department, { _id: ObjectId(req.body._department_id) }, { _id: 1, department_title: 1 });
    if (!department_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Department Not found", 'Department Not found'));
    let subject_details = await base_control.get(subject, { _id: ObjectId(req.body._subject_id) }, { _id: 1, title: 1 });
    if (!subject_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Subject Not found", 'Subject Not found'));
    let course_details = await base_control.get(course, { _id: ObjectId(req.body._course_id) }, { _id: 1, courses_name: 1 });
    if (!course_details.status) return res.status(404).send(common_files.response_function(res, 404, false, "Course Not found", 'Course Not found'));
    let query = {
      _institution_id: ObjectId(req.headers._institution_id), 'program._program_id': ObjectId(req.body._program_id),
      'department._department_id': ObjectId(req.body._department_id), 'subject._subject_id': ObjectId(req.body._subject_id),
      'course._course_id': ObjectId(req.body._course_id)
    };
    let obj = {};
    if (req.body.batch == 'regular') {
      obj = { $set: { "course_administrator_regular": {} } };
    } else {
      obj = { $set: { "course_administrator_interim": {} } };
    }
    let doc = await base_control.update_condition(course_staff_allocation, query, obj);
    if (doc.status) return res.status(200).send(common_files.response_function(res, 200, true, "Course Staff Removed Added successfully", doc.data));
    return res.status(404).send(common_files.response_function(res, 404, false, "Error", doc.data));
  }
  catch (error) {
    console.log(error);
    return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
  }
};