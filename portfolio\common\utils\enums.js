const enums = Object.freeze({
    ON_GOING: 'ON_GOING',
    PUBLISHED: 'PUBLISHED',
    COMPLETED: 'COMPLETED',
    NOT_STARTED: 'NOT_STARTED',

    // Rubrics
    POINT: 'POINT',
    PERCENTAGE: 'PERCENTAGE',
    DIMENSIONAL: 'DIMENSIONAL',
    COMPONENTIAL: 'COMPONENTIAL',
    HEADER: 'HEADER',
    SUB_HEADER: 'SUB_HEADER',
    YET_TO_EVALUATE: 'YET_TO_EVALUATE',
    STARTED: 'STARTED',
    CUSTOM: 'CUSTOM',
    ANGOFF: 'ANGOFF',
    BORDER_LINE_REGRESSION: 'BORDER_LINE_REGRESSION',
    INTERNAL: 'INTERNAL',
    EXTERNAL: 'EXTERNAL',
    DRAFT: 'DRAFT',
    SENDING: 'SENDING',
    SENT: 'SENT',
    EVALUATED: 'EVALUATED',
    LB: 'LB',
    REJECTED: 'REJECTED',
    APPROVED: 'APPROVED',
    ABSENT: 'ABSENT',
    PRESENT: 'PRESENT',
    PASS: 'PASS',
    FAIL: 'FAIL',
});

module.exports = enums;
