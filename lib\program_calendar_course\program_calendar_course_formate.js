let program_calendar_formate = require('../program_calendar/program_calendar_formate');

module.exports = {
    program_calendar_course: (docs) => {
        let formate_obj = [];
        docs.forEach(doc => {
            let obj = {
                _id: doc._id,
                course: doc.course,
                duration_weel: doc.duration_weel,
                start_date: doc.start_date,
                end_date: doc.end_date,
                program_calendar: program_calendar_formate.program_calendar_ID_Only(doc.program_calendar),
                isDeleted: doc.isDeleted,
                isActive: doc.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    program_calendar_course_ID: (doc) => {
        let obj = {
            _id: doc._id,
            course: doc.course,
            duration_weel: doc.duration_weel,
            start_date: doc.start_date,
            end_date: doc.end_date,
            program_calendar: program_calendar_formate.program_calendar_ID_Only(doc.program_calendar),
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_calendar_course_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            course: doc.course,
            duration_weel: doc.duration_weel,
            start_date: doc.start_date,
            end_date: doc.end_date,
            program_calendar: doc._program_calendar_id,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        return obj;
    },

    program_calendar_course_ID_Array_Only: (docs) => {
        let formate_obj = [];
        docs.forEach(doc => {
            let obj = {
                _id: doc._id,
                course: doc.course,
                duration_weel: doc.duration_weel,
                start_date: doc.start_date,
                end_date: doc.end_date,
                program_calendar: doc._program_calendar_id,
                isDeleted: doc.isDeleted,
                isActive: doc.isActive
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}