const express = require('express');
const route = express.Router();
const {
    multiScheduleListing,
    scheduleStart,
    scheduleEnd,
    attendanceReport,
    attendanceChange,
    scheduleClose,
    mergedScheduleListing,
} = require('./staffMultiSchedule.controller');
// const validator = require('./staffMultiSchedule.validator');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get(
    '/scheduleListing',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    multiScheduleListing,
);
route.get(
    '/mergedScheduleListing',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    mergedScheduleListing,
);
route.post('/scheduleStart', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], scheduleStart);
route.post('/scheduleEnd', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], scheduleEnd);
route.post(
    '/attendanceReport',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    attendanceReport,
);
route.post(
    '/attendanceChange',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    attendanceChange,
);
route.post('/scheduleClose', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], scheduleClose);

module.exports = route;
