const userDeviceService = require('./userDevice.service');
const { logger } = require('../utility/util_keys');

/**
 * Get user registered devices by userId
 * @param {Object} query - Request query parameters
 */
exports.getUserRegisteredDevices = async ({ query = {} }) => {
    try {
        const { userId } = query;

        const result = await userDeviceService.getUserRegisteredDevices(userId);

        if (result.status) {
            return {
                statusCode: 200,
                message: 'USER_DEVICES_RETRIEVED_SUCCESSFULLY',
                data: result.data,
            };
        }
        return {
            statusCode: 500,
            message: 'ERROR_RETRIEVING_USER_DEVICES',
            error: result.message,
        };
    } catch (error) {
        logger.error(error, 'Error retrieving user registered devices');
        return {
            statusCode: 500,
            message: 'ERROR_RETRIEVING_USER_DEVICES',
            error: error.message,
        };
    }
};

/**
 * Deactivate user device to allow new device registration
 * @param {Object} headers - Request headers
 * @param {Object} body - Request body
 */
exports.deactivateUserDevice = async ({ headers = {}, body = {} }) => {
    try {
        const { userId, dbRecordId, unregisteredReason } = body;
        const { user_id: deactivatedBy } = headers;

        const result = await userDeviceService.deactivateUserDevice({
            userId,
            dbRecordId,
            deactivatedBy,
            unregisteredReason: unregisteredReason || 'User requested device deactivation',
        });

        if (result.status) {
            return {
                statusCode: 200,
                message: 'DEVICE_DEACTIVATED_SUCCESSFULLY',
            };
        }
        return {
            statusCode: 400,
            message: 'DEVICE_DEACTIVATION_FAILED',
        };
    } catch (error) {
        logger.error(error, 'Error deactivating user device');
        return {
            statusCode: 500,
            message: 'ERROR_DEACTIVATING_USER_DEVICE',
            error: error.message,
        };
    }
};
