const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const {
    STUDENT_WARNING_RECORD,
    DIGI_PROGRAM,
    DIGI_COURSE,
    INSTITUTION_CALENDAR,
    USER,
} = require('../utility/constants');

const schema = new mongoose.Schema(
    {
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        programId: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
        },
        courseId: {
            type: ObjectId,
            ref: DIGI_COURSE,
        },
        yearNo: String,
        levelNo: String,
        term: String,
        rotationCount: Number,
        warnings: Object,
    },
    { timestamps: true },
);

module.exports = mongoose.model(STUDENT_WARNING_RECORD, schema);
