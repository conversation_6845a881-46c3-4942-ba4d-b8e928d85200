const express = require('express');
const { authMiddleware } = require('../../../middleware');
const { serviceStaticAuth } = require('../../../middleware/auth.middleware');
const route = express.Router();
const {
    scheduleStart,
    scheduleStartInTab,
    scheduleDetails,
    scheduleStudentAttendance,
    scheduleCloseWithAttendance,
} = require('./schedule-multi-device-attendance.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
// const validator = require('./schedule-attendance.validator');

route.post(
    '/scheduleStart',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    scheduleStart,
);
route.post(
    '/scheduleStartInTab',
    serviceStaticAuth,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    scheduleStartInTab,
);
route.post(
    '/scheduleStudentAttendance',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    scheduleStudentAttendance,
);
route.post(
    '/scheduleCloseWithAttendance/:scheduleId',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    scheduleCloseWithAttendance,
);
route.get(
    '/scheduleDetails/:scheduleId',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    scheduleDetails,
);
// route.post('/scheduleRetake', scheduleRetake);

module.exports = route;
