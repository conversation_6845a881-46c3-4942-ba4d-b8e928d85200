const express = require('express');
const route = express.Router();
const staff_committee = require('./staff_committee_controller');
const validator = require('./staff_committee_validator');


route.post('/create_committee',validator.committee,staff_committee.create_committee);
route.get('/get_committee', staff_committee.get_committee);
route.get('/get_committee/:id',validator.committee_id, staff_committee.get_committee_id);
route.put('/update_committee/:id',validator.committee,validator.committee_id, staff_committee.update_committee);
route.post('/add_members/',validator.members,staff_committee.add_members);
route.delete('/delete_members/:id/:memberid',validator.committee_id,validator.member_id, staff_committee.delete_members);
route.delete('/delete_committee/:id',validator.committee_id,staff_committee.delete_committee);
route.get('/get_admin/:id' ,validator.admmin_id,staff_committee.get_admin);
route.get('/get_members_staff/:user_employment_type/:user_type/:role' ,staff_committee.getmembersbystaff);
module.exports = route;