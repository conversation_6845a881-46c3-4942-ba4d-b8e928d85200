const constant = require('../utility/constants');
const infrastructure_management = require('mongoose').model(constant.INFRASTRUCTURE_MANAGEMENT);
const institution = require('mongoose').model(constant.INSTITUTION);
// const time_group = require('mongoose').model(constant.TIME_GROUP);
const base_control = require('../base/base_controller');
const course_schedule = require('mongoose').model(constant.COURSE_SCHEDULE);
const common_files = require('../utility/common');
// const infrastructure_management_formate = require('./infrastructure_management_formate');
const ObjectId = common_files.convertToMongoObjectId;
exports.list = async (req, res) => {
    /* const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0; */
    const pageNo = req.query.pageNo ? Number(req.query.pageNo) : 0;
    const limits = Number(req.query.limit);
    const search_key = req.query.search_key ? req.query.search_key : undefined;
    let aggre = {};
    if (search_key) {
        //const expression = `.*${search_key}.*`;
        const regex = new RegExp(search_key, 'i');
        //console.log(search_key.slice(0, 1));
        //console.log(search_key.slice(1));

        aggre = [
            {
                $match: {
                    $or: [
                        {
                            _institution_id: ObjectId(req.headers._institution_id),
                            isDeleted: false,
                            isActive: true,
                            room_no: regex,
                        },
                        {
                            _institution_id: ObjectId(req.headers._institution_id),
                            isDeleted: false,
                            isActive: true,
                            name: regex,
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: 'time_groups',
                    localField: 'timing._time_id',
                    foreignField: '_id',
                    as: 'timing',
                },
            },
            /* { $skip: skips },
            { $limit: limits }, */
        ];
    } else {
        aggre = [
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                    isActive: true,
                },
            },
            {
                $lookup: {
                    from: 'time_groups',
                    localField: 'timing._time_id',
                    foreignField: '_id',
                    as: 'timing',
                },
            },
            /* { $skip: skips },
            { $limit: limits }, */
        ];
    }

    const doc = await base_control.get_aggregate(infrastructure_management, aggre);
    const { data, total_pages } = await common_files.paginator(doc.data, pageNo, limits);

    if (!doc.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('NO_DATA_FOUND'),
                    [],
                ),
            );
    return res
        .status(200)
        .send(
            common_files.listAllResponseFunctionWithRequest(
                req,
                200,
                true,
                req.t('INFRASTRUCTURE_MANAGEMENT_LIST'),
                doc.data.length,
                total_pages,
                pageNo,
                doc.data,
            ),
        );

    // let query = { '_institution_id': ObjectId(req.headers._institution_id), 'isDeleted': false };
    //let doc = await base_control.list(infrastructure_management, req.query.limit, req.query.pageNo, query, {});
    // const limit = req.query.limit;
    // const page = req.query.pageNo;
    // const perPage = parseInt(limit > 0 ? limit : 10);
    // const pageNo = parseInt(page > 0 ? page : 1);
    // infrastructure_management
    //     .aggregate([
    //         { $match: { isDeleted: false } },
    //         //{ $unwind: "$timing" },
    //         {
    //             $lookup: {
    //                 from: 'time_groups',
    //                 localField: 'timing._time_id',
    //                 foreignField: '_id',
    //                 as: 'timing',
    //             },
    //         },
    //         { $skip: perPage * (pageNo - 1) },
    //         { $limit: perPage },
    //     ])
    //     .exec((err, result) => {
    //         const infra_management_list = result.filter(
    //             (ele) =>
    //                 ele.isDeleted == false && ele._institution_id == req.headers._institution_id,
    //         );
    //         response_obj = {
    //             status: true,
    //             totalDoc: infra_management_list.length,
    //             totalPages: Math.ceil(infra_management_list.length / perPage),
    //             currentPage: pageNo,
    //             data: infra_management_list,
    //         };
    //         //common_files.list_all_response(res, 200, true, "program list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ program_formate.program(doc.data));
    //         if (infra_management_list.length > 0)
    //             return res
    //                 .status(200)
    //                 .send(
    //                     common_files.list_all_response_function(
    //                         res,
    //                         200,
    //                         true,
    //                         'Infrastructure management list',
    //                         response_obj.totalDoc,
    //                         response_obj.totalPages,
    //                         Number(req.query.pageNo),
    //                         infra_management_list,
    //                     ),
    //                 );
    //         return res
    //             .status(200)
    //             .send(common_files.response_function(res, 200, false, 'No Data', response_obj));
    //         //res.status(200).send(common_files.list_all_response_function(res, 200, true, " List", doc.totalDoc, doc.totalPages, doc.currentPage, infrastructure_management_formate.infrastructure_management(doc.data)));
    //     });
};

exports.list_id = async (req, res) => {
    const query = {
        _id: ObjectId(req.params._id),
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const doc = await base_control.get(infrastructure_management, query, {});
    if (doc.status) {
        // const formatted = infrastructure_management_formate.infrastructure_management_ID(doc.data);
        common_files.com_response(res, 200, true, req.t('LIST'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR_UNABLE_TO_FIND'), doc.data);
    }
};

exports.insert = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //Check Duplicate
        const query_infra = {
            isDeleted: false,
            _institution_id: ObjectId(req.headers._institution_id),
        };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query_infra,
        );

        const obj = {
            _building_id: req.body._building_id,
            building_name: req.body.building_name,
            floor_no: req.body.floor_no,
            zone: req.body.zone,
            room_no: req.body.room_no,
            name: req.body.name,
            usage: req.body.usage,
            delivery_type: req.body.delivery_type,
            timing: req.body.timing,
            program: req.body.program,
            department: req.body.department,
            subject: req.body.subject,
            capacity: req.body.capacity,
            reserved: req.body.reserved,
            _institution_id: req.body._institution_id,
        };
        if (process.env.OUTSIDE_CAMPUS && process.env.OUTSIDE_CAMPUS === 'true') {
            obj.outsideCampus = req.body.outsideCampus;
            obj.radius = req.body.radius;
            obj.latitude = req.body.latitude;
            obj.longitude = req.body.longitude;
        }
        if (infrastructure_management_data.status) {
            //filter with building name and floor
            const infra_data = infrastructure_management_data.data.filter(
                (ele) =>
                    ele._building_id.equals(obj._building_id) &&
                    ele.floor_no == obj.floor_no &&
                    ele.room_no == obj.room_no,
            );

            for (let i = 0; i < infra_data.length; i++) {
                const timing_arr = infra_data[i].timing.map((ele) => ele._time_id);
                const post_timing = obj.timing.map((ele) => ele._time_id);
                const post_zone = obj.zone;
                //const t_arr = [];
                const z_arr = [];
                //console.log(infra_data[i].zone);
                //console.log(timing_arr);
                if (
                    infra_data[i].zone.length == post_zone.length &&
                    timing_arr.length == post_timing.length
                ) {
                    for (let z = 0; z < post_zone.length; z++) {
                        const zone_ind = infra_data[i].zone.findIndex((ele) => ele == post_zone[z]);
                        if (zone_ind != -1) z_arr.push(zone_ind);
                    }
                    //Timing
                    /* for (let p = 0; p < post_timing.length; p++) {
                        const timing_ind = timing_arr.findIndex((ele) =>
                            ele.equals(post_timing[p]),
                        );
                        if (timing_ind != -1) t_arr.push(timing_ind);
                    } */
                } //console.log(post_zone, z_arr); console.log(post_timing, t_arr);
                //if (post_zone.length == z_arr.length && post_timing.length == t_arr.length)
                if (post_zone.length == z_arr.length)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('DUPLICATE_RECORD'),
                                obj,
                            ),
                        );
            }
        }
        const doc = await base_control.insert(infrastructure_management, obj);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //Check Duplicate
        const query_infra = {
            isDeleted: false,
            _institution_id: ObjectId(req.headers._institution_id),
            _id: { $ne: ObjectId(req.params._id) },
        };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query_infra,
        );
        const obj = {
            _building_id: req.body._building_id,
            building_name: req.body.building_name,
            floor_no: req.body.floor_no,
            zone: req.body.zone,
            room_no: req.body.room_no,
            name: req.body.name,
            usage: req.body.usage,
            delivery_type: req.body.delivery_type,
            timing: req.body.timing,
            program: req.body.program,
            department: req.body.department,
            subject: req.body.subject,
            capacity: req.body.capacity,
            reserved: req.body.reserved,
            radius: req.body.radius,
            latitude: req.body.latitude,
            longitude: req.body.longitude,
            outsideCampus: req.body.outsideCampus,
        };
        if (infrastructure_management_data.status) {
            //filter with building name and floor
            const infra_data = infrastructure_management_data.data.filter(
                (ele) => ele._building_id.equals(obj._building_id) && ele.floor_no == obj.floor_no,
            );
            for (let i = 0; i < infra_data.length; i++) {
                const timing_arr = infra_data[i].timing.map((ele) => ele._time_id);
                const post_timing = obj.timing.map((ele) => ele._time_id);
                const post_zone = obj.zone;
                const t_arr = [];
                const z_arr = [];
                if (
                    infra_data[i].zone.length == post_zone.length &&
                    timing_arr.length == post_timing.length
                ) {
                    for (let z = 0; z < post_zone.length; z++) {
                        const zone_ind = infra_data[i].zone.findIndex((ele) => ele == post_zone[z]);
                        if (zone_ind != -1) z_arr.push(zone_ind);
                    }
                    for (let p = 0; p < post_timing.length; p++) {
                        const timing_ind = timing_arr.findIndex((ele) =>
                            ele.equals(post_timing[p]),
                        );
                        if (timing_ind != -1) t_arr.push(timing_ind);
                    }
                }
                console.log(post_zone, z_arr);
                console.log(post_timing, t_arr);
                if (
                    (post_zone.length !== 0 || z_arr.length !== 0) &&
                    post_zone.length == z_arr.length &&
                    post_timing.length == t_arr.length
                )
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('DUPLICATE_RECORD'),
                                obj,
                            ),
                        );
            }
        }
        const query = {
            _id: ObjectId(req.params._id),
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const doc = await base_control.update(infrastructure_management, query, obj);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('UPDATE_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.delete = async (req, res) => {
    // Course Schedule Check
    const { status: cs_status } = await base_control.get(
        course_schedule,
        {
            _institution_id: ObjectId(req.headers._institution_id),
            // _institution_calendar_id: ObjectId(_institution_calendar_id),
            _infra_id: ObjectId(req.params._id),
            isDeleted: false,
            isActive: true,
        },
        { _id: 1 },
    );
    if (cs_status)
        return res
            .status(409)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    409,
                    false,
                    req.t('UNABLE_TO_REMOVE_INFRA_BECAUSE_ITS_SCHEDULED'),
                    req.t('UNABLE_TO_REMOVE_INFRA_BECAUSE_ITS_SCHEDULED'),
                ),
            );

    const query = {
        _id: ObjectId(req.params._id),
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const doc = await base_control.delete(infrastructure_management, query);
    if (doc.status)
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DELETED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    return res
        .status(500)
        .send(common_files.responseFunctionWithRequest(req, 500, false, req.t('ERROR'), doc.data));
};

exports.search = async (req, res) => {
    try {
        const search_key = req.query.search_key;
        const expression = `.*${search_key}.*`;
        const aggre = [
            {
                $match: {
                    $or: [
                        {
                            _institution_id: ObjectId(req.headers._institution_id),
                            isDeleted: false,
                            isActive: true,
                            room_no: new RegExp(expression),
                        },
                        {
                            _institution_id: ObjectId(req.headers._institution_id),
                            isDeleted: false,
                            isActive: true,
                            name: new RegExp(expression),
                        },
                    ],
                },
            },
            {
                $lookup: {
                    from: 'time_groups',
                    localField: 'timing._time_id',
                    foreignField: '_id',
                    as: 'timing',
                },
            },
        ];
        const doc = await base_control.get_aggregate(infrastructure_management, aggre);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('INFRASTRUCTURE_MANAGEMENT_LIST'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(common_files.response_function(res, 200, false, req.t('NO_DATA_FOUND'), []));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
