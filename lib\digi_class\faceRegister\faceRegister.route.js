const express = require('express');
const router = express.Router();
const catchAsync = require('../../utility/catch-async');
const {
    reRegisterFace,
    getBucketData,
    faceRegisterUserList,
    userBasedScheduleList,
    faceRegisterApproval,
} = require('./faceRegister.controller');
const { uploadFaceBiometric } = require('./faceRegister.service');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

router.post(
    '/faceUpload',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    uploadFaceBiometric,
    reRegisterFace,
);
router.post(
    '/faceGet',
    [userPolicyAuthentication(['user_management:staff_management:re-register_request:list_view'])],
    catchAsync(getBucketData),
);
router.post(
    '/faceRegisterApproval',
    [
        userPolicyAuthentication([
            'user_management:staff_management:re-register_request:approve/reject',
        ]),
    ],
    catchAsync(faceRegisterApproval),
);
router.get(
    '/reRegisterFaceList',
    [
        userPolicyAuthentication([
            'user_management:staff_management:re-register_request:view',
            'user_management:staff_management:re-register_request:search',
        ]),
    ],
    faceRegisterUserList,
);
router.get(
    '/userBasedScheduleList',
    [userPolicyAuthentication(['user_management:staff_management:re-register_request:list_view'])],
    catchAsync(userBasedScheduleList),
);

module.exports = router;
