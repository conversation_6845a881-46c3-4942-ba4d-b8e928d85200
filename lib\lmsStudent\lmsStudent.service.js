const {
    LMS,
    STUDENT,
    REGULAR,
    ABSENT,
    COMPLETED,
    PENDING,
    CA<PERSON><PERSON>LED,
    APPROVED,
    W<PERSON><PERSON><PERSON><PERSON><PERSON>,
    STUDENT_GROUP,
    COURSE_SCHEDULE,
    PROGRAM_CALENDAR,
    ATTENDANCE_DEFAULT_END_TIME,
    EVENT,
    <PERSON><PERSON><PERSON><PERSON>H<PERSON>,
    SUPPORT_SESSION,
    PM,
    PRESENT,
    STUDENT_CRITERIA_MANIPULATION,
    COURSE,
    LEAVE,
    BY_USER,
    NOT_INITIATED,
    ROLE_LIST,
    ROLE_BASED,
    USER_BASED,
    NOT_APPLICABLE,
    ALL_USERS,
    ANY_ONE_IN_EACH_ROLE,
    ANY_ONE_USER,
    REJECT,
    <PERSON>OR<PERSON><PERSON>,
    <PERSON><PERSON>Y<PERSON>,
    <PERSON><PERSON>PED,
    ANY_ONE_IN_ANY_ROLE,
    OVERWRITE_CANCELLED,
    COURSE_COORDINATOR,
    REDIS_FOLDERS: { LMS_REPORT },
    SCHEDULE_STAFF_DEFAULT_ROLE,
} = require('../utility/constants');
const lmsStudentSetting = require('../lmsStudentSetting/lmsStudentSetting.model');
const { get, get_list } = require('../base/base_controller');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const Course = require('../models/digi_course');
const CourseSchedule = require('../models/course_schedule');
const ProgramCalendar = require('../models/program_calendar');
const User = require('../models/user');
const cs = (str) => str.toString();
const { convertToMongoObjectId, clone, convertToUtcTimeFormat } = require('../utility/common');
const lms = require('mongoose').model(LMS);
const studentGroup = require('mongoose').model(STUDENT_GROUP);
const moment = require('moment');
const lmsStudentModel = require('./lmsStudent.model');
const { redisClient } = require('../../config/redis-connection');
const getRatingByCourses = async (
    institutionCalendarId,
    courseIds,
    staffId,
    year,
    level,
    term,
    rotation,
    rotationCount,
) => {
    try {
        let courseQuery;
        let yearQuery;
        let levelQuery;
        let rotationQuery;
        let rotationCountQuery;
        if (courseIds && courseIds.length === 24) {
            courseQuery = [courseIds];
            yearQuery = [year];
            levelQuery = [level];
            termQuery = [term];
            rotationQuery = [rotation];

            rotationCountQuery = [rotationCount];
        } else {
            courseQuery = courseIds;
            yearQuery = year;
            levelQuery = level;
            rotationQuery = rotation;
            termQuery = term;
            rotationCountQuery = rotationCount;
        }
        const fbQuery = {
            _course_id: { $in: courseQuery },
            year_no: { $in: yearQuery },
            level_no: { $in: levelQuery },
            term: { $in: termQuery },
            $or: [
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'sessionDetail.startBy': convertToMongoObjectId(staffId),
                },
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'staffs.status': PRESENT,
                },
            ],
            students: { $exists: true, $type: 'array', $ne: [] },
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        };

        const fbProject = {
            _course_id: 1,
            students: 1,
            year_no: 1,
            level_no: 1,
            staffs: 1,
            _id: 1,
            term: 1,
            rotation_count: 1,
            rotation: 1,
            session: 1,
        };
        let courseSchedules = await CourseSchedule.find(fbQuery, fbProject);
        courseSchedules = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find(
                (staff) =>
                    courseSchedule.session &&
                    courseSchedule.session._session_id &&
                    staff._staff_id.toString() === staffId.toString() &&
                    staff.status === PRESENT,
            ),
        );

        const feedBacks = [];

        if (courseQuery && courseQuery.length) {
            let i = 0;
            courseQuery.map((courseId) => {
                const courseDetails = courseSchedules.filter((courseSchedule) => {
                    if (courseSchedule._course_id.toString() === courseId.toString()) {
                        return true;
                    }
                    return false;
                });
                let sumOfRatings = 0;
                let count = 0;
                let levelOfCourseId;
                let yearOfCourseId;
                const schedulePush = [];
                let rotation_count;
                let rotation;
                courseDetails.map((courseDetail) => {
                    const { students, level_no, year_no, _id, term } = courseDetail;

                    rotation_count = courseDetail.rotation_count ? courseDetail.rotation_count : '';
                    rotation = courseDetail.rotation ? courseDetail.rotation : '';
                    termOfCourseId = term;
                    levelOfCourseId = level_no;
                    yearOfCourseId = year_no;
                    sumOfRatings += students
                        .filter((student) => student.feedBack && student.feedBack.rating)
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0);
                    count += students.filter(
                        (student) => student.feedBack && student.feedBack.rating,
                    ).length;
                    students.map((student) => {
                        if (student.feedBack && student.feedBack.rating) {
                            schedulePush.push(_id);
                        }
                    });
                });

                if (count) {
                    feedBacks.push({
                        _course_id: courseId,
                        level_no: levelOfCourseId,
                        year_no: yearOfCourseId,
                        term: termOfCourseId,
                        totalFeedback: count,
                        avgRating: count ? (sumOfRatings / count).toFixed(1) : 0,
                        sessionCount: schedulePush.length,
                        rotation,
                        rotationCount: rotation_count,
                    });
                }
                i++;
            });
        }
        return feedBacks;
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseAdminParams = async (staffId, institutionCalendarId) => {
    try {
        const courseFindQuery = {
            'coordinators._user_id': convertToMongoObjectId(staffId),
            'coordinators._institution_calendar_id': convertToMongoObjectId(institutionCalendarId),
        };
        const courses = await Course.find(courseFindQuery, {
            coordinators: 1,
            _program_id: 1,
            course_name: 1,
            course_code: 1,
        })
            .populate({
                path: '_program_id',
                select: { name: 1 },
            })
            .lean();
        const courseParams = [];
        if (courses.length) {
            for (course of courses) {
                for (courseParam of course.coordinators) {
                    if (
                        courseParam._user_id.toString() === staffId.toString() &&
                        courseParam._institution_calendar_id.toString() ===
                            institutionCalendarId.toString() &&
                        courseParam.status
                    ) {
                        courseParams.push({
                            term: courseParam.term,
                            level_no: courseParam.level_no,
                            year_no: courseParam.year,
                            _institution_calendar_id: courseParam._institution_calendar_id,
                            _program_id: course._program_id._id,
                            program_name: course._program_id.name,
                            _course_id: course._id,
                            course_name: course.course_name,
                            course_code: course.course_code,
                        });
                    }
                }
            }
        }
        return courseParams;
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseIdsWithSessions = async (
    staffId,
    type,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
    courseAdmin,
) => {
    try {
        const orQuery = [];
        const csQuery = {
            schedule_date: { $exists: true },
            isDeleted: false,
        };
        if (type) {
            if (type !== 'all' && type !== 'today') {
                orQuery.push(
                    {
                        'session.session_type': type,
                    },
                    {
                        type,
                    },
                );
            } else if (type === 'today') {
                const todayDate = convertToUtcFormat(new Date());
                csQuery.schedule_date = new Date(todayDate);
            }
        }
        if (courseAdmin !== 'true') {
            // check course admin verification
            const staff = await User.findOne(
                { _id: convertToMongoObjectId(staffId), user_type: 'staff' },
                { _id: 1 },
            );
            if (staff) {
                csQuery['staffs._staff_id'] = convertToMongoObjectId(staffId);
            } else {
                csQuery['students._id'] = convertToMongoObjectId(staffId);
            }
            if (_institution_calendar_id) {
                csQuery._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
            }
            if (_course_id) {
                csQuery._course_id = convertToMongoObjectId(_course_id);
                csQuery._program_id = convertToMongoObjectId(_program_id);
                csQuery.year_no = year_no;
                csQuery.level_no = level_no;
                csQuery.term = term;
                if (rotation) {
                    csQuery.rotation = rotation;
                }
                if (rotation_count) {
                    csQuery.rotation_count = rotation_count;
                }
            }
            if (orQuery.length) {
                csQuery.$or = orQuery;
            }
        } else {
            let courseParams;
            if (_institution_calendar_id) {
                courseParams = await getCourseAdminParams(staffId, _institution_calendar_id);
            } else {
                const institutionCalendarId = await InstitutionCalendar.findOne(
                    { status: PUBLISHED },
                    { _id: 1 },
                ).sort({
                    _id: -1,
                });
                courseParams = await getCourseAdminParams(staffId, institutionCalendarId._id);
            }
            if (courseParams.length) {
                if (_program_id) {
                    csQuery._course_id = convertToMongoObjectId(_course_id);
                    csQuery._institution_calendar_id =
                        convertToMongoObjectId(_institution_calendar_id);
                    csQuery._program_id = convertToMongoObjectId(_program_id);
                    csQuery.year_no = year_no;
                    csQuery.level_no = level_no;
                    csQuery.term = term;
                    if (rotation) {
                        csQuery.rotation = rotation;
                    }
                    if (rotation_count) {
                        csQuery.rotation_count = rotation_count;
                    }
                } else {
                    csQuery.$or = courseParams;
                }
            } else {
                return {
                    courseIds: [],
                    programIds: [],
                    year: [],
                    courseSchedule: [],
                    level: [],
                    rotation: [],
                    rotationCount: [],
                    courses: [],
                };
            }
        }
        const csProject = {
            _course_id: 1,
            session: 1,
            student_groups: 1,
            year_no: 1,
            level_no: 1,
            _program_id: 1,
            end: 1,
            start: 1,
            schedule_date: 1,
            mode: 1,
            subjects: 1,
            _infra_id: 1,
            infra_id: '$_infra_id',
            infra_name: 1,
            staffs: 1,
            status: 1,
            sessionDetail: 1,
            students: 1,
            uuid: 1,
            socket_port: 1,
            _institution_calendar_id: 1,
            rotation: 1,
            rotation_count: 1,
            program_name: 1,
            course_name: 1,
            course_code: 1,
            type: 1,
            title: 1,
            merge_status: 1,
            merge_with: 1,
            topic: 1,
            sub_type: 1,
            isActive: 1,
            isDeleted: 1,
            term: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            zoomDetail: 1,
            teamsDetail: 1,
            remotePlatform: 1,
            staffBuzz: 1,
        };
        let courseSchedule = await CourseSchedule.find(csQuery, csProject)
            .sort({ 'sessions.delivery_symbol': 1, 'sessions.delivery_no': 1 })
            .populate([
                {
                    path: '_infra_id',
                    select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
                },
                {
                    path: '_course_id',
                    select: { course_assigned_details: 1 },
                },
            ])
            .lean();
        let mergedSchedules = courseSchedule.filter(
            (courseScheduleEntry) => courseScheduleEntry.merge_status,
        );
        mergedSchedules = mergedSchedules.map((mergedSchedule) => mergedSchedule.merge_with);
        // eslint-disable-next-line no-sequences
        mergedSchedules = mergedSchedules.reduce((r, e) => (r.push(...e), r), []);
        const mergedScheduleIds = mergedSchedules.map((mergedSchedule) =>
            convertToMongoObjectId(mergedSchedule.schedule_id),
        );
        mergedSchedules = await CourseSchedule.find({ _id: { $in: mergedScheduleIds } }, csProject);

        courseSchedule = courseSchedule.map((schedule) => {
            let sumOfRatings = 0;
            let count = 0;
            let mergedStudents = [];
            const { students, _infra_id, staffs, merge_status, merge_with, _program_id, level_no } =
                schedule;
            const startBy = staffs.find(
                (staff) => staff._staff_id.toString() === staffId.toString(),
            );
            const courseId = schedule._course_id._id;
            const checkAutoEndAttendanceIn = schedule._course_id.course_assigned_details.find(
                (element) =>
                    element._program_id.toString() === _program_id.toString() &&
                    element.level_no === level_no,
            );
            schedule.auto_end_attendance_in =
                checkAutoEndAttendanceIn && checkAutoEndAttendanceIn.auto_end_attendance_in
                    ? checkAutoEndAttendanceIn.auto_end_attendance_in
                    : ATTENDANCE_DEFAULT_END_TIME;

            schedule._course_id = courseId;
            mergedStudents = mergedStudents.concat(students);
            if (merge_status) {
                const mergedWithSchedules = merge_with.map((mergeWith) => mergeWith.schedule_id);
                mergedWithSchedules.forEach((mergedWithSchedule) => {
                    const mergedWithScheduleStudents = mergedSchedules.find(
                        (mergedSchedule) =>
                            mergedSchedule._id.toString() === mergedWithSchedule.toString(),
                    );
                    if (mergedWithScheduleStudents) {
                        mergedStudents = mergedStudents.concat(mergedWithScheduleStudents.students);
                    }
                });
            }

            if (
                (courseAdmin !== 'true' &&
                    mergedStudents &&
                    mergedStudents.length &&
                    startBy &&
                    startBy.status === PRESENT) ||
                (courseAdmin === 'true' && mergedStudents && mergedStudents.length)
            ) {
                sumOfRatings += mergedStudents
                    .filter((student) => student.feedBack && student.feedBack.rating)
                    .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                    .reduce((a, b) => a + b, 0);

                count += mergedStudents.filter(
                    (student) => student.feedBack && student.feedBack.rating,
                ).length;
            }
            if (count) {
                schedule.feedBack = {
                    _session_id: schedule._id,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                };
            }
            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                schedule.infra_name = infraName;
            }
            return schedule;
        });

        const courseIds = [...new Set(courseSchedule.map((schedule) => cs(schedule._course_id)))];
        // Getting Course
        const programCalendarData = await ProgramCalendar.find(
            {
                // _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                // _program_id: convertToMongoObjectId(_program_id),
            },
            {
                _institution_calendar_id: 1,
                _program_id: 1,
                'level.level_no': 1,
                'level.term': 1,
                'level.year': 1,
                'level.end_date': 1,
                'level.start_date': 1,
                'level.course._course_id': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        );

        const splittedCourses = [];
        for (const courseId of courseIds) {
            const filterCourseSchedule = courseSchedule.filter(
                (courseScheduleEntry) => cs(courseScheduleEntry._course_id) === cs(courseId),
            );
            // different calendar and year and level based courses
            const courses = filterCourseSchedule.reduce((acc, current) => {
                const x = acc.find(
                    (item) =>
                        item._institution_calendar_id.toString() ===
                            current._institution_calendar_id.toString() &&
                        item._program_id.toString() === current._program_id.toString() &&
                        item.year_no === current.year_no &&
                        item.level_no === current.level_no &&
                        item.term === current.term &&
                        ((item.rotation.toString() === 'yes' &&
                            item.rotation_count &&
                            current.rotation_count &&
                            item.rotation_count.toString() === current.rotation_count.toString()) ||
                            item.rotation.toString() === 'no'),
                );

                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            if (courses.length) {
                for (const course of courses) {
                    const {
                        _institution_calendar_id,
                        _program_id,
                        year_no,
                        level_no,
                        rotation,
                        rotation_count,
                        _course_id,
                        term,
                    } = course;
                    if (programCalendarData) {
                        const { level } = programCalendarData.find(
                            (calendarElement) =>
                                calendarElement._institution_calendar_id.toString() ===
                                    _institution_calendar_id.toString() &&
                                calendarElement._program_id.toString() === _program_id.toString(),
                        );
                        const levelsEntry = level.find(
                            (levelEntry) =>
                                levelEntry.year === year_no &&
                                levelEntry.level_no === level_no &&
                                levelEntry.term === term,
                        );
                        const { course: coursesEntry, rotation_course } = levelsEntry;
                        let dates;
                        if (rotation === 'yes') {
                            dates = rotation_course
                                .filter(
                                    (rotationCourseEntry) =>
                                        rotationCourseEntry.rotation_count === rotation_count,
                                )
                                .map((rCourse) => rCourse.course)
                                .flat()
                                .find(
                                    (rotationCourse) =>
                                        rotationCourse._course_id.toString() ===
                                        _course_id.toString(),
                                );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                            course.rotation_count = rotation_count;
                        } else {
                            dates = coursesEntry.find(
                                (courseEntry) =>
                                    courseEntry._course_id.toString() === _course_id.toString(),
                            );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                        }
                    }
                }
                splittedCourses.push(courses);
            }
        }
        // eslint-disable-next-line prefer-spread
        const mergedCourses = [].concat.apply([], splittedCourses);
        const programIds = [...new Set(courseSchedule.map((schedule) => schedule._program_id))];
        const year = [...new Set(courseSchedule.map((schedule) => schedule.year_no))];
        const level = [...new Set(courseSchedule.map((schedule) => schedule.level_no))];
        const courseTerm = [...new Set(courseSchedule.map((schedule) => schedule.term))];
        const rotations = [...new Set(courseSchedule.map((schedule) => schedule.rotation))];
        const rotationCount = [
            ...new Set(courseSchedule.map((schedule) => schedule.rotation_count)),
        ];
        return {
            courseIds,
            programIds,
            year,
            courseSchedule,
            level,
            rotation: rotations,
            rotationCount,
            courses: mergedCourses,
            term: courseTerm,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const lmsSettings = async (_institution_id) => {
    try {
        let warningAbsenceData = [{ typeName: 'Denial', percentage: 100, message: 'Denial' }];
        const leave_category = await lmsStudentSetting.findOne(
            {
                isDeleted: false,
                _institution_id,
                classificationType: LEAVE,
            },
            { _id: 1, warningConfig: 1 },
        );
        if (
            leave_category &&
            leave_category &&
            leave_category.warningConfig &&
            leave_category.warningConfig.length !== 0 &&
            leave_category.warningConfig.filter((ele) => ele.isActive === true).length !== 0
        )
            warningAbsenceData = clone(
                leave_category.warningConfig.filter((ele) => ele.isActive === true),
            );
        warningAbsenceData = clone(
            warningAbsenceData.sort((a, b) => {
                let comparison = 0;
                if (parseInt(a.percentage) > parseInt(b.percentage)) {
                    comparison = -1;
                } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                    comparison = 1;
                }
                return comparison;
            }),
        );

        const finaleWarning =
            warningAbsenceData.length !== 1
                ? warningAbsenceData[1] && warningAbsenceData[1].typeName
                    ? warningAbsenceData[1].typeName
                    : undefined
                : undefined;
        const denialWarning = warningAbsenceData[0].typeName;
        return {
            warningAbsenceData,
            finaleWarning,
            denialWarning,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getSessions = (
    lmsData,
    studentCriteriaData,
    courseSchedules,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    userId,
    rotation,
    rotation_count,
    getSchedule,
) => {
    try {
        let courseSessions = [];
        const courseSchedulesEntry =
            rotation === 'yes'
                ? courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation &&
                          rotation_count &&
                          rotation_count === courseSchedule.rotation_count,
                  )
                : courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation,
                  );

        const isExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter((courseSession) => {
                if (
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    courseSession._session_id &&
                    sessionIdOrType &&
                    courseSession._session_id.toString() === sessionIdOrType.toString()
                ) {
                    if (
                        mergeStatus &&
                        scheduleId.toString() === courseSession.scheduleId.toString()
                    ) {
                        return true;
                    }
                    if (!mergeStatus) {
                        return true;
                    }
                }
                return false;
            }).length;

        const isSessionTypeExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter(
                (courseSession) =>
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    !courseSession._session_id &&
                    courseSession.session_topic === sessionIdOrType &&
                    courseSession.scheduleId &&
                    scheduleId.toString() === courseSession.scheduleId.toString(),
            ).length;

        courseSchedulesEntry.forEach((courseSchedule) => {
            courseSchedule.student_groups = courseSchedule.student_groups.map((studentGroup) => {
                studentGroup.students = [];
                return studentGroup;
            });
            if (
                courseSchedule.session &&
                !isExist(
                    courseSchedule.merge_status,
                    courseSchedule.session._session_id,
                    courseSchedule._id,
                )
            ) {
                const { _id, session, merge_status, merge_with, student_groups, status } =
                    courseSchedule;
                session.merge_status = merge_status;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (session && !merge_status) {
                    session.status = status;
                    session.student_groups = student_groups;
                    session.scheduleId = _id;
                    courseSessions.push(session);
                } else {
                    const mergedSessions = [];
                    const mergedStudents = [];
                    merge_with.forEach((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                    mergeWith.schedule_id.toString() &&
                                courseScheduleEntry.session &&
                                courseScheduleEntry.session._session_id.toString() ===
                                    mergeWith.session_id.toString(),
                        );
                        if (sessionDetails) {
                            mergeWith.session = {
                                _session_id: sessionDetails.session._session_id,
                                s_no: sessionDetails.session.s_no,
                                delivery_symbol: sessionDetails.session.delivery_symbol,
                                delivery_no: sessionDetails.session.delivery_no,
                                topic: sessionDetails.topic,
                                session_type: sessionDetails.session.session_type,
                                session_topic: sessionDetails.session.session_topic,
                            };
                            if (sessionDetails.students && sessionDetails.students.length) {
                                mergedStudents.push(sessionDetails.students);
                            }
                        }
                        mergedSessions.push(mergeWith);
                    });
                    const mergedScheduleIds = merge_with.map((mergeWith) =>
                        mergeWith.schedule_id.toString(),
                    );
                    let sessionMerged = merge_with.filter((mergeWith) => mergeWith.session_id);
                    sessionMerged = sessionMerged.map((mergeWith) =>
                        mergeWith.session_id.toString(),
                    );
                    courseSchedule.merge_with = mergedSessions;
                    if (mergedStudents.length) {
                        if (courseSchedule.students && courseSchedule.students.length)
                            mergedStudents.push(courseSchedule.students);
                        // eslint-disable-next-line prefer-spread
                        const concatMergedStudents = [].concat.apply([], mergedStudents);
                        //Sort the result to show beedback first then only we can identify using find if merge is scheduled
                        const sortFeedbackBasedStudents = concatMergedStudents.sort(function (
                            a,
                            b,
                        ) {
                            return b.feedBack ? 1 : -1;
                        });
                        // duplicate student removed
                        const students = sortFeedbackBasedStudents.reduce((acc, current) => {
                            const x = acc.find(
                                (item) => item._id.toString() === current._id.toString(),
                            );
                            if (!x) {
                                return acc.concat([current]);
                            }
                            return acc;
                        }, []);
                        courseSchedule.students = students;
                    }
                    const duplicateMergedSession = courseSessions.find(
                        (courseSession) =>
                            courseSession._session_id &&
                            sessionMerged.includes(courseSession._session_id.toString()) &&
                            courseSession.merge_status.toString() ===
                                courseSchedule.merge_status.toString() &&
                            mergedScheduleIds.includes(courseSession.scheduleId.toString()),
                    );

                    const mergedSessionSchedules = courseSchedulesEntry.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    session.scheduleId = _id;
                    if (!duplicateMergedSession) courseSessions.push(session);
                }
            }
            // splitted to session type based
            const sessionTypes = [EVENT, SUPPORT_SESSION];
            if (
                courseSchedule.type &&
                sessionTypes.includes(courseSchedule.type) &&
                !isSessionTypeExist(
                    courseSchedule.merge_status,
                    courseSchedule.title,
                    courseSchedule._id,
                )
            ) {
                const { type, merge_status, student_groups, merge_with } = courseSchedule;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (type) {
                    courseSessions.push({
                        scheduleId: courseSchedule._id,
                        session_type: courseSchedule.type,
                        session_topic: courseSchedule.title,
                        merge_status,
                        student_groups,
                    });
                }
                if (merge_status) {
                    const mergedSessions = merge_with.map((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString(),
                        );
                        mergeWith.session = {
                            session_type: sessionDetails.type,
                            session_topic: sessionDetails.title,
                        };
                        return mergeWith;
                    });
                    courseSchedule.merge_with = mergedSessions;
                    courseSessions.push(session);
                }
            }
        });

        let totalSessions = 0;
        let completedSessions = 0;
        let warningCount = 0;
        let presentCount = 0;
        let leaveCount = 0;
        let absentCount = 0;
        // sort by session
        let courseSessionsRegular = courseSessions.filter(
            (courseSession) => courseSession._session_id,
        );
        const courseSessionsOthers = courseSessions.filter(
            (courseSession) => !courseSession._session_id,
        );
        courseSessionsRegular = courseSessionsRegular.sort((a, b) =>
            a.s_no && b.s_no && a.s_no > b.s_no ? 1 : -1,
        );
        courseSessions = courseSessionsRegular.concat(courseSessionsOthers);
        const courseScheduleStringify = clone(courseSchedulesEntry);
        courseSessions.forEach((courseSession) => {
            const { merge_status: courseSessionMergeStatus, scheduleId } = courseSession;
            // courseSession.documentCount = 0; // To Do
            // courseSession.activityCount = 0; // To Do
            let schedules = courseSession._session_id
                ? courseScheduleStringify.filter((courseSchedule) => {
                      if (
                          courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          if (
                              courseSessionMergeStatus &&
                              scheduleId.toString() === courseSchedule._id.toString()
                          ) {
                              return true;
                          }
                      } else if (
                          !courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          return true;
                      }
                  })
                : courseScheduleStringify.filter(
                      (courseSchedule) =>
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSession.session_topic === courseSchedule.title &&
                          courseSchedule._id &&
                          courseSchedule._id.toString() === scheduleId.toString(),
                  );
            schedules = schedules.map((schedule) => {
                const {
                    start: { hour: startHour, minute: startMinute, format: startFormat },
                    end: { hour: endHour, minute: endMinute, format: endFormat },
                    schedule_date,
                } = schedule;

                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    startHours,
                    startMinute,
                    0,
                );
                const endDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    endHours,
                    endMinute,
                    0,
                );
                schedule.start_date = startDateAndTime;
                schedule.end_date = endDateAndTime;
                return schedule;
            });
            courseSession.absentCount = 0;
            courseSession.leaveCount = 0;
            courseSession.warningCount = 0;
            courseSession.presentCount = 0;
            courseSession.totalSchedules = 0;
            schedules.forEach((cSchedule) => {
                if (
                    cSchedule.session &&
                    cSchedule.session._session_id &&
                    cSchedule.status === COMPLETED &&
                    cSchedule.students &&
                    cSchedule.students.length
                ) {
                    const students = cSchedule.students.reduce(
                        (scheduleStudents, currentStudent) => {
                            const uniqueStudent = scheduleStudents.find(
                                (scheduleStudent) =>
                                    scheduleStudent._id &&
                                    scheduleStudent._id.toString() ===
                                        currentStudent._id.toString(),
                            );
                            if (!uniqueStudent) {
                                return scheduleStudents.concat([currentStudent]);
                            }
                            return scheduleStudents;
                        },
                        [],
                    );

                    students.forEach((student) => {
                        if (cs(student._id) === cs(userId)) {
                            if (student.status === 'absent' || student.status === 'pending') {
                                courseSession.absentCount++;
                                if (cSchedule.merge_status) {
                                    courseSession.absentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (student.status === 'present' && cSchedule.isActive) {
                                courseSession.presentCount++;
                                //form adding merge sessions count
                                if (cSchedule.merge_status) {
                                    courseSession.presentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (
                                student.status === 'leave' ||
                                student.status === 'on_duty' ||
                                student.status === 'permission'
                            )
                                courseSession.leaveCount++;
                            if (student.status === 'leave') courseSession.warningCount++; //To Do
                        }
                    });

                    cSchedule.staffs.forEach((staff) => {
                        if (cSchedule.status === COMPLETED && cs(staff._staff_id) === cs(userId)) {
                            if (staff.status === 'absent' || staff.status === 'pending')
                                courseSession.absentCount++;
                            if (staff.status === 'present' && cSchedule.isActive) {
                                courseSession.presentCount++;
                                //form adding merge sessions count
                                if (cSchedule.merge_status) {
                                    courseSession.presentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (
                                staff.status === 'leave' ||
                                staff.status === 'on_duty' ||
                                staff.status === 'permission'
                            )
                                courseSession.leaveCount++;
                            if (staff.status === 'leave') courseSession.warningCount++;
                        }
                    });
                    if (cSchedule.isActive === true) courseSession.totalSchedules++;
                }
                cSchedule.student_groups = cSchedule.student_groups.map((studentGroup) => {
                    studentGroup.students = [];
                    return studentGroup;
                });
                if (!getSchedule) {
                    cSchedule.studentsCount = cSchedule.students.length;
                    const studentData = cSchedule.students.find(
                        (studentElement) => studentElement._id.toString() === userId.toString(),
                    );
                    cSchedule.students = studentData
                        ? [{ _id: studentData._id, status: studentData.status }]
                        : [];
                }
            });
            warningCount += courseSession.warningCount;
            presentCount += courseSession.presentCount;
            leaveCount += courseSession.leaveCount;
            absentCount += courseSession.absentCount;
            courseSession.schedules = schedules;
        });

        const sessionsCount = courseSessions.filter(
            (courseSession) =>
                courseSession._session_id &&
                courseSession.schedules.find((schedule) => schedule.isActive),
        );
        sessionsCount.forEach((session) => {
            totalSessions += session.schedules.length;
        });
        //form adding merge sessions count
        const mergedSessions = courseSessions.filter((courseSession) => courseSession.merge_status);
        if (mergedSessions) {
            for (mergedSessionsEntry of mergedSessions) {
                totalSessions += mergedSessionsEntry.merge_with.length;
            }
        }
        const groupNames = [];

        for (const courseSession of courseSessions) {
            if (courseSession.student_groups) {
                for (const studentGroup of courseSession.student_groups) {
                    if (
                        studentGroup.group_name &&
                        !groupNames.find((groupName) => groupName === studentGroup.group_name)
                    ) {
                        groupNames.push(studentGroup.group_name);
                    }
                }
            }
        }

        const uniqueSessionTypes = [
            ...new Set(
                courseSessions
                    .map((type) => {
                        if (type._session_id) return type.session_type;
                    })
                    .filter((item) => item),
            ),
        ];
        const sessDetails = courseSessions.map((courseSession) => {
            const groupNamesArray = [];

            if (courseSession.student_groups) {
                courseSession.student_groups.forEach((groupName) => {
                    groupNamesArray.push(groupName.group_name);
                });
            }
            courseSession.groupNames = groupNamesArray;
            return courseSession;
        });
        const courseSessionDetails = groupNames.map((groupName) => {
            const totalGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );
            const completedGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === COMPLETED &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const pendingGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === PENDING &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const courseSessions = uniqueSessionTypes.map((uniqueSessionType) => {
                const completedCount = completedGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === COMPLETED &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                const pendingCount = pendingGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === PENDING &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;
                const totalCount = totalGroup.filter(
                    (sessionData) =>
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                return {
                    deliveryName: uniqueSessionType,
                    totalCount,
                    completedCount,
                    pendingCount,
                };
            });
            const totalGroups = totalGroup.length;
            return {
                studentGroupName: groupName.split('-').splice(-2).join('-'),
                totalSessions: totalGroups,
                completedSessions: completedGroup.length,
                pendingSessions: pendingGroup.length,
                courseSessions,
            };
        });
        sessionsCount.forEach((session) => {
            const sessionCompleted = session.schedules.filter(
                (courseSchedule) => courseSchedule.status === COMPLETED,
            );
            if (sessionCompleted.length && courseSchedules.length) {
                completedSessions += sessionCompleted.length;
            }
            const mergedCompletedSessions = sessionCompleted.filter(
                (courseSession) => courseSession.merge_status,
            );
            if (mergedCompletedSessions) {
                for (mergedCompletedSessionsEntry of mergedCompletedSessions) {
                    completedSessions += mergedCompletedSessionsEntry.merge_with.length;
                }
            }
        });
        const attendedSessions = presentCount;
        const absentSessions = absentCount;
        const denialPercentage = (absentSessions / totalSessions) * 100;
        const studentWarningAbsence = clone(lmsData.warningAbsenceData);
        if (studentCriteriaData.status != false) {
            const studentManipulation = studentCriteriaData.data.find(
                (absenceElement) => absenceElement.studentId.toString() === userId.toString(),
            );
            userId.manipulationStatus = false;
            userId.manipulationPercentage = 0;
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].percentage &&
                studentWarningAbsence[0].percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].percentage = studentManipulation.absencePercentage;
                userId.manipulationStatus = true;
                userId.manipulationPercentage = studentManipulation.absencePercentage;
            }
        }
        let warningData = studentWarningAbsence.find(
            (ele) => ele.percentage && parseFloat(denialPercentage) >= parseFloat(ele.percentage),
        );
        if (warningData) {
            warningData =
                warningData.typeName === lmsData.finaleWarning
                    ? 'Final Warning'
                    : warningData.typeName;
        } else {
            warningData = '';
        }
        const absentPercentage = (absentCount / totalSessions) * 100;
        return {
            courseSessionDetails,
            warningCount,
            presentCount,
            totalSessions,
            completedSessions,
            courseSessions,
            attendedSessions,
            leaveCount,
            absentCount,
            warningData,
            absentPercentage,
        };
    } catch (error) {
        throw new Error(error);
    }
};

const getAllCourses = async (staffId, coordinators, institutionCalendarId, _institution_id) => {
    try {
        const { courseIds, courseSchedule, courses, year, level, rotation, term, rotationCount } =
            await getCourseIdsWithSessions(staffId, '', '', institutionCalendarId);
        const feedBackData = await getRatingByCourses(
            institutionCalendarId,
            courseIds,
            staffId,
            year,
            level,
            term,
            rotation,
            rotationCount,
        );
        const courseIdsResults = courses.map((courseId) => {
            return courseId._course_id;
        });

        let lmsData = await lmsSettings(_institution_id);
        const studentCriteriaData = await get_list(studentCriteriaCollection, {
            courseId: { $in: courseIds },
        });
        const lmsClonedData = clone(lmsData);
        const sgQuery = {
            isDeleted: false,
            _institution_calendar_id: institutionCalendarId,
            'groups.courses._course_id': { $in: courseIds },
        };
        const studentGroups = await studentGroup.find(sgQuery).lean();
        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIdsResults },
            },
            { _id: 1, course_type: 1, 'coordinators._user_id': 1 },
        );
        const adminChkcourses = [];
        if (coordinators === 'true') {
            for (adminCourses of courseTypeResult) {
                const userData = [];
                if (adminCourses.coordinators) {
                    for (courseAdmin of adminCourses.coordinators) {
                        const user = await User.findOne(
                            { _id: courseAdmin._user_id },
                            { name: 1, email: 1 },
                        );
                        userData.push({ name: user.name, email: user.email });
                    }
                }
                adminChkcourses.push({ _course_id: adminCourses._id, coordinatorData: userData });
            }
        }
        const coursesList = courses
            .map((course) => {
                const {
                    _course_id: courseId,
                    _institution_calendar_id,
                    _program_id,
                    program_name,
                    course_name,
                    course_code,
                    level_no,
                    year_no,
                    term: courseTerm,
                    isActive,
                    subjects,
                    rotation,
                    end_date,
                    start_date,
                    rotation_count,
                } = course;

                courseAdminNames = adminChkcourses.find(
                    (adminCourses) => adminCourses._course_id.toString() === courseId.toString(),
                );
                for (const student of studentGroups) {
                    for (const groups of student.groups) {
                        if (groups.level === level_no) {
                            for (const course of groups.courses) {
                                if (
                                    lmsData.warningAbsenceData[0] &&
                                    course.student_absence_percentage &&
                                    course.student_absence_percentage !== 0
                                ) {
                                    lmsData.warningAbsenceData[0].percentage =
                                        course.student_absence_percentage;
                                } else {
                                    lmsData = lmsClonedData;
                                }
                            }
                        }
                    }
                }
                const result = getSessions(
                    lmsData,
                    studentCriteriaData,
                    courseSchedule,
                    courseId,
                    _institution_calendar_id,
                    _program_id,
                    year_no,
                    level_no,
                    courseTerm,
                    staffId,
                    rotation,
                    rotation_count,
                );
                const {
                    courseSessions,
                    courseSessionDetails,
                    totalSessions,
                    completedSessions,
                    attendedSessions,
                    presentCount,
                    warningCount,
                    leaveCount,
                    absentCount,
                    warningData,
                    absentPercentage,
                } = result;
                let feedback;
                let rotationCount;
                if (!course.rotation_count) {
                    rotationCount = '';
                } else {
                    rotationCount = course.rotation_count;
                }

                const feedBacks = rotationCount
                    ? feedBackData.find(
                          (feedBackDetail) =>
                              feedBackDetail._course_id.toString() === courseId.toString() &&
                              feedBackDetail.level_no === level_no &&
                              feedBackDetail.term === courseTerm &&
                              feedBackDetail.year_no === year_no &&
                              feedBackDetail.rotation === rotation &&
                              feedBackDetail.rotationCount === rotationCount,
                      )
                    : feedBackData.find(
                          (feedBackDetail) =>
                              feedBackDetail._course_id.toString() === courseId.toString() &&
                              feedBackDetail.level_no === level_no &&
                              feedBackDetail.term === courseTerm &&
                              feedBackDetail.year_no === year_no &&
                              feedBackDetail.rotation === rotation,
                      );
                if (feedBacks) feedback = feedBacks;

                let course_type;
                const courseTypes = courseTypeResult.find(
                    (courseType) => courseType._id.toString() === courseId.toString(),
                );
                if (courseTypes) course_type = courseTypes.course_type;
                return {
                    _id: courseId,
                    _institution_calendar_id,
                    _program_id,
                    program_name,
                    course_name,
                    course_code,
                    courseAdminNames,
                    courseSessionDetails,
                    totalSessions,
                    completedSessions,
                    attendedSessions,
                    leaveCount,
                    absentCount,
                    warningCount,
                    warningData,
                    presentCount,
                    absentPercentage,
                    subjects,
                    year: year_no,
                    level: level_no,
                    term: courseTerm,
                    isActive,
                    feedback,
                    rotation,
                    end_date,
                    start_date,
                    rotation_count,
                    course_type,
                };
            })
            .filter((course) => course.warningData != '');
        return coursesList;
    } catch (error) {
        throw new Error(error);
    }
};

const logicFlowsForStudentApprovals = async ({
    studentWarningData,
    roleAssignDocs,
    _user_id,
    denial,
    courseCoordinators,
}) => {
    let levelApprover = studentWarningData.levelApprover;
    let approvalUserId;
    let approvalRoleId;
    if (studentWarningData && studentWarningData.approvalFrom) {
        approvalUserId = studentWarningData.approvalFrom.map((el) => el.userId._id.toString());
        approvalRoleId = studentWarningData.approvalFrom.map((el) => {
            if (el.roleId) return el.roleId._id.toString();
        });
    }
    let approvalStatus = `Pending with ${levelApprover.level[0].levelName}`;
    for (const [index, approver] of levelApprover.level.entries()) {
        approver.user = [];
        approver.role = [];
        const currentApproval = levelApprover.level[index].approvalConfig;
        const currentCategoryBased = levelApprover.level[index].categoryBased;
        const currentOverWrite = levelApprover.level[index].overwritePreviousLevelApproval;
        const previousCategoryBased =
            index - 1 >= 0 ? levelApprover.level[index - 1].categoryBased : null;
        const previousApproval =
            index - 1 >= 0 ? levelApprover.level[index - 1].approvalConfig : null;
        let currentStatus =
            currentCategoryBased === USER_BASED
                ? levelApprover.level[index].user.map((el) => el.status)
                : [];
        let previousStatus =
            previousCategoryBased === USER_BASED
                ? index - 1 >= 0
                    ? levelApprover.level[index - 1].user.map((el) => el.status)
                    : []
                : [];
        const previousEscalate =
            index - 1 >= 0 ? levelApprover.level[index - 1].escalateRequest : false;
        const currentEscalate = index >= 0 ? levelApprover.level[index].escalateRequest : false;
        const appliedDate = moment(studentWarningData.createdAt);
        const now = moment(new Date());
        const diff = now.diff(appliedDate, 'days');
        let currentTat = Number(levelApprover.level[index].turnAroundTime);
        let previousTat = index - 1 >= 0 ? levelApprover.level[index - 1].turnAroundTime : 0;
        if (index) {
            for (let i = index - 1; i >= 0; i--) {
                currentTat += Number(levelApprover.level[i].turnAroundTime);
            }
        }
        if (index - 1) {
            for (let i = index - 2; i >= 0; i--) {
                previousTat += Number(levelApprover.level[i].turnAroundTime);
            }
        }
        let currentRoleStatus =
            currentCategoryBased === ROLE_BASED
                ? levelApprover.level[index].role
                    ? levelApprover.level[index].role
                          .map((el) => el.roleUsers)
                          .flat()
                          .map((nestedElement) => nestedElement.status)
                    : []
                : [];
        let currentRoleWiseStatus =
            currentCategoryBased === ROLE_BASED
                ? levelApprover.level[index].role
                    ? levelApprover.level[index].role.map((el) => {
                          if (el.roleUsers.length) {
                              return el.roleUsers.map((nestedElement) => nestedElement.status);
                          }
                      })
                    : []
                : [];
        const previousRoleWiseStatus =
            [index - 1] >= 0 &&
            previousCategoryBased === ROLE_BASED &&
            levelApprover.level[index - 1] &&
            levelApprover.level[index - 1].role
                ? levelApprover.level[index - 1].role.map((el) => {
                      if (el.roleUsers.length) {
                          return el.roleUsers.map((nestedElement) => nestedElement.status);
                      }
                  })
                : [];
        const previousRoleStatus =
            [index - 1] >= 0 &&
            previousCategoryBased === ROLE_BASED &&
            levelApprover.level[index - 1] &&
            levelApprover.level[index - 1].role
                .map((el) => el.roleUsers)
                .flat()
                .map((nestedElement) => nestedElement.status);
        if (approver.categoryBased === USER_BASED) {
            for (const [userIdIndex, userId] of approver.userIds.entries()) {
                if (approvalUserId.includes(userId._id.toString())) {
                    const staffApprovalStatus = studentWarningData.approvalFrom.find(
                        (element) => element.userId._id.toString() === userId._id.toString(),
                    );
                    approver.user.push({
                        _id: userId._id,
                        name: userId.name,
                        status: staffApprovalStatus.status,
                        approvalDate: staffApprovalStatus.date,
                        reason: staffApprovalStatus.reason ? staffApprovalStatus.reason : '',
                        isCurrentUser: _user_id.toString() === userId._id.toString(),
                    });
                    if (
                        currentApproval === ANY_ONE_USER ||
                        currentApproval === ANY_ONE_IN_ANY_ROLE
                    ) {
                        if (userIdIndex) {
                            for (let i = userIdIndex - 1; i >= 0; i--) {
                                approver.user[i].status = NOT_APPLICABLE;
                            }
                        }
                    } else if (
                        currentApproval === ALL_USERS ||
                        currentApproval === ANY_ONE_IN_EACH_ROLE
                    ) {
                        if (userIdIndex) {
                            for (let i = userIdIndex - 1; i >= 0; i--) {
                                approver.user[i].status =
                                    approver.user[i].status === NOT_INITIATED
                                        ? PENDING
                                        : approver.user[i].status;
                            }
                        }
                    }
                    if (approver.skipPreviousLevelApproval === true) {
                        if (index) {
                            for (let i = index - 1; i >= 0; i--) {
                                if (levelApprover.level[i].categoryBased === USER_BASED) {
                                    for (const [
                                        previousUserIdIndex,
                                        previousUserId,
                                    ] of levelApprover.level[i].userIds.entries()) {
                                        if (
                                            levelApprover.level[i].user[previousUserIdIndex]
                                                .status === PENDING ||
                                            levelApprover.level[i].user[previousUserIdIndex]
                                                .status === NOT_INITIATED
                                        ) {
                                            levelApprover.level[i].user[
                                                previousUserIdIndex
                                            ].status = SKIPPED;
                                        }
                                    }
                                } else {
                                    for (const [
                                        previousRoleIdIndex,
                                        previousRoleId,
                                    ] of levelApprover.level[i].roleIds.entries()) {
                                        if (
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === PENDING) ||
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some(
                                                (item) => item.status === NOT_INITIATED,
                                            ) ||
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === DELAYED)
                                        ) {
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers = levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.map((item) => {
                                                return {
                                                    ...item,
                                                    status:
                                                        item.status === FORWARD
                                                            ? FORWARD
                                                            : item.status === REJECT
                                                            ? REJECT
                                                            : item.status === NOT_APPLICABLE
                                                            ? NOT_APPLICABLE
                                                            : item.status === DELAYED
                                                            ? DELAYED
                                                            : SKIPPED,
                                                };
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    const overWritePreviousLevelApprovalFlowForUser = () => {
                        if (
                            previousApproval === ANY_ONE_IN_ANY_ROLE ||
                            previousApproval === ANY_ONE_USER
                        ) {
                            if (currentOverWrite) {
                                if (Number(diff) >= Number(currentTat)) {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: DELAYED,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                } else {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: PENDING,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                }
                            } else {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: NOT_APPLICABLE,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            }
                        } else {
                            if (
                                previousCategoryBased === ROLE_BASED &&
                                previousRoleWiseStatus.some((elem) => elem.includes(PENDING))
                            ) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: NOT_INITIATED,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else if (
                                previousCategoryBased === USER_BASED &&
                                previousStatus.some((elem) => elem.includes(PENDING))
                            ) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: NOT_INITIATED,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else {
                                if (currentOverWrite) {
                                    if (Number(diff) >= Number(currentTat)) {
                                        approver.user.push({
                                            _id: userId._id,
                                            name: userId.name,
                                            status: DELAYED,
                                            isCurrentUser:
                                                _user_id.toString() === userId._id.toString(),
                                        });
                                    } else {
                                        approver.user.push({
                                            _id: userId._id,
                                            name: userId.name,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === userId._id.toString(),
                                        });
                                    }
                                } else {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: NOT_APPLICABLE,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                }
                            }
                        }
                    };
                    const skipPreviousLevelApprovalForAllUserFlow = () => {
                        if (approver.skipPreviousLevelApproval) {
                            const previousStatusCheckForSkipPrevious = [];
                            for (let i = userIdIndex - 1; i >= 0; i--) {
                                previousStatusCheckForSkipPrevious.push(approver.user[i].status);
                            }
                            if (!previousStatusCheckForSkipPrevious.length) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: NOT_INITIATED,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else if (
                                previousStatusCheckForSkipPrevious.every(
                                    (el) => el === NOT_INITIATED,
                                )
                            ) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: NOT_INITIATED,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: PENDING,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            }
                        } else {
                            approver.user.push({
                                _id: userId._id,
                                name: userId.name,
                                status: NOT_INITIATED,
                                isCurrentUser: _user_id.toString() === userId._id.toString(),
                            });
                        }
                    };
                    const approvalPreviousCategoryBasedLogicForUsers = () => {
                        if (previousCategoryBased === USER_BASED) {
                            if (
                                previousApproval === ANY_ONE_IN_ANY_ROLE ||
                                previousApproval === ANY_ONE_USER
                            ) {
                                if (
                                    previousStatus.includes(APPROVED) ||
                                    previousStatus.includes(FORWARD)
                                ) {
                                    if (Number(diff) >= Number(currentTat)) {
                                        approver.user.push({
                                            _id: userId._id,
                                            name: userId.name,
                                            status: DELAYED,
                                            isCurrentUser:
                                                _user_id.toString() === userId._id.toString(),
                                        });
                                    } else {
                                        approver.user.push({
                                            _id: userId._id,
                                            name: userId.name,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === userId._id.toString(),
                                        });
                                    }
                                } else if (
                                    previousStatus.includes(CANCELLED) ||
                                    previousStatus.includes(REJECT) ||
                                    previousStatus.every((el) => el.includes(NOT_APPLICABLE))
                                ) {
                                    overWritePreviousLevelApprovalFlowForUser();
                                } else if (previousEscalate) {
                                    if (Number(diff) >= Number(previousTat)) {
                                        if (
                                            previousStatus.includes(CANCELLED) ||
                                            previousStatus.includes(REJECT) ||
                                            previousStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForUser();
                                        } else if (
                                            Number(diff) >= Number(currentTat) &&
                                            !previousStatus.includes(NOT_INITIATED)
                                        ) {
                                            approver.user.push({
                                                _id: userId._id,
                                                name: userId.name,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === userId._id.toString(),
                                            });
                                        } else {
                                            if (
                                                (previousStatus.includes(REJECT) ||
                                                    previousStatus.includes(CANCELLED)) &&
                                                currentOverWrite
                                            ) {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: PENDING,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            } else {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: NOT_APPLICABLE,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            }
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllUserFlow();
                                    }
                                } else {
                                    skipPreviousLevelApprovalForAllUserFlow();
                                }
                            } else {
                                if (
                                    previousStatus.includes(PENDING) ||
                                    previousStatus.includes(NOT_INITIATED) ||
                                    previousStatus.includes(NOT_APPLICABLE) ||
                                    previousStatus.includes(DELAYED)
                                ) {
                                    if (previousEscalate) {
                                        if (Number(diff) >= Number(previousTat)) {
                                            if (
                                                previousStatus.includes(CANCELLED) ||
                                                previousStatus.includes(REJECT) ||
                                                previousStatus.every((el) =>
                                                    el.includes(NOT_APPLICABLE),
                                                )
                                            ) {
                                                overWritePreviousLevelApprovalFlowForUser();
                                            } else if (
                                                Number(diff) >= Number(currentTat) &&
                                                !previousStatus.includes(NOT_INITIATED)
                                            ) {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: DELAYED,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            } else {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: PENDING,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            }
                                        } else {
                                            skipPreviousLevelApprovalForAllUserFlow();
                                        }
                                    } else {
                                        if (
                                            previousStatus.includes(CANCELLED) ||
                                            previousStatus.includes(REJECT) ||
                                            previousStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForUser();
                                        } else {
                                            if (
                                                previousStatus.every((el) =>
                                                    el.includes(APPROVED),
                                                ) ||
                                                previousStatus.every((el) => el.includes(FORWARD))
                                            ) {
                                                if (Number(diff) >= Number(currentTat)) {
                                                    approver.user.push({
                                                        _id: userId._id,
                                                        name: userId.name,
                                                        status: DELAYED,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            userId._id.toString(),
                                                    });
                                                } else {
                                                    approver.user.push({
                                                        _id: userId._id,
                                                        name: userId.name,
                                                        status: PENDING,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            userId._id.toString(),
                                                    });
                                                }
                                            } else {
                                                skipPreviousLevelApprovalForAllUserFlow();
                                            }
                                        }
                                    }
                                } else {
                                    if (
                                        previousStatus.includes(CANCELLED) ||
                                        previousStatus.includes(REJECT) ||
                                        previousStatus.every((el) => el.includes(NOT_APPLICABLE))
                                    ) {
                                        overWritePreviousLevelApprovalFlowForUser();
                                    } else if (
                                        previousStatus.every((el) => el.includes(APPROVED)) ||
                                        previousStatus.every((el) => el.includes(FORWARD))
                                    ) {
                                        if (Number(diff) >= Number(currentTat)) {
                                            approver.user.push({
                                                _id: userId._id,
                                                name: userId.name,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === userId._id.toString(),
                                            });
                                        } else {
                                            approver.user.push({
                                                _id: userId._id,
                                                name: userId.name,
                                                status: PENDING,
                                                isCurrentUser:
                                                    _user_id.toString() === userId._id.toString(),
                                            });
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllUserFlow();
                                    }
                                }
                            }
                        } else if (previousCategoryBased === ROLE_BASED) {
                            if (
                                previousApproval === ANY_ONE_IN_ANY_ROLE ||
                                previousApproval === ANY_ONE_USER
                            ) {
                                if (
                                    previousRoleStatus.includes(APPROVED) ||
                                    previousRoleStatus.includes(FORWARD)
                                ) {
                                    if (Number(diff) >= Number(currentTat)) {
                                        approver.user.push({
                                            _id: userId._id,
                                            name: userId.name,
                                            status: DELAYED,
                                            isCurrentUser:
                                                _user_id.toString() === userId._id.toString(),
                                        });
                                    } else {
                                        approver.user.push({
                                            _id: userId._id,
                                            name: userId.name,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === userId._id.toString(),
                                        });
                                    }
                                } else if (
                                    previousRoleStatus.some((el) => el.includes(CANCELLED)) ||
                                    previousRoleStatus.some((el) => el.includes(REJECT)) ||
                                    previousRoleStatus.every((el) => el.includes(NOT_APPLICABLE))
                                ) {
                                    overWritePreviousLevelApprovalFlowForUser();
                                } else if (previousEscalate) {
                                    if (Number(diff) >= Number(previousTat)) {
                                        if (
                                            previousRoleStatus.some((el) =>
                                                el.includes(CANCELLED),
                                            ) ||
                                            previousRoleStatus.some((el) => el.includes(REJECT)) ||
                                            previousRoleStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForUser();
                                        } else if (
                                            Number(diff) >= Number(currentTat) &&
                                            !previousRoleStatus.includes(NOT_INITIATED)
                                        ) {
                                            approver.user.push({
                                                _id: userId._id,
                                                name: userId.name,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === userId._id.toString(),
                                            });
                                        } else {
                                            if (
                                                (previousRoleStatus.some((el) =>
                                                    el.includes(REJECT),
                                                ) ||
                                                    previousRoleStatus.some((el) =>
                                                        el.includes(CANCELLED),
                                                    )) &&
                                                currentOverWrite
                                            ) {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: PENDING,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            } else {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: NOT_APPLICABLE,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            }
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllUserFlow();
                                    }
                                } else {
                                    skipPreviousLevelApprovalForAllUserFlow();
                                }
                            } else {
                                if (
                                    previousRoleWiseStatus.some((item) => item.includes(PENDING)) ||
                                    previousRoleWiseStatus.some((item) =>
                                        item.includes(CANCELLED),
                                    ) ||
                                    previousRoleWiseStatus.some((item) =>
                                        item.includes(NOT_INITIATED),
                                    ) ||
                                    previousRoleWiseStatus.some((item) => item.includes(DELAYED)) ||
                                    previousRoleWiseStatus.some((item) => item.includes(REJECT))
                                ) {
                                    if (previousEscalate) {
                                        if (Number(diff) >= Number(previousTat)) {
                                            if (
                                                previousRoleWiseStatus.some((el) =>
                                                    el.includes(CANCELLED),
                                                ) ||
                                                previousRoleWiseStatus.some((el) =>
                                                    el.includes(REJECT),
                                                ) ||
                                                previousRoleWiseStatus.every((el) =>
                                                    el.includes(NOT_APPLICABLE),
                                                )
                                            ) {
                                                overWritePreviousLevelApprovalFlowForUser();
                                            } else if (
                                                Number(diff) >= Number(currentTat) &&
                                                !previousRoleWiseStatus.some((item) =>
                                                    item.includes(NOT_INITIATED),
                                                )
                                            ) {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: DELAYED,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            } else {
                                                approver.user.push({
                                                    _id: userId._id,
                                                    name: userId.name,
                                                    status: PENDING,
                                                    isCurrentUser:
                                                        _user_id.toString() ===
                                                        userId._id.toString(),
                                                });
                                            }
                                        } else {
                                            skipPreviousLevelApprovalForAllUserFlow();
                                        }
                                    } else {
                                        if (
                                            previousRoleWiseStatus.some((el) =>
                                                el.includes(CANCELLED),
                                            ) ||
                                            previousRoleWiseStatus.some((el) =>
                                                el.includes(REJECT),
                                            ) ||
                                            previousRoleWiseStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForUser();
                                        } else {
                                            skipPreviousLevelApprovalForAllUserFlow();
                                        }
                                    }
                                } else {
                                    if (
                                        previousRoleWiseStatus.some((item) =>
                                            item.includes(REJECT),
                                        ) ||
                                        previousRoleWiseStatus.some((item) =>
                                            item.includes(CANCELLED),
                                        ) ||
                                        previousRoleStatus.every((item) =>
                                            item.includes(NOT_APPLICABLE),
                                        )
                                    ) {
                                        overWritePreviousLevelApprovalFlowForUser();
                                    } else if (
                                        previousRoleWiseStatus.every((el) =>
                                            el.includes(APPROVED),
                                        ) ||
                                        previousRoleWiseStatus.every((el) => el.includes(FORWARD))
                                    ) {
                                        if (Number(diff) >= Number(currentTat)) {
                                            approver.user.push({
                                                _id: userId._id,
                                                name: userId.name,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === userId._id.toString(),
                                            });
                                        } else {
                                            approver.user.push({
                                                _id: userId._id,
                                                name: userId.name,
                                                status: PENDING,
                                                isCurrentUser:
                                                    _user_id.toString() === userId._id.toString(),
                                            });
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllUserFlow();
                                    }
                                }
                            }
                        }
                    };
                    currentStatus =
                        currentCategoryBased === USER_BASED
                            ? levelApprover.level[index].user.map((el) => el.status)
                            : [];
                    previousStatus =
                        previousCategoryBased === USER_BASED
                            ? index - 1 >= 0
                                ? levelApprover.level[index - 1].user.map((el) => el.status)
                                : []
                            : [];
                    if (index === 0) {
                        if (
                            currentApproval === ALL_USERS ||
                            currentApproval === ANY_ONE_IN_EACH_ROLE
                        ) {
                            if (Number(diff) >= Number(currentTat)) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: DELAYED,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: PENDING,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            }
                        } else {
                            if (
                                currentStatus.includes(APPROVED) ||
                                currentStatus.includes(FORWARD) ||
                                currentStatus.includes(REJECT) ||
                                currentStatus.includes(CANCELLED) ||
                                currentStatus.includes(NOT_APPLICABLE)
                            ) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: NOT_APPLICABLE,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else if (Number(diff) >= Number(currentTat)) {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: DELAYED,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            } else {
                                approver.user.push({
                                    _id: userId._id,
                                    name: userId.name,
                                    status: PENDING,
                                    isCurrentUser: _user_id.toString() === userId._id.toString(),
                                });
                            }
                        }
                    } else if (currentApproval === ANY_ONE_USER) {
                        if (
                            currentStatus.includes(APPROVED) ||
                            currentStatus.includes(CANCELLED) ||
                            currentStatus.includes(FORWARD) ||
                            currentStatus.includes(REJECT) ||
                            currentStatus.includes(NOT_APPLICABLE)
                        ) {
                            approver.user.push({
                                _id: userId._id,
                                name: userId.name,
                                status: NOT_APPLICABLE,
                                isCurrentUser: _user_id.toString() === userId._id.toString(),
                            });
                        } else if (previousEscalate) {
                            if (
                                previousCategoryBased === USER_BASED &&
                                Number(diff) >= Number(previousTat) &&
                                !previousStatus.includes(NOT_INITIATED)
                            ) {
                                if (
                                    previousStatus.includes(CANCELLED) ||
                                    previousStatus.includes(REJECT) ||
                                    previousStatus.every((el) => el.includes(NOT_APPLICABLE))
                                ) {
                                    overWritePreviousLevelApprovalFlowForUser();
                                } else if (Number(diff) >= Number(currentTat)) {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: DELAYED,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                } else {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: PENDING,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                }
                            } else if (
                                previousCategoryBased === ROLE_BASED &&
                                Number(diff) >= Number(previousTat) &&
                                !previousRoleStatus.includes(NOT_INITIATED)
                            ) {
                                if (Number(diff) >= Number(currentTat)) {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: DELAYED,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                } else {
                                    approver.user.push({
                                        _id: userId._id,
                                        name: userId.name,
                                        status: PENDING,
                                        isCurrentUser:
                                            _user_id.toString() === userId._id.toString(),
                                    });
                                }
                            } else {
                                approvalPreviousCategoryBasedLogicForUsers();
                            }
                        } else {
                            approvalPreviousCategoryBasedLogicForUsers();
                        }
                    } else {
                        approvalPreviousCategoryBasedLogicForUsers();
                    }
                }
            }
            currentStatus =
                currentCategoryBased === USER_BASED
                    ? levelApprover.level[index].user.map((el) => el.status)
                    : [];
            let overWriteIndex;
            if (levelApprover.level.length - 1 === index) {
                levelApprover.level.filter((elem, index) => {
                    if (elem.overwritePreviousLevelApproval) {
                        overWriteIndex = index;
                    }
                });
                if (
                    approver.approvalConfig === ANY_ONE_USER ||
                    approver.approvalConfig === ANY_ONE_IN_ANY_ROLE
                ) {
                    if (currentStatus.includes(PENDING)) {
                        approvalStatus = `Pending with ${approver.levelName}`;
                    } else if (currentStatus.includes(DELAYED)) {
                        approvalStatus = DELAYED;
                    } else if (
                        currentStatus.includes(APPROVED) ||
                        currentStatus.includes(FORWARD)
                    ) {
                        if (approver.overwritePreviousLevelApproval) {
                            approvalStatus = APPROVED;
                        } else {
                            for (let i = index - 1; i >= 0; i--) {
                                if (levelApprover.level[i].categoryBased === USER_BASED) {
                                    if (
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(REJECT),
                                        ) ||
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(CANCELLED),
                                        )
                                    ) {
                                        if (overWriteIndex === -1) {
                                            approvalStatus = OVERWRITE_CANCELLED;
                                        } else {
                                            for (
                                                let j = overWriteIndex;
                                                j < levelApprover.level.length;
                                                j++
                                            ) {
                                                if (
                                                    levelApprover.level[j].categoryBased ===
                                                    USER_BASED
                                                ) {
                                                    if (
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(REJECT),
                                                        ) ||
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(CANCELLED),
                                                        )
                                                    ) {
                                                        approvalStatus = OVERWRITE_CANCELLED;
                                                    }
                                                } else if (
                                                    levelApprover.level[j].categoryBased ===
                                                    ROLE_BASED
                                                ) {
                                                    for (const [
                                                        previousRoleIdIndex,
                                                        previousRoleId,
                                                    ] of levelApprover.level[j].roleIds.entries()) {
                                                        if (
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === REJECT,
                                                            ) ||
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === CANCELLED,
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    for (const [
                                        previousRoleIdIndex,
                                        previousRoleId,
                                    ] of levelApprover.level[i].roleIds.entries()) {
                                        if (
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === REJECT) ||
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === CANCELLED)
                                        ) {
                                            if (overWriteIndex === -1) {
                                                approvalStatus = OVERWRITE_CANCELLED;
                                            } else {
                                                for (
                                                    let j = overWriteIndex;
                                                    j < levelApprover.level.length;
                                                    j++
                                                ) {
                                                    if (
                                                        levelApprover.level[j].categoryBased ===
                                                        ROLE_BASED
                                                    ) {
                                                        for (const [
                                                            previousRoleIdIndex,
                                                            previousRoleId,
                                                        ] of levelApprover.level[
                                                            j
                                                        ].roleIds.entries()) {
                                                            if (
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === REJECT,
                                                                ) ||
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === CANCELLED,
                                                                )
                                                            ) {
                                                                approvalStatus =
                                                                    OVERWRITE_CANCELLED;
                                                            }
                                                        }
                                                    } else if (
                                                        levelApprover.level[j].categoryBased ===
                                                        USER_BASED
                                                    ) {
                                                        if (
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(REJECT),
                                                            ) ||
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(CANCELLED),
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            approvalStatus =
                                approvalStatus === OVERWRITE_CANCELLED ? CANCELLED : APPROVED;
                        }
                    } else if (
                        currentStatus.includes(REJECT) ||
                        currentStatus.includes(CANCELLED)
                    ) {
                        approvalStatus = REJECT;
                    }
                } else if (
                    approver.approvalConfig === ALL_USERS ||
                    approver.approvalConfig === ANY_ONE_IN_EACH_ROLE
                ) {
                    if (currentStatus.some((statusElement) => statusElement.includes(PENDING))) {
                        approvalStatus = `Pending with ${approver.levelName}`;
                    } else if (currentStatus.includes(DELAYED)) {
                        approvalStatus = DELAYED;
                    } else if (
                        currentStatus.every((everyStatusElement) =>
                            everyStatusElement.includes(APPROVED),
                        ) ||
                        currentStatus.every((everyStatusElement) =>
                            everyStatusElement.includes(FORWARD),
                        )
                    ) {
                        if (approver.overwritePreviousLevelApproval) {
                            approvalStatus = APPROVED;
                        } else {
                            for (let i = index - 1; i >= 0; i--) {
                                if (levelApprover.level[i].categoryBased === USER_BASED) {
                                    if (
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(REJECT),
                                        ) ||
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(CANCELLED),
                                        )
                                    ) {
                                        if (overWriteIndex === -1) {
                                            approvalStatus = REJECT;
                                        } else {
                                            for (
                                                let j = overWriteIndex;
                                                j < levelApprover.level.length;
                                                j++
                                            ) {
                                                if (
                                                    levelApprover.level[j].categoryBased ===
                                                    USER_BASED
                                                ) {
                                                    if (
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(REJECT),
                                                        ) ||
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(CANCELLED),
                                                        )
                                                    ) {
                                                        approvalStatus = OVERWRITE_CANCELLED;
                                                    }
                                                } else if (
                                                    levelApprover.level[j].categoryBased ===
                                                    ROLE_BASED
                                                ) {
                                                    for (const [
                                                        previousRoleIdIndex,
                                                        previousRoleId,
                                                    ] of levelApprover.level[j].roleIds.entries()) {
                                                        if (
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === REJECT,
                                                            ) ||
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === CANCELLED,
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    for (const [
                                        previousRoleIdIndex,
                                        previousRoleId,
                                    ] of levelApprover.level[i].roleIds.entries()) {
                                        if (
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === REJECT) ||
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === CANCELLED)
                                        ) {
                                            if (overWriteIndex === -1) {
                                                approvalStatus = OVERWRITE_CANCELLED;
                                            } else {
                                                for (
                                                    let j = overWriteIndex;
                                                    j < levelApprover.level.length;
                                                    j++
                                                ) {
                                                    if (
                                                        levelApprover.level[j].categoryBased ===
                                                        ROLE_BASED
                                                    ) {
                                                        for (const [
                                                            previousRoleIdIndex,
                                                            previousRoleId,
                                                        ] of levelApprover.level[
                                                            j
                                                        ].roleIds.entries()) {
                                                            if (
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === REJECT,
                                                                ) ||
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === CANCELLED,
                                                                )
                                                            ) {
                                                                approvalStatus =
                                                                    OVERWRITE_CANCELLED;
                                                            }
                                                        }
                                                    } else if (
                                                        levelApprover.level[j].categoryBased ===
                                                        USER_BASED
                                                    ) {
                                                        if (
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(REJECT),
                                                            ) ||
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(CANCELLED),
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            approvalStatus =
                                approvalStatus === OVERWRITE_CANCELLED ? CANCELLED : APPROVED;
                        }
                    } else if (
                        currentStatus.some((someStatusElement) =>
                            someStatusElement.includes(REJECT),
                        ) ||
                        currentStatus.some((someStatusElement) =>
                            someStatusElement.includes(CANCELLED),
                        )
                    ) {
                        approvalStatus = REJECT;
                    }
                }
            } else if (
                approver.approvalConfig === ANY_ONE_USER ||
                approver.approvalConfig === ANY_ONE_IN_ANY_ROLE
            ) {
                if (currentStatus.includes(PENDING)) {
                    approvalStatus = `Pending with ${approver.levelName}`;
                } else if (currentStatus.includes(REJECT) || currentStatus.includes(CANCELLED)) {
                    approvalStatus = REJECT;
                }
            } else if (
                approver.approvalConfig === ALL_USERS ||
                approver.approvalConfig === ANY_ONE_IN_EACH_ROLE
            ) {
                if (currentStatus.some((statusElement) => statusElement.includes(PENDING))) {
                    approvalStatus = `Pending with ${approver.levelName}`;
                } else if (
                    currentStatus.some(
                        (statusElement) =>
                            statusElement.includes(REJECT) || statusElement.includes(CANCELLED),
                    )
                ) {
                    approvalStatus = REJECT;
                }
            }
        } else if (approver.categoryBased === ROLE_BASED) {
            for (const [roleIndex, roleId] of approver.roleIds.entries()) {
                let roleUsers = roleAssignDocs
                    .filter((roleAssignDoc) => {
                        return roleAssignDoc.roles.filter((role) => {
                            if (
                                role.role_name === SCHEDULE_STAFF_DEFAULT_ROLE
                                    ? studentWarningData.roleUsers.find(
                                          (roleUserElement) =>
                                              String(roleUserElement) ===
                                              String(roleAssignDoc._user_id._id),
                                      )
                                    : role.program.find(
                                          (roleWiseELement) =>
                                              roleWiseELement &&
                                              String(roleWiseELement._program_id) ===
                                                  String(studentWarningData.programId),
                                      )
                            ) {
                                return role._role_id.toString() === roleId._id.toString();
                            }
                        }).length;
                    })
                    .map((role) => {
                        if (role._user_id) return role._user_id;
                        return null;
                    })
                    .filter((element) => element != null);
                let userRoleIds =
                    roleUsers && roleUsers.length
                        ? roleUsers.map((roleUser) => {
                              if (roleUser._id) {
                                  return roleUser._id.toString();
                              }
                          })
                        : [];
                if (roleId.name === COURSE_COORDINATOR) {
                    roleUsers = courseCoordinators;
                    userRoleIds =
                        roleUsers && roleUsers.length
                            ? roleUsers.map((roleUser) => {
                                  if (roleUser._id) {
                                      return roleUser._id.toString();
                                  }
                              })
                            : [];
                }
                if (approvalRoleId.includes(roleId._id.toString())) {
                    for (const userId of userRoleIds) {
                        if (approvalUserId.includes(userId.toString())) {
                            const staffApprovalStatus = studentWarningData.approvalFrom.find(
                                (element) => {
                                    return (
                                        element.userId._id.toString() === userId.toString() &&
                                        element.roleId._id.toString() === roleId._id.toString()
                                    );
                                },
                            );
                            if (staffApprovalStatus) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map((item) => {
                                        if (item._id.toString() === userId.toString()) {
                                            return {
                                                ...item,
                                                status: staffApprovalStatus.status,
                                                approvalDate: staffApprovalStatus.date,
                                                reason: staffApprovalStatus.reason
                                                    ? staffApprovalStatus.reason
                                                    : '',
                                                isCurrentUser:
                                                    _user_id.toString() === item._id.toString(),
                                            };
                                        }
                                        return {
                                            ...item,
                                            status: NOT_APPLICABLE,
                                            isCurrentUser:
                                                _user_id.toString() === item._id.toString(),
                                        };
                                    }),
                                });
                                if (
                                    currentApproval === ANY_ONE_USER ||
                                    currentApproval === ANY_ONE_IN_ANY_ROLE
                                ) {
                                    if (roleIndex) {
                                        for (let i = roleIndex - 1; i >= 0; i--) {
                                            approver.role[i].roleUsers = approver.role[
                                                i
                                            ].roleUsers.map((item) => {
                                                return { ...item, status: NOT_APPLICABLE };
                                            });
                                        }
                                    }
                                } else if (
                                    currentApproval === ALL_USERS ||
                                    currentApproval === ANY_ONE_IN_EACH_ROLE
                                ) {
                                    if (roleIndex) {
                                        for (let i = roleIndex - 1; i >= 0; i--) {
                                            approver.role[i].roleUsers = approver.role[
                                                i
                                            ].roleUsers.map((item) => {
                                                if (item.status === NOT_INITIATED) {
                                                    return { ...item, status: PENDING };
                                                }
                                                return item;
                                            });
                                        }
                                    }
                                }
                                if (approver.skipPreviousLevelApproval === true) {
                                    if (index) {
                                        for (let i = index - 1; i >= 0; i--) {
                                            if (
                                                levelApprover.level[i].categoryBased === USER_BASED
                                            ) {
                                                for (const [
                                                    previousUserIdIndex,
                                                    previousUserId,
                                                ] of levelApprover.level[i].userIds.entries()) {
                                                    if (
                                                        levelApprover.level[i].user[
                                                            previousUserIdIndex
                                                        ].status === PENDING ||
                                                        levelApprover.level[i].user[
                                                            previousUserIdIndex
                                                        ].status === NOT_INITIATED
                                                    ) {
                                                        levelApprover.level[i].user[
                                                            previousUserIdIndex
                                                        ].status = SKIPPED;
                                                    }
                                                }
                                            } else {
                                                for (const [
                                                    previousRoleIdIndex,
                                                    previousRoleId,
                                                ] of levelApprover.level[i].roleIds.entries()) {
                                                    if (
                                                        levelApprover.level[i].role[
                                                            previousRoleIdIndex
                                                        ].roleUsers.some(
                                                            (item) => item.status === PENDING,
                                                        ) ||
                                                        levelApprover.level[i].role[
                                                            previousRoleIdIndex
                                                        ].roleUsers.some(
                                                            (item) => item.status === NOT_INITIATED,
                                                        ) ||
                                                        levelApprover.level[i].role[
                                                            previousRoleIdIndex
                                                        ].roleUsers.some(
                                                            (item) => item.status === DELAYED,
                                                        )
                                                    ) {
                                                        levelApprover.level[i].role[
                                                            previousRoleIdIndex
                                                        ].roleUsers = levelApprover.level[i].role[
                                                            previousRoleIdIndex
                                                        ].roleUsers.map((item) => {
                                                            return {
                                                                ...item,
                                                                status:
                                                                    item.status === FORWARD
                                                                        ? FORWARD
                                                                        : item.status === REJECT
                                                                        ? REJECT
                                                                        : item.status ===
                                                                          NOT_APPLICABLE
                                                                        ? NOT_APPLICABLE
                                                                        : item.status === DELAYED
                                                                        ? DELAYED
                                                                        : SKIPPED,
                                                            };
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    currentRoleStatus = levelApprover.level[index].role
                        ? levelApprover.level[index].role
                              .map((el) => el.roleUsers)
                              .flat()
                              .map((nestedElement) => nestedElement.status)
                        : [];
                    const overWritePreviousLevelApprovalFlowForRole = () => {
                        if (
                            previousApproval === ANY_ONE_IN_ANY_ROLE ||
                            previousApproval === ANY_ONE_USER
                        ) {
                            if (currentOverWrite) {
                                if (Number(diff) >= Number(currentTat)) {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: DELAYED,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                } else {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                }
                            } else {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: NOT_APPLICABLE,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            }
                        } else {
                            if (
                                previousCategoryBased === ROLE_BASED &&
                                previousRoleWiseStatus.some((elem) => elem.includes(PENDING))
                            ) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: NOT_INITIATED,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            } else if (
                                previousCategoryBased === USER_BASED &&
                                previousStatus.some((elem) => elem.includes(PENDING))
                            ) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: NOT_INITIATED,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            } else {
                                if (currentOverWrite) {
                                    if (Number(diff) >= Number(currentTat)) {
                                        approver.role.push({
                                            roleId: roleId._id,
                                            name: roleId.name,
                                            roleUsers: roleUsers.map(({ ...role }) => ({
                                                ...role,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === role._id.toString(),
                                            })),
                                        });
                                    } else {
                                        approver.role.push({
                                            roleId: roleId._id,
                                            name: roleId.name,
                                            roleUsers: roleUsers.map(({ ...role }) => ({
                                                ...role,
                                                status: PENDING,
                                                isCurrentUser:
                                                    _user_id.toString() === role._id.toString(),
                                            })),
                                        });
                                    }
                                } else {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: NOT_APPLICABLE,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                }
                            }
                        }
                    };
                    const skipPreviousLevelApprovalForAllRoleFlow = () => {
                        if (approver.skipPreviousLevelApproval) {
                            const previousStatusCheckForSkipPrevious = [];
                            for (let i = roleIndex - 1; i >= 0; i--) {
                                previousStatusCheckForSkipPrevious.push(
                                    approver.role[i].roleUsers.map((item) => item.status),
                                );
                            }
                            if (
                                previousStatusCheckForSkipPrevious.length &&
                                previousStatusCheckForSkipPrevious
                                    .flat()
                                    .every((el) => el === NOT_INITIATED)
                            ) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: NOT_INITIATED,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            } else if (!previousStatusCheckForSkipPrevious.length) {
                                if (
                                    //check Previous Approval whether each role is approved
                                    previousApproval === ANY_ONE_IN_EACH_ROLE &&
                                    (previousRoleWiseStatus.every((item) =>
                                        item.includes(NOT_APPLICABLE),
                                    ) ||
                                        previousRoleWiseStatus.every((item) =>
                                            item.includes(FORWARD),
                                        ) ||
                                        previousRoleWiseStatus.every((item) =>
                                            item.includes(REJECT),
                                        ) ||
                                        previousRoleWiseStatus.every(
                                            (item) =>
                                                item.includes(FORWARD) || item.includes(REJECT),
                                        ))
                                ) {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                } else {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: NOT_INITIATED,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                }
                            } else {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: PENDING,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            }
                        } else {
                            approver.role.push({
                                roleId: roleId._id,
                                name: roleId.name,
                                roleUsers: roleUsers.map(({ ...role }) => ({
                                    ...role,
                                    status: NOT_INITIATED,
                                    isCurrentUser: _user_id.toString() === role._id.toString(),
                                })),
                            });
                        }
                    };
                    const approvalPreviousCategoryBasedLogicForRoles = () => {
                        if (previousCategoryBased === ROLE_BASED) {
                            if (
                                previousApproval === ANY_ONE_IN_ANY_ROLE ||
                                previousApproval === ANY_ONE_USER
                            ) {
                                if (
                                    previousRoleStatus.includes(APPROVED) ||
                                    previousRoleStatus.includes(FORWARD)
                                ) {
                                    if (Number(diff) >= Number(currentTat)) {
                                        approver.role.push({
                                            roleId: roleId._id,
                                            name: roleId.name,
                                            roleUsers: roleUsers.map(({ ...role }) => ({
                                                ...role,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === role._id.toString(),
                                            })),
                                        });
                                    } else {
                                        approver.role.push({
                                            roleId: roleId._id,
                                            name: roleId.name,
                                            roleUsers: roleUsers.map(({ ...role }) => ({
                                                ...role,
                                                status: PENDING,
                                                isCurrentUser:
                                                    _user_id.toString() === role._id.toString(),
                                            })),
                                        });
                                    }
                                } else if (
                                    previousRoleStatus.some((el) => el.includes(CANCELLED)) ||
                                    previousRoleStatus.some((el) => el.includes(REJECT)) ||
                                    previousRoleStatus.every((el) => el.includes(NOT_APPLICABLE))
                                ) {
                                    overWritePreviousLevelApprovalFlowForRole();
                                } else if (previousEscalate) {
                                    if (Number(diff) >= Number(previousTat)) {
                                        if (
                                            previousRoleStatus.some((el) =>
                                                el.includes(CANCELLED),
                                            ) ||
                                            previousRoleStatus.some((el) => el.includes(REJECT)) ||
                                            previousRoleStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForRole();
                                        } else if (
                                            Number(diff) >= Number(currentTat) &&
                                            !previousRoleStatus.includes(NOT_INITIATED)
                                        ) {
                                            approver.role.push({
                                                roleId: roleId._id,
                                                name: roleId.name,
                                                roleUsers: roleUsers.map(({ ...role }) => ({
                                                    ...role,
                                                    status: DELAYED,
                                                    isCurrentUser:
                                                        _user_id.toString() === role._id.toString(),
                                                })),
                                            });
                                        } else {
                                            if (
                                                (previousRoleStatus.some((el) =>
                                                    el.includes(REJECT),
                                                ) ||
                                                    previousRoleStatus.some((el) =>
                                                        el.includes(CANCELLED),
                                                    )) &&
                                                currentOverWrite
                                            ) {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: PENDING,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            } else {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: NOT_APPLICABLE,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            }
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllRoleFlow();
                                    }
                                } else {
                                    skipPreviousLevelApprovalForAllRoleFlow();
                                }
                            } else {
                                if (
                                    previousRoleWiseStatus.some((item) => item.includes(PENDING)) ||
                                    previousRoleWiseStatus.some((item) =>
                                        item.includes(NOT_INITIATED),
                                    ) ||
                                    // previousRoleWiseStatus.some((item) =>
                                    //     item.includes(NOT_APPLICABLE),
                                    // ) ||
                                    previousRoleWiseStatus.some((item) => item.includes(DELAYED))
                                ) {
                                    if (previousEscalate) {
                                        if (Number(diff) >= Number(previousTat)) {
                                            if (
                                                previousRoleWiseStatus.some((el) =>
                                                    el.includes(CANCELLED),
                                                ) ||
                                                previousRoleWiseStatus.some((el) =>
                                                    el.includes(REJECT),
                                                ) ||
                                                previousRoleWiseStatus.every((el) =>
                                                    el.includes(NOT_APPLICABLE),
                                                )
                                            ) {
                                                overWritePreviousLevelApprovalFlowForRole();
                                            } else if (
                                                Number(diff) >= Number(currentTat) &&
                                                !previousRoleWiseStatus.some((item) =>
                                                    item.includes(NOT_INITIATED),
                                                )
                                            ) {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: DELAYED,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            } else {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: PENDING,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            }
                                        } else {
                                            skipPreviousLevelApprovalForAllRoleFlow();
                                        }
                                    } else {
                                        if (
                                            previousRoleWiseStatus.some((el) =>
                                                el.includes(CANCELLED),
                                            ) ||
                                            previousRoleWiseStatus.some((el) =>
                                                el.includes(REJECT),
                                            ) ||
                                            previousRoleWiseStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForRole();
                                        } else {
                                            if (
                                                previousRoleWiseStatus.every((el) =>
                                                    el.includes(APPROVED),
                                                ) ||
                                                previousRoleWiseStatus.every((el) =>
                                                    el.includes(FORWARD),
                                                )
                                            ) {
                                                if (Number(diff) >= Number(currentTat)) {
                                                    approver.role.push({
                                                        roleId: roleId._id,
                                                        name: roleId.name,
                                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                                            ...role,
                                                            status: DELAYED,
                                                            isCurrentUser:
                                                                _user_id.toString() ===
                                                                role._id.toString(),
                                                        })),
                                                    });
                                                } else {
                                                    approver.role.push({
                                                        roleId: roleId._id,
                                                        name: roleId.name,
                                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                                            ...role,
                                                            status: PENDING,
                                                            isCurrentUser:
                                                                _user_id.toString() ===
                                                                role._id.toString(),
                                                        })),
                                                    });
                                                }
                                            } else {
                                                skipPreviousLevelApprovalForAllRoleFlow();
                                            }
                                        }
                                    }
                                } else {
                                    if (
                                        previousRoleWiseStatus.some((item) =>
                                            item.includes(REJECT),
                                        ) ||
                                        previousRoleWiseStatus.some((item) =>
                                            item.includes(CANCELLED),
                                        ) ||
                                        previousRoleStatus.every((item) =>
                                            item.includes(NOT_APPLICABLE),
                                        )
                                    ) {
                                        overWritePreviousLevelApprovalFlowForRole();
                                    } else if (
                                        previousRoleWiseStatus.every((el) =>
                                            el.includes(APPROVED),
                                        ) ||
                                        previousRoleWiseStatus.every((el) => el.includes(FORWARD))
                                    ) {
                                        if (Number(diff) >= Number(currentTat)) {
                                            approver.role.push({
                                                roleId: roleId._id,
                                                name: roleId.name,
                                                roleUsers: roleUsers.map(({ ...role }) => ({
                                                    ...role,
                                                    status: DELAYED,
                                                    isCurrentUser:
                                                        _user_id.toString() === role._id.toString(),
                                                })),
                                            });
                                        } else {
                                            approver.role.push({
                                                roleId: roleId._id,
                                                name: roleId.name,
                                                roleUsers: roleUsers.map(({ ...role }) => ({
                                                    ...role,
                                                    status: PENDING,
                                                    isCurrentUser:
                                                        _user_id.toString() === role._id.toString(),
                                                })),
                                            });
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllRoleFlow();
                                    }
                                }
                            }
                        } else if (previousCategoryBased === USER_BASED) {
                            if (
                                previousApproval === ANY_ONE_IN_ANY_ROLE ||
                                previousApproval === ANY_ONE_USER
                            ) {
                                if (
                                    previousStatus.includes(APPROVED) ||
                                    previousStatus.includes(FORWARD)
                                ) {
                                    if (Number(diff) >= Number(currentTat)) {
                                        approver.role.push({
                                            roleId: roleId._id,
                                            name: roleId.name,
                                            roleUsers: roleUsers.map(({ ...role }) => ({
                                                ...role,
                                                status: DELAYED,
                                                isCurrentUser:
                                                    _user_id.toString() === role._id.toString(),
                                            })),
                                        });
                                    } else {
                                        approver.role.push({
                                            roleId: roleId._id,
                                            name: roleId.name,
                                            roleUsers: roleUsers.map(({ ...role }) => ({
                                                ...role,
                                                status: PENDING,
                                                isCurrentUser:
                                                    _user_id.toString() === role._id.toString(),
                                            })),
                                        });
                                    }
                                } else if (
                                    previousStatus.includes(CANCELLED) ||
                                    previousStatus.includes(REJECT) ||
                                    previousStatus.every((el) => el.includes(NOT_APPLICABLE))
                                ) {
                                    overWritePreviousLevelApprovalFlowForRole();
                                } else if (previousEscalate) {
                                    if (Number(diff) >= Number(previousTat)) {
                                        if (
                                            previousStatus.includes(CANCELLED) ||
                                            previousStatus.includes(REJECT) ||
                                            previousStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForRole();
                                        } else if (
                                            Number(diff) >= Number(currentTat) &&
                                            !previousRoleStatus.includes(NOT_INITIATED)
                                        ) {
                                            approver.role.push({
                                                roleId: roleId._id,
                                                name: roleId.name,
                                                roleUsers: roleUsers.map(({ ...role }) => ({
                                                    ...role,
                                                    status: DELAYED,
                                                    isCurrentUser:
                                                        _user_id.toString() === role._id.toString(),
                                                })),
                                            });
                                        } else {
                                            if (
                                                (previousStatus.includes(REJECT) ||
                                                    previousStatus.includes(CANCELLED)) &&
                                                currentOverWrite
                                            ) {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: PENDING,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            } else {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: NOT_APPLICABLE,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            }
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllRoleFlow();
                                    }
                                } else {
                                    skipPreviousLevelApprovalForAllRoleFlow();
                                }
                            } else {
                                if (
                                    previousStatus.includes(PENDING) ||
                                    previousStatus.includes(NOT_INITIATED) ||
                                    previousStatus.includes(NOT_APPLICABLE) ||
                                    previousStatus.includes(DELAYED)
                                ) {
                                    if (previousEscalate) {
                                        if (Number(diff) >= Number(previousTat)) {
                                            if (
                                                previousStatus.includes(CANCELLED) ||
                                                previousStatus.includes(REJECT) ||
                                                previousStatus.every((el) =>
                                                    el.includes(NOT_APPLICABLE),
                                                )
                                            ) {
                                                overWritePreviousLevelApprovalFlowForRole();
                                            } else if (
                                                Number(diff) >= Number(currentTat) &&
                                                !previousStatus.includes(NOT_INITIATED)
                                            ) {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: DELAYED,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            } else {
                                                approver.role.push({
                                                    roleId: roleId._id,
                                                    name: roleId.name,
                                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                                        ...role,
                                                        status: PENDING,
                                                        isCurrentUser:
                                                            _user_id.toString() ===
                                                            role._id.toString(),
                                                    })),
                                                });
                                            }
                                        } else {
                                            skipPreviousLevelApprovalForAllRoleFlow();
                                        }
                                    } else {
                                        if (
                                            previousStatus.includes(CANCELLED) ||
                                            previousStatus.includes(REJECT) ||
                                            previousStatus.every((el) =>
                                                el.includes(NOT_APPLICABLE),
                                            )
                                        ) {
                                            overWritePreviousLevelApprovalFlowForRole();
                                        } else {
                                            skipPreviousLevelApprovalForAllRoleFlow();
                                        }
                                    }
                                } else {
                                    if (
                                        previousStatus.includes(CANCELLED) ||
                                        previousStatus.includes(REJECT) ||
                                        previousStatus.every((el) => el.includes(NOT_APPLICABLE))
                                    ) {
                                        overWritePreviousLevelApprovalFlowForRole();
                                    } else if (
                                        previousStatus.every((el) => el.includes(APPROVED)) ||
                                        previousStatus.every((el) => el.includes(FORWARD))
                                    ) {
                                        if (Number(diff) >= Number(currentTat)) {
                                            approver.role.push({
                                                roleId: roleId._id,
                                                name: roleId.name,
                                                roleUsers: roleUsers.map(({ ...role }) => ({
                                                    ...role,
                                                    status: DELAYED,
                                                    isCurrentUser:
                                                        _user_id.toString() === role._id.toString(),
                                                })),
                                            });
                                        } else {
                                            approver.role.push({
                                                roleId: roleId._id,
                                                name: roleId.name,
                                                roleUsers: roleUsers.map(({ ...role }) => ({
                                                    ...role,
                                                    status: PENDING,
                                                    isCurrentUser:
                                                        _user_id.toString() === role._id.toString(),
                                                })),
                                            });
                                        }
                                    } else {
                                        skipPreviousLevelApprovalForAllRoleFlow();
                                    }
                                }
                            }
                        }
                    };
                    if (index === 0) {
                        if (
                            levelApprover.level[index].approvalConfig === ANY_ONE_IN_ANY_ROLE ||
                            levelApprover.level[index].approvalConfig === ANY_ONE_USER
                        ) {
                            if (
                                currentRoleStatus.includes(APPROVED) ||
                                currentRoleStatus.includes(FORWARD) ||
                                currentRoleStatus.includes(CANCELLED) ||
                                currentRoleStatus.includes(REJECT) ||
                                currentRoleStatus.includes(NOT_APPLICABLE)
                            ) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: NOT_APPLICABLE,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            } else if (Number(diff) >= Number(currentTat)) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers.map(({ ...role }) => ({
                                        ...role,
                                        status: DELAYED,
                                        isCurrentUser: _user_id.toString() === role._id.toString(),
                                    })),
                                });
                            } else {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers
                                        ? roleUsers.length
                                            ? roleUsers.map((role) => {
                                                  if (role._id) {
                                                      return {
                                                          ...role,
                                                          status: PENDING,
                                                          isCurrentUser:
                                                              _user_id.toString() ===
                                                              role._id.toString(),
                                                      };
                                                  }
                                              })
                                            : []
                                        : [],
                                });
                            }
                        } else {
                            if (Number(diff) >= Number(currentTat)) {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers
                                        ? roleUsers.length
                                            ? roleUsers.map((role) => {
                                                  if (role._id) {
                                                      return {
                                                          ...role,
                                                          status: DELAYED,
                                                          isCurrentUser:
                                                              _user_id.toString() ===
                                                              role._id.toString(),
                                                      };
                                                  }
                                              })
                                            : []
                                        : [],
                                });
                            } else {
                                approver.role.push({
                                    roleId: roleId._id,
                                    name: roleId.name,
                                    roleUsers: roleUsers
                                        ? roleUsers.length
                                            ? roleUsers.map((role) => {
                                                  if (role._id) {
                                                      return {
                                                          ...role,
                                                          status: PENDING,
                                                          isCurrentUser:
                                                              _user_id.toString() ===
                                                              role._id.toString(),
                                                      };
                                                  }
                                              })
                                            : []
                                        : [],
                                });
                            }
                        }
                    } else if (
                        levelApprover.level[index].approvalConfig === ANY_ONE_IN_ANY_ROLE ||
                        levelApprover.level[index].approvalConfig === ANY_ONE_USER
                    ) {
                        if (
                            currentRoleStatus.includes(APPROVED) ||
                            currentRoleStatus.includes(CANCELLED) ||
                            currentRoleStatus.includes(FORWARD) ||
                            currentRoleStatus.includes(REJECT) ||
                            currentRoleStatus.includes(NOT_APPLICABLE)
                        ) {
                            approver.role.push({
                                roleId: roleId._id,
                                name: roleId.name,
                                roleUsers: roleUsers.map(({ ...role }) => ({
                                    ...role,
                                    status: NOT_APPLICABLE,
                                    isCurrentUser: _user_id.toString() === role._id.toString(),
                                })),
                            });
                        } else if (previousEscalate) {
                            if (
                                previousCategoryBased === ROLE_BASED &&
                                Number(diff) >= Number(previousTat) &&
                                !previousRoleStatus.includes(NOT_INITIATED)
                            ) {
                                if (
                                    previousRoleStatus.some((el) => el.includes(CANCELLED)) ||
                                    previousRoleStatus.some((el) => el.includes(REJECT)) ||
                                    previousRoleStatus.every((el) => el.includes(NOT_APPLICABLE))
                                ) {
                                    overWritePreviousLevelApprovalFlowForRole();
                                } else if (Number(diff) >= Number(currentTat)) {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: DELAYED,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                } else {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                }
                            } else if (
                                previousCategoryBased === USER_BASED &&
                                Number(diff) >= Number(previousTat) &&
                                !previousStatus.includes(NOT_INITIATED)
                            ) {
                                if (Number(diff) >= Number(currentTat)) {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: DELAYED,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                } else {
                                    approver.role.push({
                                        roleId: roleId._id,
                                        name: roleId.name,
                                        roleUsers: roleUsers.map(({ ...role }) => ({
                                            ...role,
                                            status: PENDING,
                                            isCurrentUser:
                                                _user_id.toString() === role._id.toString(),
                                        })),
                                    });
                                }
                            } else {
                                approvalPreviousCategoryBasedLogicForRoles();
                            }
                        } else {
                            approvalPreviousCategoryBasedLogicForRoles();
                        }
                    } else {
                        approvalPreviousCategoryBasedLogicForRoles();
                    }
                }
            }
            currentRoleStatus =
                currentCategoryBased === ROLE_BASED
                    ? levelApprover.level[index].role
                        ? levelApprover.level[index].role
                              .map((el) => el.roleUsers)
                              .flat()
                              .map((nestedElement) => nestedElement.status)
                        : []
                    : [];
            currentRoleWiseStatus =
                currentCategoryBased === ROLE_BASED
                    ? levelApprover.level[index].role
                        ? levelApprover.level[index].role.map((el) => {
                              if (el.roleUsers.length) {
                                  return el.roleUsers.map((nestedElement) => nestedElement.status);
                              }
                          })
                        : []
                    : [];
            let overWriteIndex;
            if (levelApprover.level.length - 1 === index) {
                levelApprover.level.filter((elem, index) => {
                    if (elem.overwritePreviousLevelApproval) {
                        overWriteIndex = index;
                    }
                });
                if (
                    approver.approvalConfig === ANY_ONE_USER ||
                    approver.approvalConfig === ANY_ONE_IN_ANY_ROLE
                ) {
                    if (currentRoleStatus.includes(PENDING)) {
                        approvalStatus = `Pending with ${approver.levelName}`;
                    } else if (currentRoleStatus.includes(DELAYED)) {
                        approvalStatus = DELAYED;
                    } else if (
                        currentRoleStatus.includes(APPROVED) ||
                        currentRoleStatus.includes(FORWARD)
                    ) {
                        if (approver.overwritePreviousLevelApproval) {
                            approvalStatus = APPROVED;
                        } else {
                            for (let i = index - 1; i >= 0; i--) {
                                if (levelApprover.level[i].categoryBased === USER_BASED) {
                                    if (
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(REJECT),
                                        ) ||
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(CANCELLED),
                                        )
                                    ) {
                                        if (overWriteIndex === -1) {
                                            approvalStatus = OVERWRITE_CANCELLED;
                                        } else {
                                            for (
                                                let j = overWriteIndex;
                                                j < levelApprover.level.length;
                                                j++
                                            ) {
                                                if (
                                                    levelApprover.level[j].categoryBased ===
                                                    USER_BASED
                                                ) {
                                                    if (
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(REJECT),
                                                        ) ||
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(CANCELLED),
                                                        )
                                                    ) {
                                                        approvalStatus = OVERWRITE_CANCELLED;
                                                    }
                                                } else if (
                                                    levelApprover.level[j].categoryBased ===
                                                    ROLE_BASED
                                                ) {
                                                    for (const [
                                                        previousRoleIdIndex,
                                                        previousRoleId,
                                                    ] of levelApprover.level[j].roleIds.entries()) {
                                                        if (
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === REJECT,
                                                            ) ||
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === CANCELLED,
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    for (const [
                                        previousRoleIdIndex,
                                        previousRoleId,
                                    ] of levelApprover.level[i].roleIds.entries()) {
                                        if (
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === REJECT) ||
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === CANCELLED)
                                        ) {
                                            if (overWriteIndex === -1) {
                                                approvalStatus = REJECT;
                                            } else {
                                                for (
                                                    let j = overWriteIndex;
                                                    j < levelApprover.level.length;
                                                    j++
                                                ) {
                                                    if (
                                                        levelApprover.level[j].categoryBased ===
                                                        ROLE_BASED
                                                    ) {
                                                        for (const [
                                                            previousRoleIdIndex,
                                                            previousRoleId,
                                                        ] of levelApprover.level[
                                                            j
                                                        ].roleIds.entries()) {
                                                            if (
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === REJECT,
                                                                ) ||
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === CANCELLED,
                                                                )
                                                            ) {
                                                                approvalStatus =
                                                                    OVERWRITE_CANCELLED;
                                                            }
                                                        }
                                                    } else if (
                                                        levelApprover.level[j].categoryBased ===
                                                        USER_BASED
                                                    ) {
                                                        if (
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(REJECT),
                                                            ) ||
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(CANCELLED),
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            approvalStatus =
                                approvalStatus === OVERWRITE_CANCELLED ? CANCELLED : APPROVED;
                        }
                    } else if (
                        currentRoleStatus.includes(REJECT) ||
                        currentRoleStatus.includes(CANCELLED)
                    ) {
                        approvalStatus = REJECT;
                    }
                } else if (
                    approver.approvalConfig === ALL_USERS ||
                    approver.approvalConfig === ANY_ONE_IN_EACH_ROLE
                ) {
                    if (
                        currentRoleWiseStatus.some((statusElement) =>
                            statusElement.includes(PENDING),
                        )
                    ) {
                        approvalStatus = `Pending with ${approver.levelName}`;
                    } else if (
                        currentRoleWiseStatus.some((statusElement) =>
                            statusElement.includes(DELAYED),
                        )
                    ) {
                        approvalStatus = DELAYED;
                    } else if (
                        currentRoleWiseStatus.every((everyStatusElement) =>
                            everyStatusElement.includes(APPROVED),
                        ) ||
                        currentRoleWiseStatus.every((everyStatusElement) =>
                            everyStatusElement.includes(FORWARD),
                        )
                    ) {
                        if (approver.overwritePreviousLevelApproval) {
                            approvalStatus = APPROVED;
                        } else {
                            for (let i = index - 1; i >= 0; i--) {
                                if (levelApprover.level[i].categoryBased === USER_BASED) {
                                    if (
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(REJECT),
                                        ) ||
                                        levelApprover.level[i].user.some((ele) =>
                                            ele.status.includes(CANCELLED),
                                        )
                                    ) {
                                        if (overWriteIndex === -1) {
                                            approvalStatus = OVERWRITE_CANCELLED;
                                        } else {
                                            for (
                                                let j = overWriteIndex;
                                                j < levelApprover.level.length;
                                                j++
                                            ) {
                                                if (
                                                    levelApprover.level[j].categoryBased ===
                                                    USER_BASED
                                                ) {
                                                    if (
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(REJECT),
                                                        ) ||
                                                        levelApprover.level[j].user.some((ele) =>
                                                            ele.status.includes(CANCELLED),
                                                        )
                                                    ) {
                                                        approvalStatus = OVERWRITE_CANCELLED;
                                                    }
                                                } else if (
                                                    levelApprover.level[j].categoryBased ===
                                                    ROLE_BASED
                                                ) {
                                                    for (const [
                                                        previousRoleIdIndex,
                                                        previousRoleId,
                                                    ] of levelApprover.level[j].roleIds.entries()) {
                                                        if (
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === REJECT,
                                                            ) ||
                                                            levelApprover.level[j].role[
                                                                previousRoleIdIndex
                                                            ].roleUsers.some(
                                                                (item) => item.status === CANCELLED,
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    for (const [
                                        previousRoleIdIndex,
                                        previousRoleId,
                                    ] of levelApprover.level[i].roleIds.entries()) {
                                        if (
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === REJECT) ||
                                            levelApprover.level[i].role[
                                                previousRoleIdIndex
                                            ].roleUsers.some((item) => item.status === CANCELLED)
                                        ) {
                                            if (overWriteIndex === -1) {
                                                approvalStatus = OVERWRITE_CANCELLED;
                                            } else {
                                                for (
                                                    let j = overWriteIndex;
                                                    j < levelApprover.level.length;
                                                    j++
                                                ) {
                                                    if (
                                                        levelApprover.level[j].categoryBased ===
                                                        ROLE_BASED
                                                    ) {
                                                        for (const [
                                                            previousRoleIdIndex,
                                                            previousRoleId,
                                                        ] of levelApprover.level[
                                                            j
                                                        ].roleIds.entries()) {
                                                            if (
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === REJECT,
                                                                ) ||
                                                                levelApprover.level[j].role[
                                                                    previousRoleIdIndex
                                                                ].roleUsers.some(
                                                                    (item) =>
                                                                        item.status === CANCELLED,
                                                                )
                                                            ) {
                                                                approvalStatus =
                                                                    OVERWRITE_CANCELLED;
                                                            }
                                                        }
                                                    } else if (
                                                        levelApprover.level[j].categoryBased ===
                                                        USER_BASED
                                                    ) {
                                                        if (
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(REJECT),
                                                            ) ||
                                                            levelApprover.level[j].user.some(
                                                                (ele) =>
                                                                    ele.status.includes(CANCELLED),
                                                            )
                                                        ) {
                                                            approvalStatus = OVERWRITE_CANCELLED;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            approvalStatus =
                                approvalStatus === OVERWRITE_CANCELLED ? CANCELLED : APPROVED;
                        }
                    } else if (
                        currentRoleWiseStatus.some((someStatusElement) =>
                            someStatusElement.includes(REJECT),
                        ) ||
                        currentRoleWiseStatus.some((someStatusElement) =>
                            someStatusElement.includes(CANCELLED),
                        )
                    ) {
                        approvalStatus = REJECT;
                    }
                }
            } else if (
                approver.approvalConfig === ANY_ONE_USER ||
                approver.approvalConfig === ANY_ONE_IN_ANY_ROLE
            ) {
                if (currentRoleStatus.includes(PENDING) || currentRoleStatus.includes(DELAYED)) {
                    approvalStatus = `Pending with ${approver.levelName}`;
                } else if (
                    currentRoleStatus.includes(REJECT) ||
                    currentRoleStatus.includes(CANCELLED)
                ) {
                    approvalStatus = REJECT;
                }
            } else if (
                approver.approvalConfig === ALL_USERS ||
                approver.approvalConfig === ANY_ONE_IN_EACH_ROLE
            ) {
                if (
                    currentRoleWiseStatus.some((statusElement) =>
                        statusElement.includes(PENDING),
                    ) ||
                    currentRoleWiseStatus.some((statusElement) => statusElement.includes(DELAYED))
                ) {
                    approvalStatus = `Pending with ${approver.levelName}`;
                } else if (
                    currentRoleWiseStatus.some(
                        (statusElement) =>
                            statusElement.includes(CANCELLED) || statusElement.includes(REJECT),
                    )
                ) {
                    approvalStatus = REJECT;
                }
            }
        }
    }
    const updatedApprovalStatus =
        studentWarningData.approvalStatus !== CANCELLED &&
        studentWarningData.approvalStatus !== WITHDRAWN
            ? await lmsStudentModel.findByIdAndUpdate(
                  { _id: studentWarningData._id },
                  { $set: { approvalStatus } },
                  { new: true },
              )
            : { approvalStatus: studentWarningData.approvalStatus };
    levelApprover = levelApprover.level.map(({ userIds, roleIds, ...rest }) => ({
        ...rest,
    }));
    return { levelApprover, approvalStatus: updatedApprovalStatus.approvalStatus, denial };
};
const staffApprovalStatusLogics = async ({
    lmsStudentData,
    roleAssignDocs,
    roleId,
    userId,
    courseCoordinatorsId,
    digiCourse,
    scheduleStaffRole,
}) => {
    const bulkWrites = [];
    for (const lmsStudent of lmsStudentData) {
        //To get current level of approval
        let lmsCourse;

        //course-coordinator
        if (digiCourse && digiCourse.length) {
            lmsCourse = digiCourse.filter((courseElement) => {
                if (Array.isArray(courseElement.coordinators)) {
                    for (const coordinatorElement of courseElement.coordinators) {
                        if (
                            coordinatorElement._institution_calendar_id.toString() ===
                                lmsStudent._institution_calendar_id._id.toString() &&
                            coordinatorElement._user_id._id.toString() === userId.toString() &&
                            coordinatorElement.year === lmsStudent.year &&
                            coordinatorElement.level_no === lmsStudent.level &&
                            coordinatorElement.term === lmsStudent.term
                        ) {
                            return true;
                        }
                    }
                }
                return false;
            });
        }
        const levelApprover = lmsStudent.levelApprover;
        lmsStudent.isRole = false;
        const matchUserId = levelApprover.level.find((levelElement) => {
            if (levelElement.categoryBased === USER_BASED) {
                return levelElement.userIds.toString().includes(userId.toString());
            }
        });
        if (matchUserId) {
            lmsStudent.isRole = true;
        }
        const { programIds, genderSegregation, ...levels } = levelApprover;
        const lmsProgram =
            roleAssignDocs &&
            roleAssignDocs.find((roleAssignElement) => {
                return roleAssignElement.roles.find((roleElement) => {
                    return (
                        roleAssignElement._user_id._id.toString() === userId.toString() &&
                        roleElement._role_id.toString() === roleId.toString() &&
                        roleElement.program.find(
                            (roleProgram) =>
                                roleProgram._program_id.toString() ===
                                lmsStudent.programId._id.toString(),
                        )
                    );
                });
            });
        if (
            lmsCourse &&
            lmsCourse.length &&
            roleId.toString() === courseCoordinatorsId.toString()
        ) {
            lmsStudent.isRole = true;
        }

        if (lmsProgram) {
            lmsStudent.isRole = true;
        }
        if (
            scheduleStaffRole &&
            scheduleStaffRole._id &&
            roleId.toString() === scheduleStaffRole._id.toString()
        ) {
            lmsStudent.isRole = true;
        }
        lmsStudent.levelApprover = levels;

        const userLevelIndex = lmsStudent.levelApprover.level.findIndex((levelElement) => {
            if (levelElement.categoryBased === USER_BASED) {
                return levelElement.userIds.toString().includes(userId.toString());
            }
            if (roleId && roleId.length) {
                return levelElement.roleIds.toString().includes(roleId[0].toString());
            }
        });
        //To check if the current user is in last level
        lmsStudent.isLastLevel = false;
        lmsStudent.myApprovalStatus = 'No action';
        const lastLevel = levelApprover.level[levelApprover.level.length - 1];
        if (lastLevel.categoryBased === ROLE_BASED) {
            if (
                roleId &&
                roleId.length &&
                lastLevel.roleIds.some((ele) => roleId.toString().includes(ele.toString()))
            ) {
                lmsStudent.isLastLevel = true;
            }
        } else if (lastLevel.categoryBased === USER_BASED) {
            if (lastLevel.userIds.toString().includes(userId.toString())) {
                lmsStudent.isLastLevel = true;
            }
        }
        //To set current user approval status if already exists
        for (approvalFromElement of lmsStudent.approvalFrom) {
            if (
                approvalFromElement.userId &&
                approvalFromElement.roleId &&
                approvalFromElement.userId._id.toString() === userId.toString() &&
                approvalFromElement.roleId._id.toString() === roleId.toString()
            ) {
                lmsStudent.myApprovalStatus = approvalFromElement.status;
            }
        }
        //To set current user pending status (ex:pending with you or others)
        if (
            lmsStudent.approvalStatus === APPROVED ||
            lmsStudent.approvalStatus === CANCELLED ||
            lmsStudent.approvalStatus === REJECT ||
            lmsStudent.approvalStatus === WITHDRAWN
        ) {
            lmsStudent.finishedStatus = true;
            lmsStudent.staffStatus = 'Completed';
        } else {
            lmsStudent.finishedStatus = false;
            let level_name;
            let pendingLevelIndex;
            if (lmsStudent.approvalStatus === DELAYED) {
                lmsStudent.staffStatus = DELAYED;
                if (lmsStudent.isLastLevel && lmsStudent.myApprovalStatus === 'No action') {
                    lmsStudent.staffStatus = 'pending with you';
                } else if (lmsStudent.isLastLevel && lmsStudent.myApprovalStatus !== 'No action') {
                    lmsStudent.staffStatus = 'pending with others';
                } else {
                    if (
                        lmsStudent.levelApprover.level[userLevelIndex].escalateRequest &&
                        !lmsStudent.isLastLevel
                    ) {
                        lmsStudent.staffStatus = 'pending with others';
                    } else {
                        const lmsIndex = lmsStudent.levelApprover.level.length - 1;
                        let lmsStaff = false;
                        for (let i = userLevelIndex; i < lmsIndex; i++) {
                            if (levelApprover.level[i].categoryBased === ROLE_BASED) {
                                const lmsStaffStatus = levelApprover.level[i].roleIds.some(
                                    (roleElement) =>
                                        lmsStudent.approvalFrom
                                            .filter((approvalElement) => approvalElement.roleId)
                                            .map((ele) => ele.roleId._id.toString())
                                            .includes(roleElement.toString()),
                                );
                                if (lmsStaffStatus) {
                                    lmsStaff = true;
                                }
                            } else if (levelApprover.level[i].categoryBased === USER_BASED) {
                                const lmsStaffStatus = levelApprover.level[i].userIds.some(
                                    (userElement) =>
                                        lmsStudent.approvalFrom
                                            .filter((approvalElement) => !approvalElement.roleId)
                                            .map((ele) => ele.userId._id.toString())
                                            .includes(userElement.toString()),
                                );
                                if (lmsStaffStatus) {
                                    lmsStaff = true;
                                }
                            }
                        }
                        if (lmsStaff === true) {
                            lmsStudent.staffStatus = 'pending with others';
                        } else {
                            lmsStudent.staffStatus = 'pending with you';
                        }
                    }
                }
            } else {
                level_name = lmsStudent.approvalStatus
                    .toLowerCase()
                    .replace('pending with', '')
                    .trim();
                pendingLevelIndex = lmsStudent.levelApprover.level.findIndex(
                    (pendingLevelElement) =>
                        pendingLevelElement.levelName.toLowerCase().trim() ===
                        level_name.toLowerCase().trim(),
                );
                let tat = 0;
                for (const [levelIndex, levelElement] of levelApprover.level.entries()) {
                    tat += levelElement.turnAroundTime;
                    if (
                        levelElement.levelName.toLowerCase().trim() === level_name.toLowerCase() &&
                        levelElement.categoryBased === USER_BASED
                    ) {
                        if (levelElement.userIds.toString().includes(userId.toString())) {
                            if (
                                levelElement.approvalConfig === ANY_ONE_USER ||
                                levelElement.approvalConfig === ANY_ONE_IN_ANY_ROLE
                            ) {
                                lmsStudent.staffStatus = levelElement.userIds.some((userElement) =>
                                    lmsStudent.approvalFrom
                                        .filter((approvalElement) => !approvalElement.roleId)
                                        .map((ele) => ele.userId._id.toString())
                                        .includes(userElement.toString()),
                                )
                                    ? 'pending with others'
                                    : 'pending with you';
                            } else if (
                                levelElement.approvalConfig === ALL_USERS ||
                                levelElement.approvalConfig === ANY_ONE_IN_EACH_ROLE
                            ) {
                                lmsStudent.staffStatus = lmsStudent.approvalFrom
                                    .filter((approvalElement) => !approvalElement.roleId)
                                    .map((ele) => ele.userId._id.toString())
                                    .includes(userId.toString())
                                    ? 'pending with others'
                                    : 'pending with you';
                            }
                        } else {
                            lmsStudent.staffStatus = 'pending with others';
                        }
                    } else if (
                        levelElement.levelName.trim().toLowerCase().trim() ===
                            level_name.toLowerCase().trim() &&
                        levelElement.categoryBased === ROLE_BASED
                    ) {
                        if (roleId.some((item) => levelElement.roleIds.toString().includes(item))) {
                            if (
                                levelElement.approvalConfig === ANY_ONE_USER ||
                                levelElement.approvalConfig === ANY_ONE_IN_ANY_ROLE
                            ) {
                                lmsStudent.staffStatus = levelElement.roleIds.some((roleElement) =>
                                    lmsStudent.approvalFrom
                                        .filter((approvalElement) => approvalElement.roleId)
                                        .map((ele) => ele.roleId._id.toString())
                                        .includes(roleElement.toString()),
                                )
                                    ? 'pending with others'
                                    : 'pending with you';
                            } else if (
                                levelElement.approvalConfig === ALL_USERS ||
                                levelElement.approvalConfig === ANY_ONE_IN_EACH_ROLE
                            ) {
                                lmsStudent.staffStatus = lmsStudent.approvalFrom
                                    .filter((approvalElement) => approvalElement.roleId)
                                    .map((elem) => elem.roleId._id.toString())
                                    .find((roleElem) => roleId.toString().includes(roleElem))
                                    ? 'pending with others'
                                    : 'pending with you';
                            }
                        } else {
                            lmsStudent.staffStatus = 'pending with others';
                        }
                    }
                    const appliedDate = moment(lmsStudent.createdAt);
                    const now = moment(new Date());
                    const diff = now.diff(appliedDate, 'days');
                    let currentLevelTat = tat;
                    if (lmsStudent.staffStatus === 'pending with you') {
                        if (
                            Number(diff) >= Number(tat) &&
                            levelApprover.level[pendingLevelIndex].escalateRequest
                        ) {
                            lmsStudent.staffStatus = 'pending with others';
                            for (
                                let i = pendingLevelIndex + 1;
                                i <= levelApprover.level.length - 1;
                                i++
                            ) {
                                currentLevelTat += levelApprover.level[i].turnAroundTime;
                                if (
                                    Number(diff) >= Number(currentLevelTat) &&
                                    i === levelApprover.level.length - 1
                                ) {
                                    lmsStudent.approvalStatus = DELAYED;
                                    lmsStudent.delayedStatus = true;
                                    bulkWrites.push({
                                        updateMany: {
                                            filter: {
                                                _id: convertToMongoObjectId(lmsStudent._id),
                                            },
                                            update: {
                                                approvalStatus: DELAYED,
                                            },
                                        },
                                    });
                                } else {
                                    lmsStudent.approvalStatus = `Pending with ${levelApprover.level[i].levelName}`;
                                    bulkWrites.push({
                                        updateMany: {
                                            filter: {
                                                _id: convertToMongoObjectId(lmsStudent._id),
                                            },
                                            update: {
                                                approvalStatus: `Pending with ${levelApprover.level[i].levelName}`,
                                            },
                                        },
                                    });
                                }
                            }
                        } else if (
                            Number(diff) >= Number(tat) &&
                            !levelApprover.level[pendingLevelIndex].escalateRequest
                        ) {
                            lmsStudent.staffStatus = 'pending with you';
                            lmsStudent.delayedStatus = true;
                            if (pendingLevelIndex === levelApprover.level.length - 1) {
                                lmsStudent.approvalStatus = DELAYED;
                                bulkWrites.push({
                                    updateMany: {
                                        filter: {
                                            _id: convertToMongoObjectId(lmsStudent._id),
                                        },
                                        update: {
                                            approvalStatus: DELAYED,
                                        },
                                    },
                                });
                            }
                        }
                    }
                    if (lmsStudent.staffStatus === 'pending with others') {
                        if (lmsStudent.approvalStatus.toLowerCase().includes('pending with')) {
                            const level_name = lmsStudent.approvalStatus
                                .toLowerCase()
                                .replace('pending with', '')
                                .trim();
                            //To get current level of approval
                            const levelApprover = lmsStudent.levelApprover;
                            const { programIds, genderSegregation, ...levels } = levelApprover;
                            lmsStudent.levelApprover = levels;
                            pendingLevelIndex = lmsStudent.levelApprover.level.findIndex(
                                (pendingLevelElement) =>
                                    pendingLevelElement.levelName.toLowerCase().trim() ===
                                    level_name.toLowerCase().trim(),
                            );
                        }
                        for (let i = pendingLevelIndex; i <= userLevelIndex; i++) {
                            let approverTat = 0;
                            for (let j = pendingLevelIndex; j >= 0; j--) {
                                approverTat += Number(
                                    lmsStudent.levelApprover.level[pendingLevelIndex]
                                        .turnAroundTime,
                                );
                            }
                            if (
                                Number(diff) >= Number(approverTat) &&
                                lmsStudent.levelApprover.level[pendingLevelIndex].escalateRequest
                            ) {
                                //to set current pending level index
                                if (pendingLevelIndex + 1 < lmsStudent.levelApprover.level.length) {
                                    bulkWrites.push({
                                        updateMany: {
                                            filter: {
                                                _id: convertToMongoObjectId(lmsStudent._id),
                                            },
                                            update: {
                                                approvalStatus: `Pending with ${
                                                    lmsStudent.levelApprover.level[
                                                        pendingLevelIndex + 1
                                                    ].levelName
                                                }`,
                                            },
                                        },
                                    });
                                    lmsStudentData.approvalStatus = `Pending with ${
                                        lmsStudent.levelApprover.level[pendingLevelIndex + 1]
                                            .levelName
                                    }`;
                                } else {
                                    bulkWrites.push({
                                        updateMany: {
                                            filter: {
                                                _id: convertToMongoObjectId(lmsStudent._id),
                                            },
                                            update: {
                                                approvalStatus: DELAYED,
                                            },
                                        },
                                    });
                                    lmsStudentData.approvalStatus = DELAYED;
                                }
                            }
                        }
                        // set staff status for null(if !skip)
                        const currentLevelName = lmsStudent.approvalStatus
                            .toLowerCase()
                            .replace('pending with', '')
                            .trim();
                        const currentPendingLevelIndex = lmsStudent.levelApprover.level.findIndex(
                            (elem) =>
                                elem.levelName.toLowerCase().trim() ===
                                currentLevelName.toLowerCase().trim(),
                        );

                        if (
                            userLevelIndex === levelIndex &&
                            levelIndex > currentPendingLevelIndex &&
                            !lmsStudent.levelApprover.level[levelIndex].skipPreviousLevelApproval
                        ) {
                            lmsStudent.staffStatus = null;
                        }
                    }
                }
            }
        }
    }
    lmsStudentData = lmsStudentData.filter((lmsStudentElement) => {
        return lmsStudentElement.staffStatus != null;
    });
    return {
        lmsStudentData,
        bulkWrites,
    };
};
const pagination = (value) => {
    const { pageNo = 1, limit = 10 } = value;
    return {
        pageNo: (Number(pageNo) - 1) * Number(limit),
        limit: Number(limit),
    };
};
const getLmsReportFromCache = async ({
    state,
    user_id,
    _institution_calendar_id,
    _institution_id,
    from,
    to,
}) => {
    const lmsReportRedisKey = `${LMS_REPORT}:${user_id}`;
    if (state === 'fresh') {
        const lmsStudentData = await lmsStudentModel
            .find(
                {
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    createdAt: {
                        $gte: new Date(from).setUTCHours(0, 0, 0, 0),
                        $lte: new Date(to).setUTCHours(23, 59, 59, 999),
                    },
                    $or: [
                        { approvalStatus: { $regex: /^Pending/i } },
                        { approvalStatus: APPROVED },
                        { approvalStatus: REJECT },
                        { approvalStatus: WITHDRAWN },
                        { approvalStatus: DELAYED },
                    ],
                    programId: { $ne: null },
                },
                {
                    classificationType: 1,
                    studentId: 1,
                    categoryName: 1,
                    categoryId: 1,
                    dateAndTimeRange: 1,
                    noOfHours: 1,
                    programId: 1,
                    level: 1,
                    year: 1,
                    approvalStatus: 1,
                    term: 1,
                    createdAt: 1,
                },
            )
            .populate({
                path: 'studentId',
                select: { name: 1, user_id: 1 },
            })
            .populate({
                path: 'programId',
                select: { name: 1 },
            })
            .sort({ createdAt: -1 })
            .lean();
        if (!lmsStudentData.length) {
            return [];
        }
        if (lmsStudentData && lmsStudentData.length) {
            await redisClient.Client.set(lmsReportRedisKey, JSON.stringify(lmsStudentData));
            return lmsStudentData;
        }
    } else if (state === 'regular') {
        const lmsReportCache = await redisClient.Client.get(lmsReportRedisKey);
        if (!lmsReportCache) {
            const lmsStudentData = lmsStudentModel
                .find(
                    {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        createdAt: {
                            $gte: new Date(from).setUTCHours(0, 0, 0, 0),
                            $lte: new Date(to).setUTCHours(23, 59, 59, 999),
                        },
                        programId: { $ne: null },
                        $or: [
                            { approvalStatus: { $regex: /^Pending/i } },
                            { approvalStatus: APPROVED },
                            { approvalStatus: REJECT },
                            { approvalStatus: WITHDRAWN },
                        ],
                    },
                    {
                        classificationType: 1,
                        studentId: 1,
                        categoryName: 1,
                        categoryId: 1,
                        dateAndTimeRange: 1,
                        noOfHours: 1,
                        programId: 1,
                        level: 1,
                        year: 1,
                        approvalStatus: 1,
                        term: 1,
                        createdAt: 1,
                    },
                )
                .populate({
                    path: 'studentId',
                    select: { name: 1, user_id: 1 },
                })
                .populate({
                    path: 'programId',
                    select: { name: 1 },
                })
                .lean();
            if (!lmsStudentData.length) {
                return [];
            }
            if (lmsStudentData && lmsStudentData.length) {
                await redisClient.Client.set(lmsReportRedisKey, JSON.stringify(lmsStudentData));
                return lmsStudentData;
            }
        }
        return JSON.parse(lmsReportCache);
    }
};
module.exports = {
    getCourseAdminParams,
    getCourseIdsWithSessions,
    lmsSettings,
    getSessions,
    getAllCourses,
    logicFlowsForStudentApprovals,
    staffApprovalStatusLogics,
    pagination,
    getLmsReportFromCache,
};
