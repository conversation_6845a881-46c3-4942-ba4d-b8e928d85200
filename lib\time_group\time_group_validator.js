// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.time_group = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    type: Joi.string()
                        .valid(
                            constant.TIME_GROUP_BOOKING_TYPE.ONSITE,
                            constant.TIME_GROUP_BOOKING_TYPE.REMOTE,
                        )
                        .required()
                        .error((error) => {
                            return req.t('MUST_BE_ONSITE_REMOTE');
                        }),
                    start_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('START_TIME_REQUIRED');
                        }),
                    end_time: Joi.date()
                        .required()
                        .error((error) => {
                            return req.t('END_TIME_REQUIRED');
                        }),
                    gender: Joi.string()
                        .valid(constant.GENDER.MALE, constant.GENDER.FEMALE, constant.GENDER.BOTH)
                        .required()
                        .error((error) => {
                            return req.t('MUST_BE_MALE_FEMALE_BOTH');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.time_group_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
