const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { QAPC_SETTING_TAG, TAG_LEVEL, INSTITUTION } = require('../../utility/constants');

const qapcSettingTagSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        name: { type: String },
        isDefault: { type: Boolean, default: false },
        level: {
            type: String,
            enums: [TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION],
        },
        subTag: [{ type: String }],
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_SETTING_TAG, qapcSettingTagSchema);
