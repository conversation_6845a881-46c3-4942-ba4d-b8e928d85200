const express = require("express");
const route = express.Router();
const course_staff_allocation = require('./course_staff_allocation_controller');
const validator = require('./course_staff_allocation_validator');

route.put('/:_id', validator.course_staff_allocation_id, validator.course_staff_allocation_validator, course_staff_allocation.update);
route.post('/', validator.course_staff_allocation_validator, course_staff_allocation.insert);
route.delete('/', /* validator.course_staff_allocation_id, validator.course_staff_allocation_validator, */ course_staff_allocation.admin_remove);
route.get('/program_department_subject_course', course_staff_allocation.program_department_subject_course);

module.exports = route;