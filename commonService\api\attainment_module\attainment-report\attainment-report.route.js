const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    getAttainmentProgramList,
    getReportCourseList,
    getCourseAttainmentReport,
    getProgramCourseAttainmentReport,
    getCourseAttainmentReportWithFilter,
    getCourseStudentAttainmentReport,
    getProgramCourseList,
    updateProgramCourseList,
    getProgramAttainmentReportWithFilter,
    generateProgramCourseAttainmentReport,
} = require('./attainment-report.controller');

router.get('/program-list', catchAsync(getAttainmentProgramList));
router.get('/program-level-course-list', catchAsync(getReportCourseList));
router.get('/course-report', catchAsync(getCourseAttainmentReport));
router.post('/course-report-assessment', catchAsync(getCourseAttainmentReportWithFilter));
router.post('/course-student-report-assessment', catchAsync(getCourseStudentAttainmentReport));
router.get('/program-report', catchAsync(getProgramCourseAttainmentReport));
router.get('/program-course-list', catchAsync(getProgramCourseList));
router.put('/program-course-list', catchAsync(updateProgramCourseList));
router.post('/program-report-assessment', catchAsync(getProgramAttainmentReportWithFilter));
router.get(
    '/generate-program-course-assessment-report',
    catchAsync(generateProgramCourseAttainmentReport),
);
module.exports = router;
