// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
//const constant = require('../../utility/constants');

exports.program_calendar = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    _course_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('_COURSE_ID_REQUIRED');
                        }),
                    _institution_calendar_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('_INSTITUTION_CALENDAR_ID_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                    level: Joi.string()
                        .min(1)
                        .max(3)
                        .error((error) => {
                            return req.t('LEVEL_REQUIRED');
                        }),
                    start_date: Joi.date().error((error) => {
                        return req.t('START_DATE_REQUIRED');
                    }),
                    end_date: Joi.date().error((error) => {
                        return req.t('END_DATE_REQUIRED');
                    }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_PROGRAM_ID_REQUIRED');
                        }),
                    calendar: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_setting_year = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    calendar: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.calendar_get_setting = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    calendar: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_CALENDAR_ID_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_calendar_program_setting_year = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('_PROGRAM_ID_REQUIRED');
                        }),
                    year: Joi.string()
                        .min(1)
                        .max(10)
                        .required()
                        .error((error) => {
                            return req.t('YEAR_REQUIRED');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
