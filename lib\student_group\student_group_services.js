const constant = require('../utility/constants');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const course_schedule = require('mongoose').model(constant.COURSE_SCHEDULE);
const {
    createdStudentCheckExistAssignment,
    removedStudentCheckExistAssignment,
} = require('../../commonService/api/assignment_module/assignment/assignment.controller');
const { convertToMongoObjectId } = require('../utility/common');
const { bulk_write, update_push_pull_many } = require('../base/base_controller');

const { clearItem, allStudentGroupYesterday } = require('../../service/cache.service');
const { scheduleDateFormateChange } = require('../utility/common_functions');
const { usersCoursesRedisCacheRemove } = require('../../service/redisCache.service');

// Updating Student Group Flat Caching Data
exports.updateStudentGroupFlatCacheData = async () => {
    clearItem('allStudentGroup');
    await allStudentGroupYesterday('', scheduleDateFormateChange(new Date()));
};

exports.addingStudentSchedule = async ({
    _institution_id,
    institutionCalendarId,
    studentGroup,
    studentData,
    studentIds,
    deliveryGroups,
    courseId,
    batch,
    level,
    status,
}) => {
    try {
        const studentPushData = [];
        const bulkWriteData = [];
        const studentStatusData = [];
        for (stdElement of studentIds) {
            const studentIdData = studentData.find(
                (ele) => ele._student_id.toString() === stdElement.toString(),
            );
            if (studentIdData)
                studentPushData.push({
                    _id: studentIdData._student_id,
                    name: studentIdData.name,
                });
            if (
                status &&
                status.length &&
                [constant.ABSENT, constant.PRESENT, constant.EXCLUDE].includes(status)
            )
                studentStatusData.push({
                    _id: studentIdData._student_id,
                    name: studentIdData.name,
                    status,
                });
        }
        const deliveryGroupIds = [];
        for (deliveryElement of deliveryGroups) {
            const sgDeliveryData = studentGroup.session_setting.find(
                (ele) => ele.session_type === deliveryElement.session_type,
            );
            if (sgDeliveryData) {
                const deliveryGroupData = sgDeliveryData.groups.find(
                    (ele) => ele.group_no.toString() === deliveryElement.group_no.toString(),
                );
                if (deliveryGroupData) {
                    deliveryGroupIds.push(convertToMongoObjectId(deliveryGroupData._id));
                }
            }
        }
        const csQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _course_id: convertToMongoObjectId(courseId),
            'student_groups.session_group.session_group_id': { $in: deliveryGroupIds },
            isActive: true,
            isDeleted: false,
        };
        if (studentPushData.length || studentStatusData.length) {
            try {
                const totalConductedSchedulesForChangedGroups = await course_schedule
                    .find(csQuery, {
                        'students._id': 1,
                        status: 1,
                        'session._session_id': 1,
                    })
                    .lean();
                if (
                    totalConductedSchedulesForChangedGroups &&
                    totalConductedSchedulesForChangedGroups.length
                ) {
                    const existingGroupsCompletedSchedule = await course_schedule
                        .find(
                            {
                                _institution_id: convertToMongoObjectId(_institution_id),
                                _institution_calendar_id:
                                    convertToMongoObjectId(institutionCalendarId),
                                _course_id: convertToMongoObjectId(courseId),
                                status: constant.COMPLETED,
                                'student_groups.session_group.session_group_id': {
                                    $nin: deliveryGroupIds,
                                },
                                'students._id': { $in: studentIds },
                                isActive: true,
                                isDeleted: false,
                            },
                            { 'session._session_id': 1, 'students._id': 1 },
                        )
                        .lean();
                    for (const totalConductedScheduleELement of totalConductedSchedulesForChangedGroups) {
                        const { status, students, _id, session } = totalConductedScheduleELement;
                        const existingStudentIdsForChangedGroup = students.map((studentsELement) =>
                            studentsELement._id.toString(),
                        );
                        if (
                            (status === constant.PENDING || status === constant.MISSED) &&
                            studentPushData.length
                        ) {
                            const newStudentsWithoutStatus = studentPushData.filter(
                                ({ _id }) =>
                                    !existingStudentIdsForChangedGroup.includes(_id.toString()),
                            );
                            if (newStudentsWithoutStatus && newStudentsWithoutStatus.length) {
                                bulkWriteData.push({
                                    updateMany: {
                                        filter: { _id },
                                        update: { $push: { students: newStudentsWithoutStatus } },
                                    },
                                });
                            }
                        } else if (
                            (status === constant.COMPLETED || status === constant.ONGOING) &&
                            studentStatusData.length
                        ) {
                            const existingGroupsCompletedScheduleForSameSession =
                                existingGroupsCompletedSchedule.find(
                                    (existingStudentScheduleElement) =>
                                        existingStudentScheduleElement.session._session_id.toString() ===
                                        session._session_id.toString(),
                                );
                            const checkStudentAlreadyAttendedThisSession = ({
                                currentStudentId,
                            }) => {
                                if (!existingGroupsCompletedScheduleForSameSession) {
                                    return false;
                                }
                                return existingGroupsCompletedScheduleForSameSession.students.find(
                                    (studentsELement) =>
                                        studentsELement._id.toString() === currentStudentId,
                                );
                            };
                            const newStudentsWithStatus = studentStatusData.filter(({ _id }) => {
                                const currentStudentId = _id.toString();
                                return (
                                    !existingStudentIdsForChangedGroup.includes(currentStudentId) &&
                                    !checkStudentAlreadyAttendedThisSession({ currentStudentId })
                                );
                            });
                            if (newStudentsWithStatus && newStudentsWithStatus.length) {
                                bulkWriteData.push({
                                    updateMany: {
                                        filter: { _id },
                                        update: { $push: { students: newStudentsWithStatus } },
                                    },
                                });
                            }
                        }
                    }
                }
            } catch (error) {
                console.log(`Error in adding students in existing schedules ${error.message}`);
            }
        }
        if (bulkWriteData.length > 0) console.log(await bulk_write(course_schedule, bulkWriteData));
        const checkStudentFromExistAssignment = await createdStudentCheckExistAssignment({
            batch,
            level,
            courseId,
            studentIds,
            _institution_id,
            institutionCalendarId,
        });
        await usersCoursesRedisCacheRemove({ userIds: studentIds, institutionCalendarId });
    } catch (error) {
        console.log(error);
        throw error;
    }
};
exports.removeStudentSchedule = async (
    _institution_id,
    institutionCalendarId,
    term,
    level_no,
    _course_id,
    studentIds,
) => {
    try {
        const csQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            term,
            level_no,
            // _course_id: convertToMongoObjectId(_course_id),
            $or: [
                {
                    status: constant.PENDING,
                },
                {
                    status: constant.MISSED,
                },
            ],
            isActive: true,
            isDeleted: false,
        };
        if (_course_id && _course_id.length === 24)
            csQuery._course_id = convertToMongoObjectId(_course_id);
        const csCondition = {
            $pull: {
                students: { _id: { $in: studentIds } },
            },
        };
        console.log(await update_push_pull_many(course_schedule, csQuery, csCondition));
        await removedStudentCheckExistAssignment({
            _institution_id,
            institutionCalendarId,
            term,
            level_no,
            _course_id,
            studentIds,
        });
        await usersCoursesRedisCacheRemove({ userIds: studentIds, institutionCalendarId });
    } catch (error) {
        console.log(error);
        throw error;
    }
};
