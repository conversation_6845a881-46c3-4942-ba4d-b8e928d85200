const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const { validate } = require('../../middleware/validation');
const {
    applyOnDuty,
    updateStatus,
    listData,
    getAttendanceSheets,
    previouslyLeaveStatus,
    listSchedule,
    updateAgree,
    listApprovalLevelWiseStatus,
    studentListForStaffApprover,
    staffApproval,
    revokeApproval,
    editStaffApproval,
    updateTurnAroundTime,
    getLeaveManagement,
    getApplicationStatus,
    getProgramReport,
    getStudentApplicationStatus,
    programYearWiseReport,
    getAttachemntSignedUrl,
    getGlobalSessionSettings,
    sessionWiseLeaveApply,
    sessionWiseLeaveListForApprover,
    sessionWiseStaffApproval,
    sessionWiseLeaveHistoryList,
} = require('./lmsStudent.controller');
const {
    lmsApplyValidator,
    updateStatusValidator,
    listDataValidator,
    previouslyLeaveStatusValidator,
    listScheduleValidator,
    updateAgreeValidator,
    staffApprovalValidator,
    revokeApprovalValidator,
    editStaffApprovalValidator,
    getLeaveManagementValidator,
    getApplicationStatusValidator,
    getStudentApplicationValidator,
    programYearWiseReportValidator,
    sessionWiseLeaveApplyValidator,
    sessionWiseLeaveListForApproverValidator,
    sessionWiseStaffApprovalValidator,
    sessionWiseLeaveHistoryListValidator,
} = require('./lmsStudent.validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.post(
    '/apply',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validate(lmsApplyValidator),
    catchAsync(applyOnDuty),
);
router.put(
    '/statusUpdate',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validate(updateStatusValidator),
    catchAsync(updateStatus),
);
router.get(
    '/listData',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validate(listDataValidator),
    catchAsync(listData),
);
router.get('/getAttendanceSheet', catchAsync(getAttendanceSheets));
router.get(
    '/previouslyLeaveStatus',
    validate(previouslyLeaveStatusValidator),
    catchAsync(previouslyLeaveStatus),
);
router.get(
    '/listSchedule',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT, defaultPolicy.DC_STAFF])],
    validate(listScheduleValidator),
    catchAsync(listSchedule),
);
router.put(
    '/updateAgree',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validate(updateAgreeValidator),
    catchAsync(updateAgree),
);
router.get(
    '/listApprovalLevelWiseStatus',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(listApprovalLevelWiseStatus),
);
router.put(
    '/studentListForStaffApprover',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(studentListForStaffApprover),
);
router.put(
    '/staffApproval',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT, defaultPolicy.DC_STAFF])],
    validate(staffApprovalValidator),
    catchAsync(staffApproval),
);
router.delete(
    '/revokeApproval',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate(revokeApprovalValidator),
    catchAsync(revokeApproval),
);
router.put(
    '/editStaffApproval',
    validate(editStaffApprovalValidator),
    catchAsync(editStaffApproval),
);
router.put('/updateTurnAroundTime', catchAsync(updateTurnAroundTime));
router.get(
    '/getGlobalSessionSettings',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(getGlobalSessionSettings),
);
//lms report
router.get(
    '/report/getLeaveManagement',
    getLeaveManagementValidator,
    catchAsync(getLeaveManagement),
);
router.get(
    '/report/getApplicationStatus',
    getApplicationStatusValidator,
    catchAsync(getApplicationStatus),
);
router.get('/report/getProgramReport', getLeaveManagementValidator, catchAsync(getProgramReport));
router.get(
    '/report/getStudentApplicationStatus',
    getStudentApplicationValidator,
    catchAsync(getStudentApplicationStatus),
);
router.get(
    '/report/programYearWiseReport',
    programYearWiseReportValidator,
    catchAsync(programYearWiseReport),
);
router.get('/getAttachemntSignedUrl/:id', catchAsync(getAttachemntSignedUrl));

//SAAM - revamp
router.post(
    '/sessionWiseLeaveApply',
    validate(sessionWiseLeaveApplyValidator),
    catchAsync(sessionWiseLeaveApply),
);
router.get(
    '/sessionWiseLeaveListForApprover',
    validate(sessionWiseLeaveListForApproverValidator),
    catchAsync(sessionWiseLeaveListForApprover),
);
router.put(
    '/sessionWiseStaffApproval',
    validate(sessionWiseStaffApprovalValidator),
    catchAsync(sessionWiseStaffApproval),
);
router.get(
    '/sessionWiseLeaveHistoryList',
    validate(sessionWiseLeaveHistoryListValidator),
    catchAsync(sessionWiseLeaveHistoryList),
);
module.exports = router;
