const { S3 } = require('aws-sdk');

const {
    AWS_ACCESS_KEY,
    AWS_SECRET_KEY,
    DIGIVAL_CLOUD_PROVIDER,
    OCI_REGION,
    OCI_ACCESS_KEY_ID,
    OCI_SECRET_ACCESS_KEY,
    OCI_AWS_S3_API,
} = require('../lib/utility/util_keys');

const s3 = new S3({
    accessKeyId: AWS_ACCESS_KEY,
    secretAccessKey: AWS_SECRET_KEY,
    httpOptions: { timeout: 3600000, connectTimeout: 3600000 },
});

const s3Oci = new S3({
    region: OCI_REGION,
    accessKeyId: OCI_ACCESS_KEY_ID,
    secretAccessKey: OCI_SECRET_ACCESS_KEY,
    endpoint: OCI_AWS_S3_API,
    s3ForcePathStyle: true,
    signatureVersion: 'v4',
});
const s3Config = DIGIVAL_CLOUD_PROVIDER === 'OCI' ? s3Oci : s3;

const getS3Object = ({ bucket, key }) =>
    s3Config
        .getObject({
            Bucket: bucket,
            Key: key,
        })
        .promise();

const putS3Object = ({ bucket, key, body }) =>
    s3Config
        .putObject({
            Bucket: bucket,
            Key: key,
            Body: body,
        })
        .promise();

const deleteS3Object = ({ bucket, key }) =>
    s3Config
        .deleteObject({
            Bucket: bucket,
            Key: key,
        })
        .promise();

const getS3SignedUrl = ({ bucket, key, expiresInSec = 600 /* 10 min */ /* 28800 /* 8 hours */ }) =>
    s3Config.getSignedUrlPromise('getObject', {
        Bucket: bucket,
        Key: key,
        Expires: expiresInSec,
    });

const getSizeOfS3Url = ({ bucket, key }) => {
    return s3Config
        .headObject({ Key: key, Bucket: bucket })
        .promise()
        .then((res) => res.ContentLength);
};

const getS3UnsignedUrl = ({ bucket, key, expiresInSec = 600 /* 10 min */ }) =>
    s3Config.getSignedUrlPromise('getObject', {
        Bucket: bucket,
        Key: key,
        Expires: expiresInSec,
    });

module.exports = {
    s3,
    getS3Object,
    putS3Object,
    deleteS3Object,
    getS3SignedUrl,
    getSizeOfS3Url,
    getS3UnsignedUrl,
};
