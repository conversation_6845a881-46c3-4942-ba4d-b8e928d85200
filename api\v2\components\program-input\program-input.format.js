const { getPresignedUrlsForPrograms } = require('./program-input.util');

module.exports = {
    program: async (programs, instituteId) => {
        const formattedPrograms = [];
        for (const program of programs) {
            const formattedProgram = await getPresignedUrlsForPrograms(program, instituteId);
            formattedPrograms.push(formattedProgram);
        }
        return formattedPrograms;
    },
};
