const formInitiatorSchema = require('../formInitiator/qapcFormInitiator.model');
const qapcGuideResourceSchema = require('../formInitiator/formGuideResources.model');
const qapcUserDashboardSettingsSchema = require('./qapcDashboard.model');
const qapcIncorporateSectionSchema = require('../formInitiator/qapcIncorporateSection.model');
const { convertToMongoObjectId } = require('../../utility/common');
const { userFromInitiatedList } = require('../formInitiator/formInitiator.service');
const {
    PUBLISHED,
    DRAFT,
    RE_SUBMIT,
    IN_REVIEW,
    REJECTED,
    Q360: {
        CATEGORY,
        DELETE_CATEGORY,
        GRAPH,
        PROGRAM,
        COURSE,
        QA_DASHBOARD,
        CATEGORY_LEVEL,
        PROGRAM_LEVEL,
        INSTITUTION,
        INCORPORATED,
        INSTITUTION_LEVEL,
    },
} = require('../../utility/constants');

const checkDuplicateCategorySettings = async ({ settingsQuery, filteredItems = {} }) => {
    const dashboardSettings = await qapcUserDashboardSettingsSchema
        .findOne(
            {
                ...settingsQuery,
            },
            {
                _id: 1,
                ...filteredItems,
            },
        )
        .lean();
    return dashboardSettings;
};

exports.createCategoryItems = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { selectedItems, institutionCalendarId } = body;

        const settingsQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            userId: convertToMongoObjectId(user_id),
            roleId: convertToMongoObjectId(role_id),
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        };

        // check the Duplicate
        const duplicateCategorySettings = await checkDuplicateCategorySettings({ settingsQuery });
        if (duplicateCategorySettings) {
            return { statusCode: 400, message: 'CATEGORY_NAME_ALREADY_EXISTS' };
        }
        const createCategorySettings = await qapcUserDashboardSettingsSchema.create({
            ...settingsQuery,
            selectedItems: [selectedItems],
        });
        if (!createCategorySettings) return { statusCode: 400, message: 'NOT_SAVED' };

        return {
            statusCode: 200,
            message: 'SAVED_SUCCESSFULLY',
            data: createCategorySettings,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getDashboardCategorySettings = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { institutionCalendarId } = query;
        const settingsQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            userId: convertToMongoObjectId(user_id),
            roleId: convertToMongoObjectId(role_id),
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        };
        const qapcDashboardSettingsData = await qapcUserDashboardSettingsSchema
            .findOne(
                {
                    ...settingsQuery,
                },
                { colorCodes: 1, levels: 1, selectedItems: 1 },
            )
            .lean();

        if (!qapcDashboardSettingsData)
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };

        if (qapcDashboardSettingsData && qapcDashboardSettingsData.selectedItems) {
            qapcDashboardSettingsData.selectedItems.sort(
                (a, b) => new Date(b.updatedAt) - new Date(a.updatedAt),
            );
        }

        return { statusCode: 200, message: 'DATA_RETRIEVED', data: qapcDashboardSettingsData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateCategoryItems = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { selectedItems, institutionCalendarId, categoryId, type, colorCodes, levels } = body;

        const settingsQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            userId: convertToMongoObjectId(user_id),
            roleId: convertToMongoObjectId(role_id),
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
            ...(![DELETE_CATEGORY].includes(type) && { _id: convertToMongoObjectId(categoryId) }),
        };

        const existingCategorySettings = await checkDuplicateCategorySettings({
            settingsQuery,
            filteredItems: { selectedItems: 1 },
        });
        if (!existingCategorySettings) {
            return { statusCode: 400, message: 'SETTING_NOT_FOUND' };
        }

        switch (type) {
            case CATEGORY: {
                const existingSelectedItems = existingCategorySettings.selectedItems;
                const findCategory = existingSelectedItems.find(
                    (categoryElement) =>
                        categoryElement.categoryId.toString() ===
                        selectedItems.categoryId.toString(),
                );
                if (findCategory) {
                    selectedItems._id = findCategory._id;
                }
                const updatedSelectedItems = [
                    ...existingSelectedItems.filter(
                        (categoryElement) =>
                            categoryElement.categoryId.toString() !==
                            selectedItems.categoryId.toString(),
                    ),
                    selectedItems,
                ];

                const updateCategorySettings = await qapcUserDashboardSettingsSchema.updateOne(
                    {
                        ...settingsQuery,
                    },
                    {
                        $set: {
                            ...(selectedItems && { selectedItems: updatedSelectedItems }),
                        },
                    },
                );
                return {
                    statusCode: 200,
                    message: 'UPDATED_SUCCESSFULLY',
                    data: updateCategorySettings,
                };
            }
            case DELETE_CATEGORY: {
                const existingSelectedItems = existingCategorySettings.selectedItems;
                const findCategory = existingSelectedItems.find(
                    (categoryElement) =>
                        categoryElement.categoryId.toString() ===
                        selectedItems.categoryId.toString(),
                );
                if (findCategory) {
                    selectedItems._id = findCategory._id;
                }
                const updatedSelectedItems = [
                    ...existingSelectedItems.filter(
                        (categoryElement) =>
                            categoryElement.categoryId.toString() !==
                            selectedItems.categoryId.toString(),
                    ),
                ];

                const updateCategorySettings = await qapcUserDashboardSettingsSchema.updateOne(
                    {
                        ...settingsQuery,
                    },
                    {
                        $set: {
                            ...(selectedItems && { selectedItems: updatedSelectedItems }),
                        },
                    },
                );
                return {
                    statusCode: 200,
                    message: 'DELETED_SUCCESSFULLY',
                    data: updateCategorySettings,
                };
            }
            case GRAPH: {
                const updateCategorySettings = await qapcUserDashboardSettingsSchema.updateOne(
                    {
                        ...settingsQuery,
                    },
                    {
                        $set: {
                            colorCodes,
                            levels,
                        },
                    },
                );
                return {
                    statusCode: 200,
                    message: 'UPDATED_SUCCESSFULLY',
                    data: updateCategorySettings,
                };
            }
            default:
                return {
                    statusCode: 200,
                    message: 'SAVED_SUCCESSFULLY',
                    data: existingCategorySettings,
                };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCountOfToDoMissed = ({
    filteredFormGroupData,
    startMonth,
    endMonth,
    institutionCalendarId,
    currentMonth,
}) => {
    let totalTodo = 0;
    let totalCompletedTodo = 0;
    let totalMissedExpired = 0;
    let actualPendingTodo = 0;
    const groupDataIds = [];
    const pendingToDoArray = [];
    const missedArray = [];

    filteredFormGroupData
        .filter((formElement) =>
            formElement.RPSelectedCalender.map((calendarElement) =>
                calendarElement.toString(),
            ).includes(institutionCalendarId.toString()),
        )
        .forEach((formElement) => {
            if (
                (formElement.startMonth === 0 && formElement.startMonth === 0) ||
                (formElement.startMonth >= startMonth && formElement.endMonth <= endMonth)
            ) {
                groupDataIds.push(formElement._id);
                totalTodo += formElement.minimum;
                const currentCompletedTodo =
                    formElement.createdDocument && formElement.createdDocument.length
                        ? formElement.createdDocument
                              .filter(
                                  (documentElement) =>
                                      documentElement.institutionCalenderId.toString() ===
                                      institutionCalendarId.toString(),
                              )
                              .reduce((acc, filteredCount) => {
                                  return acc + filteredCount.minimum;
                              }, 0)
                        : 0;
                totalCompletedTodo += currentCompletedTodo;

                if (formElement.createdDocument && formElement.createdDocument.length) {
                    const createdDocumentData = formElement.createdDocument.find(
                        (documentElement) =>
                            documentElement.institutionCalenderId.toString() ===
                            institutionCalendarId.toString(),
                    );
                    if (createdDocumentData) {
                        const minimumValue = createdDocumentData.minimum;
                        if (formElement.minimum > minimumValue) {
                            actualPendingTodo += 1;
                            pendingToDoArray.push(formElement._id);
                        }
                    }
                } else {
                    actualPendingTodo += 1;
                    pendingToDoArray.push(formElement._id);
                }
                const hasEndMonthSmall = formElement.startMonth > formElement.endMonth;
                const formEndMonth = formElement.endMonth === 0 ? 12 : formElement.endMonth;
                if (!hasEndMonthSmall && formEndMonth < currentMonth) {
                    totalMissedExpired += formElement.minimum - currentCompletedTodo;
                    missedArray.push(formElement._id);
                    if (pendingToDoArray.includes(formElement._id)) {
                        actualPendingTodo -= 1;
                        const findPendingElement = pendingToDoArray.indexOf(formElement._id);
                        if (findPendingElement > -1) {
                            pendingToDoArray.splice(findPendingElement, 1);
                        }
                    }
                }
            }
        });

    const pendingToDo = totalTodo - totalCompletedTodo - totalMissedExpired;
    const pendingToDoCount = pendingToDo > -1 ? pendingToDo : 0;
    return {
        totalTodo,
        totalCompletedTodo,
        totalPendingTodo: pendingToDoCount,
        totalMissedExpired,
        groupDataIds,
        pendingToDoArray,
        missedArray,
        actualPendingTodo,
    };
};

const getLevelData = ({
    levelElement,
    categoryData,
    institutionCalendarId,
    startMonth,
    endMonth,
    currentMonth,
}) => {
    const { levelName, levelData } = levelElement;
    const categoryFormGroupData = categoryData.categoryFormGroupData;
    const categoryFormCourseData = categoryData.categoryFormCourseData;
    switch (levelName) {
        case CATEGORY: {
            return levelData.forEach((levelDataElement) => {
                const { categoryId, formId } = levelDataElement;
                const categoryFormCourseId = categoryFormCourseData
                    .filter(
                        (courseElement) =>
                            courseElement.categoryId.toString() === categoryId.toString() &&
                            courseElement.categoryFormId.toString() === formId.toString(),
                    )
                    .map((courseElement) => courseElement._id.toString());
                const filteredFormGroupData = categoryFormGroupData.filter((groupElement) => {
                    return (
                        groupElement.categoryId.toString() === categoryId.toString() &&
                        groupElement.categoryFormId.toString() === formId.toString() &&
                        categoryFormCourseId.includes(
                            groupElement.categoryFormCourseId.toString(),
                        ) &&
                        (groupElement.academicYear === 'every'
                            ? groupElement.RPSelectedCalender.includes(
                                  institutionCalendarId.toString(),
                              )
                            : true)
                    );
                });
                let countOfToDoMissed = {};
                if (filteredFormGroupData && filteredFormGroupData.length) {
                    countOfToDoMissed = getCountOfToDoMissed({
                        filteredFormGroupData,
                        startMonth,
                        endMonth,
                        institutionCalendarId,
                        currentMonth,
                    });
                }
                levelDataElement.counts = countOfToDoMissed;
            });
        }
        case PROGRAM: {
            return levelData.forEach((levelDataElement) => {
                const { categoryId, formId, programId } = levelDataElement;
                const categoryFormCourseId = categoryFormCourseData
                    .filter(
                        (courseElement) =>
                            courseElement.categoryId.toString() === categoryId.toString() &&
                            courseElement.categoryFormId.toString() === formId.toString() &&
                            courseElement.programId.toString() === programId.toString(),
                    )
                    .map((courseElement) => courseElement._id.toString());

                const filteredFormGroupData = categoryFormGroupData.filter((groupElement) => {
                    return (
                        groupElement.categoryId.toString() === categoryId.toString() &&
                        groupElement.categoryFormId.toString() === formId.toString() &&
                        categoryFormCourseId.includes(
                            groupElement.categoryFormCourseId.toString(),
                        ) &&
                        (groupElement.academicYear === 'every'
                            ? groupElement.RPSelectedCalender.includes(
                                  institutionCalendarId.toString(),
                              )
                            : true)
                    );
                });
                let countOfToDoMissed = {};
                if (filteredFormGroupData && filteredFormGroupData.length) {
                    countOfToDoMissed = getCountOfToDoMissed({
                        filteredFormGroupData,
                        startMonth,
                        endMonth,
                        institutionCalendarId,
                        currentMonth,
                    });
                }
                levelDataElement.counts = countOfToDoMissed;
            });
        }
        case COURSE: {
            return levelData.forEach((levelDataElement) => {
                const { categoryId, formId, programId, courseId } = levelDataElement;
                const categoryFormCourseId = categoryFormCourseData
                    .filter(
                        (courseElement) =>
                            courseElement.categoryId.toString() === categoryId.toString() &&
                            courseElement.categoryFormId.toString() === formId.toString() &&
                            courseElement.programId.toString() === programId.toString() &&
                            courseElement.courseId.toString() === courseId.toString(),
                    )
                    .map((courseElement) => courseElement._id.toString());
                const filteredFormGroupData = categoryFormGroupData.filter((groupElement) => {
                    return (
                        groupElement.categoryId.toString() === categoryId.toString() &&
                        groupElement.categoryFormId.toString() === formId.toString() &&
                        categoryFormCourseId.includes(
                            groupElement.categoryFormCourseId.toString(),
                        ) &&
                        (groupElement.academicYear === 'every'
                            ? groupElement.RPSelectedCalender.includes(
                                  institutionCalendarId.toString(),
                              )
                            : true)
                    );
                });
                let countOfToDoMissed = {};
                if (filteredFormGroupData && filteredFormGroupData.length) {
                    countOfToDoMissed = getCountOfToDoMissed({
                        filteredFormGroupData,
                        startMonth,
                        endMonth,
                        institutionCalendarId,
                        currentMonth,
                    });
                }
                levelDataElement.counts = countOfToDoMissed;
            });
        }
        case INSTITUTION: {
            return levelData.forEach((levelDataElement) => {
                const { categoryId, formId, institutionId } = levelDataElement;
                const categoryFormCourseId = categoryFormCourseData
                    .filter(
                        (courseElement) =>
                            courseElement.categoryId.toString() === categoryId.toString() &&
                            courseElement.categoryFormId.toString() === formId.toString() &&
                            courseElement.assignedInstitutionId.toString() ===
                                institutionId.toString(),
                    )
                    .map((courseElement) => courseElement._id.toString());

                const filteredFormGroupData = categoryFormGroupData.filter((groupElement) => {
                    return (
                        groupElement.categoryId.toString() === categoryId.toString() &&
                        groupElement.categoryFormId.toString() === formId.toString() &&
                        categoryFormCourseId.includes(
                            groupElement.categoryFormCourseId.toString(),
                        ) &&
                        (groupElement.academicYear === 'every'
                            ? groupElement.RPSelectedCalender.includes(
                                  institutionCalendarId.toString(),
                              )
                            : true)
                    );
                });
                let countOfToDoMissed = {};
                if (filteredFormGroupData && filteredFormGroupData.length) {
                    countOfToDoMissed = getCountOfToDoMissed({
                        filteredFormGroupData,
                        startMonth,
                        endMonth,
                        institutionCalendarId,
                        currentMonth,
                    });
                }
                levelDataElement.counts = countOfToDoMissed;
            });
        }
        default:
            return levelData.forEach((levelDataElement) => {
                levelDataElement.counts = {};
            });
    }
};

const getInitiatorStatusCount = ({ status, groupDataIds, qapcFormInitiatorData }) => {
    const filteredGroupData = qapcFormInitiatorData
        .filter((statusElement) =>
            groupDataIds
                .map((groupElement) => groupElement.toString())
                .includes(statusElement.categoryFormGroupId.toString()),
        )
        .filter((statusElement) => statusElement.status === status);

    const categoryFormGroupId = filteredGroupData.map((groupElement) => {
        return {
            _id: groupElement._id.toString(),
            categoryFormGroupId: groupElement.categoryFormGroupId.toString(),
        };
    });

    return categoryFormGroupId;
    // const categoryFormGroupId = filteredGroupData.map((groupElement) =>
    //     groupElement.categoryFormGroupId.toString(),
    // );
    // const uniqueCategoryFormGroupId = [...new Set(categoryFormGroupId)];
    // return uniqueCategoryFormGroupId;
};

const getApproverLevels = ({ groupDataIds, qapcFormInitiatorData }) => {
    const filteredGroupData = qapcFormInitiatorData
        .filter((statusElement) =>
            groupDataIds
                .map((groupElement) => groupElement.toString())
                .includes(statusElement.categoryFormGroupId.toString()),
        )
        .filter((statusElement) => statusElement.status === IN_REVIEW);

    const updatedApprovalLevels = filteredGroupData.map((groupElement) => {
        return {
            _id: groupElement._id,
            categoryFromId: groupElement.categoryFormId._id,
            approvalLevel: groupElement.categoryFormId.approvalLevel.length,
            approverList: groupElement.approverList.map((approvalElement) => {
                return {
                    level: approvalElement.level,
                    levelStatus: approvalElement.levelStatus,
                };
            }),
        };
    });
    return updatedApprovalLevels;
};

const getInitiatorStatus = ({ levelElement, qapcFormInitiatorData }) => {
    return levelElement.levelData.forEach((levelDataElement) => {
        const groupDataIds = levelDataElement.counts.groupDataIds;
        if (!groupDataIds) {
            return levelDataElement.counts;
        }
        const draftStatus = getInitiatorStatusCount({
            status: DRAFT,
            groupDataIds,
            qapcFormInitiatorData,
        });
        const reviewStatus = getInitiatorStatusCount({
            status: IN_REVIEW,
            groupDataIds,
            qapcFormInitiatorData,
        });
        const rejectedStatus = getInitiatorStatusCount({
            status: REJECTED,
            groupDataIds,
            qapcFormInitiatorData,
        });
        const publishedStatus = getInitiatorStatusCount({
            status: PUBLISHED,
            groupDataIds,
            qapcFormInitiatorData,
        });
        const resubmitStatus = getInitiatorStatusCount({
            status: RE_SUBMIT,
            groupDataIds,
            qapcFormInitiatorData,
        });

        const countOfToDoMissed = {
            totalDraft: draftStatus.length,
            draftArray: draftStatus,
            totalReview: reviewStatus.length,
            reviewArray: reviewStatus,
            totalRejected: rejectedStatus.length,
            rejectedArray: rejectedStatus,
            totalPublished: publishedStatus.length,
            publishedArray: publishedStatus,
            totalResubmit: resubmitStatus.length,
            reSubmitArray: resubmitStatus,
            ...levelDataElement.counts,
        };
        levelDataElement.counts = countOfToDoMissed;
        levelDataElement.approverLevels = getApproverLevels({
            groupDataIds,
            qapcFormInitiatorData,
        });
    });
};

const getInCorporateStatusCount = ({ status, formInitiatorIds, qapcGuideResource }) => {
    if (formInitiatorIds && formInitiatorIds.length) {
        const incorporateFrom = qapcGuideResource.filter((resourceElement) =>
            resourceElement.formInitiatorIds.some((initiatorElement) =>
                formInitiatorIds.includes(initiatorElement.toString()),
            ),
        );
        if (incorporateFrom && incorporateFrom.length) {
            const incorporateData = incorporateFrom.flatMap((incorporateElement) =>
                incorporateElement.incorporateFrom.filter(
                    (incorporateElement) => incorporateElement.isLike === status,
                ),
            );
            const formInitiatorIds = incorporateFrom
                .filter((incorporateElement) =>
                    incorporateElement.incorporateFrom.some(
                        (incorporate) => incorporate.isLike === status,
                    ),
                )
                .flatMap((incorporateElement) => incorporateElement.formInitiatorIds);
            return { count: incorporateData.length, formInitiatorIds };
        }
    }
    return {
        count: 0,
        formInitiatorIds: [],
    };
};

const getInCorporateStatus = ({ levelElement, qapcGuideResource, qapcFormInitiatorData }) => {
    return levelElement.levelData.forEach((levelDataElement) => {
        const groupDataIds = levelDataElement.counts.groupDataIds;
        const formInitiatorIds = qapcFormInitiatorData
            .filter((initiatorElement) =>
                groupDataIds
                    .map((groupElement) => groupElement.toString())
                    .includes(initiatorElement.categoryFormGroupId.toString()),
            )
            .map((initiatorElement) => initiatorElement._id.toString());
        const incorporatedCount = getInCorporateStatusCount({
            status: true,
            formInitiatorIds,
            qapcGuideResource,
        });
        const notIncorporatedCount = getInCorporateStatusCount({
            status: false,
            formInitiatorIds,
            qapcGuideResource,
        });
        const countOfToDoMissed = {
            totalIncorporate: incorporatedCount.count,
            totalNotIncorporatedCount: notIncorporatedCount.count,
            incorporateFormInitiatorIds: incorporatedCount.formInitiatorIds,
            notIncorporateFormInitiatorIds: notIncorporatedCount.formInitiatorIds,
            ...levelDataElement.counts,
        };
        levelDataElement.counts = countOfToDoMissed;
    });
};

const getCategoryFormGroupId = (groupIdArray) => {
    if (!groupIdArray) return [];
    return groupIdArray.map((groupElement) => groupElement.categoryFormGroupId.toString());
};

const getTodoMissedStatusExclude = ({
    levelElement,
    filteredDuplicateGroupIds,
    enableTagFilter,
}) => {
    return levelElement.levelData.forEach((levelDataElement) => {
        const levelCounts = levelDataElement.counts;
        const groupDataIdsArray = levelCounts.groupDataIds;
        if (groupDataIdsArray && groupDataIdsArray.length) {
            const draftArray = getCategoryFormGroupId(levelCounts.draftArray);
            const rejectedArray = getCategoryFormGroupId(levelCounts.rejectedArray);
            const checkExcludeArray = [...draftArray];

            if (checkExcludeArray && checkExcludeArray.length) {
                const missedArray = levelCounts.missedArray.filter(
                    (missedArrayElement) =>
                        !checkExcludeArray.includes(missedArrayElement.toString()),
                );
                levelDataElement.counts.missedArray = missedArray;
                levelDataElement.counts.totalMissedExpired = missedArray.length;
            }

            if (enableTagFilter && filteredDuplicateGroupIds && filteredDuplicateGroupIds.length) {
                const filterGroupIds = groupDataIdsArray.filter((groupIdElement) =>
                    filteredDuplicateGroupIds
                        .map((filteredGroupIdElement) => filteredGroupIdElement.toString())
                        .includes(groupIdElement.toString()),
                );
                levelDataElement.counts.groupDataIds = filterGroupIds;
                levelDataElement.counts.totalCompletedTodo =
                    filterGroupIds.length - levelDataElement.counts.totalCompletedTodo;
            }

            if (rejectedArray && rejectedArray.length) {
                levelDataElement.counts.totalTodo =
                    rejectedArray.length + levelDataElement.counts.totalTodo;
            }

            return levelDataElement;
        }
        return levelDataElement;
    });
};

const getFormInitiatorGroupIds = ({ formInitiatorIds, qapcFormInitiatorData }) => {
    if (formInitiatorIds) {
        return formInitiatorIds
            .map((initiatorElement) => {
                const findGroupIds = qapcFormInitiatorData.find(
                    (formElement) => formElement._id.toString() === initiatorElement.toString(),
                );
                return findGroupIds ? findGroupIds.categoryFormGroupId.toString() : '';
            })
            .filter((initiatorElement) => initiatorElement !== '');
    }
    return [];
};

const mergeUniqueFormGroupIds = (arrElement1, arrElement2) => [
    ...new Set([...arrElement1, ...arrElement2]),
];

const transformTagsList = (tagsList) => {
    if (!tagsList.length) return [];
    const groupedTagData = {};
    tagsList.forEach((tagElement) => {
        const { name, subTags, formInitiatorIds, categoryFormGroupIds, isDefault } = tagElement;
        if (!groupedTagData[name]) {
            groupedTagData[name] = {
                name,
                subTags: [],
                formInitiatorIds: [],
                categoryFormGroupIds: [],
                isDefault,
            };
        }
        groupedTagData[name].formInitiatorIds = mergeUniqueFormGroupIds(
            groupedTagData[name].formInitiatorIds,
            formInitiatorIds,
        );
        groupedTagData[name].categoryFormGroupIds = mergeUniqueFormGroupIds(
            groupedTagData[name].categoryFormGroupIds,
            categoryFormGroupIds,
        );

        subTags.forEach((subTagElement) => {
            const existingSubTag = groupedTagData[name].subTags.find(
                (subTagsNameElement) => subTagsNameElement.name === subTagElement.name,
            );

            if (existingSubTag) {
                existingSubTag.formInitiatorIds = mergeUniqueFormGroupIds(
                    existingSubTag.formInitiatorIds,
                    subTagElement.formInitiatorIds,
                );
                existingSubTag.categoryFormGroupIds = mergeUniqueFormGroupIds(
                    existingSubTag.categoryFormGroupIds,
                    subTagElement.categoryFormGroupIds,
                );
            } else {
                groupedTagData[name].subTags.push({ ...subTagElement });
            }
        });
    });
    return Object.values(groupedTagData);
};

exports.getQADashboard = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { institutionCalendarId, startMonth, endMonth, queryCategoryFormGroupIds } = query;
        const settingsQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            userId: convertToMongoObjectId(user_id),
            roleId: convertToMongoObjectId(role_id),
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
        };
        const qapcDashboardSettingsData = await qapcUserDashboardSettingsSchema
            .findOne(
                {
                    ...settingsQuery,
                },
                { colorCodes: 1, levels: 1, selectedItems: 1 },
            )
            .lean();

        if (!qapcDashboardSettingsData)
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };

        const { colorCodes, levels, selectedItems } = qapcDashboardSettingsData;
        if (levels.length === 0) return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };

        let tagsList = [];
        const categoryData = await userFromInitiatedList({
            userId: user_id,
            roleId: role_id,
            subModuleType: QA_DASHBOARD,
        });

        const categoryList = selectedItems.map((categoryElement) => {
            return {
                _id: categoryElement.categoryId,
                categoryName: categoryElement.categoryName,
            };
        });

        const currentMonth = new Date().getMonth() + 1;
        if (categoryData && categoryData.data) {
            levels.forEach((levelElement) => {
                return getLevelData({
                    levelElement,
                    categoryData: categoryData.data || {},
                    institutionCalendarId,
                    startMonth,
                    endMonth,
                    currentMonth,
                });
            });
        }

        const groupDataIds = levels.flatMap((levelElement) =>
            levelElement.levelData.flatMap((groupElement) => groupElement.counts.groupDataIds),
        );

        const queryCategoryFormGroupIdsArray =
            queryCategoryFormGroupIds !== '' ? queryCategoryFormGroupIds.split(',') : [];

        const enableTagFilter = queryCategoryFormGroupIdsArray.length > 0;

        const filteredDuplicateGroupIds = [...new Set(groupDataIds)].filter((initiatorElement) =>
            queryCategoryFormGroupIdsArray.length
                ? queryCategoryFormGroupIdsArray.includes(initiatorElement.toString())
                : initiatorElement,
        );
        const qapcFormInitiatorData = await formInitiatorSchema
            .find(
                {
                    categoryFormGroupId: { $in: filteredDuplicateGroupIds },
                    'formCalenderIds.institutionCalenderId':
                        convertToMongoObjectId(institutionCalendarId),
                },
                {
                    status: 1,
                    categoryFormGroupId: 1,
                    approverList: 1,
                    categoryFormId: 1,
                },
            )
            .populate({
                path: 'categoryFormId',
                select: { approvalLevel: 1 },
            })
            .lean();

        if (qapcFormInitiatorData && qapcFormInitiatorData.length) {
            levels.forEach((levelElement) => {
                return getInitiatorStatus({
                    levelElement,
                    qapcFormInitiatorData,
                });
            });

            const formInitiatorIds = qapcFormInitiatorData.map(
                (initiatorElement) => initiatorElement._id,
            );

            const qapcGuideResource = await qapcGuideResourceSchema
                .find(
                    {
                        formInitiatorIds: { $in: formInitiatorIds },
                        isDeleted: false,
                        // incorporateFrom: { $exists: true, $ne: [] },
                    },
                    {
                        incorporateFrom: 1,
                        formInitiatorIds: 1,
                        incorporateFromReason: 1,
                        tags: 1,
                    },
                )
                .lean();

            if (qapcGuideResource && qapcGuideResource.length) {
                levels.forEach((levelElement) => {
                    return getInCorporateStatus({
                        levelElement,
                        qapcGuideResource,
                        qapcFormInitiatorData,
                    });
                });

                tagsList = qapcGuideResource.flatMap((resourceElement) => {
                    const formInitiatorGroupIds = getFormInitiatorGroupIds({
                        formInitiatorIds: resourceElement.formInitiatorIds,
                        qapcFormInitiatorData,
                    });
                    return resourceElement.tags.map((tagElement) => ({
                        name: tagElement.name,
                        subTags: tagElement.subTag.map((subTagElement) => ({
                            name: subTagElement,
                            formInitiatorIds: resourceElement.formInitiatorIds,
                            categoryFormGroupIds: formInitiatorGroupIds,
                        })),
                        isDefault: tagElement.isDefault,
                        formInitiatorIds: resourceElement.formInitiatorIds,
                        categoryFormGroupIds: formInitiatorGroupIds,
                    }));
                });
                tagsList = transformTagsList(tagsList);
            }
        }

        levels.forEach((levelElement) => {
            return getTodoMissedStatusExclude({
                levelElement,
                filteredDuplicateGroupIds,
                enableTagFilter,
            });
        });

        //Remove empty groupIds
        const filteredLevels = levels
            .map((levelElement) => {
                return {
                    ...levelElement,
                    levelData: levelElement.levelData.filter(
                        (levelDataElement) =>
                            levelDataElement.counts.groupDataIds &&
                            levelDataElement.counts.groupDataIds.length > 0,
                    ),
                };
            })
            .filter((levelElement) => levelElement.levelData.length > 0);

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { colorCodes, levels: filteredLevels, categoryList, tagsList },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getGuideResourceReason = ({ guideResourceReason, formInitiatorId }) => {
    if (guideResourceReason && guideResourceReason.length) {
        const findReasonData = guideResourceReason.find((resourceElement) =>
            resourceElement.formInitiatorIds
                .map((initiatorElement) => initiatorElement.toString())
                .includes(formInitiatorId.toString()),
        );
        return findReasonData && findReasonData.incorporateFromReason
            ? findReasonData.incorporateFromReason
            : '';
    }
    return '';
};

const getTransformedIncorporateData = ({
    levelName,
    qapcDashboardSettingsData,
    seriesName,
    guideResourceReason,
}) => {
    switch (levelName) {
        case CATEGORY_LEVEL: {
            const courseMap = new Map();
            qapcDashboardSettingsData.forEach((sectionElement) => {
                sectionElement.formInitiatorIds.forEach((courseElement) => {
                    if (!courseMap.has(courseElement.courseId)) {
                        courseMap.set(courseElement.courseId, {
                            courseId: courseElement.courseId,
                            courseName: courseElement.courseName,
                            reason: getGuideResourceReason({
                                guideResourceReason,
                                formInitiatorId: courseElement._id,
                            }),
                            incorporateFrom: [],
                            ...(seriesName === INCORPORATED && { incorporateTo: [] }),
                        });
                    }
                    courseMap.get(courseElement.courseId).incorporateFrom.push({
                        sectionName: sectionElement.sectionName,
                        sectionId: sectionElement.sectionId,
                    });
                    if (seriesName === INCORPORATED) {
                        courseMap.get(courseElement.courseId).incorporateTo = [
                            ...courseMap.get(courseElement.courseId).incorporateTo,
                            ...sectionElement.incorporateTo,
                        ];
                    }
                });
            });
            const formattedData = Array.from(courseMap.values());
            return formattedData;
        }
        case PROGRAM_LEVEL: {
            const courseMap = new Map();
            qapcDashboardSettingsData.forEach((sectionElement) => {
                sectionElement.formInitiatorIds.forEach((courseElement) => {
                    if (!courseMap.has(courseElement.programId)) {
                        courseMap.set(courseElement.programId, {
                            courseId: courseElement.programId,
                            courseName: courseElement.programName,
                            reason: getGuideResourceReason({
                                guideResourceReason,
                                formInitiatorId: courseElement._id,
                            }),
                            incorporateFrom: [],
                            ...(seriesName === INCORPORATED && { incorporateTo: [] }),
                        });
                    }
                    courseMap.get(courseElement.programId).incorporateFrom.push({
                        sectionName: sectionElement.sectionName,
                        sectionId: sectionElement.sectionId,
                    });
                    if (seriesName === INCORPORATED) {
                        courseMap.get(courseElement.programId).incorporateTo = [
                            ...courseMap.get(courseElement.programId).incorporateTo,
                            ...sectionElement.incorporateTo,
                        ];
                    }
                });
            });
            const formattedData = Array.from(courseMap.values());
            return formattedData;
        }
        case INSTITUTION_LEVEL: {
            const courseMap = new Map();
            qapcDashboardSettingsData.forEach((sectionElement) => {
                sectionElement.formInitiatorIds.forEach((courseElement) => {
                    if (!courseMap.has(courseElement.assignedInstitutionId)) {
                        courseMap.set(courseElement.assignedInstitutionId, {
                            courseId: courseElement.assignedInstitutionId,
                            courseName: courseElement.institutionName,
                            reason: getGuideResourceReason({
                                guideResourceReason,
                                formInitiatorId: courseElement._id,
                            }),
                            incorporateFrom: [],
                            ...(seriesName === INCORPORATED && { incorporateTo: [] }),
                        });
                    }
                    courseMap.get(courseElement.assignedInstitutionId).incorporateFrom.push({
                        sectionName: sectionElement.sectionName,
                        sectionId: sectionElement.sectionId,
                    });
                    if (seriesName === INCORPORATED) {
                        courseMap.get(courseElement.assignedInstitutionId).incorporateTo = [
                            ...courseMap.get(courseElement.assignedInstitutionId).incorporateTo,
                            ...sectionElement.incorporateTo,
                        ];
                    }
                });
            });
            const formattedData = Array.from(courseMap.values());
            return formattedData;
        }
        default:
            return [];
    }
};

exports.getIncorporateSection = async ({ headers = {}, body = {} }) => {
    try {
        const { formInitiatorIds, levelName, formId, seriesName } = body;

        const qapcDashboardSettingsData = await qapcIncorporateSectionSchema
            .find(
                {
                    formInitiatorIds: { $in: formInitiatorIds },
                    isDeleted: false,
                    ...([INSTITUTION_LEVEL, PROGRAM_LEVEL].includes(levelName) && {
                        categoryFormId: formId,
                    }),
                },
                {
                    sectionName: 1,
                    incorporateTo: 1,
                    sectionId: 1,
                    categoryId: 1,
                    categoryName: 1,
                    categoryFormId: 1,
                    formName: 1,
                    formInitiatorIds: 1,
                },
            )
            .populate({
                path: 'formInitiatorIds',
                select: {
                    programId: 1,
                    programName: 1,
                    courseId: 1,
                    courseName: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                    level: 1,
                },
            })
            .lean();

        let guideResourceReason = [];
        if (seriesName !== INCORPORATED) {
            guideResourceReason = await qapcGuideResourceSchema
                .find(
                    {
                        formInitiatorIds: { $in: formInitiatorIds },
                        isDeleted: false,
                    },
                    {
                        formInitiatorIds: 1,
                        incorporateFromReason: 1,
                    },
                )
                .lean();
        }

        if (!qapcDashboardSettingsData)
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };

        const transformedIncorporateData = getTransformedIncorporateData({
            levelName,
            qapcDashboardSettingsData,
            seriesName,
            guideResourceReason,
        });

        return {
            statusCode: 200,
            data: transformedIncorporateData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
