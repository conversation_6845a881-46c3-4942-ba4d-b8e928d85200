const express = require('express');
const { getAllClo, getAllPlo } = require('./learning_outcomes.controller');
const route = express.Router();
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.post('/clo', [userPolicyAuthentication([])], getAllClo);
route.post('/plo', [userPolicyAuthentication([])], getAllPlo);

module.exports = route;
