const citiesSchema = require('./cities.model');
const { getModel } = require('../../utility/common');
const { CITY } = require('../../utility/constants');

const getCities = async ({ query = {}, headers = {} }) => {
    const { tenantURL } = headers;
    const citiesModel = getModel(tenantURL, CITY, citiesSchema);
    const { stateId } = query;
    const allCities = await citiesModel.find({ stateId }).lean();
    return { statusCode: 200, data: allCities };
};

module.exports = { getCities };
