const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_FORM_SETTING,
    PUBLISHED,
    INACTIVE,
    DRAFT,
    QAPC_FORM_CATEGORY,
    PENDING,
    ENTIRE,
    SPECIFIC,
    INST<PERSON>UTION,
    COMPLETE,
    SECTION,
    FORM,
    TEMPLATE,
    DIGI_PROGRAM,
    TAG_LEVEL,
    UNPUBLISHED,
} = require('../../utility/constants');

const formSettingSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        categoryId: { type: ObjectId, ref: QAPC_FORM_CATEGORY },
        templateId: { type: ObjectId },
        formName: { type: String },
        describe: { type: String },
        incorporateMandatory: { type: Boolean, default: false },
        categorySettings: {
            level: {
                type: String,
                enum: [TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION],
            },
        },
        status: {
            type: String,
            default: DRAFT,
            enum: [PUBLISHED, INACTIVE, DRAFT, UNPUBLISHED],
        },
        step: [{ type: Number }],
        categoryFormType: { type: String, enum: [FORM, TEMPLATE], default: FORM },
        selectedProgram: [
            {
                programId: { type: ObjectId, ref: DIGI_PROGRAM },
                programName: { type: String },
                all: { type: Boolean },
            },
        ],
        selectedInstitution: [
            {
                assignedInstitutionId: { type: ObjectId, ref: INSTITUTION },
                institutionName: { type: String },
            },
        ],
        approvalLevel: [
            {
                name: { type: String },
                levelNumber: { type: Number },
                approvalStatus: { type: String, default: PENDING },
                requireAll: { type: Boolean, default: true },
                requireMinium: { type: Boolean, default: false },
                minimum_user: { type: Number },
                turnAroundTime: { type: Number },
                escalateRequest: { type: Boolean, default: false },
                allowToSkipping: { type: Boolean, default: false },
                allowToOverwrite: { type: Boolean, default: false },
                category: { type: String, enum: [ENTIRE, SPECIFIC] },
                specificSections: [{ type: String }],
            },
        ],
        attachments: [
            {
                url: String,
                name: String,
            },
        ],
        sectionAttachments: [
            {
                sectionName: { type: String },
                description: { type: String },
            },
        ],
        archive: { type: Boolean, default: false },
        formType: { type: String, enum: [COMPLETE, SECTION], default: COMPLETE },
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_FORM_SETTING, formSettingSchema);
