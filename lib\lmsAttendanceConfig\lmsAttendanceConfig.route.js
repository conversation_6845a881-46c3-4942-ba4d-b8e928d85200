const router = require('express').Router();
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const catchAsync = require('../utility/catch-async');
const {
    attendanceAddRow,
    getAttendanceConfig,
    attendanceConfig,
    updateLateExclude,
    getLateExcludeStatus,
} = require('./lmsAttendanceConfig.controller');
const {
    attendanceAddRowValidator,
    getAttendanceConfigValidator,
} = require('./lmsAttendanceConfig.validator');
router.post(
    '/attendanceAddRow',
    [userPolicyAuthentication(['leave_management:leave_settings:student_lms:leave:view'])],
    [
        userPolicyAuthentication([
            'leave_management:student_register:view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_view',
        ]),
    ],
    attendanceAddRowValidator,
    catchAsync(attendanceAddRow),
);
router.get(
    '/getAttendanceConfig',
    [
        userPolicyAuthentication([
            'global_search:dashboard:view',
            'leave_management:student_register:view',
            'leave_management:leave_settings:student_lms:leave:view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_view',
        ]),
    ],
    getAttendanceConfigValidator,
    catchAsync(getAttendanceConfig),
);
router.get('/attendanceConfig', getAttendanceConfigValidator, catchAsync(attendanceConfig));

router.put(
    '/updateLateExclude',
    [
        userPolicyAuthentication([
            'leave_management:student_register:view',
            'leave_management:student_register:attendance_sheet:view',
            'global_search:dashboard:view',
        ]),
    ],
    catchAsync(updateLateExclude),
);
router.get(
    '/getLateExcludeStatus',
    [
        userPolicyAuthentication([
            'leave_management:student_register:view',
            'global_search:dashboard:view',
        ]),
    ],
    catchAsync(getLateExcludeStatus),
);
module.exports = router;
