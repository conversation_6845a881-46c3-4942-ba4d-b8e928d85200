const constant = require('../utility/constants');
const lms_review = require('mongoose').model(constant.LMS_REVIEW);
const lms = require('mongoose').model(constant.LMS);
const User = require('mongoose').model(constant.USER);
const DSetting = require('mongoose').model('dashboard_setting');
const DeptSetting = require('mongoose').model('department_setting');
const institution = require('mongoose').model(constant.INSTITUTION);
const user = require('mongoose').model(constant.USER);
const Program = require('mongoose').model(constant.DIGI_PROGRAM);
// const Curriculum = require('mongoose').model(constant.DIGI_CURRICULUM);
const PCalender = require('mongoose').model(constant.PROGRAM_CALENDAR);
const DeptSubject = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const Course = require('mongoose').model(constant.DIGI_COURSE);
const CourseSchedule = require('mongoose').model(constant.COURSE_SCHEDULE);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const session_delivery_type = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
const studentCriteriaCollection = require('mongoose').model(constant.STUDENT_CRITERIA_MANIPULATION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const { getTotalYears, getTotalStaffDeptSubDetails } = require('./service');
const ObjectId = common_files.convertToMongoObjectId;
const {
    sendResponse,
    clone,
    convertToMongoObjectId,
    responseFunctionWithRequest,
    sendResponseWithRequest,
} = require('../utility/common');
const {
    COMPLETED,
    ABSENT,
    LEAVE_TYPE: { PERMISSION },
    LEAVE,
    SCHEDULE_TYPES,
    COURSE_WISE,
    EXCLUDE,
} = require('../utility/constants');
const getJSON = base_control.dsGetAllWithSortAsJSON;
const cs = (value) => {
    return value.toString();
};
const { get_list, get } = require('../base/base_controller');
const { logger, SCHEDULE_SESSION_BASED_REPORT } = require('../utility/util_keys');
const {
    lmsSettings,
    userRoleData,
    courseCoordinatorBasedIds,
    userCourseScheduleList,
    programWiseYearLevelData,
    programWiseDepartmentSubjectData,
    yearLevelData,
} = require('./dashboard.service');
const {
    allCourseList,
    allProgramCalendarDatas,
    allSessionDeliveryTypesDatas,
    allSessionOrderDatas,
    allStudentGroupYesterday,
    allDepartmentSubjectList,
    userDashboardSettingDatas,
    clearItem,
    allProgramDatas,
} = require('../../service/cache.service');
const {
    lmsNewSetting,
    getLateAutoAndManualRange,
    getLateConfigAndStudentLateAbsent,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
} = require('../utility/utility.service');
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const {
    scheduleDateFormateChange,
    courseSessionOrderFilterBasedSchedule,
} = require('../utility/common_functions');

// Updating Course Flat Caching Data
const updateUserSettingFlatCacheData = async (userId) => {
    const itemName = `${userId.toString()}-userSetting`;
    clearItem(itemName);
    await userDashboardSettingDatas(userId);
};

const lmsStudentSettingData = async (_institution_id) => {
    // Leave Setting Data
    let warningAbsenceData = [
        { warning: 'Denial', absence_percentage: 101, warning_message: 'Denial' },
    ];
    const leave_category = await get(
        lms,
        {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
        },
        { _id: 1, category: 1, student_warning_absence_calculation: 1 },
    );
    if (
        leave_category.status &&
        leave_category.data &&
        leave_category.data.student_warning_absence_calculation &&
        leave_category.data.student_warning_absence_calculation.length !== 0 &&
        leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        ).length !== 0
    )
        warningAbsenceData = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
    warningAbsenceData = clone(
        warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        }),
    );

    const finaleWarning =
        warningAbsenceData.length !== 1
            ? warningAbsenceData[1] && warningAbsenceData[1].warning
                ? warningAbsenceData[1].warning
                : undefined
            : undefined;
    const denialWarning = warningAbsenceData[0].warning;
    return {
        warningAbsenceData,
        finaleWarning,
        denialWarning,
    };
};

const studentAttendanceReport = (
    courseStudentIds,
    courseScheduled,
    warningAbsenceData,
    studentCriteriaData,
) => {
    const studentData = [];
    for (studentElement of courseStudentIds) {
        let studentSchedule = 0;
        let absentSchedules = 0;
        for (scheduleElement of courseScheduled) {
            const studentObject = scheduleElement.students.find(
                (ele2) => ele2._id.toString() === studentElement.toString(),
            );
            if (studentObject) {
                studentSchedule++;
                if (
                    studentObject.status === ABSENT ||
                    studentObject.status === PERMISSION ||
                    studentObject.status === LEAVE
                )
                    absentSchedules++;
            }
        }
        const denialPercentage = (absentSchedules / studentSchedule) * 100;
        studentElement.absence = denialPercentage.toFixed(2);

        const studentManipulation = studentCriteriaData.data.find(
            (absenceElement) => absenceElement.studentId.toString() === studentElement.toString(),
        );
        const studentWarningAbsence = clone(warningAbsenceData);
        if (
            studentManipulation &&
            studentManipulation.absencePercentage &&
            studentWarningAbsence[0] &&
            studentWarningAbsence[0].absence_percentage &&
            studentWarningAbsence[0].absence_percentage < studentManipulation.absencePercentage
        )
            studentWarningAbsence[0].absence_percentage = studentManipulation.absencePercentage;
        const warningData = studentWarningAbsence.find(
            (ele) =>
                ele.absence_percentage &&
                parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
        );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.warning : '',
        });
    }
    return studentData;
};
const getAttendanceReport = (
    courseStudentIds,
    courseScheduled,
    warningAbsenceData,
    studentCriteriaData,
    courseId,
    term,
    rotation_count,
    denialLabel,
    lateDurationRange,
    manualLateRange,
    manualLateData,
    _institution_id,
    institutionCalendar,
    programId,
    level_no,
    lateExcludeManagement,
) => {
    const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
        _institution_calendar_id: institutionCalendar,
        programId,
        courseId,
        levelNo: level_no,
        term,
        rotationCount: rotation_count,
        lateExcludeManagement,
    });
    const studentData = [];
    for (studentElement of courseStudentIds) {
        const manipulatedPercentage = clone(warningAbsenceData);
        manipulatedPercentage[0].isManipulated = false;
        let studentSchedule = 0;
        let absentSchedules = 0;
        let completedSchedule = 0;
        for (scheduleElement of courseScheduled) {
            const studentObject = scheduleElement.students.find(
                (studentScheduleElement) =>
                    studentScheduleElement._id.toString() === studentElement.toString() &&
                    studentScheduleElement.status !== EXCLUDE,
            );
            if (studentObject) {
                if (scheduleElement.status == COMPLETED) {
                    completedSchedule++;
                }
                studentSchedule++;
                if (studentObject.status === ABSENT || studentObject.status === LEAVE)
                    absentSchedules++;
            }
        }
        const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendar,
            programId,
            courseId,
            levelNo: level_no,
            term,
            rotationCount: rotation_count,
            lateExcludeManagement,
            studentId: studentElement,
        }).lateExclude;
        let studentLateAbsent = 0;
        if (!lateExclude && !lateExcludeForStudent) {
            const { studentLateAbsent: updated } = getLateConfigAndStudentLateAbsent({
                lateDurationRange,
                manualLateRange,
                manualLateData,
                scheduleData: courseScheduled,
                studentElement: { _student_id: studentElement },
                lateExcludeManagement,
            });
            studentLateAbsent = updated;
        }
        const denialPercentage = ((absentSchedules + studentLateAbsent) / studentSchedule) * 100;
        const isMixedStudent =
            studentCriteriaData.length &&
            studentCriteriaData.find(
                (denialStudentElement) =>
                    ((rotation_count && rotation_count > 0
                        ? parseInt(rotation_count) === denialStudentElement.rotationCount
                        : true) &&
                        denialStudentElement.term === term &&
                        denialStudentElement.courseId.toString() === courseId.toString() &&
                        denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                    (denialStudentElement.term === term &&
                        denialStudentElement.studentId &&
                        denialStudentElement.courseId.toString() === courseId.toString() &&
                        denialStudentElement.studentId.toString() === studentElement.toString()),
            );
        if (
            isMixedStudent &&
            isMixedStudent.absencePercentage &&
            warningAbsenceData[0] &&
            warningAbsenceData[0].percentage &&
            warningAbsenceData[0].percentage < isMixedStudent.absencePercentage
        ) {
            manipulatedPercentage[0].percentage = isMixedStudent.absencePercentage;
            manipulatedPercentage[0].isManipulated = true;
        }
        const warningData =
            manipulatedPercentage[0].labelName === denialLabel &&
            manipulatedPercentage[0].isManipulated
                ? manipulatedPercentage.find(
                      (manipulatedElement) =>
                          manipulatedElement.percentage &&
                          parseFloat(denialPercentage) > parseFloat(manipulatedElement.percentage),
                  )
                : warningAbsenceData.find(
                      (warningElement) =>
                          warningElement.percentage &&
                          parseFloat(denialPercentage) > parseFloat(warningElement.percentage),
                  );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.labelName : '',
        });
    }
    return studentData;
};
const courseCreditContactHours = (sessionDeliveryTypesData, courseScheduled, sessionOrderData) => {
    let endSchedule = 0;
    let scheduleSessionCount = 0;
    // const sessionDeliveryTypesData = sessionDeliveryTypes.filter(
    //     (ele) => ele._program_id.toString() === _program_id.toString(),
    // );
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        if (deliveryTypeData.length) {
            for (sessionOrderElement of sessionOrderData) {
                const sessionSchedule = deliveryTypeData.filter(
                    (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
                );
                if (sessionSchedule.length > 0) scheduleSessionCount++;
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                ) {
                    endSchedule++;
                }
            }
        }
    }
    return { session_count: scheduleSessionCount, complete: endSchedule };
};

const getUserRoleProgram = async (userId, roleId) => {
    const ra_project = {
        'roles._role_id': 1,
        'roles.role_name': 1,
        'roles.program': 1,
        'roles.isAdmin': 1,
    };
    const roleAssign = await get(
        role_assign,
        { _user_id: convertToMongoObjectId(userId) },
        ra_project,
    );
    roleAssign.data = roleAssign.status ? roleAssign.data : [{ roles: [] }];
    const userRoleData = roleAssign.data.roles.find(
        (ele) => ele._role_id.toString() === roleId.toString(),
    );
    const roleProgramIds =
        userRoleData?.program.map((ele) => ele && ele._program_id.toString()) ?? [];
    return {
        status: true,
        isAdmin: userRoleData?.isAdmin ?? false,
        roleName: userRoleData?.role_name ?? '',
        data: roleProgramIds,
    };
};

// Course Listing
const getCourseList = async (_institution_id) => {
    const courseProject = {
        course_name: 1,
        course_code: 1,
        course_type: 1,
        _program_id: 1,
        'administration._program_id': 1,
        'administration._department_id': 1,
        'administration._subject_id': 1,
        'participating._program_id': 1,
        'participating._department_id': 1,
        'participating._subject_id': 1,
        'course_assigned_details._program_id': 1,
        'course_assigned_details.program_name': 1,
        'course_assigned_details.year': 1,
        'course_assigned_details.level_no': 1,
        'course_assigned_details.course_shared_with._program_id': 1,
        'course_assigned_details.course_shared_with.program_name': 1,
        coordinators: 1,
    };
    const courseList = await get_list(
        Course,
        { _institution_id: convertToMongoObjectId(_institution_id), isDeleted: false },
        courseProject,
    );
    courseList.data = courseList.status ? courseList.data : [];
    return courseList.data;
};

const courseCoordinatorProgramIds = async (courseList, userId, institutionCalendar) => {
    const userCourseList = courseList.filter((ele) =>
        ele.coordinators.find(
            (ele2) =>
                ele2._user_id.toString() === userId.toString() &&
                ele2._institution_calendar_id.toString() === institutionCalendar.toString(),
        ),
    );
    let programIds = userCourseList.map((ele) => ele._program_id.toString());
    const courseIds = userCourseList.map((ele) => ele._id.toString());
    const courseWithTerm = [];
    userCourseList.forEach((eleUCL) => {
        const coordinator = eleUCL.coordinators.filter(
            (ele2) =>
                ele2._user_id.toString() === userId.toString() &&
                ele2._institution_calendar_id.toString() === institutionCalendar.toString(),
        );
        if (coordinator) {
            coordinator.forEach((eleC) => {
                courseWithTerm.push({
                    _course_id: eleUCL._id.toString(),
                    term: eleC.term,
                    year: eleC.year,
                    level_no: eleC.level_no,
                });
            });
        }
    });

    for (courseData of userCourseList) {
        for (courseAssigned of courseData.course_assigned_details) {
            for (courseShared of courseAssigned.course_shared_with) {
                programIds.push(courseShared._program_id.toString());
            }
        }
    }
    programIds = [...new Set(programIds)];
    //console.log('Course Term: ', courseWithTerm);
    return { programIds, courseIds, courseWithTerm };
};

const courseScheduleList = async (userId, institutionCalendar, isAdmin, programIds, courseIds) => {
    const csQuery = {
        _institution_calendar_id: convertToMongoObjectId(institutionCalendar),
        type: constant.SCHEDULE_TYPES.REGULAR,
        isActive: true,
        isDeleted: false,
    };
    if (isAdmin) {
        csQuery._program_id = { $in: programIds };
    } else if (courseIds && courseIds.length !== 0) {
        // csQuery._course_id = { $in: courseIds };
        Object.assign(csQuery, {
            $or: [
                { _course_id: { $in: courseIds } },
                { 'staffs._staff_id': convertToMongoObjectId(userId) },
            ],
        });
    } else {
        Object.assign(csQuery, { 'staffs._staff_id': convertToMongoObjectId(userId) });
    }
    const csProject = {
        _course_id: 1,
        _program_id: 1,
        term: 1,
        level_no: 1,
        subjects: 1,
        rotation: 1,
        rotation_count: 1,
        session: 1,
        'students._id': 1,
        'students.status': 1,
        status: 1,
        staffs: 1,
    };
    const courseScheduleListDatas = await get_list(CourseSchedule, csQuery, csProject);
    courseScheduleListDatas.data = courseScheduleListDatas.status
        ? courseScheduleListDatas.data
        : [];

    const csl_data = [];
    courseScheduleListDatas.data.forEach((csElement) => {
        if (
            csElement.staffs.findIndex(
                (eleStaff) => eleStaff._staff_id.toString() === userId.toString(),
            ) != -1
        ) {
            //Staff
            csl_data.push({
                _id: cs(csElement._id),
                _subject_id: csElement.subjects.map((ele) => cs(ele._subject_id)),
                _program_id: cs(csElement._program_id),
                term: csElement.term,
                level_no: csElement.level_no,
                rotation: csElement.rotation,
                rotation_count: csElement.rotation_count,
                students: csElement.students,
                session: csElement.session,
                status: csElement.status,
                _course_id: cs(csElement._course_id),
                _session_id:
                    csElement.session && csElement.session._session_id
                        ? csElement.session._session_id
                        : undefined,
                staffs: csElement.staffs,
            });
        } else if ((courseIds && courseIds.length !== 0) || isAdmin) {
            //Course Coordinator and Super admin
            csl_data.push({
                _id: cs(csElement._id),
                _subject_id: csElement.subjects.map((ele) => cs(ele._subject_id)),
                _program_id: cs(csElement._program_id),
                term: csElement.term,
                level_no: csElement.level_no,
                rotation: csElement.rotation,
                rotation_count: csElement.rotation_count,
                students: csElement.students,
                session: csElement.session,
                status: csElement.status,
                _course_id: cs(csElement._course_id),
                _session_id:
                    csElement.session && csElement.session._session_id
                        ? csElement.session._session_id
                        : undefined,
                staffs: csElement.staffs,
            });
        }
    });
    /* courseScheduleListDatas.data = clone(
        courseScheduleListDatas.data.map((csElement) => {
            if (
                csElement.staffs.findIndex(
                    (eleStaff) => eleStaff._staff_id.toString() === userId.toString(),
                ) != -1
            )
                return {
                    _id: cs(csElement._id),
                    _subject_id: csElement.subjects.map((ele) => cs(ele._subject_id)),
                    _program_id: cs(csElement._program_id),
                    term: csElement.term,
                    level_no: csElement.level_no,
                    rotation: csElement.rotation,
                    rotation_count: csElement.rotation_count,
                    students: csElement.students,
                    session: csElement.session,
                    status: csElement.status,
                    _course_id: cs(csElement._course_id),
                    _session_id:
                        csElement.session && csElement.session._session_id
                            ? csElement.session._session_id
                            : undefined,
                    staffs: csElement.staffs,
                };
        }),
    ); */
    //console.log(csl_data);
    const scheduleCourseTerm = [];
    for (scheduleElement of csl_data) {
        const ind = scheduleCourseTerm.findIndex(
            (ele) =>
                ele._course_id &&
                ele._course_id.toString() === scheduleElement._course_id.toString() &&
                ele.term === scheduleElement.term,
        );
        //console.log(ind);
        if (ind == -1) {
            scheduleCourseTerm.push({
                _course_id: scheduleElement._course_id.toString(),
                term: scheduleElement.term,
            });
        }
    }
    console.log('Scheduled Term: ', scheduleCourseTerm);
    return { scheduleCourseTerm, scheduleList: csl_data };
};

const getProgramIds = async (user_id, role_id) => {
    const u_query = { isDeleted: false, isActive: true, _id: ObjectId(user_id) };
    const u_project = { _id: 1, _role_id: 1 };
    const users = (await getJSON(User, u_query, u_project)).data;
    if (!users.length) return { status: false, isAdmin: false, data: [] };
    const ra_query = {
        isDeleted: false,
        isActive: true,
        _id: ObjectId(users[0]._role_id),
    };
    const ra_project = {};
    const RoleAssigns = (await getJSON(role_assign, ra_query, ra_project)).data;
    let isAdmin = false;
    let roleName = '';
    const programIds = [];
    RoleAssigns.forEach((ra) => {
        ra.roles.forEach((role) => {
            if (cs(role._role_id) === cs(role_id)) {
                isAdmin = role.isAdmin || false;
                roleName = role.role_name;
                role.program.forEach((program) => {
                    programIds.push(program._program_id);
                });
            }
        });
    });
    return { status: true, isAdmin, roleName, data: programIds };
};
const getCourseCoordinatorProgramIDs = async (user_id) => {
    const c_project = {
        _id: 1,
        coordinators: 1,
        _program_id: 1,
        'course_assigned_details._program_id': 1,
        'course_assigned_details.course_shared_with._program_id': 1,
    };
    c_query = {
        'coordinators._user_id': ObjectId(user_id),
        isActive: true,
        isDeleted: false,
    };
    const CourseData = (await getJSON(Course, c_query, c_project)).data;
    const program_ids = CourseData.map((ele) => ele._program_id);
    const CourseD = [];
    let courseCoordinatorTerm = [];
    CourseData.forEach((ele) => {
        const cc = ele.coordinators.filter(
            (eleCC) => eleCC._user_id.toString() === user_id.toString(),
        );
        if (cc.length > 0) {
            const term = cc.map((ele) => ele.term);
            courseCoordinatorTerm = [...courseCoordinatorTerm, ...term];
            CourseD.push({
                _id: ele._id,
                _program_id: ele._program_id,
                course_assigned_details: ele.course_assigned_details,
                coordinators: cc,
            });
        }
    });
    for (courseData of CourseD) {
        for (courseAssigned of courseData.course_assigned_details) {
            if (courseAssigned.course_shared_with.length !== 0) {
                for (courseShared of courseAssigned.course_shared_with) {
                    program_ids.push(courseShared._program_id);
                }
            }
        }
    }
    let duplicateTermRemoved = [];
    if (courseCoordinatorTerm) duplicateTermRemoved = [...new Set(courseCoordinatorTerm)];
    return { program_ids, term: duplicateTermRemoved };
};

const getCourseCoordinatorSchedules = async (user_id) => {
    const c_project = { _id: 1, coordinators: 1 };
    c_query = {
        'coordinators._user_id': ObjectId(user_id),
        isActive: true,
        isDeleted: false,
    };
    const CourseData = (await getJSON(Course, c_query, c_project)).data;

    if (CourseData.length == 0) return [];
    const CourseD = [];
    CourseData.forEach((ele) => {
        const cc = ele.coordinators.filter(
            (eleCC) => eleCC._user_id.toString() === user_id.toString(),
        );
        if (cc.length > 0) {
            CourseD.push({
                _id: ele._id,
                coordinators: cc,
            });
        }
    });
    const CCcourseIds = CourseD.map((ele) => ele._id);

    const cs_project = {
        _course_id: 1,
        _program_id: 1,
        term: 1,
        level_no: 1,
        subjects: 1,
        rotation: 1,
        rotation_count: 1,
        session: 1,
        'students._id': 1,
        'students.status': 1,
        status: 1,
    };
    cs_query = {
        type: constant.SCHEDULE_TYPES.REGULAR,
        isActive: true,
        isDeleted: false,
        _course_id: { $in: CCcourseIds },
    };
    const CCCourseSchedules = (await getJSON(CourseSchedule, cs_query, cs_project)).data;
    return CCCourseSchedules;
};

const getCourseIds = async (user_id, isAdmin, roleName) => {
    let cs_query;
    if (isAdmin) {
        cs_query = {
            type: constant.SCHEDULE_TYPES.REGULAR,
            isActive: true,
            isDeleted: false,
        };
    } else {
        cs_query = {
            type: constant.SCHEDULE_TYPES.REGULAR,
            isActive: true,
            isDeleted: false,
            'staffs._staff_id': ObjectId(user_id),
        };
    }
    const cs_project = {
        _course_id: 1,
        _program_id: 1,
        term: 1,
        level_no: 1,
        subjects: 1,
        rotation: 1,
        rotation_count: 1,
        session: 1,
        'students._id': 1,
        'students.status': 1,
        status: 1,
    };
    let CourseSchedules = [];

    //Get Course Coordinator courses and schedules
    let CCCourseSchedules = [];
    console.log(roleName);
    if (roleName == 'Course Coordinator') {
        CCCourseSchedules = await getCourseCoordinatorSchedules(user_id);
        if (CCCourseSchedules.length > 0) CourseSchedules = CCCourseSchedules;
    } else {
        CourseSchedules = (await getJSON(CourseSchedule, cs_query, cs_project)).data;
    }

    if (!CourseSchedules.length) return { status: false, data: [] };

    const courseIds = CourseSchedules.map((CourseSchedule) => {
        return {
            _id: cs(CourseSchedule._course_id),
            _subject_id: CourseSchedule.subjects.map((ele) => cs(ele._subject_id)),
            _program_id: cs(CourseSchedule._program_id),
            term: CourseSchedule.term,
            level_no: CourseSchedule.level_no,
            rotation: CourseSchedule.rotation,
            rotation_count: CourseSchedule.rotation_count,
            students: CourseSchedule.students,
            session: CourseSchedule.session,
            status: CourseSchedule.status,
            _session_id:
                CourseSchedule.session && CourseSchedule.session._session_id
                    ? CourseSchedule.session._session_id
                    : undefined,
        };
    });
    return { status: true, data: courseIds };
};

exports.getLeaveList = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const user_get = await base_control.get(
            user,
            { _id: ObjectId(req.params.user_id) },
            { staff_employment_type: 1 },
        );
        if (!user_get.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('USER_NOT_FOUND'),
                        user_get.data,
                    ),
                );

        const user_role_assign = await base_control.get(
            role_assign,
            { _user_id: ObjectId(req.params.user_id) },
            {},
        );
        if (!user_role_assign.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('USER_NOT_FOUND'),
                        user_role_assign.data,
                    ),
                );

        const role_ind = user_role_assign.data.roles.findIndex(
            (i) => i._role_id.toString() === req.params.role_id.toString(),
        );
        if (role_ind == -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('USER_ROLE_NOT_FOUND'),
                        user_role_assign.data,
                    ),
                );
        const role_id = user_role_assign.data.roles[role_ind]._role_id;
        const role_program = user_role_assign.data.roles[role_ind].program.map((i) =>
            ObjectId(i._program_id),
        );
        let role_department = [];
        if (user_role_assign.data.roles[role_ind].department.length != 0)
            role_department = user_role_assign.data.roles[role_ind].department.map((i) =>
                ObjectId(i._department_id),
            );
        const lms_setting = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id) },
            { leave_approval: 1 },
        );
        if (!lms_setting.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('NO_LEAVE_SETTING_FOUND'),
                        req.t('NO_LEAVE_SETTING_FOUND'),
                    ),
                );

        if (!role_id)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_ROLE'),
                        req.t('UNABLE_TO_FIND_ROLE'),
                    ),
                );
        let approver_loc = -1;
        lms_setting.data.leave_approval.forEach((element, index) => {
            if (
                approver_loc == -1 &&
                ((element.role &&
                    element.role._roles_id &&
                    element.role._roles_id.toString() === role_id.toString()) ||
                    (element._staff_id &&
                        element._staff_id.toString() === req.params.user_id.toString()))
            )
                approver_loc = index;
        });
        if (approver_loc == -1)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('NO_LEAVES_FOUND'),
                        [],
                    ),
                );
        approver_level = lms_setting.data.leave_approval[approver_loc].level_no;
        const type_approvers = lms_setting.data.leave_approval.filter(
            (i) =>
                i.staff_type.toString() ==
                lms_setting.data.leave_approval[approver_loc].staff_type.toString(),
        );
        //Control Hierarchy level Staff Application
        const level_role_ids = [];
        const staff_levels = [];
        lms_setting.data.leave_approval.forEach((element) => {
            if (
                element.level_no >= approver_level &&
                element.staff_type.toString() ==
                    lms_setting.data.leave_approval[approver_loc].staff_type.toString()
            ) {
                if (element.role && element.role._roles_id)
                    level_role_ids.push(ObjectId(element.role._roles_id));
                else if (element._staff_id) staff_levels.push(ObjectId(element._staff_id));
            }
        });
        // console.log('Level ', level_role_ids);
        const lms_role_query = {
            $or: [
                { roles: { $elemMatch: { _role_id: { $in: level_role_ids } } } },
                { _user_id: { $in: staff_levels } },
            ],
        };
        const lms_role_data = await base_control.get_list(role_assign, lms_role_query, {
            _user_id: 1,
        });
        let upperUsers = lms_role_data.status
            ? lms_role_data.data.map((i) => ObjectId(i._user_id))
            : [];
        if (type_approvers.length === approver_level)
            upperUsers = upperUsers.filter(
                (ele) => ele.toString() !== req.params.user_id.toString(),
            );

        // return res.send(lms_role_data);
        const aggre = [];
        if (upperUsers.length != 0) aggre.push({ $match: { _user_id: { $nin: upperUsers } } });
        aggre.push(
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _institution_calendar_id: ObjectId(req.params.inst_cal_id),
                    isDeleted: false,
                    isActive: true,
                    type: { $ne: 'report_absence' },
                },
            },
            // { $match: { type: req.params.type } },
            {
                $lookup: {
                    from: 'users',
                    localField: '_user_id',
                    foreignField: '_id',
                    as: 'staff',
                },
            },
            { $unwind: { path: '$staff', preserveNullAndEmptyArrays: true } },
        );
        if (lms_setting.data.leave_approval[approver_loc].staff_type.toString() == 'admin') {
            aggre.push({ $match: { 'staff.staff_employment_type': 'administration' } });
        } else if (
            lms_setting.data.leave_approval[approver_loc].staff_type.toString() == 'academic' ||
            lms_setting.data.leave_approval[approver_loc].staff_type.toString() == 'both'
        ) {
            //Need to filter Program Details based on Approver based
            aggre.push(
                {
                    $match: {
                        $or: [
                            { 'staff.staff_employment_type': 'both' },
                            { 'staff.staff_employment_type': 'academic' },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: '$staff.academic_allocation',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                { $match: { 'staff.academic_allocation.allocation_type': 'primary' } },
                // { $match: { 'staff.academic_allocation._program_id': { $in: role_program } } },
                {
                    $lookup: {
                        from: 'digi_programs',
                        localField: 'staff.academic_allocation._program_id',
                        foreignField: '_id',
                        as: 'staff.academic_allocation.program',
                    },
                },
                {
                    $unwind: {
                        path: '$staff.academic_allocation.program',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                { $addFields: { 'staff.program': '$staff.academic_allocation.program.name' } },
            );
            if (role_program.length !== 0) {
                aggre.push({
                    $match: { 'staff.academic_allocation._program_id': { $in: role_program } },
                });
            }
            if (role_department.length != 0) {
                aggre.push({
                    $match: {
                        'staff.academic_allocation._department_id': { $in: role_department },
                    },
                });
            }
        }
        aggre.push({
            $project: {
                _id: 1,
                isActive: 1,
                _user_id: 1,
                type: 1,
                from: 1,
                to: 1,
                days: 1,
                permission_hour: 1,
                comments: 1,
                leave_category: 1,
                leave_type: 1,
                status: 1,
                reason: 1,
                status_time: 1,
                payment_status: 1,
                session: 1,
                level_approvers: 1,
                _leave_reason_doc: 1,
                'staff.name': 1,
                'staff.user_id': 1,
                'staff.gender': 1,
                'staff.role': 1,
                'staff.staff_employment_type': 1,
                'staff.academic_allocation': 1,
                'staff.program': 1,
            },
        });
        // return res.send(aggre);
        const doc = await base_control.get_aggregate(lms_review, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('LEAVE_LIST'),
                        doc.data,
                    ),
                );
        const pending = [];
        const reviewed = [];
        const rejected = [];
        let role = 'reviewer';
        if (approver_level == 1) {
            doc.data.forEach((element) => {
                if (element.level_approvers.length == 0) pending.push(element);
                else {
                    const level_ind = element.level_approvers.findIndex(
                        (i) => i.level_no == approver_level,
                    );
                    if (
                        level_ind !== -1 &&
                        element.level_approvers[level_ind].status == 'approve'
                    ) {
                        reviewed.push(element);
                    } else {
                        rejected.push(element);
                    }
                }
            });
        } else {
            let upper_users = [];
            if (approver_level - 1 != -1) {
                const previous_level = lms_setting.data.leave_approval.findIndex(
                    (i) => i.level_no == approver_level - 1,
                );
                if (
                    previous_level != -1 &&
                    lms_setting.data.leave_approval[previous_level].role &&
                    lms_setting.data.leave_approval[previous_level].role._roles_id
                ) {
                    const lms_role_query = {
                        roles: {
                            $elemMatch: {
                                _role_id: ObjectId(
                                    lms_setting.data.leave_approval[previous_level].role._roles_id,
                                ),
                            },
                        },
                    };
                    const lms_role_datas = await base_control.get_list(
                        role_assign,
                        lms_role_query,
                        {
                            _user_id: 1,
                        },
                    );
                    if (lms_role_datas.status) {
                        upper_users = lms_role_datas.data.map((i) => ObjectId(i._user_id));
                    }
                }
            }
            // return res.send(doc.data);
            doc.data.forEach((element) => {
                const pre_level_ind = element.level_approvers.findIndex(
                    (i) => i.level_no == approver_level - 1,
                );
                if (pre_level_ind != -1) {
                    if (element.level_approvers[pre_level_ind].status == 'approve') {
                        const level_ind = element.level_approvers.findIndex(
                            (i) => i.level_no == approver_level,
                        );
                        if (level_ind != -1) {
                            if (element.level_approvers[level_ind].status == 'approve') {
                                reviewed.push(element);
                            } else {
                                rejected.push(element);
                            }
                        } else if (!element.status || element.status !== 'approve') {
                            pending.push(element);
                        }
                    }
                } else {
                    if (
                        (upper_users.length != 0 &&
                            upper_users.findIndex(
                                (i) => i.toString() == element._user_id.toString(),
                            ) != -1) ||
                        (element._user_id.toString() === req.params.user_id.toString() &&
                            type_approvers.length === approver_level)
                    ) {
                        const level_ind = element.level_approvers.findIndex(
                            (i) => i.level_no == approver_level,
                        );
                        if (level_ind != -1) {
                            if (element.level_approvers[level_ind].status == 'approve') {
                                reviewed.push(element);
                            } else {
                                rejected.push(element);
                            }
                        } else if (!element.status || element.status !== 'approve') {
                            pending.push(element);
                        }
                    }
                }
            });
        }
        role = type_approvers.length == approver_level ? 'approver' : 'reviewer';
        const data = { pending, reviewed, rejected };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STAFF_LEAVE_LIST'),
                    data,
                ),
            );
        // return res.status(200).send(common_files.response_function(res, 200, true, "Staff LEAVE_LIST", data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.getProgramList = async (req, res) => {
    try {
        const { user_id, role_id } = req.params;
        const programIdResult = await getProgramIds(user_id, role_id);
        if (!programIdResult.status) {
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ROLE_ASSIGN_RECORD_NOT_FOUND'),
                        [],
                    ),
                );
        }
        let programIds = [];
        if (programIdResult.isAdmin) programIds = programIdResult.data;
        else {
            const courseDatas = await getCourseIds(
                user_id,
                programIdResult.isAdmin,
                programIdResult.roleName,
            );
            if (!courseDatas.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('NO_COURSE_SCHEDULED'),
                            [],
                        ),
                    );
            programIds = courseDatas.data.map((ele) => ele._program_id);
            // const u_query = { isDeleted: false, isActive: true, _id: ObjectId(user_id) };
            // const u_project = { _id: 1, academic_allocation: 1 };
            // const users = (await getJSON(User, u_query, u_project)).data;
            // programIds = users[0].academic_allocation.map((ele) => ele._program_id);
        }
        const p_query = { isDeleted: false, isActive: true, _id: { $in: programIds } };
        const programs = (await getJSON(Program, p_query, {})).data;

        //Staff Data
        const u_query = { isDeleted: false, isActive: true, user_type: 'staff' };
        const u_project = { _id: 1, name: 1, academic_allocation: 1 };
        const users = (await getJSON(User, u_query, u_project)).data;

        //Course schedule data
        const cs_query = {
            isDeleted: false,
            isActive: true,
            _institution_id: ObjectId(req.headers._institution_id),
        };
        const cs_project = { _id: 1, subjects: 1, staffs: 1, _program_id: 1 };
        const course_schedule_data = (await getJSON(CourseSchedule, cs_query, cs_project)).data;

        //Department Subject Data
        const d_query = {
            isDeleted: false,
            isActive: true,
            _institution_id: ObjectId(req.headers._institution_id),
            //program_id: ObjectId(program._id),
        };
        const d_project = { _id: 1, department_name: 1, subject: 1, program_id: 1 };
        const deptData = (await getJSON(DeptSubject, d_query, d_project)).data;

        for (const program of programs) {
            const counts = await getTotalYears(program._id);
            program.counts = counts;
            program.totalDeptCourse = 0;
            program.totalCurriculums = counts.curriculums.length;
            program.totalYears = counts.totalYears;
            program.totalLevels = counts.totalLevels;
            program.totalCourses = counts.totalCourse;

            const deptDataByProgram = deptData.filter(
                (ele) => ele.program_id.toString() === program._id.toString(),
            );
            const csDataByProgram = course_schedule_data.filter(
                (ele) => ele._program_id.toString() === program._id.toString(),
            );
            const staffDeptSubDetails = await getTotalStaffDeptSubDetails(
                program,
                users,
                csDataByProgram,
                deptDataByProgram,
            );
            program.totalStaffOld = staffDeptSubDetails.staffSubjectDetails.length;
            program.totalStaff = staffDeptSubDetails.staffCount;
            program.totalDepartments = staffDeptSubDetails.departmentCount;
            program.totalSubjects = staffDeptSubDetails.subjectCount;
            program.staffDetails = staffDeptSubDetails.staffSubjectDetails;
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_LIST_RETRIEVED'),
                    programs,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, true, req.t('SERVER_ERROR'), error));
    }
};

exports.getConfigureSettingListOld = async (req, res) => {
    const { user_id, inst_cal_id, role_id } = req.params;
    try {
        // get All Programs ids of given user
        const programIdResult = await getProgramIds(user_id, role_id);
        if (!programIdResult.status)
            return sendResponse(res, 404, false, req.t('USER_RECORD_NOT_FOUND'), []);

        if (programIdResult.roleName == 'Course Coordinator') {
            programIdResult.data = await getCourseCoordinatorProgramIDs(user_id);
        }

        const programIds = programIdResult.data;
        let courseDatas = [];
        courseDatas = (
            await getCourseIds(user_id, programIdResult.isAdmin, programIdResult.roleName)
        ).data;
        // get All Program Names
        const p_query = { isDeleted: false, isActive: true, _id: { $in: programIds } };
        const p_project = { _id: 1, name: 1 };
        const programs = (await getJSON(Program, p_query, p_project)).data;
        const getProgramName = (program_id) => {
            let returnableProgramName;
            programs.forEach((program) => {
                if (cs(program_id) === cs(program._id)) {
                    returnableProgramName = program.name;
                }
            });
            return returnableProgramName;
        };

        // get All Program calender
        pc_query = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: ObjectId(inst_cal_id),
            _program_id: { $in: programIds },
        };
        let courseIds = [];
        if (!programIdResult.isAdmin && courseDatas.length) {
            courseIds = courseDatas.map((courseElement) => cs(courseElement._id));
            courseIds = [...new Set(courseIds)];
            pc_query.$or = [
                { 'level.course._course_id': { $in: courseIds } },
                { 'level.rotation_course.course._course_id': { $in: courseIds } },
            ];
        }
        pc_project = {
            _id: 1,
            _program_id: 1,
            isActive: 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.course._course_id': 1,
            'level.course.courses_name': 1,
            'level.course.start_date': 1,
            'level.course.end_date': 1,
            'level.course.courses_number': 1,
            'level.course.model': 1,
            'level._id': 1,
            'level.curriculum': 1,
            'level.term': 1,
            'level.year': 1,
            'level.level_no': 1,
            'level._program_id': 1,
            'level.rotation_course.rotation_count': 1,
            'level.rotation_course.course._course_id': 1,
            'level.rotation_course.course.courses_name': 1,
            'level.rotation_course.course.start_date': 1,
            'level.rotation_course.course.end_date': 1,
            'level.rotation_course.course.courses_number': 1,
            'level.rotation_course.course.model': 1,
            'level.rotation': 1,
        };
        const PCalenders = (await getJSON(PCalender, pc_query, pc_project)).data;
        console.log(PCalenders);
        const getRCourses = (rotation_course) => {
            const allRCourses = [];
            if (rotation_course.length) {
                rotation_course.forEach((rorElement) => {
                    rorElement.course.forEach((r_course) => {
                        r_course.rotation = 'yes';
                        r_course.rotation_count = rorElement.rotation_count;
                        allRCourses.push(r_course);
                    });
                });
            }
            return allRCourses;
        };
        PCalenders.forEach((PCalender, pcIndex) => {
            PCalender.level.forEach((level, levelIndex) => {
                const tempCourse = [];
                if (level.rotation === 'yes') {
                    level.course = getRCourses(level.rotation_course);
                    delete PCalenders[pcIndex].level[levelIndex].rotation_course;
                }
                level.course.forEach((course) => {
                    if (courseIds.length) {
                        if (courseIds.includes(cs(course._course_id))) {
                            const courseIndex = courseDatas.findIndex(
                                (ele) => cs(ele._id) === cs(course._course_id),
                            );
                            if (
                                courseDatas[courseIndex].level_no === level.level_no &&
                                courseDatas[courseIndex].term === level.term
                            )
                                tempCourse.push(course);
                        }
                    } else if (programIdResult.isAdmin) tempCourse.push(course);
                });
                level.course = tempCourse;
            });
        });
        // return res.send(PCalenders);
        // merge existing DSetting value
        const DS_Query = { _user_id: ObjectId(user_id) };
        const DS_Project = {};
        const DSettings = (await getJSON(DSetting, DS_Query, DS_Project)).data;
        const getIsConfigured = (_level_id, _program_id, _course_id) => {
            let returnable = false;
            DSettings.forEach((DSetting) => {
                DSetting.settings.forEach((setting) => {
                    if (
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._level_id) === cs(_level_id) &&
                        cs(setting._course_id) === cs(_course_id)
                    ) {
                        returnable = setting.isConfigured;
                    }
                });
            });
            return returnable;
        };
        // return res.send(courseDatas);
        const userCourses = [];
        const userCourseIds = [];
        PCalenders.forEach((PCalender) => {
            PCalender.isConfigured = false;
            PCalender.level.forEach((level) => {
                PCalender.program_name = getProgramName(level._program_id);
                level.isConfigured = false;
                // level.course =
                //     level.rotation == 'no' ? level.course : getRCourses(level.rotation_course);
                level.course.forEach((course) => {
                    const courseScheduled =
                        course.rotation && course.rotation === 'yes'
                            ? courseDatas.filter(
                                  (ele) =>
                                      cs(ele._id) === cs(course._course_id) &&
                                      ele.rotation_count === course.rotation_count,
                              )
                            : courseDatas.filter(
                                  (ele) =>
                                      cs(ele._id) === cs(course._course_id) &&
                                      ele.term === level.term,
                              );
                    const courseScheduleCompleted = courseScheduled.filter(
                        (ele) => ele.status && ele.status === COMPLETED && ele.term === level.term,
                    );
                    let sessionIds = courseScheduled.map((csElement) => csElement._session_id);
                    sessionIds = sessionIds.filter(
                        (item, index) =>
                            sessionIds.findIndex(
                                (itm) => itm && item && itm.toString() === item.toString(),
                            ) === index,
                    );
                    course.session_count = sessionIds.length;
                    course.total_session = courseScheduled.length;
                    course.completed_session = courseScheduleCompleted.length;
                    // Need to get Finale Warning & Denial count from LMS
                    course.final_warning = 5;
                    course.denial = 2;
                    course.term = level.term;
                    userCourses.push(course);
                    userCourseIds.push(course._course_id);
                    course.isConfigured = getIsConfigured(
                        level._id,
                        level._program_id,
                        course._course_id,
                    );
                    if (course.isConfigured) level.isConfigured = true;
                });
                if (level.isConfigured) PCalender.isConfigured = true;
            });
        });
        const DeptSub_Query = {
            isDeleted: false,
            isActive: true,
        };
        const DeptSub_Project = {
            _id: 1,
            isActive: 1,
            department_name: 1,
            program_id: 1,
            program_name: 1,
            shared_with: 1,
            'subject._id': 1,
            'subject.subject_name': 1,
            'subject.shared_with': 1,
            'subject.isDeleted': 1,
        };
        let DeptSubs = (await getJSON(DeptSubject, DeptSub_Query, DeptSub_Project)).data;
        // get courses for Department and subjects
        const C_Query = {
            isDeleted: false,
            isActive: true,
            _id: { $in: userCourseIds },
        };
        const C_Project = {
            _id: 1,
            course_code: 1,
            course_name: 1,
            duration: 1,
            course_type: 1,
            _program_id: 1,
            administration: 1,
            participating: 1,
        };
        const Courses = (await getJSON(Course, C_Query, C_Project)).data;
        // prepare department and subject with course
        const mergeUserCourse = (course) => {
            const sCourse = userCourses.filter(
                (uCourse) => cs(uCourse._course_id) === cs(course._id),
            );
            const pcDatas = PCalenders.filter(
                (ele) => cs(ele._program_id) === cs(course._program_id),
            );
            let crsData = {};
            for (levelData of pcDatas[0].level) {
                const courseData = levelData.course.map((ele) => cs(ele._course_id));
                if (courseData.indexOf(cs(course._id)) !== -1) {
                    crsData = {
                        program_name: pcDatas[0].program_name,
                        curriculums: levelData.curriculum,
                        year: levelData.year,
                        level_no: levelData.level_no,
                    };
                }
            }
            if (sCourse.length !== 1) {
                const rotationCourse = [];
                sCourse.forEach((crs) => {
                    delete crs._id;
                    delete crs.course_name;
                    rotationCourse.push({ ...crs, ...course, ...crsData });
                });
                return rotationCourse;
            }
            delete sCourse[0]._id;
            delete sCourse[0].course_name;
            course = { ...sCourse[0], ...course, ...crsData };
            return course;
            /* const sCourse = userCourses.find(
                (uCourse) => cs(uCourse._course_id) === cs(course._id),
            );
            const pcDatas = PCalenders.filter(
                (ele) => cs(ele._program_id) === cs(course._program_id),
            );
            let crsData = {};
            for (levelData of pcDatas[0].level) {
                const courseData = levelData.course.map((ele) => cs(ele._course_id));
                if (courseData.indexOf(cs(course._id)) !== -1) {
                    crsData = {
                        program_name: pcDatas[0].program_name,
                        curriculums: levelData.curriculum,
                        year: levelData.year,
                        level_no: levelData.level_no,
                    };
                }
            }
            if (sCourse) {
                delete sCourse._id;
                delete sCourse.course_name;
                course = { ...sCourse, ...course, ...crsData };
                return course;
            } */
        };

        // Getting Course Schedule Staff Lists
        const cs_project = { _program_id: 1, year_no: 1, level_no: 1, _course_id: 1, staffs: 1 };
        const scheduleData = await base_control.get_list(
            CourseSchedule,
            { _institution_calendar_id: ObjectId(inst_cal_id), isDeleted: false, isActive: true },
            cs_project,
        );
        const courseStaff = [];
        if (scheduleData.status) {
            for (const csElement of scheduleData.data) {
                const loc = courseStaff.findIndex(
                    (ele) =>
                        ele &&
                        ele._course_id &&
                        ele._course_id.toString() === csElement._course_id.toString(),
                );
                if (loc === -1) {
                    courseStaff.push(csElement);
                } else {
                    courseStaff[loc].staffs = [...courseStaff[loc].staffs, ...csElement.staffs];
                }
            }
            for (cElement of courseStaff) {
                cElement.staffs = cElement.staffs.filter(
                    (item, index) =>
                        cElement.staffs.findIndex(
                            (ele) => ele._staff_id.toString() === item._staff_id.toString(),
                        ) === index,
                );
            }
        }
        let adminCourseIds = [];
        const getCourse = (programId, deptId, subject_id) => {
            SelectedCourses = [];
            Courses.forEach((course) => {
                if (cs(course._program_id) === cs(programId)) {
                    // const courseScheduled = courseDatas.filter(
                    //     (ele) => cs(ele._id) === cs(course._id),
                    // );
                    // let scheduleTotal = 0;
                    // let completedSession = 0;
                    // courseScheduled.forEach((ele) => {
                    //     if (programIdResult.isAdmin) {
                    //         scheduleTotal++;
                    //         if (ele.status && ele.status ===COMPLETED) completedSession++;
                    //     } else if (ele._subject_id.indexOf(cs(subject_id)) !== -1) {
                    //         scheduleTotal++;
                    //         if (ele.status && ele.status ===COMPLETED) completedSession++;
                    //     }
                    // });
                    const staffLoc = courseStaff.findIndex(
                        (ele) =>
                            ele._program_id.toString() === programId.toString() &&
                            ele._course_id.toString() === course._id.toString(),
                    );
                    course.staffs = staffLoc !== -1 ? courseStaff[staffLoc].staffs : [];
                    // course.total_session = scheduleTotal;
                    // course.completed_session = completedSession;
                    if (
                        course.administration &&
                        cs(course.administration._subject_id) === cs(subject_id)
                    ) {
                        course.AdminCourse = true;
                        course.participatingCourse = false;
                        const mergedCourse = mergeUserCourse(course);
                        if (Array.isArray(mergedCourse)) {
                            mergedCourse.forEach((mergedCourseElement) => {
                                adminCourseIds.push(cs(mergedCourseElement._course_id));
                                SelectedCourses.push(mergedCourseElement);
                            });
                        } else {
                            adminCourseIds.push(cs(mergedCourse._course_id));
                            SelectedCourses.push(mergedCourse);
                        }
                        // adminCourseIds.push(cs(mergedCourse._course_id));
                        // SelectedCourses.push(mergedCourse);
                    } else {
                        if (course.participating) {
                            course.participating.forEach((participating) => {
                                if (
                                    participating &&
                                    cs(participating._subject_id) === cs(subject_id)
                                ) {
                                    course.AdminCourse = false;
                                    course.participatingCourse = true;
                                    const mergedCourse = mergeUserCourse(course);
                                    if (Array.isArray(mergedCourse)) {
                                        mergedCourse.forEach((mergedCourseElement) => {
                                            SelectedCourses.push(mergedCourseElement);
                                        });
                                    } else {
                                        SelectedCourses.push(mergedCourse);
                                    }
                                    // const mergedCourse = mergeUserCourse(course);
                                    // SelectedCourses.push(mergedCourse);
                                }
                            });
                        }
                    }
                }
            });
            return SelectedCourses;
        };
        const shared_subject = [];
        const shared_depart = [];
        DeptSubs.forEach((deptShare) => {
            deptShare.shared = false;
            const subject_data = deptShare.subject.filter((ele) => ele.isDeleted == false);
            for (element of subject_data) {
                if (element.shared_with && element.shared_with.length != 0) {
                    shared_subject.push({
                        program_id: deptShare.program_id,
                        program_name: deptShare.program_name,
                        department_id: deptShare._id,
                        department_name: deptShare.department_name,
                        subject_id: element._id,
                        subject_name: element.subject_name,
                        shared_with: element.shared_with,
                        shared: false,
                        isDeleted: element.isDeleted,
                    });
                }
            }
            if (
                programIds.findIndex(
                    (ele) => ele && ele.toString() === deptShare.program_id.toString(),
                ) !== -1
            ) {
                deptShare.subject.forEach((ele2) => {
                    ele2.shared = false;
                });
                deptShare.shared_with.forEach((ele3) => {
                    // deptShare.shared = true;
                    shared_depart.push({
                        _id: deptShare._id,
                        isActive: deptShare.isActive,
                        program_id: ele3.program_id,
                        program_name: ele3.program_name,
                        // program_id: deptShare.program_id,
                        // program_name: deptShare.program_name,
                        shared_from_program_id: deptShare.program_id,
                        shared_from_program_name: deptShare.program_name,
                        department_name: deptShare.department_name,
                        subject: deptShare.subject,
                        shared_with: deptShare.shared_with,
                        shared: true,
                    });
                });
            }
        });
        DeptSubs = [...DeptSubs, ...shared_depart];
        DeptSubs.forEach((deptShare) => {
            for (element of shared_subject) {
                const datas = deptShare;
                if (
                    element.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                )
                    deptShare.subject.push({
                        _id: element.subject_id,
                        subject_name: element.subject_name,
                        subject_shared_from_program_id: element.program_id,
                        subject_shared_from_program_name: element.program_name,
                        isDeleted: element.isDeleted,
                        shared: true,
                    });
            }
        });
        let scheduledSubject = [];
        courseDatas.forEach((ele) => {
            scheduledSubject = scheduledSubject.concat(ele._subject_id);
        });
        scheduledSubject = [...new Set(scheduledSubject)];
        const subjectCourses = JSON.parse(JSON.stringify(DeptSubs));
        DeptSubs.forEach((DeptSub, deptIndex) => {
            adminCourseIds = [];
            DeptSub.subject.forEach((subject, subIndex) => {
                if (programIdResult.isAdmin) {
                    subjectCourses[deptIndex].subject[subIndex].courses = getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                } else if (scheduledSubject.indexOf(cs(subject._id)) !== -1) {
                    subjectCourses[deptIndex].subject[subIndex].courses = getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                } else {
                    subjectCourses[deptIndex].subject[subIndex].courses = [];
                }
            });
        });
        // merge existing DeptSetting vale
        const DeptS_Query = { _user_id: ObjectId(user_id) };
        const DeptS_Project = {};
        const DeptSettings = (await getJSON(DeptSetting, DeptS_Query, DeptS_Project)).data;
        const getIsDeptConfigured = (_department_id, _subject_id, _program_id, _course_id) => {
            let returnable = false;
            DeptSettings.forEach((DeptSetting) => {
                DeptSetting.settings.forEach((setting) => {
                    if (
                        cs(setting._department_id) === cs(_department_id) &&
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._subject_id) === cs(_subject_id) &&
                        cs(setting._course_id) === cs(_course_id)
                    ) {
                        returnable = setting.isConfigured;
                    }
                });
            });
            return returnable;
        };

        const subjectShared = [];
        subjectCourses.forEach((DeptSub, departIndex) => {
            DeptSub.isConfigured = false;
            DeptSub.subject.forEach((subject, subjectIndex) => {
                subject.isConfigured = false;
                subject.courses.forEach((course, courseIndex) => {
                    course.isConfigured = getIsDeptConfigured(
                        DeptSub._id,
                        subject._id,
                        DeptSub.program_id,
                        course._course_id,
                    );
                    if (course.isConfigured) subject.isConfigured = true;
                    delete subjectCourses[departIndex].subject[subjectIndex].courses[courseIndex]
                        .participating;
                    delete subjectCourses[departIndex].subject[subjectIndex].courses[courseIndex]
                        .administration;
                });
                if (subject.isConfigured) DeptSub.isConfigured = true;
            });
            const sharedSubjects = JSON.parse(
                JSON.stringify(DeptSub.subject.filter((ele) => ele.subject_shared_from_program_id)),
            );
            if (sharedSubjects.length !== 0)
                subjectShared.push({
                    _id: DeptSub._id,
                    isActive: DeptSub.isActive,
                    program_id: DeptSub.program_id,
                    program_name: DeptSub.program_name,
                    department_name: DeptSub.department_name,
                    subject: sharedSubjects,
                    shared_with: DeptSub.shared_with,
                    shared: DeptSub.shared,
                    isConfigured: DeptSub.isConfigured,
                });
        });

        //Get Subject Shared Details
        subjectShared.forEach((sharedElement) => {
            sharedElement.subject.forEach((sharedSubjectElement) => {
                const sharedPrograms = JSON.parse(
                    JSON.stringify(
                        subjectCourses.filter(
                            (ele) =>
                                ele.program_id.toString() ===
                                    sharedSubjectElement.subject_shared_from_program_id.toString() &&
                                ele.subject.some(
                                    (subjectShareElement) =>
                                        subjectShareElement._id.toString() ===
                                        sharedSubjectElement._id.toString(),
                                ),
                        ),
                    ),
                );
                sharedPrograms.forEach((ele1) => {
                    const deptLoc = subjectCourses.findIndex(
                        (ele2) => ele2._id.toString() === ele1._id.toString(),
                    );
                    const subLoc = subjectCourses[deptLoc].subject.findIndex(
                        (ele3) => ele3._id.toString() === sharedSubjectElement._id.toString(),
                    );
                    subjectCourses[deptLoc].subject[subLoc].courses = [
                        ...subjectCourses[deptLoc].subject[subLoc].courses,
                        ...sharedSubjectElement.courses,
                    ];
                });
            });
        });
        const sharedWith = JSON.parse(
            JSON.stringify(subjectCourses.filter((ele) => ele.shared_from_program_name)),
        );
        sharedWith.forEach((departmentElement) => {
            const shareInd = subjectCourses.findIndex(
                (ele2) => ele2._id.toString() === departmentElement._id.toString(),
            );
            if (shareInd !== -1) {
                departmentElement.subject.forEach((subElement) => {
                    if (subElement.courses.length !== 0) {
                        const subShareInd = subjectCourses[shareInd].subject.findIndex(
                            (ele3) => ele3._id.toString() === subElement._id.toString(),
                        );
                        if (subShareInd !== -1) {
                            subElement.courses.forEach((element) => {
                                element._program_id = subjectCourses[shareInd].program_id;
                                subjectCourses[shareInd].subject[subShareInd].courses.push(element);
                            });
                        }
                    }
                });
            }
        });
        // send Response
        return res.status(200).send(
            common_files.response_function(res, 200, true, req.t('DATA_RETRIEVED'), {
                PCalenders,
                DeptSubs: subjectCourses,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), error));
    }
};

exports.getConfigureSettingList = async (req, res) => {
    // const { user_id, inst_cal_id, role_id } = req.params;
    try {
        const {
            headers: { _institution_id },
            params: { user_id, inst_cal_id, role_id },
        } = req;

        // LMS Setting Data
        const lmsData = await lmsStudentSettingData(convertToMongoObjectId(_institution_id));

        // get All Programs ids of given user
        const programIdResult = await getProgramIds(user_id, role_id);
        if (!programIdResult.status)
            return sendResponse(res, 404, false, req.t('USER_RECORD_NOT_FOUND'), []);

        if (programIdResult.roleName == 'Course Coordinator') {
            programIdResult.data = await getCourseCoordinatorProgramIDs(user_id);
        }
        const programIds = programIdResult.data.program_ids;
        const term = programIdResult.data.term;
        let courseDatas = [];
        courseDatas = (
            await getCourseIds(user_id, programIdResult.isAdmin, programIdResult.roleName)
        ).data;
        if (programIds.length === 0)
            return sendResponse(res, 404, false, req.t('PROGRAM_NOT_FOUND'), []);
        // get All Program Names
        const p_query = { isDeleted: false, isActive: true, _id: { $in: programIds } };
        const p_project = { _id: 1, name: 1 };
        const programs = (await getJSON(Program, p_query, p_project)).data;
        const getProgramName = (program_id) => {
            let returnableProgramName;
            programs.forEach((program) => {
                if (cs(program_id) === cs(program._id)) {
                    returnableProgramName = program.name;
                }
            });
            return returnableProgramName;
        };

        // get All Program calender
        pc_query = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: ObjectId(inst_cal_id),
            _program_id: { $in: programIds },
        };
        let courseIds = [];
        if (!programIdResult.isAdmin && courseDatas.length) {
            courseIds = courseDatas.map((courseElement) => cs(courseElement._id));
            courseIds = [...new Set(courseIds)];
            pc_query.$or = [
                { 'level.course._course_id': { $in: courseIds } },
                { 'level.rotation_course.course._course_id': { $in: courseIds } },
            ];
        }
        pc_project = {
            _id: 1,
            _program_id: 1,
            isActive: 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.course._course_id': 1,
            'level.course.courses_name': 1,
            'level.course.start_date': 1,
            'level.course.end_date': 1,
            'level.course.courses_number': 1,
            'level.course.model': 1,
            'level.course.credit_hours': 1,
            'level._id': 1,
            'level.curriculum': 1,
            'level.term': 1,
            'level.year': 1,
            'level.level_no': 1,
            'level._program_id': 1,
            'level.rotation_course.rotation_count': 1,
            'level.rotation_course.course._course_id': 1,
            'level.rotation_course.course.courses_name': 1,
            'level.rotation_course.course.start_date': 1,
            'level.rotation_course.course.end_date': 1,
            'level.rotation_course.course.courses_number': 1,
            'level.rotation_course.course.model': 1,
            'level.rotation_course.course.credit_hours': 1,
            'level.rotation': 1,
        };
        const PCalenders = (await getJSON(PCalender, pc_query, pc_project)).data;
        const getRCourses = (rotation_course) => {
            const allRCourses = [];
            if (rotation_course.length) {
                rotation_course.forEach((rorElement) => {
                    rorElement.course.forEach((r_course) => {
                        r_course.rotation = 'yes';
                        r_course.rotation_count = rorElement.rotation_count;
                        allRCourses.push(r_course);
                    });
                });
            }
            return allRCourses;
        };
        // return res.send(PCalenders);
        PCalenders.forEach((PCalender, pcIndex) => {
            PCalender.level.forEach((level, levelIndex) => {
                const tempCourse = [];
                if (level.rotation === 'yes') {
                    level.course = getRCourses(level.rotation_course);
                    delete PCalenders[pcIndex].level[levelIndex].rotation_course;
                }
                level.course.forEach((course) => {
                    if (courseIds.length) {
                        if (
                            courseIds.includes(cs(course._course_id)) &&
                            term.includes(level.term)
                        ) {
                            // const courseIndex = courseDatas.findIndex(
                            //     (ele) => cs(ele._id) === cs(course._course_id),
                            // );
                            // if (
                            //     courseDatas[courseIndex].level_no === level.level_no &&
                            //     courseDatas[courseIndex].term === level.term
                            // )
                            tempCourse.push(course);
                        }
                    } else if (programIdResult.isAdmin) tempCourse.push(course);
                });
                level.course = tempCourse;
            });
        });
        // return res.send(PCalenders);
        // merge existing DSetting value
        const DS_Query = { _user_id: ObjectId(user_id) };
        const DS_Project = {};
        const DSettings = (await getJSON(DSetting, DS_Query, DS_Project)).data;
        const getIsConfigured = (_level_id, _program_id, _course_id) => {
            let returnable = false;
            DSettings.forEach((DSetting) => {
                DSetting.settings.forEach((setting) => {
                    if (
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._level_id) === cs(_level_id) &&
                        cs(setting._course_id) === cs(_course_id)
                    ) {
                        returnable = setting.isConfigured;
                    }
                });
            });
            return returnable;
        };
        // return res.send(courseDatas);

        const sessionDeliveryTypes = await base_control.get_list(
            session_delivery_type,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: { $in: programIds },
            },
            {},
        );
        const sessionOrder = await base_control.get_list(
            session_order,
            {
                ...common_files.query,
                _institution_id: convertToMongoObjectId(_institution_id),
                // _course_id: { $in: courseIds },
            },
            {
                _program_id: 1,
                _course_id: 1,
                'session_flow_data._id': 1,
                'session_flow_data.duration': 1,
            },
        );

        //Student group data
        const studentGroupData = await base_control.get_list(
            student_group,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(inst_cal_id),
            },
            {
                'master._program_id': 1,
                'groups.term': 1,
                'groups.year': 1,
                'groups.level': 1,
                'groups.rotation': 1,
                'groups.students': 1,
                'groups.courses._course_id': 1,
                'groups.courses.student_absence_percentage': 1,
                'groups.courses.setting': 1,
            },
        );
        const LevelCourses = [];
        if (!studentGroupData.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_CREATED'),
                        req.t('STUDENT_GROUP_NOT_CREATED'),
                    ),
                );
        for (sgElement of studentGroupData.data) {
            for (sgLevel of sgElement.groups) {
                for (sgCourse of sgLevel.courses) {
                    if (sgLevel.rotation === 'yes') {
                        const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                        for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                            let studentIds = [];
                            for (sgSetting of sgCourse.setting) {
                                if (
                                    sgSetting &&
                                    sgSetting._group_no.toString() === i.toString() &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups
                                ) {
                                    for (sgSession of sgSetting.session_setting[0].groups) {
                                        studentIds = [...studentIds, ...sgSession._student_ids];
                                    }
                                    studentIds = studentIds.filter(
                                        (ele, index) =>
                                            studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                }
                            }
                            const courseScheduled = clone(
                                courseDatas.filter(
                                    (ele) =>
                                        ele._id.toString() === sgCourse._course_id.toString() &&
                                        ele.term === sgLevel.term &&
                                        ele.level_no === sgLevel.level &&
                                        ele.rotation === 'yes' &&
                                        ele.rotation_count &&
                                        ele.rotation_count.toString() === i.toString(),
                                ),
                            );
                            //Warning Calculations
                            if (
                                lmsData.warningAbsenceData[0] &&
                                sgCourse.student_absence_percentage &&
                                sgCourse.student_absence_percentage !== 0
                            ) {
                                lmsData.warningAbsenceData[0].absence_percentage =
                                    sgCourse.student_absence_percentage;
                            }
                            const studentReport = studentAttendanceReport(
                                studentIds,
                                courseScheduled,
                                lmsData.warningAbsenceData,
                            );
                            // Course Schedule Status
                            const courseSessionOrder = sessionOrder.data.find(
                                (ele) =>
                                    ele._course_id.toString() === sgCourse._course_id.toString(),
                            );
                            const sessionOrderData = courseSessionOrder
                                ? courseSessionOrder.session_flow_data
                                : [];
                            const courseScheduleStatus = courseCreditContactHours(
                                sessionDeliveryTypes.data.filter(
                                    (ele) =>
                                        ele._program_id.toString() ===
                                        sgElement.master._program_id.toString(),
                                ),
                                courseScheduled,
                                sessionOrderData,
                            );
                            LevelCourses.push({
                                _program_id: sgElement.master._program_id,
                                level: sgLevel.level,
                                term: sgLevel.term,
                                rotation: sgLevel.rotation,
                                rotation_count: i,
                                _course_id: sgCourse._course_id,
                                final_warning: lmsData.finaleWarning
                                    ? studentReport.filter(
                                          (ele) => ele.warning === lmsData.finaleWarning,
                                      ).length
                                    : 0,
                                denial: studentReport.filter(
                                    (ele) => ele.warning === lmsData.denialWarning,
                                ).length,
                                courseScheduleStatus,
                            });
                        }
                    } else {
                        let studentIds = [];
                        for (sgSetting of sgCourse.setting) {
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            ) {
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                            }
                        }
                        const courseScheduled = clone(
                            courseDatas.filter(
                                (ele) =>
                                    ele._id.toString() === sgCourse._course_id.toString() &&
                                    ele.term === sgLevel.term &&
                                    ele.rotation === 'no' &&
                                    ele.level_no === sgLevel.level,
                            ),
                        );
                        //Warning Calculations
                        if (
                            lmsData.warningAbsenceData[0] &&
                            sgCourse.student_absence_percentage &&
                            sgCourse.student_absence_percentage !== 0
                        ) {
                            lmsData.warningAbsenceData[0].absence_percentage =
                                sgCourse.student_absence_percentage;
                        }
                        const studentReport = studentAttendanceReport(
                            studentIds,
                            courseScheduled,
                            lmsData.warningAbsenceData,
                        );
                        // Course Schedule Status
                        const courseSessionOrder = sessionOrder.data.find(
                            (ele) => ele._course_id.toString() === sgCourse._course_id.toString(),
                        );
                        const sessionOrderData = courseSessionOrder
                            ? courseSessionOrder.session_flow_data
                            : [];
                        const courseScheduleStatus = courseCreditContactHours(
                            sessionDeliveryTypes.data.filter(
                                (ele) =>
                                    ele._program_id.toString() ===
                                    sgElement.master._program_id.toString(),
                            ),
                            courseScheduled,
                            sessionOrderData,
                        );
                        LevelCourses.push({
                            _program_id: sgElement.master._program_id,
                            level: sgLevel.level,
                            term: sgLevel.term,
                            rotation: sgLevel.rotation,
                            _course_id: sgCourse._course_id,
                            final_warning: lmsData.finaleWarning
                                ? studentReport.filter(
                                      (ele) => ele.warning === lmsData.finaleWarning,
                                  ).length
                                : 0,
                            denial: studentReport.filter(
                                (ele) => ele.warning === lmsData.denialWarning,
                            ).length,
                            courseScheduleStatus,
                        });
                    }
                }
            }
        }
        // return res.send(LevelCourses);
        // get courses for Department and subjects
        const C_Query = {
            isDeleted: false,
            isActive: true,
            // _id: { $in: userCourseIds },
        };
        const C_Project = {
            _id: 1,
            course_code: 1,
            course_name: 1,
            duration: 1,
            course_type: 1,
            _program_id: 1,
            administration: 1,
            participating: 1,
            'course_assigned_details._program_id': 1,
            'course_assigned_details.program_name': 1,
        };
        const Courses = (await getJSON(Course, C_Query, C_Project)).data;
        const userCourses = [];
        const userCourseIds = [];
        PCalenders.forEach((PCalender) => {
            PCalender.isConfigured = false;
            PCalender.level.forEach((level) => {
                PCalender.program_name = getProgramName(level._program_id);
                level.isConfigured = false;
                // level.course =
                //     level.rotation == 'no' ? level.course : getRCourses(level.rotation_course);
                level.course.forEach((course) => {
                    // const courseScheduled =
                    //     course.rotation && course.rotation === 'yes'
                    //         ? courseDatas.filter(
                    //               (ele) =>
                    //                   cs(ele._id) === cs(course._course_id) &&
                    //                   ele.rotation_count === course.rotation_count,
                    //           )
                    //         : courseDatas.filter(
                    //               (ele) =>
                    //                   cs(ele._id) === cs(course._course_id) &&
                    //                   ele.term === level.term,
                    //           );
                    // const courseScheduleCompleted = courseScheduled.filter(
                    //     (ele) => ele.status && ele.status === COMPLETED && ele.term === level.term,
                    // );
                    // let sessionIds = courseScheduled.map((csElement) => csElement._session_id);
                    // sessionIds = sessionIds.filter(
                    //     (item, index) =>
                    //         sessionIds.findIndex(
                    //             (itm) => itm && item && itm.toString() === item.toString(),
                    //         ) === index,
                    // );
                    // course.session_count = sessionIds.length;
                    // course.total_session = courseScheduled.length;
                    // course.completed_session = courseScheduleCompleted.length;
                    // Need to get Finale Warning & Denial count from LMS
                    // const levelCourseData = LevelCourses.find(
                    //     (ele) =>
                    //         ele._program_id.toString() === level._program_id.toString() &&
                    //         ele.level === level.level_no &&
                    //         ele.term === level.term &&
                    //         ((course.rotation &&
                    //             course.rotation_count &&
                    //             course.rotation === 'yes' &&
                    //             course.rotation_count === ele.rotation_count &&
                    //             ele._course_id.toString() === course._course_id.toString()) ||
                    //             ele._course_id.toString() === course._course_id.toString()),
                    // );
                    const levelCourseData =
                        course.rotation && course.rotation_count && course.rotation === 'yes'
                            ? LevelCourses.find(
                                  (ele) =>
                                      ele._program_id.toString() === level._program_id.toString() &&
                                      ele.level === level.level_no &&
                                      ele.term === level.term &&
                                      course.rotation_count === ele.rotation_count &&
                                      ele._course_id.toString() === course._course_id.toString(),
                              )
                            : LevelCourses.find(
                                  (ele) =>
                                      ele._program_id.toString() === level._program_id.toString() &&
                                      ele.level === level.level_no &&
                                      ele.term === level.term &&
                                      ele._course_id.toString() === course._course_id.toString(),
                              );

                    course.final_warning = levelCourseData ? levelCourseData.final_warning : 0;
                    course.denial = levelCourseData ? levelCourseData.denial : 0;
                    const courseScheduleStatus = levelCourseData
                        ? levelCourseData.courseScheduleStatus
                        : { session_count: 0, complete: 0 };
                    course.session_count = courseScheduleStatus.session_count;
                    course.total_session = courseScheduleStatus.session_count;
                    course.completed_session = courseScheduleStatus.complete;
                    course.term = level.term;
                    course.program_name = PCalender.program_name;
                    course._program_id = level._program_id;
                    course.year = level.year;
                    course.level_no = level.level_no;
                    course.curriculums = level.curriculum;
                    course.rotation = level.rotation;

                    const sCourse = clone(
                        Courses.find((uCourse) => cs(uCourse._id) === cs(course._course_id)),
                    );
                    course.course_shared =
                        sCourse._program_id.toString() !== course._program_id.toString();
                    course.course_shared_program = course.course_shared
                        ? sCourse.course_assigned_details.find(
                              (cad) =>
                                  cad._program_id.toString() === sCourse._program_id.toString(),
                          ).program_name
                        : '';
                    userCourses.push(course);
                    userCourseIds.push(course._course_id);
                    course.isConfigured = getIsConfigured(
                        level._id,
                        level._program_id,
                        course._course_id,
                    );
                    if (course.isConfigured) level.isConfigured = true;
                });
                if (level.isConfigured) PCalender.isConfigured = true;
            });
        });
        const DeptSub_Query = {
            isDeleted: false,
            isActive: true,
        };
        const DeptSub_Project = {
            _id: 1,
            isActive: 1,
            department_name: 1,
            program_id: 1,
            program_name: 1,
            shared_with: 1,
            'subject._id': 1,
            'subject.subject_name': 1,
            'subject.shared_with': 1,
            'subject.isDeleted': 1,
        };
        let DeptSubs = (await getJSON(DeptSubject, DeptSub_Query, DeptSub_Project)).data;
        // prepare department and subject with course
        const mergeUserCourse = (course) => {
            const sCourse = userCourses.filter(
                (uCourse) => cs(uCourse._course_id) === cs(course._id),
            );
            if (sCourse.length !== 1) {
                const rotationCourse = [];
                sCourse.forEach((crs) => {
                    delete crs._id;
                    delete crs.course_name;
                    rotationCourse.push({ ...crs, ...course /* , ...crsData */ });
                });
                return rotationCourse;
            }
            delete sCourse[0]._id;
            delete sCourse[0].course_name;
            course = { ...sCourse[0], ...course /* , ...crsData */ };
            return course;
        };

        // Getting Course Schedule Staff Lists
        const cs_project = { _program_id: 1, year_no: 1, level_no: 1, _course_id: 1, staffs: 1 };
        const scheduleData = await base_control.get_list(
            CourseSchedule,
            { _institution_calendar_id: ObjectId(inst_cal_id), isDeleted: false, isActive: true },
            cs_project,
        );
        const courseStaff = [];
        if (scheduleData.status) {
            for (const csElement of scheduleData.data) {
                const loc = courseStaff.findIndex(
                    (ele) =>
                        ele &&
                        ele._course_id &&
                        ele._course_id.toString() === csElement._course_id.toString(),
                );
                if (loc === -1) {
                    courseStaff.push(csElement);
                } else {
                    courseStaff[loc].staffs = [...courseStaff[loc].staffs, ...csElement.staffs];
                }
            }
            for (cElement of courseStaff) {
                cElement.staffs = cElement.staffs.filter(
                    (item, index) =>
                        cElement.staffs.findIndex(
                            (ele) => ele._staff_id.toString() === item._staff_id.toString(),
                        ) === index,
                );
            }
        }
        let adminCourseIds = [];
        const getCourse = (programId, deptId, subject_id) => {
            SelectedCourses = [];
            Courses.forEach((course) => {
                if (cs(course._program_id) === cs(programId)) {
                    const staffLoc = courseStaff.findIndex(
                        (ele) =>
                            ele._program_id.toString() === programId.toString() &&
                            ele._course_id.toString() === course._id.toString(),
                    );
                    course.staffs = staffLoc !== -1 ? courseStaff[staffLoc].staffs : [];
                    if (
                        course.administration &&
                        cs(course.administration._subject_id) === cs(subject_id)
                    ) {
                        course.AdminCourse = true;
                        course.participatingCourse = false;
                        const mergedCourse = mergeUserCourse(course);
                        if (Array.isArray(mergedCourse)) {
                            mergedCourse.forEach((mergedCourseElement) => {
                                adminCourseIds.push(cs(mergedCourseElement._course_id));
                                SelectedCourses.push(mergedCourseElement);
                            });
                        } else {
                            adminCourseIds.push(cs(mergedCourse._course_id));
                            SelectedCourses.push(mergedCourse);
                        }
                    } else {
                        if (course.participating) {
                            course.participating.forEach((participating) => {
                                if (
                                    participating &&
                                    cs(participating._subject_id) === cs(subject_id)
                                ) {
                                    course.AdminCourse = false;
                                    course.participatingCourse = true;
                                    const mergedCourse = mergeUserCourse(course);
                                    if (Array.isArray(mergedCourse)) {
                                        mergedCourse.forEach((mergedCourseElement) => {
                                            SelectedCourses.push(mergedCourseElement);
                                        });
                                    } else {
                                        SelectedCourses.push(mergedCourse);
                                    }
                                }
                            });
                        }
                    }
                }
            });
            return SelectedCourses;
        };
        const shared_subject = [];
        const shared_depart = [];
        DeptSubs.forEach((deptShare) => {
            deptShare.shared = false;
            const subject_data = deptShare.subject.filter((ele) => ele.isDeleted == false);
            for (element of subject_data) {
                if (element.shared_with && element.shared_with.length != 0) {
                    shared_subject.push({
                        program_id: deptShare.program_id,
                        program_name: deptShare.program_name,
                        department_id: deptShare._id,
                        department_name: deptShare.department_name,
                        subject_id: element._id,
                        subject_name: element.subject_name,
                        shared_with: element.shared_with,
                        shared: false,
                        isDeleted: element.isDeleted,
                    });
                }
            }
            if (
                programIds.findIndex(
                    (ele) => ele && ele.toString() === deptShare.program_id.toString(),
                ) !== -1
            ) {
                deptShare.subject.forEach((ele2) => {
                    ele2.shared = false;
                });
                deptShare.shared_with.forEach((ele3) => {
                    // deptShare.shared = true;
                    shared_depart.push({
                        _id: deptShare._id,
                        isActive: deptShare.isActive,
                        program_id: ele3.program_id,
                        program_name: ele3.program_name,
                        department_share: true,
                        shared_from_program_id: deptShare.program_id,
                        shared_from_program_name: deptShare.program_name,
                        department_name: deptShare.department_name,
                        subject: deptShare.subject,
                        shared_with: deptShare.shared_with,
                        shared: true,
                    });
                });
            }
        });
        DeptSubs = [...DeptSubs, ...shared_depart];
        DeptSubs = clone(DeptSubs);
        DeptSubs.forEach((deptShare) => {
            for (element of shared_subject) {
                const datas = clone(deptShare);
                if (
                    !datas.department_share &&
                    element.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                ) {
                    deptShare.subject.push({
                        _id: element.subject_id,
                        subject_name: element.subject_name,
                        subject_shared_from_program_id: element.program_id,
                        subject_shared_from_program_name: element.program_name,
                        isDeleted: element.isDeleted,
                        shared: true,
                    });
                }
            }
        });
        // return res.send(DeptSubs);
        let scheduledSubject = [];
        courseDatas.forEach((ele) => {
            scheduledSubject = scheduledSubject.concat(ele._subject_id);
        });
        scheduledSubject = [...new Set(scheduledSubject)];
        const subjectCourses = JSON.parse(JSON.stringify(DeptSubs));
        DeptSubs.forEach((DeptSub, deptIndex) => {
            adminCourseIds = [];
            DeptSub.subject.forEach((subject, subIndex) => {
                if (programIdResult.isAdmin) {
                    subjectCourses[deptIndex].subject[subIndex].courses = getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                } else if (scheduledSubject.indexOf(cs(subject._id)) !== -1) {
                    subjectCourses[deptIndex].subject[subIndex].courses = getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                } else {
                    subjectCourses[deptIndex].subject[subIndex].courses = [];
                }
            });
        });
        // merge existing DeptSetting vale
        const DeptS_Query = { _user_id: ObjectId(user_id) };
        const DeptS_Project = {};
        const DeptSettings = (await getJSON(DeptSetting, DeptS_Query, DeptS_Project)).data;
        const getIsDeptConfigured = (_department_id, _subject_id, _program_id, _course_id) => {
            let returnable = false;
            DeptSettings.forEach((DeptSetting) => {
                DeptSetting.settings.forEach((setting) => {
                    if (
                        cs(setting._department_id) === cs(_department_id) &&
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._subject_id) === cs(_subject_id) &&
                        cs(setting._course_id) === cs(_course_id)
                    ) {
                        returnable = setting.isConfigured;
                    }
                });
            });
            return returnable;
        };

        const subjectShared = [];
        subjectCourses.forEach((DeptSub, departIndex) => {
            DeptSub.isConfigured = false;
            DeptSub.subject.forEach((subject, subjectIndex) => {
                subject.isConfigured = false;
                subject.courses.forEach((course, courseIndex) => {
                    course.isConfigured = getIsDeptConfigured(
                        DeptSub._id,
                        subject._id,
                        DeptSub.program_id,
                        course._course_id,
                    );
                    if (course.isConfigured) subject.isConfigured = true;
                    delete subjectCourses[departIndex].subject[subjectIndex].courses[courseIndex]
                        .participating;
                    delete subjectCourses[departIndex].subject[subjectIndex].courses[courseIndex]
                        .administration;
                });
                if (subject.isConfigured) DeptSub.isConfigured = true;
            });
            const sharedSubjects = JSON.parse(
                JSON.stringify(DeptSub.subject.filter((ele) => ele.subject_shared_from_program_id)),
            );
            if (sharedSubjects.length !== 0)
                subjectShared.push({
                    _id: DeptSub._id,
                    isActive: DeptSub.isActive,
                    program_id: DeptSub.program_id,
                    program_name: DeptSub.program_name,
                    department_name: DeptSub.department_name,
                    subject: sharedSubjects,
                    shared_with: DeptSub.shared_with,
                    shared: DeptSub.shared,
                    isConfigured: DeptSub.isConfigured,
                });
        });

        //Get Subject Shared Details
        subjectShared.forEach((sharedElement) => {
            sharedElement.subject.forEach((sharedSubjectElement) => {
                const sharedPrograms = JSON.parse(
                    JSON.stringify(
                        subjectCourses.filter(
                            (ele) =>
                                ele.program_id.toString() ===
                                    sharedSubjectElement.subject_shared_from_program_id.toString() &&
                                ele.subject.some(
                                    (subjectShareElement) =>
                                        subjectShareElement._id.toString() ===
                                        sharedSubjectElement._id.toString(),
                                ),
                        ),
                    ),
                );
                sharedPrograms.forEach((ele1) => {
                    const deptLoc = subjectCourses.findIndex(
                        (ele2) => ele2._id.toString() === ele1._id.toString(),
                    );
                    const subLoc = subjectCourses[deptLoc].subject.findIndex(
                        (ele3) => ele3._id.toString() === sharedSubjectElement._id.toString(),
                    );
                    subjectCourses[deptLoc].subject[subLoc].courses = [
                        ...subjectCourses[deptLoc].subject[subLoc].courses,
                        ...sharedSubjectElement.courses,
                    ];
                });
            });
        });
        const sharedWith = JSON.parse(
            JSON.stringify(subjectCourses.filter((ele) => ele.shared_from_program_name)),
        );
        sharedWith.forEach((departmentElement) => {
            const shareInd = subjectCourses.findIndex(
                (ele2) => ele2._id.toString() === departmentElement._id.toString(),
            );
            if (shareInd !== -1) {
                departmentElement.subject.forEach((subElement) => {
                    if (subElement.courses.length !== 0) {
                        const subShareInd = subjectCourses[shareInd].subject.findIndex(
                            (ele3) => ele3._id.toString() === subElement._id.toString(),
                        );
                        if (subShareInd !== -1) {
                            subElement.courses.forEach((element) => {
                                element._program_id = subjectCourses[shareInd].program_id;
                                subjectCourses[shareInd].subject[subShareInd].courses.push(element);
                            });
                        }
                    }
                });
            }
        });
        // send Response
        return res.status(200).send(
            common_files.response_function(res, 200, true, req.t('DATA_RETRIEVED'), {
                PCalenders,
                DeptSubs: subjectCourses,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), error));
    }
};

exports.updateConfigureSettingYearLevel = async (req, res) => {
    const { user_id, inst_cal_id, role_id } = req.params;
    const { flag } = req.body;
    try {
        const bulk = [];
        let existingSettings = [];
        const isExist = (_program_id, _level_id, _course_id, term, rotation_count) => {
            let isExist = false;
            existingSettings.forEach((setup) => {
                setup.settings.forEach((setting) => {
                    if (
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._level_id) === cs(_level_id) &&
                        cs(setting._course_id) === cs(_course_id) &&
                        cs(setting.term) === cs(term) &&
                        (rotation_count && setting.rotation_count
                            ? cs(setting.rotation_count) === cs(rotation_count)
                            : true)
                    ) {
                        isExist = true;
                    }
                });
            });
            return isExist;
        };
        const isExistDeptSetting = (
            _department_id,
            _program_id,
            _subject_id,
            _course_id,
            term,
            rotation_count,
        ) => {
            let isExist = false;
            existingSettings.forEach((setup) => {
                setup.settings.forEach((setting) => {
                    if (
                        cs(setting._department_id) === cs(_department_id) &&
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._subject_id) === cs(_subject_id) &&
                        cs(setting._course_id) === cs(_course_id) &&
                        cs(setting.term) === cs(term) &&
                        (rotation_count && setting.rotation_count
                            ? cs(setting.rotation_count) === cs(rotation_count)
                            : true)
                    ) {
                        isExist = true;
                    }
                });
            });
            return isExist;
        };
        if (flag === 'PCalenders') {
            const monitorUpdatable = [];
            for (monitorElement of req.body.updatable) {
                if (
                    !monitorUpdatable.find(
                        (updatableElement) =>
                            updatableElement.program_id.toString() ===
                                monitorElement.program_id.toString() &&
                            updatableElement.level_id.toString() ===
                                monitorElement.level_id.toString() &&
                            updatableElement.course_id.toString() ===
                                monitorElement.course_id.toString() &&
                            updatableElement.term.toString() === monitorElement.term.toString() &&
                            (monitorElement.rotation_count && updatableElement.rotation_count
                                ? updatableElement.rotation_count.toString() ===
                                  monitorElement.rotation_count.toString()
                                : true),
                    )
                )
                    monitorUpdatable.push(monitorElement);
            }
            existingSettings = (
                await getJSON(DSetting, {
                    _user_id: user_id,
                    _institution_calendar_id: inst_cal_id,
                    _role_id: role_id,
                })
            ).data;
            for (const iterator of monitorUpdatable) {
                const { program_id, level_id, course_id, checked, term, rotation_count } = iterator;
                const data = {
                    _user_id: user_id,
                    _institution_calendar_id: inst_cal_id,
                    _role_id: role_id,
                    settings: [
                        {
                            _program_id: program_id,
                            _level_id: level_id,
                            _course_id: course_id,
                            isConfigured: checked,
                            term,
                            rotation_count,
                        },
                    ],
                };
                if (
                    !isExist(cs(program_id), cs(level_id), cs(course_id), cs(term), rotation_count)
                ) {
                    bulk.push({ insertOne: { document: data } });
                } else {
                    bulk.push({
                        updateOne: {
                            filter: {
                                _user_id: user_id,
                                _institution_calendar_id: inst_cal_id,
                                _role_id: role_id,
                                'settings._program_id': program_id,
                                'settings._level_id': level_id,
                                'settings._course_id': course_id,
                                'settings.term': term,
                                'settings.rotation_count': rotation_count,
                            },
                            update: { $set: data },
                        },
                    });
                }
            }
            await DSetting.bulkWrite(bulk);
        } else {
            const monitorUpdatable = [];
            for (monitorElement of req.body.updatable) {
                if (
                    !monitorUpdatable.find(
                        (updatableElement) =>
                            updatableElement.program_id.toString() ===
                                monitorElement.program_id.toString() &&
                            updatableElement.department_id.toString() ===
                                monitorElement.department_id.toString() &&
                            updatableElement.subject_id.toString() ===
                                monitorElement.subject_id.toString() &&
                            updatableElement.course_id.toString() ===
                                monitorElement.course_id.toString() &&
                            updatableElement.term.toString() === monitorElement.term.toString() &&
                            (monitorElement.rotation_count && updatableElement.rotation_count
                                ? updatableElement.rotation_count.toString() ===
                                  monitorElement.rotation_count.toString()
                                : true),
                    )
                )
                    monitorUpdatable.push(monitorElement);
            }
            existingSettings = (
                await getJSON(DeptSetting, {
                    _user_id: user_id,
                    _institution_calendar_id: inst_cal_id,
                    _role_id: role_id,
                })
            ).data;
            for (const iterator of monitorUpdatable) {
                const {
                    department_id,
                    program_id,
                    subject_id,
                    course_id,
                    checked,
                    term,
                    rotation_count,
                } = iterator;
                const data = {
                    _user_id: user_id,
                    _institution_calendar_id: inst_cal_id,
                    _role_id: role_id,
                    settings: [
                        {
                            _department_id: department_id,
                            _program_id: program_id,
                            _subject_id: subject_id,
                            _course_id: course_id,
                            isConfigured: checked,
                            term,
                            rotation_count,
                        },
                    ],
                };
                if (
                    !isExistDeptSetting(
                        cs(department_id),
                        cs(program_id),
                        cs(subject_id),
                        cs(course_id),
                        cs(term),
                        rotation_count,
                    )
                ) {
                    bulk.push({ insertOne: { document: data } });
                } else {
                    bulk.push({
                        updateOne: {
                            filter: {
                                _user_id: user_id,
                                _institution_calendar_id: inst_cal_id,
                                _role_id: role_id,
                                'settings._department_id': department_id,
                                'settings._program_id': program_id,
                                'settings._subject_id': subject_id,
                                'settings._course_id': course_id,
                                'settings.term': term,
                                'settings.rotation_count': rotation_count,
                            },
                            update: { $set: data },
                        },
                    });
                }
            }
            await DeptSetting.bulkWrite(bulk);
        }
        updateUserSettingFlatCacheData(user_id);
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('UPDATED_SUCCESSFULLY'), []),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), error));
    }
};

exports.dashboard = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { userId, institutionCalendar, roleId },
        } = req;

        // LMS Setting Data
        const lmsData = await lmsStudentSettingData(convertToMongoObjectId(_institution_id));
        // get All Programs ids of given user
        const programIdResult = await getUserRoleProgram(userId, roleId);
        if (!programIdResult.status)
            return sendResponse(res, 404, false, req.t('USER_RECORD_NOT_FOUND'), []);
        const courseData = clone(await getCourseList(_institution_id));
        let courseIds = [];
        let courseWithTerm = [];
        if (programIdResult.roleName === 'Course Coordinator') {
            const ccDatas = await courseCoordinatorProgramIds(
                courseData,
                userId,
                institutionCalendar,
            );
            courseIds = ccDatas.courseIds;
            programIdResult.data = ccDatas.programIds;
            courseWithTerm = ccDatas.courseWithTerm;
        }
        let programIds = programIdResult.data;
        // Schedule Gets
        const { scheduleCourseTerm, scheduleList } = await courseScheduleList(
            userId,
            institutionCalendar,
            programIdResult.isAdmin,
            programIds,
            courseIds,
        );
        //return res.send(scheduleList);
        programIds = [...programIds, ...scheduleList.map((ele) => ele._program_id.toString())];
        courseIds = [...courseIds, ...scheduleList.map((ele) => ele._course_id)];
        courseIds = [...new Set(courseIds)];
        if (programIdResult.isAdmin === false && courseIds.length === 0)
            return sendResponse(res, 404, false, req.t('COURSE_NOT_FOUND'), []);
        // get All Program calender
        const pcQuery = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: ObjectId(institutionCalendar),
        };
        let pcOrQuery = [];
        if (programIdResult.isAdmin)
            pcOrQuery.push({
                _program_id: { $in: programIds },
            });
        else if (courseIds.length !== 0)
            pcOrQuery = [
                ...pcOrQuery,
                ...[
                    { 'level.course._course_id': { $in: courseIds } },
                    { 'level.rotation_course.course._course_id': { $in: courseIds } },
                ],
            ];
        if (pcOrQuery.length !== 0) pcQuery.$or = pcOrQuery;
        const pcProject = {
            _id: 1,
            _program_id: 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.course._course_id': 1,
            'level.course.courses_name': 1,
            'level.course.start_date': 1,
            'level.course.end_date': 1,
            'level.course.courses_number': 1,
            'level.course.model': 1,
            'level.course.credit_hours': 1,
            'level._id': 1,
            'level.curriculum': 1,
            'level.term': 1,
            'level.year': 1,
            'level.level_no': 1,
            'level._program_id': 1,
            'level.rotation_course.rotation_count': 1,
            'level.rotation_course.course._course_id': 1,
            'level.rotation_course.course.courses_name': 1,
            'level.rotation_course.course.start_date': 1,
            'level.rotation_course.course.end_date': 1,
            'level.rotation_course.course.courses_number': 1,
            'level.rotation_course.course.model': 1,
            'level.rotation_course.course.credit_hours': 1,
            'level.rotation': 1,
        };
        const programCalendarList = await get_list(PCalender, pcQuery, pcProject);
        const getRCourses = async (rotation_course) => {
            const allRCourses = [];
            for (rorElement of rotation_course) {
                for (rotationCourse of rorElement.course) {
                    rotationCourse.rotation = 'yes';
                    rotationCourse.rotation_count = rorElement.rotation_count;
                    allRCourses.push(rotationCourse);
                }
            }
            return allRCourses;
        };
        programCalendarList.data = clone(programCalendarList.data);
        for (pcElement of programCalendarList.data) {
            const levelData = [];
            for (pcLevelElement of pcElement.level) {
                const tempCourse = [];
                if (pcLevelElement.rotation === 'yes') {
                    pcLevelElement.course = await getRCourses(pcLevelElement.rotation_course);
                    delete pcLevelElement.rotation_course;
                }
                for (courseElement of pcLevelElement.course) {
                    if (programIdResult.isAdmin) tempCourse.push(courseElement);
                    else if (
                        programIdResult.roleName === 'Course Coordinator' &&
                        courseWithTerm.find(
                            (ele) =>
                                ele._course_id.toString() === courseElement._course_id.toString() &&
                                ele.term === pcLevelElement.term,
                        )
                    )
                        tempCourse.push(courseElement);
                    else if (
                        scheduleCourseTerm.find(
                            (ele1) =>
                                ele1._course_id.toString() ===
                                    courseElement._course_id.toString() &&
                                ele1.term === pcLevelElement.term,
                        )
                    )
                        tempCourse.push(courseElement);
                }
                pcLevelElement.course = tempCourse;
                if (pcLevelElement.course.length !== 0) levelData.push(pcLevelElement);
            }
            pcElement.level = levelData;
        }
        // return res.send(programCalendarList);
        // merge existing DSetting value
        const DS_Query = { _user_id: ObjectId(userId) };
        const DS_Project = {};
        const DSettings = await get_list(DSetting, DS_Query, DS_Project);
        DSettings.data = DSettings.status ? DSettings.data : [];
        const getIsConfigured = async (_level_id, _program_id, _course_id) => {
            let returnable = false;
            for (DSettingElement of DSettings.data) {
                for (setting of DSettingElement.settings) {
                    if (
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._level_id) === cs(_level_id) &&
                        cs(setting._course_id) === cs(_course_id)
                    ) {
                        returnable = setting.isConfigured;
                    }
                }
            }
            return returnable;
        };

        const sessionDeliveryTypes = await get_list(
            session_delivery_type,
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: { $in: programIds },
            },
            {},
        );
        const sessionOrder = await get_list(
            session_order,
            {
                ...common_files.query,
                _institution_id: convertToMongoObjectId(_institution_id),
                _course_id: { $in: courseIds },
            },
            {
                _program_id: 1,
                _course_id: 1,
                'session_flow_data._id': 1,
                'session_flow_data.duration': 1,
            },
        );
        sessionOrder.data = sessionOrder.status ? sessionOrder.data : [];
        const DeptSub_Query = {
            isDeleted: false,
            isActive: true,
        };
        const DeptSub_Project = {
            _id: 1,
            isActive: 1,
            department_name: 1,
            program_id: 1,
            program_name: 1,
            shared_with: 1,
            'subject._id': 1,
            'subject.subject_name': 1,
            'subject.shared_with': 1,
            'subject.isDeleted': 1,
        };
        let DeptSubs = clone(await get_list(DeptSubject, DeptSub_Query, DeptSub_Project)).data;
        //Student group data
        const sgQuery = {
            isDeleted: false,
            _institution_calendar_id: ObjectId(institutionCalendar),
            // _program_id: { $in: programIds },
        };
        const sgOrQuery = [];
        if (programIdResult.isAdmin)
            sgOrQuery.push({
                'master._program_id': { $in: programIds },
            });
        else if (courseIds.length !== 0)
            sgOrQuery.push({ 'groups.courses._course_id': { $in: courseIds } });
        sgQuery.$or = sgOrQuery;
        const studentGroupData = await get_list(student_group, sgQuery, {
            'master._program_id': 1,
            'groups.term': 1,
            'groups.year': 1,
            'groups.level': 1,
            'groups.rotation': 1,
            'groups.students': 1,
            'groups.courses._course_id': 1,
            'groups.courses.student_absence_percentage': 1,
            'groups.courses.setting': 1,
        });
        const LevelCourses = [];
        if (!studentGroupData.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_CREATED'),
                        req.t('STUDENT_GROUP_NOT_CREATED'),
                    ),
                );
        for (sgElement of studentGroupData.data) {
            for (sgLevel of sgElement.groups) {
                let levelCourseList = [];
                if (programIdResult.isAdmin) levelCourseList = sgLevel.courses;
                else if (programIdResult.roleName === 'Course Coordinator')
                    levelCourseList = sgLevel.courses.filter((ele) =>
                        courseWithTerm.find(
                            (ele1) =>
                                ele1._course_id.toString() === ele._course_id.toString() &&
                                ele1.term === sgLevel.term,
                        ),
                    );
                else
                    levelCourseList = sgLevel.courses.filter((ele) =>
                        scheduleCourseTerm.find(
                            (ele1) =>
                                ele1._course_id.toString() === ele._course_id.toString() &&
                                ele1.term === sgLevel.term,
                        ),
                    );
                for (sgCourse of levelCourseList) {
                    if (sgLevel.rotation === 'yes') {
                        const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                        for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                            let studentIds = [];
                            for (sgSetting of sgCourse.setting) {
                                if (
                                    sgSetting &&
                                    sgSetting._group_no.toString() === i.toString() &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups
                                ) {
                                    for (sgSession of sgSetting.session_setting[0].groups) {
                                        studentIds = [...studentIds, ...sgSession._student_ids];
                                    }
                                    studentIds = studentIds.filter(
                                        (ele, index) =>
                                            studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                }
                            }
                            const courseScheduled = clone(
                                scheduleList.filter(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                            sgCourse._course_id.toString() &&
                                        ele.term === sgLevel.term &&
                                        ele.level_no === sgLevel.level &&
                                        ele.rotation === 'yes' &&
                                        ele.rotation_count &&
                                        ele.rotation_count.toString() === i.toString(),
                                ),
                            );
                            //Warning Calculations
                            if (
                                lmsData.warningAbsenceData[0] &&
                                sgCourse.student_absence_percentage &&
                                sgCourse.student_absence_percentage !== 0
                            ) {
                                lmsData.warningAbsenceData[0].absence_percentage =
                                    sgCourse.student_absence_percentage;
                            }
                            const studentReport = studentAttendanceReport(
                                studentIds,
                                courseScheduled,
                                lmsData.warningAbsenceData,
                            );
                            // Course Schedule Status
                            const courseSessionOrder = sessionOrder.data.find(
                                (ele) =>
                                    ele._course_id.toString() === sgCourse._course_id.toString(),
                            );
                            const sessionOrderData = courseSessionOrder
                                ? courseSessionOrder.session_flow_data
                                : [];
                            const courseScheduleStatus = courseCreditContactHours(
                                sessionDeliveryTypes.data.filter(
                                    (ele) =>
                                        ele._program_id.toString() ===
                                        sgElement.master._program_id.toString(),
                                ),
                                courseScheduled,
                                sessionOrderData,
                            );
                            LevelCourses.push({
                                _program_id: sgElement.master._program_id,
                                level: sgLevel.level,
                                term: sgLevel.term,
                                rotation: sgLevel.rotation,
                                rotation_count: i,
                                _course_id: sgCourse._course_id,
                                final_warning: lmsData.finaleWarning
                                    ? studentReport.filter(
                                          (ele) => ele.warning === lmsData.finaleWarning,
                                      ).length
                                    : 0,
                                denial: studentReport.filter(
                                    (ele) => ele.warning === lmsData.denialWarning,
                                ).length,
                                courseScheduleStatus,
                            });
                        }
                    } else {
                        let studentIds = [];
                        for (sgSetting of sgCourse.setting) {
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            ) {
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                            }
                        }
                        const courseScheduled = clone(
                            scheduleList.filter(
                                (ele) =>
                                    ele._course_id.toString() === sgCourse._course_id.toString() &&
                                    ele.term === sgLevel.term &&
                                    ele.rotation === 'no' &&
                                    ele.level_no === sgLevel.level,
                            ),
                        );
                        //Warning Calculations
                        if (
                            lmsData.warningAbsenceData[0] &&
                            sgCourse.student_absence_percentage &&
                            sgCourse.student_absence_percentage !== 0
                        ) {
                            lmsData.warningAbsenceData[0].absence_percentage =
                                sgCourse.student_absence_percentage;
                        }
                        const studentReport = studentAttendanceReport(
                            studentIds,
                            courseScheduled,
                            lmsData.warningAbsenceData,
                        );
                        // Course Schedule Status
                        const courseSessionOrder = sessionOrder.data.find(
                            (ele) => ele._course_id.toString() === sgCourse._course_id.toString(),
                        );
                        const sessionOrderData = courseSessionOrder
                            ? courseSessionOrder.session_flow_data
                            : [];
                        const courseScheduleStatus = courseCreditContactHours(
                            sessionDeliveryTypes.data.filter(
                                (ele) =>
                                    ele._program_id.toString() ===
                                    sgElement.master._program_id.toString(),
                            ),
                            courseScheduled,
                            sessionOrderData,
                        );
                        LevelCourses.push({
                            _program_id: sgElement.master._program_id,
                            level: sgLevel.level,
                            term: sgLevel.term,
                            rotation: sgLevel.rotation,
                            _course_id: sgCourse._course_id,
                            final_warning: lmsData.finaleWarning
                                ? studentReport.filter(
                                      (ele) => ele.warning === lmsData.finaleWarning,
                                  ).length
                                : 0,
                            denial: studentReport.filter(
                                (ele) => ele.warning === lmsData.denialWarning,
                            ).length,
                            courseScheduleStatus,
                        });
                    }
                }
            }
        }
        // return res.send(LevelCourses);
        const userCourses = [];
        const userCourseIds = [];
        for (pCalenderElement of programCalendarList.data) {
            pCalenderElement.isConfigured = false;
            for (level of pCalenderElement.level) {
                pCalenderElement.program_name = DeptSubs.find(
                    (ele) => ele.program_id.toString() === level._program_id.toString(),
                ).program_name;
                level.isConfigured = false;
                for (course of level.course) {
                    const levelCourseData =
                        course.rotation && course.rotation_count && course.rotation === 'yes'
                            ? LevelCourses.find(
                                  (ele) =>
                                      ele._program_id.toString() === level._program_id.toString() &&
                                      ele.level === level.level_no &&
                                      ele.term === level.term &&
                                      course.rotation_count === ele.rotation_count &&
                                      ele._course_id.toString() === course._course_id.toString(),
                              )
                            : LevelCourses.find(
                                  (ele) =>
                                      ele._program_id.toString() === level._program_id.toString() &&
                                      ele.level === level.level_no &&
                                      ele.term === level.term &&
                                      ele._course_id.toString() === course._course_id.toString(),
                              );

                    course.final_warning = levelCourseData ? levelCourseData.final_warning : 0;
                    course.denial = levelCourseData ? levelCourseData.denial : 0;
                    const courseScheduleStatus = levelCourseData
                        ? levelCourseData.courseScheduleStatus
                        : { session_count: 0, complete: 0 };
                    course.session_count = courseScheduleStatus.session_count;
                    course.total_session = courseScheduleStatus.session_count;
                    course.completed_session = courseScheduleStatus.complete;
                    course.term = level.term;
                    course.program_name = pCalenderElement.program_name;
                    course._program_id = level._program_id;
                    course.year = level.year;
                    course.level_no = level.level_no;
                    course.curriculums = level.curriculum;
                    course.rotation = level.rotation;

                    const sCourse = clone(
                        courseData.find((uCourse) => cs(uCourse._id) === cs(course._course_id)),
                    );
                    course.course_shared =
                        sCourse._program_id.toString() !== course._program_id.toString();
                    course.course_shared_program = course.course_shared
                        ? sCourse.course_assigned_details.find(
                              (cad) =>
                                  cad._program_id.toString() === sCourse._program_id.toString(),
                          ).program_name
                        : '';
                    userCourses.push(course);
                    userCourseIds.push(course._course_id);
                    course.isConfigured = await getIsConfigured(
                        level._id,
                        level._program_id,
                        course._course_id,
                    );
                    if (course.isConfigured) level.isConfigured = true;
                }
                if (level.isConfigured) pCalenderElement.isConfigured = true;
            }
        }
        // return res.send(userCourses);
        // return res.send(programCalendarList);
        // prepare department and subject with course
        const mergeUserCourse = async (course) => {
            const reqCourse = clone(course);
            const sCourse = clone(
                userCourses.filter((uCourse) => cs(uCourse._course_id) === cs(reqCourse._id)),
            );
            if (sCourse.length !== 1) {
                const rotationCourse = [];
                for (crs of sCourse) {
                    // delete crs._id;
                    // delete crs.course_name;
                    rotationCourse.push({ ...crs, ...reqCourse });
                }
                return rotationCourse;
            }
            // delete sCourse[0]._id;
            // delete sCourse[0].course_name;
            const courseElement = { ...sCourse[0], ...reqCourse };
            return courseElement;
        };

        const courseStaff = [];
        for (const csElement of scheduleList) {
            const loc = courseStaff.findIndex(
                (ele) =>
                    ele &&
                    ele._course_id &&
                    ele._course_id.toString() === csElement._course_id.toString(),
            );
            if (loc === -1) {
                courseStaff.push(csElement);
            } else {
                courseStaff[loc].staffs = [...courseStaff[loc].staffs, ...csElement.staffs];
            }
        }
        for (cElement of courseStaff) {
            cElement.staffs = cElement.staffs.filter(
                (item, index) =>
                    cElement.staffs.findIndex(
                        (ele) => ele._staff_id.toString() === item._staff_id.toString(),
                    ) === index,
            );
        }
        let adminCourseIds = [];
        const getCourse = async (programId, deptId, subject_id) => {
            SelectedCourses = [];
            for (course of courseData) {
                if (cs(course._program_id) === cs(programId)) {
                    const staffLoc = courseStaff.findIndex(
                        (ele) =>
                            ele._program_id.toString() === programId.toString() &&
                            ele._course_id.toString() === course._id.toString(),
                    );
                    course.staffs = staffLoc !== -1 ? courseStaff[staffLoc].staffs : [];
                    if (
                        course.administration &&
                        cs(course.administration._subject_id) === cs(subject_id)
                    ) {
                        course.AdminCourse = true;
                        course.participatingCourse = false;
                        const mergedCourse = await mergeUserCourse(course);
                        if (Array.isArray(mergedCourse)) {
                            for (mergedCourseElement of mergedCourse) {
                                adminCourseIds.push(cs(mergedCourseElement._course_id));
                                SelectedCourses.push(mergedCourseElement);
                            }
                        } else {
                            adminCourseIds.push(cs(mergedCourse._course_id));
                            SelectedCourses.push(mergedCourse);
                        }
                    } else {
                        if (course.participating) {
                            for (participating of course.participating) {
                                if (
                                    participating &&
                                    cs(participating._subject_id) === cs(subject_id)
                                ) {
                                    course.AdminCourse = false;
                                    course.participatingCourse = true;
                                    const mergedCourse = await mergeUserCourse(course);
                                    if (Array.isArray(mergedCourse)) {
                                        for (mergedCourseElement of mergedCourse) {
                                            SelectedCourses.push(mergedCourseElement);
                                        }
                                    } else {
                                        SelectedCourses.push(mergedCourse);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return SelectedCourses;
        };
        const shared_subject = [];
        const shared_depart = [];
        DeptSubs.forEach((deptShare) => {
            deptShare.shared = false;
            const subject_data = deptShare.subject.filter((ele) => ele.isDeleted == false);
            for (element of subject_data) {
                if (element.shared_with && element.shared_with.length != 0) {
                    shared_subject.push({
                        program_id: deptShare.program_id,
                        program_name: deptShare.program_name,
                        department_id: deptShare._id,
                        department_name: deptShare.department_name,
                        subject_id: element._id,
                        subject_name: element.subject_name,
                        shared_with: element.shared_with,
                        shared: false,
                        isDeleted: element.isDeleted,
                    });
                }
            }
            if (
                programIds.findIndex(
                    (ele) => ele && ele.toString() === deptShare.program_id.toString(),
                ) !== -1
            ) {
                deptShare.subject.forEach((ele2) => {
                    ele2.shared = false;
                });
                deptShare.shared_with.forEach((ele3) => {
                    // deptShare.shared = true;
                    shared_depart.push({
                        _id: deptShare._id,
                        isActive: deptShare.isActive,
                        program_id: ele3.program_id,
                        program_name: ele3.program_name,
                        department_share: true,
                        shared_from_program_id: deptShare.program_id,
                        shared_from_program_name: deptShare.program_name,
                        department_name: deptShare.department_name,
                        subject: deptShare.subject,
                        shared_with: deptShare.shared_with,
                        shared: true,
                    });
                });
            }
        });
        DeptSubs = [...DeptSubs, ...shared_depart];
        DeptSubs = clone(DeptSubs);
        DeptSubs.forEach((deptShare) => {
            for (element of shared_subject) {
                const datas = clone(deptShare);
                if (
                    !datas.department_share &&
                    element.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                ) {
                    deptShare.subject.push({
                        _id: element.subject_id,
                        subject_name: element.subject_name,
                        subject_shared_from_program_id: element.program_id,
                        subject_shared_from_program_name: element.program_name,
                        isDeleted: element.isDeleted,
                        shared: true,
                    });
                }
            }
        });
        // return res.send({ courseData, DeptSubs });
        let scheduledSubject = [];
        scheduleList.forEach((ele) => {
            scheduledSubject = scheduledSubject.concat(ele._subject_id);
        });
        scheduledSubject = [...new Set(scheduledSubject)];
        // return res.send(scheduledSubject);
        const subjectCourses = JSON.parse(JSON.stringify(DeptSubs));
        for (const [deptIndex, DeptSub] of DeptSubs.entries()) {
            adminCourseIds = [];
            for (const [subIndex, subject] of DeptSub.subject.entries()) {
                if (programIdResult.isAdmin) {
                    subjectCourses[deptIndex].subject[subIndex].courses = await getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                } else if (scheduledSubject.indexOf(cs(subject._id)) !== -1) {
                    subjectCourses[deptIndex].subject[subIndex].courses = await getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                } else {
                    subjectCourses[deptIndex].subject[subIndex].courses = await getCourse(
                        DeptSub.program_id,
                        DeptSub._id,
                        subject._id,
                    );
                }
            }
        }
        // return res.send({ subjectCourses });
        // merge existing DeptSetting vale
        const DeptS_Query = { _user_id: ObjectId(userId) };
        const DeptS_Project = {};
        const DeptSettings = (await getJSON(DeptSetting, DeptS_Query, DeptS_Project)).data;
        const getIsDeptConfigured = (_department_id, _subject_id, _program_id, _course_id) => {
            let returnable = false;
            DeptSettings.forEach((DeptSettingElement) => {
                DeptSettingElement.settings.forEach((setting) => {
                    if (
                        setting._department_id &&
                        cs(setting._department_id) === cs(_department_id) &&
                        cs(setting._program_id) === cs(_program_id) &&
                        cs(setting._subject_id) === cs(_subject_id) &&
                        cs(setting._course_id) === cs(_course_id)
                    ) {
                        returnable = setting.isConfigured;
                    }
                });
            });
            return returnable;
        };
        const subjectShared = [];
        subjectCourses.forEach((DeptSub, departIndex) => {
            DeptSub.isConfigured = false;
            DeptSub.subject.forEach((subject, subjectIndex) => {
                subject.isConfigured = false;
                subject.courses.forEach((course, courseIndex) => {
                    course.isConfigured = getIsDeptConfigured(
                        DeptSub._id,
                        subject._id,
                        DeptSub.program_id,
                        course._course_id,
                    );
                    if (course.isConfigured) subject.isConfigured = true;
                    delete subjectCourses[departIndex].subject[subjectIndex].courses[courseIndex]
                        .participating;
                    delete subjectCourses[departIndex].subject[subjectIndex].courses[courseIndex]
                        .administration;
                });
                if (subject.isConfigured) DeptSub.isConfigured = true;
            });
            const sharedSubjects = JSON.parse(
                JSON.stringify(DeptSub.subject.filter((ele) => ele.subject_shared_from_program_id)),
            );
            if (sharedSubjects.length !== 0)
                subjectShared.push({
                    _id: DeptSub._id,
                    isActive: DeptSub.isActive,
                    program_id: DeptSub.program_id,
                    program_name: DeptSub.program_name,
                    department_name: DeptSub.department_name,
                    subject: sharedSubjects,
                    shared_with: DeptSub.shared_with,
                    shared: DeptSub.shared,
                    isConfigured: DeptSub.isConfigured,
                });
        });

        //Get Subject Shared Details
        subjectShared.forEach((sharedElement) => {
            sharedElement.subject.forEach((sharedSubjectElement) => {
                const sharedPrograms = JSON.parse(
                    JSON.stringify(
                        subjectCourses.filter(
                            (ele) =>
                                ele.program_id.toString() ===
                                    sharedSubjectElement.subject_shared_from_program_id.toString() &&
                                ele.subject.some(
                                    (subjectShareElement) =>
                                        subjectShareElement._id.toString() ===
                                        sharedSubjectElement._id.toString(),
                                ),
                        ),
                    ),
                );
                sharedPrograms.forEach((ele1) => {
                    const deptLoc = subjectCourses.findIndex(
                        (ele2) => ele2._id.toString() === ele1._id.toString(),
                    );
                    const subLoc = subjectCourses[deptLoc].subject.findIndex(
                        (ele3) => ele3._id.toString() === sharedSubjectElement._id.toString(),
                    );
                    subjectCourses[deptLoc].subject[subLoc].courses = [
                        ...subjectCourses[deptLoc].subject[subLoc].courses,
                        ...sharedSubjectElement.courses,
                    ];
                });
            });
        });
        const sharedWith = JSON.parse(
            JSON.stringify(subjectCourses.filter((ele) => ele.shared_from_program_name)),
        );
        sharedWith.forEach((departmentElement) => {
            const shareInd = subjectCourses.findIndex(
                (ele2) => ele2._id.toString() === departmentElement._id.toString(),
            );
            if (shareInd !== -1) {
                departmentElement.subject.forEach((subElement) => {
                    if (subElement.courses.length !== 0) {
                        const subShareInd = subjectCourses[shareInd].subject.findIndex(
                            (ele3) => ele3._id.toString() === subElement._id.toString(),
                        );
                        if (subShareInd !== -1) {
                            subElement.courses.forEach((element) => {
                                element._program_id = subjectCourses[shareInd].program_id;
                                subjectCourses[shareInd].subject[subShareInd].courses.push(element);
                            });
                        }
                    }
                });
            }
        });
        // send Response
        return res.status(200).send(
            common_files.response_function(res, 200, true, req.t('DATA_RETRIEVED'), {
                PCalenders: programCalendarList.data,
                DeptSubs: subjectCourses,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, req.t('SERVER_ERROR'), error));
    }
};

exports.programWiseData = async (req, res) => {
    try {
        const {
            params: { userId, institutionCalendar, roleId, tab },
            query: { programId },
        } = req;
        const programIdResult = await userRoleData(userId, roleId);
        if (!programIdResult.status)
            return sendResponseWithRequest(
                req,
                res,
                404,
                false,
                req.t('USER_RECORD_NOT_FOUND'),
                [],
            );
        const programCourseData = (await allCourseList()).map((courseElement) => {
            return {
                _id: courseElement._id,
                course_name: courseElement.course_name,
                // course_code: courseElement.course_code,
                // course_type: courseElement.course_type,
                _program_id: courseElement._program_id,
                administration: courseElement.administration,
                participating: courseElement.participating,
                course_assigned_details: courseElement.course_assigned_details,
                coordinators: courseElement.coordinators,
                versionNo: courseElement.versionNo || 1,
                versioned: courseElement.versioned || false,
                versionName: courseElement.versionName || '',
                versionedFrom: courseElement.versionedFrom || null,
                versionedCourseIds: courseElement.versionedCourseIds || [],
            };
        });
        let courseIds = [];
        let courseWithTerm = [];
        if (programIdResult.roleName === 'Course Coordinator') {
            const ccDatas = await courseCoordinatorBasedIds(
                programCourseData,
                userId,
                institutionCalendar,
            );
            courseIds = ccDatas.courseIds;
            programIdResult.data = ccDatas.programIds;
            courseWithTerm = ccDatas.courseWithTerm;
        }
        const programIds =
            /* programId && programId.length === 24 ? [programId] : */ programIdResult.data;
        // Schedule Gets
        // const { scheduleCourseTerm, courseScheduleFilteredData, courseStaff } =
        //     await userCourseScheduleList(
        //         userId,
        //         institutionCalendar,
        //         programIdResult.isAdmin,
        //         programIds,
        //         courseIds,
        //         programIdResult,
        //     );
        // programIds = [
        //     ...programIds,
        //     ...courseScheduleFilteredData.map((ele) => ele._program_id.toString()),
        // ];
        let userScheduleData = [];
        if (programIdResult.isAdmin === false) {
            const scheduleQuery = {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendar),
                type: 'regular',
                // _program_id: convertToMongoObjectId(programId),
            };
            if (courseIds.length !== 0) {
                Object.assign(scheduleQuery, {
                    $or: [
                        { _course_id: { $in: courseIds } },
                        { 'staffs._staff_id': convertToMongoObjectId(userId) },
                    ],
                });
            } else
                Object.assign(scheduleQuery, {
                    'staffs._staff_id': convertToMongoObjectId(userId),
                });
            console.log(scheduleQuery);
            console.time('userScheduleData');
            userScheduleData = await CourseSchedule.find(scheduleQuery, {
                _course_id: 1,
                term: 1,
                // rotation: 1,
                rotation_count: 1,
                // 'staffs._staff_id': 1,
                // 'staffs.name': 1,
            });
            console.timeEnd('userScheduleData');
            if (userScheduleData && userScheduleData.length)
                courseIds = [...courseIds, ...userScheduleData.map((ele) => ele._course_id)];
        }
        // if (programIdResult.isAdmin === false) {
        //     if (courseIds.length !== 0) {
        //         Object.assign(scheduleQuery, {
        //             $on: [
        //                 { _course_id: { $in: courseIds } },
        //                 { 'staffs._staff_id': convertToMongoObjectId(userId) },
        //             ],
        //         });
        //     } else
        //         Object.assign(scheduleQuery, {
        //             'staffs._staff_id': convertToMongoObjectId(userId),
        //         });
        // } else Object.assign(scheduleQuery, { 'staffs._staff_id': convertToMongoObjectId(userId) });
        // console.log(scheduleQuery);
        // console.time('userScheduleData');
        // const userScheduleData = await CourseSchedule.find(scheduleQuery, {
        //     _course_id: 1,
        //     term: 1,
        //     rotation: 1,
        //     rotation_count: 1,
        //     'staffs._staff_id': 1,
        //     'staffs.name': 1,
        // });
        // console.timeEnd('userScheduleData');
        // return res.send(userScheduleData);

        // courseIds = [...courseIds, ...courseScheduleFilteredData.map((ele) => ele._course_id)];
        courseIds = [...new Set(courseIds)];
        if (programIdResult.isAdmin === false && courseIds.length === 0)
            return sendResponseWithRequest(req, res, 404, false, req.t('COURSE_NOT_FOUND'), []);
        const programCalendarData = (await allProgramCalendarDatas()).filter(
            (programCalendarElement) =>
                programCalendarElement._institution_calendar_id.toString() ===
                institutionCalendar.toString() /* &&
                programIds.find(
                    (programIdElement) =>
                        programIdElement.toString() ===
                        programCalendarElement._program_id.toString(),
                ), */,
        );
        const programList = await allProgramDatas();
        let response = {};
        const courseWithResponseData = await programWiseYearLevelData({
            programCalendarData,
            programCourseData,
            programId,
            courseIds,
            isAdmin: programIdResult.isAdmin,
            roleName: programIdResult.roleName,
            scheduleCourseTerm: userScheduleData,
            courseWithTerm,
        });
        switch (tab) {
            case 'yearLevel':
                response = courseWithResponseData.response;
                break;
            case 'departmentSubject':
                {
                    console.time('user');
                    const staffData = await user
                        .find({
                            status: 'completed',
                            user_type: 'staff',
                            'academic_allocation.allocation_type': 'primary',
                            'academic_allocation._program_id': convertToMongoObjectId(programId),
                            isActive: true,
                            isDeleted: false,
                        })
                        .countDocuments()
                        .exec();
                    console.timeEnd('user');
                    console.time('userData');
                    const userData =
                        programIdResult.isAdmin === false ||
                        (programIdResult.isAdmin === false &&
                            programIdResult.roleName !== 'Course Coordinator')
                            ? await user.findOne(
                                  { _id: convertToMongoObjectId(userId) },
                                  { academic_allocation: 1 },
                              )
                            : undefined;
                    console.timeEnd('userData');
                    const departmentSubjectDatas = await allDepartmentSubjectList();
                    const programDepartmentSubjectDatas = departmentSubjectDatas.filter(
                        (departmentSubjectElement) =>
                            departmentSubjectElement.program_id.toString() === programId.toString(),
                        // programIds.find(
                        //     (programIdElement) =>
                        //         programIdElement.toString() ===
                        //         departmentSubjectElement.program_id.toString(),
                        // ),
                    );
                    response.departmentSubjectCourses = await programWiseDepartmentSubjectData({
                        programId,
                        courseStaff: [],
                        departmentSubjectDatas,
                        courseWithResponseData,
                        programDepartmentSubjectDatas,
                        programCourseData,
                        userData,
                        programList,
                    });
                    response.staffCount = staffData;
                }
                break;
            default:
                break;
        }

        return res
            .status(200)
            .send(
                responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_WISE_DATE'), response),
            );
    } catch (error) {
        logger.error(error, 'INTERNAL_SERVER_ISSUE');
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.dashboardData = async (req, res) => {
    try {
        const {
            params: { userId, institutionCalendar, roleId, tab },
        } = req;
        console.time('dashboardSetting');
        const settingQuery = {
            _institution_calendar_id: institutionCalendar.toString(),
            _user_id: userId.toString() /* 'settings.isConfigured': true */,
            'settings.isConfigured': true,
            _role_id: roleId,
        };
        console.time('dashboardSetting-DB-DSettings&DepartmentSetting');
        const DSettings = await DSetting.find(settingQuery, {}).lean();
        const DeptSettings = await DeptSetting.find(settingQuery, {}).lean();
        console.timeEnd('dashboardSetting-DB-DSettings&DepartmentSetting');
        for (settingElement of DSettings) settingElement.settings = settingElement.settings[0];
        // const settingCourseIds = [
        //     ...new Set(
        //         DSettings.map((settingElement) => settingElement.settings._course_id.toString()),
        //     ),
        // ];
        console.time('dashboardSetting-DB-userRoleData');
        const programIdResult = await userRoleData(userId, roleId);
        if (!programIdResult.status)
            return sendResponseWithRequest(
                req,
                res,
                404,
                false,
                req.t('USER_RECORD_NOT_FOUND'),
                [],
            );
        console.timeEnd('dashboardSetting-DB-userRoleData');
        console.time('dashboardSetting-LOCAL_CACHE-allCourseList');
        const programCourseData = (await allCourseList()).map((courseElement) => {
            return {
                _id: courseElement._id,
                course_name: courseElement.course_name,
                course_code: courseElement.course_code,
                course_type: courseElement.course_type,
                _program_id: courseElement._program_id,
                versionNo: courseElement.versionNo || 1,
                versioned: courseElement.versioned || false,
                versionName: courseElement.versionName || '',
                versionedFrom: courseElement.versionedFrom || null,
                versionedCourseIds: courseElement.versionedCourseIds || [],
                administration: courseElement.administration,
                participating: courseElement.participating,
                course_assigned_details: courseElement.course_assigned_details,
                coordinators: courseElement.coordinators,
            };
        });
        console.timeEnd('dashboardSetting-LOCAL_CACHE-allCourseList');
        let courseIds = [];
        let courseWithTerm = [];
        if (programIdResult.roleName === 'Course Coordinator') {
            const ccDatas = await courseCoordinatorBasedIds(
                programCourseData,
                userId,
                institutionCalendar,
            );
            courseIds = ccDatas.courseIds;
            programIdResult.data = ccDatas.programIds;
            courseWithTerm = ccDatas.courseWithTerm;
        }
        // courseIds = [...new Set([...courseIds, ...settingCourseIds])];
        let programIds = programIdResult.data ? programIdResult.data : [];

        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendar),
            type: SCHEDULE_TYPES.REGULAR,
        };
        if (programIdResult.isAdmin === false) {
            if (courseIds.length !== 0) {
                Object.assign(scheduleQuery, {
                    $or: [
                        { _course_id: { $in: courseIds } },
                        { 'staffs._staff_id': convertToMongoObjectId(userId) },
                    ],
                });
            } else
                Object.assign(scheduleQuery, {
                    'staffs._staff_id': convertToMongoObjectId(userId),
                });
        } else Object.assign(scheduleQuery, { 'staffs._staff_id': convertToMongoObjectId(userId) });
        console.time('dashboardSetting-DB-userScheduleData');
        // Program Id List in Schedule by staffID
        let userScheduleData = await CourseSchedule.find(scheduleQuery, {
            _id: 0,
            _course_id: 1,
            term: 1,
            _program_id: 1,
            rotation_count: 1,
        });
        console.timeEnd('dashboardSetting-DB-userScheduleData');
        if (userScheduleData && userScheduleData.length)
            programIds = [
                ...programIds,
                ...userScheduleData.map((userProgramIdElement) =>
                    userProgramIdElement._program_id.toString(),
                ),
            ];
        userScheduleData = clone(userScheduleData);
        // Schedule Gets
        // const { scheduleCourseTerm, courseScheduleFilteredData, courseStaff } =
        //     await userCourseScheduleList(
        //         userId,
        //         institutionCalendar,
        //         programIdResult.isAdmin,
        //         programIds,
        //         courseIds,
        //         programIdResult,
        //     );
        // programIds = [
        //     ...programIds,
        //     ...courseScheduleFilteredData.map((ele) => ele._program_id.toString()),
        // ];
        courseIds = [...courseIds, ...userScheduleData.map((ele) => ele._course_id.toString())];
        courseIds = [...new Set(courseIds)];
        if (programIdResult.isAdmin === false && courseIds.length === 0)
            return sendResponseWithRequest(req, res, 404, false, req.t('COURSE_NOT_FOUND'), []);
        console.time(
            'dashboardSetting-LOCAL_CACHE-allProgramCalendarDatas&allDepartmentSubjectList',
        );
        const programCalendarData = (await allProgramCalendarDatas()).filter(
            (programCalendarElement) =>
                programCalendarElement._institution_calendar_id.toString() ===
                institutionCalendar.toString() /* &&
                programIds.find(
                    (programIdElement) =>
                        programIdElement.toString() ===
                        programCalendarElement._program_id.toString(),
                ), */,
        );

        const departmentSubjectDatas = (await allDepartmentSubjectList()).map(
            (departmentSubjectElement) => {
                return {
                    _id: departmentSubjectElement._id,
                    program_id: departmentSubjectElement.program_id,
                    program_name: departmentSubjectElement.program_name,
                    department_name: departmentSubjectElement.department_name,
                    subject: departmentSubjectElement.subject,
                    shared_with: departmentSubjectElement.shared_with,
                };
            },
        );
        console.timeEnd(
            'dashboardSetting-LOCAL_CACHE-allProgramCalendarDatas&allDepartmentSubjectList',
        );
        const programDepartmentSubjectDatas = departmentSubjectDatas.filter(
            (departmentSubjectElement) =>
                programIds.find(
                    (programIdElement) =>
                        programIdElement.toString() ===
                        departmentSubjectElement.program_id.toString(),
                ),
        );
        console.time('dashboardSetting-DB-userData');
        const userData =
            programIdResult.isAdmin === false ||
            (programIdResult.isAdmin === false && programIdResult.roleName !== 'Course Coordinator')
                ? await user.findOne(
                      { _id: convertToMongoObjectId(userId) },
                      { academic_allocation: 1 },
                  )
                : undefined;
        console.timeEnd('dashboardSetting-DB-userData');
        const programList = await allProgramDatas();
        console.time('dashboardSetting-LOOP-yearLevelData');
        const response = await yearLevelData({
            programCalendarData,
            programCourseData,
            DSettings,
            courseIds,

            // Department Subject Datas
            programIds,
            departmentSubjectDatas,
            programDepartmentSubjectDatas,
            DeptSettings,
            isAdmin: programIdResult.isAdmin,
            roleName: programIdResult.roleName,
            scheduleCourseTerm: userScheduleData,
            courseWithTerm,
            userData,
            programList,
        });
        console.timeEnd('dashboardSetting-LOOP-yearLevelData');
        console.timeEnd('dashboardSetting');

        return res
            .status(200)
            .send(
                responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_WISE_DATA'), response),
            );
    } catch (error) {
        logger.error(error, 'INTERNAL_SERVER_ISSUE');
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

const getIsConfiguredDepartment = ({
    DeptSettings,
    _department_id,
    _subject_id,
    _program_id,
    _course_id,
    term,
    rotation_count,
}) => {
    let returnable = false;
    DeptSettings.forEach((DeptSettingElement) => {
        DeptSettingElement.settings.forEach((setting) => {
            if (
                setting._department_id &&
                setting._department_id.toString() === _department_id.toString() &&
                setting._program_id.toString() === _program_id.toString() &&
                setting._subject_id.toString() === _subject_id.toString() &&
                setting._course_id.toString() === _course_id.toString() &&
                setting.term.toString() === term.toString() &&
                (rotation_count && setting.rotation_count
                    ? setting.rotation_count.toString() === rotation_count.toString()
                    : true)
            ) {
                returnable = setting.isConfigured;
            }
        });
    });
    return returnable;
};

exports.monitoringCourses = async (req, res) => {
    try {
        console.time('monitoringCourses');
        const {
            headers: { _institution_id },
            params: { userId, institutionCalendar, roleId },
        } = req;

        // merge existing DSetting value
        const DS_Query = {
            _user_id: userId,
            _institution_calendar_id: institutionCalendar,
            'settings.isConfigured': true,
            _role_id: roleId,
        };
        const DS_Project = {};
        console.time('monitoringCourses-DB-DSettings');
        const DSettings = await get_list(DSetting, DS_Query, DS_Project);
        console.timeEnd('monitoringCourses-DB-DSettings');
        console.time('monitoringCourses-DB-userDepartmentSetting');
        const userDepartmentSetting = await get_list(DeptSetting, DS_Query, DS_Project);
        console.timeEnd('monitoringCourses-DB-userDepartmentSetting');
        DSettings.data = DSettings.status ? DSettings.data : [];
        userDepartmentSetting.data = userDepartmentSetting.status ? userDepartmentSetting.data : [];
        const userYearLevelSettingData = DSettings.data
            .map((userSettingElement) => userSettingElement.settings)
            .flat();
        const userDepartmentSettingData = userDepartmentSetting.data
            .filter(
                (userSettingElement) =>
                    userSettingElement._institution_calendar_id.toString() ===
                    institutionCalendar.toString(),
            )
            .map((userSettingElement) => userSettingElement.settings)
            .flat();
        let userSettingCourseIds = [
            ...userYearLevelSettingData.map((levelSettingElement) =>
                levelSettingElement._course_id.toString(),
            ),
            ...userDepartmentSettingData.map((levelSettingElement) =>
                levelSettingElement._course_id.toString(),
            ),
        ];
        userSettingCourseIds = [...new Set(userSettingCourseIds)];
        // get All Programs ids of given user
        console.time('monitoringCourses-DB-programIdResult');
        const programIdResult = await getUserRoleProgram(userId, roleId);
        console.timeEnd('monitoringCourses-DB-programIdResult');
        if (!programIdResult.status)
            return sendResponseWithRequest(
                req,
                res,
                404,
                false,
                req.t('USER_RECORD_NOT_FOUND'),
                [],
            );
        console.time('monitoringCourses-DB-courseData');
        const courseData = (await allCourseList())
            .filter((courseElement) =>
                userSettingCourseIds.find(
                    (courseIdElement) =>
                        courseIdElement.toString() === courseElement._id.toString(),
                ),
            )
            .map((courseElement) => {
                return {
                    _id: courseElement._id,
                    _program_id: courseElement._program_id,
                    administration: courseElement.administration,
                    participating: courseElement.participating,
                    course_assigned_details: courseElement.course_assigned_details,
                    coordinators: courseElement.coordinators,
                    versionNo: courseElement.versionNo || 1,
                    versioned: courseElement.versioned || false,
                    versionName: courseElement.versionName || '',
                    versionedFrom: courseElement.versionedFrom || null,
                    versionedCourseIds: courseElement.versionedCourseIds || [],
                };
            });
        console.timeEnd('monitoringCourses-DB-courseData');
        let courseIds = [];
        let courseWithTerm = [];
        if (programIdResult.roleName === 'Course Coordinator') {
            console.time('monitoringCourses-DB-courseCoordinatorProgramIds');
            const ccDatas = await courseCoordinatorProgramIds(
                courseData,
                userId,
                institutionCalendar,
            );
            courseIds = ccDatas.courseIds;
            programIdResult.data = ccDatas.programIds;
            courseWithTerm = ccDatas.courseWithTerm;
            console.timeEnd('monitoringCourses-DB-courseCoordinatorProgramIds');
        }
        courseIds = [
            ...courseIds,
            ...courseData.map((courseDataElement) => courseDataElement._id.toString()),
        ];
        courseIds = [...new Set(courseIds)];
        let programIds = programIdResult.data;
        programIds = [
            ...programIds,
            ...courseData.map((courseElement) => courseElement._program_id),
        ];
        if (programIdResult.isAdmin === false && courseIds.length === 0)
            return sendResponseWithRequest(req, res, 200, true, req.t('COURSE_NOT_FOUND'), []);

        console.time('monitoringCourses-DB-programCalendarList');
        const programCalendarList = (await allProgramCalendarDatas()).filter(
            (programCalendarElement) =>
                programCalendarElement._institution_calendar_id.toString() ===
                institutionCalendar.toString() /*  &&
                programIds.find(
                    (programIdElement) =>
                        programIdElement.toString() ===
                        programCalendarElement._program_id.toString(),
                ), */,
        );
        console.timeEnd('monitoringCourses-DB-programCalendarList');
        const getRCourses = async (rotation_course) => {
            const allRCourses = [];
            for (rorElement of rotation_course) {
                for (rotationCourse of rorElement.course) {
                    rotationCourse.rotation = 'yes';
                    rotationCourse.rotation_count = rorElement.rotation_count;
                    allRCourses.push(rotationCourse);
                }
            }
            return allRCourses;
        };
        console.time('monitoringCourses-LOCAL_CACHE-allProgramDatas');
        const programList = await allProgramDatas();
        console.timeEnd('monitoringCourses-LOCAL_CACHE-allProgramDatas');
        const programCalendarCourseDatas = [];
        const programYearLevelCourse = [];
        console.time('monitoringCourses-LOOP-programCalendarList');
        for (pcElement of programCalendarList) {
            const levelData = [];
            // const pcLevel = pcElement.level.filter((levelElement) =>
            //     userYearLevelSettingData.find(
            //         (userYearLevelSettingDataElement) =>
            //             userYearLevelSettingDataElement._level_id.toString() ===
            //             levelElement._id.toString(),
            //     ),
            // );
            for (pcLevelElement of pcElement.level) {
                delete pcLevelElement.events;
                const levelCourse = [];
                if (pcLevelElement.rotation === 'yes') {
                    pcLevelElement.course = await getRCourses(pcLevelElement.rotation_course);
                    delete pcLevelElement.rotation_course;
                }
                const pcLevelCourse = pcLevelElement.course.filter(
                    (levelCourseElement) =>
                        userSettingCourseIds.find(
                            (courseIdElement) =>
                                courseIdElement.toString() ===
                                levelCourseElement._course_id.toString(),
                        ),
                    // userYearLevelSettingData.find(
                    //     (userYearLevelSettingDataElement) =>
                    //         userYearLevelSettingDataElement._level_id.toString() ===
                    //             pcLevelElement._id.toString() &&
                    //         userYearLevelSettingDataElement._course_id.toString() ===
                    //             levelCourseElement._course_id.toString(),
                    // ),
                );
                for (courseElement of pcLevelCourse) {
                    // Adding Admin course key
                    const isAdmin = programIdResult.isAdmin
                        ? true
                        : !!(
                              programIdResult.roleName === 'Course Coordinator' &&
                              courseWithTerm.find(
                                  (courseWithTermElement) =>
                                      courseWithTermElement._course_id.toString() ===
                                          courseElement._course_id.toString() &&
                                      courseWithTermElement.term === pcLevelElement.term,
                              )
                          );
                    const courseVersionedDetails = courseData.find(
                        (courseDataELement) =>
                            courseDataELement._id.toString() ===
                            courseElement._course_id.toString(),
                    );
                    const courseObject = {
                        _id: courseElement._course_id,
                        _course_id: courseElement._course_id,
                        courses_name: courseElement.courses_name,
                        courses_number: courseElement.courses_number,
                        versionNo: courseVersionedDetails.versionNo || 1,
                        versioned: courseVersionedDetails.versioned || false,
                        versionName: courseVersionedDetails.versionName || '',
                        versionedFrom: courseVersionedDetails.versionedFrom || null,
                        versionedCourseIds: courseVersionedDetails.versionedCourseIds || [],
                        model: courseElement.model,
                        start_date: courseElement.start_date,
                        end_date: courseElement.end_date,
                        _program_id: pcLevelElement._program_id,
                        course_type: courseElement.model,
                        courseProgramId: pcLevelElement._program_id,
                        course_shared: false,
                        course_shared_program: '',
                        isConfigured: true,
                        rotation: courseElement.rotation || 'no',
                        rotation_count: courseElement.rotation_count,
                        isAdmin,
                    };
                    if (
                        userYearLevelSettingData.find(
                            (userYearLevelSettingDataElement) =>
                                userYearLevelSettingDataElement._level_id.toString() ===
                                    pcLevelElement._id.toString() &&
                                userYearLevelSettingDataElement._course_id.toString() ===
                                    courseElement._course_id.toString() &&
                                userYearLevelSettingDataElement.term.toString() ===
                                    pcLevelElement.term.toString() &&
                                (courseElement.rotation_count &&
                                userYearLevelSettingDataElement.rotation_count
                                    ? courseElement.rotation_count.toString() ===
                                      userYearLevelSettingDataElement.rotation_count.toString()
                                    : true),
                        )
                    )
                        levelCourse.push(courseObject);
                    programYearLevelCourse.push({
                        ...courseObject,
                        ...{
                            level: pcLevelElement.level_no,
                            year: pcLevelElement.year,
                            term: pcLevelElement.term,
                            curriculum: pcLevelElement.curriculum,
                        },
                    });
                }
                if (
                    programIds.find(
                        (programIdElement) =>
                            programIdElement.toString() === pcElement._program_id.toString(),
                    )
                ) {
                    pcLevelElement.course = levelCourse;
                    pcLevelElement.isConfigured = true;
                    if (pcLevelElement.course.length) levelData.push(pcLevelElement);
                }
            }
            if (
                levelData.length &&
                programIds.find(
                    (programIdElement) =>
                        programIdElement.toString() === pcElement._program_id.toString(),
                )
            )
                programCalendarCourseDatas.push({
                    _id: pcElement._id,
                    _program_id: pcElement._program_id,
                    programName:
                        programList.find(
                            (programListElement) =>
                                programListElement._id.toString() ===
                                pcElement._program_id.toString(),
                        ).name || '',
                    isConfigured: true,
                    level: levelData,
                });
        }
        console.timeEnd('monitoringCourses-LOOP-programCalendarList');
        // return res.send({ programYearLevelCourse, programCalendarCourseDatas, courseData });
        // Department Subject Course Flow
        console.time('monitoringCourses-LOCAL_CACHE-allDepartmentSubjectList');
        const departmentSubjectDatas = (await allDepartmentSubjectList()).map(
            (departmentSubjectElement) => {
                return {
                    _id: departmentSubjectElement._id,
                    program_id: departmentSubjectElement.program_id,
                    program_name: departmentSubjectElement.program_name,
                    department_name: departmentSubjectElement.department_name,
                    subject: departmentSubjectElement.subject,
                    shared_with: departmentSubjectElement.shared_with,
                };
            },
        );
        const programDepartmentSubjectDatas = departmentSubjectDatas.filter(
            (departmentSubjectElement) =>
                programIds.find(
                    (programIdElement) =>
                        programIdElement.toString() ===
                        departmentSubjectElement.program_id.toString(),
                ),
        );
        console.timeEnd('monitoringCourses-LOCAL_CACHE-allDepartmentSubjectList');

        console.time('monitoringCourses-LOOP-courseMerge');
        const sharedSubject = [];
        const sharedDepart = [];
        for (departmentElement of departmentSubjectDatas) {
            departmentElement.shared = false;
            const subjectData = departmentElement.subject.filter(
                (subjectElement) => subjectElement.isDeleted === false,
            );
            for (subjectElement of subjectData) {
                delete subjectElement.isDeleted;
                delete subjectElement.isActive;
                if (subjectElement.shared_with && subjectElement.shared_with.length !== 0) {
                    sharedSubject.push({
                        program_id: departmentElement.program_id,
                        program_name: departmentElement.program_name,
                        department_id: departmentElement._id,
                        department_name: departmentElement.department_name,
                        subject_id: subjectElement._id,
                        subject_name: subjectElement.subject_name,
                        shared_with: subjectElement.shared_with,
                        shared: false,
                        isDeleted: subjectElement.isDeleted,
                    });
                }
            }
            departmentElement.subject.forEach((ele2) => {
                ele2.shared = false;
            });
            departmentElement.shared_with.forEach((ele3) => {
                sharedDepart.push({
                    _id: departmentElement._id,
                    program_id: ele3.program_id,
                    program_name: ele3.program_name,
                    department_share: true,
                    shared_from_program_id: departmentElement.program_id,
                    shared_from_program_name: departmentElement.program_name,
                    department_name: departmentElement.department_name,
                    subject: departmentElement.subject,
                    shared_with: departmentElement.shared_with,
                    shared: true,
                });
            });
        }
        let programDepartmentSubjectDatasWithShare = [
            ...programDepartmentSubjectDatas,
            ...sharedDepart,
        ];
        programDepartmentSubjectDatasWithShare = clone(programDepartmentSubjectDatasWithShare);
        for (departmentElement of programDepartmentSubjectDatasWithShare) {
            for (sharedSubjectElement of sharedSubject) {
                const datas = clone(departmentElement);
                if (
                    !datas.department_share &&
                    sharedSubjectElement.shared_with.findIndex(
                        (i) =>
                            i.program_id.toString() == datas.program_id.toString() &&
                            i.department_id.toString() == datas._id.toString(),
                    ) != -1
                ) {
                    departmentElement.subject.push({
                        _id: sharedSubjectElement.subject_id,
                        subject_name: sharedSubjectElement.subject_name,
                        subject_shared_from_program_id: sharedSubjectElement.program_id,
                        subject_shared_from_program_name: sharedSubjectElement.program_name,
                        isDeleted: sharedSubjectElement.isDeleted,
                        shared: true,
                    });
                }
            }
        }

        // Department Subject Course Listing
        const mergeUserCourse = (course) => {
            const reqCourse = clone(course);
            delete reqCourse.course_assigned_details;
            // delete reqCourse.administration;
            delete reqCourse.participating;
            delete reqCourse.coordinators;
            reqCourse.administration = { program_name: reqCourse.administration.program_name };
            const sCourse = clone(
                programYearLevelCourse.filter(
                    (uCourse) => uCourse._course_id.toString() === reqCourse._id.toString(),
                ),
            );
            if (sCourse.length !== 1) {
                const rotationCourse = [];
                for (crs of sCourse) rotationCourse.push({ ...crs, ...reqCourse });
                return rotationCourse;
            }
            const courseElement = { ...sCourse[0], ...reqCourse };
            return courseElement;
        };
        const subjectCourses = JSON.parse(JSON.stringify(programDepartmentSubjectDatasWithShare));
        let adminCourseIds = [];
        for (const [deptIndex, DeptSub] of programDepartmentSubjectDatasWithShare.entries()) {
            adminCourseIds = [];
            for (const [subIndex, subject] of DeptSub.subject.entries()) {
                SelectedCourses = [];
                for (course of courseData) {
                    if (
                        userDepartmentSettingData.find(
                            (departmentSubjectSettingElement) =>
                                departmentSubjectSettingElement._program_id.toString() ===
                                    DeptSub.program_id.toString() &&
                                departmentSubjectSettingElement._department_id.toString() ===
                                    DeptSub._id.toString() &&
                                departmentSubjectSettingElement._subject_id.toString() ===
                                    subject._id.toString() &&
                                departmentSubjectSettingElement._course_id.toString() ===
                                    course._id.toString(),
                        )
                    )
                        if (
                            course.administration._program_id.toString() ===
                            DeptSub.program_id.toString()
                        ) {
                            if (
                                course.administration &&
                                course.administration._subject_id.toString() ===
                                    subject._id.toString()
                            ) {
                                course.AdminCourse = true;
                                course.participatingCourse = false;
                                const mergedCourse = mergeUserCourse(course);
                                if (Array.isArray(mergedCourse)) {
                                    for (mergedCourseElement of mergedCourse) {
                                        mergedCourseElement.isConfigured =
                                            getIsConfiguredDepartment({
                                                DeptSettings: userDepartmentSetting.data,
                                                _program_id: DeptSub.program_id.toString(),
                                                _department_id: DeptSub._id.toString(),
                                                _subject_id: subject._id.toString(),
                                                _course_id:
                                                    mergedCourseElement._course_id.toString(),
                                                term: mergedCourseElement.term,
                                                rotation_count: mergedCourseElement.rotation_count,
                                            });
                                        if (mergedCourseElement.isConfigured) {
                                            adminCourseIds.push(
                                                mergedCourseElement._course_id.toString(),
                                            );
                                            SelectedCourses.push(mergedCourseElement);
                                        }
                                    }
                                } else {
                                    mergedCourse.isConfigured = getIsConfiguredDepartment({
                                        DeptSettings: userDepartmentSetting.data,
                                        _program_id: DeptSub.program_id.toString(),
                                        _department_id: DeptSub._id.toString(),
                                        _subject_id: subject._id.toString(),
                                        _course_id: mergedCourse._course_id.toString(),
                                        term: mergedCourse.term,
                                        rotation_count: mergedCourse.rotation_count,
                                    });
                                    if (mergedCourse.isConfigured) {
                                        adminCourseIds.push(mergedCourse._course_id.toString());
                                        SelectedCourses.push(mergedCourse);
                                    }
                                }
                            } else {
                                if (course.participating) {
                                    for (participating of course.participating) {
                                        if (
                                            participating &&
                                            participating._subject_id.toString() ===
                                                subject._id.toString()
                                        ) {
                                            course.AdminCourse = false;
                                            course.participatingCourse = true;
                                            const mergedCourse = mergeUserCourse(course);
                                            if (Array.isArray(mergedCourse)) {
                                                for (mergedCourseElement of mergedCourse) {
                                                    mergedCourseElement.isConfigured =
                                                        getIsConfiguredDepartment({
                                                            DeptSettings:
                                                                userDepartmentSetting.data,
                                                            _program_id:
                                                                DeptSub.program_id.toString(),
                                                            _department_id: DeptSub._id.toString(),
                                                            _subject_id: subject._id.toString(),
                                                            _course_id:
                                                                mergedCourseElement._course_id.toString(),
                                                            term: mergedCourseElement.term,
                                                            rotation_count:
                                                                mergedCourseElement.rotation_count,
                                                        });
                                                    if (mergedCourseElement.isConfigured)
                                                        SelectedCourses.push(mergedCourseElement);
                                                }
                                            } else {
                                                mergedCourse.isConfigured =
                                                    getIsConfiguredDepartment({
                                                        DeptSettings: userDepartmentSetting.data,
                                                        _program_id: DeptSub.program_id.toString(),
                                                        _department_id: DeptSub._id.toString(),
                                                        _subject_id: subject._id.toString(),
                                                        _course_id:
                                                            mergedCourse._course_id.toString(),
                                                        term: mergedCourse.term,
                                                        rotation_count: mergedCourse.rotation_count,
                                                    });
                                                if (mergedCourse.isConfigured)
                                                    SelectedCourses.push(mergedCourse);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                }
                subjectCourses[deptIndex].subject[subIndex].courses = SelectedCourses;
                subjectCourses[deptIndex].subject[subIndex].isConfigured = !!SelectedCourses.find(
                    (courseElement) => courseElement.isConfigured === true,
                );
            }
            subjectCourses[deptIndex].isConfigured = !!subjectCourses[deptIndex].subject.find(
                (courseElement) => courseElement.isConfigured === true,
            );
        }

        const subjectShared = [];
        for (DeptSub of subjectCourses) {
            const sharedSubjects = JSON.parse(
                JSON.stringify(DeptSub.subject.filter((ele) => ele.subject_shared_from_program_id)),
            );
            if (sharedSubjects.length !== 0)
                subjectShared.push({
                    _id: DeptSub._id,
                    program_id: DeptSub.program_id,
                    program_name: DeptSub.program_name,
                    department_name: DeptSub.department_name,
                    subject: sharedSubjects,
                    shared_with: DeptSub.shared_with,
                    shared: DeptSub.shared,
                });
        }
        for (sharedElement of subjectShared) {
            for (sharedSubjectElement of sharedElement.subject) {
                const sharedPrograms = JSON.parse(
                    JSON.stringify(
                        subjectCourses.filter(
                            (ele) =>
                                ele.program_id.toString() ===
                                    sharedSubjectElement.subject_shared_from_program_id.toString() &&
                                ele.subject.some(
                                    (subjectShareElement) =>
                                        subjectShareElement._id.toString() ===
                                        sharedSubjectElement._id.toString(),
                                ),
                        ),
                    ),
                );
                if (sharedPrograms && sharedPrograms.subject)
                    for (ele1 of sharedPrograms.subject) {
                        const deptLoc = subjectCourses.findIndex(
                            (ele2) => ele2._id.toString() === ele1._id.toString(),
                        );
                        const subLoc = subjectCourses[deptLoc].subject.findIndex(
                            (ele3) => ele3._id.toString() === sharedSubjectElement._id.toString(),
                        );
                        subjectCourses[deptLoc].subject[subLoc].courses = [
                            ...subjectCourses[deptLoc].subject[subLoc].courses,
                            ...sharedSubjectElement.courses,
                        ];
                    }
            }
        }
        const sharedWith = JSON.parse(
            JSON.stringify(subjectCourses.filter((ele) => ele.shared_from_program_name)),
        );
        for (departmentElement of sharedWith) {
            const shareInd = subjectCourses.findIndex(
                (ele2) => ele2._id.toString() === departmentElement._id.toString(),
            );
            if (shareInd !== -1) {
                for (subElement of departmentElement.subject) {
                    if (subElement.courses.length !== 0) {
                        const subShareInd = subjectCourses[shareInd].subject.findIndex(
                            (ele3) => ele3._id.toString() === subElement._id.toString(),
                        );
                        if (subShareInd !== -1) {
                            for (element of subElement.courses) {
                                // element._program_id = subjectCourses[shareInd].program_id;
                                if (
                                    !subjectCourses[shareInd].subject[subShareInd].courses.find(
                                        (courseElement) =>
                                            courseElement._course_id.toString() ===
                                                element._course_id.toString() &&
                                            courseElement.term === element.term,
                                    )
                                )
                                    subjectCourses[shareInd].subject[subShareInd].courses.push(
                                        element,
                                    );
                            }
                        }
                    }
                }
            }
        }
        const departmentCourses = [];
        for (departmentSubject of subjectCourses) {
            const subjectCourses = [];
            for (departmentSubjectElement of departmentSubject.subject) {
                if (departmentSubjectElement.courses.length)
                    subjectCourses.push(departmentSubjectElement);
            }
            if (subjectCourses.length) {
                departmentSubject.subject = subjectCourses;
                departmentCourses.push(departmentSubject);
            }
        }
        console.timeEnd('monitoringCourses-LOOP-courseMerge');
        console.timeEnd('monitoringCourses');
        return res.status(200).send(
            common_files.responseFunctionWithRequest(req, 200, true, req.t('DATA_RETRIEVED'), {
                programCalendars: programCalendarCourseDatas,
                departmentSubjects: departmentCourses,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error,
                ),
            );
    }
};

exports.monitoringCourseDetails = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { userId, institutionCalendar, roleId, programId, courseId, term, level_no },
            query: { rotation_count },
        } = req;
        console.time('monitoringCourseDetails');
        console.time('monitoringCourseDetails-DB-userRoleData');
        const programIdResult = await userRoleData(userId, roleId);
        if (!programIdResult.status)
            return common_files.sendResponseWithRequest(
                req,
                res,
                404,
                false,
                req.t('USER_RECORD_NOT_FOUND'),
                [],
            );
        console.timeEnd('monitoringCourseDetails-DB-userRoleData');
        console.time('monitoringCourseDetails-LOCAL_CACHE-allCourseList');
        const programCourseData = (await allCourseList()).map((courseElement) => {
            return {
                _id: courseElement._id,
                course_name: courseElement.course_name,
                course_code: courseElement.course_code,
                course_type: courseElement.course_type,
                _program_id: courseElement._program_id,
                administration: courseElement.administration,
                participating: courseElement.participating,
                course_assigned_details: courseElement.course_assigned_details,
                coordinators: courseElement.coordinators,
            };
        });
        console.timeEnd('monitoringCourseDetails-LOCAL_CACHE-allCourseList');
        let courseIds = [];
        let courseWithTerm = [];
        console.time('monitoringCourseDetails-DB-courseCoordinatorBasedIds');
        if (programIdResult.roleName === 'Course Coordinator') {
            const ccDatas = await courseCoordinatorBasedIds(
                programCourseData,
                userId,
                institutionCalendar,
            );
            courseIds = ccDatas.courseIds;
            programIdResult.data = ccDatas.programIds;
            courseWithTerm = ccDatas.courseWithTerm;
        }
        console.timeEnd('monitoringCourseDetails-DB-courseCoordinatorBasedIds');
        const scheduleQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendar),
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            type: 'regular',
            term,
            level_no,
            rotation_count,
            isActive: true,
            isDeleted: false,
        };
        // if (programIdResult.isAdmin === false && programIdResult.roleName !== 'Course Coordinator')
        //     Object.assign(scheduleQuery, { 'staffs._staff_id': convertToMongoObjectId(userId) });
        console.time('monitoringCourseDetails-LOCAL_CACHE-courseCoordinatorBasedIds');
        const studentGroupList = (
            await allStudentGroupYesterday(
                institutionCalendar,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement._institution_calendar_id.toString() === institutionCalendar.toString() &&
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement.groups.some(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level.toString() === level_no.toString() &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        console.timeEnd('monitoringCourseDetails-LOCAL_CACHE-courseCoordinatorBasedIds');
        const errorResponse = {
            final_warning: 0,
            denial: 0,
            no_session: 0,
            completed_session: 0,
            student_absence_percentage: 0,
        };
        if (!studentGroupList)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('LEVEL_NOT_FOUND_IN_STUDENT_GROUP'),
                        errorResponse,
                    ),
                );
        const sessionOrderList = (await allSessionOrderDatas()).find(
            (sessionOrderElement) =>
                sessionOrderElement._course_id.toString() === courseId.toString(),
        );

        const sgLevel = studentGroupList.groups.find(
            (ele) => ele.level.toString() === level_no.toString() && ele.term === term,
        );
        if (!sgLevel)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('LEVEL_NOT_FOUND_IN_STUDENT_GROUP'),
                        errorResponse,
                    ),
                );
        const sgCourseData = sgLevel.courses.find((ele) =>
            rotation_count && rotation_count !== 0
                ? ele._course_id.toString() === courseId &&
                  ele.setting.find(
                      (ele2) => ele2._group_no.toString() === rotation_count.toString(),
                  )
                : ele._course_id.toString() === courseId,
        );
        let studentIds = [];
        if (sgCourseData && sgCourseData.setting)
            for (sgSetting of sgCourseData.setting) {
                if (
                    sgSetting &&
                    sgSetting.session_setting &&
                    sgSetting.session_setting.length &&
                    sgSetting.session_setting[0].groups &&
                    (rotation_count && rotation_count !== 0
                        ? sgSetting._group_no.toString() === rotation_count.toString()
                        : true)
                )
                    for (sgSession of sgSetting.session_setting[0].groups) {
                        studentIds = [...studentIds, ...sgSession._student_ids];
                    }
                studentIds = studentIds.filter(
                    (ele, index) =>
                        studentIds.findIndex((ele2) => ele2.toString() === ele.toString()) ===
                        index,
                );
            }
        const lmsData = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id: institutionCalendar,
        });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendar,
            courseId,
            programId,
            levelNo: level_no,
            term,
            rotationCount: rotation_count,
        });
        console.time('studentCriteriaData');
        const studentCriteriaData = await lmsDenialSchema
            .find({
                courseId: convertToMongoObjectId(courseId),
                _institution_calendar_id: institutionCalendar,
                levelNo: level_no,
                term,
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 })
            .lean();
        console.timeEnd('studentCriteriaData');
        console.time('courseSchedule');
        const scheduleData = await get_list(CourseSchedule, scheduleQuery, {
            _id: 0,
            'session._session_id': 1,
            status: 1,
            'students._id': 1,
            'students.status': 1,
            'students.primaryTime': 1,
            'students.lateExclude': 1,
            'students.tardisId': 1,
            'staffs._staff_id': 1,
            scheduleStartDateAndTime: 1,
            _course_id: 1,
            _program_id: 1,
            rotation_count: 1,
            term: 1,
            level_no: 1,
            _institution_calendar_id: 1,
        });
        scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
        console.timeEnd('monitoringCourseDetails-DB-courseSchedule');

        // * Session Schedule Based Report Course Session Order filter based on Schedule
        let alterSessionOrder = sessionOrderList
            ? clone(sessionOrderList)
            : { session_flow_data: [] };
        if (SCHEDULE_SESSION_BASED_REPORT && SCHEDULE_SESSION_BASED_REPORT === 'true') {
            alterSessionOrder = courseSessionOrderFilterBasedSchedule({
                courseSessionFlow: sessionOrderList,
                courseSchedule: scheduleData.data,
            });
        }
        const courseSessionFlow = alterSessionOrder
            ? alterSessionOrder.session_flow_data.map((ele) => ele._id)
            : [];

        //Warning Calculations
        if (
            lmsData.warningAbsenceData[0] &&
            sgCourseData &&
            sgCourseData.student_absence_percentage &&
            sgCourseData.student_absence_percentage !== 0
        ) {
            lmsData.warningAbsenceData[0].percentage = sgCourseData.student_absence_percentage;
        }
        console.time('studentAttendanceReport');
        const studentReport = getAttendanceReport(
            studentIds,
            scheduleData.data,
            lmsData.warningAbsenceData,
            studentCriteriaData,
            courseId,
            term,
            rotation_count,
            lmsData.denialLabel,
            lateDurationRange,
            manualLateRange,
            manualLateData,
            _institution_id,
            institutionCalendar,
            programId,
            level_no,
            lateExcludeManagement,
        );
        console.timeEnd('monitoringCourseDetails-DB-studentAttendanceReport');
        console.time('monitoringCourseDetails-LOOP-DataAssign');
        const sessionDetails = { no_session: 0, completed_session: 0 };
        if (
            programIdResult.isAdmin === false &&
            (programIdResult.roleName !== 'Course Coordinator' ||
                !courseWithTerm.find(
                    (courseWithTermElement) =>
                        courseWithTermElement._course_id.toString() === courseId.toString() &&
                        courseWithTermElement.term === term,
                ))
        ) {
            const staffSchedule = scheduleData.data.filter((scheduleElement) =>
                scheduleElement.staffs.find(
                    (staffElement) => staffElement._staff_id.toString() === userId.toString(),
                ),
            );
            sessionDetails.no_session = staffSchedule.length;
            sessionDetails.completed_session = staffSchedule.filter(
                (scheduleElement) => scheduleElement.status === COMPLETED,
            ).length;
        } else {
            sessionDetails.no_session = courseSessionFlow.length;
            for (sessionOrderElement of courseSessionFlow) {
                const sessionSchedule = scheduleData.data.filter(
                    (scheduleElement) =>
                        scheduleElement.session._session_id.toString() ===
                        sessionOrderElement.toString(),
                );
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                )
                    sessionDetails.completed_session++;
            }
        }
        const response = {
            final_warning: lmsData.finaleWarning
                ? studentReport.filter((ele) => ele.warning === lmsData.finaleWarning).length
                : 0,
            denial: studentReport.filter((ele) => ele.warning === lmsData.denialLabel).length,
            ...sessionDetails,
            denialLabel: lmsData.denialLabel,
            student_absence_percentage: 0,
        };
        console.timeEnd('monitoringCourseDetails-LOOP-DataAssign');
        console.timeEnd('monitoringCourseDetails');
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DATA_RETRIEVED'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error,
                ),
            );
    }
};
