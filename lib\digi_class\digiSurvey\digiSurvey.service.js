const { convertToMongoObjectId, query: dbQuery } = require('../../utility/common');
const {
    SURVEY_USERS,
    DS_CREATE,
    DS_UPDATE,
    SURVEY_USER_LIST_EXPIRE_TIME,
    GENDER: { MALE, FEMALE },
    SURVEY_QUESTION_TYPE: { RATING },
    LEARNING_OUTCOME_SECTION,
    DS_PROGRAM_KEY,
    DS_COURSE_KEY,
    CORRELATION_MATRIX_FORMULA: {
        PEARSON_CORRELATION_COEFFICIENT,
        SPEARMAN_CORRELATION_COEFFICIENT,
    },
    LEARNING_OUTCOME_LEVEL: { PLO, CLO },
} = require('../../utility/constants');
const { redisClient } = require('../../../config/redis-connection');
const surveySchemas = require('../../models/digiSurvey');
const userSchema = require('../../models/user');
const { mean, std, sum, variance, sqrt } = require('mathjs');

const extractUniqueValues = (surveyData, surveyProperty) => {
    return [...new Set(surveyData.flatMap((surveyItem) => surveyItem[surveyProperty].toString()))];
};

const getSurveyUserListFromRedis = async ({ surveyId, redisType = DS_CREATE }) => {
    const surveyUserRedisKey = `${SURVEY_USERS}:surveyId-${surveyId}`;
    const existingUserDetails = await redisClient.Client.get(surveyUserRedisKey);
    if (!existingUserDetails || redisType === DS_UPDATE) {
        const surveyUserList = await surveySchemas
            .findOne(
                {
                    ...dbQuery,
                    _id: convertToMongoObjectId(surveyId),
                },
                {
                    surveyUsers: 1,
                    _id: 0,
                    userResponseSettings: 1,
                    surveyLevel: 1,
                    'multiSelectedProgramIds.programId': 1,
                    'multiSelectedProgramIds.term': 1,
                    'multiSelectedProgramIds.yearNo': 1,
                    'multiSelectedProgramIds.levelNo': 1,
                    'multiSelectedProgramIds.courseId': 1,
                    'multiSelectedProgramIds.rotationCount': 1,
                },
            )
            .populate([
                {
                    path: 'surveyUsers',
                    select: { name: 1, user_id: 1, user_type: 1, gender: 1 },
                },
                { path: 'multiSelectedProgramIds.programId', select: { name: 1 } },
                { path: 'multiSelectedProgramIds.courseId', select: { course_name: 1 } },
            ])
            .lean();
        if (surveyUserList && surveyUserList.surveyUsers.length) {
            await redisClient.Client.set(
                surveyUserRedisKey,
                JSON.stringify(surveyUserList),
                'EX',
                SURVEY_USER_LIST_EXPIRE_TIME,
            );
            return surveyUserList;
        }
        return [];
    }
    return JSON.parse(existingUserDetails);
};

const getTotalPublishedSurveyUserFromRedis = async ({ publishedSurveyIds = [] }) => {
    const surveyUserRedisKey = publishedSurveyIds.map(
        (publishedSurveyIdElement) => `${SURVEY_USERS}:surveyId-${publishedSurveyIdElement}`,
    );
    const surveyUsersCacheData = await redisClient.Client.mget(...surveyUserRedisKey);
    const totalUsersCacheData = [];
    const missingSurveyIds = [];
    //check if cache retrieved all requested survey ids
    surveyUsersCacheData.forEach((surveyUsersCacheDataElement, surveyUsersCacheDataIndex) => {
        if (surveyUsersCacheDataElement) {
            totalUsersCacheData.push(...JSON.parse(surveyUsersCacheDataElement));
        } else {
            missingSurveyIds.push(publishedSurveyIds[surveyUsersCacheDataIndex]);
        }
    });
    // if all requested survey details retrieved from cache - return directly
    if (!missingSurveyIds.length) {
        return totalUsersCacheData;
    }
    // if any survey details is missed get data for surveysIds from DB and update in cache
    const surveyUserList = await surveySchemas
        .find(
            {
                ...dbQuery,
                _id: { $in: missingSurveyIds },
            },
            { surveyUsers: 1, _id: 1 },
        )
        .lean();
    if (surveyUserList && surveyUserList.length) {
        const constructedSurveyUserList = new Map();
        const totalUserIds = [];
        surveyUserList.forEach(({ _id, surveyUsers = [] }) => {
            totalUserIds.push(...surveyUsers);
            constructedSurveyUserList.set(String(_id), surveyUsers);
        });
        const surveyUserDetails = await userSchema
            .find({ _id: { $in: totalUserIds } }, { name: 1, user_id: 1, user_type: 1, gender: 1 })
            .lean();
        const constructedSurveyUserDetails = new Map();
        surveyUserDetails.forEach(({ _id, name, user_id, user_type }) => {
            constructedSurveyUserDetails.set(String(_id), { _id, name, user_id, user_type });
        });

        // map user details with user ids
        const surveyPipeline = redisClient.Client.pipeline();
        const newCacheData = [];
        for (const [
            constructedSurveyUserIndex,
            constructedSurveyUserElement,
        ] of constructedSurveyUserList.entries()) {
            const userIdWithDetails = constructedSurveyUserElement
                .map((userIdElement) => constructedSurveyUserDetails.get(String(userIdElement)))
                .filter((userDetailElement) => userDetailElement);
            newCacheData.push(...userIdWithDetails);
            const newCacheKey = `${SURVEY_USERS}:surveyId-${constructedSurveyUserIndex}`;
            surveyPipeline.set(newCacheKey, JSON.stringify(userIdWithDetails));
            surveyPipeline.expire(newCacheKey, SURVEY_USER_LIST_EXPIRE_TIME);
        }
        await surveyPipeline.exec();
        return [...totalUsersCacheData, ...newCacheData];
    }
    return totalUsersCacheData;
};

const calculateMean = (value) => (value.length ? mean(value) : 0);

const calculateVariance = (value) => (value.length ? variance(value, 'unbiased') : 0);

const calculateSum = (value) => (value.length ? sum(value) : 0);

const calculateStandard = (value) => (value.length ? std(value) : 0);

const calculateSquare = (value) => (value.length ? sqrt(value) : 0);

const calculateSem = (value) => {
    //formula (sem = standardDeviation/squareRoot(no of responders))
    const standardDeviation = calculateStandard(value);
    return standardDeviation / Math.sqrt(value.length);
};

const getSurveyUserListBasedOnGender = ({ surveyUsers = [], isStudentDetailsRequired = false }) => {
    const maleUserIds = [];
    const femaleUserIds = [];
    const constructedUserDetails = new Map();
    surveyUsers.forEach(({ gender, _id, user_id = '', name = {} }) => {
        if (gender === MALE) {
            maleUserIds.push(String(_id));
        } else {
            femaleUserIds.push(String(_id));
        }
        if (isStudentDetailsRequired) {
            constructedUserDetails.set(String(_id), {
                gender,
                user_id,
                name,
            });
        }
    });
    return { maleUserIds, femaleUserIds, constructedUserDetails };
};

//  multi tag and outcomes selection for survey reports (phase - 2)
const getMappedOutcomeDetails = ({
    questionNo = '',
    mappedOutcomeDetails = [],
    surveyLevel = '',
}) => {
    const currentSurveyLevel = surveyLevel === DS_PROGRAM_KEY ? 'PLO' : DS_COURSE_KEY ? 'CLO' : '';
    //because multiple outcome tagged for same question
    const mappedGeneralQuestionOutcomeDetail = mappedOutcomeDetails.filter(
        (mappedOutcomeDetailELement) => mappedOutcomeDetailELement.questionId === questionNo,
    );
    return mappedGeneralQuestionOutcomeDetail.map(
        (tagDetailElement) => `${currentSurveyLevel} ${tagDetailElement?.outcomeNo || ''}`,
    );
};

const getMappedTagDetail = ({ questionNo, mappedTagDetails = [] }) => {
    const tagDetail = mappedTagDetails.filter(
        ({ questionId = '' }) => String(questionId) === String(questionNo),
    );
    return tagDetail;
};

const getQuestionBasedReport = ({
    questions,
    userResponse,
    maleUserIds,
    femaleUserIds,
    publicUserCount,
    totalUserCount,
    mappedOutcomeDetails = [],
    mappedTagDetails = [],
    surveyLevel = '',
}) => {
    const studentRatingSemTotal = [];
    const studentRatingMeanTotal = [];
    const studentRatingStandardDeviationTotal = [];
    const studentRatingVarianceTotal = [];
    const studentRatingSumTotal = [];
    const individualStudentWiseSum = {};
    let totalQuestions = 0;
    const studentResponseGenderBased = { male: [], female: [], public: [] };
    const processQuestionResponses = ({
        questionElement,
        userResponse,
        individualStudentWiseSum,
    }) => {
        const userResponseData = userResponse.filter(
            ({ questionNo }) => questionNo === questionElement.name,
        );
        const updateIndividualStudentQuestionSum = ({ studentId, studentAnswer }) => {
            const existingDetails = individualStudentWiseSum[studentId];
            if (!existingDetails) {
                individualStudentWiseSum[studentId] = [studentAnswer];
            } else {
                existingDetails.push(studentAnswer);
            }
        };
        const questionStudentResponse = { maleUser: [], femaleUser: [], publicUser: [] };
        userResponseData.forEach(({ gender = '', answer, userId }) => {
            updateIndividualStudentQuestionSum({ studentId: userId, studentAnswer: answer });
            switch (gender) {
                case MALE:
                    questionStudentResponse.maleUser.push(answer);
                    break;
                case FEMALE:
                    questionStudentResponse.femaleUser.push(answer);
                    break;
                default:
                    questionStudentResponse.publicUser.push(answer);
            }
        });
        const studentOverAllResponse = {
            maleResponseCount: questionStudentResponse.maleUser.length,
            femaleResponseCount: questionStudentResponse.femaleUser.length,
            publicUserResponseCount: questionStudentResponse.publicUser.length,
            totalResponseCount:
                questionStudentResponse.maleUser.length +
                questionStudentResponse.femaleUser.length +
                questionStudentResponse.publicUser.length,
        };
        if (questionElement.rateValues) {
            questionElement.rateValues.forEach((rateValueElement) => {
                rateValueElement.studentResponse = {
                    maleResponseCount: questionStudentResponse.maleUser.filter(
                        (optionElement) => optionElement === rateValueElement.value,
                    ).length,
                    femaleResponseCount: questionStudentResponse.femaleUser.filter(
                        (optionElement) => optionElement === rateValueElement.value,
                    ).length,
                    publicUserResponseCount: questionStudentResponse.publicUser.filter(
                        (optionElement) => optionElement === rateValueElement.value,
                    ).length,
                };
            });
        }
        return { questionStudentResponse, studentOverAllResponse };
    };
    const getPValueCalculation = ({ maleUser, femaleUser, publicUser }) => {
        return {
            maleMean: calculateMean(maleUser),
            femaleMean: calculateMean(femaleUser),
            pubicUserMean: calculateMean(publicUser),
            maleVariance: calculateVariance(maleUser),
            femaleVariance: calculateVariance(femaleUser),
            publicUserVariance: calculateVariance(publicUser),
        };
    };
    const calculateQuestionMetrics = ({ questionStudentResponse }) => {
        const {
            maleMean,
            femaleMean,
            pubicUserMean,
            maleVariance,
            femaleVariance,
            publicUserVariance,
        } = getPValueCalculation({
            maleUser: questionStudentResponse.maleUser,
            femaleUser: questionStudentResponse.femaleUser,
            publicUser: questionStudentResponse.publicUser,
        });
        return {
            pValue:
                (maleMean - femaleMean - pubicUserMean) /
                calculateSquare(
                    maleVariance / questionStudentResponse.maleUser.length +
                        femaleVariance / questionStudentResponse.femaleUser.length +
                        publicUserVariance / questionStudentResponse.publicUser.length,
                ),
            male: {
                mean: calculateMean(questionStudentResponse.maleUser),
                standardDeviation: calculateStandard(questionStudentResponse.maleUser),
                male: calculateVariance(questionStudentResponse.maleUser),
            },
            female: {
                mean: calculateMean(questionStudentResponse.femaleUser),
                standardDeviation: calculateStandard(questionStudentResponse.femaleUser),
                female: calculateVariance(questionStudentResponse.femaleUser),
            },
            public: {
                mean: calculateMean(questionStudentResponse.publicUser),
                standardDeviation: calculateStandard(questionStudentResponse.publicUser),
                public: calculateVariance(questionStudentResponse.publicUser),
            },
            sem: calculateSem([
                ...questionStudentResponse.maleUser,
                ...questionStudentResponse.femaleUser,
                ...questionStudentResponse.publicUser,
            ]),
            overAll: {
                mean: calculateMean([
                    ...questionStudentResponse.maleUser,
                    ...questionStudentResponse.femaleUser,
                    ...questionStudentResponse.publicUser,
                ]),
                standardDeviation: calculateStandard([
                    ...questionStudentResponse.maleUser,
                    ...questionStudentResponse.femaleUser,
                    ...questionStudentResponse.publicUser,
                ]),
                variance: {
                    total: calculateVariance([
                        ...questionStudentResponse.maleUser,
                        ...questionStudentResponse.femaleUser,
                        ...questionStudentResponse.publicUser,
                    ]),
                },
            },
        };
    };
    const calculateOverallMetrics = ({
        totalQuestions,
        studentRatingSemTotal,
        studentRatingMeanTotal,
        studentRatingStandardDeviationTotal,
        studentResponseGenderBased,
        studentRatingVarianceTotal,
        studentRatingSumTotal,
        individualStudentWiseSum,
    }) => {
        const {
            maleMean,
            femaleMean,
            pubicUserMean,
            maleVariance,
            femaleVariance,
            publicUserVariance,
        } = getPValueCalculation({
            maleUser: studentResponseGenderBased.male,
            femaleUser: studentResponseGenderBased.female,
            publicUser: studentResponseGenderBased.public,
        });
        const overAllMean = calculateMean(studentRatingMeanTotal);
        const calculatedSemValue = calculateSem(studentRatingSemTotal);
        const studentOverAllSum = Object.values(individualStudentWiseSum).map((answerElement) =>
            calculateSum(answerElement),
        );
        return {
            semValue: calculatedSemValue,
            pValue:
                (maleMean - femaleMean - pubicUserMean) /
                calculateSquare(
                    maleVariance / studentResponseGenderBased.male.length +
                        femaleVariance / studentResponseGenderBased.female.length +
                        publicUserVariance / studentResponseGenderBased.public.length,
                ),
            mean: overAllMean,
            standardDeviation: calculateMean(studentRatingStandardDeviationTotal),
            confidentialInterval: ((overAllMean + calculatedSemValue) / 5) * 100,
            // cronBranch alpha formula(CA)
            testReadability:
                (totalQuestions / (totalQuestions - 1)) *
                (1 -
                    calculateSum(studentRatingVarianceTotal) /
                        calculateVariance(studentOverAllSum)),
        };
    };
    // calculate values for each question
    questions.forEach((questionElement) => {
        // reports generated only for rating type questions
        if (questionElement.type === RATING) {
            totalQuestions += 1;
            const { questionStudentResponse, studentOverAllResponse } = processQuestionResponses({
                questionElement,
                userResponse,
                individualStudentWiseSum,
            });
            questionElement.studentOverAllResponse = {
                ...studentOverAllResponse,
                maleCount: maleUserIds.length,
                femaleCount: femaleUserIds.length,
                publicUserCount,
                totalUserCount,
            };
            questionElement.studentResponseValue = calculateQuestionMetrics({
                questionStudentResponse,
            });

            studentResponseGenderBased.male.push(...questionStudentResponse.maleUser);
            studentResponseGenderBased.female.push(...questionStudentResponse.femaleUser);
            studentResponseGenderBased.public.push(...questionStudentResponse.publicUser);
            const overAllStudentResponse = [
                ...questionStudentResponse.maleUser,
                ...questionStudentResponse.femaleUser,
                ...questionStudentResponse.publicUser,
            ];
            studentRatingSemTotal.push(calculateSem(overAllStudentResponse));
            studentRatingMeanTotal.push(calculateMean(overAllStudentResponse));
            studentRatingStandardDeviationTotal.push(calculateStandard(overAllStudentResponse));
            studentRatingVarianceTotal.push(calculateVariance(overAllStudentResponse));
            studentRatingSumTotal.push(calculateSum(overAllStudentResponse));
            //mapped outcome details
            questionElement.mappedOutcomeDetail = getMappedOutcomeDetails({
                questionNo: questionElement.uniqueId,
                mappedOutcomeDetails,
                surveyLevel,
            });

            questionElement.mappedTagDetail = getMappedTagDetail({
                questionNo: questionElement.uniqueId,
                mappedTagDetails,
            });
        }
    });
    // // Cronbach's Alpha
    const overAllCalculation = calculateOverallMetrics({
        totalQuestions,
        studentRatingSemTotal,
        studentRatingMeanTotal,
        studentRatingStandardDeviationTotal,
        studentResponseGenderBased,
        studentRatingVarianceTotal,
        studentRatingSumTotal,
        individualStudentWiseSum,
    });
    return { overAllCalculation, surveyQuestionData: questions };
};

const calculatePearsonCorrelation = ({ firstSectionResponse = [], secondSectionResponse = [] }) => {
    const totalResponseCount = firstSectionResponse.length;
    const sumOfX = firstSectionResponse.reduce((acc, val) => acc + val, 0);
    const sumOfY = secondSectionResponse.reduce((acc, val) => acc + val, 0);
    const sumOfXY = firstSectionResponse.reduce(
        (acc, val, idx) => acc + val * secondSectionResponse[idx],
        0,
    );
    const sumOfX2 = firstSectionResponse.reduce((acc, val) => acc + val * val, 0);
    const sumOfY2 = secondSectionResponse.reduce((acc, val) => acc + val * val, 0);
    const numerator = totalResponseCount * sumOfXY - sumOfX * sumOfY;
    const denominator = Math.sqrt(
        (totalResponseCount * sumOfX2 - sumOfX ** 2) * (totalResponseCount * sumOfY2 - sumOfY ** 2),
    );
    return denominator === 0 ? 0 : numerator / denominator;
};

const rankSectionResponse = ({ sectionResponse }) => {
    const rankInAscendingOrder = [...sectionResponse].sort((a, b) => a - b);
    return sectionResponse.map(
        (sectionResponseElement) => rankInAscendingOrder.indexOf(sectionResponseElement) + 1,
    );
};

const calculateSpearmanCorrelation = ({
    firstSectionResponse = [],
    secondSectionResponse = [],
}) => {
    const totalResponseCount = firstSectionResponse.length;
    const rankFirstSectionResponse = rankSectionResponse({ sectionResponse: firstSectionResponse });
    const rankSecondSectionResponse = rankSectionResponse({
        sectionResponse: secondSectionResponse,
    });
    // Calculate the difference in ranks and square the differences
    const squaredRankDifference = rankFirstSectionResponse.reduce((acc, rankOfX, rankIndex) => {
        const rankOfY = rankSecondSectionResponse[rankIndex];
        const rankDifference = rankOfX - rankOfY;
        return acc + rankDifference * rankDifference;
    }, 0);
    //Spearman's rank correlation formula
    const spearmanNumerator = 6 * squaredRankDifference;
    const spearmanDenominator = totalResponseCount * (totalResponseCount * totalResponseCount - 1);
    return 1 - spearmanNumerator / spearmanDenominator;
};

const calculateCorrelation = ({
    firstSectionResponse,
    secondSectionResponse,
    correlationFormula = PEARSON_CORRELATION_COEFFICIENT,
}) => {
    const minLength = Math.min(firstSectionResponse.length, secondSectionResponse.length);
    firstSectionResponse = firstSectionResponse.slice(0, minLength);
    secondSectionResponse = secondSectionResponse.slice(0, minLength);

    if (correlationFormula === PEARSON_CORRELATION_COEFFICIENT) {
        return calculatePearsonCorrelation({ firstSectionResponse, secondSectionResponse });
    }
    if (correlationFormula === SPEARMAN_CORRELATION_COEFFICIENT) {
        return calculateSpearmanCorrelation({ firstSectionResponse, secondSectionResponse });
    }
    return 0;
};

module.exports = {
    extractUniqueValues,
    getSurveyUserListFromRedis,
    getTotalPublishedSurveyUserFromRedis,
    calculateMean,
    calculateVariance,
    calculateSum,
    calculateStandard,
    calculateSquare,
    getSurveyUserListBasedOnGender,
    getQuestionBasedReport,
    calculateCorrelation,
    getMappedOutcomeDetails,
    getMappedTagDetail,
};
