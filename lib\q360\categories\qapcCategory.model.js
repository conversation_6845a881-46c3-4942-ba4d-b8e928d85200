const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_FORM_CATEGORY,
    TAG_LEVEL,
    ANNUAL,
    PERIODIC,
    USER,
    FORM,
    INSTITUTION,
    TEMPLATE,
} = require('../../utility/constants');

const qapcFormCategorySchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        categoryName: { type: String },
        level: {
            type: String,
            enum: [TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION],
            default: TAG_LEVEL.INSTITUTION,
        },
        describe: { type: String },
        categoryFor: { type: String, default: ANNUAL, enum: [ANNUAL, PERIODIC] },
        periodicInterval: { type: Number, default: 3 },
        template: [
            {
                isDefault: { type: Boolean, default: false },
                formName: { type: String },
            },
        ],
        categoryFormType: { type: String, enum: [FORM, TEMPLATE], default: FORM },
        createdBy: { type: ObjectId, ref: USER },
        isActive: { type: Boolean, default: true },
        isConfigure: { type: Boolean, default: false },
        actions: {
            studentGroups: { type: Boolean, default: false },
            everyAcademic: { type: Boolean, default: false },
            occurrenceConfiguration: { type: Boolean, default: false },
            academicTerms: { type: Boolean, default: false },
            attemptType: { type: Boolean, default: false },
            incorporateMandatory: { type: Boolean, default: false },
            displayMandatory: { type: Boolean, default: false },
        },
        isDefault: { type: Boolean, default: false },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_FORM_CATEGORY, qapcFormCategorySchema);
