const express = require('express');
const route = express.Router();
const infrastructure_delivery_type = require('./infrastructure_delivery_type_controller');
const validater = require('./infrastructure_delivery_type_validator');
route.post('/list', infrastructure_delivery_type.list_values);
route.get('/:id', validater.infrastructure_delivery_type_id, infrastructure_delivery_type.list_id);
route.get('/', infrastructure_delivery_type.list);
route.post('/', validater.infrastructure_delivery_type, infrastructure_delivery_type.insert);
route.put('/:id', validater.infrastructure_delivery_type_id, validater.infrastructure_delivery_type, infrastructure_delivery_type.update);
route.delete('/:id', validater.infrastructure_delivery_type_id, infrastructure_delivery_type.delete);

module.exports = route;