let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let infrastructure_event_exam = new Schema({
    type: {
        type: String,
        required: true
    },
    event: {
        type: Boolean,
        default: false
    },
    exam: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
},
    { timestamps: true});


module.exports = mongoose.model(constant.INFRASTRUCTURE_EVENT_EXAM,infrastructure_event_exam);