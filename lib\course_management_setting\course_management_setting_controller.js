const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_functions = require('../utility/common_functions');
const constant = require('../utility/constants');
const ObjectId = common_files.convertToMongoObjectId;
const route = require('./course_management_setting_route');
const course_management_setting_model = require('../models/course_management_setting');
const course_management_setting = require('mongoose').model(constant.COURSE_MANAGEMENT_SETTING);
const institution = require('mongoose').model(constant.INSTITUTION);
const program = require('mongoose').model(constant.PROGRAM);

exports.insert = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const program_details = await base_control.get(
            program,
            { _id: ObjectId(req.body._program_id) },
            { _id: 1, name: 1, no: 1 },
        );
        if (!program_details.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const obj = {
            _institution_id: ObjectId(req.headers._institution_id),
            program: {
                _program_id: program_details.data._id,
                program_name: program_details.data.name,
            },
            session_type: req.body.session_type,
            title: req.body.title,
            gender: req.body.gender,
            time: {
                start_time: req.body.start_time,
                end_time: req.body.end_time,
            },
            days: {
                mode: req.body.mode,
                weekly: req.body.mode == constant.DAYS_MODE.WEEKLY ? req.body.selected_days : [],
            },
            start_date: req.body.start_date,
            end_date: req.body.end_date,
        };

        const doc = await base_control.insert(course_management_setting, obj);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('COURSE_MANAGEMENT_SETTING_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('ERROR'), doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
exports.update = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const course_management_setting_details = await base_control.get(
            course_management_setting,
            { _id: ObjectId(req.params._id) },
            {},
        );
        if (!course_management_setting_details.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SESSION_DATA_IS_NOT_FOUND'),
                        req.t('SESSION_DATA_IS_NOT_FOUND'),
                    ),
                );
        const program_details = await base_control.get(
            program,
            { _id: ObjectId(req.body._program_id) },
            { _id: 1, name: 1, no: 1 },
        );
        if (!program_details.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const obj = {
            session_type: req.body.session_type,
            title: req.body.title,
            gender: req.body.gender,
            time: {
                start_time: req.body.start_time,
                end_time: req.body.end_time,
            },
            days: {
                mode: req.body.mode,
                weekly: req.body.mode == constant.DAYS_MODE.WEEKLY ? req.body.selected_days : [],
            },
            start_date: req.body.start_date,
            end_date: req.body.end_date,
        };
        const result = await base_control.update_many(
            course_management_setting,
            ObjectId(req.params._id),
            obj,
        );
        if (result.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('SESSION_UPDATED_SUCCESSFULLY'),
                        result.data,
                    ),
                );
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('ERROR'), req.t('ERROR')));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
exports.delete = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await base_control.delete(course_management_setting, req.params._id);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(common_files.response_function(res, 500, false, req.t('ERROR'), doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.course_management_setting_session_list = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            isDeleted: false,
            _institution_id: ObjectId(req.headers._institution_id),
            'program._program_id': req.params._program_id,
        };
        const project = {
            _id: 1,
            program: 1,
            days: 1,
            session_type: 1,
            title: 1,
            time: 1,
            date: 1,
        };
        const doc = await base_control.get_list(course_management_setting, query, project);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SESSION_LIST_IS_NOT_FOUND'),
                        req.t('SESSION_LIST_IS_NOT_FOUND'),
                    ),
                );
        const extra_curricular_list = doc.data.filter(
            (ele) => ele.session_type == constant.COURSE_MANAGEMENT_SESSION_TYPE.EXTRA_CURRICULAR,
        );
        const breaks_list = doc.data.filter(
            (ele) => ele.session_type == constant.COURSE_MANAGEMENT_SESSION_TYPE.BREAK,
        );
        const obj = [
            {
                extra_curricular_list,
                breaks_list,
            },
        ];
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('SESSION_LIST'), obj));
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};
