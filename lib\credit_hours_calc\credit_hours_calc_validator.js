const Joi = require('joi');
const common_files = require('../utility/common');

exports.credit_hours_calc = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).required().allow('').error(error => {
                        return error;
                    }),
                    session_type: Joi.string().alphanum().min(1).max(20).required().error(error => {
                        return error;
                    }),
                    contact_hours: Joi.number().min(1).max(100).required().error(error => {
                        return error;
                    }),
                    credit_hours: Joi.number().min(1).max(10).required().error(error => {
                        return error;
                    }),
                    per_session: Joi.number().min(1).max(300).required().error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.credit_hours_calc_update = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    session_type: Joi.string().alphanum().min(1).max(20).error(error => {
                        return error;
                    }),
                    contact_hours: Joi.number().min(1).max(100).error(error => {
                        return error;
                    }),
                    credit_hours: Joi.number().min(1).max(10).error(error => {
                        return error;
                    }),
                    per_session: Joi.number().min(1).max(300).error(error => {
                        return error;
                    }),
                    _program_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.credit_hours_calc_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}