const identifyOrigin = (origin) => {
    // const urlParts = /([a-z-0-9]{2,63}).([a-z.]{2,5})$/.exec(origin);
    // if (!urlParts) return;
    // const [, domain, type] = urlParts;
    // let subdomain = origin.replace(`${domain}.${type}`, '').slice(0, -1);
    // subdomain = subdomain.slice(8, subdomain.length);
    // return {
    //     domain,
    //     type,
    //     subdomain,
    // };
    const urlParts = origin.split('.');
    const protocolSubdomain = urlParts[0];
    const sliceIndex = protocolSubdomain.includes('https') ? 8 : 7;
    return { subdomain: protocolSubdomain.slice(sliceIndex, protocolSubdomain.length) };
};

module.exports = { identifyOrigin };
