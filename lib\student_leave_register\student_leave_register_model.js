let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

const schema = new Schema({
    student_id:{
        type: String,
        required: true,
        trim: true,
    },
    student_name:{
        type: String,
        required: true,
        trim: true,
    },
    leave_type:{
        type: String,
        required: true,
        trim: true,
    },
    leave_start_date:{
        type: Date,
        required: true
    },
    leave_end_date:{
        type: Date,
        required: true
    },
    reason:{
        type: String,
        required: true,
        trim: true,
    },
    leave_document:{
        type: String,
        required: true,
        trim: true,
    },
    approval_date:{
        type: String,
        required: true,
        trim: true,
    },
    approver_comment:{
        type: String,
        required: true,
        trim: true,
    },
    comment:{
        type: String,
        trim: true,
    }
},
{ timestamps: true });
module.exports = mongoose.model(constant.STUDENT_LEAVE_REGISTER, schema);

