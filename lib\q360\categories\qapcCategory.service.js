const qapcFormCategorySchema = require('./qapcCategory.model');
const { convertToMongoObjectId } = require('../../utility/common');

const checkDuplicateValue = ({ searchKey }) => {
    try {
        return { $regex: new RegExp(`^${searchKey}$`, 'i') };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

const checkDuplicateCategoryName = async ({ categoryName, categoryId }) => {
    try {
        const checkCategoryNameCount = await qapcFormCategorySchema.countDocuments({
            ...(categoryId && { _id: { $ne: convertToMongoObjectId(categoryId) } }),
            categoryName: checkDuplicateValue({ searchKey: categoryName }),
        });
        return { checkCategoryNameCount };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    checkDuplicateCategoryName,
    checkDuplicateValue,
};
