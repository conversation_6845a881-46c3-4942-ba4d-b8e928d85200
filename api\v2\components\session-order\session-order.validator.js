const Joi = require('joi');

const addEditSessionValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                sessions: Joi.any()
                    .when('areActive', {
                        is: false,
                        then: Joi.array()
                            .items(
                                Joi.object().keys({
                                    _id: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .error((error) => error),
                                    sNo: Joi.number()
                                        .integer()
                                        .optional()
                                        .error((error) => error),
                                    deliveryType: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    mode: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    deliveryNumber: Joi.number()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    deliverySymbol: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    deliveryTypeId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .optional()
                                        .error((error) => error),
                                    deliveryTopic: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    subjects: Joi.array()
                                        .allow([])
                                        .optional()
                                        .items(
                                            Joi.object().keys({
                                                _subject_id: Joi.string()
                                                    .alphanum()
                                                    .length(24)
                                                    .optional()
                                                    .error((error) => error),
                                                subjectName: Joi.string()
                                                    .optional()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    duration: Joi.number()
                                        .allow('')
                                        .integer()
                                        .optional()
                                        .error((error) => error),
                                    sessionId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .optional()
                                        .error((error) => error),
                                    week: Joi.array()
                                        .optional()
                                        .allow([])
                                        .items(
                                            Joi.object().keys({
                                                _level_id: Joi.string()
                                                    .alphanum()
                                                    .length(24)
                                                    .error((error) => error),
                                                levelNo: Joi.string()
                                                    .optional()
                                                    .error((error) => error),
                                                weekNo: Joi.number()
                                                    .integer()
                                                    .optional()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    theme: Joi.string().error((error) => error),
                                }),
                            )
                            .optional(),
                        otherwise: Joi.array()
                            .items(
                                Joi.object().keys({
                                    _id: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .error((error) => error),
                                    sNo: Joi.number()
                                        .integer()
                                        .required()
                                        .error((error) => error),
                                    deliveryType: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    mode: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    deliveryNumber: Joi.number()
                                        .required()
                                        .error((error) => error),
                                    deliverySymbol: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    deliveryTypeId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .required()
                                        .error((error) => error),
                                    deliveryTopic: Joi.string().error((error) => error),
                                    subjects: Joi.array()
                                        .required()
                                        .items(
                                            Joi.object().keys({
                                                _subject_id: Joi.string()
                                                    .alphanum()
                                                    .length(24)
                                                    .required()
                                                    .error((error) => error),
                                                subjectName: Joi.string()
                                                    .required()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    duration: Joi.number()
                                        .integer()
                                        .required()
                                        .error((error) => error),
                                    sessionId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .error((error) => error),
                                    week: Joi.array()
                                        .items(
                                            Joi.object().keys({
                                                _level_id: Joi.string()
                                                    .alphanum()
                                                    .length(24)
                                                    .error((error) => error),
                                                levelNo: Joi.string()
                                                    .required()
                                                    .error((error) => error),
                                                weekNo: Joi.number()
                                                    .integer()
                                                    .required()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    theme: Joi.string().error((error) => error),
                                }),
                            )
                            .required(),
                    })
                    .error((error) => error),
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                areActive: Joi.boolean().required((error) => error),
                isCourseLevelDraft: Joi.boolean().error((error) => error),
                isSessionOrderWeekDraft: Joi.boolean().error((error) => error),
                linkSessionsOrder: Joi.array().items(
                    Joi.object({
                        startSession: Joi.number().error((error) => {
                            return error;
                        }),
                        endSession: Joi.number().error((error) => {
                            return error;
                        }),
                        year: Joi.string().error((error) => {
                            return error;
                        }),
                        levelNo: Joi.string().error((error) => {
                            return error;
                        }),
                        _level_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
                _level_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const addEditIndependentSessionValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                sessions: Joi.any()
                    .when('areActive', {
                        is: false,
                        then: Joi.array()
                            .items(
                                Joi.object().keys({
                                    _id: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .error((error) => error),
                                    sNo: Joi.number()
                                        .integer()
                                        .optional()
                                        .error((error) => error),
                                    deliveryType: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    mode: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    deliveryNumber: Joi.number()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    deliverySymbol: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    deliveryTypeId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .optional()
                                        .error((error) => error),
                                    deliveryTopic: Joi.string()
                                        .allow('')
                                        .optional()
                                        .error((error) => error),
                                    subjects: Joi.array()
                                        .allow([])
                                        .optional()
                                        .items(
                                            Joi.object().keys({
                                                _subject_id: Joi.string()
                                                    .alphanum()
                                                    .length(24)
                                                    .optional()
                                                    .error((error) => error),
                                                subjectName: Joi.string()
                                                    .optional()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    duration: Joi.number()
                                        .allow('')
                                        .integer()
                                        .optional()
                                        .error((error) => error),
                                    sessionId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .optional()
                                        .error((error) => error),
                                    week: Joi.array()
                                        .optional()
                                        .allow([])
                                        .items(
                                            Joi.object().keys({
                                                _level_id: Joi.string()
                                                    .optional()
                                                    .alphanum()
                                                    .length(24)
                                                    .error((error) => error),
                                                levelNo: Joi.string()
                                                    .optional()
                                                    .error((error) => error),
                                                weekNo: Joi.number()
                                                    .integer()
                                                    .optional()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    theme: Joi.string().error((error) => error),
                                }),
                            )
                            .optional(),
                        otherwise: Joi.array()
                            .items(
                                Joi.object().keys({
                                    _id: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .error((error) => error),
                                    sNo: Joi.number()
                                        .integer()
                                        .required()
                                        .error((error) => error),
                                    deliveryType: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    mode: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    deliveryNumber: Joi.number()
                                        .required()
                                        .error((error) => error),
                                    deliverySymbol: Joi.string()
                                        .required()
                                        .error((error) => error),
                                    deliveryTypeId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .required()
                                        .error((error) => error),
                                    deliveryTopic: Joi.string().error((error) => error),
                                    subjects: Joi.array()
                                        .required()
                                        .items(
                                            Joi.object().keys({
                                                _subject_id: Joi.string()
                                                    .alphanum()
                                                    .length(24)
                                                    .required()
                                                    .error((error) => error),
                                                subjectName: Joi.string()
                                                    .required()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    duration: Joi.number()
                                        .integer()
                                        .required()
                                        .error((error) => error),
                                    sessionId: Joi.string()
                                        .alphanum()
                                        .length(24)
                                        .error((error) => error),
                                    week: Joi.array()
                                        .items(
                                            Joi.object().keys({
                                                _level_id: Joi.string()
                                                    .alphanum()
                                                    .optional()
                                                    .length(24)
                                                    .error((error) => error),
                                                levelNo: Joi.string()
                                                    .optional()
                                                    .error((error) => error),
                                                weekNo: Joi.number()
                                                    .integer()
                                                    .required()
                                                    .error((error) => error),
                                            }),
                                        )
                                        .error((error) => error),
                                    theme: Joi.string().error((error) => error),
                                }),
                            )
                            .required(),
                    })
                    .error((error) => error),
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                areActive: Joi.boolean().required((error) => error),
                isWeekActiveForIndependentCourse: Joi.boolean()
                    .optional()
                    .error((error) => error),
                linkSessionsOrder: Joi.array().items(
                    Joi.object({
                        startSession: Joi.number().error((error) => {
                            return error;
                        }),
                        endSession: Joi.number().error((error) => {
                            return error;
                        }),
                        year: Joi.string().error((error) => {
                            return error;
                        }),
                        levelNo: Joi.string().error((error) => {
                            return error;
                        }),
                        _level_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _year_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                    }),
                ),
                isCourseLevelDraft: Joi.boolean().error((error) => error),
                isSessionOrderWeekDraft: Joi.boolean().error((error) => error),
                _level_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const deleteSessionValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error((error) => error),
        }),
    })
    .unknown(true);

const getSessionsValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                limit: Joi.number()
                    .integer()
                    .required()
                    .error((error) => error),
                pageNo: Joi.number()
                    .integer()
                    .required()
                    .error((error) => error),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
                _program_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

const getIndependentCourseSessionsValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                limit: Joi.number()
                    .integer()
                    .required()
                    .error((error) => error),
                pageNo: Joi.number()
                    .integer()
                    .required()
                    .error((error) => error),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);

module.exports = {
    addEditSessionValidator,
    deleteSessionValidator,
    getSessionsValidator,
    addEditIndependentSessionValidator,
    getIndependentCourseSessionsValidator,
};
