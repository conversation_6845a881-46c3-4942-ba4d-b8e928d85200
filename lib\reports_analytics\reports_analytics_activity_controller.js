const {
    response_function,
    convertToMongoObjectId,
    clone,
    responseFunctionWithRequest,
    query: commonQuery,
} = require('../utility/common');
const cloudFunctionAxios = require('../../cloud-function/cloud-function.axios');
const { redisClient } = require('../../config/redis-connection');
const { get_list, get, get_list_populate } = require('../base/base_controller');
const { staffSplit } = require('../utility/common_functions');
const { DIGI_COURSE, DIGI_SESSION_ORDER } = require('../utility/constants');
const course = require('mongoose').model(DIGI_COURSE);
const session_orders = require('mongoose').model(DIGI_SESSION_ORDER);
const {
    studentGroupList,
    activityQuestionList,
    studentActivityReport,
    activityFormate,
    studentGroupActivityMerge,
    activitySGGroupMerge,
    getCourseCLO,
    getCourseSLO,
    getCoursePLO,
    studentActivitySLOReport,
    studentGroupActivitySLOMerge,
    activitySGGroupSLOMerge,
    studentActivityCLOReport,
    studentGroupActivityCLOMerge,
    activitySGGroupCLOMerge,
    studentActivityPLOReport,
    studentGroupActivityPLOMerge,
    activitySGGroupPLOMerge,
    mapCloSloToClo,
    calculateMarks,
    getStudentGroupWithMappedCloSlo,
    mapCloSloToPlo,
    calculateMarksForPLO,
    getAllQuestionPLO,
    getStudentGroupQuestionsPLO,
    getGroupPloData,
    getClosBySloIds,
    getClosBySloId,
    getPlosByCloIds,
    getGroupCloData,
    studentActivityPLOReportForStudent,
    getStudentsWithMappedCloSloForStudent,
    mapCloSloToCloForStudents,
    mapCloSloToPloForStudents,
    calculateMarksForPLOForStudents,
    getAllActivityPLO,
    getGroupPloDataForStudents,
    calculateMarksForCLOForStudents,
    getAllActivityCLO,
    studentActivitySessionReport,
    getSloMappedCount,
    getPlosByCloId,
} = require('./reports_analytics_services');
const { DC_STAFF, SUMMARY, SLO, CLO, PLO } = require('../utility/constants');
const { allCourseList, allSessionOrderDatas } = require('../../service/cache.service');
const { activityReportFromCloudFunction } = require('../utility/utility.service');

// Get Activity List based on Course wise
exports.courseActivitySummary = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, levelNo, term, userType, tab },
            query: { rotation_count, userId },
        } = req;
        let response = {};
        let activityReportRedisKey = `Activity_Reports:activityReport_${institutionCalendarId}_${programId}_${levelNo}_${term}_${courseId}_${userType}_${tab}`;
        if (userId !== undefined) {
            activityReportRedisKey += `_${userId}`;
        }
        if (rotation_count !== undefined) {
            activityReportRedisKey += `_${rotation_count}`;
        }
        let cacheResponse = await redisClient.Client.get(activityReportRedisKey);
        if (cacheResponse) {
            response = JSON.parse(cacheResponse);
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        'COURSE_ACTIVITY_SUMMARY',
                        response,
                    ),
                );
        }
        await activityReportFromCloudFunction({
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotation_count,
            userId,
        });
        cacheResponse = await redisClient.Client.get(activityReportRedisKey);
        if (cacheResponse) {
            response = JSON.parse(cacheResponse);
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        'COURSE_ACTIVITY_SUMMARY',
                        response,
                    ),
                );
        }
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );
        // const courseData = await get(course, { _id: convertToMongoObjectId(courseId) }, {});
        if (!courseData)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, req.t('COURSE_NOT_FOUND'), {}));
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        );
        const course_assigned_details = courseData.course_assigned_details.find(
            (ele) => ele.level_no === levelNo,
        );
        const course_details = {
            course_name: courseData.course_name,
            course_code: courseData.course_code,
            versioned: courseData.versioned || false,
            versionName: courseData.versionName || '',
            versionedFrom: courseData.versionedFrom || null,
            versionedCourseIds: courseData.versionedCourseIds || [],
            framework_id: courseData.framework._id,
            framework_name: courseData.framework.name,
            framework_code: courseData.framework.code,
            program_name: course_assigned_details ? course_assigned_details.program_name : '',
            _program_id: course_assigned_details ? course_assigned_details._program_id : '',
            _year_id: course_assigned_details ? course_assigned_details._year_id : '',
            year: course_assigned_details ? course_assigned_details.year : '',
            level_no: course_assigned_details ? course_assigned_details.level_no : '',
            term,
        };
        response.course_details = course_details;
        console.time('activityQuestionList');
        const { activityList, questionList } = await activityQuestionList(
            courseId,
            term,
            rotation_count,
            institutionCalendarId,
            levelNo,
        );
        console.timeEnd('activityQuestionList');
        console.time('studentGroupList');
        const studentGroups = await studentGroupList(
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotation_count,
        );
        console.timeEnd('studentGroupList');
        let activitiesData = [];
        switch (tab) {
            case SUMMARY:
                await studentActivityReport(
                    studentGroups.studentWithOutGroupList,
                    activityList.data,
                );
                await studentGroupActivityMerge(studentGroups);
                activitiesData = await activityFormate(activityList.data);
                await activitySGGroupMerge(activitiesData, studentGroups.studentWithGroupList);
                response.questionListData = clone(activitiesData);
                if (userType === DC_STAFF) {
                    const courseGroup = [];
                    for (activityQues of activitiesData) {
                        let mark = 0;
                        let count = 0;
                        for (group of studentGroups.studentWithGroupList) {
                            const quesInd = group.studentGroupActivityQuestions.findIndex(
                                (ele) => ele._id.toString() === activityQues._id.toString(),
                            );
                            if (quesInd != -1) {
                                count++;
                                mark += group.studentGroupActivityQuestions[quesInd].mark;
                            }
                        }
                        if (mark != 0 && count != 0) mark /= count;
                        courseGroup.push({
                            _id: activityQues._id,
                            name: activityQues.name,
                            mark: activityQues.mark,
                        });
                    }
                    response.courseGroup = clone(courseGroup);
                    //Course Group Average
                    let students_count = 0;
                    let students_absent_count = 0;
                    let students_present_count = 0;
                    let avg = 0;
                    studentGroups.studentWithGroupList.forEach((ele) => {
                        if (ele.average != null) {
                            students_count += ele.students.length;
                            avg += ele.average;
                        }
                        let students_absent_count_group_wise = 0;
                        let students_present_count_group_wise = 0;
                        ele.students.forEach((eleStudent) => {
                            if (eleStudent.mark === null) {
                                students_absent_count++;
                                students_absent_count_group_wise++;
                            } else {
                                students_present_count++;
                                students_present_count_group_wise++;
                            }
                        });
                        ele.students_present_count = students_present_count_group_wise;
                        ele.students_absent_count = students_absent_count_group_wise;
                    });
                    if (avg != 0) response.courseGroupAverage = avg / students_present_count;
                    else response.courseGroupAverage = null;
                    response.studentPresentCount = students_present_count;
                    response.studentAbsentCount = students_absent_count;
                    //response.students_count = students_count;

                    response.studentGroups = studentGroups.studentWithGroupList;
                } else {
                    const student = studentGroups.studentWithOutGroupList.find(
                        (studentElement) =>
                            studentElement._student_id.toString() === userId.toString(),
                    );
                    response.courseGroup = [];
                    response.studentData = student;
                }
                break;
            case SLO:
                {
                    const courseSloData = await getCourseSLO({ courseSessionOrder });
                    // activitiesData = await activityFormate(activityList.data);
                    await studentActivitySLOReport(
                        studentGroups.studentWithOutGroupList,
                        activityList.data,
                        courseSloData.data,
                    );
                    await studentGroupActivitySLOMerge(studentGroups);
                    const groupSloData = await activitySGGroupSLOMerge(
                        studentGroups.studentWithGroupList,
                    );
                    response.groupSloData = clone(
                        groupSloData.sort((a, b) => {
                            return a.lecture.localeCompare(b.lecture, undefined, {
                                numeric: true,
                                sensitivity: 'base',
                            });
                        }),
                    );
                    if (userType === DC_STAFF) {
                        //Average calc
                        studentGroups.studentWithGroupList.forEach((eleStudG) => {
                            let mark = 0;
                            let count = 0;
                            eleStudG.studentGroupActivitySLO.forEach((eleStudGActivity) => {
                                count++;
                                mark += eleStudGActivity.mark;
                            });
                            eleStudG.average = count == 0 ? null : mark / count;
                        });
                        //Course Group Average
                        let students_count = 0;
                        let students_absent_count = 0;
                        let students_present_count = 0;
                        let avg = 0;
                        studentGroups.studentWithGroupList.forEach((ele) => {
                            if (ele.average != null) {
                                students_count += ele.students.length;
                                avg += ele.average;
                            }
                            let students_absent_count_group_wise = 0;
                            let students_present_count_group_wise = 0;
                            ele.students.forEach((eleStudent) => {
                                if (eleStudent.mark === null) {
                                    students_absent_count++;
                                    students_absent_count_group_wise++;
                                } else {
                                    students_present_count++;
                                    students_present_count_group_wise++;
                                }
                            });
                            ele.students_present_count = students_present_count_group_wise;
                            ele.students_absent_count = students_absent_count_group_wise;
                        });
                        if (avg != 0) response.courseGroupAverage = avg / students_present_count;
                        else response.courseGroupAverage = null;
                        response.studentGroups = studentGroups.studentWithGroupList;
                        response.studentPresentCount = students_present_count;
                        response.studentAbsentCount = students_absent_count;
                        //response.students_count = students_count;
                    } else {
                        const studentData = clone(
                            studentGroups.studentWithOutGroupList.find(
                                (studentElement) =>
                                    studentElement._student_id.toString() === userId.toString(),
                            ),
                        );
                        for (studentActivityElement of studentData.studentActivities) {
                            for (groupStudentElement of studentGroups.studentWithOutGroupList) {
                                const otherStudentActivity =
                                    groupStudentElement.studentActivities.find(
                                        (groupStudentActivityElement) =>
                                            groupStudentActivityElement._id.toString() ===
                                            studentActivityElement._id.toString(),
                                    );
                                if (otherStudentActivity)
                                    for (studentSloElement of studentActivityElement.solData) {
                                        const otherStudentActivitySlo =
                                            otherStudentActivity.solData.find(
                                                (otherSloElement) =>
                                                    otherSloElement._id.toString() ===
                                                    studentSloElement._id.toString(),
                                            );
                                        if (
                                            otherStudentActivitySlo &&
                                            otherStudentActivitySlo.mark !== undefined &&
                                            otherStudentActivitySlo.mark !== null &&
                                            otherStudentActivitySlo.count !== 0
                                        ) {
                                            studentSloElement.otherMark +=
                                                otherStudentActivitySlo.mark;
                                            studentSloElement.otherCount++;
                                        }
                                    }
                            }
                            const studentSlo = { mark: 0, count: 0 };
                            for (studentSloElement of studentActivityElement.solData) {
                                studentSloElement.otherMarks =
                                    studentSloElement.otherMark / studentSloElement.otherCount;
                                if (
                                    studentSloElement.mark !== null &&
                                    studentSloElement.count !== null &&
                                    studentSloElement.count !== 0
                                ) {
                                    studentSlo.mark += studentSloElement.mark;
                                    studentSlo.count++;
                                }
                            }
                            if (studentSlo.count !== 0)
                                studentActivityElement.mark = studentSlo.mark / studentSlo.count;
                        }
                        //Average calc
                        // let count = 0;
                        // let mark = 0;
                        // studentGroups.studentWithGroupList.forEach((eleStudG) => {
                        //     eleStudG.studentGroupActivitySLO.forEach((eleStudGActivity) => {
                        //         count++;
                        //         mark += eleStudGActivity.mark;
                        //     });
                        //     eleStudG.average = count === 0 ? 0 : mark / count;
                        // });
                        response.studentData = studentData;
                    }
                }
                break;
            case CLO:
                {
                    const courseCloData = await getCourseCLO(courseId);
                    const courseSloData = await getCourseSLO({ courseSessionOrder });
                    // Framework Theme wise loading
                    const themeDatas = [];
                    if (userType === DC_STAFF) {
                        for (themeElement of courseCloData.data) {
                            const studentGroupElement = studentGroups;
                            //getStudentWiseMappedCLOSLOQuestions
                            const studGroupWithMappedCloSlo = await getStudentGroupWithMappedCloSlo(
                                studentGroupElement.studentWithGroupList,
                                activityList.data,
                                themeElement.clos,
                                courseSloData.data,
                            );
                            // return res.send(studGroupWithMappedCloSlo);
                            const mappedCloSloToClo = await mapCloSloToClo(
                                studGroupWithMappedCloSlo,
                                themeElement.clos,
                            );

                            //Calculate Marks and allStudentQuestion calculation
                            const mappedCloSloToCloMarksCalculation = await calculateMarks(
                                mappedCloSloToClo,
                            );

                            //Group CLO Data
                            const groupCloDataNew = [];
                            for (eleMappedCloSloToCloMarksCalculation of mappedCloSloToCloMarksCalculation) {
                                for (eleStudentGroupQuestionsClos of eleMappedCloSloToCloMarksCalculation.studentGroupQuestionsClos) {
                                    const ind = groupCloDataNew.findIndex(
                                        (ele) =>
                                            ele._id.toString() ===
                                            eleStudentGroupQuestionsClos._id.toString(),
                                    );
                                    if (ind === -1) {
                                        groupCloDataNew.push({
                                            _id: eleStudentGroupQuestionsClos._id,
                                            no: eleStudentGroupQuestionsClos.no,
                                            name: eleStudentGroupQuestionsClos.name,
                                            mark: eleStudentGroupQuestionsClos.mark,
                                            count: 1,
                                        });
                                    } else {
                                        groupCloDataNew[ind].mark +=
                                            eleStudentGroupQuestionsClos.mark;
                                        groupCloDataNew[ind].count++;
                                    }
                                }
                            }

                            for (eleGroupCloDataNew of groupCloDataNew) {
                                eleGroupCloDataNew.mark /= eleGroupCloDataNew.count;
                            }
                            //Average Calculation
                            for (eleStudentGroups of mappedCloSloToCloMarksCalculation) {
                                //Students Average
                                for (eleStudents of eleStudentGroups.students) {
                                    let mark = 0;
                                    for (eleAllQuestionsClos of eleStudents.AllQuestionsClos) {
                                        mark += eleAllQuestionsClos.mark;
                                    }
                                    eleStudents.mark = null;
                                    if (eleStudents.AllQuestionsClos.length > 0)
                                        eleStudents.mark =
                                            mark / eleStudents.AllQuestionsClos.length;
                                }
                                //Student Group average
                                let sgMark = 0;
                                for (eleStudentGroupQuestionsClos of eleStudentGroups.studentGroupQuestionsClos) {
                                    sgMark += eleStudentGroupQuestionsClos.mark;
                                }
                                eleStudentGroups.average = null;
                                if (eleStudentGroups.studentGroupQuestionsClos.length > 0)
                                    eleStudentGroups.average =
                                        sgMark / eleStudentGroups.studentGroupQuestionsClos.length;
                            }

                            //Course Group Average
                            const studentGroupData = clone(mappedCloSloToCloMarksCalculation);
                            let students_count = 0;
                            let students_absent_count = 0;
                            let students_present_count = 0;
                            let avg = 0;
                            studentGroupData.forEach((ele) => {
                                if (ele.average != null) {
                                    students_count += ele.students.length;
                                    avg += ele.average;
                                }
                                let students_absent_count_group_wise = 0;
                                let students_present_count_group_wise = 0;
                                ele.students.forEach((eleStudent) => {
                                    if (eleStudent.mark === null) {
                                        students_absent_count++;
                                        students_absent_count_group_wise++;
                                    } else {
                                        students_present_count++;
                                        students_present_count_group_wise++;
                                    }
                                });
                                ele.students_present_count = students_present_count_group_wise;
                                ele.students_absent_count = students_absent_count_group_wise;
                            });
                            let courseGroupAverage = 0;
                            if (avg != 0) courseGroupAverage = avg / students_present_count;
                            else courseGroupAverage = null;

                            themeDatas.push({
                                _id: themeElement._id,
                                no: themeElement.no,
                                name: themeElement.name,
                                groupCloData: groupCloDataNew,
                                courseGroupAverage,
                                students_present_count,
                                students_absent_count,
                                //studentGroups: clone(mappedCloSloToCloMarksCalculation),
                                studentGroups: clone(studentGroupData),
                            });
                        }
                    } else {
                        for (themeElement of courseCloData.data) {
                            // Staff Flow for getting overAll average
                            const studentGroupStaffElement = clone(
                                studentGroups.studentWithGroupList,
                            );
                            //getStudentWiseMappedCLOSLOQuestions
                            const studGroupWithMappedCloSloStaffFlow =
                                await getStudentGroupWithMappedCloSlo(
                                    studentGroupStaffElement,
                                    activityList.data,
                                    themeElement.clos,
                                    courseSloData.data,
                                );
                            const mappedCloSloToCloStaffFlow = await mapCloSloToClo(
                                studGroupWithMappedCloSloStaffFlow,
                                themeElement.clos,
                            );
                            const mappedCloSloToCloMarksCalculationStaffFlow = await calculateMarks(
                                mappedCloSloToCloStaffFlow,
                            );
                            const groupCloDataNew = [];
                            for (eleMappedCloSloToCloMarksCalculation of mappedCloSloToCloMarksCalculationStaffFlow) {
                                for (eleStudentGroupQuestionsClos of eleMappedCloSloToCloMarksCalculation.studentGroupQuestionsClos) {
                                    const ind = groupCloDataNew.findIndex(
                                        (ele) =>
                                            ele._id.toString() ===
                                            eleStudentGroupQuestionsClos._id.toString(),
                                    );
                                    if (ind === -1) {
                                        groupCloDataNew.push({
                                            _id: eleStudentGroupQuestionsClos._id,
                                            no: eleStudentGroupQuestionsClos.no,
                                            name: eleStudentGroupQuestionsClos.name,
                                            mark: eleStudentGroupQuestionsClos.mark,
                                            count: 1,
                                        });
                                    } else {
                                        groupCloDataNew[ind].mark +=
                                            eleStudentGroupQuestionsClos.mark;
                                        groupCloDataNew[ind].count++;
                                    }
                                }
                            }

                            for (eleGroupCloDataNew of groupCloDataNew) {
                                eleGroupCloDataNew.mark /= eleGroupCloDataNew.count;
                            }

                            // Student Flow
                            const studentGroupElement = clone(studentGroups);
                            const studGroupWithMappedCloSlo =
                                await getStudentsWithMappedCloSloForStudent(
                                    studentGroupElement.studentWithOutGroupList,
                                    activityList.data,
                                    themeElement.clos,
                                    courseSloData.data,
                                );
                            const mappedCloSloToClo = await mapCloSloToCloForStudents(
                                studGroupWithMappedCloSlo,
                                themeElement.clos,
                            );
                            const mappedCloSloToCloMarksCalculation =
                                await calculateMarksForCLOForStudents(mappedCloSloToClo);
                            //AllActivity PLO
                            const allActivityCLO = await getAllActivityCLO(
                                mappedCloSloToCloMarksCalculation,
                            );
                            // const groupCloData = await getGroupCloData(
                            //     studentGroupElement.studentWithOutGroupList,
                            // );
                            const studentData = allActivityCLO.find(
                                (ele) => ele._student_id.toString() === userId.toString(),
                            );

                            for (studentActivityElement of studentData.studentActivities) {
                                for (groupStudentElement of allActivityCLO) {
                                    const otherStudentActivity =
                                        groupStudentElement.studentActivities.find(
                                            (groupStudentActivityElement) =>
                                                groupStudentActivityElement._id.toString() ===
                                                studentActivityElement._id.toString(),
                                        );
                                    if (
                                        otherStudentActivity &&
                                        otherStudentActivity._id.toString() ===
                                            studentActivityElement._id.toString()
                                    ) {
                                        for (studentCloElement of studentActivityElement.cloData) {
                                            const otherStudentActivityClo =
                                                otherStudentActivity.cloData.find(
                                                    (otherSloElement) =>
                                                        otherSloElement._id.toString() ===
                                                        studentCloElement._id.toString(),
                                                );
                                            if (
                                                otherStudentActivityClo &&
                                                otherStudentActivityClo.mark !== undefined &&
                                                otherStudentActivityClo.mark !== null
                                            ) {
                                                studentCloElement.otherMark +=
                                                    otherStudentActivityClo.mark;
                                                studentCloElement.otherCount++;
                                                /* studentCloElement.otherCount +=
                                                    otherStudentActivityClo.count; */
                                            }
                                        }
                                    }
                                }
                                for (studentCloElement of studentActivityElement.cloData)
                                    studentCloElement.otherMarks =
                                        studentCloElement.otherMark / studentCloElement.otherCount;
                            }
                            themeDatas.push({
                                _id: themeElement._id,
                                no: themeElement.no,
                                name: themeElement.name,
                                groupCloData: clone(groupCloDataNew),
                                studentData,
                            });
                        }
                    }
                    response.theme = clone(themeDatas);
                }
                break;
            case PLO:
                {
                    const courseCloData = await getCourseCLO(courseId);
                    const courseSloData = await getCourseSLO({ courseSessionOrder });
                    const cloIds = courseCloData.data
                        .map((ele) => ele.clos)
                        .flat()
                        .map((ele2) => ele2._id);
                    const ploDetails = await getCoursePLO(cloIds);
                    const courseClos = courseCloData.data.map((ele) => ele.clos).flat();
                    const curriculums = [];
                    if (userType === DC_STAFF) {
                        for (curriculumElement of ploDetails.data) {
                            // Framework Theme wise loading
                            const themeDatas = [];
                            for (themeElement of curriculumElement.framework.domains) {
                                const studentGroupElement = clone(studentGroups);
                                const studGroupWithMappedCloSlo =
                                    await getStudentGroupWithMappedCloSlo(
                                        studentGroupElement.studentWithGroupList,
                                        activityList.data,
                                        courseClos,
                                        courseSloData.data,
                                    );
                                const mappedCloSloToClo = await mapCloSloToClo(
                                    studGroupWithMappedCloSlo,
                                    courseClos,
                                );
                                const mappedCloSloToPlo = await mapCloSloToPlo(
                                    mappedCloSloToClo,
                                    themeElement.plo,
                                );
                                const mappedCloSloToPloMarksCalculation =
                                    await calculateMarksForPLO(mappedCloSloToPlo);

                                const allQuetionsPLOSG = await getAllQuestionPLO(
                                    mappedCloSloToPloMarksCalculation,
                                );
                                const studentGroupQuetionsPLO = await getStudentGroupQuestionsPLO(
                                    allQuetionsPLOSG,
                                );
                                //return res.send(studentGroupQuetionsPLO);
                                const groupPloData = await getGroupPloData(studentGroupQuetionsPLO);
                                const ploMappedData = clone(studentGroupQuetionsPLO);

                                //Average calc
                                ploMappedData.forEach((eleStudG) => {
                                    let aMark = 0;
                                    let aCount = 0;
                                    eleStudG.studentGroupQuestionsPlos.forEach((eleSGQuestPLO) => {
                                        aCount++;
                                        aMark += eleSGQuestPLO.mark;
                                    });
                                    eleStudG.average =
                                        aCount == 0 ? null : Math.round(aMark / aCount);
                                });
                                //Course Group Average
                                const studentGroupData = clone(ploMappedData);
                                let students_count = 0;
                                let students_absent_count = 0;
                                let students_present_count = 0;
                                let avg = 0;
                                studentGroupData.forEach((ele) => {
                                    if (ele.average != null) {
                                        students_count += ele.students.length;
                                        avg += ele.average;
                                    }
                                    let students_absent_count_group_wise = 0;
                                    let students_present_count_group_wise = 0;
                                    ele.students.forEach((eleStudent) => {
                                        if (eleStudent.mark === null) {
                                            students_absent_count++;
                                            students_absent_count_group_wise++;
                                        } else {
                                            students_present_count++;
                                            students_present_count_group_wise++;
                                        }
                                    });
                                    ele.students_present_count = students_present_count_group_wise;
                                    ele.students_absent_count = students_absent_count_group_wise;
                                });
                                let courseGroupAverage = 0;
                                if (avg != 0) courseGroupAverage = avg / students_present_count;
                                else courseGroupAverage = null;
                                themeDatas.push({
                                    _id: themeElement._id,
                                    no: themeElement.no,
                                    name: themeElement.name,
                                    groupPloData,
                                    courseGroupAverage,
                                    students_present_count,
                                    students_absent_count,
                                    //plo: clone(ploDatas),
                                    //studentGroups: clone(ploMappedData),
                                    studentGroups: clone(studentGroupData),
                                });
                            }
                            curriculums.push({
                                curriculum_name: curriculumElement.curriculum_name,
                                framework: {
                                    name: curriculumElement.framework.name,
                                    code: curriculumElement.framework.code,
                                    theme: clone(themeDatas),
                                },
                            });
                        }
                    } else {
                        for (curriculumElement of ploDetails.data) {
                            // Framework Theme wise loading
                            const themeDatas = [];
                            for (themeElement of curriculumElement.framework.domains) {
                                // Getting Student Overall Mark as PLO based Staff Flow
                                const studentGroupElements = clone(
                                    studentGroups.studentWithGroupList,
                                );
                                // const studentGroupElements = clone(sgGroup);
                                const studGroupWithMappedCloSlo =
                                    await getStudentGroupWithMappedCloSlo(
                                        studentGroupElements,
                                        activityList.data,
                                        courseClos,
                                        courseSloData.data,
                                    );
                                const mappedCloSloToCloStaffFlow = await mapCloSloToClo(
                                    studGroupWithMappedCloSlo,
                                    courseClos,
                                );
                                const mappedCloSloToPloStaffFlow = await mapCloSloToPlo(
                                    mappedCloSloToCloStaffFlow,
                                    themeElement.plo,
                                );
                                const mappedCloSloToPloMarksCalculationStaffFlow =
                                    await calculateMarksForPLO(mappedCloSloToPloStaffFlow);

                                const allQuetionsPLOSG = await getAllQuestionPLO(
                                    mappedCloSloToPloMarksCalculationStaffFlow,
                                );
                                const studentGroupQuetionsPLO = await getStudentGroupQuestionsPLO(
                                    allQuetionsPLOSG,
                                );
                                const groupPloStaffData = await getGroupPloData(
                                    studentGroupQuetionsPLO,
                                );
                                let studentAllActivityPloReportBasedStaff = [];
                                for (sgElement of allQuetionsPLOSG) {
                                    const studentData = sgElement.students.find(
                                        (studentElement) =>
                                            studentElement._student_id.toString() ===
                                            userId.toString(),
                                    );
                                    if (studentData && studentData.AllQuestionsPlos)
                                        studentAllActivityPloReportBasedStaff =
                                            studentData.AllQuestionsPlos;
                                }

                                // Student Flow
                                const studentGroupElement = clone(studentGroups);
                                const studentsWithMappedCloSlo =
                                    await getStudentsWithMappedCloSloForStudent(
                                        studentGroupElement.studentWithOutGroupList,
                                        activityList.data,
                                        courseClos,
                                        courseSloData.data,
                                    );
                                const mappedCloSloToClo = await mapCloSloToCloForStudents(
                                    studentsWithMappedCloSlo,
                                    courseClos,
                                );
                                const mappedCloSloToPlo = await mapCloSloToPloForStudents(
                                    mappedCloSloToClo,
                                    themeElement.plo,
                                );

                                const mappedCloSloToPloMarksCalculation =
                                    await calculateMarksForPLOForStudents(mappedCloSloToPlo);
                                //AllActivity PLO
                                const allActivityPLO = await getAllActivityPLO(
                                    mappedCloSloToPloMarksCalculation,
                                    studentAllActivityPloReportBasedStaff,
                                );
                                const studentData = clone(
                                    allActivityPLO.find(
                                        (ele) => ele._student_id.toString() === userId.toString(),
                                    ),
                                );
                                for (studentActivityElement of studentData.studentActivities) {
                                    for (groupStudentElement of allActivityPLO) {
                                        const otherStudentActivity =
                                            groupStudentElement.studentActivities.find(
                                                (groupStudentActivityElement) =>
                                                    groupStudentActivityElement._id.toString() ===
                                                    studentActivityElement._id.toString(),
                                            );
                                        if (otherStudentActivity)
                                            for (studentPloElement of studentActivityElement.ploData) {
                                                const otherStudentActivityPlo =
                                                    otherStudentActivity.ploData.find(
                                                        (otherSloElement) =>
                                                            otherSloElement._id.toString() ===
                                                            studentPloElement._id.toString(),
                                                    );
                                                if (
                                                    otherStudentActivityPlo &&
                                                    //otherStudentActivityPlo.mark &&
                                                    otherStudentActivityPlo.mark !== null
                                                ) {
                                                    studentPloElement.otherMark +=
                                                        otherStudentActivityPlo.mark;
                                                    studentPloElement.otherCount++;
                                                    /* studentPloElement.otherCount +=
                                                        otherStudentActivityPlo.count; */
                                                }
                                            }
                                    }
                                    for (studentPloElement of studentActivityElement.ploData)
                                        studentPloElement.otherMarks =
                                            studentPloElement.otherMark /
                                            studentPloElement.otherCount;
                                }
                                //GetGroupPloData
                                // const groupPloData = await getGroupPloDataForStudents(
                                //     allActivityPLO,
                                // );
                                themeDatas.push({
                                    _id: themeElement._id,
                                    no: themeElement.no,
                                    name: themeElement.name,
                                    // groupPloData: clone(groupPloData),
                                    groupPloData: clone(groupPloStaffData),
                                    //groupPloData: [],
                                    //average: groupPloAverage,
                                    studentData,
                                });
                            }
                            curriculums.push({
                                curriculum_name: curriculumElement.curriculum_name,
                                framework: {
                                    name: curriculumElement.framework.name,
                                    code: curriculumElement.framework.code,
                                    theme: clone(themeDatas),
                                },
                            });
                        }
                    }

                    response.curriculums = clone(curriculums);
                }
                break;
            case 'session':
                {
                    console.time('getCourseSLO');
                    const courseSloData = await getCourseSLO({ courseSessionOrder });
                    console.timeEnd('getCourseSLO');
                    const courseSession = courseSessionOrder.session_flow_data.map(
                        (sessionElement) => {
                            return {
                                _id: sessionElement._id,
                                s_no: sessionElement.s_no,
                                delivery_symbol: sessionElement.delivery_symbol,
                                delivery_no: sessionElement.delivery_no,
                                sloIds: sessionElement.slo.map((sloElement) =>
                                    sloElement._id.toString(),
                                ),
                            };
                        },
                    );
                    if (userType === DC_STAFF) {
                        response.sessionActivityReport = {};
                    } else {
                        console.time('studentActivitySessionReport');
                        const sessionActivityReport = await studentActivitySessionReport({
                            userId,
                            studentData: studentGroups.studentWithOutGroupList,
                            activityList: activityList.data,
                            courseSloData: courseSloData.data,
                            courseSession,
                        });
                        //return res.send(sessionActivityReport)
                        /* sessionActivityReport.courseSession = clone(
                            sessionActivityReport.courseSession.filter(
                                (ele) => ele.userSessionAllActivity !== null,
                            ),
                        ); */

                        console.timeEnd('studentActivitySessionReport');
                        const courseSessionArr = [];
                        for (eleCourseSession of sessionActivityReport.courseSession) {
                            if (eleCourseSession.userSessionAllActivity == null) continue;
                            courseSessionArr.push(eleCourseSession);
                        }
                        sessionActivityReport.courseSession = clone(courseSessionArr);
                        response.sessionActivityReport = clone(sessionActivityReport);
                        /* response.courseSloData = courseSloData.data.filter((eleCourseSloData) =>
                            sessionActivityReport.courseSession.find(
                                (eleCourseSession) =>
                                    eleCourseSession._id.toString() ===
                                    eleCourseSloData._session_id.toString(),
                            ),
                        ); */
                        response.courseSloData = courseSloData.data.map((sloElement) => {
                            return {
                                _id: sloElement._id,
                                no: sloElement.no,
                                delivery_symbol: sloElement.delivery_symbol,
                                s_no: sloElement.s_no,
                            };
                        });
                    }
                }
                break;
            default:
                break;
        }
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_ACTIVITY_SUMMARY'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
exports.getAllClo = async (req, res) => {
    try {
        const { sloIds } = req.body;
        const result = await getClosBySloIds(sloIds);
        return res.status(200).send(response_function(res, 200, true, req.t('CLO_LIST'), result));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR_CATCH'), error.toString()));
    }
};
exports.getAllCloNew = async (req, res) => {
    try {
        const { sloId, courseId } = req.params;
        const { cloDatas, cloMappedCount } = await getClosBySloId(sloId, courseId);
        return res
            .status(200)
            .send(
                response_function(res, 200, true, req.t('CLO_LIST'), { cloDatas, cloMappedCount }),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR_CATCH'), error.toString()));
    }
};
exports.getAllPlo = async (req, res) => {
    try {
        const { cloIds } = req.body;
        const result = await getPlosByCloIds(cloIds);
        return res.status(200).send(response_function(res, 200, true, req.t('PLO_LIST'), result));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR_CATCH'), error.toString()));
    }
};
exports.getAllPloNew = async (req, res) => {
    try {
        const { cloId } = req.params;
        const { ploDatas, ploMappedCount } = await getPlosByCloId(cloId);
        return res
            .status(200)
            .send(
                response_function(res, 200, true, req.t('PLO_LIST'), { ploDatas, ploMappedCount }),
            );
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR_CATCH'), error.toString()));
    }
};
exports.getSessionSlos = async (req, res) => {
    try {
        const { courseId, sessionId } = req.params;
        const sessionData = await get(
            session_orders,
            {
                ...commonQuery,
                _course_id: convertToMongoObjectId(courseId),
                'session_flow_data._id': convertToMongoObjectId(sessionId),
            },
            {},
        );
        if (!sessionData.status)
            return res
                .status(200)
                .send(response_function(res, 200, true, req.t('SESSION_NOT_FOUND'), {}));

        //return res.send(sessionData.data);
        const session_details = sessionData.data.session_flow_data.find(
            (ele) => ele._id.toString() === sessionId.toString(),
        );
        const slo = [];
        if (session_details) {
            for (const eleSlo of session_details.slo) {
                const sloDetails = await getClosBySloId(eleSlo._id, courseId);
                slo.push({
                    _id: eleSlo._id,
                    no: eleSlo.no,
                    name: eleSlo.name,
                    delivery_symbol: session_details.delivery_symbol,
                    clo_mapped_count: sloDetails.cloMappedCount ? sloDetails.cloMappedCount : 0,
                });
            }
        }
        return res.status(200).send(response_function(res, 200, true, req.t('SLO_LIST'), slo));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR_CATCH'), error.toString()));
    }
};
