const constant = require('../utility/constants');
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
// const program = require('mongoose').model(constant.PROGRAM);
const digi_program = require('mongoose').model(constant.DIGI_PROGRAM);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const staff = require('mongoose').model(constant.USER);
// const notification = require('../notification_manager/notification_manager_controller');
const user = require('mongoose').model(constant.USER);
const common_fun = require('../utility/common_functions');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const base_control = require('../base/base_controller');
// const { USER } = require('../utility/constants');

const { clearItem, allProgramCalendarDatas } = require('../../service/cache.service');

// Updating Program Calendar Flat Caching Data
const updateProgramCalendarFlatCacheData = async () => {
    clearItem('allProgramCalendar');
    await allProgramCalendarDatas();
};

exports.add_reviewer = async (req, res) => {
    try {
        const staff_id = [...new Set(req.body._reviewer_id)];
        const user_check = await base_control.check_id(user, {
            _id: { $in: staff_id },
            isDeleted: false,
        });
        const checks_dup = await base_control.get(
            program_calendar,
            {
                _id: ObjectId(req.body._calendar_id),
                review: { $elemMatch: { _reviewer_id: { $in: staff_id } } },
                isDeleted: false,
            },
            { _id: 1 },
        );
        if (!user_check.status || (user_check.status && user_check.data.length !== staff_id.length))
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('USER_NOT_FOUND'),
                        req.t('USER_NOT_FOUND'),
                    ),
                );
        if (checks_dup.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('ERROR_REVIEWER_ALREADY_PRESENT'),
                        req.t('DUPLICATE_REVIEWER'),
                    ),
                );
        const reviewers = user_check.data.map((userField) => ({
            _reviewer_id: userField._id,
            reviewer_name: userField.name,
            status: 'Pending',
        }));
        const reviewer_add = await base_control.update_push_pull_many(
            program_calendar,
            { _id: ObjectId(req.body._calendar_id) },
            { $push: { review: reviewers } },
        );
        updateProgramCalendarFlatCacheData();
        if (!reviewer_add.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_ADD_REVIEWER'),
                        req.t('UNABLE_TO_ADD_REVIEWER'),
                    ),
                );
        await base_control.update_condition(
            user,
            { _id: { $in: staff_id } },
            {
                /* $set: { role: 'Program_Calendar_Reviewer' }, */ $push: {
                    sub_role: 'Program_Calendar_Reviewer',
                },
            },
        );
        //Need to Set Role & Permission for Program Calendar Reviewer
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('REVIEWER_ADD_SUCCESSFULLY'),
                    req.t('REVIEWER_ADD_SUCCESSFULLY'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.remove_reviewer = async (req, res) => {
    const doc = await base_control.update_push_pull_many(
        program_calendar,
        { _id: ObjectId(req.params.id), isDeleted: false },
        { $pull: { review: { _reviewer_id: ObjectId(req.params.reviewer) } } },
    );
    updateProgramCalendarFlatCacheData();
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_REMOVE_REVIEWER'),
                    req.t('UNABLE_TO_REMOVE_REVIEWER'),
                ),
            );

    //Need to Set Role & Permission for Program Calendar Reviewer
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('REVIEWER_REMOVED_SUCCESSFULLY'),
                req.t('REVIEWER_REMOVED_SUCCESSFULLY'),
            ),
        );
};

exports.add_reviewer_review = async (req, res) => {
    const id = ObjectId(req.body._calendar_id);
    checks = await base_control.check_id(program_calendar, { _id: id, isDeleted: false });
    if (!checks.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('PROGRAM_CALENDAR_ID_NOT_MATCH'),
                    req.t('PROGRAM_CALENDAR_ID_NOT_MATCH'),
                ),
            );
    let object_id = { _id: id, isDeleted: false, 'review._reviewer_id': req.body._reviewer_id };
    const objs = {};
    if (req.body.reviewer_type === 'reviewer') {
        if (req.body.review !== undefined) {
            Object.assign(objs, { 'review.$.reviews': req.body.review });
        }
        if (req.body.review_comment !== undefined && req.body.review_comment.length !== 0) {
            Object.assign(objs, { 'review.$.reviewer_comment': req.body.review_comment });
        }
        Object.assign(objs, { 'review.$.reviewer_timestamp': common_fun.timestampNow() });
        Object.assign(objs, { 'review.$.status': 'Reviewed' });
    } else if (req.body.reviewer_type === 'creator') {
        if (req.body.review !== undefined) {
            Object.assign(objs, { 'review.$.creater_feedback': req.body.review });
        }
        if (req.body.review_comment !== undefined && req.body.review_comment.length !== 0) {
            Object.assign(objs, { 'review.$.creater_comment': req.body.review_comment });
        }
        Object.assign(objs, { 'review.$.creater_timestamp': common_fun.timestampNow() });
        Object.assign(objs, { 'review.$.status': 'Done' });
    } else if (req.body.reviewer_type === 'dean') {
        if (req.body.review !== undefined) {
            Object.assign(objs, { 'dean.review': req.body.review });
        }
        if (req.body.review_comment !== undefined && req.body.review_comment.length !== 0) {
            Object.assign(objs, { 'dean.comment': req.body.review_comment });
        }
        Object.assign(objs, { 'dean.timestamp': common_fun.timestampNow() });
        Object.assign(objs, { 'dean.status': 'Done' });
        object_id = { _id: id, isDeleted: false };
    }
    const doc = await base_control.update_condition(program_calendar, object_id, objs);
    updateProgramCalendarFlatCacheData();
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_YOU_REVIEW'),
                    req.t('UNABLE_TO_YOU_REVIEW'),
                ),
            );
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('REVIEW_ADDED_SUCCESSFULLY'),
                req.t('REVIEW_ADDED_SUCCESSFULLY'),
            ),
        );
};

exports.send_reviewer_notification = async (req, res) => {
    const staff_id = [...new Set(req.body.data.map((user_ids) => ObjectId(user_ids._reviewer_id)))];
    const user_check = await base_control.get_list(staff, {
        _id: { $in: staff_id },
        isDeleted: false,
    });
    if (!user_check.status || (user_check.status && user_check.data.length !== staff_id.length))
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('ERROR_USER_ID_NOT_MATCH'),
                    req.t('ERROR_USER_ID_NOT_MATCH'),
                ),
            );
    const objs = {
        'review.$[i].expire': {
            expire_date: req.body.expire_date,
            expire_time: req.body.expire_time,
        },
    };
    const filter = {
        arrayFilters: [
            {
                'i._reviewer_id': { $in: staff_id },
            },
        ],
    };
    const staff_data = await base_control.get_list(staff, { _id: { $in: staff_id } }, {});
    const doc = await base_control.update_condition_array_filter(
        program_calendar,
        {
            _id: ObjectId(req.body._calendar_id),
        },
        { $set: objs },
        filter,
    );
    updateProgramCalendarFlatCacheData();
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_SEND_NOTIFICATION'),
                    req.t('UNABLE_TO_SEND_NOTIFICATION'),
                ),
            );
    for (element of req.body.data) {
        const staff_details =
            staff_data.data[
                staff_data.data.findIndex(
                    (i) => i._id.toString() === element._reviewer_id.toString(),
                )
            ];
        const name = staff_details.name.middle
            ? staff_details.name.first +
              ' ' +
              staff_details.name.middle +
              ' ' +
              staff_details.name.last
            : staff_details.name.first + ' ' + staff_details.name.last;
        const messages =
            '<p> ' +
            req.t('DEAR') +
            ' ' +
            name +
            ',<br>' +
            common_fun.emailGreetingContent() +
            req.t(
                'INSTITUTION_HAS_ASSIGNED_YOU_AS_REVIEWER_FOR_PROGRAM_CALENDAR_SO_PLEASE_SHARE_YOU_REVIEW',
            ) +
            common_fun.emailRegardsContent() +
            '</p>';

        if (element.notify_via.indexOf('email') !== -1) {
            common_fun.send_email(staff_details.email, req.t('DIGISCHEDULER_ALERT'), messages);
        }
        if (element.notify_via.indexOf('sms') !== -1) {
            const sms_messages =
                req.t('DEAR') +
                ' ' +
                name +
                ',' +
                req.t(
                    'INSTITUTION_HAS_ASSIGNED_YOU_AS_REVIEWER_FOR_PROGRAM_CALENDAR_SO_PLEASE_SHARE_YOU_REVIEW',
                );
            common_fun.send_sms(staff_details.mobile, sms_messages);
        }
    }
    return res
        .status(200)
        .send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('REVIEW_REQUEST_SEND'),
                req.t('REVIEW_REQUEST_SEND'),
            ),
        );
};

exports.send_dean_reviewer_notification = async (req, res) => {
    let documents;
    let doc;
    switch (req.body.to) {
        case 'creator': {
            const aggre = [
                { $match: { _id: ObjectId(req.body._calendar_id) } },
                { $match: { isDeleted: false } },
                {
                    $lookup: {
                        from: constant.USER,
                        localField: 'creater._creater_id',
                        foreignField: '_id',
                        as: 'staffs',
                    },
                },
            ];
            documents = await base_control.get_aggregate(program_calendar, aggre);
            if (!documents.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                            req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        ),
                    );
            const stf = [];
            const stf_mail = documents.data[0].staffs[0].email;
            const stf_mobile = documents.data[0].staffs[0].mobile;
            stf.push(documents.data[0].staffs[0]._id);
            const messages = '<p>' + req.body.message + '</p>';
            if (req.body.notify_via.indexOf('email') !== -1) {
                common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
            }
            if (req.body.notify_via.indexOf('sms') !== -1) {
                const sms_messages = req.t('DIGISCHEDULER') + ' ' + req.body.message;
                common_fun.send_sms(stf_mobile, sms_messages);
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('YOUR_REVIEW_SENT_TO_VICE_DEAN_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        // break;
        case 'reviewer': {
            const aggrereviewer = [
                { $match: { _id: ObjectId(req.body._calendar_id) } },
                { $match: { isDeleted: false } },
                { $limit: 1 },
                {
                    $lookup: {
                        from: constant.USER,
                        localField: 'review._reviewer_id',
                        foreignField: '_id',
                        as: 'staffs',
                    },
                },
                { $addFields: { staff_ids: '$staffs._id' } },
            ];
            documents = await base_control.get_aggregate(program_calendar, aggrereviewer);
            if (!documents.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                            req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        ),
                    );
            documents.data[0].staffs.forEach(async (element) => {
                const stf_mail = element.email;
                const stf_mobile = element.mobile;
                const messages = '<p>' + req.body.message + '</p>';
                if (req.body.notify_via.indexOf('email') !== -1) {
                    common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
                }
                if (req.body.notify_via.indexOf('sms') !== -1) {
                    const sms_messages = req.t('DIGISCHEDULER') + ' ' + req.body.message;
                    common_fun.send_sms(stf_mobile, sms_messages);
                }
            });
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('YOUR_REVIEW_SENT_TO_VICE_DEAN_SUCCESSFULLY'),
                        req.t('YOUR_REVIEW_SENT_TO_VICE_DEAN_SUCCESSFULLY'),
                    ),
                );
        }
        case 'publish': {
            documents = await base_control.get_list(
                user,
                { status: 'completed', isDeleted: false, isActive: true },
                { _id: 1, name: 1, email: 1, mobile: 1 },
            );
            if (!documents.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('USER_NOT_FOUND'),
                            req.t('USER_NOT_FOUND'),
                        ),
                    );
            await base_control.update(program_calendar, ObjectId(req.body._calendar_id), {
                review: [],
                status: 'published',
            });
            updateProgramCalendarFlatCacheData();
            const stf_mail = documents.data
                .filter((userElement) => userElement.email !== null)
                .map((item) => item.email);
            const stf_mobile = documents.data
                .filter((userElement) => userElement.mobile && userElement.mobile !== null)
                .map((item) => item.mobile);
            const messages = '<p>' + req.body.message + '</p>';
            if (req.body.notify_via.indexOf('email') !== -1) {
                common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
            }
            if (req.body.notify_via.indexOf('sms') !== -1) {
                const sms_messages = req.t('DIGISCHEDULER') + ' ' + req.body.message;
                common_fun.send_sms(stf_mobile, sms_messages);
            }
            // const staff_ids = [];
            // documents.data.forEach(async (element) => {
            //     staff_ids.push(element._id);
            //     const stf_mail = element.email;
            //     const stf_mobile = element.mobile;
            //     const messages = '<p>' + req.body.message + '</p>';
            //     if (req.body.notify_via.indexOf('email') !== -1) {
            //         common_fun.send_email(stf_mail, 'DigiScheduler Alert', messages);
            //     }
            //     if (req.body.notify_via.indexOf('sms') !== -1 && stf_mobile) {
            //         const sms_messages = 'DigiScheduler ' + req.body.message;
            //         common_fun.send_sms(stf_mobile, sms_messages);
            //     }
            // });
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('PROGRAM_CALENDAR_PUBLISHED_SUCCESSFULLY'),
                        req.t('PROGRAM_CALENDAR_PUBLISHED_SUCCESSFULLY'),
                    ),
                );
        }
        case 'dean': {
            const aggre = [
                { $match: { _id: ObjectId(req.body._calendar_id) } },
                { $match: { isDeleted: false } },
                {
                    $lookup: {
                        from: constant.USER,
                        localField: 'dean._dean_id',
                        foreignField: '_id',
                        as: 'staffs',
                    },
                },
            ];
            documents = await base_control.get_aggregate(program_calendar, aggre);
            if (!documents.status)
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            req.t('DEAN_NOT_FOUND'),
                            req.t('DEAN_NOT_FOUND'),
                        ),
                    );
            let dean_status = { 'dean.status': 'pending' };
            if (documents.data[0].dean.review === undefined || documents.data[0].dean.review) {
                dean_status = { 'dean.status': 'pending' };
            } else {
                dean_status = { 'dean.status': 're-pending' };
            }
            await base_control.update(
                program_calendar,
                ObjectId(req.body._calendar_id),
                dean_status,
            );
            updateProgramCalendarFlatCacheData();
            const stf = [];
            const staffDetails =
                documents.data[0].staffs && documents.data[0].staffs.length
                    ? documents.data[0].staffs[0]
                    : null;
            const stf_mail = staffDetails && staffDetails.email ? staffDetails.email : '';
            const stf_mobile = staffDetails && staffDetails.mobile ? staffDetails.mobile : '';
            if (staffDetails && staffDetails._id) {
                stf.push(staffDetails._id);
            }
            const messages = '<p>' + req.body.message + '</p>';
            if (req.body.notify_via.indexOf('email') !== -1) {
                common_fun.send_email(stf_mail, req.t('DIGISCHEDULER_ALERT'), messages);
            }
            if (req.body.notify_via.indexOf('sms') !== -1) {
                const sms_messages = req.t('DIGISCHEDULER') + ' ' + req.body.message;
                common_fun.send_sms(stf_mobile, sms_messages);
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('PROGRAM_CALENDAR_SENT_TO_DEAN_SUCCESSFULLY'),
                        req.t('PROGRAM_CALENDAR_SENT_TO_DEAN_SUCCESSFULLY'),
                    ),
                );
        }
        default:
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        true,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
    }
};

exports.list_reviewer = async (req, res) => {
    const doc = await base_control.get(
        program_calendar,
        { _id: ObjectId(req.params.id) },
        { 'review._reviewer_id': 1, 'review.reviewer_name': 1 },
    );
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_REVIEWER_DETAILS'),
            doc.data,
        );
    } else {
        common_files.com_response(
            res,
            410,
            false,
            req.t('ERROR_REVIEWER_NOT_FOUND'),
            req.t('ERROR_REVIEWER_NOT_FOUND'),
        );
    }
};

exports.list_review = async (req, res) => {
    const id = ObjectId(req.params.id);
    let query;
    let project;
    if (req.params.whom === 'creator') {
        query = { _id: id, isDeleted: false };
        project = { dean: 1, creater: 1, review: 1 };
    } else if (req.params.whom === 'reviewer') {
        query = { _id: id, isDeleted: false };
        project = { creater: 1, review: 1 };
    } else if (req.params.whom === 'dean') {
        query = { _id: id, isDeleted: false };
        project = { dean: 1 };
    }
    const doc = await base_control.get(program_calendar, query, project);
    if (!doc.status)
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_REVIEW_NOT_FOUND'),
            req.t('ERROR_REVIEW_NOT_FOUND'),
        );
    common_files.com_response(res, 200, true, req.t('PROGRAM_CALENDAR_REVIEW'), doc.data);
};

exports.list_approver_reviewer_review = async (req, res) => {
    const id = ObjectId(req.params.id);
    let institutionCalendarId;
    let query;
    query = { calendar_type: 'primary', isDeleted: false, status: 'published' };
    const project = { _id: 1, calendar_name: 1 };
    const sort = { updatedAt: -1 };
    if (req.query.institutionCalendarId)
        institutionCalendarId = ObjectId(req.query.institutionCalendarId);
    else {
        const ic_doc = await base_control.get_sort_limt(
            institution_calendar,
            query,
            project,
            sort,
            1,
        );
        institutionCalendarId = ObjectId(ic_doc.data[0]._id);
    }
    if (req.params.whom === 'reviewer') {
        query = [
            { $match: { _institution_calendar_id: institutionCalendarId, isDeleted: false } },
            { $match: { 'review._reviewer_id': id } },
            {
                $lookup: {
                    from: constant.DIGI_PROGRAM,
                    localField: '_program_id',
                    foreignField: '_id',
                    as: 'program',
                },
            },
            { $project: { creater: 1, review: 1, 'program._id': 1, 'program.name': 1 } },
            { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
            {
                $addFields: {
                    // 'program.name': {
                    //     $substr: [
                    //         '$program.name',
                    //         0,
                    //         {
                    //             $subtract: [
                    //                 {
                    //                     $strLenCP: '$program.name',
                    //                 },
                    //                 4,
                    //             ],
                    //         },
                    //     ],
                    // },
                    'program.reviewer': '$review',
                    'program.creater': '$creater',
                },
            },
            { $project: { program: 1 } },
        ];
    } else if (req.params.whom === 'dean') {
        query = [
            { $match: { _institution_calendar_id: institutionCalendarId, isDeleted: false } },
            { $match: { 'dean._dean_id': id } },
            { $match: { 'dean.status': { $ne: 'waiting' } } },
            {
                $lookup: {
                    from: constant.DIGI_PROGRAM,
                    localField: '_program_id',
                    foreignField: '_id',
                    as: 'program',
                },
            },
            { $project: { dean: 1, 'program._id': 1, 'program.name': 1 } },
            { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
            {
                $addFields: {
                    // 'program.name': {
                    //     $substr: [
                    //         '$program.name',
                    //         0,
                    //         {
                    //             $subtract: [
                    //                 {
                    //                     $strLenCP: '$program.name',
                    //                 },
                    //                 4,
                    //             ],
                    //         },
                    //     ],
                    // },
                    'program.dean': '$dean',
                },
            },
            { $project: { program: 1 } },
        ];
    }
    const doc = await base_control.get_aggregate(program_calendar, query);
    // let doc = await base_control.get(program_calendar, query, project);
    // return res.send(doc);
    if (doc.status) {
        const review_data = [];
        const programs = [];
        let creator = {};
        await doc.data.forEach((element) => {
            if (
                element.program.name &&
                programs
                    .map(function (item) {
                        return item.program_name;
                    })
                    .indexOf(element.program.name) === -1
            ) {
                programs.push({
                    program_id: element.program._id,
                    program_name: element.program.name,
                });
                if (req.params.whom === 'dean') {
                    review_data.push({
                        _id: element._id,
                        program_id: element.program._id,
                        dean: element.program.dean,
                    });
                } else {
                    review_data.push({
                        _id: element._id,
                        program_id: element.program._id,
                        reviewer: element.program.reviewer,
                    });
                    creator = element.program.creater;
                }
                console.log(review_data);
            }
        });
        const res_data = {
            programs,
            creator,
            review: review_data,
        };
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_REVIEWS'),
            res_data /* institution_formate.institution_calendar_event_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR_REVIEWERS_NOT_FOUND'), doc.data);
    }
};

exports.staff_student_calendar_view = async (req, res) => {
    try {
        let query = { calendar_type: 'primary', isDeleted: false, status: 'published' };
        const project = { _id: 1, calendar_name: 1, start_date: 1, end_date: 1, isActive: 1 };
        const sort = { _id: -1 };
        const ic_doc = await base_control.get_list_sort(institution_calendar, query, project, sort);
        // console.log(ic_doc);
        // if (!ic_doc.status)
        //     return res
        //         .status(410)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 410,
        //                 false,
        //                 'No Institution calendar review',
        //                 'No Institution calendar review',
        //             ),
        //         );
        const activeInstitutionCalendarIds = ic_doc.data
            .filter((institutionCalendarElement) => institutionCalendarElement.isActive === true)
            .map((institutionCalendarElement) => institutionCalendarElement._id);
        query = [
            {
                $match: {
                    _institution_calendar_id: { $in: activeInstitutionCalendarIds },
                    isDeleted: false,
                },
            },
            {
                $lookup: {
                    from: 'digi_programs',
                    localField: '_program_id',
                    foreignField: '_id',
                    as: 'program',
                },
            },
            { $project: { dean: 1, 'program._id': 1, 'program.name': 1, status: 1 } },
            { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
            { $project: { program: 1, status: 1 } },
        ];
        const doc = await base_control.get_aggregate(program_calendar, query);
        // let program_no = null;
        // const program_no_ara = [];
        // const program_cri = [];
        // const program_data = await base_control.get_list_sort(
        //     program,
        //     { isDeleted: false },
        //     {},
        //     { name: -1 },
        // );
        const new_program = await base_control.get_list_sort(
            digi_program,
            { isDeleted: false },
            { name: 1, program_type: 1 },
            { name: -1 },
        );
        // program_data.data.forEach((main_element) => {
        //     program_no = main_element.no;
        //     program_data.data.forEach((element) => {
        //         if (program_no_ara.indexOf(program_no) === -1 && program_no === element.no) {
        //             program_cri.push({
        //                 program_id: element._id,
        //                 name: element.name.substring(0, element.name.length - 4),
        //             });
        //             program_no_ara.push(element.no);
        //         }
        //     });
        // });
        const pc_status = [];
        // if (!doc.status)
        //     return res
        //         .status(410)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 410,
        //                 false,
        //                 'No Institution calendar review',
        //                 'No Institution calendar review',
        //             ),
        //         );
        // for (element of program_cri) {
        //     const locs = doc.data.findIndex(
        //         (i) => i.program._id.toString() === element.program_id.toString(),
        //     );
        //     if (locs === -1) {
        //         pc_status.push({
        //             program_id: element.program_id,
        //             program_name: element.name,
        //             status: false,
        //             program_calendar_id: '',
        //         });
        //     } else {
        //         if (doc.data[locs].status && doc.data[locs].status === 'published') {
        //             pc_status.push({
        //                 program_id: element.program_id,
        //                 program_name: element.name,
        //                 status: true,
        //                 program_calendar_id: doc.data[locs]._id,
        //             });
        //         } else {
        //             pc_status.push({
        //                 program_id: element.program_id,
        //                 program_name: element.name,
        //                 status: false,
        //                 program_calendar_id: '',
        //             });
        //         }
        //     }
        // }
        if (new_program.status) {
            for (element of new_program.data) {
                const locs = doc.data.findIndex(
                    (i) => i.program && i.program._id.toString() === element._id.toString(),
                );
                if (locs === -1) {
                    pc_status.push({
                        program_id: element._id,
                        program_name: element.name,
                        program_type: element.program_type,
                        status: false,
                        program_calendar_id: '',
                    });
                } else {
                    if (doc.data[locs].status && doc.data[locs].status === 'published') {
                        pc_status.push({
                            program_id: element._id,
                            program_name: element.name,
                            program_type: element.program_type,
                            status: true,
                            program_calendar_id: doc.data[locs]._id,
                        });
                    } else {
                        pc_status.push({
                            program_id: element._id,
                            program_name: element.name,
                            program_type: element.program_type,
                            status: false,
                            program_calendar_id: '',
                        });
                    }
                }
                // pc_status.push({ program_id: element._id, program_name: element.name, status: false, program_calendar_id: "" });
            }
        }
        const res_data = {
            institution_calendar: ic_doc.status ? ic_doc.data : [],
            programs: pc_status,
        };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_CALENDAR_REVIEWS'),
                    res_data /* institution_formate.institution_calendar_event_ID(doc.data[0]) */,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};
