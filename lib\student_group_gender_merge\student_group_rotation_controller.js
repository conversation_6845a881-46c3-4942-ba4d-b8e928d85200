const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const institution = require('mongoose').model(constant.INSTITUTION);
// const program = require('mongoose').model(constant.PROGRAM);
// const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
const {
    removeStudentSchedule,
    updateStudentGroupFlatCacheData,
} = require('./student_group_services');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
exports.rotation_group_setting = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const student_group_data = await base_control.get(
            student_group,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                _id: ObjectId(req.body._id),
                isDeleted: false,
            },
            {},
        );
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_STUDENT_GROUP'),
                        req.t('UNABLE_TO_FIND_STUDENT_GROUP'),
                    ),
                );
        const group_ind = student_group_data.data.groups.findIndex(
            (i) => i.level === req.body.level,
        );
        if (group_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const group_name = student_group_data.data.groups[group_ind].group_name;
        const groups_setting = [];
        req.body.groups.forEach((element) => {
            let name = `${group_name}-SG`;
            let g_type;
            if (element.gender === constant.GENDER.MALE) {
                name = `${group_name}-M-RG`;
                g_type = 'specific';
            } else if (element.gender === constant.GENDER.FEMALE) {
                name = `${group_name}-F-RG`;
                g_type = 'specific';
            } else if (element.gender === constant.GENDER.BOTH) {
                name = `${group_name}-RSG`;
                g_type = 'mixed';
            }
            const gs_obj = {
                gender_type: g_type,
                gender: element.gender,
                group_no: element.group_no,
                no_of_student: element.no_of_students,
                group_name: name + element.group_no.toString(),
                _student_ids: [],
            };
            groups_setting.push(gs_obj);
        });
        let objects = { $set: { 'groups.$[i].rotation_group_setting': groups_setting } };
        let filter = {
            arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
        };
        const obj = await base_control.update_condition_array_filter(
            student_group,
            { _id: ObjectId(req.body._id), isDeleted: false },
            objects,
            filter,
        );
        const courses = student_group_data.data.groups[group_ind].courses;
        for (element of courses) {
            objects = {
                $set: { 'groups.$[i].courses.$[j].setting': [] },
            };
            filter = {
                arrayFilters: [
                    { 'i.term': req.body.batch, 'i.level': req.body.level },
                    { 'j._course_id': element._course_id },
                ],
            };
            await base_control.update_condition_array_filter(
                student_group,
                { _id: ObjectId(req.body._id), isDeleted: false },
                objects,
                filter,
            );
        }
        if (!obj.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_SET_SETTING_PLS_RETRY'),
                        obj.data,
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        res.status(200).send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('STUDENTS_GROUP_SETTING_IS_CREATED'),
                req.t('STUDENTS_GROUP_SETTING_IS_CREATED'),
            ),
        );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_group_list_filter = async (req, res) => {
    // let skips = Number(req.query.limit * (req.query.pageNo - 1));
    // let limits = Number(req.query.limit) || 0;
    const query = {
        _id: ObjectId(req.params.id),
        'master.term': req.params.batch,
        isDeleted: false,
    };
    const project = {};
    // let doc = await base_control.list(student_group, limits, skips, query, project);
    const doc = await base_control.get(student_group, query, project);
    if (doc.status) {
        const objs = {
            _id: doc.data._id,
            _institution_calendar_id: doc.data._institution_calendar_id,
            group_name: doc.data.group_name,
            master: doc.data.master,
        };
        const students = [];
        let ung_count = 0;
        const group_student_list = [];
        let all_student_id = [];
        doc.data.ungrouped.forEach((element) => {
            const std_data =
                doc.data.students[
                    doc.data.students.findIndex(
                        (i) => i._student_id.toString() === element.toString(),
                    )
                ];
            if (std_data.gender === req.params.gender) {
                ung_count++;
                all_std++;
                all_student_id.push(element);
            }
        });
        group_student_list.push({ title: 'ungrouped', students_id: all_student_id });
        const g_list = { ungrouped: ung_count };
        doc.data.rotation_group_setting.forEach((element) => {
            if (element.gender === req.params.gender) {
                // element.groups.forEach(sub_element => {
                const gc = 'rotation' + element.group_no;
                g_list[gc] = element._student_ids.length;
                all_student_id = all_student_id.concat(element._student_ids);
                group_student_list.push({ title: gc, students_id: element._student_ids });
                // });
            }
        });
        group_student_list.push({ title: 'all', students_id: all_student_id });
        g_list.all = all_student_id.length;
        Object.assign(objs, { groups_list: g_list });
        const group_data =
            group_student_list[group_student_list.findIndex((i) => i.title === req.params.group)];
        if (group_data !== undefined) {
            group_data.students_id.forEach((element) => {
                students.push(
                    doc.data.students[
                        doc.data.students.findIndex(
                            (i) => i._student_id.toString() === element.toString(),
                        )
                    ],
                );
            });
            Object.assign(objs, { students });
            common_files.com_response(res, 200, true, req.t('STUDENT_GROUP_DATA'), objs);
        } else {
            common_files.com_response(
                res,
                404,
                false,
                req.t('PLS_CHECK_GROUP_NAME_OR_GENDER_OR_TERM'),
                req.t('PLS_CHECK_GROUP_NAME_OR_GENDER_OR_TERM'),
            );
        }
    } else {
        common_files.com_response(
            res,
            404,
            false,
            req.t('ERROR_UNABLE_TO_GET_LIST'),
            req.t('UNABLE_TO_GET_LIST'),
        );
    }
};

exports.rotation_setting_get = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const student_group_data = await base_control.get(
            student_group,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                _id: ObjectId(req.params._id),
                isDeleted: false,
            },
            {},
        );
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_STUDENT_GROUP'),
                        req.t('UNABLE_TO_FIND_STUDENT_GROUP'),
                    ),
                );
        const group_loc = student_group_data.data.groups.findIndex(
            (i) => i.term === req.params.term && i.level === req.params.level,
        );
        if (group_loc === -1)
            return res
                .status(400)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        400,
                        false,
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                    ),
                );
        let male_count = 0;
        let female_count = 0;
        const objs = {
            _id: student_group_data.data.groups[group_loc]._id,
            master: student_group_data.data.groups[group_loc].master,
            group_name: student_group_data.data.groups[group_loc].group_name,
            total: student_group_data.data.groups[group_loc].ungrouped.length,
        };
        student_group_data.data.groups[group_loc].ungrouped.forEach((element) => {
            const std_data =
                student_group_data.data.groups[group_loc].students[
                    student_group_data.data.groups[group_loc].students.findIndex(
                        (i) => i._student_id.toString() === element.toString(),
                    )
                ];
            if (std_data.gender === constant.GENDER.MALE) {
                male_count++;
            } else {
                female_count++;
            }
        });
        Object.assign(objs, { male_count, female_count });
        Object.assign(objs, {
            rotation_count: student_group_data.data.groups[group_loc].rotation_count,
        });
        const setting = [];
        const grp_no = [];
        let mode = 'specific';
        let grouped_status = false;
        student_group_data.data.groups[group_loc].rotation_group_setting.forEach((element) => {
            if (grp_no.indexOf(element.group_no) === -1) {
                grp_no.push(element.group_no);
            } else {
                mode = 'segregation';
            }
            setting.push({
                gender: element.gender,
                group_no: element.group_no,
                no_of_student: element.no_of_student,
                group_name: element.group_name,
            });
            if (!grouped_status) grouped_status = element._student_ids.length !== 0;
        });
        Object.assign(objs, { mode, setting, grouped_status });
        res.status(200).send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('STUDENTS_GROUP_SETTING_IS_CREATED'),
                objs,
            ),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_student_bulk_delete = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = { _id: ObjectId(req.body._id), isDeleted: false };
    const project = {};
    const student_group_data = await base_control.get(student_group, query, project);
    if (!student_group_data.status)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('ERROR_ID_NOT_MATCH'),
                    req.t('CHECK_PARSING_REFERENCE_ID'),
                ),
            );
    const student_group_row = student_group_data.data.groups.findIndex(
        (i) => i.term === req.body.batch && i.level.toString() === req.body.level.toString(),
    );
    if (student_group_row === -1)
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    404,
                    false,
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                ),
            );
    const group_course = student_group_data.data.groups[student_group_row].courses;
    let doc;
    doc = await base_control.update_condition_array_filter(
        student_group,
        query,
        {
            $pull: {
                'groups.$[i].students': { _student_id: { $in: req.body._student_ids } },
                'groups.$[i].ungrouped': { $in: req.body._student_ids },
            },
        },
        {
            arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
        },
    );
    const group_setting = student_group_data.data.groups[
        student_group_row
    ].rotation_group_setting.filter((i) =>
        req.query.mode
            ? req.query.mode === i.gender
            : i.gender.toString() === req.body.gender.toString(),
    );
    const group = [];
    for (let index = 0; index < group_setting.length; index++) {
        const element = group_setting[index].group_no;
        group.push(element);
    }
    for (sub_element of group) {
        const objs = {
            $pull: {
                'groups.$[i].students': { _student_id: { $in: req.body._student_ids } },
                'groups.$[i].ungrouped': { $in: req.body._student_ids },
                'groups.$[i].rotation_group_setting.$[k]._student_ids': {
                    $in: req.body._student_ids,
                },
            },
        };
        const filter = {
            arrayFilters: [
                { 'i.term': req.body.batch, 'i.level': req.body.level },
                {
                    'k.gender': req.query.mode ? req.query.mode : req.body.gender,
                    'k.group_no': sub_element,
                },
            ],
        };
        doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
    }

    // Need to remove based on course
    // return res.send(group_course);
    const course_data = [];
    let setting_data = [];
    let session_setting_data = [];
    let groups_data = [];
    let master_remove = [];
    let un_group = [];
    let student_ids = [];
    for (course_element of group_course) {
        // console.log(course_element._removed_student_ids.filter(item => !remove.includes((item).toString())));
        master_remove = course_element._removed_student_ids.filter(
            (item) => !req.body._student_ids.includes(item.toString()),
        );
        setting_data = [];
        for (course_setting_element of course_element.setting) {
            session_setting_data = [];
            for (course_session_setting of course_setting_element.session_setting) {
                groups_data = [];
                for (course_groups of course_session_setting.groups) {
                    student_ids = course_groups._student_ids.filter(
                        (item) => !req.body._student_ids.includes(item.toString()),
                    );
                    groups_data.push({
                        _student_ids: student_ids,
                        _id: course_groups._id,
                        group_no: course_groups.group_no,
                        group_name: course_groups.group_name,
                    });
                }
                session_setting_data.push({
                    _id: course_session_setting._id,
                    group_name: course_session_setting.group_name,
                    session_type: course_session_setting.session_type,
                    no_of_group: course_session_setting.no_of_group,
                    no_of_student: course_session_setting.no_of_student,
                    groups: groups_data,
                });
            }
            un_group = course_setting_element.ungrouped.filter(
                (item) => !req.body._student_ids.includes(item.toString()),
            );
            setting_data.push({
                ungrouped: un_group,
                _id: course_setting_element._id,
                _group_no: course_setting_element._group_no,
                gender: course_setting_element.gender,
                session_setting: session_setting_data,
            });
        }
        course_data.push({
            _removed_student_ids: master_remove,
            _id: course_element._id,
            _course_id: course_element._course_id,
            course_name: course_element.course_name,
            course_no: course_element.course_no,
            course_type: course_element.course_type,
            session_types: course_element.session_types,
            setting: setting_data,
        });
    }
    const objs = {
        $set: {
            'groups.$[i].courses': course_data,
        },
    };
    const filter = {
        arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
    };
    doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);

    /* for (let index = 0; index < group_course.length; index++) {
        const ids = group_course[index]._course_id
        id.push(ids)
        const setting = group_course[index].setting
        for (let j = 0; j < setting.length; j++) {
            const element = setting[j]._group_no;
            grp_no.push(element)
        }
    }
    for (elements of id) {
        let group_course_row = student_group_data.data.groups[student_group_row].courses.findIndex(i => i._course_id === elements);
        if (group_course_row !== -1) {
            let group_course_session = student_group_data.data.groups[student_group_row].courses[group_course_row].session_types;
            for (sub_element of grp_no) {
                let group_course_setting = student_group_data.data.groups[student_group_row].courses[group_course_row].setting.findIndex(i => (i.gender === req.body.gender && i._group_no === sub_element));
                if (group_course_setting !== -1) {
                    let session_settings = student_group_data.data.groups[student_group_row].courses[group_course_row].setting[group_course_setting].session_setting
                    for (ele of group_course_session) {
                        let session_setting = session_settings.findIndex(i => i.session_type === ele.symbol);
                        if (session_setting !== -1) {
                            let group = student_group_data.data.groups[student_group_row].courses[group_course_row].setting[group_course_setting].session_setting[session_setting].groups
                            for (sub_ele of group) {
                                let objs = {
                                    $pull: {
                                        'groups.$[i].courses.$[u]._removed_student_ids': { $in: req.body._student_ids },
                                        'groups.$[i].courses.$[u].setting.$[p].ungrouped': { $in: req.body._student_ids },
                                        'groups.$[i].courses.$[u].setting.$[p].session_setting.$[l].groups.$[m]._student_ids': { $in: req.body._student_ids }
                                    }
                                };
                                let filter = {
                                    arrayFilters: [
                                        { 'i.term': req.body.batch, 'i.level': req.body.level },
                                        { 'u._course_id': elements },
                                        { 'p.gender': req.body.gender, 'p._group_no': sub_element },
                                        { 'l.session_type': ele.symbol },
                                        { 'm.group_no': sub_ele.group_no }
                                    ]
                                }
                                doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
                            }
                        }
                    }
                }
            }
        }
    } */
    if (!doc.status)
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_REMOVE_STUDENT'),
                    doc.data,
                ),
            );
    await removeStudentSchedule(
        student_group_data.data._institution_id,
        student_group_data.data._institution_calendar_id,
        req.body.batch,
        req.body.level,
        undefined,
        req.body._student_ids,
    );
    updateStudentGroupFlatCacheData();
    await updateStudentGroupRedisKey({
        courseId: req.body._course_id,
        level: req.body.level,
        batch: req.body.batch,
    });
    return res
        .status(200)
        .send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('SUCCESSFULLY_REMOVED_STUDENT_MANUALLY'),
                req.t('SUCCESSFULLY_REMOVED_STUDENT_MANUALLY'),
            ),
        );
};

exports.rotation_get_student = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            user_id: req.params.academic_no,
            user_type: constant.EVENT_WHOM.STUDENT,
            isDeleted: false,
            isActive: true,
        };
        const project = {};
        const user_data = await base_control.get(user, query, project);
        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    ),
                );

        const query_stud_group = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: req.params._id,
            isDeleted: false,
            isActive: true,
        };
        const student_group_data = await base_control.get_list(student_group, query_stud_group, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );

        const group_ind = student_group_data.data[0].groups.findIndex(
            (ele) => ele.level === req.params.level && ele.term === req.params.batch,
        );
        if (group_ind === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const rot_settings = student_group_data.data[0].groups[group_ind].rotation_group_setting;
        const ungrouped_student_arr = student_group_data.data[0].groups[group_ind].ungrouped;
        const student_arr = [];
        for (let i = 0; i < rot_settings.length; i++) {
            //student_arr = student_arr.concat(rot_settings[i]._student_ids);
            student_arr.push({
                student_ids: rot_settings[i]._student_ids,
                group_no: rot_settings[i].group_no,
                group_name: rot_settings[i].group_name,
            });
        }
        //Grouped check
        let ind = -1;
        let group_no = '';
        let group_name = '';
        for (let i = 0; i < student_arr.length; i++) {
            ind = student_arr[i].student_ids.findIndex((ele) => ele.equals(user_data.data._id));
            if (ind !== -1) {
                group_no = student_arr[i].group_no;
                group_name = student_arr[i].group_name;
                break;
            }
        }
        const student_details = [
            {
                name: user_data.data.name,
                gender: user_data.data.gender,
                enrolled_program: user_data.data.programs,
                group_no,
                group_name,
                _id: user_data.data._id,
            },
        ];

        //Un grouped check
        const ungrouped_ind = ungrouped_student_arr.findIndex((ele) =>
            ele.equals(user_data.data._id),
        );
        if (ungrouped_ind !== -1)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        true,
                        req.t('STUDENT_EXIST_IN_UNGROUPED'),
                        student_details,
                    ),
                );

        if (student_arr.length > 0) {
            if (ind !== -1)
                return res
                    .status(409)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            409,
                            true,
                            req.t('STUDENT_IS_ALREADY_EXIST_IN_THIS_GROUP'),
                            student_details,
                        ),
                    );
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('YOU_CAN_ADD_STUDENT_TO_THIS_GROUP'),
                        student_details,
                    ),
                );
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('YOU_CAN_ADD_STUDENT_TO_THIS_GROUP'),
                    student_details,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.rotation_add_student = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
        };
        const student_group_check = await base_control.get(student_group, query, {});
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        const user_checks = await base_control.get_list(
            user,
            { _id: { $in: [req.body._user_id, req.body._student_id] }, isDeleted: false },
            {},
        );
        if (!user_checks.status && user_checks.data.length !== 2)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        const student_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._student_id.toString(),
        );
        const staff_loc = user_checks.data.findIndex(
            (i) => i._id.toString() === req.body._user_id.toString(),
        );
        if (student_loc === -1 || staff_loc === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                        req.t('ERROR_STUDENT_ACADEMIC_NO_NOT_MATCH'),
                    ),
                );
        const level_pos = student_group_check.data.groups.findIndex(
            (i) => i.level === req.body.level && i.term === req.body.batch,
        );
        if (level_pos === -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_status = [];
        const courses = student_group_check.data.groups[level_pos].courses.map((i) => i._course_id);
        for (cro of courses) {
            course_status.push({ _course_id: cro, status: 'pending' });
        }
        const objs = {
            $push: {
                'groups.$[i].students': {
                    _student_id: user_checks.data[student_loc]._id,
                    academic_no: user_checks.data[student_loc].user_id,
                    name: user_checks.data[student_loc].name,
                    gender: user_checks.data[student_loc].gender,
                    mark: req.body.mark,
                    imported_on: common_fun.timestampNow(),
                    _imported_by: req.body._user_id,
                    imported_by: user_checks.data[staff_loc].name,
                    course_group_status: course_status,
                },
                'groups.$[i].ungrouped': user_checks.data[student_loc]._id,
            },
        };
        const filter = {
            arrayFilters: [{ 'i.term': req.body.batch, 'i.level': req.body.level }],
        };
        const doc = await base_control.update_condition_array_filter(
            student_group,
            query,
            objs,
            filter,
        );
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UPDATING'),
                        req.t('ERROR'),
                    ),
                );
        updateStudentGroupFlatCacheData();
        await updateStudentGroupRedisKey({
            courseId: req.body._course_id,
            level: req.body.level,
            batch: req.body.batch,
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                    req.t('SUCCESSFULLY_ADDED_STUDENT_MANUALLY'),
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
