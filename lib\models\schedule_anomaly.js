const mongoose = require('mongoose');
const { Schema } = mongoose;

const {
    INSTITUTION,
    COURSE_SCHEDULE,
    USER,
    SCHEDULE_ANOMALIES,
    // RETAKE_ALL,
    // RETAKE_ABSENT,
    // BUZZER,
    // SURPRISE_QUIZ,
    // PRIMARY,
} = require('../utility/constants');

const scheduleAttendance = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        scheduleId: {
            type: Schema.Types.ObjectId,
            ref: COURSE_SCHEDULE,
        },
        // modeBy: {
        //     type: String,
        //     enum: [PRIMARY, RETAKE_ALL, RETAKE_ABSENT, BUZZER, SURPRISE_QUIZ],
        // },
        studentId: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        faceData: { type: String },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        pure: { type: Boolean, default: false },
    },
    { timestamps: true },
);
module.exports = mongoose.model(SCHEDULE_ANOMALIES, scheduleAttendance);
