const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const {
    PROGRAM_REPORT_SETTINGS,
    INSTITUTION,
    DIGI_PROGRAM,
    DIGI_COURSE,
} = require('../utility/constants');

const programReportSettings = new Schema(
    {
        _program_id: { type: Schema.Types.ObjectId, ref: DIGI_PROGRAM, required: true },
        _institution_id: { type: Schema.Types.ObjectId, ref: INSTITUTION, required: true },
        selfEvaluationSurvey: {
            isChecked: { type: Boolean },
            limit: {
                from: { type: Number },
                to: { type: Number },
            },
            scalePoints: [
                {
                    sNo: { type: Number },
                    scalePointSurveyValue: { type: String },
                    description: { type: String },
                    colorCode: { type: String },
                },
            ],
            benchMarks: [
                {
                    name: { type: String },
                    shortName: { type: String },
                    maxValue: { type: Number },
                    colorCode: { type: String },
                    module: { type: String },
                },
            ],
            scalePointStatus: { type: String, enum: ['draft', 'saved'] },
            benchMarkStatus: { type: String, enum: ['draft', 'saved'] },
            advance: {
                getWrittenFeedbackFromStudent: { type: Boolean, default: false },
            },
        },
        sessionExperienceSurvey: {
            isChecked: { type: Boolean },
            limit: {
                from: { type: Number },
                to: { type: Number },
            },
            separateRatingScaleValueForEachQuestion: {
                type: Boolean,
            },
            noOfQuestion: { type: Number },
            scalePoints: [
                {
                    sNo: { type: Number },
                    scalePointSurveyValue: { type: String },
                    description: { type: String },
                    colorCode: { type: String },
                },
            ],
            questions: [
                {
                    content: { type: String },
                    scalePoints: [
                        {
                            sNo: { type: Number },
                            scalePointSurveyValue: { type: String },
                            description: { type: String },
                            colorCode: { type: String },
                        },
                    ],
                },
            ],
            benchMarks: [
                {
                    name: { type: String },
                    shortName: { type: String },
                    maxValue: { type: Number },
                    colorCode: { type: String },
                    module: { type: String },
                },
            ],
            scalePointStatus: { type: String, enum: ['draft', 'saved'] },
            benchMarkStatus: { type: String, enum: ['draft', 'saved'] },
            advance: {
                getWrittenFeedbackFromStudent: { type: Boolean, default: false },
                allowStudentsToRateAnyTime: { type: Boolean, default: false },
            },
        },
        activity: {
            isChecked: { type: Boolean },
            benchMarks: [
                {
                    name: { type: String },
                    shortName: { type: String },
                    maxValue: { type: String },
                    colorCode: { type: String },
                    module: { type: String },
                },
            ],
            benchMarkStatus: { type: String, enum: ['draft', 'saved'] },
        },
        courseAdminUserPermission: { type: Boolean },
        rattingScaleForSurvey: { type: String, enum: ['common', 'independent'] },
        benchMarkForSurveyAndActivities: { type: String, enum: ['common', 'independent'] },
        scalePoints: [
            {
                sNo: { type: Number },
                scalePointSurveyValue: { type: String },
                description: { type: String },
                colorCode: { type: String },
            },
        ],
        step: { type: Number },
        noOfQuestion: { type: Number },
        questions: [{ content: { type: String } }],
        status: { type: String, enum: ['draft', 'saved'] },
        benchMarkValue: { type: String, enum: ['points', 'percentage'] },
        benchMarks: [
            {
                name: { type: String },
                shortName: { type: String },
                maxValue: { type: Number },
                colorCode: { type: String },
                module: { type: String },
            },
        ],
        scalePointStatus: { type: String, enum: ['draft', 'saved'] },
        benchMarkStatus: { type: String, enum: ['draft', 'saved'] },
        courses: [
            {
                courseId: {
                    type: Schema.Types.ObjectId,
                    ref: DIGI_COURSE,
                },
                year: { type: String },
                level: { type: String },
                term: { type: String },
                rotation: { type: String },
                rotationCount: { type: Number },
                selfEvaluationSurvey: { type: Boolean, default: true },
                sessionExperienceSurvey: { type: Boolean, default: true },
            },
        ],
    },
    { timestamps: true },
);
module.exports = mongoose.model(PROGRAM_REPORT_SETTINGS, programReportSettings);
