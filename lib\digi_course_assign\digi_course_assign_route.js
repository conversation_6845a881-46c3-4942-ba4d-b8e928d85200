const express = require('express');
const route = express.Router();
const digi_course_assign = require('./digi_course_assign_controller');
const validator = require('./digi_course_assign_validator');


route.post('/', digi_course_assign.insert);
route.put('/update_session_flow', digi_course_assign.update_session_flow);
route.put('/:id', digi_course_assign.update);
//route.get('/:program_id/:curriculum_id/:year_id/:level_id', digi_course_assign.course_assign_list);
route.get('/group_data/:program_id/:curriculum_id/:year_id/:level_id', digi_course_assign.list_course_assign_group_data);
route.get('/session_flow_details/:course_id', digi_course_assign.get_session_flow_details_by_course_id);
route.delete('/:id', digi_course_assign.delete_course_assign);


module.exports = route;