const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    createParameter,
    getParameters,
    editParameters,
    saveLeaderBoard,
    deleteParameters,
    leaderDashboard,
    leaderDashboardFilter,
    singleUserDashboard,
} = require('./leader_board.controller');
const {
    createParameterValidator,
    getParametersValidator,
    editParametersValidator,
    deleteParametersValidator,
} = require('./leader_board.validator');
router.post('/createParameter', createParameterValidator, catchAsync(createParameter));
router.get('/getParameters', getParametersValidator, catchAsync(getParameters));
router.put('/editParameters', editParametersValidator, catchAsync(editParameters));
router.post('/saveLeaderBoard', catchAsync(saveLeaderBoard));
router.put('/deleteParameters', deleteParametersValidator, catchAsync(deleteParameters));
router.get('/leaderDashboard', catchAsync(leaderDashboard));
router.get('/leaderDashboardFilter', catchAsync(leaderDashboardFilter));
router.get('/singleUserDashboard', catchAsync(singleUserDashboard));
module.exports = router;
