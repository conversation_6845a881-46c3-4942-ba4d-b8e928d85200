const mongoose = require('mongoose');
const { Schema } = mongoose;

const {
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    INSTITUTION,
    COURSE_SCHEDULE,
    USER,
    RET<PERSON>KE_ALL,
    RETAKE_ABSENT,
    BUZZER,
    SURPRISE_QUIZ,
    SCHEDULE_ATTENDANCE,
    PRESENT,
    ABSENT,
    PENDING,
    RUNNING,
    COMPLETED,
    MANUAL,
} = require('../utility/constants');

const scheduleAttendance = new Schema(
    {
        updateTime: { type: Date },
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        scheduleId: [
            {
                type: Schema.Types.ObjectId,
                ref: COURSE_SCHEDULE,
            },
        ],
        _staff_id: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        modeBy: {
            type: String,
            enum: [RETAKE_ALL, RETAKE_ABSENT, BUZ<PERSON><PERSON>, SURPRISE_QUIZ, MANU<PERSON>],
        },
        status: {
            type: String,
            enum: [RUNNING, COMPLETED],
        },
        students: [
            {
                _student_id: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                status: {
                    type: String,
                    enum: [PRESENT, ABSENT, ONDUTY, LEAVE, PERMISSION],
                    default: PENDING,
                },
                time: Date,
            },
        ],
        quizDetails: {
            // commenting because, we are not storing student answer details in to DB
            /*  studentsResponse: [
                {
                    _student_id: {
                        type: Schema.Types.ObjectId,
                        ref: USER,
                    },
                    questions: [
                        {
                            _question_id: {
                                type: Schema.Types.ObjectId,
                            },
                            _option_id: {
                                type: Schema.Types.ObjectId,
                            },
                        },
                    ],
                },
            ], */
            quiz: {
                quizTitle: { type: String },
                questions: [
                    {
                        text: { type: String },
                        questionType: { type: String },
                        attachments: {
                            url: { type: String },
                            name: { type: String },
                        },
                        options: [
                            {
                                text: { type: String },
                                attachments: { url: { type: String }, name: { type: String } },
                                answer: { type: Boolean },
                            },
                        ],
                        order: { type: Number },
                    },
                ],
            },
        },
        isCompared: [
            {
                type: Schema.Types.ObjectId,
            },
        ],
        attendanceCondition: {
            type: String,
        },
        isLive: { type: Boolean, default: false },
        faceAuthentication: { type: Boolean, default: true },
    },
    { timestamps: true },
);
module.exports = mongoose.model(SCHEDULE_ATTENDANCE, scheduleAttendance);
