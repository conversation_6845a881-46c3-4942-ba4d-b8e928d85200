const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const curriculum = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_PROGRAM,
        },
        program_name: String,
        curriculum_name: String,
        programCode: String,
        curriculumCode: String,
        credit_hours: {
            hours_as: String,
            values_as: String,
            credit: [
                {
                    session_type: String,
                    min: Number,
                    max: Number,
                },
            ],
        },
        _framework_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_PROGRAM,
        },
        framework_name: String,
        start_at: Number,
        end_at: Number,
        // is_pre_requisite: Boolean,
        // _pre_requisite_id: {
        //     type: Schema.Types.ObjectId,
        //     ref: constant.PROGRAM
        // },
        // _pre_requisite_name: String,
        year_level: [
            {
                pre_requisite_name: String,
                _pre_requisite_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.DIGI_PROGRAM,
                },
                y_type: String,
                yearCode: { type: String },
                no_of_level: Number,
                levels: [
                    {
                        level_name: String,
                        levelCode: { type: String },
                        start_week: Number,
                        end_week: Number,
                    },
                ],
            },
        ],
        framework: {
            _id: {
                type: String,
                trim: true,
            },
            name: {
                type: String,
                trim: true,
            },
            code: {
                type: String,
                trim: true,
            },
            domains: [
                {
                    no: String,
                    name: String,
                    plo: [
                        {
                            no: String,
                            name: String,
                            clos: [
                                {
                                    clo_id: String,
                                    no: String,
                                    name: String,
                                    year_id: Schema.Types.ObjectId,
                                    year_name: String,
                                    level_id: Schema.Types.ObjectId,
                                    level_name: String,
                                    mapped_value: String,
                                    content_mapped_value: String,
                                },
                            ],
                            isDeleted: {
                                type: Boolean,
                                default: false,
                            },
                            isActive: {
                                type: Boolean,
                                default: true,
                            },
                        },
                    ],
                },
            ],
        },
        standard_range_settings: [
            {
                year_program: String,
                domain_id: Schema.Types.ObjectId,
                domain_name: String,
                year: Number,
                higher_limit: Number,
                lower_limit: Number,
            },
        ],
        mapping_type: String,
        content_mapping_type: String,
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.DIGI_CURRICULUM, curriculum);
