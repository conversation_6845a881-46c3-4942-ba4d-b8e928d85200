const mongoose = require('mongoose');
const { Schema } = mongoose;
const { INSTITUTION, VACCINATION } = require('../utility/constants');
const vaccination_schema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
            required: true,
        },
        vaccination_name: {
            type: String,
            trim: true,
        },
        no_of_dosses: {
            type: Number,
            trim: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(VACCINATION, vaccination_schema);
