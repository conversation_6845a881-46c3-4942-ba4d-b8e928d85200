const express = require('express');
const route = express.Router();
const { validate } = require('../../../middleware/validation');
const {
    pushStudentSchema,
    updateCourseSessionSchema,
    getCoursesParamSchema,
} = require('./course_admin_validate_schema');

const {
    course_session_flow_list,
    document_course_session_flow_list,
    courseSessionFlow,
    courseSessionFlowSlo,
    course,
    getCourseDocuments,
    getSessions,
    getSchedule,
    getScheduleByDate,
    getCourseOnly,
    getTodaySchedule,
    getStudentDocuments,
    updateFeedback,
    getDropdownSchedule,
    getCourseSession,
    getScheduleBySessionOrScheduleId,
    riyadhToUTC,
    getCourses,
} = require('./course_admin_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get(
    '/get-courses/:staffId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCoursesParamSchema,
    getCourses,
);
route.get(
    '/course_session_flow_list/:id',
    [userPolicyAuthentication([])],
    course_session_flow_list,
);
route.get('/course/:userId', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], course);
route.get('/getStudentDocuments/:studentId', [userPolicyAuthentication([])], getStudentDocuments);
route.get(
    '/course/:userId/:courseId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    course,
);
route.get('/documents/:courseId', [userPolicyAuthentication([])], getCourseDocuments);
route.get('/documents/session/:sessionId', [userPolicyAuthentication([])], getCourseDocuments);
route.get('/documents/:courseId/:sessionId', [userPolicyAuthentication([])], getCourseDocuments);
route.get('/get-schedules/:userId', [userPolicyAuthentication([])], getSessions);
route.get('/get-schedule/:scheduleId', [userPolicyAuthentication([])], getSchedule);
route.get(
    '/get-schedule-by-date/:userId/:date',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getScheduleByDate,
);
route.get(
    '/course-session-flow/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    courseSessionFlow,
);
route.get('/get-course-only/:staffId', [userPolicyAuthentication([])], getCourseOnly);
route.get('/get-today-schedule/:staffId', [userPolicyAuthentication([])], getTodaySchedule);
route.get(
    '/get-session-schedule/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getDropdownSchedule,
);
route.get(
    '/course-session-flow-slo/:courseId/:sessionId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    courseSessionFlowSlo,
);
route.get(
    '/document_course_session_flow_list',
    [userPolicyAuthentication([])],
    document_course_session_flow_list,
);
route.get('/riyadhToUTC', [userPolicyAuthentication([])], riyadhToUTC);
route.put(
    '/feedback/:studentId/:scheduleId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateCourseSessionSchema,
    updateFeedback,
);

route.get('/getCourseSession', [userPolicyAuthentication([])], getCourseSession);
route.get(
    '/getSchedules/:sessionOrScheduleId',
    [userPolicyAuthentication([])],
    getScheduleBySessionOrScheduleId,
);

module.exports = route;
