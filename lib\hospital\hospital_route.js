const express = require('express');
const route = express.Router();
const hospital = require('../hospital/hospital_controller');
const validater = require('./hospital_validator');
route.post('/list', hospital.list_values);
route.get('/locations', hospital.locations_list);
route.get('/:id', validater.hospital_id, hospital.list_id);
route.get('/', hospital.list);
route.post('/', validater.hospital, hospital.insert);
route.put('/:id', validater.hospital_id, validater.hospital, hospital.update);
route.delete('/:id', validater.hospital_id, hospital.delete);

module.exports = route;