const Joi = require('joi');
const { TAG_LEVEL } = require('../../utility/constants');
const objectId = Joi.string().alphanum().length(24).required();

exports.saveConfigureValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                tags: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            name: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'NAME_MUST_BE_STRING';
                                }),
                            level: Joi.string()
                                .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                                .optional()
                                .error(() => {
                                    return 'LEVEL_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:PROGRAM,COURSE,INSTITUTION';
                                }),
                            isDefault: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            subTag: Joi.array()
                                .items(Joi.string())
                                .optional()
                                .error(() => {
                                    return 'SUB_TAG_MUST_BE_STRING';
                                }),
                            isActive: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                        }),
                    )
                    .optional()
                    .error(() => {
                        return 'TAGS_MUST BE AN ARRAY';
                    }),
                attemptType: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            name: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'NAME_MUST_BE_STRING';
                                }),
                            isActive: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                        }),
                    )
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getConfigureValidator = Joi.object()
    .keys({
        Headers: Joi.object()
            .keys({
                _institution_id: objectId.error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.createCalendarValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                upcomingInstitution: Joi.array().items(
                    Joi.object().keys({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ID_REQUIRED';
                            }),
                        calendar_name: Joi.string()
                            .optional()
                            .error(() => {
                                return 'CALENDAR_NAME_MUST_BE_STRING';
                            }),
                        batch: Joi.string()
                            .optional()
                            .error(() => {
                                return 'BATCH_MUST_BE_STRING';
                            }),
                        start_date: Joi.date()
                            .iso()
                            .optional()
                            .error(() => {
                                return 'START_DATE_MUST_BE_DATE';
                            }),
                        end_date: Joi.date()
                            .iso()
                            .optional()
                            .error(() => {
                                return 'END_DATE_MUST_BE_DATE';
                            }),
                        isDeleted: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);
