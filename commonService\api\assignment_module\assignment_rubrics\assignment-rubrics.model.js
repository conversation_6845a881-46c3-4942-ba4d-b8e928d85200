const mongoose = require('mongoose');
const {
    INSTITUTION,
    DIGI_COURSE,
    DIGI_PROGRAM,
    GLOBAL,
    COURSE_BASED,
    ASSIGNMENT_RUBRICS,
} = require('../../../utility/constants');

const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;
const assignmentRubricsSchema = new Schema(
    {
        _institution_id: {
            type: ObjectId,
            ref: INSTITUTION,
        },
        courseId: {
            type: ObjectId,
            ref: DIGI_COURSE,
        },
        programId: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        rubricScope: { type: String, enum: [GLOBAL, COURSE_BASED] },
        code: { type: String },
        name: { type: String },
        abbreviation: { type: String },
        description: {
            value: { type: String },
            attachments: [
                {
                    url: String,
                    signedUrl: String,
                    sizeInKb: Number,
                    name: String,
                },
            ],
        },
        evaluateInSequence: { type: Boolean },
        isPublic: { type: Boolean },
        scoreType: { type: String },
        // weightAgeType: { type: String }, //phase2
        rubrics: [
            {
                rubricsType: { type: String },
                applyWeightAgeTo: { type: String },
                criteria: {
                    name: String,
                    outcome: [{ id: ObjectId, name: String }],
                    totalPercent: Number,
                    // weightAge: { type: Number },
                },
                dimensional: [
                    {
                        name: String,
                        description: String,
                        feedback: String,
                        min: Number,
                        max: Number,
                    },
                ],
                subRubrics: [
                    {
                        applyWeightAgeTo: { type: String },
                        criteria: {
                            name: String,
                            outcome: [{ id: Schema.Types.ObjectId, name: String }],
                            totalPercent: Number,
                        },
                        dimensional: [
                            {
                                name: String,
                                description: String,
                                feedback: String,
                                min: Number,
                                max: Number,
                            },
                        ],
                    },
                ],
            },
        ],
        duplicateFromTheRubrics: {
            type: ObjectId,
            ref: ASSIGNMENT_RUBRICS,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(ASSIGNMENT_RUBRICS, assignmentRubricsSchema);
