const moduleSchema = require('./module.model');
const { MODULES } = require('../../../utility/constants');
const { convertToMongoObjectId, getModel } = require('../../../utility/common');

const getAllModuleList = async ({ headers = {} }) => {
    const { tenantURL } = headers;
    const moduleModel = getModel(tenantURL, MODULES, moduleSchema);
    const moduleList = await moduleModel.find(
        {
            isDeleted: false,
        },
        {},
    );

    if (!moduleList) {
        return { statusCode: 200, message: 'Modules List', data: [] };
    }

    return { statusCode: 200, message: 'Modules List', data: moduleList };
};

const createModule = async ({ body = {}, headers = {} }) => {
    const { tenantURL } = headers;
    const moduleModel = getModel(tenantURL, MODULES, moduleSchema);
    const moduleUpdate = await moduleModel.create(body);
    if (!moduleUpdate) {
        return { statusCode: 200, message: 'Unable to Create Modules', data: moduleUpdate };
    }
    return { statusCode: 200, message: 'Modules Created', data: moduleUpdate };
};

const updateModule = async ({ params = {}, body = {}, headers = {} }) => {
    const { id } = params;
    const { tenantURL } = headers;
    const moduleModel = getModel(tenantURL, MODULES, moduleSchema);
    const moduleUpdate = await moduleModel.updateOne(
        { _id: convertToMongoObjectId(id) },
        { $set: body },
    );
    if (!moduleUpdate) {
        return { statusCode: 200, message: 'Unable to Update Modules', data: moduleUpdate };
    }

    return { statusCode: 200, message: 'Modules Updated', data: moduleUpdate };
};

const deleteModule = async ({ params = {}, headers = {} }) => {
    const { id } = params;
    const { tenantURL } = headers;
    const moduleModel = getModel(tenantURL, MODULES, moduleSchema);
    const moduleDelete = await moduleModel.deleteOne(
        { _id: convertToMongoObjectId(id) },
        { $set: { isDeleted: true } },
    );
    if (!moduleDelete) {
        return { statusCode: 200, message: 'Unable to Removed Modules', data: moduleDelete };
    }

    return { statusCode: 200, message: 'Modules Removed', data: moduleDelete };
};

module.exports = {
    getAllModuleList,
    createModule,
    updateModule,
    deleteModule,
};
