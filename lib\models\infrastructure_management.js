const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const infrastructure_management = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
            required: true,
        },
        _building_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INFRASTRUCTURE,
            required: true,
        },
        building_name: {
            type: String,
            required: true,
        },
        floor_no: {
            type: String,
        },
        zone: [{ type: String }],
        room_no: {
            type: String,
            required: true,
        },
        name: {
            type: String,
            required: true,
        },
        usage: {
            type: String,
            required: true,
        },
        // delivery_type: [{ type: String, required: true }],
        delivery_type: [
            {
                _delivery_type_id: { type: String, required: true },
                delivery_symbol: { type: String, required: true },
            },
        ],
        timing: [
            {
                _time_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.TIME_GROUP,
                },
                /* time: {
            type: String,
            required: true
        } */
            },
        ],
        program: [
            {
                _program_id: Schema.Types.ObjectId,
                name: {
                    type: String,
                    required: true,
                },
            },
        ],
        department: [
            {
                _department_id: Schema.Types.ObjectId,
                name: {
                    type: String,
                    required: true,
                },
            },
        ],
        subject: [
            {
                _subject_id: Schema.Types.ObjectId,
                name: {
                    type: String,
                    required: true,
                },
            },
        ],
        capacity: { type: Number, required: true },
        reserved: { type: Number, required: true },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        outsideCampus: { type: Boolean, default: false },
        radius: { type: Number },
        latitude: { type: String },
        longitude: { type: String },
    },
    { timestamps: true },
);

module.exports = mongoose.model(constant.INFRASTRUCTURE_MANAGEMENT, infrastructure_management);
