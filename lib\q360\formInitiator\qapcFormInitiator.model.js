const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_FORM_INITIATOR,
    INSTITUTION,
    DRAFT,
    APPROVED,
    PUBLISHED,
    REJECTED,
    RE_SUBMIT,
    RESUBMISSION,
    SUBMITTED,
    QAPC_FORM_CATEGORY,
    QAPC_FORM_SETTING,
    QAPC_FORM_SETTING_COURSES,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    DIGI_COURSE,
    QAPC_FORM_COURSES_GROUPS,
    INSTITUTION_CALENDAR,
    TAG_LEVEL,
    PENDING,
    USER,
    QAPC_ROLE,
    FORWARD,
    IN_REVIEW,
    YET_TO_START,
} = require('../../utility/constants');

const qapcFormInitiatorSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        createdUserIds: [{ userId: { type: ObjectId } }],
        categoryId: { type: ObjectId, ref: QAPC_FORM_CATEGORY },
        categoryName: { type: String },
        level: {
            type: String,
            enum: [TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION],
        },
        categoryFormId: { type: ObjectId, ref: QAPC_FORM_SETTING },
        formName: { type: String },
        categoryFormCourseId: { type: ObjectId, ref: QAPC_FORM_SETTING_COURSES },
        assignedInstitutionId: { type: ObjectId, ref: INSTITUTION },
        institutionName: { type: String },
        programId: { type: ObjectId, ref: DIGI_PROGRAM },
        programName: { type: String },
        curriculumId: { type: ObjectId, ref: DIGI_CURRICULUM },
        curriculumName: { type: String },
        year: { type: String },
        term: { type: String },
        attemptTypeName: { type: String },
        formAttachment: { type: Boolean, default: false },
        courseId: { type: ObjectId, ref: DIGI_COURSE },
        courseName: { type: String },
        courseCode: { type: String },
        selectedGroupName: [{ type: String }],
        categoryFormGroupId: { type: ObjectId, ref: QAPC_FORM_COURSES_GROUPS },
        mergedFormId: [
            {
                formInitiatorId: { type: ObjectId, ref: QAPC_FORM_INITIATOR },
                isDeleted: { type: Boolean, default: false },
            },
        ],
        mergeStatus: {
            type: Boolean,
            default: false,
        },
        startMonth: { type: Date },
        endMonth: { type: Date },
        formCalenderIds: [
            {
                institutionCalenderId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
                isDeleted: { type: Boolean, default: false },
            },
        ],
        submissionStatus: { type: String, enum: [DRAFT, RESUBMISSION, SUBMITTED], default: DRAFT },
        submissionDate: { type: Date },
        status: {
            type: String,
            enum: [DRAFT, APPROVED, PUBLISHED, REJECTED, RE_SUBMIT, IN_REVIEW, YET_TO_START],
            default: DRAFT,
        },
        resubmissionLog: [
            {
                level: { type: Number },
                roleId: { type: ObjectId, ref: QAPC_ROLE },
                userId: { type: ObjectId, ref: USER },
                reason: { type: String },
                resubmissionDate: { type: String },
            },
        ],
        crossedTAT: [
            {
                level: { type: Number },
                endDate: { type: Date },
            },
        ],
        //levelStartTime
        levelStartTime: [
            {
                level: { type: Number },
                startDate: { type: Date },
            },
        ],
        approverDetails: {
            currentLevel: { type: Number, default: 1 },
            levelStatus: {
                type: String,
                default: PENDING,
                enum: [APPROVED, PENDING, REJECTED, FORWARD, PUBLISHED],
            },
            isHistory: { type: Boolean },
            userLevel: { type: Number },
            startDate: { type: Date },
        },
        rejectionDetails: [
            {
                level: { type: Number },
                roleId: { type: ObjectId, ref: QAPC_ROLE },
                userId: { type: ObjectId, ref: USER },
                reason: { type: String },
            },
        ],
        approverList: [
            {
                level: { type: Number },
                levelStatus: { type: String },
                updateLevelDate: { type: Date },
                isSkipped: { type: Boolean },
                roleUser: [
                    {
                        roleId: { type: ObjectId, ref: QAPC_ROLE },
                        roleName: { type: String },
                        userIds: [
                            {
                                userId: { type: ObjectId, ref: USER },
                                status: { type: String, default: PENDING },
                                statusDate: { type: Date },
                                reason: { type: String },
                            },
                        ],
                    },
                ],
            },
        ],
        archive: { type: Boolean, default: false },
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_FORM_INITIATOR, qapcFormInitiatorSchema);
