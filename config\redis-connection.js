// redis-connection.js

const Redis = require('ioredis');

class RedisClient {
    constructor() {
        this.Client = null;
        this.Subscriber = null;
    }

    async connect() {
        //sample command to check on cli
        //redis-cli -h ************* -p 6379 -a <PERSON>is@321
        this.Client = new Redis({
            port: process.env.REDIS_PORT, // Redis port
            host: process.env.REDIS_HOST, // Redis host
            password: process.env.REDIS_PASSWORD,
        });
        return this.Client;
    }

    async duplicate() {
        this.Subscriber = new Redis({
            port: process.env.REDIS_PORT, // Redis port
            host: process.env.REDIS_HOST, // Redis host
            password: process.env.REDIS_PASSWORD,
        });
        return this.Subscriber;
    }
}
module.exports = {
    redisClient: new RedisClient(),
};
