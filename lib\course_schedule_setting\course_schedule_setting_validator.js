/* eslint-disable no-useless-escape */
const Joi = require('joi');
const { com_response } = require('../utility/common');
const {
    GENDER,
    REMOTE_PLATFORM: { ZOOM, TEAMS },
} = require('../utility/constants');
exports.insertRemoteScheduling = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                meetingTitle: Joi.string().required(),
                gender: Joi.string().valid(GENDER.MALE, GENDER.FEMALE, GENDER.BOTH).required(),
                // remotePlatform: Joi.string()
                //     .valid(TEAMS, ZOOM)
                //     .required()
                //     .error((error) => {
                //         return req.t('REMOTE_PLATFORM_REQUIRED');
                //     }),
                meetingUrl: Joi.string()
                    .regex(
                        /^(?:(?:https?|ftp):\/\/)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})))(?::\d{2,5})?(?:\/\S*)?$/,
                    )
                    .when('remotePlatform', {
                        is: TEAMS,
                        then: Joi.optional(),
                        otherwise: Joi.required(),
                    }),
                remotePlatform: Joi.string().valid(TEAMS, ZOOM),
                meetingUsername: Joi.string().when('remotePlatform', {
                    is: TEAMS,
                    then: Joi.optional(),
                    otherwise: Joi.required(),
                }),
                associatedEmail: Joi.string()
                    .regex(
                        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                    )
                    .allow('')
                    .when('remotePlatform', {
                        is: TEAMS,
                        then: Joi.optional(),
                        otherwise: Joi.required(),
                    }),
                meetingId: Joi.string().when('remotePlatform', {
                    is: TEAMS,
                    then: Joi.optional(),
                    otherwise: Joi.required(),
                }),
                passCode: Joi.string().when('remotePlatform', {
                    is: TEAMS,
                    then: Joi.optional(),
                    otherwise: Joi.required(),
                }),
                password: Joi.string().allow('').when('remotePlatform', {
                    is: TEAMS,
                    then: Joi.optional(),
                    otherwise: Joi.required(),
                }),
                term: Joi.string().required(),
                levelId: Joi.string().alphanum().length(24).required(),
                levelName: Joi.string().required(),
                yearId: Joi.string().alphanum().length(24).required(),
                yearName: Joi.string().required(),
                apiKey: Joi.string().when('remotePlatform', {
                    is: TEAMS,
                    then: Joi.optional(),
                    otherwise: Joi.required(),
                }),
                apiSecretKey: Joi.string().when('remotePlatform', {
                    is: TEAMS,
                    then: Joi.optional(),
                    otherwise: Joi.required(),
                }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
exports.idValidate = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.programIdValidate = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    program_id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
