const express = require('express');
const route = express.Router();
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
const catchAsync = require('../../utility/catch-async');
const { getDateBasedScheduleList } = require('./calendar.controller');
const { getSingleSchedule } = require('./calendar.controller');

route.get(
    '/dateBasedCalendarList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(getDateBasedScheduleList),
);
route.get(
    '/getSingleSchedule',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(getSingleSchedule),
);
module.exports = route;
