const { isEncryptedRoute, encryptDecryptApplicable } = require('../../service/endpoint.util');
const { DS_DATA_RETRIEVED, CLIENT_END } = require('./constants');
const { encryptData } = require('./encrypt_decrypt.util');
const {
    SERVICES: { ENCRYPT_PAYLOAD },
} = require('./util_keys');
const { encryptPayload } = require('../../middleware/encryptionDecryption.service');

module.exports = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next))
        .then(({ statusCode = 200, message = DS_DATA_RETRIEVED, data = {} }) => {
            const translatedMessage = req.t(message);
            const url = req?.originalUrl?.split('?')?.at(0);
            let responseData = { message: translatedMessage, data };
            if (
                ENCRYPT_PAYLOAD === 'true' &&
                isEncryptedRoute({
                    url,
                    urlMethod: req?.method,
                    applicableTo: encryptDecryptApplicable.response,
                })
            ) {
                const encryptedSessionKey = req?.headers['x-session-key'];
                responseData = {
                    data: encryptedSessionKey
                        ? encryptPayload({
                              data: { message: translatedMessage, data },
                              encryptedSessionKey,
                          })
                        : encryptData({
                              content: { message: translatedMessage, data },
                              handledBy: CLIENT_END,
                          }),
                };
            }
            res.status(statusCode).send(responseData);
        })
        .catch((err) => next(err));
};
