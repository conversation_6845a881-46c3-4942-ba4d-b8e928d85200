let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let countrySchema = new Schema({
    name: {
        type: String,
        required: true
    },
    code: {
        type: String,
        required: true
    },
    currency: {
        type: String,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });

// countrySchema.index({ name: 1, isDeleted: 1 }, { unique: true });
// module.exports = mongoose.model('conty', countrySchema);
module.exports = mongoose.model(constant.COUNTRY, countrySchema);