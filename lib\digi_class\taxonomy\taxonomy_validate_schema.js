const Joi = require('joi');
const { com_response } = require('../../utility/common');

// create activities schema
function createTaxonomySchema(req, res, next) {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            name: Joi.string().optional(),
            createdBy: Joi.string().length(24).optional(),
        }),
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}
// get result schema
function getTaxonomySchema(req, res, next) {
    const schema = Joi.object().keys({
        params: {
            id: Joi.string().length(24),
        },
        query: {
            userId: Joi.string().length(24),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// update activities schema
function updateTaxonomySchema(req, res, next) {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            name: Joi.string().optional(),
        }),
        params: {
            id: Joi.string().length(24),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

// delete activities schema
function deleteTaxonomySchema(req, res, next) {
    const schema = Joi.object().keys({
        params: {
            id: Joi.string().length(24),
        },
    });
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
}

module.exports = {
    createTaxonomySchema,
    getTaxonomySchema,
    updateTaxonomySchema,
    deleteTaxonomySchema,
};
