const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.framework = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    name: Joi.string()
                        .min(2)
                        .max(50)
                        .error((error) => {
                            return req.t('MIN_2_MAX_50_CHARACTERS_ALLOWED');
                        }),
                    code: Joi.string()
                        .alphanum()
                        .min(2)
                        .max(50)
                        .error((error) => {
                            return req.t('MIN_2_MAX_50_CHARACTERS_ALLOWED');
                        }),
                    domains: Joi.array().items({
                        no: Joi.string(),
                        name: Joi.string(),
                    }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.framework_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return req.t('MUST_ONLY_ALPHANUMERIC_WITH_24_CHARACTER');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.add_clo = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    clo: Joi.object()
                        .keys({
                            no: Joi.string()
                                .min(1)
                                .max(6)
                                .required()
                                .error((error) => {
                                    return req.t('MIN_1_MAX_6_CHARACTERS_ALLOWED');
                                }),
                            name: Joi.string()
                                .min(2)
                                .max(1000)
                                .required()
                                .error((error) => {
                                    return req.t('MIN_2_MAX_1000_CHARACTERS_ALLOWED');
                                }),
                        })
                        .unknown(true),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return req.t('MUST_ONLY_ALPHANUMERIC_WITH_24_CHARACTER');
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.update_mapping_type_course = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    mapping_type: Joi.string()
                        .valid(
                            constant.MAPPING_TYPE_LIST.IMPACT,
                            constant.MAPPING_TYPE_LIST.ALIGNMENT,
                            '',
                        )
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.update_mapping_type = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    mapping_type: Joi.string()
                        .valid(
                            constant.MAPPING_TYPE_LIST.IMPACT,
                            constant.MAPPING_TYPE_LIST.ALIGNMENT,
                            '',
                        )
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.update_content_mapping_type = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    content_mapping_type: Joi.string()
                        .valid(
                            constant.CONTENT_MAPPING_TYPE.REQUIRED,
                            constant.CONTENT_MAPPING_TYPE.OPTIONAL,
                        )
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
exports.update_content_mapping_type_course = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    content_mapping_type: Joi.string()
                        .valid(
                            constant.CONTENT_MAPPING_TYPE.REQUIRED,
                            constant.CONTENT_MAPPING_TYPE.OPTIONAL,
                        )
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
