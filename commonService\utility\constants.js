const constants = Object.freeze({
    LOCAL: 'local',
    INSTITUTION_CALENDAR: 'institution_calendars',
    PROGRAM_CALENDAR: 'program_calendars',
    STUDENT_GROUP: 'student_groups',
    DIGI_INSTITUTE: 'digi_institute',
    DIGI_PROGRAM: 'digi_programs',
    DIGI_DEPARTMENT_SUBJECT: 'digi_department_subject',
    DIGI_SESSION_DELIVERY_TYPES: 'digi_session_delivery_types',
    DIGI_CURRICULUM: 'digi_curriculums',
    DIGI_COURSE: 'digi_course',
    DIGI_SESSION_ORDER: 'digi_session_order',
    DS_DATA_RETRIEVED: 'Data retrieved',
    ASSESSMENT_MANAGEMENT: 'assessment_management',
    ASSIGNMENT_CATEGORY: 'assignment_category',
    ASSIGNMENT_GROUP: 'assignment_group',
    ATTAINMENT_MANAGEMENT: 'attainment_management',
    ATTAINMENT_REPORT: 'attainment_report',
    TERM_CALENDAR: 'term_calendar',
    COURSE_DETAILS: 'course_details',
    MIXED: 'MIXED',
    DIRECT: 'direct',
    INDIRECT: 'indirect',
    INTERNAL: 'internal',
    EXTERNAL: 'external',
    ASSESSMENT_PLANNING: 'assessment_planning',
    ASSIGNMENT: 'assignment',
    ASSIGNMENT_ANSWER: 'assignment_answer',
    ASSIGNMENT_PROMPT_ANSWER: 'assignment_prompt_answer',
    ASSIGNMENT_COMMENTS: 'assignment_comments',
    ASSIGNMENT_RUBRICS: 'assignment_rubrics',
    ASSESSMENT_COURSE_PROGRAM: 'assessment_course_program',
    ASSESSMENT_LIBRARY: 'assessment_library',
    ASSIGNMENT_EVALUATION_NONE: 'None',
    GENDER: {
        MALE: 'male',
        FEMALE: 'female',
        BOTH: 'both',
    },
    STUDENT: 'student',
    STUDENT_GROUP_MODE: {
        FYD: 'foundation',
        COURSES: 'course',
        ROTATION: 'rotation',
    },
    SLO: 'slo',
    CLO: 'clo',
    PLO: 'plo',
    RANGE: 'range',
    FIXED: 'fixed',
    EQUAL: 'equal',
    GREATER_EQUAL: 'greater_equal',
    COURSE_COORDINATOR: 'Course Coordinator',
    ASSIGNMENT_SETTINGS: 'assignment_settings',
    INSTITUTION: 'institutions',
    PROGRAM: 'programs',
    ASSIGNMENT_PROMPT: 'assignment_prompt',
    CURRICULUM: 'curriculums',
    DEPARTMENT: 'departments',
    DEPARTMENT_SUBJECT: 'department_subjects',
    SESSION_DELIVERY_TYPES: 'session_delivery_types',
    USER: 'users',
    COURSE: 'courses',
    SESSION_ORDER: 'session_orders',
    COMPLETED: 'completed',
    EXCUSED: 'excused',
    COURSE_SCHEDULE: 'course_schedules',
    ROLE_ASSIGNS: 'role_assigns',
    TAXONOMY: 'taxonomy',
    SUBMITTED: 'submitted',
    PENDING: 'pending',
    MISSED: 'missed',
    PUBLISHED: 'published',
    TO_GRADE: 'to_grade',
    IN_GRADING: 'in_grading',
    IN_REVIEW: 'in_review',
    GRADED: 'graded',
    RELEASED: 'released',
    RESUBMITTED: 'resubmitted',
    GLOBAL: 'global',
    COURSE_BASED: 'course_based',
    DURATION: 'duration',
    RESUBMIT: 'Resubmit',
    ASSIGNMENT_REPORT: 'assignment_report',
    PUBLISH: 'publish',
    DRAFT: 'draft',
    ASSIGNMENT_STUDENT_REPORT: 'assignment_student_report',
    REMOTE_PLATFORM: {
        ZOOM: 'zoom',
        TEAMS: 'teams',
    },
    PROMPT: 'Prompt',
});

module.exports = constants;
