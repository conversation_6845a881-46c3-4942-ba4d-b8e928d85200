// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
// const constant = require('../../../utility/constants');


exports.id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.university_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            university_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.insert_college = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            college_name: Joi.string().min(2).max(100).required().error(error => {
                return error;
            }),
            logo: Joi.string().required().error(error => {
                return error;
            }),
            address_details: Joi.object().keys({
                address: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                country: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                state: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                district: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                city: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                zipcode: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
            }).unknown(true)

        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
exports.update_college = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            college_name: Joi.string().min(2).max(100).required().error(error => {
                return error;
            }),
            // logo: Joi.string().min(2).max(100).required().error(error => {
            //     return error;
            // }),
            address_details: Joi.object().keys({
                address: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                country: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                state: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                district: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                city: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
                zipcode: Joi.string().min(2).max(100).required().error(error => {
                    return error;
                }),
            }).unknown(true)

        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}
