const User = require('../../models/user');
const { response_function, convertToMongoObjectId, clone } = require('../../utility/common');
const InstitutionCalender = require('../../models/institution_calendar');
const CourseSchedule = require('../../models/course_schedule');
const student_group = require('../../models/student_group');
const ProgramCalender = require('../../models/program_calendar');
const { IN_PROGRESS, COMPLETED } = require('../../utility/enums');
const {
    SCHEDULE_TYPES,
    ABSENT,
    LEAVE,
    PRESENT,
    PENDING,
    LEAVE_TYPE,
    LMS,
} = require('../../utility/constants');
const lms = require('mongoose').model(LMS);
const {
    get_list,
    get,
    get_list_populate,
    dsGetAllWithSortAsJSON,
} = require('../../base/base_controller');

const lmsStudentSettingData = async (_institution_id) => {
    //Leave Setting Data
    const leave_category = await get(
        lms,
        {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
        },
        { _id: 1, category: 1, student_warning_absence_calculation: 1 },
    );
    if (!leave_category.status) leave_category.data.student_warning_absence_calculation = [];
    let warningAbsenceData = leave_category.data.student_warning_absence_calculation.filter(
        (ele) => ele.isDeleted === false,
    );
    warningAbsenceData = clone(
        warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        }),
    );

    const finaleWarning =
        warningAbsenceData[1] && warningAbsenceData[1].warning
            ? warningAbsenceData[1].warning
            : undefined;
    const denialWarning = warningAbsenceData[0].warning;
    return {
        warningAbsenceData,
        finaleWarning,
        denialWarning,
    };
};

const studentAttendanceReport = (courseStudentIds, courseScheduled, warningAbsenceData) => {
    const studentData = [];
    for (studentElement of courseStudentIds) {
        const studentSchedule = courseScheduled.filter(
            (ele) =>
                ele &&
                ele.students.find((ele2) => ele2._id.toString() === studentElement.toString()),
        );
        // const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
        const absentSchedules = studentSchedule.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === LEAVE_TYPE.PERMISSION ||
                            ele2.status === LEAVE),
                ),
        );
        const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
        // studentElement.absence = denialPercentage.toFixed(2);
        const warningData = warningAbsenceData.find(
            (ele) =>
                ele.absence_percentage &&
                parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
        );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.warning : '',
        });
    }
    return studentData;
};

/**
 * get institution calender for validation purpose
 * @param {string} institutionCalenderId
 * @param {string} mode
 * @returns InstitutionCalender object or objects
 */
exports.getInstitutionCalender = async (institutionCalenderId, mode) => {
    try {
        let data;
        if (mode === IN_PROGRESS) {
            const institutionCalenderQuery = {
                _id: institutionCalenderId,
                isDeleted: false,
                isActive: true,
            };
            data = await InstitutionCalender.findOne(institutionCalenderQuery);
        } else if (mode === COMPLETED) {
            const institutionCalenderQuery = {
                _id: { $ne: institutionCalenderId },
                isDeleted: false,
                isActive: true,
            };
            data = await InstitutionCalender.find(institutionCalenderQuery).sort({
                start_date: -1,
            });
        } else if (mode === 'all') {
            const institutionCalenderQuery = {
                isDeleted: false,
                isActive: true,
            };
            data = await InstitutionCalender.find(institutionCalenderQuery).sort({
                start_date: -1,
            });
        }
        return data;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get all program calenders of given course id
 * @param [string] institutionCalenderIds
 * @param [string] courseIds
 * @returns objects of ProgramCalender
 */
exports.getProgramCalenders = async (institutionCalenderIds, courseIds) => {
    try {
        const programCalenderQuery = {
            _institution_calendar_id: { $in: institutionCalenderIds },
            $or: [
                { 'level.course._course_id': { $in: courseIds } },
                { 'level.rotation_course.course._course_id': { $in: courseIds } },
            ],
            isDeleted: false,
            isActive: true,
        };
        const programCalenderProject = {
            _id: 1,
            level: 1,
            _program_id: 1,
            _institution_calendar_id: 1,
        };
        const programCalenders = await ProgramCalender.find(
            programCalenderQuery,
            programCalenderProject,
        );
        return programCalenders;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get Student of given id for validation purpose
 * @param {string} studentId
 * @returns User object or null
 */
exports.getStudent = async (studentId) => {
    try {
        const studentQuery = { _id: studentId, isDeleted: false, isActive: true };
        const student = await User.findOne(studentQuery);
        return student;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * find start and end date of course from program calendar
 * @param {*} programCalenders
 * @param {*} course
 * @returns object {start_date, end_date}
 */
exports.findCourseStartAndEndDate = (programCalenders, course) => {
    try {
        const dates = { start_date: '', end_date: '' };
        for (const programCalender of programCalenders) {
            const level = programCalender.level.find(
                (level) =>
                    level._program_id.toString() === course._program_id.toString() &&
                    level.rotation === course.rotation &&
                    level.level_no === course.level_no &&
                    level.year === course.year_no,
            );
            if (level && level.rotation === 'no') {
                const pcCourse = level.course.find(
                    (pcCourse) => pcCourse._course_id.toString() === course._course_id.toString(),
                );
                if (pcCourse) {
                    dates.start_date = pcCourse.start_date;
                    dates.end_date = pcCourse.end_date;
                }
            } else if (level && level.rotation === 'yes') {
                const rCourses = level.rotation_course.find(
                    (rotation_course) => rotation_course.rotation_count === course.rotation_count,
                );
                if (rCourses) {
                    const pcCourse = rCourses.course.find(
                        (pcCourse) =>
                            pcCourse._course_id.toString() === course._course_id.toString(),
                    );
                    if (pcCourse) {
                        dates.start_date = pcCourse.start_date;
                        dates.end_date = pcCourse.end_date;
                    }
                }
            }
        }
        return dates;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get course with present absent count
 * @param {string} studentId
 * @param {string} institutionCalendarId
 * @returns Course object
 */
exports.getInprogressCourseDetails = async (studentId, institutionCalendarId) => {
    try {
        // get regular course schedules of given student
        const query = {
            _institution_calendar_id: institutionCalendarId,
            'students._id': studentId,
            type: SCHEDULE_TYPES.REGULAR,
            isDeleted: false,
            isActive: true,
        };
        const project = {};
        const courseSchedules = await CourseSchedule.find(query, project);
        // unique courseIds of courseSchedules
        const courseIds = [
            ...new Set(
                courseSchedules.map((courseSchedule) => courseSchedule._course_id.toString()),
            ),
        ];
        // get program calenders for start and end dates
        const programCalenders = await this.getProgramCalenders([institutionCalendarId], courseIds);
        // map courses with counts (present, absent, leave, warning, etc...)
        const courses = [];
        for (const courseId of courseIds) {
            const schedules = courseSchedules.filter((schedule) => {
                return schedule._course_id.toString() === courseId.toString();
            });
            const counts = {
                totalCount: schedules.length,
                absentCount: 0,
                leaveCount: 0,
                warningCount: 0,
                presentCount: 0,
                pendingCount: 0,
                permissionCount: 0,
                onDutyCount: 0,
            };
            schedules.forEach((schedule) => {
                const { students } = schedule;
                const student = students.find(
                    (student) => student._id.toString() === studentId.toString(),
                );
                if (student.status === ABSENT) counts.absentCount++;
                else if (student.status === LEAVE) counts.leaveCount++;
                else if (student.status === PRESENT) counts.presentCount++;
                else if (student.status === PENDING) counts.pendingCount++;
                else if (student.status === LEAVE_TYPE.PERMISSION) counts.permissionCount++;
                else if (student.status === LEAVE_TYPE.ONDUTY) counts.onDutyCount++;
            });
            if (schedules[0]) {
                const schedule = schedules[0];
                const course = {
                    _program_id: schedule._program_id,
                    _institution_calendar_id: schedule._institution_calendar_id,
                    program_name: schedule.program_name,
                    _course_id: schedule._course_id,
                    course_name: schedule.course_name,
                    course_code: schedule.course_code,
                    year_no: schedule.year_no,
                    level_no: schedule.level_no,
                    type: schedule.type,
                    rotation: schedule.rotation,
                    rotation_count: schedule.rotation_count,
                    ...counts,
                };
                const dates = this.findCourseStartAndEndDate(programCalenders, course);
                course.start_date = dates.start_date;
                course.end_date = dates.end_date;
                courses.push(course);
            }
        }
        return courses;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get studentId and return on going
 * courses of given student
 * @param {string} studentId
 * @param {string} institutionCalenderId
 * @returns object {status:boolean, message:string, data:[]}
 */
exports.getInprogressCourses = async (studentId, institutionCalenderId) => {
    try {
        // set default result
        const result = { status: true, message: '', data: [] };
        // get student, institution and check that is exist or not
        const student = await this.getStudent(studentId);
        const institution = await this.getInstitutionCalender(institutionCalenderId, IN_PROGRESS);
        if (!student || !institution) result.status = false;
        if (!student) result.message = 'Student not found';
        if (!institution) result.message = 'Institution not found';
        if (!result.status) return result;
        // get all course & schedule of given student and current institution calender
        const courses = await this.getInprogressCourseDetails(studentId, institutionCalenderId);
        result.data = courses;
        return result;
    } catch (error) {
        throw new Error(error);
    }
};

/**
 * get courses of previous institution calenders
 * @param {string} studentId
 * @param {string} institutionCalenderId
 * @returns Institution calendar with courses
 */
exports.getCompletedCourseDetails = async (studentId, institutionCalenderId, _institution_id) => {
    try {
        // get all previous institution calendars
        const allInstitutionCalenders = await this.getInstitutionCalender(
            institutionCalenderId,
            COMPLETED,
        );
        const allInstitutionCalendarIds = allInstitutionCalenders.map((institutionCalender) => {
            return institutionCalender._id;
        });
        // get regular course schedules of given student
        const query = {
            _institution_calendar_id: { $in: allInstitutionCalendarIds },
            'students._id': studentId,
            type: SCHEDULE_TYPES.REGULAR,
            isDeleted: false,
            isActive: true,
        };
        const project = {};
        const courseSchedules = await CourseSchedule.find(query, project);
        // unique current student institution calender id of course schedule
        const institutionCalendarIds = [
            ...new Set(
                courseSchedules.map((courseSchedule) => {
                    return courseSchedule._institution_calendar_id.toString();
                }),
            ),
        ];
        // map calender wise course
        const institutionCalenders = [];
        for (const institutionCalenderId of institutionCalendarIds) {
            // unique courseIds of this institutionCalender
            const courseIds = [];
            courseSchedules.forEach((courseSchedule) => {
                if (
                    courseSchedule._institution_calendar_id.toString() === institutionCalenderId &&
                    !courseIds.includes(courseSchedule._course_id.toString())
                ) {
                    courseIds.push(courseSchedule._course_id.toString());
                }
            });
            // get program calenders for start and end dates
            const programCalenders = await this.getProgramCalenders(
                [institutionCalenderId],
                courseIds,
            );
            // map courses with counts (present, absent, leave, warning, etc...)
            const courses = [];
            for (const courseId of courseIds) {
                const schedules = courseSchedules.filter((schedule) => {
                    return schedule._course_id.toString() === courseId.toString();
                });

                // LMS Setting Data
                const lmsData = await lmsStudentSettingData(
                    convertToMongoObjectId(_institution_id),
                );

                //Student Group data
                let courseAbsencePercentage = 0;
                if (schedules[0]) {
                    const studentGroupData = await get(
                        student_group,
                        {
                            isDeleted: false,
                            _institution_calendar_id: convertToMongoObjectId(institutionCalenderId),
                            'master._program_id': convertToMongoObjectId(schedules[0]._program_id),
                            'groups.courses._course_id': convertToMongoObjectId(courseId),
                        },
                        {},
                    );
                    //console.log(studentGroupData.data._id, 'SG');
                    if (studentGroupData.status) {
                        const groupInd = studentGroupData.data.groups.findIndex(
                            (ele) =>
                                ele.term === schedules[0].term &&
                                ele.level === schedules[0].level_no,
                        );
                        if (groupInd != -1) {
                            const course_ind = studentGroupData.data.groups[
                                groupInd
                            ].courses.findIndex(
                                (ele) => ele._course_id.toString() === courseId.toString(),
                            );
                            console.log(course_ind, 'Course Ind');
                            if (course_ind != -1) {
                                if (
                                    studentGroupData.data.groups[groupInd].courses[course_ind]
                                        .student_absence_percentage
                                )
                                    courseAbsencePercentage =
                                        studentGroupData.data.groups[groupInd].courses[course_ind]
                                            .student_absence_percentage;
                            }
                        }
                    }
                }

                //Warning Calculations
                if (courseAbsencePercentage && courseAbsencePercentage !== 0) {
                    lmsData.warningAbsenceData[0].absence_percentage = courseAbsencePercentage;
                }
                let studentReport = [];

                studentReport = studentAttendanceReport(
                    [studentId],
                    schedules,
                    lmsData.warningAbsenceData,
                );
                console.log(studentReport);
                let no_of_warning = 0;
                if (studentReport.length > 0) {
                    no_of_warning = studentReport.find(
                        (ele) => ele.student_id.toString() === studentId.toString(),
                    ).warning;
                }

                const counts = {
                    totalCount: schedules.length,
                    absentCount: 0,
                    leaveCount: 0,
                    warningCount: no_of_warning,
                    presentCount: 0,
                    pendingCount: 0,
                    permissionCount: 0,
                    onDutyCount: 0,
                };
                schedules.forEach((schedule) => {
                    const { students } = schedule;
                    const student = students.find(
                        (student) => student._id.toString() === studentId.toString(),
                    );
                    if (student.status === ABSENT) counts.absentCount++;
                    else if (student.status === LEAVE) counts.leaveCount++;
                    else if (student.status === PRESENT) counts.presentCount++;
                    else if (student.status === PENDING) counts.pendingCount++;
                    else if (student.status === LEAVE_TYPE.PERMISSION) counts.permissionCount++;
                    else if (student.status === LEAVE_TYPE.ONDUTY) counts.onDutyCount++;
                });
                if (schedules[0]) {
                    const schedule = schedules[0];
                    const course = {
                        _program_id: schedule._program_id,
                        program_name: schedule.program_name,
                        _course_id: schedule._course_id,
                        course_name: schedule.course_name,
                        course_code: schedule.course_code,
                        year_no: schedule.year_no,
                        level_no: schedule.level_no,
                        type: schedule.type,
                        rotation: schedule.rotation,
                        rotation_count: schedule.rotation_count,
                        ...counts,
                    };
                    const dates = this.findCourseStartAndEndDate(programCalenders, course);
                    course.start_date = dates.start_date;
                    course.end_date = dates.end_date;
                    courses.push(course);
                }
            }
            institutionCalenders.push({ institutionCalenderId, courses });
        }
        return institutionCalenders;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

/**
 * get studentId and return completed
 * courses of given student
 * @param {string} studentId
 * @param {string} institutionCalenderId
 * @returns object {status:boolean, message:string, data:[]}
 */
exports.getCompletedCourses = async (studentId, institutionCalenderId, _institution_id) => {
    try {
        // set default result
        const result = { status: true, message: '', data: [] };
        // get student, institution and check that is exist or not
        const student = await this.getStudent(studentId);
        const institution = await this.getInstitutionCalender(institutionCalenderId, IN_PROGRESS);
        if (!student || !institution) result.status = false;
        if (!student) result.message = 'Student not found';
        if (!institution) result.message = 'Institution not found';
        if (!result.status) return result;
        result.data = await this.getCompletedCourseDetails(
            studentId,
            institutionCalenderId,
            _institution_id,
        );
        return result;
    } catch (error) {
        throw new Error(error);
    }
};
