const route = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const { authentication } = require('../../middleware/auth.middleware');
const {
    getGlobalConfiguration,
    updateGlobalConfiguration,
    updateWorkingDays,
    addBreaks,
    updateBreaks,
    removeBreaks,
    updateEventType,
    addEventType,
    addLanguage,
    updateLanguage,
    updateDefaultInLanguageSetting,
    getProgramInputs,
    resetLabelConfiguration,
    updateLabelConfiguration,
    generateLabelConfiguration,
    activateOrDeactivateWorkingDays,
    createProgramType,
    removeProgramType,
    updateProgramType,
    updateCurriculumNaming,
    updateCreditHours,
    updateIndependentCourseCreditHours,
    addEmailIdConfiguration,
    removeEmailIdConfiguration,
    updateEmailIdConfiguration,
    addDesignation,
    designationCheck,
    designationUpdate,
    designationDelete,
    getDesignationLists,
    getLabelFieldLists,
    labelFieldUpdate,
    labelField<PERSON>heck,
    existingLabelFieldUpdate,
    biometric<PERSON>heck,
    getBiometricList,
    updateMailContent,
    getMailContent,
    updateDocumentFormatAndSize,
    updateMaximumSize,
    addDocumentsCategory,
    updateDocumentsCategory,
    deleteDocumentsCategory,
    addDocuments,
    updateDocuments,
    deleteDocuments,
    updateReminderMail,
    updateMailSettings,
    getDocumentContent,
    getMailSettingsContent,
    mailContentReset,
    labelFieldReset,
    labelVaccineFieldCheck,
    addVaccineCategory,
    editVaccineCategory,
    deleteVaccineCategory,
    addVaccineDetails,
    editVaccineDetails,
    deleteVaccineDetails,
    getVaccination,
    vaccineDetailsStatusCheck,
    getIndependentCourseInput,
    staffManagementDashboard,
    getEmailIdConfiguration,
    toggleAllowMixedVaccination,
    phoneFieldUpdate,
    privacyUpdate,
    allowMultipleUpdate,
    sendTestMail,
    existingPrivacyFieldUpdate,
    updateNotificationMail,
    updateDepartmentSubjectMode,
    getDepartmentSubject,
    particularMailContentReset,
    toggleWithWithoutCredit,
    updateProgramDurationFormat,
    updateDepartmentHierarchyMode,
    addDepartmentType,
    deleteDepartmentType,
    getDepartmentType,
} = require('./setting.controller');

const validator = require('./setting.validator');
const { validate } = require('../../utility/input-validation');
// Global Settings --> Basic  Details
route.get('/global-configuration', authentication, catchAsync(getGlobalConfiguration));

route.put(
    '/global-configuration/:id',
    validate(validator.updateGlobalConfiguration),
    catchAsync(updateGlobalConfiguration),
);

route.patch(
    '/global-configuration/working-days/:id',
    validate(validator.updateWorkingDays),
    catchAsync(updateWorkingDays),
);

route.put(
    '/global-configuration/active/:id',
    validate(validator.updateWorkingDays),
    catchAsync(activateOrDeactivateWorkingDays),
);

route.post(
    '/global-configuration/breaks',
    validate(validator.breakValidation),
    catchAsync(addBreaks),
);

route.put(
    '/global-configuration/breaks/:id',
    validate(validator.breakValidation),
    catchAsync(updateBreaks),
);

route.delete(
    '/global-configuration/breaks/:id',
    validate(validator.breakRemoveValidation),
    catchAsync(removeBreaks),
);

route.post(
    '/global-configuration/emailConfig',
    validate(validator.emailConfigValidation),
    catchAsync(addEmailIdConfiguration),
);
route.post(
    '/global-configuration/send-test-email',
    validate(validator.emailConfigValidation),
    catchAsync(sendTestMail),
);
route.get(
    '/global-configuration/emailConfig/:settingId',
    validate(validator.designationGetValidation),
    catchAsync(getEmailIdConfiguration),
);

route.put(
    '/global-configuration/emailConfig',
    validate(validator.updateEmailConfigValidation),
    catchAsync(updateEmailIdConfiguration),
);

route.delete(
    '/global-configuration/emailConfig',
    validate(validator.breakRemoveValidation),
    catchAsync(removeEmailIdConfiguration),
);

route.post(
    '/global-configuration/event-type',
    validate(validator.eventTypeValidation),
    catchAsync(addEventType),
);

route.put(
    '/global-configuration/event-type/:id',
    validate(validator.eventTypeUpdateValidation),
    catchAsync(updateEventType),
);

route.post(
    '/global-configuration/language',
    validate(validator.languageValidation),
    catchAsync(addLanguage),
);

route.put(
    '/global-configuration/language/:id',
    validate(validator.languageValidation),
    catchAsync(updateLanguage),
);

route.put('/global-configuration/language/default/:id', catchAsync(updateDefaultInLanguageSetting));

// Global Settings --> Program Inputs
route.get('/program-input', catchAsync(getProgramInputs));

route.put(
    '/program-input/label-configuration/reset/:id',
    validate(validator.labelConfigurationResetValidation),
    catchAsync(resetLabelConfiguration),
);

route.put(
    '/program-input/label-configuration/:id',
    validate(validator.labelConfigurationValidation),
    catchAsync(updateLabelConfiguration),
);

route.put(
    '/program-input/label-configuration/generate/:id',
    validate(validator.labelConfigurationResetValidation),
    catchAsync(generateLabelConfiguration),
);

route.post(
    '/program-input/program-type',
    validate(validator.ProgramTypeValidation),
    catchAsync(createProgramType),
);

route.put(
    '/program-input/program-type/:id',
    validate(validator.ProgramTypeValidation),
    catchAsync(updateProgramType),
);

route.delete(
    '/program-input/program-type/:id',
    validate(validator.ProgramTypeRemoveValidation),
    catchAsync(removeProgramType),
);

route.put(
    '/program-input/curriculum-naming/:id',
    validate(validator.ProgramTypeRemoveValidation),
    catchAsync(updateCurriculumNaming),
);

route.put(
    '/program-input/credit-hours/:id',
    validate(validator.ProgramTypeRemoveValidation),
    catchAsync(updateCreditHours),
);

route.put(
    '/program-input/update-program-duration-format/:id',
    validate(validator.ProgramTypeRemoveValidation),
    catchAsync(updateProgramDurationFormat),
);

route.put('/independent-course-input/:id', catchAsync(updateIndependentCourseCreditHours));
route.get(
    '/global-configuration/staff-management/dashboard/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(staffManagementDashboard),
);
route.post(
    '/global-configuration/staff-management/designation/:type',
    validate(validator.addDesignationValidation),
    catchAsync(addDesignation),
);
route.put(
    '/global-configuration/staff-management/designation-chk/:designationId/:type',
    validate(validator.designationCheckValidation),
    catchAsync(designationCheck),
);
route.put(
    '/global-configuration/staff-management/designation/:designationId/:type',
    validate(validator.designationUpdateValidation),
    catchAsync(designationUpdate),
);
route.delete(
    '/global-configuration/staff-management/designation/:settingId/:designationId/:type',
    validate(validator.designationDeleteValidation),
    catchAsync(designationDelete),
);
route.get(
    '/global-configuration/staff-management/designation/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(getDesignationLists),
);
route.get(
    '/global-configuration/staff-management/label-field-configuration/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(getLabelFieldLists),
);
route.get(
    '/global-configuration/staff-management/label-field-configuration/:settingId/:language/:type',
    validate(validator.designationGetValidation),
    catchAsync(getLabelFieldLists),
);
route.put(
    '/global-configuration/staff-management/label-field-configuration/:labelId/:type',
    validate(validator.labelFieldUpdateValidation),
    catchAsync(labelFieldUpdate),
);
route.put(
    '/global-configuration/staff-management/label-field-configuration-chk/:labelId/:type',
    validate(validator.labelFieldCheckValidation),
    catchAsync(labelFieldCheck),
);

route.put(
    '/global-configuration/staff-management/label-field-configuration/vaccination-chk-all/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(labelVaccineFieldCheck),
);
route.put(
    '/global-configuration/staff-management/vaccination-details-chk/:settingId/:vaccineId/:type',
    validate(validator.vaccineDetailsUpdateValidation),
    catchAsync(vaccineDetailsStatusCheck),
);
route.put(
    '/global-configuration/staff-management/label-field-update/:type',
    catchAsync(existingLabelFieldUpdate),
);

route.put(
    '/global-configuration/staff-management/biometric-configuration-chk/:labelId/:type',
    validate(validator.biometricCheckValidation),
    catchAsync(biometricCheck),
);
route.get(
    '/global-configuration/staff-management/biometric-configuration/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(getBiometricList),
);
route.put(
    '/global-configuration/staff-management/mail-configuration/:labelId/:type',
    validate(validator.updateMailContentValidation),
    catchAsync(updateMailContent),
);
route.get(
    '/global-configuration/staff-management/mail-configuration/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(getMailContent),
);
route.put(
    '/global-configuration/staff-management/document-configuration/update-document-format/:type',
    validate(validator.updateDocumentFormatValidation),
    catchAsync(updateDocumentFormatAndSize),
);
route.post(
    '/global-configuration/staff-management/document-configuration/add-document-category/:type',
    validate(validator.addDocumentCategoryValidation),
    catchAsync(addDocumentsCategory),
);
route.put(
    '/global-configuration/staff-management/document-configuration/update-document-category/:settingId/:documentCategoryId/:type',
    validate(validator.editDocumentCategoryValidation),
    catchAsync(updateDocumentsCategory),
);
route.delete(
    '/global-configuration/staff-management/document-configuration/delete-document-category/:settingId/:documentCategoryId/:type',
    validate(validator.deleteDocumentCategoryValidation),
    catchAsync(deleteDocumentsCategory),
);
route.post(
    '/global-configuration/staff-management/document-configuration/add-document/:type',
    validate(validator.addDocumentValidation),
    catchAsync(addDocuments),
);
route.put(
    '/global-configuration/staff-management/document-configuration/edit-document/:settingId/:documentCategoryId/:documentId/:type',
    validate(validator.editDocumentValidation),
    catchAsync(updateDocuments),
);
route.delete(
    '/global-configuration/staff-management/document-configuration/:settingId/:documentCategoryId/:documentId/:type',
    validate(validator.deleteDocumentValidation),
    catchAsync(deleteDocuments),
);
route.put(
    '/global-configuration/staff-management/document-configuration/remainder-mail/:settingId/:type',
    validate(validator.remainderMailValidation),
    catchAsync(updateReminderMail),
);
route.put(
    '/global-configuration/staff-management/mail-settings-configuration/:settingId/:userType',
    validate(validator.mailSettingsValidation),
    catchAsync(updateMailSettings),
);
route.get(
    '/global-configuration/staff-management/document-configuration/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(getDocumentContent),
);
route.get(
    '/global-configuration/staff-management/mail-settings-configuration/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(getMailSettingsContent),
);
route.put(
    '/global-configuration/staff-management/label-field-configuration/reset/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(labelFieldReset),
);
route.put(
    '/global-configuration/staff-management/mail-content/reset/:settingId/:type',
    validate(validator.designationGetValidation),
    catchAsync(mailContentReset),
);
route.put(
    '/global-configuration/staff-management/particular-mail-content/reset/:settingId/:type',
    validate(validator.resetLabelConfiguration),
    catchAsync(particularMailContentReset),
);
route.post(
    '/global-configuration/staff-management/vaccine-configuration/:settingId',
    validate(validator.vaccineCategoryValidation),
    catchAsync(addVaccineCategory),
);
route.put(
    '/global-configuration/staff-management/vaccine-configuration/:settingId/:vaccineId',
    validate(validator.vaccineCategoryEditValidation),
    catchAsync(editVaccineCategory),
);
route.delete(
    '/global-configuration/staff-management/vaccine-configuration/:settingId/:vaccineId',
    catchAsync(deleteVaccineCategory),
);
route.post(
    '/global-configuration/staff-management/vaccine-configuration/vaccine-details/:settingId/:vaccineId',
    validate(validator.vaccineDetailsAddValidation),
    catchAsync(addVaccineDetails),
);
route.put(
    '/global-configuration/staff-management/vaccine-configuration/vaccine-details/:settingId/:vaccineId/:vaccineDetailsId',
    validate(validator.vaccineDetailsEditValidation),
    catchAsync(editVaccineDetails),
);
route.delete(
    '/global-configuration/staff-management/vaccine-configuration/vaccine-details/:settingId/:vaccineId/:vaccineDetailsId',
    validate(validator.vaccineDetailsDeleteValidation),
    catchAsync(deleteVaccineDetails),
);
route.get(
    '/global-configuration/staff-management/vaccine-configuration/:settingId',
    validate(validator.designationGetValidation),
    catchAsync(getVaccination),
);
route.get(
    '/global-configuration/independent-course-input/:id',
    validate(validator.getIndependentCourseInputValidator),
    catchAsync(getIndependentCourseInput),
);
route.patch(
    '/global-configuration/staff-management/toggle-vaccine-configuration/mixed-vaccines',
    validate(validator.vaccineCategoryToggleMixedVaccineValidation),
    catchAsync(toggleAllowMixedVaccination),
);
route.put(
    '/global-configuration/staff-management/label-field-configuration-phone/:settingId/:labelId/:type',
    validate(validator.phoneFieldValidation),
    catchAsync(phoneFieldUpdate),
);
route.put(
    '/global-configuration/privacy-settings/:settingId/:labelId/:type',
    validate(validator.privacyValidation),
    catchAsync(privacyUpdate),
);
route.put(
    '/global-configuration/staff-management/document-configuration/update-document-category-chk/:settingId/:labelId/:type',
    validate(validator.allowCheckBoxValidation),
    catchAsync(allowMultipleUpdate),
);
route.get(
    '/global-configuration/existing-privacy-field-update',
    catchAsync(existingPrivacyFieldUpdate),
);
route.put(
    '/global-configuration/staff-management/document-configuration/notification-mail/:settingId/:type',
    validate(validator.notificationMailValidation),
    catchAsync(updateNotificationMail),
);
route.put(
    '/global-configuration/department-subject/:settingId/:labelId',
    validate(validator.DepartmentSubjectValidation),
    catchAsync(updateDepartmentSubjectMode),
);
route.get(
    '/global-configuration/department-subject/:settingId',
    validate(validator.departmentSubjectGetValidation),
    catchAsync(getDepartmentSubject),
);
route.get('/toggle-without-credithour/:id', catchAsync(toggleWithWithoutCredit));
route.put(
    '/global-configuration/basic-details/department-hierarchy/:labelId/:settingId',
    validate(validator.DepartmentSubjectValidation),
    catchAsync(updateDepartmentHierarchyMode),
);
route.post(
    '/global-configuration/department-management/department-types/:settingId',
    validate(validator.DepartmentTypeValidation),
    catchAsync(addDepartmentType),
);
route.delete(
    '/global-configuration/department-management/department-types/:settingId/:labelId',
    validate(validator.DepartmentTypeDeleteValidation),
    catchAsync(deleteDepartmentType),
);
route.get(
    '/global-configuration/department-management/department-types',
    catchAsync(getDepartmentType),
);
module.exports = route;
