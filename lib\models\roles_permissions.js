let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');


let roles_permissions = new Schema ({
    role_name: {
        type: String,
        required: true
    },
    _role_id: {
        type: Schema.Types.ObjectId,
        ref: constant.ROLES_OFFICES_LIST,
        required: true
    },
    _assigned_by_id: {
        type: Schema.Types.ObjectId,
        ref: constant.ROLES_OFFICES_LIST,
        required: true
    },
    assigned_by: {
        type: String,
        required: true
    },
    _office_id: {
        type: Schema.Types.ObjectId,
        ref: constant.ROLES_OFFICES_LIST,
        required: true
    },
    office_name: {
        type: String,
        required: true
    },
    program_mode: {
        type: String
    },
    _program_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM
    }],
    program_name: {
        type: String
    },
    _department_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT
    },
    department_name: {
        type: String
    },
    _permissions:[{
        type: Schema.Types.ObjectId,
        ref: constant.PERMISSION
    }],
    isActive: {
       type: Boolean,
       default: true
   },
    isDeleted: {
       type: Boolean,
       default: false
   }
},
    { timestamps: true });


module.exports = mongoose.model(constant.ROLES_PERMISSION,roles_permissions);