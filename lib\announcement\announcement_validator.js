const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
exports.listManageAnnouncementValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            user_Id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('USER_ID_MUST_BE_OBJECTID');
                }),
            announcementType: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ANNOUNCEMENT_TYPE_MUST_BE_OBJECTID');
                    }),
            ),
            priorityType: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('PRIORITY_TYPE_MUST_BE_OBJECTID');
                    }),
            ),
            status: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('STATUS_MUST_BE_STRING');
                    }),
            ),
        }).unknown(true),
    });

    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.revokeStatusValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            announcementId: objectId,
        }).unknown(true),
    });

    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.listBoardAnnouncementValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            announcementType: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ANNOUNCEMENT_TYPE_MUST_BE_STRING');
                    }),
            ),
            priorityType: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('PRIORITY_TYPE_MUST_BE_STRING');
                    }),
            ),
            user_Id: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.userFilterTypeValidation = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            user_Id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('USER_ID_MUST_BE_OBJECTID');
                }),
        }).unknown(true),
    });

    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.singleListAnnouncementValidation = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            announcementId: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message.join(','));
        return res.status(400).json(errors);
    }
    next();
};
exports.userViewedNotificationValidation = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            announcementId: Joi.array().items(
                Joi.string()
                    .required()
                    .error(() => {
                        return new Error('ANNOUNCEMENT_ID_MUST_BE_OBJECTID');
                    }),
            ),
            userId: objectId,
        }).unknown(true),
    });

    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message);
        return res.status(400).json(errors);
    }

    next();
};
exports.userViewedNotificationListValidation = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            userId: Joi.string()
                .required()
                .error(() => {
                    return req.t('USER_ID_MUST_BE_OBJECTID');
                }),
            announcementType: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('ANNOUNCEMENT_TYPE_MUST_BE_STRING');
                    }),
            ),
            priorityType: Joi.array().items(
                Joi.string()
                    .optional()
                    .error(() => {
                        return req.t('PRIORITY_TYPE_MUST_BE_STRING');
                    }),
            ),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.receiveNotificationCountValidation = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            userId: Joi.string()
                .required()
                .error(() => {
                    return new Error('USER_ID_MUST_BE_OBJECTID');
                }),
        }).unknown(true),
    });

    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message);
        return res.status(400).json(errors);
    }

    next();
};
