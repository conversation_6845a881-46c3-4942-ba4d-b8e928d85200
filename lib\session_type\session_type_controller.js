const constant = require('../utility/constants');
var session_type = require('mongoose').model(constant.SESSION_TYPE);
var program = require('mongoose').model(constant.PROGRAM);
const institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const session_type_formate = require('./session_type_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.CREDIT_HOURS_CALC, localField: '_credit_calc_id', foreignField: '_id', as: 'credit_calc' } },
        { $unwind: { path: '$credit_calc', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(session_type, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "session_type list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ session_type_formate.session_type(doc.data));
        // common_files.list_all_response(res, 200, true, "session_type list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* session_type_formate.session_type(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.CREDIT_HOURS_CALC, localField: '_credit_calc_id', foreignField: '_id', as: 'credit_calc' } },
        { $unwind: { path: '$credit_calc', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },

    ];
    let doc = await base_control.get_aggregate(session_type, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "session_type details", /* doc.data */session_type_formate.session_type_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "session_type details", doc.data/* session_type_formate.session_type_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let docs = { status: false };
    let program_ids = [];
    let status, datas;
    await req.body.data.forEach(element => {
        if (program_ids.indexOf(element._program_id) == -1) {
            program_ids.push(element._program_id);
        }
    });
    let checks = await base_control.check_id(program, { _id: { $in: program_ids }, 'isDeleted': false });
    if (checks.status) {
        await req.body.data.forEach(async (doc, index) => {
            let objects = {
                session_type: doc.session_type,
                delivery_type: doc.delivery_type,
                delivery_mode: doc.delivery_mode,
                delivery_symbol: doc.delivery_symbol,
                _credit_calc_id: doc._credit_calc_id,
                _program_id: doc._program_id
            };
            if (doc.id == '' && doc.id.length == 0) {
                docs = await base_control.insert(session_type, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            } else {
                docs = await base_control.update(session_type, doc.id, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            }
            if (req.body.data.length == index + 1) {
                if (status) {
                    let aggre = [
                        { $match: { '_program_id': ObjectId(doc._program_id) } },
                        { $match: { 'isDeleted': false } }
                    ];
                    let session_types_data = await base_control.get_aggregate(session_type, aggre);
                    common_files.com_response(res, 201, true, "session type Added successfully", session_types_data);
                } else {
                    common_files.com_response(res, 500, false, "Error", docs.data);
                }
            }
        });
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._program_id != undefined) {
        checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(session_type, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "session_type update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(session_type, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "session_type deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }

        let doc = await base_control.get_list(session_type, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "session_type List", session_type_formate.session_type_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_session_type_division_subject = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }

        let doc = await base_control.get_list(session_type, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "session_type List", session_type_formate.session_type_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_program = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(req.params.id) } },
        { $lookup: { from: constant.CREDIT_HOURS_CALC, localField: '_credit_calc_id', foreignField: '_id', as: 'credit_calc' } },
        { $unwind: { path: '$credit_calc', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { 'credit_calc.per_session': 1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(session_type, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "session_type list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ session_type_formate.session_type(doc.data));
        // common_files.list_all_response(res, 200, true, "session_type list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* session_type_formate.session_type(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};


exports.list_symbol_per_session = async (req, res) => {
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(req.params.id) } },
        { $lookup: { from: constant.CREDIT_HOURS_CALC, localField: '_credit_calc_id', foreignField: '_id', as: 'credit_calc' } },
        { $unwind: { path: '$credit_calc', preserveNullAndEmptyArrays: true } },
        { $sort: { 'credit_calc.per_session': 1 } },
        { $addFields: { contact_hours: { $divide: ['$credit_calc.per_session', 60] } } },
        { $project: { 'delivery_symbol': 1, 'contact_hours': 1 } }
    ];
    let doc = await base_control.get_aggregate(session_type, aggre);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "session_type list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ session_type_formate.session_type(doc.data));
        common_files.com_response(res, 200, true, "session types list", doc.data /* session_type_formate.session_type(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};
exports.get_delivery_types = async (req, res) => {
    try {
        let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
        if (!institution_check.status)
            return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found", 'Institution Not found'));
        let query = { 'isDeleted': false, 'isActive': true };
        let session_type_data = await base_control.distinct_list(session_type, 'delivery_symbol', query);
        if (!session_type_data.status)
            return res.status(404).send(common_files.response_function(res, 404, false, "Session Type Not found", 'Session Type Not found'));
        return res.status(200).send(common_files.response_function(res, 200, true, "Delivery Types", session_type_data.data));
    }
    catch (error) {
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }

};