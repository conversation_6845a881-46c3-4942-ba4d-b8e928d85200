const catchAsync = require('../utility/catch-async');
const {
    getUserProgramList,
    getProgramLevels,
    getCoursesFromProgramLevel,
    getStudentsWithDenialCourses,
    updateDenialPercentage,
    getStudentsWithWarningStats,
    listDenialData,
    denialManagementList,
    checkDenialAccess,
    getStudentsListForStudentsWarningStats,
    resetDenials,
    getCoursesFromProgramLevelRefactored,
    getStudentsWithWarningStatsRefactored,
} = require('./lms_denial_controller');
const { lmsAuthorizationMiddleware } = require('./lms_denial_service');
const {
    getUserProgramListValidator,
    getProgramLevelsValidator,
    getCoursesFromProgramLevelValidator,
    getStudentsStatsValidator,
    listDenialDataValidator,
    updateDenialPercentageValidator,
    denialManagementListValidator,
    studentsListValidator,
    checkDenialAccessValidator,
} = require('./lms_denial_validator');

const router = require('express').Router();
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.get(
    '/get-programs',
    [getUserProgramListValidator, lmsAuthorizationMiddleware],
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getUserProgramList),
);
router.get(
    '/get-program-levels',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getProgramLevelsValidator,
    catchAsync(getProgramLevels),
);
router.get(
    '/get-courses',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCoursesFromProgramLevelValidator,
    catchAsync(getCoursesFromProgramLevelRefactored),
);
router.get(
    '/get-students-denial',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getCoursesFromProgramLevelValidator,
    catchAsync(getStudentsWithDenialCourses),
);
router.get(
    '/get-student-stats',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    getStudentsStatsValidator,
    catchAsync(getStudentsWithWarningStatsRefactored),
);
router.put(
    '/updateDenialPercentage',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    updateDenialPercentageValidator,
    catchAsync(updateDenialPercentage),
);
router.put('/listDenialData', listDenialDataValidator, catchAsync(listDenialData));
router.get(
    '/denialManagementList',
    [denialManagementListValidator, lmsAuthorizationMiddleware],
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(denialManagementList),
);
router.get(
    '/studentsList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    studentsListValidator,
    catchAsync(getStudentsListForStudentsWarningStats),
);
router.get(
    '/check-denial-access',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    checkDenialAccessValidator,
    catchAsync(checkDenialAccess),
);
router.get('/reset-denials', catchAsync(resetDenials));
module.exports = router;
