const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const getPortfolioSchema = Joi.object({
    query: Joi.object({
        programId: objectId.required(),
        courseId: objectId.required(),
        institutionCalendarId: objectId.required(),
    }),
}).unknown(true);

const updatePortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
    }),
    body: Joi.object({
        components: Joi.array().required(),
        totalMarks: Joi.number().required(),
    }),
}).unknown(true);

const deletePortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
    }),
}).unknown(true);

const publishPortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
    }),
    body: Joi.object({
        level: Joi.string().required(),
        term: Joi.string().required(),
        year: Joi.string().required(),
        rotation: Joi.string().optional(),
        rotationCount: Joi.string().optional(),
    }),
}).unknown(true);

const getComponentFormSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required(),
    }),
}).unknown(true);

const updateComponentFormSchema = Joi.object({
    query: Joi.object({
        formId: objectId.required().messages({
            'any.required': 'FORM_ID_REQUIRED',
        }),
    }),
    body: Joi.object({
        title: Joi.string().optional(),
        type: Joi.object().optional(),
        description: Joi.string().optional(),
        pages: Joi.array().min(1).optional().messages({
            'array.min': 'PAGES_REQUIRED',
        }),
    }),
}).unknown(true);

const assignStudentSchema = Joi.object({
    body: Joi.object({
        programId: objectId.required(),
        courseId: objectId.required(),
        institutionCalendarId: objectId.required(),
        portfolioId: objectId.required(),
        componentId: objectId.required(),
        childrenId: objectId.required(),
        students: Joi.array().min(1).required(),
    }),
}).unknown(true);

const getStudentsByCourseSchema = Joi.object({
    query: Joi.object({
        programId: objectId.required(),
        year: Joi.string().required(),
        level: Joi.string().required(),
        rotation: Joi.string().optional(),
        rotationCount: Joi.string().optional(),
        term: Joi.string().required(),
        courseId: objectId.required(),
        institutionCalendarId: objectId.required(),
    }),
}).unknown(true);

const updateStudentReviewSchema = Joi.object({
    query: Joi.object({
        componentId: objectId.required(),
        childrenId: objectId.required(),
        studentId: objectId.required(),
        userId: objectId.required(),
    }),
    body: Joi.object({
        text: Joi.string().required(),
    }),
}).unknown(true);

const getPortfolioForAssignSchema = Joi.object({
    query: Joi.object({
        programId: objectId.required(),
        courseId: objectId.required(),
        institutionCalendarId: objectId.required(),
    }),
}).unknown(true);

module.exports = {
    getPortfolioSchema,
    updatePortfolioSchema,
    deletePortfolioSchema,
    publishPortfolioSchema,
    getComponentFormSchema,
    updateComponentFormSchema,
    assignStudentSchema,
    getStudentsByCourseSchema,
    updateStudentReviewSchema,
    getPortfolioForAssignSchema,
};
