let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let position = new Schema({
    position_title: {
        type: String,
        unique: true
    },
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.POSITION, position);