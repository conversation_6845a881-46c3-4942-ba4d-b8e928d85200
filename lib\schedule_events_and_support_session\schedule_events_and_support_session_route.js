const express = require('express');
const route = express.Router();
const schedule_events_and_support_session = require('./schedule_events_and_support_session_controller');

route.get('/subject_list/:courseId', schedule_events_and_support_session.subjectListByCourseId);
route.get('/staff_list/:courseId', schedule_events_and_support_session.staffListByCourseId);
route.get('/infra_list/:courseId', schedule_events_and_support_session.infraListByCourseId);
route.get(
    '/course_group_list/:programId/:instCalId/:courseId',
    schedule_events_and_support_session.courseGroupListByCourseId,
);

module.exports = route;
