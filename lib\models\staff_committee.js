let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let staff_committee = new Schema({
     committee_name: {
         type: String,
         required: true
     },
     committee_description: {
         type: String,
         required: true
     },
     committee_admin: [{
         type: Schema.Types.ObjectId,
         ref:constant.USER,
         required: true
     }],
     _member_id: [{
        type: Schema.Types.ObjectId,
        ref:constant.USER,
        required: true
     }],
     isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
},
    { timestamps: true });


module.exports = mongoose.model(constant.STAFF_COMMITTEE,staff_committee)