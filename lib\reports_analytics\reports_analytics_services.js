const { response_function, convertToMongoObjectId, clone } = require('../utility/common');
const {
    get_list,
    get,
    get_list_populate,
    dsGetAllWithSortAsJSON,
} = require('../base/base_controller');
const {
    INSTITUTION,
    // PROGRAM_CALENDAR,
    // DIGI_COURSE,
    // USER,
    // ROLE,
    ROLE_ASSIGN,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    USER,
    PART_TIME,
    FULL_TIME,
    PROGRAM_CALENDAR,
    STUDENT_GROUP,
    DIGI_SESSION_ORDER,
    DIGI_SESSION_DELIVERY_TYPES,
    DIGI_DEPARTMENT_SUBJECT,
    DIGI_COURSE,
    <PERSON>ADEM<PERSON>,
    ADMINISTRATION,
    BOTH,
    COURSE_SCHEDULE,
    COMPLETED,
    PRIMARY,
    PRESENT,
    LMS,
    ABSENT,
    LEAVE,
    GENDER: { MALE },
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    ACTIVITIES,
    QUESTION,
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
} = require('../utility/constants');
const { staffSplit, scheduleDateFormateChange } = require('../utility/common_functions');
const { QUIZ } = require('../utility/enums');
const institution = require('mongoose').model(INSTITUTION);
const program_calendar = require('mongoose').model(PROGRAM_CALENDAR);
// const course = require('mongoose').model(DIGI_COURSE);
const user = require('mongoose').model(USER);
const role_assign = require('mongoose').model(ROLE_ASSIGN);
const digi_curriculum = require('mongoose').model(DIGI_CURRICULUM);
const program = require('mongoose').model(DIGI_PROGRAM);
const student_group = require('mongoose').model(STUDENT_GROUP);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const session_delivery_type = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const department_subject = require('mongoose').model(DIGI_DEPARTMENT_SUBJECT);
const course = require('mongoose').model(DIGI_COURSE);
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
// const activity = require('mongoose').model(ACTIVITIES);
// const question = require('mongoose').model(QUESTION);
const activity = require('../models/activities');
const question = require('../models/question');
const { logger } = require('../utility/util_keys');
const { allStudentGroupYesterday } = require('../../service/cache.service');

// Get Activity List based on Course wise
exports.studentGroupList = async (
    institutionCalendarId,
    programId,
    courseId,
    levelNo,
    term,
    rotationCount,
    res,
) => {
    try {
        let studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.isDeleted === false &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === levelNo &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        // const studentGroupData = await get(
        //     student_group,
        //     {
        //         isDeleted: false,
        //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //         'master._program_id': convertToMongoObjectId(programId),
        //         'groups.term': term,
        //         'groups.level': levelNo,
        //         'groups.courses._course_id': convertToMongoObjectId(courseId),
        //     },
        //     {},
        // );
        if (!studentGroupData) studentGroupData = { groups: [] };
        let sgLevel = studentGroupData.groups.find(
            (ele) => ele.level.toString() === levelNo.toString() && ele.term === term,
        );
        if (!sgLevel) sgLevel = { courses: [] };
        const sgCourseData = sgLevel.courses.find((ele) => ele._course_id.toString() === courseId);
        const studentWithGroupList = [];
        let studentWithOutGroupList = [];
        const sgCourseSettingData = sgCourseData
            ? rotationCount && parseInt(rotationCount) !== 0
                ? sgCourseData.setting.filter(
                      (ele) => parseInt(ele._group_no) === parseInt(rotationCount),
                  )
                : sgCourseData.setting
            : [];
        for (courseSetting of sgCourseSettingData) {
            const group_name =
                courseSetting._group_no !== undefined
                    ? courseSetting.gender === BOTH
                        ? 'SG' + courseSetting._group_no
                        : courseSetting.gender === MALE
                        ? 'MG-' + courseSetting._group_no
                        : 'FG-' + courseSetting._group_no
                    : courseSetting.gender === BOTH
                    ? 'SG1'
                    : courseSetting.gender === MALE
                    ? 'MG-1'
                    : 'FG-1';
            // const group_name = courseSetting.gender === MALE ? 'MG-1' : 'FG-1';
            const deliveryGroup =
                courseSetting &&
                courseSetting.session_setting &&
                courseSetting.session_setting.length &&
                courseSetting.session_setting[0].groups
                    ? courseSetting.session_setting[0].groups
                    : [];
            const studentIds = deliveryGroup.map((ele) => ele._student_ids).flat();
            const groupStudentData = sgLevel.students
                .filter((ele) =>
                    studentIds.find((ele2) => ele2.toString() === ele._student_id.toString()),
                )
                .map((ele3) => {
                    return {
                        _student_id: ele3._student_id,
                        name: ele3.name,
                        academic_no: ele3.academic_no,
                        gender: ele3.gender,
                    };
                });
            studentWithGroupList.push({
                _id: courseSetting._id,
                gender: courseSetting.gender,
                group_name,
                students: groupStudentData,
            });
            studentWithOutGroupList = studentWithOutGroupList.concat(groupStudentData);
        }
        return {
            studentWithGroupList,
            studentWithOutGroupList,
        } /* { master_group, sgCourseData } */;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

// Get Activity List based on Course wise
exports.activityQuestionList = async (
    courseId,
    term,
    rotationCount,
    institutionCalendarId,
    levelNo,
) => {
    try {
        const populate = {
            path: 'quizStartedBy',
            select: {
                name: 1,
                user_id: 1,
            },
        };
        const activityQuery = {
            isDeleted: false,
            quizType: QUIZ,
            courseId: convertToMongoObjectId(courseId),
            status: COMPLETED,
        };
        // generate activity report based on level wise
        if (levelNo) {
            activityQuery.level_no = levelNo;
        }
        if (institutionCalendarId)
            activityQuery._institution_calendar_id = convertToMongoObjectId(institutionCalendarId);
        if (term) activityQuery.term = term;
        if (rotationCount && parseInt(rotationCount) !== 0)
            activityQuery.rotation_count = parseInt(rotationCount);
        const activityList = await get_list_populate(activity, activityQuery, {}, populate);
        if (!activityList.status) activityList.data = [];
        const questionIds = activityList.data
            .map((activityElement) =>
                activityElement.questions
                    .map((questionElement) => questionElement._id.toString())
                    .flat(),
            )
            .flat();
        const questionList = await get_list(
            question,
            {
                _id: { $in: questionIds },
                isDeleted: false,
            },
            {},
        );
        if (!questionList.status) questionList.data = [];
        activityList.data = clone(activityList.data);
        const activityScheduleIds = activityList.data
            .map((activityElement) => activityElement.scheduleIds)
            .flat();
        const activityScheduleDatas = await get_list(
            course_schedule,
            {
                _id: { $in: activityScheduleIds },
                isDeleted: false,
            },
            {
                students: 1,
            },
        );
        activityScheduleDatas.data = activityScheduleDatas.status ? activityScheduleDatas.data : [];
        for (activityElement of activityList.data) {
            const questionWithWrongOption = [];
            for (questionElement of activityElement.questions) {
                const questionData = questionList.data.find(
                    (ele) => ele._id.toString() === questionElement._id.toString(),
                );
                if (!questionData) continue;
                const optionData = questionData.options.find((ele2) => ele2.answer);
                const wrongOptionData = questionData.options.find((ele2) => ele2.answer === false);
                if (questionData && optionData) {
                    questionElement.correctOption = optionData._id;
                    questionElement.questionData = questionData;
                }
                if (questionData && ['SAQ', 'MQ'].includes(questionData.questionType)) {
                    questionElement.questionData = questionData;
                }
                questionWithWrongOption.push({
                    _id: convertToMongoObjectId(),
                    _questionId: convertToMongoObjectId(questionElement._id),
                    _optionId: wrongOptionData && convertToMongoObjectId(wrongOptionData._id),
                });
            }
            for (activityStudentElement of activityElement.students) {
                activityStudentElement.studentAnsweredQuestions = clone(
                    activityStudentElement.questions,
                );
                const missingQuestionDatas = questionWithWrongOption.filter(
                    (wrongElement) =>
                        !activityStudentElement.questions.find(
                            (questionElement) =>
                                wrongElement._questionId.toString() ===
                                questionElement._questionId.toString(),
                        ),
                );
                if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                    activityStudentElement.questions = [
                        ...activityStudentElement.questions,
                        ...missingQuestionDatas,
                    ];
                }
            }
            // const scheduleStudents = activityScheduleDatas.data.find((scheduleELement) =>
            //     activityElement.scheduleIds.find(
            //         (activityScheduleId) =>
            //             activityScheduleId.toString() === scheduleELement._id.toString(),
            //     ),
            // );
            // if (scheduleStudents && scheduleStudents.students)
            //     activityElement.scheduleStudents = scheduleStudents.students;
            // else activityElement.scheduleStudents = [];
            activityElement.scheduleStudents = activityScheduleDatas.data
                .filter((scheduleELement) =>
                    activityElement.scheduleIds.find(
                        (activityScheduleId) =>
                            activityScheduleId.toString() === scheduleELement._id.toString(),
                    ),
                )
                .map((studentElement) => studentElement.students)
                .flat();
        }
        return { activityList, questionList };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

exports.activityFormate = async (activityList) => {
    const formattedActivityDatas = activityList.map((activityElement) => {
        return {
            _id: activityElement._id,
            name: activityElement.name,
            startTime: activityElement.startTime,
            endTime: activityElement.endTime,
            quizType: activityElement.quizType,
            questionCount: activityElement.questions.length,
            studentCount: activityElement.scheduleStudents.length,
            noQuizAnswered: activityElement.students.length,
            mark: 0,
            quizStartedBy: activityElement.quizStartedBy,
            // scheduleStudents: activityElement.scheduleStudents,
        };
    });
    return formattedActivityDatas;
};

exports.studentGroupActivityMerge = async (studentGroups) => {
    for (studentList of studentGroups.studentWithGroupList) {
        const studentGroupActivityQuestions = [];
        let total_students = 0;
        for (studentElement of studentList.students) {
            for (activityElement of studentElement.studentActivities) {
                const loc = studentGroupActivityQuestions.findIndex(
                    (ele) => ele._id.toString() === activityElement._id.toString(),
                );
                if (loc !== -1) {
                    studentGroupActivityQuestions[loc].mark += activityElement.mark;
                    studentGroupActivityQuestions[loc].count++;
                } else {
                    studentGroupActivityQuestions.push({
                        _id: activityElement._id,
                        name: activityElement.name,
                        startTime: activityElement.startTime,
                        endTime: activityElement.endTime,
                        quizType: activityElement.quizType,
                        questionCount: activityElement.questionCount,
                        noQuizAnswered: activityElement.noQuizAnswered,
                        mark: activityElement.mark,
                        count: 1,
                    });
                }
            }
            if (studentElement.mark != null) total_students++;
        }
        for (element of studentGroupActivityQuestions) {
            element.mark /= element.count;
        }
        studentList.studentGroupActivityQuestions = clone(studentGroupActivityQuestions);
        studentList.total_students = total_students;
    }
};

exports.activitySGGroupMerge = async (activityList, studentGroups) => {
    const studentGroupActivityDatas = studentGroups
        .map((ele) => ele.studentGroupActivityQuestions)
        .flat();
    for (activityElement of activityList) {
        const groupActivity = studentGroupActivityDatas.filter(
            (ele) => ele._id.toString() === activityElement._id.toString(),
        );
        const count = groupActivity.reduce(function (accumulator, currentValue) {
            return accumulator + currentValue.count;
        }, 0);
        if (count)
            activityElement.mark =
                groupActivity.reduce(function (accumulator, currentValue) {
                    return accumulator + currentValue.mark;
                }, 0) / groupActivity.length /*  / count */;
    }
    //over all group avg
    for (studGroup of studentGroups) {
        let avg = 0;
        let mark = 0;
        for (sgActivityQues of studGroup.studentGroupActivityQuestions) {
            mark += sgActivityQues.mark;
        }
        if (studGroup.studentGroupActivityQuestions.length)
            avg = mark / studGroup.studentGroupActivityQuestions.length;
        studGroup.average = studGroup.studentGroupActivityQuestions.length !== 0 ? avg : null;
    }
};

exports.getQuestionAnswered = ({ questionData, questionElement }) => {
    if (questionData) {
        const { _optionId, _optionIdArray, marks, staffCorrectedAnswer } = questionElement;
        if (!(questionData && questionData.questionData)) {
            return false;
        }
        const { questionType, options, mark, benchMark } = questionData.questionData;
        if (staffCorrectedAnswer && staffCorrectedAnswer !== '') {
            if (staffCorrectedAnswer === 'correct') return true;
            return false;
        }

        switch (questionType) {
            case 'SCQ':
            case 'MCQ':
            case 'TF': {
                const correctOptions = options
                    .filter((optionElement) => optionElement.answer)
                    .map((optionElement) => optionElement._id.toString());
                if (_optionIdArray && _optionIdArray.length) {
                    return _optionIdArray.length === correctOptions.length
                        ? _optionIdArray.every((optionElement) => {
                              return correctOptions.includes(optionElement.toString());
                          })
                        : false;
                }
                if (_optionId) {
                    return correctOptions.includes(_optionId);
                }
                return false;
            }
            case 'SAQ': {
                return marks && mark && benchMark && marks >= benchMark;
            }
            case 'MQ': {
                return marks && mark && mark !== null && marks === mark;
            }
            default:
                return false;
        }
    }
    return false;
};

exports.studentActivityReport = async (studentData, activityList) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            // const scheduleActivityStudentData = activityList.filter((ele) =>
            //     ele.scheduleStudents.find(
            //         (ele2) =>
            //             ele2._id.toString() === studentElement._student_id.toString() &&
            //             ele2.status === ABSENT,
            //     ),
            // );
            // logger.debug(scheduleActivityStudentData);
            // const studentScheduleData = studentActivityElement.find((ele) =>
            //     ele.scheduleStudents.find(
            //         (ele2) => ele2._id.toString() === studentElement._student_id.toString(),
            //     ),
            // );
            let studentMark = 0;
            const studentActivities = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = clone(
                    studentActivityElement.students.find(
                        (ele) =>
                            ele._studentId.toString() === studentElement._student_id.toString(),
                    ),
                );
                //let quizAnsweredCount = 0;
                for (questionElement of studentActivityReports.questions) {
                    const questionData = studentActivityElement.questions.find(
                        (ele) => ele._id.toString() === questionElement._questionId.toString(),
                    );
                    const questionAnswered = this.getQuestionAnswered({
                        questionData,
                        questionElement,
                    });
                    if (questionAnswered) {
                        studentQuestionMarks++;
                    }
                    // Old code
                    // if (questionData && questionElement._optionId) {
                    //     //quizAnsweredCount++;
                    //     if (
                    //         questionData &&
                    //         questionElement._optionId &&
                    //         questionData.correctOption &&
                    //         questionData.correctOption.toString() ===
                    //             questionElement._optionId.toString()
                    //     )
                    //         studentQuestionMarks++;
                    // }
                }

                studentMark +=
                    (studentQuestionMarks / studentActivityReports.questions.length) * 100;
                studentActivities.push({
                    _id: studentActivityElement._id,
                    name: studentActivityElement.name,
                    startTime: studentActivityElement.startTime,
                    endTime: studentActivityElement.endTime,
                    quizType: studentActivityElement.quizType,
                    questionCount: studentActivityReports.questions.length,
                    //noQuizAnswered: quizAnsweredCount,
                    noQuizAnswered: studentActivityReports.studentAnsweredQuestions.length,
                    mark: (studentQuestionMarks / studentActivityReports.questions.length) * 100,
                    isNewActivity: !!(
                        studentActivityElement.correctionType &&
                        studentActivityElement.correctionType !== ''
                    ),
                });
            }
            studentElement.mark =
                studentActivityList.length !== 0 ? studentMark / studentActivityList.length : null;
            studentElement.studentActivities = clone(studentActivities);
            studentElement.absentActivities = [];
            // studentElement.absentActivities = scheduleActivityStudentData.map((ele) =>
            //     ele._id.toString(),
            // );
        }
        // return { studentData };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

exports.studentActivitySLOReport = async (studentData, activityList, courseSloData) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            // const scheduleActivityStudentData = activityList.filter((ele) =>
            //     ele.scheduleStudents.find(
            //         (ele2) =>
            //             ele2._id.toString() === studentElement._student_id.toString() &&
            //             ele2.status === ABSENT,
            //     ),
            // );

            // studentElement.absentActivities = scheduleActivityStudentData.map((ele) =>
            //     ele._id.toString(),
            // );
            let studentMark = 0;
            const studentActivities = [];
            const studentActivitySlos = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSolsData = [];
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    )
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                // const obj = {
                                //     _id: sloData._id,
                                //     no: sloData.no,
                                //     name: sloData.name,
                                //     delivery_symbol: sloData.delivery_symbol,
                                //     mark: 0,
                                //     count: 1,
                                // };
                                const loc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSolsData[loc].count++;
                                else
                                    quizSolsData.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivitySlos[loc2].count++;
                                else
                                    studentActivitySlos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                }
                if (quizSolsData.length) {
                    for (questionElement of studentActivityReports.questions) {
                        const questionData = studentActivityElement.questions.find(
                            (ele) => ele._id.toString() === questionElement._questionId.toString(),
                        );
                        // if (
                        //     questionData &&
                        //     questionElement._optionId &&
                        //     questionData.correctOption &&
                        //     questionData.correctOption.toString() ===
                        //         questionElement._optionId.toString()
                        // ) {
                        const questionAnswered = this.getQuestionAnswered({
                            questionData,
                            questionElement,
                        });
                        if (questionAnswered) {
                            studentQuestionMarks++;
                            for (sloElement of questionData.questionData.sloIds) {
                                const sloLoc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloElement.toString(),
                                );
                                const overAllSloLoc = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloElement.toString(),
                                );
                                if (sloLoc !== -1) {
                                    quizSolsData[sloLoc].mark++;
                                    // quizSolsData[sloLoc].count++;
                                }
                                if (overAllSloLoc !== -1) {
                                    studentActivitySlos[overAllSloLoc].mark++;
                                    // studentActivitySlos[overAllSloLoc].count++;
                                }
                            }
                        }
                    }
                    studentMark +=
                        (studentQuestionMarks / studentActivityElement.questions.length) * 100;
                    for (SloElement of quizSolsData) {
                        SloElement.otherMark = 0;
                        SloElement.otherCount = 0;
                        SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                    }
                    studentActivities.push({
                        _id: studentActivityElement._id,
                        name: studentActivityElement.name,
                        startTime: studentActivityElement.startTime,
                        endTime: studentActivityElement.endTime,
                        quizType: studentActivityElement.quizType,
                        questionCount: studentActivityElement.questions.length,
                        noQuizAnswered: studentActivityElement.questions.length,
                        mark:
                            (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                        solData: clone(quizSolsData),
                    });
                }
            }
            // studentElement.mark =
            //     studentActivityList.length !== 0 ? studentMark / studentActivitySlos.length : null;
            studentElement.studentActivities = clone(studentActivities);
            let studentSloMark = 0;
            for (SloElement of studentActivitySlos) {
                SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                studentSloMark += SloElement.mark;
            }
            studentElement.mark =
                studentActivitySlos.length !== 0
                    ? studentSloMark / studentActivitySlos.length
                    : null;
            studentElement.AllActivitySlos = clone(studentActivitySlos);
        }
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

exports.studentGroupActivitySLOMerge = async (studentGroups) => {
    for (studentList of studentGroups.studentWithGroupList) {
        const studentGroupActivitySLOQuestions = [];
        for (studentElement of studentList.students) {
            for (activityElement of studentElement.AllActivitySlos) {
                const loc = studentGroupActivitySLOQuestions.findIndex(
                    (ele) => ele._id.toString() === activityElement._id.toString(),
                );
                if (loc !== -1) {
                    studentGroupActivitySLOQuestions[loc].mark += activityElement.mark;
                    studentGroupActivitySLOQuestions[loc].count++;
                } else {
                    studentGroupActivitySLOQuestions.push({
                        _id: activityElement._id,
                        no: activityElement.no,
                        name: activityElement.name,
                        delivery_symbol: activityElement.delivery_symbol,
                        mark: activityElement.mark,
                        count: 1,
                    });
                }
            }
        }
        for (element of studentGroupActivitySLOQuestions) {
            element.mark /= element.count;
        }
        studentList.studentGroupActivitySLO = clone(studentGroupActivitySLOQuestions);
    }
};

exports.activitySGGroupSLOMerge = async (studentGroups) => {
    const sloData = [];
    for (groupElement of studentGroups) {
        // for (groupSloElement of groupElement.studentGroupActivitySLO) {
        for (studentElement of groupElement.students) {
            for (groupSloElement of studentElement.AllActivitySlos) {
                const loc = sloData.findIndex(
                    (ele) => ele._id.toString() === groupSloElement._id.toString(),
                );
                if (loc !== -1) {
                    sloData[loc].mark += groupSloElement.mark;
                    sloData[loc].count++;
                } else {
                    sloData.push({
                        _id: groupSloElement._id,
                        no: groupSloElement.no,
                        name: groupSloElement.name,
                        delivery_symbol: groupSloElement.delivery_symbol,
                        lecture: groupSloElement.delivery_symbol + groupSloElement.no,
                        mark: groupSloElement.mark,
                        count: 1,
                    });
                }
            }
        }
    }
    for (element of sloData) {
        element.mark /= element.count;
    }
    return sloData;
};
exports.calculateMarks = async (mappedCloSloToCloData) => {
    try {
        //Student group
        for (eleMappedCloSloToCloData of mappedCloSloToCloData) {
            const studentGroupQuestionsClos = [];
            const hoveringSlos = [];
            for (eleStudents of eleMappedCloSloToCloData.students) {
                eleStudents.AllQuestionsClos = [];
                for (eleCloData of eleStudents.cloData) {
                    // let totalClosQuestions = 0;
                    // let totalAnsweredQuestions = 0;
                    let studentCloMark = 0;
                    for (eleMappedData of eleCloData.mappedData) {
                        const answeredQuestions = eleMappedData.questions.filter(
                            (ele) => ele.answered_status === 1,
                        );
                        const cloMark =
                            (answeredQuestions.length / eleMappedData.questions.length) * 100;
                        eleMappedData.mark = cloMark;
                        studentCloMark += cloMark;
                        hoveringSlos.push({
                            _id: eleMappedData._id,
                            no: eleMappedData.no,
                            mark: eleMappedData.mark,
                        });
                        // totalClosQuestions += eleMappedData.questions.length;
                        // totalAnsweredQuestions += answeredQuestions.length;
                    }
                    if (eleCloData.mappedData.length == 0) continue;
                    const mark = studentCloMark / eleCloData.mappedData.length;
                    // const mark = (totalAnsweredQuestions / totalClosQuestions) * 100;
                    eleStudents.AllQuestionsClos.push({
                        _id: eleCloData._id,
                        no: eleCloData.no,
                        name: eleCloData.name,
                        mark,
                    });
                    const ind = studentGroupQuestionsClos.findIndex(
                        (ele) => ele._id.toString() === eleCloData._id.toString(),
                    );
                    if (ind == -1) {
                        studentGroupQuestionsClos.push({
                            _id: eleCloData._id,
                            no: eleCloData.no,
                            name: eleCloData.name,
                            mark,
                            count: 1,
                        });
                    } else {
                        studentGroupQuestionsClos[ind].mark += mark;
                        studentGroupQuestionsClos[ind].count += 1;
                    }
                    //delete eleCloData.mappedData;
                }
            }
            for (eleStudentGroupQuestionsClos of studentGroupQuestionsClos)
                eleStudentGroupQuestionsClos.mark /= eleStudentGroupQuestionsClos.count;

            eleMappedCloSloToCloData.studentGroupQuestionsClos = clone(studentGroupQuestionsClos);
            eleMappedCloSloToCloData.slos = hoveringSlos;
        }
        return mappedCloSloToCloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const calculateMarksForCLO = (mappedCloSloToCloData) => {
    try {
        //Student group
        for (eleMappedCloSloToCloData of mappedCloSloToCloData) {
            const studentGroupQuestionsClos = [];
            for (eleStudents of eleMappedCloSloToCloData.students) {
                eleStudents.AllQuestionsClos = [];
                for (eleCloData of eleStudents.cloData) {
                    let totalClosQuestions = 0;
                    let totalAnsweredQuestions = 0;
                    for (eleMappedData of eleCloData.mappedData) {
                        const answeredQuestions = eleMappedData.questions.filter(
                            (ele) => ele.answered_status === 1,
                        );
                        eleMappedData.mark =
                            (answeredQuestions.length / eleMappedData.questions.length) * 100;
                        totalClosQuestions += eleMappedData.questions.length;
                        totalAnsweredQuestions += answeredQuestions.length;
                    }
                    if (eleCloData.mappedData.length == 0) continue;
                    const mark = (totalAnsweredQuestions / totalClosQuestions) * 100;
                    eleStudents.AllQuestionsClos.push({
                        _id: eleCloData._id,
                        no: eleCloData.no,
                        name: eleCloData.name,
                        mark,
                    });
                    const ind = studentGroupQuestionsClos.findIndex(
                        (ele) => ele._id.toString() === eleCloData._id.toString(),
                    );
                    if (ind == -1)
                        studentGroupQuestionsClos.push({
                            _id: eleCloData._id,
                            no: eleCloData.no,
                            name: eleCloData.name,
                            mark,
                            count: 1,
                        });
                    else {
                        studentGroupQuestionsClos[ind].mark += mark;
                        studentGroupQuestionsClos[ind].count += 1;
                    }
                }
            }
            for (eleStudentGroupQuestionsClos of studentGroupQuestionsClos)
                eleStudentGroupQuestionsClos.mark /= eleStudentGroupQuestionsClos.count;

            eleMappedCloSloToCloData.studentGroupQuestionsClos = clone(studentGroupQuestionsClos);
        }
        return mappedCloSloToCloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.calculateMarksForPLO = async (mappedCloSloToPloData) => {
    try {
        //Student group
        for (elestudentsGroup of mappedCloSloToPloData) {
            for (eleStudents of elestudentsGroup.students) {
                for (elePloData of eleStudents.ploData) {
                    let ploMark = 0;
                    let ploCount = 0;
                    for (eleCloData of elePloData.mappedData) {
                        let cloMark = 0;
                        let cloCount = 0;
                        for (eleMappedData of eleCloData.mappedData) {
                            const answeredQuestions = eleMappedData.questions.filter(
                                (ele) => ele.answered_status === 1,
                            );
                            eleMappedData.mark =
                                (answeredQuestions.length / eleMappedData.questions.length) * 100;
                            cloMark += eleMappedData.mark;
                            cloCount++;
                        }
                        if (eleCloData.mappedData.length != 0) {
                            eleCloData.mark = cloMark / cloCount;
                            eleCloData.count = cloCount;

                            ploMark += eleCloData.mark;
                            ploCount++;
                        }
                    }
                    if (elePloData.mappedData.length != 0) {
                        elePloData.mark = ploMark / ploCount;
                        elePloData.count = ploCount;
                    }
                }
            }
        }
        return mappedCloSloToPloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.calculateMarksForCLOForStudents = async (mappedCloSloToCloData) => {
    try {
        for (eleStudents of mappedCloSloToCloData) {
            const cloDataBasedActivity = [];
            for (eleStudentActivities of eleStudents.studentActivities) {
                let studentActivitiesMark = 0;
                let studentActivitiesCount = 0;
                for (eleCloData of eleStudentActivities.cloData) {
                    eleCloData.otherMark = 0;
                    eleCloData.otherCount = 0;
                    let cloMark = 0;
                    let cloCount = 0;
                    for (eleMappedData of eleCloData.mappedData) {
                        const answeredQuestions = eleMappedData.questions.filter(
                            (ele) => ele.answered_status === 1,
                        );
                        const cloLoc = cloDataBasedActivity.findIndex(
                            (cloElement) =>
                                cloElement._id.toString() === eleMappedData._id.toString(),
                        );
                        if (cloLoc === -1)
                            cloDataBasedActivity.push({
                                _id: eleMappedData._id,
                                no: eleMappedData.no,
                                name: eleMappedData.name,
                                mark: eleMappedData.mark,
                                count: eleMappedData.count,
                                correctCount: answeredQuestions.length,
                                cloId: [eleCloData._id.toString()],
                            });
                        else {
                            cloDataBasedActivity[cloLoc].count += eleMappedData.count;
                            cloDataBasedActivity[cloLoc].mark += eleMappedData.mark;
                            cloDataBasedActivity[cloLoc].correctCount += answeredQuestions.length;
                            let cloIds = [
                                ...cloDataBasedActivity[cloLoc].cloId,
                                ...[eleCloData._id.toString()],
                            ];
                            cloIds = [...new Set(cloIds)];
                            cloDataBasedActivity[cloLoc].cloId = cloIds;
                        }
                        eleMappedData.correctCount = answeredQuestions.length;
                        eleMappedData.mark =
                            (answeredQuestions.length / eleMappedData.questions.length) * 100;
                        cloMark += eleMappedData.mark;
                        cloCount++;
                        delete eleMappedData.questions;
                    }
                    if (eleCloData.mappedData.length != 0) {
                        eleCloData.mark = cloMark / cloCount;
                        eleCloData.count = cloCount;
                        //studentActivitiesMark += eleCloData.mark;

                        //Nullify!
                        if (studentActivitiesMark == null || Number.isNaN(studentActivitiesMark)) {
                            studentActivitiesMark = eleCloData.mark;
                        } else {
                            studentActivitiesMark +=
                                eleCloData.mark == null || Number.isNaN(eleCloData.mark)
                                    ? 0
                                    : eleCloData.mark;
                        }
                        studentActivitiesCount +=
                            eleCloData.mark == null || Number.isNaN(eleCloData.mark) ? 0 : 1;
                    }
                }
                eleStudentActivities.mark =
                    studentActivitiesMark / eleStudentActivities.cloData.length;

                eleStudentActivities.mark = null;
                if (studentActivitiesCount != 0)
                    eleStudentActivities.mark = studentActivitiesMark / studentActivitiesCount;
            }
            eleStudents.cloDataBasedActivity = cloDataBasedActivity;
        }
        return mappedCloSloToCloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.calculateMarksForPLOForStudents = async (mappedCloSloToPloData) => {
    try {
        for (eleStudents of mappedCloSloToPloData) {
            const ploDataBasedActivity = [];
            for (eleStudentActivities of eleStudents.studentActivities) {
                let studentActivitiesMark = null;
                let studentActivitiesCount = 0;
                for (elePloData of eleStudentActivities.ploData) {
                    elePloData.otherMark = 0;
                    elePloData.otherCount = 0;
                    let ploMark = 0;
                    let ploCount = 0;
                    const cloDataBasedActivity = [];
                    for (eleCloData of elePloData.mappedData) {
                        let cloMark = 0;
                        let cloCount = 0;
                        for (eleMappedData of eleCloData.mappedData) {
                            const answeredQuestions = eleMappedData.questions.filter(
                                (ele) => ele.answered_status === 1,
                            );
                            const cloLoc = cloDataBasedActivity.findIndex(
                                (cloElement) =>
                                    cloElement._id.toString() === eleMappedData._id.toString(),
                            );
                            if (cloLoc === -1)
                                cloDataBasedActivity.push({
                                    _id: eleMappedData._id,
                                    no: eleMappedData.no,
                                    name: eleMappedData.name,
                                    mark: eleMappedData.mark,
                                    count: eleMappedData.count,
                                    correctCount: answeredQuestions.length,
                                    cloId: [eleCloData._id.toString()],
                                    ploIds: [elePloData._id.toString()],
                                });
                            else {
                                cloDataBasedActivity[cloLoc].count += eleMappedData.count;
                                cloDataBasedActivity[cloLoc].mark += eleMappedData.mark;
                                cloDataBasedActivity[cloLoc].correctCount +=
                                    answeredQuestions.length;
                                let cloIds = [
                                    ...cloDataBasedActivity[cloLoc].cloId,
                                    ...[eleCloData._id.toString()],
                                ];
                                cloIds = [...new Set(cloIds)];
                                cloDataBasedActivity[cloLoc].cloId = cloIds;
                                let ploIds = [
                                    ...cloDataBasedActivity[cloLoc].ploIds,
                                    ...[elePloData._id.toString()],
                                ];
                                ploIds = [...new Set(ploIds)];
                                cloDataBasedActivity[cloLoc].ploIds = ploIds;
                            }
                            eleMappedData.mark =
                                (answeredQuestions.length / eleMappedData.questions.length) * 100;
                            cloMark += eleMappedData.mark;
                            cloCount++;
                            delete eleMappedData.questions;
                        }
                        if (eleCloData.mappedData.length != 0) {
                            eleCloData.mark = cloMark / cloCount;
                            eleCloData.count = cloCount;

                            ploMark += eleCloData.mark;
                            ploCount++;
                        }
                    }
                    const ploLoc = ploDataBasedActivity.findIndex((ploElement) =>
                        ploElement.ploIds.find(
                            (ploIdElement) => ploIdElement.toString() === elePloData._id.toString(),
                        ),
                    );
                    if (ploLoc === -1) {
                        let mark = 0;
                        let count = 0;
                        let correctCount = 0;
                        for (cloActivityElement of cloDataBasedActivity) {
                            mark += cloActivityElement.correctCount / cloActivityElement.count;
                            count += cloActivityElement.count;
                            correctCount += cloActivityElement.correctCount;
                        }
                        ploDataBasedActivity.push({
                            ploIds: [elePloData._id.toString()],
                            no: elePloData.no,
                            name: elePloData.name,
                            cloDataBasedActivity,
                            mark,
                            count,
                            correctCount,
                            cloIds: [
                                ...new Set(cloDataBasedActivity.map((ele) => ele.cloId).flat()),
                            ],
                        });
                    } else {
                        const oldCloDataBasedActivity =
                            ploDataBasedActivity[ploLoc].cloDataBasedActivity;
                        for (cloActivityElement of cloDataBasedActivity) {
                            const cloLoc = oldCloDataBasedActivity.findIndex(
                                (cloElement) =>
                                    cloElement._id.toString() === cloActivityElement._id.toString(),
                            );
                            if (cloLoc === -1) oldCloDataBasedActivity.push(cloActivityElement);
                            else {
                                // cloActivityElement.
                                oldCloDataBasedActivity[cloLoc].count += cloActivityElement.count;
                                oldCloDataBasedActivity[cloLoc].mark +=
                                    cloActivityElement.correctCount / cloActivityElement.count;
                                oldCloDataBasedActivity[cloLoc].correctCount +=
                                    cloActivityElement.correctCount;
                                let cloIds = [
                                    ...oldCloDataBasedActivity[cloLoc].cloId,
                                    ...cloActivityElement.cloId,
                                ];
                                cloIds = [...new Set(cloIds)];
                                oldCloDataBasedActivity[cloLoc].cloId = cloIds;
                            }
                        }
                        let mark = 0;
                        let count = 0;
                        let correctCount = 0;
                        for (cloActivityElement of oldCloDataBasedActivity) {
                            mark += cloActivityElement.mark;
                            count += cloActivityElement.count;
                            correctCount += cloActivityElement.correctCount;
                        }
                        ploDataBasedActivity[ploLoc].cloDataBasedActivity = oldCloDataBasedActivity;
                        let ploIds = [
                            ...ploDataBasedActivity[ploLoc].ploIds,
                            ...[elePloData._id.toString()],
                        ];
                        ploIds = [...new Set(ploIds)];
                        ploDataBasedActivity[ploLoc].ploIds = ploIds;
                        ploDataBasedActivity[ploLoc].mark = mark;
                        ploDataBasedActivity[ploLoc].count = count;
                        ploDataBasedActivity[ploLoc].correctCount = correctCount;
                    }

                    if (elePloData.mappedData.length != 0) {
                        elePloData.mark = ploMark / ploCount;
                        elePloData.count = ploCount;

                        //Nullify!
                        if (studentActivitiesMark == null || Number.isNaN(studentActivitiesMark)) {
                            studentActivitiesMark = elePloData.mark;
                        } else {
                            studentActivitiesMark +=
                                elePloData.mark == null || Number.isNaN(elePloData.mark)
                                    ? 0
                                    : elePloData.mark;
                        }
                        studentActivitiesCount +=
                            elePloData.mark == null || Number.isNaN(elePloData.mark) ? 0 : 1;
                    }
                }
                eleStudentActivities.mark = null;
                if (studentActivitiesCount != 0)
                    eleStudentActivities.mark = studentActivitiesMark / studentActivitiesCount;
            }
            eleStudents.ploDataBasedActivity = ploDataBasedActivity;
        }
        return mappedCloSloToPloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getAllActivityCLO = async (studentsData) => {
    try {
        for (eleStudents of studentsData) {
            const allActivityClo = [];
            for (eleStudentActivities of eleStudents.studentActivities) {
                //Unwanted data removes
                delete eleStudentActivities.questions;
                delete eleStudentActivities.studentsAnsweredDetails;
                for (eleCloData of eleStudentActivities.cloData) {
                    const ind = allActivityClo.findIndex(
                        (ele) => ele._id && ele._id.toString() === eleCloData._id.toString(),
                    );
                    if (ind === -1) {
                        allActivityClo.push({
                            _id: eleCloData._id,
                            no: eleCloData.no,
                            name: eleCloData.name,
                            mark: eleCloData.mark,
                            count: eleCloData.mark == null || Number.isNaN(eleCloData.mark) ? 0 : 1,
                            mappedData: clone(eleCloData.mappedData),
                        });
                    } else {
                        if (eleCloData.mark == null || Number.isNaN(eleCloData.mark)) {
                            allActivityClo[ind].mark +=
                                eleCloData.mark == null || Number.isNaN(eleCloData.mark)
                                    ? 0
                                    : eleCloData.mark;
                            allActivityClo[ind].count++;
                        }
                        for (mappedElement of eleCloData.mappedData) {
                            const mappedLoc = allActivityClo[ind].mappedData.findIndex(
                                (sloElement) =>
                                    sloElement._id.toString() === mappedElement._id.toString(),
                            );
                            if (mappedLoc !== -1) {
                                allActivityClo[ind].mappedData[mappedLoc].mark +=
                                    mappedElement.mark !== null ? mappedElement.mark : 0;
                                allActivityClo[ind].mappedData[mappedLoc].count +=
                                    mappedElement.count !== null ? mappedElement.count : 0;
                                allActivityClo[ind].mappedData[mappedLoc].correctCount +=
                                    mappedElement.correctCount !== null
                                        ? mappedElement.correctCount
                                        : 0;
                            } else allActivityClo[ind].mappedData.push(clone(mappedElement));
                        }

                        if (
                            allActivityClo[ind].mark == null ||
                            Number.isNaN(allActivityClo[ind].mark)
                        ) {
                            allActivityClo[ind].mark = eleCloData.mark;
                            allActivityClo[ind].count +=
                                eleCloData.mark == null || Number.isNaN(eleCloData.mark) ? 0 : 1;
                        } else {
                            allActivityClo[ind].mark +=
                                eleCloData.mark == null || Number.isNaN(eleCloData.mark)
                                    ? 0
                                    : eleCloData.mark;
                            allActivityClo[ind].count +=
                                eleCloData.mark == null || Number.isNaN(eleCloData.mark) ? 0 : 1;
                        }
                    }
                }
            }
            let mark = null;
            let count = 0;
            for (eleAllActivityClo of allActivityClo) {
                const cloActivity = eleStudents.cloDataBasedActivity.filter((activityElement) =>
                    activityElement.cloId.find(
                        (cloIdElement) =>
                            cloIdElement.toString() === eleAllActivityClo._id.toString(),
                    ),
                );
                let cloActivityMark = null;
                for (activityElement of cloActivity) {
                    cloActivityMark += activityElement.correctCount / activityElement.count;
                }
                eleAllActivityClo.mark =
                    cloActivityMark !== null ? (cloActivityMark / cloActivity.length) * 100 : null;

                //Nullify!
                if (mark == null || Number.isNaN(mark)) mark = eleAllActivityClo.mark;
                else
                    mark +=
                        eleAllActivityClo.mark == null || Number.isNaN(eleAllActivityClo.mark)
                            ? 0
                            : eleAllActivityClo.mark;

                count +=
                    eleAllActivityClo.mark == null || Number.isNaN(eleAllActivityClo.mark) ? 0 : 1;

                // if (eleAllActivityClo.count > 0) {
                //     eleAllActivityClo.mark /= eleAllActivityClo.count;
                // }
                // mark += eleAllActivityClo.mark;

                for (mappedElement of eleAllActivityClo.mappedData)
                    if (mappedElement.mark !== null) {
                        // mappedElement.mark /= mappedElement.count;
                        mappedElement.mark =
                            (mappedElement.correctCount / mappedElement.count) * 100;
                    }
            }
            eleStudents.AllActivityClos = clone(allActivityClo);
            eleStudents.average = 0;
            if (count != 0) eleStudents.average = mark / count;
        }
        return studentsData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getAllActivityPLO = async (studentsData, studentAllActivityPloReportBasedStaff) => {
    try {
        for (eleStudents of studentsData) {
            const allActivityPlo = [];
            for (eleStudentActivities of eleStudents.studentActivities) {
                //Unwanted data removes
                delete eleStudentActivities.questions;
                delete eleStudentActivities.studentsAnsweredDetails;
                for (elePloData of eleStudentActivities.ploData) {
                    const ind = allActivityPlo.findIndex(
                        (ele) => ele._id && ele._id.toString() === elePloData._id.toString(),
                    );
                    if (ind == -1) {
                        allActivityPlo.push({
                            _id: elePloData._id,
                            no: elePloData.no,
                            name: elePloData.name,
                            mark: elePloData.mark,
                            count: elePloData.mark == null || Number.isNaN(elePloData.mark) ? 0 : 1,
                            mappedData: clone(elePloData.mappedData),
                        });
                    } else {
                        if (
                            allActivityPlo[ind].mark == null ||
                            Number.isNaN(allActivityPlo[ind].mark)
                        ) {
                            allActivityPlo[ind].mark = elePloData.mark;
                            allActivityPlo[ind].count +=
                                elePloData.mark == null || Number.isNaN(elePloData.mark) ? 0 : 1;
                        } else {
                            allActivityPlo[ind].mark +=
                                elePloData.mark == null || Number.isNaN(elePloData.mark)
                                    ? 0
                                    : elePloData.mark;
                            allActivityPlo[ind].count +=
                                elePloData.mark == null || Number.isNaN(elePloData.mark) ? 0 : 1;
                        }

                        for (mappedElement of elePloData.mappedData) {
                            const mappedLoc = allActivityPlo[ind].mappedData.findIndex(
                                (cloElement) =>
                                    cloElement._id.toString() === mappedElement._id.toString(),
                            );
                            if (mappedLoc !== -1) {
                                allActivityPlo[ind].mappedData[mappedLoc].mark +=
                                    mappedElement.mark !== null ? mappedElement.mark : 0;
                                allActivityPlo[ind].mappedData[mappedLoc].count +=
                                    mappedElement.count !== null ? mappedElement.count : 0;
                            } else allActivityPlo[ind].mappedData.push(mappedElement);
                        }
                    }
                }
            }
            let mark = 0;
            let count = 0;
            for (eleAllActivityPlo of allActivityPlo) {
                const ploActivity = eleStudents.ploDataBasedActivity.filter((activityElement) =>
                    activityElement.ploIds.find(
                        (cloIdElement) =>
                            cloIdElement.toString() === eleAllActivityPlo._id.toString(),
                    ),
                );
                let ploActivityMark = null;
                // for (activityElement of cloActivity) {
                //     cloActivityMark += activityElement.mark;
                // }
                // eleAllActivityPlo.mark = cloActivityMark * 100;
                for (activityElement of ploActivity) {
                    let cloActivityMark = null;
                    // let cloActivityCount = null;
                    // for (cloActivityElement of activityElement.cloDataBasedActivity) {
                    //     cloActivityMark +=
                    //         cloActivityElement.correctCount / cloActivityElement.count;
                    // }
                    for (cloIdElement of activityElement.cloIds) {
                        let cloMark = null;
                        const cloBasedData = activityElement.cloDataBasedActivity.filter(
                            (cloElement) =>
                                cloElement.cloId.find(
                                    (cloIdElement2) =>
                                        cloIdElement2.toString() === cloIdElement.toString(),
                                ),
                        );
                        for (cloActivityElement of cloBasedData) {
                            cloMark += cloActivityElement.correctCount / cloActivityElement.count;
                        }
                        // cloActivityMark += cloMark;
                        // cloActivityMark = cloMark / activityElement.cloIds.length;
                        if (cloMark !== null) cloActivityMark += cloMark / cloBasedData.length;
                    }
                    if (cloActivityMark !== null)
                        ploActivityMark += cloActivityMark / activityElement.cloIds.length;
                    // ploActivityMark +=
                    //     cloActivityMark / activityElement.cloDataBasedActivity.length;
                }
                eleAllActivityPlo.mark =
                    ploActivityMark !== null
                        ? (ploActivityMark / ploActivity.length) * 100
                        : eleAllActivityPlo.mark / eleAllActivityPlo.count;
                mark +=
                    eleAllActivityPlo.mark == null || Number.isNaN(eleAllActivityPlo.mark)
                        ? 0
                        : eleAllActivityPlo.mark;
                count +=
                    eleAllActivityPlo.mark == null || Number.isNaN(eleAllActivityPlo.mark) ? 0 : 1;
                // if (eleAllActivityPlo.count > 0) {
                //     eleAllActivityPlo.mark /= eleAllActivityPlo.count;
                // }
                // mark += eleAllActivityPlo.mark;
                // for (mappedElement of eleAllActivityPlo.mappedData)
                //     if (mappedElement.mark !== null) mappedElement.mark /= mappedElement.count;
            }
            eleStudents.AllActivityPlo = clone(studentAllActivityPloReportBasedStaff);
            eleStudents.average = 0;
            if (mark != 0) {
                eleStudents.average = mark / count;
            }
        }
        return studentsData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getGroupPloDataForStudents = async (studentsData) => {
    try {
        const groupPloData = [];
        for (eleStudents of studentsData) {
            for (eleAllActivityPlo of eleStudents.AllActivityPlo) {
                const ind = groupPloData.findIndex(
                    (ele) => ele._id && ele._id.toString() === eleAllActivityPlo._id.toString(),
                );
                if (ind === -1 && eleAllActivityPlo.mark !== null) {
                    groupPloData.push({
                        _id: eleAllActivityPlo._id,
                        no: eleAllActivityPlo.no,
                        name: eleAllActivityPlo.name,
                        mark: eleAllActivityPlo.mark,
                        count: 1,
                    });
                } else if (eleAllActivityPlo.mark !== null) {
                    groupPloData[ind].mark += eleAllActivityPlo.mark;
                    groupPloData[ind].count++;
                }
            }
        }
        for (ele of groupPloData) {
            ele.mark /= ele.count;
        }
        return groupPloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

exports.getAllQuestionPLO = async (mappedCloSloToPloMarksCalculation) => {
    try {
        //Student group
        for (eleStudentGroup of mappedCloSloToPloMarksCalculation) {
            for (eleStudent of eleStudentGroup.students) {
                eleStudent.AllQuestionsPlos = [];
                for (elePloData of eleStudent.ploData) {
                    let mark = null;
                    for (eleMappedData of elePloData.mappedData) {
                        if (eleMappedData.mark >= 0) {
                            mark += eleMappedData.mark;
                        }
                    }
                    if (elePloData.mappedData.length > 0) {
                        elePloData.mark = mark;
                        eleStudent.AllQuestionsPlos.push({
                            _id: elePloData._id,
                            no: elePloData.no,
                            name: elePloData.name,
                            mark: mark === null ? null : elePloData.mark / elePloData.count,
                            count: elePloData.count,
                            mappedData: elePloData.mappedData.map((mappedElement) => {
                                return {
                                    _id: mappedElement._id,
                                    no: mappedElement.no,
                                    name: mappedElement.name,
                                    mark: mappedElement.mark,
                                    count: mappedElement.count,
                                };
                            }),
                        });
                    }
                    //delete elePloData.mappedData;
                }
            }
        }
        return mappedCloSloToPloMarksCalculation;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getStudentGroupQuestionsPLO = async (studentGroupData) => {
    try {
        //Student group
        for (eleStudentGroup of studentGroupData) {
            eleStudentGroup.studentGroupQuestionsPlos = [];
            for (eleStudents of eleStudentGroup.students) {
                const studentPLOData = [];
                for (eleAllQuestionsPlos of eleStudents.AllQuestionsPlos) {
                    const ind = eleStudentGroup.studentGroupQuestionsPlos.findIndex(
                        (ele) => ele._id.toString() === eleAllQuestionsPlos._id.toString(),
                    );
                    if (ind === -1) {
                        if (eleAllQuestionsPlos.mark !== null)
                            eleStudentGroup.studentGroupQuestionsPlos.push({
                                _id: eleAllQuestionsPlos._id,
                                no: eleAllQuestionsPlos.no,
                                name: eleAllQuestionsPlos.name,
                                mark: eleAllQuestionsPlos.mark,
                                count: 1,
                            });
                    } else if (eleAllQuestionsPlos.mark !== null) {
                        eleStudentGroup.studentGroupQuestionsPlos[ind].mark +=
                            eleAllQuestionsPlos.mark;
                        /* eleStudentGroup.studentGroupQuestionsPlos[ind].count +=
                            eleAllQuestionsPlos.count; */
                        eleStudentGroup.studentGroupQuestionsPlos[ind].count++;
                    }
                    const studentPloLoc = studentPLOData.findIndex(
                        (ele) => ele._id.toString() === eleAllQuestionsPlos._id.toString(),
                    );
                    if (studentPloLoc === -1) {
                        studentPLOData.push({
                            _id: eleAllQuestionsPlos._id,
                            no: eleAllQuestionsPlos.no,
                            name: eleAllQuestionsPlos.name,
                            mark: eleAllQuestionsPlos.mark,
                            count: 1,
                        });
                    } else {
                        studentPLOData[studentPloLoc].mark += eleAllQuestionsPlos.mark;
                        studentPLOData[studentPloLoc].count += eleAllQuestionsPlos.count;
                    }
                }
                let studentPlosMark = null;
                let studentPlosCount = 0;
                for (studentPLODataElement of studentPLOData) {
                    if (studentPLODataElement.mark !== null) {
                        studentPLODataElement.mark /= studentPLODataElement.count;
                        studentPlosMark += studentPLODataElement.mark;
                        studentPlosCount += studentPLODataElement.count;
                    }
                }
                eleStudents.mark =
                    /* studentPLOData.length === 0 && */ studentPlosMark === null
                        ? null
                        : studentPlosMark / studentPlosCount;
            }
            for (studentGroupQuestionsPlosElement of eleStudentGroup.studentGroupQuestionsPlos) {
                if (studentGroupQuestionsPlosElement.mark !== null)
                    studentGroupQuestionsPlosElement.mark /= studentGroupQuestionsPlosElement.count;
            }
        }
        return studentGroupData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getGroupPloData = async (studentGroupData) => {
    try {
        const groupPloData = [];
        //Student group
        for (eleStudentGroup of studentGroupData) {
            if (!eleStudentGroup.studentGroupQuestionsPlos) continue;
            for (eleSGPlos of eleStudentGroup.studentGroupQuestionsPlos) {
                const ind = groupPloData.findIndex(
                    (ele) => ele._id.toString() == eleSGPlos._id.toString(),
                );
                if (ind === -1) {
                    groupPloData.push({
                        _id: eleSGPlos._id,
                        no: eleSGPlos.no,
                        name: eleSGPlos.name,
                        mark: eleSGPlos.mark,
                        count: 1,
                    });
                } else {
                    groupPloData[ind].mark += eleSGPlos.mark;
                    //groupPloData[ind].count += eleSGPlos.count;
                    groupPloData[ind].count++;
                }
            }
        }
        for (ele of groupPloData) {
            ele.mark /= ele.count;
        }
        return groupPloData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.mapCloSloToClo = async (studGroupWithMappedCloSlo, courseCloData) => {
    try {
        for (eleStudGroupWithMappedCloSlo of studGroupWithMappedCloSlo) {
            for (eleStudents of eleStudGroupWithMappedCloSlo.students) {
                const mappedCLO = [];
                for (cloElement of courseCloData) {
                    const cloDatas = [
                        ...eleStudents.clos.filter(
                            (ele) => ele._id.toString() === cloElement._id.toString(),
                        ),
                        ...eleStudents.slos.filter((ele2) =>
                            cloElement.sloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    //eleStudents.cloData = clone(cloDatas);
                    mappedCLO.push({
                        _id: cloElement._id,
                        no: cloElement.no,
                        name: cloElement.name,
                        mappedData: cloDatas,
                    });
                }
                eleStudents.cloData = clone(mappedCLO);
                delete eleStudents.clos;
                delete eleStudents.slos;
                delete eleStudents.studentQuestionsAnsweredDetails;
            }
        }
        return studGroupWithMappedCloSlo;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.mapCloSloToCloForStudents = async (studentsWithMappedCloSlo, courseCloData) => {
    try {
        for (eleStudents of studentsWithMappedCloSlo) {
            for (eleStudentActivities of eleStudents.studentActivities) {
                const mappedCLO = [];
                for (cloElement of courseCloData) {
                    const cloDatas = [
                        ...eleStudentActivities.clos.filter(
                            (ele) => ele._id.toString() === cloElement._id.toString(),
                        ),
                        ...eleStudentActivities.slos.filter((ele2) =>
                            cloElement.sloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    //eleStudentActivities.cloData = clone(cloDatas);
                    mappedCLO.push({
                        _id: cloElement._id,
                        no: cloElement.no,
                        name: cloElement.name,
                        mappedData: clone(cloDatas),
                    });
                }
                eleStudentActivities.cloData = clone(mappedCLO);
                delete eleStudentActivities.clos;
                delete eleStudentActivities.slos;
                //delete eleStudentActivities.studentQuestionsAnsweredDetails;
            }
        }

        return studentsWithMappedCloSlo;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.mapCloSloToPlo = async (studGroupWithMappedCloSlo, coursePloData) => {
    try {
        for (eleStudGroupWithMappedCloSlo of studGroupWithMappedCloSlo) {
            for (eleStudents of eleStudGroupWithMappedCloSlo.students) {
                const mappedPLO = [];
                for (ploElement of coursePloData) {
                    const ploDatas = [
                        ...eleStudents.cloData.filter((ele2) =>
                            ploElement.cloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    //eleStudents.cloData = clone(ploDatas);
                    mappedPLO.push({
                        _id: ploElement._id,
                        no: ploElement.no,
                        name: ploElement.name,
                        mappedData: ploDatas,
                    });
                }
                eleStudents.ploData = clone(mappedPLO);
                delete eleStudents.cloData;
                /* delete eleStudents.clos;
                delete eleStudents.slos;
                delete eleStudents.studentQuestionsAnsweredDetails; */
            }
        }
        return studGroupWithMappedCloSlo; //Mapped CLO SLO to PLO
        /////////////////////////////////////////////
        /* for (eleStudGroupWithMappedCloSlo of studGroupWithMappedCloSlo) {
            for (eleStudents of eleStudGroupWithMappedCloSlo.students) {
                const mappedCLO = [];
                for (cloElement of courseCloData) {
                    const cloDatas = [
                        ...eleStudents.clos.filter(
                            (ele) => ele._id.toString() === cloElement._id.toString(),
                        ),
                        ...eleStudents.slos.filter((ele2) =>
                            cloElement.sloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    //eleStudents.cloData = clone(cloDatas);
                    mappedCLO.push({
                        _id: cloElement._id,
                        no: cloElement.no,
                        name: cloElement.name,
                        mappedData: cloDatas,
                    });
                }
                eleStudents.cloData = clone(mappedCLO);
                delete eleStudents.clos;
                delete eleStudents.slos;
                delete eleStudents.studentQuestionsAnsweredDetails;
            }
        }
        return studGroupWithMappedCloSlo; */
        ////////////////////////////////////////////
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.mapCloSloToPloForStudents = async (studentsWithMappedCloSlo, coursePloData) => {
    try {
        for (eleStudents of studentsWithMappedCloSlo) {
            for (eleStudentActivities of eleStudents.studentActivities) {
                const mappedPLO = [];
                for (ploElement of coursePloData) {
                    const ploDatas = [
                        ...eleStudentActivities.cloData.filter((ele2) =>
                            ploElement.cloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    mappedPLO.push({
                        _id: ploElement._id,
                        no: ploElement.no,
                        name: ploElement.name,
                        mappedData: clone(ploDatas),
                    });
                }
                eleStudentActivities.ploData = clone(mappedPLO);
                delete eleStudentActivities.cloData;
            }
        }

        return studentsWithMappedCloSlo; //Mapped CLO SLO to PLO
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getStudentAnswerDetails = ({ studentQuestions, questions }) => {
    if (questions) {
        if (!(questions && questions.questionData)) {
            return false;
        }
        const { questionType, options, _id, mark, benchMark } = questions.questionData;
        switch (questionType) {
            case 'SCQ':
            case 'MCQ':
            case 'TF': {
                const correctOptions = options
                    .filter((optionElement) => optionElement.answer)
                    .map((optionElement) => optionElement._id.toString());
                return studentQuestions
                    .filter(
                        (studentQuestionElement) =>
                            studentQuestionElement._questionId.toString() === _id.toString(),
                    )
                    .find((studentQuestionElement) => {
                        const { _optionIdArray, _optionId, staffCorrectedAnswer } =
                            studentQuestionElement;
                        if (staffCorrectedAnswer && staffCorrectedAnswer !== '') {
                            if (staffCorrectedAnswer === 'correct') return true;
                            return false;
                        }

                        if (_optionIdArray && _optionIdArray.length) {
                            return _optionIdArray.length === correctOptions.length
                                ? _optionIdArray.every((optionElement) => {
                                      return correctOptions.includes(optionElement.toString());
                                  })
                                : false;
                        }
                        if (_optionId) {
                            return correctOptions.includes(_optionId);
                        }
                    });
            }
            case 'SAQ':
            case 'MQ': {
                return studentQuestions
                    .filter(
                        (studentQuestionElement) =>
                            studentQuestionElement._questionId.toString() === _id.toString(),
                    )
                    .find((studentQuestionElement) => {
                        const { marks, staffCorrectedAnswer } = studentQuestionElement;
                        if (staffCorrectedAnswer && staffCorrectedAnswer !== '') {
                            if (staffCorrectedAnswer === 'correct') return true;
                            return false;
                        }

                        return questionType === 'SAQ'
                            ? marks && mark && benchMark && marks >= benchMark
                            : marks && mark && mark !== null && marks === mark;
                    });
            }
            default:
                return undefined;
        }
    }
};

exports.getStudentGroupWithMappedCloSlo = async (
    studentGroups,
    activityList,
    courseCloData,
    courseSloData,
) => {
    try {
        for (eleStudentGroup of studentGroups) {
            for (studentElement of eleStudentGroup.students) {
                let studentActivityList = [];
                studentActivityList = activityList.filter((ele) =>
                    ele.students.find(
                        (ele2) =>
                            ele2._studentId.toString() === studentElement._student_id.toString(),
                    ),
                );
                let questions = [];
                let studentQuestionsAnsweredDetails = [];
                for (eleStudentActivityList of studentActivityList) {
                    questions = [
                        ...questions,
                        ...eleStudentActivityList.questions.map((element) => {
                            return {
                                ...element,
                                activityId: eleStudentActivityList._id,
                            };
                        }),
                    ];
                    studentQuestionsAnsweredDetails = [
                        ...studentQuestionsAnsweredDetails,
                        ...eleStudentActivityList.students.map((element) => {
                            return {
                                ...element,
                                activityId: eleStudentActivityList._id,
                            };
                        }),
                    ];
                }
                //studentElement.questions = clone(questions);
                studentElement.studentQuestionsAnsweredDetails = clone(
                    studentQuestionsAnsweredDetails,
                );
                const slos = [];
                const clos = [];
                for (eleQuestion of questions) {
                    if (eleQuestion.questionData && eleQuestion.questionData.sloIds) {
                        for (sloIdElement of eleQuestion.questionData.sloIds) {
                            //SLO check
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const loc = slos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) slos[loc].count++;
                                else
                                    slos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }

                            //CLO check
                            const cloData = courseCloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (cloData) {
                                const loc = clos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );

                                if (loc !== -1) clos[loc].count++;
                                else
                                    clos.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        delivery_symbol: cloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                    }
                }
                studentElement.clos = clone(clos);
                studentElement.slos = clone(slos);
                for (eleQuestion of questions) {
                    //Check Question Answered correct Or Wrong
                    const studAnsweredData = studentElement.studentQuestionsAnsweredDetails.filter(
                        (ele) =>
                            ele._studentId.toString() === studentElement._student_id.toString() &&
                            eleQuestion.activityId.toString() === ele.activityId.toString(),
                    );
                    let answeredDetails = '';
                    for (eleStudAnsweredData of studAnsweredData) {
                        answeredDetails = getStudentAnswerDetails({
                            studentQuestions: eleStudAnsweredData.questions,
                            questions: eleQuestion,
                        });

                        // answeredDetails = eleStudAnsweredData.questions.find(
                        //     (ele) =>
                        //         ele._optionId &&
                        //         ele._questionId &&
                        //         eleQuestion._id &&
                        //         eleQuestion.correctOption &&
                        //         ele._questionId.toString() === eleQuestion._id.toString() &&
                        //         ele._optionId.toString() === eleQuestion.correctOption.toString(),
                        // );

                        if (answeredDetails) break;
                    }
                    if (eleQuestion.questionData && eleQuestion.questionData.sloIds)
                        for (sloIdElement of eleQuestion.questionData.sloIds) {
                            const closInd = studentElement.clos.findIndex(
                                (eleClo) => eleClo._id.toString() === sloIdElement.toString(),
                            );
                            if (closInd != -1) {
                                if (!studentElement.clos[closInd].questions)
                                    studentElement.clos[closInd].questions = [];

                                studentElement.clos[closInd].questions.push({
                                    _id: eleQuestion._id,
                                    type: eleQuestion.type,
                                    order: eleQuestion.order,
                                    correctOption: eleQuestion.correctOption,
                                    questionData: eleQuestion.questionData,
                                    answered_status: answeredDetails ? 1 : 0,
                                });
                            }

                            //SLO
                            const slosInd = studentElement.slos.findIndex(
                                (eleSlo) => eleSlo._id.toString() === sloIdElement.toString(),
                            );
                            if (slosInd != -1) {
                                if (!studentElement.slos[slosInd].questions)
                                    studentElement.slos[slosInd].questions = [];
                                studentElement.slos[slosInd].questions.push({
                                    _id: eleQuestion._id,
                                    type: eleQuestion.type,
                                    order: eleQuestion.order,
                                    correctOption: eleQuestion.correctOption,
                                    questionData: eleQuestion.questionData,
                                    answered_status: answeredDetails ? 1 : 0,
                                });
                            }
                        }
                }
            }
        }
        return studentGroups;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getStudentsWithMappedCloSloForStudent = async (
    studentData,
    activityList,
    courseCloData,
    courseSloData,
) => {
    try {
        for (studentElement of studentData) {
            let studentActivityList = [];
            studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            studentElement.studentActivities = clone(
                studentActivityList.map((ele) => {
                    return {
                        _id: ele._id,
                        name: ele.name,
                        startTime: ele.startTime,
                        endTime: ele.endTime,
                        quizType: ele.quizType,
                        questions: ele.questions,
                        studentsAnsweredDetails: ele.students,
                    };
                }),
            );
            for (eleStudentActivities of studentElement.studentActivities) {
                const slos = [];
                const clos = [];
                for (eleQuestion of eleStudentActivities.questions) {
                    if (eleQuestion.questionData && eleQuestion.questionData.sloIds) {
                        for (sloIdElement of eleQuestion.questionData.sloIds) {
                            //SLO check
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const loc = slos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) slos[loc].count++;
                                else
                                    slos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }

                            //CLO check
                            const cloData = courseCloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (cloData) {
                                const loc = clos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );

                                if (loc !== -1) clos[loc].count++;
                                else
                                    clos.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        delivery_symbol: cloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                    }
                }
                eleStudentActivities.clos = clone(clos);
                eleStudentActivities.slos = clone(slos);
                for (eleQuestion of eleStudentActivities.questions) {
                    //Check Question Answered correct Or Wrong
                    const studAnsweredData = eleStudentActivities.studentsAnsweredDetails.filter(
                        (ele) =>
                            ele._studentId.toString() === studentElement._student_id.toString(),
                    );
                    let answeredDetails = '';
                    for (eleStudAnsweredData of studAnsweredData) {
                        answeredDetails = getStudentAnswerDetails({
                            studentQuestions: eleStudAnsweredData.questions,
                            questions: eleQuestion,
                        });
                        // answeredDetails = eleStudAnsweredData.questions.find(
                        //     (ele) =>
                        //         ele._optionId &&
                        //         ele._questionId &&
                        //         eleQuestion._id &&
                        //         eleQuestion.correctOption &&
                        //         ele._questionId.toString() === eleQuestion._id.toString() &&
                        //         ele._optionId.toString() === eleQuestion.correctOption.toString(),
                        // );
                        if (answeredDetails) break;
                    }
                    if (eleQuestion.questionData && eleQuestion.questionData.sloIds)
                        for (sloIdElement of eleQuestion.questionData.sloIds) {
                            const closInd = eleStudentActivities.clos.findIndex(
                                (eleClo) => eleClo._id.toString() === sloIdElement.toString(),
                            );
                            if (closInd != -1) {
                                if (!eleStudentActivities.clos[closInd].questions)
                                    eleStudentActivities.clos[closInd].questions = [];

                                eleStudentActivities.clos[closInd].questions.push({
                                    _id: eleQuestion._id,
                                    type: eleQuestion.type,
                                    order: eleQuestion.order,
                                    correctOption: eleQuestion.correctOption,
                                    questionData: eleQuestion.questionData,
                                    answered_status: answeredDetails ? 1 : 0,
                                });
                            }

                            //SLO
                            const slosInd = eleStudentActivities.slos.findIndex(
                                (eleSlo) => eleSlo._id.toString() === sloIdElement.toString(),
                            );
                            if (slosInd != -1) {
                                if (!eleStudentActivities.slos[slosInd].questions)
                                    eleStudentActivities.slos[slosInd].questions = [];
                                eleStudentActivities.slos[slosInd].questions.push({
                                    _id: eleQuestion._id,
                                    type: eleQuestion.type,
                                    order: eleQuestion.order,
                                    correctOption: eleQuestion.correctOption,
                                    questionData: eleQuestion.questionData,
                                    answered_status: answeredDetails ? 1 : 0,
                                });
                            }
                        }
                }
            }
        }
        return studentData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.studentGroupActivityCLOMerge = async (studentGroups) => {
    for (studentList of studentGroups.studentWithGroupList) {
        const studentGroupActivityCLOQuestions = [];
        for (studentElement of studentList.students) {
            for (activityElement of studentElement.AllActivityClos) {
                const loc = studentGroupActivityCLOQuestions.findIndex(
                    (ele) => ele._id.toString() === activityElement._id.toString(),
                );
                if (loc !== -1) {
                    studentGroupActivityCLOQuestions[loc].mark += activityElement.mark;
                    studentGroupActivityCLOQuestions[loc].count++;
                } else {
                    studentGroupActivityCLOQuestions.push({
                        _id: activityElement._id,
                        no: activityElement.no,
                        name: activityElement.name,
                        delivery_symbol: activityElement.delivery_symbol,
                        mark: activityElement.mark,
                        count: 1,
                    });
                }
            }
        }
        for (element of studentGroupActivityCLOQuestions) {
            element.mark /= element.count;
        }
        studentList.studentGroupActivityCLO = clone(studentGroupActivityCLOQuestions);
    }
};

exports.activitySGGroupCLOMerge = async (studentGroups) => {
    const cloData = [];
    for (groupElement of studentGroups) {
        for (groupSloElement of groupElement.studentGroupActivityCLO) {
            const loc = cloData.findIndex(
                (ele) => ele._id.toString() === groupSloElement._id.toString(),
            );
            if (loc !== -1) {
                cloData[loc].mark += groupSloElement.mark;
                cloData[loc].count++;
            } else {
                cloData.push({
                    _id: groupSloElement._id,
                    no: groupSloElement.no,
                    name: groupSloElement.name,
                    delivery_symbol: groupSloElement.delivery_symbol,
                    mark: groupSloElement.mark,
                    count: 1,
                });
            }
        }
    }
    for (element of cloData) {
        element.mark /= element.count;
    }
    return cloData;
};

/* exports.studentActivityPLOReport = async (
    studentData,
    activityList,
    courseCloData,
    courseSloData,
    coursePloData,
) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            let studentMark = 0;
            const studentActivities = [];
            const studentActivitySlos = [];
            const studentActivityClos = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSlosData = [];
                const quizClosData = [];
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    )
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const loc = quizSlosData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSlosData[loc].count++;
                                else
                                    quizSlosData.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivitySlos[loc2].count++;
                                else
                                    studentActivitySlos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                            const cloData = courseCloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (cloData) {
                                const loc = quizClosData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivityClos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizClosData[loc].count++;
                                else
                                    quizClosData.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivityClos[loc2].count++;
                                else
                                    studentActivityClos.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                }
                for (questionElement of studentActivityReports.questions) {
                    const questionData = studentActivityElement.questions.find(
                        (ele) => ele._id.toString() === questionElement._questionId.toString(),
                    );
                    if (
                        questionData &&
                        questionElement._optionId &&
                        questionData.correctOption &&
                        questionData.correctOption.toString() ===
                            questionElement._optionId.toString()
                    ) {
                        studentQuestionMarks++;
                        for (sloElement of questionData.questionData.sloIds) {
                            const sloLoc = quizSlosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllSloLoc = studentActivitySlos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (sloLoc !== -1) {
                                quizSlosData[sloLoc].mark++;
                                // quizSlosData[sloLoc].count++;
                            }
                            if (overAllSloLoc !== -1) {
                                studentActivitySlos[overAllSloLoc].mark++;
                                // studentActivitySlos[overAllSloLoc].count++;
                            }
                            // CLOS
                            const cloLoc = quizClosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllCloLoc = studentActivityClos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (cloLoc !== -1) {
                                quizClosData[cloLoc].mark++;
                                // quizClosData[cloLoc].count++;
                            }
                            if (overAllCloLoc !== -1) {
                                studentActivityClos[overAllCloLoc].mark++;
                                // studentActivityClos[overAllCloLoc].count++;
                            }
                        }
                    }
                }
                studentMark +=
                    (studentQuestionMarks / studentActivityElement.questions.length) * 100;
                for (SloElement of quizSlosData) {
                    SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                }
                for (CloElement of quizClosData) {
                    CloElement.mark = (CloElement.mark / CloElement.count) * 100;
                }
                for (cloElement of courseCloData) {
                    const cloDatas = [
                        ...quizClosData.filter(
                            (ele) => ele._id.toString() === cloElement._id.toString(),
                        ),
                        ...quizSlosData.filter((ele2) =>
                            cloElement.sloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    cloElement.questions = clone(cloDatas);
                }
                // PLO
                const studentActivityPLO = [];
                for (ploElement of coursePloData) {
                    const questions = courseCloData
                        .filter((ele) =>
                            ploElement.cloIds.find(
                                (ele2) => ele2.toString() === ele._id.toString(),
                            ),
                        )
                        .filter((ele) => ele.questions.length !== 0)
                        .map((ele) => ele.questions);
                    studentActivityPLO.push({
                        _id: ploElement._id,
                        no: ploElement.no,
                        name: ploElement.name,
                        questions: questions.flat(),
                    });
                    console.log(
                        '_id: ',
                        ploElement._id + ', no: ',
                        ploElement.no + ', name: ',
                        ploElement.name,
                    );
                    // .map((ele3) => ele3.questions);
                }
                studentActivities.push({
                    _id: studentActivityElement._id,
                    name: studentActivityElement.name,
                    startTime: studentActivityElement.startTime,
                    endTime: studentActivityElement.endTime,
                    quizType: studentActivityElement.quizType,
                    questionCount: studentActivityElement.questions.length,
                    noQuizAnswered: studentActivityElement.questions.length,
                    mark: (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                    ploData: clone(studentActivityPLO),
                });
            }
            studentElement.mark =
                studentActivityList.length !== 0 ? studentMark / studentActivityList.length : null;
            studentElement.studentActivities = studentActivities;
            for (SloElement of studentActivityClos) {
                SloElement.mark = (SloElement.mark / SloElement.count) * 100;
            }
            // studentElement.AllActivityClos = studentActivityClos;
            //studentElement.AllActivityPlo = coursePloData;
            const allActivityPlo = [];
            for (eleStudentActivity of studentActivities) {
                for (elePloData of eleStudentActivity.ploData) {
                    const ind = allActivityPlo.findIndex(
                        (ele) => ele._id && ele._id.toString() === elePloData._id.toString(),
                    );
                    if (ind == -1) {
                        allActivityPlo.push({
                            _id: elePloData._id,
                            no: elePloData.no,
                            name: elePloData.name,
                            mark: 0,
                            questions: elePloData.questions,
                        });
                    } else {
                        allActivityPlo[ind].questions = [
                            ...allActivityPlo[ind].questions,
                            ...elePloData.questions,
                        ];
                    }
                }
            }
            for (eleAllActivityPlo of allActivityPlo) {
                let mark = 0;
                for (eleQuestions of eleAllActivityPlo.questions) {
                    mark += eleQuestions.mark;
                }
                if (eleAllActivityPlo.questions.length != 0)
                    eleAllActivityPlo.mark = mark / eleAllActivityPlo.questions.length;
            }
            studentElement.AllActivityPlo = allActivityPlo;
            // .filter((ele) => ele.questions.length !== 0)
            // .map((ele) => {
            //     return {
            //         _id: ele._id,
            //         no: ele.no,
            //         name: ele.name,
            //         questions: ele.questions,
            //     };
            // });
        }
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
}; */
exports.studentGroupActivityPLOMerge = async (studentGroups) => {
    for (studentList of studentGroups.studentWithGroupList) {
        const studentGroupActivityPLOQuestions = [];
        for (studentElement of studentList.students) {
            for (activityElement of studentElement.AllActivityPlo) {
                const loc = studentGroupActivityPLOQuestions.findIndex(
                    (ele) => ele._id.toString() === activityElement._id.toString(),
                );
                if (loc !== -1) {
                    studentGroupActivityPLOQuestions[loc].mark += activityElement.mark;
                    studentGroupActivityPLOQuestions[loc].count++;
                } else {
                    studentGroupActivityPLOQuestions.push({
                        _id: activityElement._id,
                        no: activityElement.no,
                        name: activityElement.name,
                        delivery_symbol: activityElement.delivery_symbol,
                        mark: activityElement.mark,
                        count: 1,
                    });
                }
            }
        }
        for (element of studentGroupActivityPLOQuestions) {
            element.mark /= element.count;
        }
        studentList.studentGroupActivityPLO = studentGroupActivityPLOQuestions;
    }
};
exports.activitySGGroupPLOMerge = async (studentGroups) => {
    const ploData = [];
    for (groupElement of studentGroups) {
        for (groupPloElement of groupElement.studentGroupActivityPLO) {
            const loc = ploData.findIndex(
                (ele) => ele._id.toString() === groupPloElement._id.toString(),
            );
            if (loc !== -1) {
                ploData[loc].mark += groupPloElement.mark;
                ploData[loc].count++;
            } else {
                ploData.push({
                    _id: groupPloElement._id,
                    no: groupPloElement.no,
                    name: groupPloElement.name,
                    delivery_symbol: groupPloElement.delivery_symbol,
                    mark: groupPloElement.mark,
                    count: 1,
                });
            }
        }
    }
    for (element of ploData) {
        element.mark /= element.count;
    }
    return ploData;
};

//Get Course CLO
exports.getCourseCLO = async (courseId) => {
    try {
        // Course Data
        const { data: courseData, status: course_status } = await get(
            course,
            {
                _id: convertToMongoObjectId(courseId),
                isActive: true,
                isDeleted: false,
            },
            {
                _program_id: 1,
                framework: 1,
            },
        );
        if (!course_status) return { data: [], message: 'Course not found', status: false };

        const cloDomains = [];
        for (courseFramework of courseData.framework.domains) {
            const clos = [];
            for (cloElement of courseFramework.clo) {
                const slos = [];
                if (cloElement.isDeleted === false && cloElement.isActive) {
                    for (sloElement of cloElement.slos) {
                        if (sloElement.mapped_value === 'TRUE') {
                            slos.push({
                                //_id: sloElement._id,
                                _id: sloElement.slo_id,
                                delivery_symbol: sloElement.delivery_symbol,
                                delivery_no: sloElement.delivery_no,
                                no: sloElement.no,
                            });
                        }
                    }
                    clos.push({
                        _id: cloElement._id,
                        no: cloElement.no,
                        name: cloElement.name,
                        slos,
                        sloIds: slos.map((ele) => ele._id.toString()),
                    });
                }
            }
            cloDomains.push({
                _id: courseFramework._id,
                name: courseFramework.name,
                no: courseFramework.no,
                clos,
            });
        }
        // courseData.framework.domains.forEach((eleDOM) => {
        //     eleDOM.clo.forEach((eleCLO) => {
        //         if (eleCLO.isDeleted === false && eleCLO.isActive)
        //             clo.push({
        //                 _id: eleCLO._id,
        //                 no: eleCLO.no,
        //                 name: eleCLO.name,
        //             });
        //     });
        // });
        return { data: cloDomains, message: 'CLO List', status: true };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
//Get Course SLO
exports.getCourseSLO = async ({ courseSessionOrder }) => {
    try {
        const slo = [];
        courseSessionOrder.session_flow_data.forEach((eleSFD) => {
            if (eleSFD.slo && eleSFD.slo.length > 0) {
                eleSFD.slo.forEach((eleSLO) => {
                    slo.push({
                        _id: eleSLO._id,
                        no: eleSLO.no,
                        name: eleSLO.name,
                        delivery_symbol: eleSFD.delivery_symbol,
                        s_no: eleSFD.s_no,
                        _session_id: eleSFD._id,
                    });
                });
            }
        });

        return { data: slo, message: 'SLO List', status: true };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
//Get Course PLO
exports.getCoursePLO = async (cloIds) => {
    try {
        // // Course Data
        // const { data: courseData, status: course_status } = await get(
        //     course,
        //     {
        //         _id: convertToMongoObjectId(courseId),
        //         isActive: true,
        //         isDeleted: false,
        //     },
        //     {
        //         _program_id: 1,
        //         _curriculum_id: 1,
        //     },
        // );
        // if (!course_status) return { data: [], message: 'Course not found', status: false };

        const { data: curriculumData, status: c_status } = await get_list(
            digi_curriculum,
            {
                'framework.domains.plo.clos.clo_id': { $in: cloIds },
                isActive: true,
                isDeleted: false,
            },
            {
                framework: 1,
                curriculum_name: 1,
            },
        );
        if (!c_status) return { data: [], message: 'Curriculum not found', status: false };
        const curriculumPlo = [];
        for (curriculumElement of curriculumData) {
            const domainDatas = [];
            for (domainElement of curriculumElement.framework.domains) {
                const ploDatas = [];
                for (ploElement of domainElement.plo) {
                    if (ploElement.isDeleted === false) {
                        const cloDatas = [];
                        for (cloElement of ploElement.clos) {
                            if (
                                cloIds.find(
                                    (ele) => ele.toString() === cloElement.clo_id.toString(),
                                )
                            )
                                cloDatas.push({
                                    clo_id: cloElement.clo_id,
                                    no: cloElement.no,
                                    name: cloElement.name,
                                });
                        }
                        if (cloDatas.length)
                            ploDatas.push({
                                _id: ploElement._id,
                                no: ploElement.no,
                                name: ploElement.name,
                                clo: cloDatas,
                                cloIds: cloDatas.map((ele) => ele.clo_id.toString()),
                            });
                    }
                }
                // if(ploDatas.length)
                domainDatas.push({
                    _id: domainElement._id,
                    name: domainElement.name,
                    no: domainElement.no,
                    plo: ploDatas,
                });
            }
            curriculumPlo.push({
                curriculum_name: curriculumElement.curriculum_name,
                framework: {
                    name: curriculumElement.framework.name,
                    code: curriculumElement.framework.code,
                    domains: domainDatas,
                },
            });
        }
        // const plo = [];
        // curriculumData.framework.domains.forEach((eleDOM) => {
        //     eleDOM.plo.forEach((elePLO) => {
        //         plo.push({
        //             _id: elePLO._id,
        //             name: elePLO.name,
        //             no: elePLO.no,
        //         });
        //     });
        // });

        return { data: curriculumPlo, message: 'PLO List', status: true };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
//Get Staff Ratings
exports.getStaffRatings = async (subjects, course_schedule_data) => {
    let total_session = 0;
    subjects.forEach((eleSub, indexSub) => {
        course_schedule_data.forEach((eleCS) => {
            const subInd = eleCS.subjects.findIndex(
                (eleCSSub) => eleCSSub._subject_id.toString() === eleSub._subject_id.toString(),
            );
            if (subInd != -1) {
                total_session++;
                //Staff Details
                if (!subjects[indexSub].staffs) {
                    subjects[indexSub].staffs = [];
                    eleCS.staffs.forEach((ele) => {
                        subjects[indexSub].staffs.push({
                            staff_name: ele.staff_name,
                            _staff_id: ele._staff_id,
                            students_ratings: eleCS.students.length > 0 ? eleCS.students : [],
                        });
                    });
                } else {
                    eleCS.staffs.forEach((eleCSSTAFF) => {
                        const ind = subjects[indexSub].staffs.findIndex(
                            (eleSTAFF) =>
                                eleSTAFF._staff_id.toString() === eleCSSTAFF._staff_id.toString(),
                        );
                        if (ind === -1) {
                            subjects[indexSub].staffs.push({
                                staff_name: eleCSSTAFF.staff_name,
                                _staff_id: eleCSSTAFF._staff_id,
                                students_ratings: eleCS.students.length > 0 ? eleCS.students : [],
                            });
                        } else {
                            if (eleCS.students.length > 0) {
                                subjects[indexSub].staffs[ind].students_ratings = [
                                    ...subjects[indexSub].staffs[ind].students_ratings,
                                    ...eleCS.students,
                                ];
                            }
                        }
                    });
                }

                /* //Student wise ratings Ratings
                if (!subjects[indexSub].students) {
                    subjects[indexSub].students = [];
                    eleCS.students.forEach((eleCSSTUD) => {
                        let feedback = [];
                        if (eleCSSTUD.feedback) feedback = eleCSSTUD.feedBack;
                        subjects[indexSub].students.push({
                            name: eleCSSTUD.name,
                            status: eleCSSTUD.status,
                            _id: eleCSSTUD._id,
                            feedback,
                        });
                    });
                } else {
                    eleCS.students.forEach((eleCSSTUD) => {
                        const ind = subjects[indexSub].students.findIndex(
                            (eleSTUD) => eleSTUD._id.toString() === eleCSSTUD._id.toString(),
                        );
                        if (ind === -1) {
                            let feedback = [];
                            if (eleCSSTUD.feedBack.rating) feedback = eleCSSTUD.feedBack;
                            subjects[indexSub].students.push({
                                name: eleCSSTUD.name,
                                status: eleCSSTUD.status,
                                _id: eleCSSTUD._id,
                                feedback,
                            });
                        } else {
                            if (eleCSSTUD.feedBack.rating)
                                subjects[indexSub].students[ind].feedback.push(eleCSSTUD.feedBack);
                        }
                    });
                } */
            }
        });
    });
    let total_feedback = 0;
    subjects.forEach((eleS, indS) => {
        if (eleS.staffs)
            eleS.staffs.forEach((eleSTAFF, indStaff) => {
                ratings = 0;
                count = 0;
                eleSTAFF.students_ratings.forEach((eleSR) => {
                    if (eleSR.feedBack.rating) {
                        total_feedback++;
                        ratings += eleSR.feedBack.rating;
                        count++;
                    }
                });
                if (count != 0) subjects[indS].staffs[indStaff].ratings = ratings / count;
                else subjects[indS].staffs[indStaff].ratings = 0;
                delete subjects[indS].staffs[indStaff].students_ratings;
            });
    });
    return { subjects, total_session, total_feedback };
};
const getPlosByCloIdsPrivate = async (cloIds) => {
    try {
        const query = { 'framework.domains.plo.clos.clo_id': { $in: cloIds } };
        const project = { _id: 1, framework: 1 };
        const curriculums = (await dsGetAllWithSortAsJSON(digi_curriculum, query, project)).data;
        const resultPlos = [];
        curriculums.forEach((curriculum) => {
            curriculum.framework.domains.forEach((domain) => {
                domain.plo.forEach((plo) => {
                    plo.clos.forEach((clo) => {
                        if (
                            cloIds.find((cloId) => cloId.toString() === clo.clo_id.toString()) &&
                            !resultPlos.find(
                                (resPlo) => resPlo._id.toString() === plo._id.toString(),
                            )
                        ) {
                            resultPlos.push(plo);
                        }
                    });
                });
            });
        });
        return resultPlos;
    } catch (error) {
        throw new Error(error);
    }
};
exports.getClosBySloIds = async (sloIds) => {
    try {
        const query = { 'framework.domains.clo.slos.slo_id': { $in: sloIds } };
        const project = { _id: 1, framework: 1 };
        const courses = (await dsGetAllWithSortAsJSON(course, query, project)).data;
        if (!courses.length) return [];
        const new_framework = [];
        for (const course of courses) {
            const { domains } = course.framework;
            const new_domains = [];
            let clo_count = 0;
            for (const domain of domains) {
                const clos = [];
                for (const clo of domain.clo) {
                    for (const slo of clo.slos) {
                        if (
                            sloIds.find((sloId) => sloId.toString() === slo.slo_id.toString()) &&
                            !clos.find((eleClo) => eleClo._id.toString() === clo._id.toString())
                        ) {
                            let slos = [];
                            for (eleSloId of sloIds) {
                                const filteredSloData = clo.slos.filter(
                                    (ele) => ele.slo_id.toString() === eleSloId.toString(),
                                );
                                slos = [...slos, ...filteredSloData];
                            }
                            clo.slos = clone(slos);
                            delete clo.year_id;
                            delete clo.year_name;
                            delete clo.level_id;
                            delete clo.level_name;

                            clos.push(clo);
                        }
                    }
                }
                if (clos.length > 0) {
                    new_domains.push({
                        domain_id: domain._id,
                        domain_name: domain.name,
                        no: domain.no,
                        clo: clone(clos),
                    });
                    clo_count += clos.length;
                }
            }
            if (new_domains.length > 0) {
                new_framework.push({
                    _id: course.framework._id,
                    name: course.framework.name,
                    code: course.framework.code,
                    domain: clone(new_domains),
                    clo_count,
                });
            }
        }

        return new_framework;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
/* exports.getPlosByCloIds = async (cloIds) => {
    try {
        const query = { 'framework.domains.plo.clos.clo_id': { $in: cloIds } };
        const project = {
            _id: 1,
            framework: 1,
            curriculum_name: 1,
            mapping_type: 1,
            content_mapping_type: 1,
        };
        const curriculums = (await dsGetAllWithSortAsJSON(digi_curriculum, query, project)).data;
        const resultPlos = [];
        curriculums.forEach((curriculum) => {
            curriculum.framework.domains.forEach((domain) => {
                domain.plo.forEach((plo) => {
                    for (clo of plo.clos) {
                        //Clo id only one will come from POST
                        if (
                            cloIds.find((cloId) => cloId.toString() === clo.clo_id.toString()) &&
                            !resultPlos.find(
                                (resPlo) =>
                                    resPlo._id && resPlo._id.toString() === plo._id.toString(),
                            )
                        ) {
                            let mapped_value = '';
                            //Mapped Value
                            if (clo.mapped_value && clo.mapped_value === 'H')
                                mapped_value = 'High Impact';
                            if (clo.mapped_value && clo.mapped_value === 'L')
                                mapped_value = 'Low Impact';
                            if (clo.mapped_value && clo.mapped_value === 'M')
                                mapped_value = 'Medium Impact';

                            //Content Mapped Value
                            let content_mapped_value = '';
                            if (clo.content_mapped_value && clo.content_mapped_value === 'I')
                                content_mapped_value = 'Introductory';
                            if (clo.content_mapped_value && clo.content_mapped_value === 'P')
                                content_mapped_value = 'Proficient';
                            if (clo.content_mapped_value && clo.content_mapped_value === 'A')
                                content_mapped_value = 'Advanced';

                            //Add Framework and domain to clo
                            clo.framework_name = curriculum.framework.name;
                            clo.framework_id = curriculum.framework._id;
                            clo.domain_name = domain.name;
                            clo.domain_no = domain.no;

                            resultPlos.push({
                                framework_id: curriculum.framework._id,
                                framework_name: curriculum.framework.name,
                                code: curriculum.framework.code,
                                curriculum_name: curriculum.curriculum_name,
                                curriculum_id: curriculum._id,
                                mapping_type: curriculum.mapping_type,
                                content_mapping_type: curriculum.content_mapping_type,
                                domain_id: domain.domain_id,
                                domain_name: domain.name,
                                domain_no: domain.no,
                                no: plo.no,
                                name: plo.name,

                                mapped_value,
                                content_mapped_value,
                                clos: [clo],
                            });
                        }
                    }
                });
            });
        });
        return resultPlos;
    } catch (error) {
        throw new Error(error);
    }
}; */
exports.getPlosByCloIds = async (cloIds) => {
    try {
        const query = { 'framework.domains.plo.clos.clo_id': { $in: cloIds } };
        const project = {
            _id: 1,
            framework: 1,
            curriculum_name: 1,
            mapping_type: 1,
            content_mapping_type: 1,
        };
        const curriculums = (await dsGetAllWithSortAsJSON(digi_curriculum, query, project)).data;
        const resultPlos = [];
        const new_framework = [];
        curriculums.forEach((curriculum) => {
            const new_domains = [];
            let plo_count = 0;
            curriculum.framework.domains.forEach((domain) => {
                const plos = [];
                domain.plo.forEach((plo) => {
                    const clos = [];
                    let mapped_value = '';
                    let content_mapped_value = '';
                    for (clo of plo.clos) {
                        //Clo id only one will come from POST
                        if (
                            cloIds.find((cloId) => cloId.toString() === clo.clo_id.toString()) &&
                            !resultPlos.find(
                                (resPlo) =>
                                    resPlo._id && resPlo._id.toString() === plo._id.toString(),
                            )
                        ) {
                            //Mapped Value
                            if (clo.mapped_value && clo.mapped_value === 'H')
                                mapped_value = 'High Impact';
                            if (clo.mapped_value && clo.mapped_value === 'L')
                                mapped_value = 'Low Impact';
                            if (clo.mapped_value && clo.mapped_value === 'M')
                                mapped_value = 'Medium Impact';

                            //Content Mapped Value
                            if (clo.content_mapped_value && clo.content_mapped_value === 'I')
                                content_mapped_value = 'Introductory';
                            if (clo.content_mapped_value && clo.content_mapped_value === 'P')
                                content_mapped_value = 'Proficient';
                            if (clo.content_mapped_value && clo.content_mapped_value === 'A')
                                content_mapped_value = 'Advanced';

                            //Add Framework and domain to clo
                            clo.framework_name = curriculum.framework.name;
                            clo.framework_id = curriculum.framework._id;
                            clo.domain_name = domain.name;
                            clo.domain_no = domain.no;
                            delete clo.year_id;
                            delete clo.year_name;
                            delete clo.level_id;
                            delete clo.level_name;
                            clos.push(clo);
                        }
                    }
                    if (clos.length > 0) {
                        plos.push({
                            _id: plo._id,
                            no: plo.no,
                            name: plo.name,
                            mapped_value,
                            content_mapped_value,
                            clo: clos,
                        });
                    }
                });
                if (plos.length > 0) {
                    new_domains.push({
                        _id: domain._id,
                        no: domain.no,
                        name: domain.name,
                        plo: plos,
                    });
                    plo_count += plos.length;
                }
            });
            if (new_domains.length > 0) {
                new_framework.push({
                    _id: curriculum.framework._id,
                    name: curriculum.framework.name,
                    code: curriculum.framework.code,
                    curriculum_name: curriculum.curriculum_name,
                    curriculum_id: curriculum._id,
                    domain: clone(new_domains),
                    plo_count,
                });
            }
        });
        return new_framework;
        //return resultPlos;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.getPlosByCloId = async (cloId) => {
    try {
        const query = { 'framework.domains.plo.clos.clo_id': cloId };
        const project = {
            _id: 1,
            framework: 1,
            curriculum_name: 1,
            mapping_type: 1,
            content_mapping_type: 1,
        };
        const curriculums = (await dsGetAllWithSortAsJSON(digi_curriculum, query, project)).data;
        const resultPlos = [];
        const new_framework = [];
        curriculums.forEach((curriculum) => {
            const new_domains = [];
            let plo_count = 0;
            curriculum.framework.domains.forEach((domain) => {
                const plos = [];
                domain.plo.forEach((plo) => {
                    const clos = [];
                    let mapped_value = '';
                    let content_mapped_value = '';

                    const cloData = plo.clos.find(
                        (eleClo) => eleClo.clo_id.toString() === cloId.toString(),
                    );
                    if (
                        cloData &&
                        !resultPlos.find(
                            (resPlo) => resPlo._id && resPlo._id.toString() === plo._id.toString(),
                        )
                    ) {
                        //Mapped Value
                        if (cloData.mapped_value && cloData.mapped_value === 'H')
                            mapped_value = 'High Impact';
                        if (cloData.mapped_value && cloData.mapped_value === 'L')
                            mapped_value = 'Low Impact';
                        if (cloData.mapped_value && cloData.mapped_value === 'M')
                            mapped_value = 'Medium Impact';

                        //Content Mapped Value
                        if (cloData.content_mapped_value && cloData.content_mapped_value === 'I')
                            content_mapped_value = 'Introductory';
                        if (cloData.content_mapped_value && cloData.content_mapped_value === 'P')
                            content_mapped_value = 'Proficient';
                        if (cloData.content_mapped_value && cloData.content_mapped_value === 'A')
                            content_mapped_value = 'Advanced';

                        //Add Framework and domain to cloData
                        cloData.framework_name = curriculum.framework.name;
                        cloData.framework_id = curriculum.framework._id;
                        cloData.domain_name = domain.name;
                        cloData.domain_no = domain.no;
                        delete cloData.year_id;
                        delete cloData.year_name;
                        delete cloData.level_id;
                        delete cloData.level_name;
                        clos.push(cloData);
                    }
                    if (clos.length > 0) {
                        plos.push({
                            _id: plo._id,
                            no: plo.no,
                            name: plo.name,
                            mapped_value,
                            content_mapped_value,
                            //clo: clos,
                        });
                    }
                });
                if (plos.length > 0) {
                    new_domains.push({
                        _id: domain._id,
                        no: domain.no,
                        name: domain.name,
                        plo: plos,
                    });
                    plo_count += plos.length;
                }
            });
            if (new_domains.length > 0) {
                new_framework.push({
                    _id: curriculum.framework._id,
                    name: curriculum.framework.name,
                    code: curriculum.framework.code,
                    curriculum_name: curriculum.curriculum_name,
                    curriculum_id: curriculum._id,
                    domain: clone(new_domains),
                    plo_count,
                });
            }
        });
        let ploMappedCount = 0;
        new_framework.forEach((ele) => {
            ploMappedCount += ele.plo_count;
        });
        return { ploDatas: new_framework, ploMappedCount };
        //return resultPlos;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const getPlosByCloIdPrivate = async (cloId) => {
    try {
        const query = { 'framework.domains.plo.clos.clo_id': cloId };
        const project = {
            _id: 1,
            framework: 1,
            curriculum_name: 1,
            mapping_type: 1,
            content_mapping_type: 1,
        };
        const curriculums = (await dsGetAllWithSortAsJSON(digi_curriculum, query, project)).data;
        const resultPlos = [];
        const new_framework = [];
        curriculums.forEach((curriculum) => {
            const new_domains = [];
            let plo_count = 0;
            curriculum.framework.domains.forEach((domain) => {
                const plos = [];
                domain.plo.forEach((plo) => {
                    const clos = [];
                    let mapped_value = '';
                    let content_mapped_value = '';

                    const cloData = plo.clos.find(
                        (eleClo) => eleClo.clo_id.toString() === cloId.toString(),
                    );
                    if (
                        cloData &&
                        !resultPlos.find(
                            (resPlo) => resPlo._id && resPlo._id.toString() === plo._id.toString(),
                        )
                    ) {
                        //Mapped Value
                        if (cloData.mapped_value && cloData.mapped_value === 'H')
                            mapped_value = 'High Impact';
                        if (cloData.mapped_value && cloData.mapped_value === 'L')
                            mapped_value = 'Low Impact';
                        if (cloData.mapped_value && cloData.mapped_value === 'M')
                            mapped_value = 'Medium Impact';

                        //Content Mapped Value
                        if (cloData.content_mapped_value && cloData.content_mapped_value === 'I')
                            content_mapped_value = 'Introductory';
                        if (cloData.content_mapped_value && cloData.content_mapped_value === 'P')
                            content_mapped_value = 'Proficient';
                        if (cloData.content_mapped_value && cloData.content_mapped_value === 'A')
                            content_mapped_value = 'Advanced';

                        //Add Framework and domain to cloData
                        cloData.framework_name = curriculum.framework.name;
                        cloData.framework_id = curriculum.framework._id;
                        cloData.domain_name = domain.name;
                        cloData.domain_no = domain.no;
                        delete cloData.year_id;
                        delete cloData.year_name;
                        delete cloData.level_id;
                        delete cloData.level_name;
                        clos.push(cloData);
                    }
                    if (clos.length > 0) {
                        plos.push({
                            _id: plo._id,
                            no: plo.no,
                            name: plo.name,
                            mapped_value,
                            content_mapped_value,
                            //clo: clos,
                        });
                    }
                });
                if (plos.length > 0) {
                    new_domains.push({
                        _id: domain._id,
                        no: domain.no,
                        name: domain.name,
                        plo: plos,
                    });
                    plo_count += plos.length;
                }
            });
            if (new_domains.length > 0) {
                new_framework.push({
                    _id: curriculum.framework._id,
                    name: curriculum.framework.name,
                    code: curriculum.framework.code,
                    curriculum_name: curriculum.curriculum_name,
                    curriculum_id: curriculum._id,
                    domain: clone(new_domains),
                    plo_count,
                });
            }
        });
        let ploMappedCount = 0;
        new_framework.forEach((ele) => {
            ploMappedCount += ele.plo_count;
        });
        return { ploDatas: new_framework, ploMappedCount };
        //return resultPlos;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.studentActivityCLOReport = async (
    studentData,
    activityList,
    courseCloData,
    courseSloData,
    userId,
) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            let studentMark = 0;
            const studentActivities = [];
            const studentActivitySlos = [];
            const studentActivityClos = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSolsData = [];
                const quizClosData = [];
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    ) {
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const loc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSolsData[loc].count++;
                                else
                                    quizSolsData.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivitySlos[loc2].count++;
                                else
                                    studentActivitySlos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                            const cloData = courseCloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (cloData) {
                                const loc = quizClosData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivityClos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizClosData[loc].count++;
                                else
                                    quizClosData.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivityClos[loc2].count++;
                                else
                                    studentActivityClos.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                    }
                }
                for (questionElement of studentActivityReports.questions) {
                    const questionData = studentActivityElement.questions.find(
                        (ele) => ele._id.toString() === questionElement._questionId.toString(),
                    );
                    // if (
                    //     questionData &&
                    //     questionElement._optionId &&
                    //     questionData.correctOption.toString() ===
                    //         questionElement._optionId.toString()
                    // ) {
                    const questionAnswered = this.getQuestionAnswered({
                        questionData,
                        questionElement,
                    });
                    if (questionAnswered) {
                        studentQuestionMarks++;
                        for (sloElement of questionData.questionData.sloIds) {
                            const sloLoc = quizSolsData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllSloLoc = studentActivitySlos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (sloLoc !== -1) quizSolsData[sloLoc].mark++;
                            if (overAllSloLoc !== -1) studentActivitySlos[overAllSloLoc].mark++;
                            // CLOS
                            const cloLoc = quizClosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllCloLoc = studentActivityClos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (cloLoc !== -1) quizClosData[cloLoc].mark++;
                            if (overAllCloLoc !== -1) studentActivityClos[overAllCloLoc].mark++;
                        }
                    }
                }
                studentMark +=
                    (studentQuestionMarks / studentActivityElement.questions.length) * 100;
                for (SloElement of quizSolsData) {
                    SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                }
                for (CloElement of quizClosData) {
                    CloElement.mark = (CloElement.mark / CloElement.count) * 100;
                }
                for (cloElement of courseCloData) {
                    const cloDatas = [
                        ...quizClosData.filter(
                            (ele) => ele._id.toString() === cloElement._id.toString(),
                        ),
                        ...quizSolsData.filter((ele2) =>
                            cloElement.sloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    //Mark
                    let mark = 0;
                    for (eleCloDatas of cloDatas) {
                        mark += eleCloDatas.mark;
                    }
                    cloElement.mark = mark / cloDatas.length;
                    cloElement.questions = clone(cloDatas);
                }

                studentActivities.push({
                    _id: studentActivityElement._id,
                    name: studentActivityElement.name,
                    startTime: studentActivityElement.startTime,
                    endTime: studentActivityElement.endTime,
                    quizType: studentActivityElement.quizType,
                    questionCount: studentActivityElement.questions.length,
                    noQuizAnswered: studentActivityElement.questions.length,
                    mark: (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                    cloData: courseCloData
                        .filter((ele) => ele.questions.length !== 0)
                        .map((ele) => {
                            return {
                                _id: ele._id,
                                no: ele.no,
                                name: ele.name,
                                mark: ele.mark,
                                questions: ele.questions,
                            };
                        }),
                });
            }
            studentElement.mark = studentMark !== 0 ? studentMark / studentActivityList.length : 0;
            studentElement.studentActivities = clone(studentActivities);

            const allActivityClos = [];
            const cloDataBasedActivity = [];
            for (eleStudentActivities of studentActivities) {
                for (eleCloData of eleStudentActivities.cloData) {
                    for (cloElements of eleCloData.questions) {
                        const cloLoc = cloDataBasedActivity.findIndex(
                            (cloElement) =>
                                cloElement._id.toString() === cloElements._id.toString(),
                        );
                        if (cloLoc === -1)
                            cloDataBasedActivity.push({
                                _id: cloElements._id,
                                no: cloElements.no,
                                name: cloElements.name,
                                delivery_symbol: cloElements.delivery_symbol,
                                mark: cloElements.mark,
                                count: cloElements.count,
                                correctCount: cloElements.correctCount,
                                cloId: eleCloData._id,
                            });
                        else {
                            cloDataBasedActivity[cloLoc].count += cloElements.count;
                            cloDataBasedActivity[cloLoc].mark += cloElements.mark;
                            cloDataBasedActivity[cloLoc].correctCount += cloElements.correctCount;
                        }
                    }
                    const ind = allActivityClos.findIndex(
                        (ele) => ele._id.toString() === eleCloData._id.toString(),
                    );
                    if (ind == -1) {
                        allActivityClos.push({
                            _id: eleCloData._id,
                            no: eleCloData.no,
                            name: eleCloData.name,
                            mark: eleCloData.mark,
                            count: 1,
                        });
                    } else {
                        allActivityClos[ind].mark += eleCloData.mark;
                        allActivityClos[ind].count++;
                    }
                }
            }
            for (eleAllActivityClos of allActivityClos) {
                const cloActivity = cloDataBasedActivity.filter(
                    (activityElement) =>
                        activityElement.cloId.toString() === eleAllActivityClos._id.toString(),
                );
                let cloActivityMark = null;
                for (activityElement of cloActivity) {
                    cloActivityMark += activityElement.correctCount / activityElement.count;
                }
                eleAllActivityClos.mark =
                    cloActivityMark !== null ? (cloActivityMark / cloActivity.length) * 100 : null;
                // eleAllActivityClos.mark /= eleAllActivityClos.count;
            }
            studentElement.AllActivityClos = clone(allActivityClos);

            //Average AllActivityClos
            let markAllActivityCLO = 0;
            for (eleAllActivityCLO of studentElement.AllActivityClos) {
                markAllActivityCLO += eleAllActivityCLO.mark;
            }
            let averageAllActivityCLO = 0;
            if (studentElement.AllActivityClos.length > 0)
                averageAllActivityCLO = markAllActivityCLO / studentElement.AllActivityClos.length;
            studentElement.average = averageAllActivityCLO;

            /* const studA = [...clone(studentActivityClos), ...clone(studentActivitySlos)];
            for (SloElement of studA) {
                SloElement.mark = (SloElement.mark / SloElement.count) * 100;
            }
            studentElement.AllActivityClos = clone(studA); */
        }
        //return studentData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.studentGroupActivityCLOMerge = async (studentGroups) => {
    for (eleStudent of studentGroups.studentWithOutGroupList) {
        const studentGroupActivityCLOQuestions = [];
        //for (studentElement of studentList.students) {
        for (activityElement of eleStudent.AllActivityClos) {
            const loc = studentGroupActivityCLOQuestions.findIndex(
                (ele) => ele._id.toString() === activityElement._id.toString(),
            );
            if (loc !== -1) {
                studentGroupActivityCLOQuestions[loc].mark += activityElement.mark;
                studentGroupActivityCLOQuestions[loc].count++;
            } else {
                studentGroupActivityCLOQuestions.push({
                    _id: activityElement._id,
                    no: activityElement.no,
                    name: activityElement.name,
                    delivery_symbol: activityElement.delivery_symbol,
                    mark: activityElement.mark,
                    count: 1,
                });
            }
        }
        //}
        for (element of studentGroupActivityCLOQuestions) {
            element.mark /= element.count;
        }
        eleStudent.studentGroupActivityCLO = studentGroupActivityCLOQuestions;
    }
    //return studentGroups;
};
exports.activitySGGroupCLOMerge = async (studentGroups) => {
    const cloData = [];
    for (groupElement of studentGroups) {
        for (groupSloElement of groupElement.studentGroupActivityCLO) {
            const loc = cloData.findIndex(
                (ele) => ele._id.toString() === groupSloElement._id.toString(),
            );
            if (loc !== -1) {
                cloData[loc].mark += groupSloElement.mark;
                cloData[loc].count++;
            } else {
                cloData.push({
                    _id: groupSloElement._id,
                    no: groupSloElement.no,
                    name: groupSloElement.name,
                    delivery_symbol: groupSloElement.delivery_symbol,
                    mark: groupSloElement.mark,
                    count: 1,
                });
            }
        }
    }
    for (element of cloData) {
        element.mark /= element.count;
    }
    return cloData;
};
exports.getGroupCloData = async (studentData) => {
    const cloData = [];
    for (eleSudentData of studentData) {
        for (eleAllActivityClo of eleSudentData.AllActivityClos) {
            const loc = cloData.findIndex(
                (ele) => ele._id.toString() === eleAllActivityClo._id.toString(),
            );
            if (loc !== -1 && eleAllActivityClo.mark !== null) {
                cloData[loc].mark += eleAllActivityClo.mark;
                cloData[loc].count++;
            } else if (eleAllActivityClo.mark !== null) {
                cloData.push({
                    _id: eleAllActivityClo._id,
                    no: eleAllActivityClo.no,
                    name: eleAllActivityClo.name,
                    //delivery_symbol: eleAllActivityClo.delivery_symbol,
                    mark: eleAllActivityClo.mark,
                    count: 1,
                });
            }
        }
    }
    for (element of cloData) {
        element.mark /= element.count;
    }
    return cloData;
};
exports.getGroupPloData____ = async (studentGroupData) => {
    const ploData = [];
    for (eleStudentGroupData of studentGroupData) {
        for (eleStudents of eleStudentGroupData.students) {
            for (eleAllQuestionsPlos of eleStudents.AllQuestionsPlos) {
                const loc = ploData.findIndex(
                    (ele) => ele._id.toString() === eleAllQuestionsPlos._id.toString(),
                );
                if (loc !== -1) {
                    ploData[loc].mark += eleAllQuestionsPlos.mark;
                    ploData[loc].count++;
                } else {
                    ploData.push({
                        _id: eleAllQuestionsPlos._id,
                        no: eleAllQuestionsPlos.no,
                        name: eleAllQuestionsPlos.name,
                        delivery_symbol: eleAllQuestionsPlos.delivery_symbol,
                        mark: eleAllQuestionsPlos.mark,
                        count: 1,
                    });
                }
            }
        }
    }

    for (element of ploData) {
        element.mark /= element.count;
    }
    return ploData;
};
exports.getGroupPloData = async (studentData) => {
    const ploData = [];
    for (eleAllActivityPlo of studentData) {
        for (eleAllActivityPlo of eleAllActivityPlo.studentGroupQuestionsPlos) {
            const loc = ploData.findIndex(
                (ele) => ele._id.toString() === eleAllActivityPlo._id.toString(),
            );
            if (loc !== -1) {
                ploData[loc].mark += eleAllActivityPlo.mark;
                ploData[loc].count++;
            } else {
                ploData.push({
                    _id: eleAllActivityPlo._id,
                    no: eleAllActivityPlo.no,
                    name: eleAllActivityPlo.name,
                    //delivery_symbol: eleAllActivityPlo.delivery_symbol,
                    mark: eleAllActivityPlo.mark,
                    count: 1,
                });
            }
        }
    }
    for (element of ploData) {
        element.mark /= element.count;
    }
    return ploData;
};
exports.studentActivityPLOReport__ = async (
    studentData,
    activityList,
    courseCloData,
    courseSloData,
    coursePloData,
) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            let studentMark = 0;
            const studentActivities = [];
            const studentActivitySlos = [];
            const studentActivityClos = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSlosData = [];
                const quizClosData = [];
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    ) {
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const loc = quizSlosData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSlosData[loc].count++;
                                else
                                    quizSlosData.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivitySlos[loc2].count++;
                                else
                                    studentActivitySlos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                            const cloData = courseCloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (cloData) {
                                const loc = quizClosData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivityClos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizClosData[loc].count++;
                                else
                                    quizClosData.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivityClos[loc2].count++;
                                else
                                    studentActivityClos.push({
                                        _id: cloData._id,
                                        no: cloData.no,
                                        name: cloData.name,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                    }
                }
                for (questionElement of studentActivityReports.questions) {
                    const questionData = studentActivityElement.questions.find(
                        (ele) => ele._id.toString() === questionElement._questionId.toString(),
                    );
                    // if (
                    //     questionData &&
                    //     questionElement._optionId &&
                    //     questionData.correctOption &&
                    //     questionData.correctOption.toString() ===
                    //         questionElement._optionId.toString()
                    // ) {
                    const questionAnswered = this.getQuestionAnswered({
                        questionData,
                        questionElement,
                    });
                    if (questionAnswered) {
                        studentQuestionMarks++;
                        for (sloElement of questionData.questionData.sloIds) {
                            const sloLoc = quizSlosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllSloLoc = studentActivitySlos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (sloLoc !== -1) quizSlosData[sloLoc].mark++;
                            if (overAllSloLoc !== -1) studentActivitySlos[overAllSloLoc].mark++;
                            // CLOS
                            const cloLoc = quizClosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllCloLoc = studentActivityClos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (cloLoc !== -1) quizClosData[cloLoc].mark++;
                            if (overAllCloLoc !== -1) studentActivityClos[overAllCloLoc].mark++;
                        }
                    }
                }
                studentMark +=
                    (studentQuestionMarks / studentActivityElement.questions.length) * 100;
                for (SloElement of quizSlosData) {
                    SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                }
                for (CloElement of quizClosData) {
                    CloElement.mark = (CloElement.mark / CloElement.count) * 100;
                }
                for (cloElement of courseCloData) {
                    const cloDatas = [
                        ...quizClosData.filter(
                            (ele) => ele._id.toString() === cloElement._id.toString(),
                        ),
                        ...quizSlosData.filter((ele2) =>
                            cloElement.sloIds.find(
                                (ele3) => ele3.toString() === ele2._id.toString(),
                            ),
                        ),
                    ];
                    cloElement.questions = cloDatas;
                }
                // PLO
                const studentActivityPLO = [];
                for (ploElement of coursePloData) {
                    const questions = courseCloData
                        .filter((ele) =>
                            ploElement.cloIds.find(
                                (ele2) => ele2.toString() === ele._id.toString(),
                            ),
                        )
                        .filter((ele) => ele.questions.length !== 0)
                        .map((ele) => ele.questions)
                        .flat();
                    let mark = 0;

                    for (eleQuestions of questions) {
                        mark += eleQuestions.mark;
                    }
                    studentActivityPLO.push({
                        _id: ploElement._id,
                        no: ploElement.no,
                        name: ploElement.name,
                        mark: mark / questions.length,
                        questions: clone(questions),
                    });
                }
                studentActivities.push({
                    _id: studentActivityElement._id,
                    name: studentActivityElement.name,
                    startTime: studentActivityElement.startTime,
                    endTime: studentActivityElement.endTime,
                    quizType: studentActivityElement.quizType,
                    questionCount: studentActivityElement.questions.length,
                    noQuizAnswered: studentActivityElement.questions.length,
                    mark: (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                    ploData: clone(studentActivityPLO),
                });
            }
            studentElement.mark = studentMark !== 0 ? studentMark / studentActivityList.length : 0;
            studentElement.studentActivities = clone(studentActivities);

            const allActivityPlos = [];
            for (eleStudentActivities of studentActivities) {
                for (elePloData of eleStudentActivities.ploData) {
                    const ind = allActivityPlos.findIndex(
                        (ele) => ele._id.toString() === elePloData._id.toString(),
                    );
                    if (ind == -1) {
                        allActivityPlos.push({
                            _id: elePloData._id,
                            no: elePloData.no,
                            name: elePloData.name,
                            mark: elePloData.mark,
                            count: 1,
                        });
                    } else {
                        allActivityPlos[ind].mark += elePloData.mark;
                        allActivityPlos[ind].count++;
                    }
                }
            }
            for (eleAllActivityPlos of allActivityPlos) {
                eleAllActivityPlos.mark /= eleAllActivityPlos.count;
            }
            studentElement.AllActivityPlo = clone(allActivityPlos);

            //Average AllActivityPLO
            let markAllActivityPLO = 0;
            for (eleAllActivityPLO of studentElement.AllActivityPlo) {
                markAllActivityPLO += eleAllActivityPLO.mark;
            }
            let averageAllActivityPLO = 0;
            if (studentElement.AllActivityPlo.length > 0)
                averageAllActivityPLO = markAllActivityPLO / studentElement.AllActivityPlo.length;
            studentElement.average = averageAllActivityPLO;

            /* const studA = [...studentActivityClos, ...studentActivitySlos];
            for (SloElement of studA) {
                SloElement.mark = (SloElement.mark / SloElement.count) * 100;
            }
            // studentElement.AllActivityClos = studentActivityClos;
            studentElement.AllActivityPlo = clone(studA); */
            // .filter((ele) => ele.questions.length !== 0)
            // .map((ele) => {
            //     return {
            //         _id: ele._id,
            //         no: ele.no,
            //         name: ele.name,
            //         questions: ele.questions,
            //     };
            // });
        }
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.studentActivityPLOReportForStudent = async (
    studentData,
    activityList,
    courseCloData,
    courseSloData,
    coursePloData,
) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            let studentMark = 0;
            const studentActivities = [];
            const studentActivitySlos = [];
            const studentActivityClos = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSlosData = [];
                const quizClosData = [];
                for (questionElement of studentActivityReports.questions) {
                    const questionData = studentActivityElement.questions.find(
                        (ele) => ele._id.toString() === questionElement._questionId.toString(),
                    );
                    // if (
                    //     questionData &&
                    //     questionElement._optionId &&
                    //     questionData.correctOption &&
                    //     questionData.correctOption.toString() ===
                    //         questionElement._optionId.toString()
                    // ) {
                    const questionAnswered = this.getQuestionAnswered({
                        questionData,
                        questionElement,
                    });
                    if (questionAnswered) {
                        studentQuestionMarks++;
                        for (sloElement of questionData.questionData.sloIds) {
                            const sloLoc = quizSlosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllSloLoc = studentActivitySlos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (sloLoc !== -1) quizSlosData[sloLoc].mark++;
                            if (overAllSloLoc !== -1) studentActivitySlos[overAllSloLoc].mark++;
                            // CLOS
                            const cloLoc = quizClosData.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            const overAllCloLoc = studentActivityClos.findIndex(
                                (ele) => ele._id.toString() === sloElement.toString(),
                            );
                            if (cloLoc !== -1) quizClosData[cloLoc].mark++;
                            if (overAllCloLoc !== -1) studentActivityClos[overAllCloLoc].mark++;
                        }
                    }
                }
                studentMark +=
                    (studentQuestionMarks / studentActivityElement.questions.length) * 100;

                studentActivities.push({
                    _id: studentActivityElement._id,
                    name: studentActivityElement.name,
                    startTime: studentActivityElement.startTime,
                    endTime: studentActivityElement.endTime,
                    quizType: studentActivityElement.quizType,
                    questionCount: studentActivityElement.questions.length,
                    noQuizAnswered: studentActivityElement.questions.length,
                    mark: (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                    //ploData: clone(studentActivityPLO),
                });
            }
            studentElement.mark = studentMark !== 0 ? studentMark / studentActivityList.length : 0;
            studentElement.studentActivities = clone(studentActivities);
        }
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
exports.studentGroupActivityPLOMerge = async (studentGroups) => {
    for (studentList of studentGroups.studentWithGroupList) {
        const studentGroupActivityPLOQuestions = [];
        for (studentElement of studentList.students) {
            for (activityElement of studentElement.AllActivityPlo) {
                const loc = studentGroupActivityPLOQuestions.findIndex(
                    (ele) => ele._id.toString() === activityElement._id.toString(),
                );
                if (loc !== -1) {
                    studentGroupActivityPLOQuestions[loc].mark += activityElement.mark;
                    studentGroupActivityPLOQuestions[loc].count++;
                } else {
                    studentGroupActivityPLOQuestions.push({
                        _id: activityElement._id,
                        no: activityElement.no,
                        name: activityElement.name,
                        delivery_symbol: activityElement.delivery_symbol,
                        mark: activityElement.mark,
                        count: 1,
                    });
                }
            }
        }
        for (element of studentGroupActivityPLOQuestions) {
            element.mark /= element.count;
        }
        studentList.studentGroupActivityPLO = studentGroupActivityPLOQuestions;
    }
};

exports.studentActivitySessionReport = async ({
    userId,
    studentData,
    activityList,
    courseSloData,
    courseSession,
}) => {
    try {
        // activityList = clone(
        //     activityList.filter((activityElement) =>
        //         activityElement.students.find(
        //             (studentElement) => studentElement._studentId.toString() === userId,
        //         ),
        //     ),
        // );
        const studentActivitySLO = [];
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            const studentActivities = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSolsData = [];
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    )
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const quizSolObject = {
                                    _id: sloData._id,
                                    // no: sloData.no,
                                    // name: sloData.name,
                                    // delivery_symbol: sloData.delivery_symbol,
                                    mark: 0,
                                    count: 1,
                                };
                                const loc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSolsData[loc].count++;
                                else quizSolsData.push(quizSolObject);
                            }
                        }
                }
                if (quizSolsData.length) {
                    for (questionElement of studentActivityReports.questions) {
                        const questionData = studentActivityElement.questions.find(
                            (ele) => ele._id.toString() === questionElement._questionId.toString(),
                        );
                        // if (
                        //     questionData &&
                        //     questionElement._optionId &&
                        //     questionData.correctOption.toString() ===
                        //         questionElement._optionId.toString()
                        // ) {
                        const questionAnswered = this.getQuestionAnswered({
                            questionData,
                            questionElement,
                        });
                        if (questionAnswered) {
                            studentQuestionMarks++;
                            for (sloElement of questionData.questionData.sloIds) {
                                const sloLoc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloElement.toString(),
                                );
                                if (sloLoc !== -1) {
                                    quizSolsData[sloLoc].mark++;
                                }
                            }
                        }
                    }
                    studentActivities.push({
                        _id: studentActivityElement._id,
                        name: studentActivityElement.name,
                        startTime: studentActivityElement.startTime,
                        endTime: studentActivityElement.endTime,
                        quizType: studentActivityElement.quizType,
                        questionCount: studentActivityElement.questions.length,
                        noQuizAnswered: studentActivityElement.questions.length,
                        mark:
                            (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                        solData: clone(quizSolsData),
                    });
                }
            }
            studentActivitySLO.push({
                ...studentElement,
                studentActivities,
            });
        }
        // return studentActivitySLO;
        const userActivityData = studentActivitySLO
            .find((userElement) => userElement._student_id.toString() === userId.toString())
            .studentActivities.map((userElement) => {
                return {
                    _activity_id: userElement._id,
                    name: userElement.name,
                    startTime: userElement.startTime,
                    endTime: userElement.endTime,
                    quizType: userElement.quizType,
                    mark: null,
                    count: null,
                };
            });
        const allActivity = {
            mark: null,
            count: null,
        };
        for (courseSessionElement of courseSession) {
            const studentList = clone(studentActivitySLO);
            let sessionMark = null;
            let sessionCount = null;
            for (studentActivityElement of studentList) {
                const sessionActivity = [];
                for (activityElement of studentActivityElement.studentActivities) {
                    const sessionSloData = [];
                    const isSloMatching = courseSessionElement.sloIds.filter((sloIdElement) =>
                        activityElement.solData.find(
                            (activitySloElement) =>
                                activitySloElement._id.toString() === sloIdElement.toString(),
                        ),
                    );
                    if (isSloMatching.length) {
                        for (sloElement of courseSessionElement.sloIds) {
                            const activitySlo = activityElement.solData.find(
                                (activitySloElement) =>
                                    activitySloElement._id.toString() === sloElement.toString(),
                            );
                            if (activitySlo) {
                                const sessionSloLoc = sessionSloData.findIndex(
                                    (sessionSloElement) =>
                                        sessionSloElement._slo_id.toString() ===
                                        sloElement.toString(),
                                );
                                if (sessionSloLoc == -1)
                                    sessionSloData.push({
                                        _slo_id: sloElement.toString(),
                                        // delivery_symbol: activitySlo.delivery_symbol,
                                        // no: activitySlo.no,
                                        mark: activitySlo.mark,
                                        count: activitySlo.count,
                                    });
                                else {
                                    sessionSloData[sessionSloLoc].mark += activitySlo.mark;
                                    sessionSloData[sessionSloLoc].count += activitySlo.count;
                                }
                            }
                        }
                    }
                    let sessionSloMark = null;
                    for (sessionSloElement of sessionSloData) {
                        const sloMark = (sessionSloElement.mark / sessionSloElement.count) * 100;
                        sessionSloElement.finalMark = sloMark;
                        sessionSloMark += sloMark;
                    }
                    if (sessionSloMark !== null) {
                        sessionSloMark /= sessionSloData.length;
                        sessionMark += sessionSloMark;
                        sessionCount++;
                    }
                    sessionActivity.push({
                        _activity_id: activityElement._id.toString(),
                        sessionSlos: sessionSloData,
                        userMark: sessionSloMark,
                    });
                }
                studentActivityElement.sessionActivity = sessionActivity;
                studentActivityElement.sessionMarkTotal = sessionMark;
                studentActivityElement.sessionMarkCount = sessionCount;
                studentActivityElement.sessionMark =
                    sessionMark !== null ? sessionMark / sessionCount : null;
            }
            const studentActivitiesData = studentList.find(
                (studentElement) => studentElement._student_id.toString() === userId.toString(),
            );
            const otherStudentActivitiesData = clone(studentList);
            for (userSessionActivity of studentActivitiesData.sessionActivity) {
                const userActivityDataLoc = userActivityData.findIndex(
                    (userActivityDataElement) =>
                        userActivityDataElement._activity_id.toString() ===
                        userSessionActivity._activity_id.toString(),
                );
                if (userActivityDataLoc !== -1 && userSessionActivity.userMark !== null) {
                    userActivityData[userActivityDataLoc].mark =
                        userActivityData[userActivityDataLoc].mark !== null
                            ? userActivityData[userActivityDataLoc].mark +
                              userSessionActivity.userMark
                            : userSessionActivity.userMark;
                    userActivityData[userActivityDataLoc].count =
                        userActivityData[userActivityDataLoc].count !== null
                            ? userActivityData[userActivityDataLoc].count + 1
                            : 1;
                }
                let otherStudentMark = null;
                let otherStudentCount = null;
                const otherActivitySlo = [];
                let otherActivitySloMark = null;
                for (otherStudentElement of otherStudentActivitiesData) {
                    const otherStudentActivity = otherStudentElement.sessionActivity.find(
                        (sessionActivityElement) =>
                            sessionActivityElement &&
                            sessionActivityElement._activity_id &&
                            sessionActivityElement._activity_id.toString() ===
                                userSessionActivity._activity_id.toString(),
                    );
                    if (otherStudentActivity && otherStudentElement.sessionMark !== null) {
                        otherStudentMark =
                            otherStudentMark !== null
                                ? otherStudentMark + otherStudentElement.sessionMark
                                : otherStudentElement.sessionMark;
                        otherStudentCount = otherStudentCount !== null ? otherStudentCount + 1 : 1;
                    }
                    for (courseSessionSloElement of courseSessionElement.sloIds) {
                        const userObject = { mark: null, count: null };
                        for (userActivityElement of otherStudentElement.studentActivities) {
                            if (
                                userActivityElement._id.toString() ===
                                userSessionActivity._activity_id.toString()
                            ) {
                                const activitySlo = userActivityElement.solData.find(
                                    (sloElement) =>
                                        sloElement._id.toString() ===
                                        courseSessionSloElement.toString(),
                                );
                                if (activitySlo && activitySlo.mark !== null) {
                                    userObject.mark =
                                        userObject.mark !== null
                                            ? userObject.mark + activitySlo.mark
                                            : activitySlo.mark;
                                    userObject.count =
                                        userObject.count !== null
                                            ? userObject.count + activitySlo.count
                                            : activitySlo.count;
                                }
                            }
                        }
                        if (userObject.mark !== null && userObject.count !== null) {
                            const userSloIndex = otherActivitySlo.findIndex(
                                (userSloElement) =>
                                    userSloElement._slo_id.toString() ===
                                    courseSessionSloElement.toString(),
                            );
                            if (userSloIndex === -1)
                                otherActivitySlo.push({
                                    _slo_id: courseSessionSloElement.toString(),
                                    ...userObject,
                                });
                            else {
                                otherActivitySlo[userSloIndex].mark += userObject.mark;
                                otherActivitySlo[userSloIndex].count += userObject.count;
                            }
                        }
                    }
                }
                for (userSloElement of otherActivitySlo)
                    otherActivitySloMark += (userSloElement.mark / userSloElement.count) * 100;
                userSessionActivity.otherSLOsMark = otherActivitySlo;
                userSessionActivity.otherMark =
                    otherActivitySloMark !== null
                        ? otherActivitySloMark / otherActivitySlo.length
                        : null;
            }
            courseSessionElement.ratings = studentActivitiesData.sessionActivity;

            const userSlo = [];
            // const otherSlo = [];
            let otherStudentMarks = 0;
            let otherStudentCount = 0;
            for (courseSessionSloElement of courseSessionElement.sloIds) {
                for (userActivityElement of studentActivitiesData.studentActivities) {
                    const userObject = { mark: null, count: null };
                    const activitySlo = userActivityElement.solData.find(
                        (sloElement) =>
                            sloElement._id.toString() === courseSessionSloElement.toString(),
                    );
                    if (
                        activitySlo &&
                        activitySlo.mark !== null &&
                        (activitySlo.count !== null || activitySlo.count !== 0)
                    ) {
                        userObject.mark =
                            userObject.mark !== null
                                ? userObject.mark + activitySlo.mark
                                : activitySlo.mark;
                        userObject.count =
                            userObject.count !== null
                                ? userObject.count + activitySlo.count
                                : activitySlo.count;
                        const userSloIndex = userSlo.findIndex(
                            (userSloElement) =>
                                userSloElement._slo_id.toString() ===
                                courseSessionSloElement.toString(),
                        );
                        if (userSloIndex === -1)
                            userSlo.push({
                                _slo_id: courseSessionSloElement.toString(),
                                ...userObject,
                            });
                        else {
                            userSlo[userSloIndex].mark += userObject.mark;
                            // userSlo[userSloIndex].count++;
                            userSlo[userSloIndex].count += userObject.count;
                        }
                    }
                }
                // const userSloIndex = userSlo.findIndex(
                //     (userSloElement) =>
                //         userSloElement._slo_id.toString() === courseSessionSloElement.toString(),
                // );
                // if (activitySlo)
                //     if (userSloIndex === -1)
                //         userSlo.push({
                //             _slo_id: courseSessionSloElement.toString(),
                //             // no: activitySlo.no,
                //             // delivery_symbol: activitySlo.delivery_symbol,
                //             ...userObject,
                //         });
                //     else {
                //         userSlo[userSloIndex].mark += userObject.mark;
                //         userSlo[userSloIndex].count += userObject.count;
                //     }

                // Other Student
                /* const otherStudentActivitiesData = studentList.filter(
                    (studentElement) => studentElement._student_id.toString() !== userId.toString(),
                ); */
                const otherStudentActivitiesData = clone(studentList);
                for (otherStudentElement of otherStudentActivitiesData) {
                    const otherSlo = [];
                    for (userActivityElement of otherStudentElement.studentActivities) {
                        const activitySlo = userActivityElement.solData.find(
                            (sloElement) =>
                                sloElement._id.toString() === courseSessionSloElement.toString(),
                        );
                        if (activitySlo && activitySlo.mark !== null) {
                            const userSloIndex = otherSlo.findIndex(
                                (userSloElement) =>
                                    userSloElement._slo_id.toString() ===
                                    courseSessionSloElement.toString(),
                            );
                            if (userSloIndex === -1)
                                otherSlo.push({
                                    _slo_id: courseSessionSloElement.toString(),
                                    mark: activitySlo.mark,
                                    count: activitySlo.count,
                                });
                            else {
                                otherSlo[userSloIndex].mark += activitySlo.mark;
                                otherSlo[userSloIndex].count += activitySlo.count;
                            }
                        }
                    }
                    if (otherSlo.length) {
                        for (userSloElement of otherSlo)
                            otherStudentMarks += (userSloElement.mark / userSloElement.count) * 100;
                        otherStudentCount++;
                    }
                }
            }
            let userSessionAllMark = null;
            // let OtherUserSessionAllMark = null;
            for (userSloElement of userSlo) {
                const mark = (userSloElement.mark / userSloElement.count) * 100;
                userSessionAllMark += mark;
                userSloElement.mark = mark;
            }
            courseSessionElement.userSessionAllActivity =
                userSessionAllMark !== null ? userSessionAllMark / userSlo.length : null;
            if (courseSessionElement.userSessionAllActivity !== null) {
                allActivity.mark =
                    allActivity.mark !== null
                        ? allActivity.mark + courseSessionElement.userSessionAllActivity
                        : courseSessionElement.userSessionAllActivity;
                allActivity.count = allActivity.count !== null ? allActivity.count + 1 : 1;
            }
            // for (userSloElement of otherSlo)
            //     OtherUserSessionAllMark += (userSloElement.mark / userSloElement.count) * 100;
            // courseSessionElement.otherSessionAllActivity =
            //     OtherUserSessionAllMark !== null ? OtherUserSessionAllMark / otherSlo.length : null;
            courseSessionElement.otherSessionAllActivity =
                otherStudentCount !== 0 ? otherStudentMarks / otherStudentCount : null;
            courseSessionElement.userSessionAllActivityData = clone(userSlo);
            // courseSessionElement.otherSessionAllActivityData = clone(otherSlo);
            delete courseSessionElement.sloIds;
        }
        for (userActivityDataElement of userActivityData)
            userActivityDataElement.mark /= userActivityDataElement.count;
        const allActivityMark = allActivity.mark / allActivity.count;
        return { courseSession, userActivityData, allActivityMark };
    } catch (error) {
        logger.error(
            error,
            'Report Analytics --> Service --> studentActivitySessionReport --> Student Activity Session Service',
        );
        throw new Error(error);
    }
};

exports.getClosBySloId = async (sloId, courseId) => {
    try {
        const project = { _id: 1, framework: 1 };
        const query = {
            _id: convertToMongoObjectId(courseId),
            'framework.domains.clo.slos.slo_id': sloId,
        };
        const course_data = await get(course, query, project);
        if (!course_data.status) return [];
        const new_framework = [];
        const { domains } = course_data.data.framework;
        const new_domains = [];
        let clo_count = 0;
        for (const domain of domains) {
            const clos = [];
            for (clo of domain.clo) {
                const slos = [];
                sloData = clo.slos.find(
                    (eleSlos) => eleSlos.slo_id.toString() === sloId.toString(),
                );
                if (
                    sloData &&
                    !clos.find((eleClo) => eleClo._id.toString() === clo._id.toString())
                ) {
                    slos.push(sloData);
                }
                if (slos.length > 0) {
                    //clo.slos = clone(slos);
                    //clos.push(clo);
                    const ploMappedDetails = await getPlosByCloIdPrivate(clo._id);
                    clos.push({
                        _id: clo._id,
                        no: clo.no,
                        name: clo.name,
                        isDeleted: clo.isDeleted,
                        isActive: clo.isActive,
                        plo_mapped_count: ploMappedDetails.ploMappedCount
                            ? ploMappedDetails.ploMappedCount
                            : 0,
                    });
                }
            }
            if (clos.length > 0) {
                new_domains.push({
                    domain_id: domain._id,
                    domain_name: domain.name,
                    no: domain.no,
                    clo: clone(clos),
                });
                clo_count += clos.length;
            }
        }
        let cloMappedCount = 0;
        if (new_domains.length > 0) {
            cloMappedCount += clo_count;
            new_framework.push({
                _id: course_data.data.framework._id,
                name: course_data.data.framework.name,
                code: course_data.data.framework.code,
                domain: clone(new_domains),
                clo_count,
            });
        }

        return { cloDatas: new_framework, cloMappedCount };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

exports.simplifyDeliveryGroupName = (name = '', delimiter = '') => {
    const parts = name.split('-');
    const delivery = parts[parts.length - 2];
    const index = parts[parts.length - 1];
    return `${delivery}${delimiter}${index}`;
};

// Student Group with Delivery Service
exports.studentGroupWithDeliveryService = async ({
    _institution_id,
    institutionCalendarId,
    programId,
    courseId,
    levelNo,
    term,
    rotationCount,
}) => {
    try {
        const studentGroupData = await student_group
            .findOne(
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'master._program_id': convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    'groups.courses._course_id': convertToMongoObjectId(courseId),
                    'groups.level': levelNo,
                },
                {
                    master: 1,
                    'groups.group_name': 1,
                    'groups.group_mode': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.curriculum': 1,
                    'groups.group_setting.gender': 1,
                    'groups.group_setting.groups._id': 1,
                    'groups.group_setting.groups.group_no': 1,
                    'groups.group_setting.groups.group_name': 1,
                    'groups.group_setting.groups.delivery_type': 1,
                    'groups.group_setting.groups.session_type': 1,
                    'groups.rotation_group_setting._id': 1,
                    'groups.rotation_group_setting.gender': 1,
                    'groups.rotation_group_setting.group_no': 1,
                    'groups.rotation_group_setting.group_name': 1,
                    'groups.rotation_group_setting.delivery_type': 1,
                    'groups.rotation_group_setting.session_type': 1,
                    'groups.courses._course_id': 1,
                    'groups.courses.setting._id': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.gender': 1,
                    'groups.courses.setting.session_setting.delivery_type': 1,
                    'groups.courses.setting.session_setting.session_type': 1,
                    'groups.courses.setting.session_setting.groups._id': 1,
                    'groups.courses.setting.session_setting.groups.group_no': 1,
                    'groups.courses.setting.session_setting.groups.group_name': 1,
                    'groups.courses.session_types': 1,
                    'groups.courses.versionNo': 1,
                    'groups.courses.versioned': 1,
                    'groups.courses.versionName': 1,
                    'groups.courses.versionedFrom': 1,
                    'groups.courses.gender_mixed': 1,
                },
            )
            .lean();
        const masterGroup = [];
        const processFoundationGroups = (group) => {
            const groups = [];
            group.group_setting.forEach((settingElement) => {
                settingElement.groups.forEach((groupElement) => {
                    groups.push({
                        _id: groupElement._id,
                        gender: settingElement.gender,
                        groupNo: groupElement.group_no,
                        groupName: groupElement.group_name,
                        deliveryType: groupElement.delivery_type,
                        deliverySymbol: groupElement.session_type,
                        mode: FYD,
                    });
                });
            });
            return groups;
        };

        const processRotationGroups = (group) => {
            const groups = [];
            group.rotation_group_setting
                .filter((settingElement) => settingElement.group_no == rotationCount)
                .forEach((settingElement) => {
                    groups.push({
                        _id: settingElement._id,
                        gender: settingElement.gender,
                        rotation: 'yes',
                        rotationCount: settingElement.group_no,
                        groupNo: settingElement.group_no,
                        groupName: settingElement.group_name,
                        deliveryType: settingElement.delivery_type,
                        deliverySymbol: settingElement.session_type,
                        sessionGroup: [],
                        mode: ROTATION,
                    });
                });
            return groups;
        };
        const processSessionGroups = (setting) => {
            const sessionGroupChecks = [];
            setting.session_setting.forEach((settingElement) => {
                sessionGroupChecks.push({
                    _id: setting._id,
                    deliveryType: settingElement.delivery_type,
                    deliverySymbol: settingElement.session_type,
                    sessionGroup: settingElement.groups.map((groupElement) => ({
                        _id: groupElement._id,
                        groupNo: groupElement.group_no,
                        groupName: groupElement.group_name,
                    })),
                });
            });
            return sessionGroupChecks;
        };
        const processCourseGroups = (setting) => {
            const groups = [];
            setting.session_setting.forEach((settingElement) => {
                const groupName =
                    setting.gender === BOTH ? 'SG1' : setting.gender === MALE ? 'MG-1' : 'FG-1';
                groups.push({
                    _id: setting._id,
                    gender: setting.gender,
                    groupNo: 1,
                    groupName,
                    deliveryType: settingElement.delivery_type,
                    deliverySymbol: settingElement.session_type,
                    sessionGroup: settingElement.groups.map((groupElement) => ({
                        _id: groupElement._id,
                        groupNo: groupElement.group_no,
                        groupName: this.simplifyDeliveryGroupName(groupElement.group_name),
                    })),
                });
            });
            return groups;
        };
        const groupDataByStructure = (flatData) => {
            const groupedData = {};
            flatData.forEach((itemElement) => {
                const groupName = itemElement.groupName;
                const deliverySymbol = itemElement.deliverySymbol;
                if (!groupedData[groupName]) {
                    groupedData[groupName] = {};
                }
                if (!groupedData[groupName][deliverySymbol]) {
                    groupedData[groupName][deliverySymbol] = [];
                }
                groupedData[groupName][deliverySymbol].push({
                    _id: itemElement._id,
                    gender: itemElement.gender,
                    groupNo: itemElement.groupNo,
                    deliveryType: itemElement.deliveryType,
                    sessionGroup: itemElement.sessionGroup || [],
                    ...(itemElement.rotation && { rotation: itemElement.rotation }),
                    ...(itemElement.rotationCount && {
                        rotationCount: itemElement.rotationCount,
                    }),
                });
            });
            return groupedData;
        };

        studentGroupData.groups.forEach((group) => {
            const courseElement = group.courses.find(
                (couseElement) => couseElement._course_id.toString() === courseId.toString(),
            );
            if (
                term === group.term &&
                courseElement &&
                group.level.toString() === levelNo.toString()
            ) {
                switch (group.group_mode.toString()) {
                    case FYD.toString():
                        masterGroup.push(...processFoundationGroups(group));
                        break;
                    case ROTATION.toString():
                        masterGroup.push(...processRotationGroups(group));
                        break;
                    default:
                        break;
                }

                courseElement.setting.forEach((setting) => {
                    if (group.group_mode !== COURSE) {
                        const masterGroupElement = masterGroup.find(
                            (groupElement) =>
                                groupElement.groupNo.toString() === setting._group_no.toString() &&
                                groupElement.gender === setting.gender &&
                                groupElement.mode === group.group_mode.toString(),
                        );
                        if (masterGroupElement) {
                            const sessionGroupChecks = processSessionGroups(setting);
                            if (sessionGroupChecks.length !== 0) {
                                masterGroupElement.sessionGroup = sessionGroupChecks;
                            } else {
                                const indexToRemove = masterGroup.indexOf(masterGroupElement);
                                if (indexToRemove !== -1) {
                                    masterGroup.splice(indexToRemove, 1);
                                }
                            }
                        }
                    } else {
                        masterGroup.push(...processCourseGroups(setting));
                    }
                });
            }
        });

        const groupMode = masterGroup.length > 0 ? masterGroup[0].mode || COURSE : COURSE;
        let updatedMasterGroup = masterGroup;
        if (groupMode !== COURSE) {
            const masterGroupTransformed = [];
            masterGroup.forEach((groupElement) => {
                groupElement.sessionGroup.forEach((deliveryElement) => {
                    const simplifiedSessions = deliveryElement.sessionGroup.map(
                        (sessionElement) => ({
                            _id: sessionElement._id,
                            groupNo: sessionElement.groupNo,
                            groupName: this.simplifyDeliveryGroupName(sessionElement.groupName),
                        }),
                    );
                    masterGroupTransformed.push({
                        _id: groupElement._id,
                        gender: groupElement.gender,
                        rotation: groupElement.rotation,
                        rotationCount: groupElement.rotationCount,
                        groupNo: groupElement.groupNo,
                        groupName: this.simplifyDeliveryGroupName(groupElement.groupName, '-'),
                        deliveryType: deliveryElement.deliveryType,
                        deliverySymbol: deliveryElement.deliverySymbol,
                        sessionGroup: simplifiedSessions,
                        mode: groupElement.mode,
                    });
                });
            });
            updatedMasterGroup = masterGroupTransformed;
        }
        return groupDataByStructure(updatedMasterGroup);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
