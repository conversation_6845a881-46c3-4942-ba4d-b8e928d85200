const PortfolioModel = require('../portfolio/portfolio.model');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const StudentResponseModel = require('../student-response/student-response.model');
const CourseScheduleModel = require('../../../lib/models/course_schedule');
const FormModel = require('../form/form.model');

const {
    isIDEquals,
    convertToMongoObjectId,
    isNumber,
    isBoolean,
} = require('../../common/utils/common.util');
const {
    calculateProportionalScore,
    calculatePercentage,
    getGradeBasedOnPercentage,
    getCompletionStatus,
} = require('./report.helper');
const {
    LB,
    PUBLISHED,
    ABSENT,
    PRESENT,
    NOT_STARTED,
    PASS,
    FAIL,
} = require('../../common/utils/enums');
const { BadRequestError, NotFoundError } = require('../../common/utils/api_error_util');
const { updateDocument, updateDocuments } = require('../../base/base.helper');
const { REGULAR } = require('../../common/utils/constants');
const { calculateGlobalRubricPoint } = require('../rubric/rubric.helper');

const generatePortfolioReport = async ({ portfolioId, studentIds, userId }) => {
    try {
        const portfolio = await PortfolioModel.findOne(
            { _id: portfolioId },
            { grades: 1, components: 1, totalMarks: 1 },
        ).lean();

        if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

        const studentPortfolios = await StudentPortfolioModel.find(
            {
                portfolioId: portfolio._id,
                ...(studentIds.length && { 'student._id': { $in: studentIds } }),
            },
            { student: 1, components: 1, totalMarks: 1, report: 1 },
        ).lean();

        const studentResponses = await StudentResponseModel.find(
            {
                portfolioId: {
                    $in: studentPortfolios.map(({ _id }) => convertToMongoObjectId(_id)),
                },
                ...(studentIds.length && {
                    'student._id': {
                        $in: studentIds.map((studentId) => convertToMongoObjectId(studentId)),
                    },
                }),
            },
            {
                portfolioId: 1,
                student: 1,
                componentId: 1,
                response: 1,
                totalMarks: 1,
                awardedMarks: 1,
                childrenId: 1,
                status: 1,
                globalRubricAwardedPoints: 1,
            },
        ).lean();

        const bulkUpdates = [];

        // Generate report for each student portfolio
        studentPortfolios.forEach((studentPortfolio) => {
            let totalAchievedMarksForStudent = 0;
            let isAllChildrenAbsent = true;
            studentPortfolio.components.forEach((component) => {
                if (component.hasNoGrade) return;

                let totalAchievedMarksForComponent = 0;
                component.children.forEach((child) => {
                    // If component is not LB, then calculate the total achieved marks for the component
                    if (component.code !== LB) {
                        const studentResponse = studentResponses.find(
                            (response) =>
                                isIDEquals(response.student._id, studentPortfolio.student._id) &&
                                isIDEquals(response.componentId, component._id) &&
                                isIDEquals(response.childrenId, child._id),
                        );
                        const arrayFilters = [
                            { 'componentId._id': convertToMongoObjectId(component._id) },
                            { 'childrenId._id': convertToMongoObjectId(child._id) },
                        ];
                        if (studentResponse) {
                            const totalAchievedMarks = calculateProportionalScore({
                                totalMarks: studentResponse.totalMarks,
                                awardedMarks: studentResponse.awardedMarks,
                                newTotal: child.marks,
                            });
                            totalAchievedMarksForComponent += totalAchievedMarks;
                            isAllChildrenAbsent = false;
                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(studentPortfolio._id) },
                                    update: {
                                        $set: {
                                            'components.$[componentId].children.$[childrenId].awardedMarks':
                                                totalAchievedMarks,
                                            'components.$[componentId].children.$[childrenId].attendance':
                                                studentResponse.status === NOT_STARTED &&
                                                studentResponse?.evaluationStatus === NOT_STARTED
                                                    ? ABSENT
                                                    : PRESENT,
                                            'components.$[componentId].children.$[childrenId].globalRubricAwardedPoints':
                                                studentResponse?.globalRubricAwardedPoints || 0,
                                        },
                                    },
                                    arrayFilters,
                                },
                            });
                        } else {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(studentPortfolio._id) },
                                    update: {
                                        $set: {
                                            'components.$[componentId].children.$[childrenId].attendance':
                                                ABSENT,
                                        },
                                    },
                                    arrayFilters,
                                },
                            });
                        }
                    } else {
                        // If component is LB, then calculate the total achieved marks for the child
                        const responses = studentResponses.filter(
                            (response) =>
                                isIDEquals(response.student._id, studentPortfolio.student._id) &&
                                isIDEquals(response.componentId, component._id) &&
                                isIDEquals(response.childrenId, child._id),
                        );
                        const arrayFilters = [
                            { 'componentId._id': convertToMongoObjectId(component._id) },
                            { 'childrenId._id': convertToMongoObjectId(child._id) },
                        ];

                        if (responses.length) {
                            let totalAchievedMarksForChild = 0;
                            responses.forEach((response) => {
                                const totalAchievedMarks = calculateProportionalScore({
                                    totalMarks: response.totalMarks,
                                    awardedMarks: response.awardedMarks,
                                    newTotal: child.marks,
                                });
                                totalAchievedMarksForChild += totalAchievedMarks;
                            });

                            const totalScheduleAchievedMarks =
                                totalAchievedMarksForChild / responses.length;

                            totalAchievedMarksForComponent += totalScheduleAchievedMarks;
                            isAllChildrenAbsent = false;
                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(studentPortfolio._id) },
                                    update: {
                                        $set: {
                                            'components.$[componentId].children.$[childrenId].awardedMarks':
                                                totalScheduleAchievedMarks,
                                            'components.$[componentId].children.$[childrenId].attendance':
                                                responses.some(
                                                    (response) =>
                                                        response.status !== NOT_STARTED &&
                                                        response?.evaluationStatus === NOT_STARTED,
                                                )
                                                    ? PRESENT
                                                    : ABSENT,
                                            'components.$[componentId].children.$[childrenId].globalRubricAwardedPoints':
                                                responses.some(
                                                    (response) =>
                                                        response.globalRubricAwardedPoints,
                                                )
                                                    ? responses.reduce(
                                                          (acc, response) =>
                                                              acc +
                                                              response.globalRubricAwardedPoints,
                                                          0,
                                                      ) / responses.length
                                                    : 0,
                                        },
                                    },
                                    arrayFilters,
                                },
                            });
                        } else {
                            bulkUpdates.push({
                                updateOne: {
                                    filter: { _id: convertToMongoObjectId(studentPortfolio._id) },
                                    update: {
                                        $set: {
                                            'components.$[componentId].children.$[childrenId].attendance':
                                                ABSENT,
                                        },
                                    },
                                    arrayFilters,
                                },
                            });
                        }
                    }
                });
                totalAchievedMarksForStudent += totalAchievedMarksForComponent;
                bulkUpdates.push({
                    updateOne: {
                        filter: { _id: convertToMongoObjectId(studentPortfolio._id) },
                        update: {
                            $set: {
                                'components.$[componentId].awardedMarks':
                                    totalAchievedMarksForComponent,
                            },
                        },
                        arrayFilters: [
                            {
                                'componentId._id': convertToMongoObjectId(component._id),
                            },
                        ],
                    },
                });
            });

            const percentage = calculatePercentage({
                awardedMarks:
                    totalAchievedMarksForStudent + (studentPortfolio?.report?.extraMarks || 0),
                totalMarks: studentPortfolio.totalMarks,
            });

            const grade = getGradeBasedOnPercentage({
                percentage,
                grades: portfolio.grades,
            });

            bulkUpdates.push({
                updateOne: {
                    filter: { _id: convertToMongoObjectId(studentPortfolio._id) },
                    update: {
                        $set: {
                            'report.awardedMarks': totalAchievedMarksForStudent,
                            'report.finalScore':
                                totalAchievedMarksForStudent +
                                (studentPortfolio?.report?.extraMarks || 0),
                            'report.isReportGenerated': true,
                            'report.attendance': isAllChildrenAbsent ? ABSENT : PRESENT,
                            'report.percentage': percentage,
                            'report.grade': grade.name,
                            'report.hasFailed': grade.isFail,
                        },
                        $push: {
                            history: {
                                userId: convertToMongoObjectId(userId),
                                awardedMarks: totalAchievedMarksForStudent,
                                createdAt: new Date(),
                            },
                        },
                    },
                },
            });
        });

        await StudentPortfolioModel.bulkWrite(bulkUpdates).catch(() => {
            throw new BadRequestError('ERROR_IN_UPDATING_MARKS');
        });

        await PortfolioModel.updateOne(
            { _id: convertToMongoObjectId(portfolioId) },
            { $set: { isReportGenerated: true } },
        );

        return portfolio;
    } catch (err) {
        throw new BadRequestError('ERROR_IN_GENERATING_REPORT');
    }
};

const getPortfolioReport = async ({ portfolioId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId, status: PUBLISHED },
        {
            components: 1,
            totalMarks: 1,
            _id: 1,
            programId: 1,
            courseId: 1,
            institutionCalendarId: 1,
            settings: 1,
            year: 1,
            term: 1,
            level: 1,
            rotation: 1,
            rotationCount: 1,
        },
    ).lean();
    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

    const studentPortfolios = await StudentPortfolioModel.find(
        { portfolioId, status: PUBLISHED },
        {
            student: 1,
            'components._id': 1,
            'components.code': 1,
            'components.awardedMarks': 1,
            'components.marks': 1,
            'components.percentage': 1,
            'components.children._id': 1,
            'components.children.awardedMarks': 1,
            'components.children.marks': 1,
            'components.children.weightage': 1,
            'components.children.attendance': 1,
            'components.children.mustPass': 1,
            'components.children.passMarks': 1,
            'components.children.globalRubricAwardedPoints': 1,
            report: 1,
        },
    ).lean();

    const component = portfolio.components.find((component) => component.code === LB);

    if (component) {
        const studentResponses = await StudentResponseModel.find(
            {
                componentId: convertToMongoObjectId(component._id),
                childrenId: {
                    $in: component.children.map((child) => convertToMongoObjectId(child._id)),
                },
            },
            {
                student: 1,
                componentId: 1,
                childrenId: 1,
                totalMarks: 1,
                awardedMarks: 1,
                scheduleId: 1,
                status: 1,
                evaluationStatus: 1,
            },
        ).lean();

        const courseSchedules = await CourseScheduleModel.find(
            {
                _institution_calendar_id: portfolio.institutionCalendarId,
                _program_id: portfolio.programId,
                _course_id: portfolio.courseId,
                type: REGULAR,
                isDeleted: false,
                isActive: true,
                year_no: portfolio.year,
                term: portfolio.term,
                level_no: portfolio.level,
                ...(portfolio.rotation && { rotation_no: portfolio.rotation }),
                ...(portfolio.rotationCount && { rotation_count: portfolio.rotationCount }),
            },
            {
                scheduleDate: '$schedule_date',
                session: 1,
                start: 1,
                end: 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
            },
        ).lean();

        const schedules = courseSchedules.filter((schedule) => {
            return component?.deliveryTypes?.some(
                (deliveryType) =>
                    deliveryType?.deliveryTypeSymbol === schedule?.session?.delivery_symbol,
            );
        });

        component.children.forEach((child) => {
            child.schedules = schedules;
            if (child?.rubrics?.length) {
                const { totalRubricsPoint } = calculateGlobalRubricPoint({
                    rubrics: child.rubrics,
                });
                child.globalRubricTotalPoints = totalRubricsPoint;
            }
        });

        studentPortfolios.forEach((studentPortfolio) => {
            studentPortfolio.components.forEach((component) => {
                if (component.code === LB) {
                    component.children.forEach((child) => {
                        const formattedSchedule = schedules.map((schedule) => {
                            const response = studentResponses.find(
                                (response) =>
                                    isIDEquals(response.childrenId, child._id) &&
                                    isIDEquals(response.componentId, component._id) &&
                                    isIDEquals(response.scheduleId, schedule._id) &&
                                    isIDEquals(response.student._id, studentPortfolio.student._id),
                            );

                            return {
                                _id: schedule._id,
                                awardedMarks: calculateProportionalScore({
                                    totalMarks: response?.totalMarks || 0,
                                    awardedMarks: response?.awardedMarks || 0,
                                    newTotal: child.marks,
                                }),
                                totalMarks: child?.totalMarks || 0,
                                globalRubricAwardedPoints: response?.globalRubricAwardedPoints || 0,
                                attendance:
                                    response?.status === NOT_STARTED &&
                                    response?.evaluationStatus === NOT_STARTED
                                        ? ABSENT
                                        : PRESENT,
                            };
                        });

                        child.schedules = formattedSchedule;
                    });
                }
            });
        });
    }

    portfolio.components = portfolio.components.filter((component) => !component.hasNoGrade);
    studentPortfolios.forEach((studentPortfolio) => {
        studentPortfolio.components = studentPortfolio.components.filter(
            (component) => !component.hasNoGrade,
        );

        // Calculate equivalence mark
        if (portfolio?.settings?.equivalenceMark) {
            studentPortfolio.report.equivalenceMark = calculateProportionalScore({
                totalMarks: portfolio.totalMarks,
                awardedMarks: studentPortfolio.report.awardedMarks,
                newTotal: portfolio?.settings?.equivalenceMark,
            });
        }

        const settings = portfolio?.settings;
        const report = studentPortfolio.report;

        // Overall pass check
        if (settings?.overAllPassEnabled && settings.overAllPassMarks) {
            report.overAllPass = studentPortfolio.report.awardedMarks >= settings.overAllPassMarks;
        }

        // Component-wise pass check
        if (settings?.isComponentWisePassEnabled && settings.minComponentsForInferencePass) {
            let passCount = 0;
            let mustPassFailed = false;

            studentPortfolio.components.forEach(({ children }) =>
                children.forEach(({ awardedMarks, passMarks, mustPass }) => {
                    if (awardedMarks >= passMarks) passCount++;
                    if (mustPass && awardedMarks < passMarks) mustPassFailed = true;
                }),
            );

            report.componentWisePass =
                passCount >= settings.minComponentsForInferencePass && !mustPassFailed;
        }

        // Final status based on completion rules
        if (
            settings?.isComponentWisePassEnabled &&
            settings.overAllPassEnabled &&
            settings.completionRules?.length
        ) {
            report.status = getCompletionStatus({
                componentWisePass: report.componentWisePass,
                overAllPass: report.overAllPass,
                completionRules: settings.completionRules,
            });
        } else {
            report.status = report.componentWisePass ?? report.overAllPass ? PASS : FAIL;
        }
    });

    return { portfolio, students: studentPortfolios };
};

const addExtraMark = async ({ portfolioId, studentId, marks }) => {
    const portfolio = await StudentPortfolioModel.findOne(
        {
            _id: portfolioId,
            'student._id': studentId,
            status: PUBLISHED,
            'report.isReportGenerated': true,
        },
        { totalMarks: 1, report: 1, grades: 1 },
    ).lean();
    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND_OR_REPORT_NOT_GENERATED');

    const finalScore = (portfolio?.report?.awardedMarks || 0) + marks;

    if (finalScore > portfolio.totalMarks) {
        throw new BadRequestError('EXTRA_MARKS_EXCEEDS_TOTAL_MARKS');
    }

    const percentage = calculatePercentage({
        awardedMarks: finalScore,
        totalMarks: portfolio.totalMarks,
    });

    const grade = getGradeBasedOnPercentage({
        percentage,
        grades: portfolio.grades,
    });

    await StudentPortfolioModel.updateOne(
        { _id: portfolioId },
        {
            $set: {
                'report.extraMarks': marks,
                'report.finalScore': finalScore,
                'report.percentage': percentage,
                'report.grade': grade.name,
                'report.hasFailed': grade.isFail,
            },
        },
    );
};

const updateMarkSettings = async ({ portfolioId, marks, type }) => {
    const key = `settings.${type}`;

    await updateDocument({
        Model: PortfolioModel,
        query: { _id: portfolioId, status: PUBLISHED },
        updateDoc: { $set: { [key]: marks } },
        errorMessage: 'FAILED_TO_UPDATE_EQUIVALENCE_MARK',
    });

    await updateDocuments({
        Model: StudentPortfolioModel,
        query: { portfolioId, status: PUBLISHED },
        updateDoc: { $set: { [key]: marks } },
        errorMessage: 'FAILED_TO_UPDATE_EQUIVALENCE_MARK',
    });
};

const updateReportSettings = async ({
    portfolioId,
    overAllPassEnabled,
    isComponentWisePassEnabled,
}) => {
    await updateDocument({
        Model: PortfolioModel,
        query: { _id: portfolioId, status: PUBLISHED },
        updateDoc: {
            $set: {
                'settings.overAllPassEnabled': overAllPassEnabled,
                'settings.isComponentWisePassEnabled': isComponentWisePassEnabled,
            },
        },
        errorMessage: 'FAILED_TO_UPDATE_EQUIVALENCE_MARK',
    });

    await updateDocuments({
        Model: StudentPortfolioModel,
        query: { portfolioId, status: PUBLISHED },
        updateDoc: {
            $set: {
                'settings.overAllPassEnabled': overAllPassEnabled,
                'settings.isComponentWisePassEnabled': isComponentWisePassEnabled,
            },
        },
        errorMessage: 'FAILED_TO_UPDATE_EQUIVALENCE_MARK',
    });
};

const updateComponentWisePassSettings = async ({
    portfolioId,
    componentId,
    childId,
    marks,
    mustPass,
    completionRules = [],
}) => {
    if (completionRules.length) {
        await updateDocument({
            Model: PortfolioModel,
            query: { _id: portfolioId, status: PUBLISHED },
            updateDoc: { $set: { 'settings.completionRules': completionRules } },
            errorMessage: 'FAILED_TO_UPDATE_COMPONENT_WISE_PASS_SETTINGS',
        });

        return;
    }

    const updateFields = {
        ...(isBoolean(mustPass) && {
            'components.$[componentId].children.$[childId].mustPass': mustPass,
        }),
        ...(isNumber(marks) && {
            'components.$[componentId].children.$[childId].passMarks': marks,
        }),
    };

    const arrayFilters =
        isBoolean(mustPass) || isNumber(marks)
            ? [
                  { 'componentId._id': convertToMongoObjectId(componentId) },
                  { 'childId._id': convertToMongoObjectId(childId) },
              ]
            : undefined;

    const sharedUpdateOptions = {
        updateDoc: { $set: updateFields },
        arrayFilters,
        errorMessage: 'FAILED_TO_UPDATE_COMPONENT_WISE_PASS_SETTINGS',
    };

    await updateDocument({
        Model: PortfolioModel,
        query: { _id: portfolioId, status: PUBLISHED },
        ...sharedUpdateOptions,
    });

    await updateDocuments({
        Model: StudentPortfolioModel,
        query: { portfolioId, status: PUBLISHED },
        ...sharedUpdateOptions,
    });
};

const getFormEvaluations = async ({ formIds = [] }) => {
    const forms = await FormModel.find({ _id: { $in: formIds } }, { evaluations: 1 }).lean();
    return (formId) => forms.find((f) => isIDEquals(f._id, formId))?.evaluation;
};

const extractFormIds = ({ children = [] }) =>
    children.reduce((ids, child) => {
        if (child.formId) ids.push(convertToMongoObjectId(child.formId));
        return ids;
    }, []);

const getIndividualStudentReport = async ({ portfolioId, componentId, childrenId, studentId }) => {
    const portfolio = await StudentPortfolioModel.findOne(
        {
            _id: portfolioId,
            status: PUBLISHED,
            'student._id': studentId,
        },
        {
            'components._id': 1,
            'components.code': 1,
            'components.name': 1,
            'components.marks': 1,
            'components.awardedMarks': 1,
            'components.percentage': 1,
            'components.children.awardedMarks': 1,
            'components.children.percentage': 1,
            'components.children.passMarks': 1,
            'components.children._id': 1,
            'components.children.name': 1,
            'components.children.marks': 1,
            'components.children.weightage': 1,
            'components.children.attendance': 1,
            'components.children.mustPass': 1,
            'components.children.formId': 1,
            student: 1,
            report: 1,
            totalMarks: 1,
        },
    ).lean();
    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

    portfolio.components = portfolio.components.filter((c) => !c.hasNoGrade);

    if (!componentId && !childrenId) {
        const formIds = extractFormIds({
            children: portfolio.components.flatMap((c) => c.children),
        });
        const getEvaluation = await getFormEvaluations({ formIds });

        portfolio.components.forEach((c) =>
            c.children.forEach((child) => {
                child.evaluation = getEvaluation(child.formId);
            }),
        );

        return portfolio;
    }

    const component = portfolio.components.find((c) => isIDEquals(c._id, componentId));
    if (!component) return null;

    const formIds = extractFormIds({ children: component.children });
    const getEvaluation = await getFormEvaluations({ formIds });

    if (!childrenId) {
        component.children.forEach((child) => {
            child.evaluation = getEvaluation(child.formId);
        });
        return component;
    }

    const child = component.children.find((c) => isIDEquals(c._id, childrenId));
    if (!child) return null;

    child.evaluation = getEvaluation(child.formId);
    return child;
};

const findComponent = ({ portfolio, componentId }) =>
    portfolio.components.find((c) => isIDEquals(c._id, componentId));

const findResponse = (responses, childId, componentId, scheduleId, studentId) =>
    responses.find(
        (r) =>
            isIDEquals(r.childrenId, childId) &&
            isIDEquals(r.componentId, componentId) &&
            isIDEquals(r.scheduleId, scheduleId) &&
            (!studentId || isIDEquals(r.student._id, studentId)),
    );

const formatSchedule = ({ response, marks, totalMarks, scheduleId }) => ({
    _id: scheduleId,
    awardedMarks: calculateProportionalScore({
        totalMarks: response?.totalMarks || 0,
        awardedMarks: response?.awardedMarks || 0,
        newTotal: marks,
    }),
    totalMarks: totalMarks || 0,
    attendance:
        response?.status === NOT_STARTED && response?.evaluationStatus === NOT_STARTED
            ? ABSENT
            : PRESENT,
});

const formatStudentPortfolio = (student, data, children) => ({
    student,
    totalMarks: data?.marks,
    awardedMarks: data?.awardedMarks,
    percentage: data?.percentage,
    children,
});

const getStudentResponses = async (componentId, childrenId = null) =>
    StudentResponseModel.find(
        {
            componentId: convertToMongoObjectId(componentId),
            ...(childrenId && { childrenId: convertToMongoObjectId(childrenId) }),
        },
        {
            student: 1,
            componentId: 1,
            childrenId: 1,
            totalMarks: 1,
            awardedMarks: 1,
            scheduleId: 1,
            status: 1,
            evaluationStatus: 1,
        },
    ).lean();

const getFilteredSchedules = async (deliveryTypes, portfolio) => {
    const schedules = await CourseScheduleModel.find(
        {
            _institution_calendar_id: portfolio.institutionCalendarId,
            _program_id: portfolio.programId,
            _course_id: portfolio.courseId,
            type: REGULAR,
            isDeleted: false,
            isActive: true,
            year_no: portfolio.year,
            term: portfolio.term,
            level_no: portfolio.level,
            ...(portfolio.rotation && { rotation_no: portfolio.rotation }),
            ...(portfolio.rotationCount && { rotation_count: portfolio.rotationCount }),
        },
        {
            scheduleDate: '$schedule_date',
            session: 1,
            start: 1,
            end: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
        },
    ).lean();

    return schedules.filter((s) =>
        deliveryTypes?.some((d) => d?.deliveryTypeSymbol === s?.session?.delivery_symbol),
    );
};

const getPortfolioComponentReport = async ({ portfolioId, componentId, childrenId }) => {
    const portfolio = await PortfolioModel.findOne(
        { _id: portfolioId, status: PUBLISHED },
        {
            'components._id': 1,
            'components.code': 1,
            'components.awardedMarks': 1,
            'components.marks': 1,
            'components.percentage': 1,
            'components.children': 1,
            'components.deliveryTypes': 1,
            settings: 1,
            institutionCalendarId: 1,
            programId: 1,
            courseId: 1,
            year: 1,
            term: 1,
            level: 1,
            rotation: 1,
        },
    ).lean();
    if (!portfolio) throw new NotFoundError('PORTFOLIO_NOT_FOUND');

    let studentPortfolios = await StudentPortfolioModel.find(
        { portfolioId, status: PUBLISHED },
        {
            student: 1,
            components: 1,
            report: 1,
        },
    ).lean();

    const component = portfolio.components.find((c) => isIDEquals(c._id, componentId));
    if (!component) return { component: null, students: [] };

    const schedules =
        component.code === LB ? await getFilteredSchedules(component.deliveryTypes, portfolio) : [];

    if (!childrenId && component.code === LB) {
        const studentResponses = await getStudentResponses(component._id);

        component.children.forEach((child) => {
            child.schedules = schedules;
        });

        studentPortfolios = studentPortfolios
            .map((portfolio) => {
                const comp = findComponent({ portfolio, componentId });
                if (!comp || comp.code !== LB) return null;

                component.children.forEach((child) => {
                    child.schedules = schedules.map((schedule) => {
                        const res = findResponse(
                            studentResponses,
                            child._id,
                            comp._id,
                            schedule._id,
                            portfolio.student._id,
                        );
                        return formatSchedule({
                            response: res,
                            marks: child.marks,
                            totalMarks: child.totalMarks,
                            scheduleId: schedule._id,
                        });
                    });
                });

                return formatStudentPortfolio(portfolio.student, comp, comp.children);
            })
            .filter(Boolean);

        return { component, students: studentPortfolios };
    }

    const child = component.children.find((c) => isIDEquals(c._id, childrenId));
    if (component.code === LB && child) {
        const studentResponses = await getStudentResponses(component._id, childrenId);

        studentPortfolios = studentPortfolios
            .map((portfolio) => {
                const comp = findComponent({ portfolio, componentId });
                const ch = comp?.children.find((c) => isIDEquals(c._id, childrenId));
                if (!comp || comp.code !== LB || !ch) return null;

                ch.schedules = schedules.map((schedule) => {
                    const res = findResponse(
                        studentResponses,
                        ch._id,
                        comp._id,
                        schedule._id,
                        portfolio.student._id,
                    );
                    return formatSchedule({
                        response: res,
                        marks: ch.marks,
                        totalMarks: ch.totalMarks,
                        scheduleId: schedule._id,
                    });
                });

                return formatStudentPortfolio(portfolio.student, ch, ch);
            })
            .filter(Boolean);
    }

    return { component, children: { ...child, schedules }, students: studentPortfolios };
};

module.exports = {
    generatePortfolioReport,
    getPortfolioReport,
    addExtraMark,
    updateMarkSettings,
    updateReportSettings,
    updateComponentWisePassSettings,
    getIndividualStudentReport,
    getPortfolioComponentReport,
};
