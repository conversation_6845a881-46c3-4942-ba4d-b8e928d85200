const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { QAPC_SUB_MODULE, QAPC_ACTION } = require('../../utility/constants');
const qapcSubModuleSchema = new Schema(
    {
        moduleName: { type: String },
        actionsIds: [{ type: ObjectId, ref: QAPC_ACTION }],
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_SUB_MODULE, qapcSubModuleSchema);
