const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const { paramIDValidator, addAssignmentGroupValidator } = require('./assignment_group.validator');
const { validate } = require('../../../../middleware/validation');
const {
    addAssignmentGroup,
    updateAssignmentGroup,
    getAssignmentGroup,
    listAssignmentGroup,
    deleteAssignmentGroup,
} = require('./assignment_group.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.post(
    '/create-assignment-group',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: addAssignmentGroupValidator, property: 'body' }]),
    catchAsync(addAssignmentGroup),
);
router.put(
    '/update-assignment-group/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([
        { schema: paramIDValidator, property: 'params' },
        { schema: addAssignmentGroupValidator, property: 'body' },
    ]),
    catchAsync(updateAssignmentGroup),
);
router.get(
    '/get-assignment-group/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getAssignmentGroup),
);
router.get(
    '/list-assignment-group/:_assignment_id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(listAssignmentGroup),
);
router.delete('/delete-assignment-group/:id', catchAsync(deleteAssignmentGroup));

module.exports = router;
