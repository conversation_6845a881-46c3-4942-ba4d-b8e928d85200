const assignmentAnswerSchema = require('./assignment_answer.model');
const assignmentSchema = require('../assignment/assignment.model');
const assignmentPromptAnswerSchema = require('../assignment_prompt_answer/assignment_prompt_answer.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const { getSignedUrl } = require('../assignment-settings/assignment-settings.util');

const createAssignmentAnswer = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            assignmentId,
            studentId,
            programId,
            courseId,
            promptsAnswer,
            assignmentAnswer,
            isDraft,
            noOfAttempt,
            submittedAt,
            status,
            parentPrompts,
        } = body;

        const createAssignmentAnswer = await assignmentAnswerSchema.findOneAndUpdate(
            { _institution_id, assignmentId, studentId, isDeleted: false },
            {
                _institution_id,
                assignmentId,
                studentId,
                programId,
                courseId,
                promptsAnswer,
                assignmentAnswer,
                isDraft,
                noOfAttempt,
                submittedAt,
                status,
            },
        );
        if (!createAssignmentAnswer)
            return { statusCode: 410, message: 'ERROR IN CREATING ASSIGNMENT ANSWER' };
        if (parentPrompts && parentPrompts.length) {
            const parentPromptDataBulkUpdate = [];
            for (const parentPromptElement of parentPrompts) {
                const { studentId, assignmentId, assignmentPromptId } = parentPromptElement;
                if (!studentId)
                    return {
                        statusCode: 404,
                        message: 'Required studentId field value',
                    };
                if (!assignmentId)
                    return {
                        statusCode: 404,
                        message: 'Required assignmentId field value',
                    };
                if (!assignmentPromptId)
                    return {
                        statusCode: 404,
                        message: 'Required assignmentPromptId field value',
                    };
                parentPromptDataBulkUpdate.push({
                    insertOne: {
                        document: {
                            studentId: convertToMongoObjectId(studentId),
                            assignmentId: convertToMongoObjectId(assignmentId),
                            _institution_id: convertToMongoObjectId(_institution_id),
                            assignmentPromptId: convertToMongoObjectId(assignmentPromptId),
                        },
                    },
                });
            }
            const assignmentPromptAnswerCreate = await assignmentPromptAnswerSchema.bulkWrite(
                parentPromptDataBulkUpdate,
            );
            if (!assignmentPromptAnswerCreate)
                return {
                    statusCode: 410,
                    message: 'ERROR IN CREATING ASSIGNMENT PARENT PROMPT ANSWER DATA',
                };
        }
        return {
            statusCode: 200,
            message: 'ASSIGNMENT ANSWER CREATED',
            data: { assignmentAnswerId: createAssignmentAnswer._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getAssignmentAnswer = async ({ params = {} }) => {
    try {
        const { id } = params;
        const assignmentAnswer = await assignmentAnswerSchema
            .findById({ _id: convertToMongoObjectId(id) })
            .lean();
        if (!assignmentAnswer)
            return { statusCode: 410, message: 'ERROR IN GETTING ASSIGNMENT ANSWER ' };
        return { statusCode: 200, message: 'ASSIGNMENT ANSWER', data: assignmentAnswer };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateAssignmentAnswer = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const {
            assignmentId,
            studentId,
            promptsAnswer,
            assignmentAnswer,
            isDraft,
            isResubmitted,
            noOfAttempt,
        } = body;
        let updateAssignment;
        if (isResubmitted === true) {
            updateAssignment = await assignmentAnswerSchema.findByIdAndUpdate(
                { _id: convertToMongoObjectId(id) },
                {
                    _institution_id,
                    assignmentId,
                    studentId,
                    ...(promptsAnswer && { promptsAnswer }),
                    ...(assignmentAnswer && { $addToSet: { assignmentAnswer } }),
                    isDraft,
                    noOfAttempt,
                },
            );
        } else {
            updateAssignment = await assignmentAnswerSchema.findByIdAndUpdate(
                { _id: convertToMongoObjectId(id) },
                {
                    _institution_id,
                    assignmentId,
                    studentId,
                    promptsAnswer,
                    assignmentAnswer,
                    isDraft,
                    noOfAttempt,
                },
            );
        }

        if (!updateAssignment) return { statusCode: 410, message: 'ASSIGNMENT ANSWER NOT UPDATED' };
        const assignmentUpdateStudent = await assignmentSchema.findByIdAndUpdate(
            { _id: assignmentId },
            {
                ...(isResubmitted && {
                    'submission.assignTo.$[].studentIds.$[j].status': 'submitted',
                }),
                ...(!isResubmitted && {
                    'submission.assignTo.$[].studentIds.$[j].status': 'resubmitted',
                }),
                'submission.assignTo.$[].studentIds.$[j].isDraft': isDraft,
                'submission.assignTo.$[].studentIds.$[j].noOfAttempt': noOfAttempt,
                $addToSet: {
                    'submission.assignTo.$[].studentIds.$[j].updatedAt': {
                        attemptNo: noOfAttempt,
                        date: updatedDate,
                    },
                },
            },
            { arrayFilters: [{ 'j.studentId': convertToMongoObjectId(studentId) }] },
        );
        return {
            statusCode: 200,
            message: 'ASSIGNMENT GROUP UPDATED',
            data: { assignmentAnswerId: updateAssignment._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listAssignmentAnswer = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { assignmentId, studentId } = query;
        const listAssignmentAnswer = await assignmentAnswerSchema
            .findOne({
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                assignmentId: convertToMongoObjectId(assignmentId),
                studentId: convertToMongoObjectId(studentId),
            })
            .lean();
        if (!listAssignmentAnswer)
            return { statusCode: 410, message: 'ERROR IN LISTING ASSIGNMENT ANSWER' };
        if (listAssignmentAnswer.assignmentAnswer && listAssignmentAnswer.assignmentAnswer.length)
            for (const answerItem of listAssignmentAnswer.assignmentAnswer)
                if (answerItem.attachments && answerItem.attachments.length)
                    for (const attachmentElement of answerItem.attachments)
                        attachmentElement.signedUrl = await getSignedUrl(attachmentElement.url);

        listAssignmentAnswer.todayDate = new Date();
        return { statusCode: 200, message: 'ASSIGNMENT ANSWER', data: listAssignmentAnswer };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteAssignmentAnswer = async ({ headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const deleteAssignment = await assignmentAnswerSchema
            .findByIdAndDelete({ _id: convertToMongoObjectId(id) })
            .lean();
        if (!deleteAssignment)
            return { statusCode: 410, message: 'ERROR IN DELETING ASSIGNMENT ANSWER' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT ANSWER DELETED',
            data: {},
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateCommentsAssignmentAnswer = async ({ params = {}, body = {} }) => {
    try {
        const { id } = params;
        const { comments } = body;
        const updateComment = await assignmentAnswerSchema.findByIdAndUpdate(
            { _id: convertToMongoObjectId(id) },
            { $addToSet: { comments } },
        );
        if (!updateComment) return { statusCode: 410, message: 'ASSIGNMENT COMMENT NOT UPDATED' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT COMMENT UPDATED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    createAssignmentAnswer,
    getAssignmentAnswer,
    listAssignmentAnswer,
    updateAssignmentAnswer,
    updateCommentsAssignmentAnswer,
    deleteAssignmentAnswer,
};
