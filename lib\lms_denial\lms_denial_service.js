const { redisClient } = require('../../config/redis-connection');
const constants = require('../utility/constants');
const programCalendarModel = require('../models/program_calendar');
const studentGroupModel = require('../models/student_group');
const { convertToMongoObjectId } = require('../utility/common');
const studentLmsSettingModel = require('../lmsStudentSetting/lmsStudentSetting.model');
const roleAssignModel = require('../models/role_assign');
const lmsDenialLogModel = require('./lms_denial_log_model');
const {
    getWarningSettingBasedCalendar,
} = require('../lmsStudentSetting/lmsStudentSetting.controller');
exports.getLevelsFromRedis = async (programId, _institution_calendar_id) => {
    const key = `${constants.PROGRAM_CALENDAR}:${programId}-${_institution_calendar_id}`;
    const levels = await redisClient.Client.get(key);
    const populateSelectFields = {
        versionNo: 1,
        versioned: 1,
        versionName: 1,
        versionedFrom: 1,
        versionedCourseIds: 1,
    };
    if (!(levels && JSON.parse(levels).length)) {
        const programLevels = await programCalendarModel
            .findOne(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    status: 'published',
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'level.course._course_id': 1,
                    'level.course.courses_name': 1,
                    'level.course.courses_number': 1,
                    'level.course.model': 1,
                    'level.term': 1,
                    'level.year': 1,
                    'level.level_no': 1,
                    'level._program_id': 1,
                    'level.rotation': 1,
                    'level.rotation_count': 1,
                    'level.start_date': 1,
                    'level.end_date': 1,
                    'level.rotation_course': 1,
                },
            )
            .populate([
                { path: 'level.course._course_id', select: populateSelectFields },
                { path: 'level.rotation_course.course._course_id', select: populateSelectFields },
            ])
            .lean();
        if (!(programLevels && programLevels.level)) {
            return null;
        }
        await redisClient.Client.set(key, JSON.stringify(programLevels.level));
        return programLevels.level;
    }
    return JSON.parse(levels);
};

exports.getAllCourseStudentsFromRedis = async ({
    programId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    _institution_calendar_id,
    _institution_id,
}) => {
    if (
        programId &&
        year &&
        level &&
        // rotation &&
        term &&
        _institution_calendar_id &&
        _institution_id
    ) {
        const key = `${constants.STUDENT_GROUP}:${programId}-${year}-${level}-${term}-${_institution_calendar_id}`;
        const studentsFromCache = await redisClient.Client.get(key);
        if (!(studentsFromCache && JSON.parse(studentsFromCache).length)) {
            const studentsFromDB = await studentGroupModel
                .findOne(
                    {
                        'master._program_id': programId,
                        'master.year': year,
                        groups: {
                            $elemMatch: {
                                level,
                                term,
                                ...(rotation && { rotation }),
                                // ...(rotation === 'yes' && {
                                //     rotation_count: parseInt(rotationCount),
                                // }),
                            },
                        },
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        'groups.courses._course_id': 1,
                        'groups.level': 1,
                        'groups.term': 1,
                        'groups.rotation': 1,
                        'groups.rotation_count': 1,
                        'groups.courses.course_name': 1,
                        'groups.courses.course_no': 1,
                        'groups.courses.setting._group_no': 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                        'groups.students._student_id': 1,
                        'groups.students.name': 1,
                        'groups.students.academic_no': 1,
                    },
                )
                .populate({
                    path: 'groups.courses._course_id',
                    select: {
                        versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedFrom: 1,
                        versionedCourseIds: 1,
                    },
                })
                .lean();
            if (!studentsFromDB) {
                return null;
            }
            const studentsWithCourseDetails = [];
            for (const groupElement of studentsFromDB.groups) {
                const check =
                    rotation && rotationCount
                        ? groupElement.level === level &&
                          groupElement.term === term &&
                          groupElement.rotation === rotation
                        : groupElement.level === level && groupElement.term === term;
                if (check) {
                    for (const courseElement of groupElement.courses) {
                        for (const settingElement of courseElement.setting) {
                            for (const sessionSettingElement of settingElement.session_setting) {
                                for (const sessionGroupElement of sessionSettingElement.groups) {
                                    for (const studentElement of sessionGroupElement._student_ids) {
                                        const studentDetails = groupElement.students.find(
                                            (studentNameElement) =>
                                                studentNameElement &&
                                                studentNameElement._student_id &&
                                                studentNameElement._student_id._id.toString() ===
                                                    studentElement.toString(),
                                        );
                                        const isAlreadyPresent = studentsWithCourseDetails.find(
                                            (alreadyPresentStudentElement) =>
                                                alreadyPresentStudentElement.studentId.toString() ===
                                                    studentElement.toString() &&
                                                alreadyPresentStudentElement._course_id.toString() ===
                                                    courseElement._course_id._id.toString() &&
                                                (alreadyPresentStudentElement.rotation_count
                                                    ? alreadyPresentStudentElement.rotation_count ===
                                                      settingElement._group_no
                                                    : true) &&
                                                alreadyPresentStudentElement.term ===
                                                    groupElement.term,
                                        );
                                        if (!isAlreadyPresent) {
                                            studentsWithCourseDetails.push({
                                                term: groupElement.term,
                                                _course_id: courseElement._course_id._id,
                                                versionNo: courseElement._course_id.versionNo || 1,
                                                versioned:
                                                    courseElement._course_id.versioned || false,
                                                versionName:
                                                    courseElement._course_id.versionName || '',
                                                versionedFrom:
                                                    courseElement._course_id.versionedFrom || null,
                                                versionedCourseIds:
                                                    courseElement._course_id.versionedCourseIds ||
                                                    [],
                                                rotation_count: settingElement._group_no
                                                    ? settingElement._group_no
                                                    : 0,
                                                course_name: courseElement.course_name,
                                                course_no: courseElement.course_no,
                                                studentId: studentElement,
                                                studentName: studentDetails
                                                    ? studentDetails.name
                                                    : null,
                                                academicId: studentDetails
                                                    ? studentDetails.academic_no
                                                    : null,
                                                rotation: groupElement.rotation,
                                            });
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (studentsWithCourseDetails.length) {
                await redisClient.Client.set(key, JSON.stringify(studentsWithCourseDetails));
                return studentsWithCourseDetails;
            }
            return [];
        }
        return JSON.parse(studentsFromCache);
    }
};

exports.getParticularCourseStudentsFromRedis = async ({
    programId,
    year,
    level,
    rotation,
    rotationCount,
    term,
    _institution_calendar_id,
    _institution_id,
    courseIds,
}) => {
    if (
        programId &&
        year &&
        level &&
        // rotation &&
        // term &&
        _institution_calendar_id &&
        _institution_id &&
        courseIds
    ) {
        const courseIdStudents = [];
        const studentsFromDB = await studentGroupModel
            .findOne(
                {
                    'master._program_id': programId,
                    'master.year': year,
                    groups: {
                        $elemMatch: {
                            level,
                            ...(term && { term }),
                        },
                    },
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'groups.courses._course_id': 1,
                    'groups.level': 1,
                    'groups.term': 1,
                    'groups.rotation': 1,
                    'groups.rotation_count': 1,
                    'groups.courses.course_name': 1,
                    'groups.courses.course_no': 1,
                    'groups.courses.setting._group_no': 1,
                    'groups.courses.setting.session_setting.groups._student_ids': 1,
                    'groups.students._student_id': 1,
                    'groups.students.name': 1,
                    'groups.students.academic_no': 1,
                },
            )
            .populate({
                path: 'groups.courses._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            })
            .lean();
        if (!studentsFromDB) {
            return [];
        }
        for (const courseId of [...new Set(courseIds)]) {
            let key = `${constants.STUDENT_GROUP}:${year}-${level}-${term}-${courseId}-${_institution_calendar_id}`;
            if (rotationCount) {
                key = `${constants.STUDENT_GROUP}:${year}-${level}-${term}-${courseId}-${_institution_calendar_id}-${rotationCount}`;
            }
            const students = await redisClient.Client.get(key);
            if (students && JSON.parse(students).length) {
                courseIdStudents.push(...JSON.parse(students));
            } else {
                const studentsWithCourseDetails = [];
                for (const groupElement of studentsFromDB.groups) {
                    const check =
                        rotation && rotationCount
                            ? groupElement.level === level &&
                              groupElement.term === term &&
                              groupElement.rotation === rotation
                            : groupElement.level === level &&
                              (term ? groupElement.term === term : true);

                    if (check) {
                        for (const courseElement of groupElement.courses) {
                            if (courseElement._course_id._id.toString() === courseId.toString()) {
                                for (const settingElement of courseElement.setting) {
                                    const checkRotation = rotationCount
                                        ? settingElement._group_no === parseInt(rotationCount)
                                        : true;
                                    if (checkRotation) {
                                        for (const sessionSettingElement of settingElement.session_setting) {
                                            for (const sessionGroupElement of sessionSettingElement.groups) {
                                                for (const studentElement of sessionGroupElement._student_ids) {
                                                    const studentDetails =
                                                        groupElement.students.find(
                                                            (studentNameElement) =>
                                                                studentNameElement &&
                                                                studentNameElement._student_id &&
                                                                studentNameElement._student_id._id.toString() ===
                                                                    studentElement.toString(),
                                                        );
                                                    const isAlreadyPresent =
                                                        studentsWithCourseDetails.find(
                                                            (alreadyPresentStudentElement) =>
                                                                alreadyPresentStudentElement.studentId.toString() ===
                                                                    studentElement.toString() &&
                                                                alreadyPresentStudentElement._course_id.toString() ===
                                                                    courseElement._course_id._id.toString() &&
                                                                (alreadyPresentStudentElement.rotation_count
                                                                    ? alreadyPresentStudentElement.rotation_count ===
                                                                      settingElement._group_no
                                                                    : true) &&
                                                                alreadyPresentStudentElement.term ===
                                                                    groupElement.term,
                                                        );
                                                    if (!isAlreadyPresent && studentDetails) {
                                                        studentsWithCourseDetails.push({
                                                            term: groupElement.term,
                                                            _course_id:
                                                                courseElement._course_id._id,
                                                            versionNo:
                                                                courseElement._course_id
                                                                    .versionNo || 1,
                                                            versioned:
                                                                courseElement._course_id
                                                                    .versioned || false,
                                                            versionName:
                                                                courseElement._course_id
                                                                    .versionName || '',
                                                            versionedFrom:
                                                                courseElement._course_id
                                                                    .versionedFrom || null,
                                                            versionedCourseIds:
                                                                courseElement._course_id
                                                                    .versionedCourseIds || [],
                                                            rotation_count: settingElement._group_no
                                                                ? settingElement._group_no
                                                                : 0,
                                                            course_name: courseElement.course_name,
                                                            course_no: courseElement.course_no,
                                                            studentId: studentElement,
                                                            studentName: studentDetails
                                                                ? studentDetails.name
                                                                : null,
                                                            academicId: studentDetails
                                                                ? studentDetails.academic_no
                                                                : null,
                                                            rotation: groupElement.rotation,
                                                        });
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                const filteredStudents = studentsWithCourseDetails.filter(
                    (studentElement) => studentElement._course_id === courseId.toString(),
                );
                await redisClient.Client.set(key, JSON.stringify(filteredStudents));
                courseIdStudents.push(...studentsWithCourseDetails);
            }
        }
        return courseIdStudents;
    }
};

exports.sortArrayByName = (array) => {
    return array.sort((prev, curr) => {
        if (prev.studentName.first.toLowerCase() < curr.studentName.first.toLowerCase()) return -1;
        if (prev.studentName.first.toLowerCase() > curr.studentName.first.toLowerCase()) return 1;
        return 0;
    });
};

exports.lmsAuthorizationMiddleware = async (req, res, next) => {
    const { _institution_id, _user_id } = req.headers;
    const { _institution_calendar_id } = req.query;
    const lmsStudentSettings = await studentLmsSettingModel
        .findOne(
            {
                _institution_id,
                classificationType: constants.LEAVE_TYPE.LEAVE,
                'warningConfig.isActive': true,
            }, // leave type  is hardcoded
            {
                'warningConfig.percentage': 1,
                'warningConfig.isActive': 1,
                'warningConfig.denialManagement': 1,
            },
        )
        .lean();
    if (!lmsStudentSettings) {
        return res
            .status(200)
            .json({ message: 'leave management settings not found / active', data: [] });
    }
    let { warningConfig } = lmsStudentSettings;
    const warningConfigForCalendar = await getWarningSettingBasedCalendar({
        _institution_calendar_id,
    });
    if (warningConfigForCalendar.length) {
        warningConfig = warningConfigForCalendar;
    }
    let denialConfig;
    let isViewMode = false;
    for (let i = warningConfig.length - 1; i >= 0; i--) {
        if (
            (warningConfig[i].percentage > denialConfig && denialConfig.percentage) ||
            denialConfig == null
        ) {
            denialConfig = warningConfig[i];
        }
        if (!warningConfig[i].isActive) {
            warningConfig.splice(i, 1);
        }
    }
    if (!denialConfig.isActive) {
        isViewMode = true;
    }

    const reverseSortedWarningConfig = warningConfig.sort((a, b) => {
        let comparison = 0;
        if (Number(a.percentage) > Number(b.percentage)) {
            comparison = -1;
        } else if (Number(a.percentage) < Number(b.percentage)) {
            comparison = 1;
        }
        return comparison;
    });
    const accessType = isViewMode
        ? denialConfig.denialManagement.accessType
        : reverseSortedWarningConfig[0] &&
          reverseSortedWarningConfig[0].denialManagement.accessType;
    const selectAccessIds = isViewMode
        ? denialConfig.denialManagement
        : reverseSortedWarningConfig[0].denialManagement;

    const accessIds =
        accessType === 'user'
            ? selectAccessIds.userIds
            : accessType === 'role'
            ? selectAccessIds.roleIds
            : [];
    req.query.denialPercentage = isViewMode
        ? denialConfig.percentage
        : reverseSortedWarningConfig[0].percentage;
    if (accessType === 'user') {
        if (!accessIds.find((userIdElement) => userIdElement.toString() === _user_id)) {
            return res.status(200).json({ message: 'unauthorized access', data: [] });
        }
    } else if (accessType === 'role') {
        const assignedRoles = await roleAssignModel
            .findOne(
                {
                    _user_id: convertToMongoObjectId(_user_id),
                    isActive: true,
                    isDeleted: false,
                },
                { 'roles.isAdmin': 1, 'roles._role_id': 1, 'roles.role_name': 1 },
            )
            .lean();
        const stringedAccessIds = accessIds.map((accessIdElement) => accessIdElement.toString());
        const isValidRole =
            !!assignedRoles &&
            assignedRoles.roles.find(
                (roleElement) =>
                    (roleElement.role_name === constants.COURSE_COORDINATOR ||
                        roleElement.isAdmin === true) &&
                    stringedAccessIds.includes(roleElement._role_id.toString()),
            );
        if (!isValidRole) {
            return res.status(200).json({ message: 'unauthorized access', data: [] });
        }
    } else {
        return res.status(200).json({ message: 'unauthorized access', data: [] });
    }
    next();
};

exports.lmsDenialList = async (_institution_id, _institution_calendar_id) => {
    const lmsDenial = await lmsDenialLogModel
        .find({
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            // isDeleted: false,
            // isActive: true,
        })
        .populate({ path: 'programId', select: { name: 1, code: 1 } })
        .populate({ path: 'studentId', select: { name: 1, user_id: 1 } })
        .populate({
            path: 'courseId',
            select: {
                course_name: 1,
                course_code: 1,
                course_type: 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        })
        .populate({ path: 'updateBy', select: { name: 1, user_id: 1 } })
        .sort({ updatedAt: -1 })
        .lean();
    return lmsDenial;
};
exports.getDenialFromCache = async (_institution_id, _institution_calendar_id, _user_id, state) => {
    const lmsDenialRedisKey = `${constants.REDIS_FOLDERS.LMS_REPORT_DENIAL}:${_user_id}`;
    if (state === 'regular') {
        const lmsDenialCache = await redisClient.Client.get(lmsDenialRedisKey);
        if (lmsDenialCache) {
            return JSON.parse(lmsDenialCache);
        }
    }
    const lmsDenialData = await this.lmsDenialList(_institution_id, _institution_calendar_id);

    if (!lmsDenialData) return [];
    await redisClient.Client.set(lmsDenialRedisKey, JSON.stringify(lmsDenialData));
    return lmsDenialData;
};
