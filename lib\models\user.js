const mongoose = require('mongoose');
const Schemas = mongoose.Schema;
const constant = require('../utility/constants');

const userSchemas = new Schemas(
    {
        user_type: {
            type: String,
            required: true,
        },
        user_id: {
            type: String,
            unique: true,
            required: true,
            trim: true,
        },
        ioToken: {
            type: String,
            default: null,
            trim: true,
        },
        program: {
            program_no: String,
            _program_id: {
                type: Schemas.Types.ObjectId,
                ref: constant.DIGI_PROGRAM,
            },
        },
        batch: {
            type: String,
            // enum: [constant.BATCH.REGULAR, constant.BATCH.INTERIM],
        },
        // employee_id: {
        //     type: String,
        //     unique: true,
        //     required: true,
        //     trim: true,
        // },
        // academic_no: {
        //     type: String,
        //     unique: true,
        //     required: true,
        //     trim: true,
        // },
        email: {
            type: String,
            required: true,
            unique: true,
            sparse: true,
        },
        name: {
            first: {
                type: String,
                trim: true,
            },
            middle: {
                type: String,
                trim: true,
            },
            last: {
                type: String,
                trim: true,
            },
            family: {
                type: String,
                trim: true,
            },
        },
        dob: Date,
        password: String,
        enrollment_year: Date,
        gender: {
            type: String,
            enum: [constant.GENDER.MALE, constant.GENDER.FEMALE],
        },
        mobile: {
            type: Number,
            unique: true,
            sparse: true,
        },
        id_doc: {
            _employee_id_doc: String,
            _college_id_doc: String,
        },
        address: {
            nationality_id: {
                type: String,
                // lowercase: true // Always convert to lowercase
            },
            _nationality_id: {
                type: Schemas.Types.ObjectId,
                ref: constant.COUNTRY,
            },
            _nationality_id_doc: String,
            building: String,
            city: String,
            district: String,
            zip_code: Number,
            unit: String,
            street_no: String,
            passport_no: {
                type: String,
            },
            _address_doc: String,
        },
        contact: [
            {
                relation_type: {
                    type: String,
                },
                name: String,
                email: String,
                mobile: Number,
            },
        ],
        student_docs: {
            _school_certificate_doc: String,
            _entrance_exam_certificate_doc: String,
        },
        office: {
            office_extension: Number,
            office_room_no: String,
        },
        enrollment: {
            _appointment_order_doc: String,
            _admission_order_doc: String,
        },
        qualifications: {
            degree: [
                {
                    degree_name: String,
                    _degree_doc: String,
                },
            ],
            // _position_id: {
            //     type: Schemas.Types.ObjectId,
            //     ref: constant.POSITION
            // },
            // designation: String,
        },
        role: String,
        sub_role: [String],
        programs: [
            {
                name: String,
                _program_id: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.DIGI_PROGRAM,
                },
                no: String,
            },
        ],
        _role_id: {
            type: Schemas.Types.ObjectId,
            ref: constant.ROLE_ASSIGN,
        },
        academic_allocation: [
            {
                allocation_type: {
                    type: String,
                    enum: [constant.PRIMARY, constant.AUXILIARY],
                },
                _program_id: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.DIGI_PROGRAM,
                },
                _department_id: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.DIGI_DEPARTMENT_SUBJECT,
                },
                _department_division_id: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.DEPARTMENT_DIVISIONS,
                },
                _department_subject_id: [
                    {
                        type: Schemas.Types.ObjectId,
                        ref: constant.DIGI_DEPARTMENT_SUBJECT,
                    },
                ],
            },
        ],
        staff_employment_type: {
            type: String,
            enum: [constant.ACADEMIC, constant.ADMINISTRATION, constant.BOTH],
        },
        institution_role: String,
        employment: {
            user_type: {
                type: String,
                enum: [constant.ACADEMIC, constant.ADMINISTRATION, constant.BOTH],
            },
            institution_role: String,
            user_employment_type: {
                type: String,
                enum: [constant.FULL_TIME, constant.PART_TIME],
            },
            user_schedule_type: {
                type: String,
                enum: [constant.BY_DATE, constant.BY_DAY],
            },
            academic_year: String,

            schedule_times: {
                full_time: [
                    {
                        mode: {
                            type: String,
                            enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH],
                        },
                        days: [
                            {
                                type: String,
                                enum: [
                                    constant.DAYS.SUNDAY,
                                    constant.DAYS.MONDAY,
                                    constant.DAYS.TUESDAY,
                                    constant.DAYS.WEDNESDAY,
                                    constant.DAYS.THURSDAY,
                                    constant.DAYS.FRIDAY,
                                    constant.DAYS.SATURDAY,
                                ],
                            },
                        ],
                        start_time: String,
                        end_time: String,
                    },
                ],
                by_date: [
                    {
                        start_date: String,
                        end_date: String,
                        schedule: [
                            {
                                mode: {
                                    type: String,
                                    enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH],
                                },
                                days: [
                                    {
                                        type: String,
                                        enum: [
                                            constant.DAYS.SUNDAY,
                                            constant.DAYS.MONDAY,
                                            constant.DAYS.TUESDAY,
                                            constant.DAYS.WEDNESDAY,
                                            constant.DAYS.THURSDAY,
                                            constant.DAYS.FRIDAY,
                                            constant.DAYS.SATURDAY,
                                        ],
                                    },
                                ],
                                start_time: String,
                                end_time: String,
                            },
                        ],
                    },
                ],
                by_day: [
                    {
                        mode: {
                            type: String,
                            enum: [constant.ONLINE, constant.ON_SITE, constant.BOTH],
                        },
                        days: {
                            type: String,
                        },
                        start_time: String,
                        end_time: String,
                    },
                ],
            },
        },
        verification: {
            // verification status
            email: {
                type: Boolean,
                default: false,
            },
            mobile: {
                type: Boolean,
                default: false,
            },
            data: {
                type: String,
                default: constant.PENDING,
                enum: [
                    constant.VALID,
                    constant.INVALID,
                    constant.PENDING,
                    constant.CORRECT,
                    constant.CORRECTION_REQUIRED,
                    constant.DONE,
                ],
            },
            face: {
                // facial
                type: Boolean,
                default: false,
            },
            finger: {
                // finger_print
                type: Boolean,
                default: false,
            },
        },
        biometric_data: {
            finger: [
                {
                    name: String,
                    image: String,
                    isotemplate: String,
                    active: Boolean,
                },
            ],
            face: [
                {
                    type: String,
                },
            ],
            userRegisterDocument: { type: String },
            facialTrained: { type: Array },
        },
        correction: [{ type: String, default: null }],
        status: {
            type: String,
            // default: constant.ACTIVE,
            enum: [
                constant.IMPORTED,
                constant.PASSWORD_CONFIRMED,
                constant.PROFILE_UPDATED,
                constant.VERIFICATION_DONE,
                constant.DONE,
                constant.ACTIVE,
                constant.INACTIVE,
                constant.BLOCKED,
                constant.COMPLETED,
            ],
        },
        device_type: String,
        last_login_device_type: String,
        web_fcm_token: {
            type: String,
        },
        fcm_token: {
            type: String,
        },
        otp: {
            no: {
                type: Number,
                default: 0,
            },
            expiry_date: {
                type: Date,
                default: Date.now(),
            },
        },
        last_login: {
            type: Date,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        socketEventId: {
            dashboardEventId: { type: String },
            activityEventId: { type: String },
            chatEventId: { type: String },
            sessionEventId: { type: String },
            courseEventId: { type: String },
        },
        logout_time: { type: Date },
        zoomDetails: {
            userId: { type: String },
            startUrl: { type: String },
            joinUrl: { type: String },
            meetingId: { type: String },
            meetingUuid: { type: String },
        },
        originalRegisteredFace: [{ type: String }],
        approvalStatus: { type: String, default: constant.INITIATED },
        academicCourses: [
            {
                programId: {
                    type: Schemas.Types.ObjectId,
                    ref: constant.DIGI_PROGRAM,
                },
                courseIds: [
                    {
                        type: Schemas.Types.ObjectId,
                        ref: constant.DIGI_COURSE,
                    },
                ],
            },
        ],
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.USER, userSchemas);
