const ScheduleAttendanceModel = require('../../models/schedule_attendance');
const CourseSchedule = require('../../models/course_schedule');
const user = require('../../models/user');
const scheduleAnomalySchema = require('../../models/schedule_anomaly');
const institutionSchema = require('../../models/institution');
const disciplinaryRemarksSchema = require('../../disciplinary_remarks/disciplinaryRemarks.model');
const {
    convertToMongoObjectId,
    sendResponse,
    sendResponseWithRequest,
} = require('../../utility/common');
const {
    RETAKE_ALL,
    RETAKE_ABSENT,
    BUZZER,
    SURPRISE_QUIZ,
    COMPLETED,
    RUNNING,
    PRESENT,
    ABSENT,
    LEAVE_TYPE,
    MANUAL,
    PENDING,
    ONGOING,
    TIME_GROUP_BOOKING_TYPE: { REMOTE },
    EXCLUDE,
} = require('../../utility/constants');
const {
    scheduleAttendanceRetake,
    scheduleAttendanceRetakeForAbsentees,
    scheduleAttendanceBuzzer,
    scheduleAttendanceQuiz,
    sendStudentResponseToStaff,
    sendStudentAttendanceToStaff,
} = require('./schedule-attendance.service');
const push = require('../../utility/notification_push');
const { timestampNow, getSignedURL } = require('../../utility/common_functions');
const { SERVICES } = require('../../utility/util_keys');
const { checkRestrictCourse } = require('../../utility/utility.service');
exports.sessionRetake = async (req, res) => {
    try {
        const {
            body: { modeBy, _staff_id, _id, scheduleAttendanceId, isLive, faceAuthentication },
            headers: { _institution_id },
        } = req;
        const existStaffAttendanceCheck = await ScheduleAttendanceModel.find(
            {
                scheduleId: {
                    $in: [convertToMongoObjectId(_id)],
                },
                $or: [
                    {
                        status: RUNNING,
                    },
                    {
                        modeBy: RETAKE_ABSENT,
                    },
                ],
            },
            { _id: 1, status: 1, modeBy: 1 },
        ).lean();
        if (
            existStaffAttendanceCheck &&
            ((Array.isArray(existStaffAttendanceCheck) &&
                existStaffAttendanceCheck.find(
                    (scheduleAttendanceElement) => scheduleAttendanceElement.status === RUNNING,
                )) ||
                (existStaffAttendanceCheck.status && existStaffAttendanceCheck.status === RUNNING))
        ) {
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ALREADY_YOU_HAVE_RUNNING_SESSION'),
                null,
            );
        }
        if (
            modeBy === RETAKE_ABSENT &&
            existStaffAttendanceCheck &&
            ((Array.isArray(existStaffAttendanceCheck) &&
                existStaffAttendanceCheck.find(
                    (scheduleAttendanceElement) =>
                        scheduleAttendanceElement.modeBy === RETAKE_ABSENT,
                )) ||
                (existStaffAttendanceCheck.modeBy &&
                    existStaffAttendanceCheck.modeBy === RETAKE_ABSENT))
        )
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ALREADY_YOU_RETAKE_FOR_ABSENT_COMPLETED'),
                null,
            );

        let responseData = {};
        const updateAlreadyRunningAttendance = await ScheduleAttendanceModel.updateMany(
            { scheduleId: { $in: [convertToMongoObjectId(_id)] }, status: RUNNING },
            {
                $set: {
                    status: COMPLETED,
                },
            },
        ).lean();
        switch (modeBy) {
            case RETAKE_ALL:
                responseData = await scheduleAttendanceRetake({
                    modeBy,
                    _staff_id,
                    _id,
                    _institution_id,
                    isLive,
                    faceAuthentication,
                });
                break;
            case RETAKE_ABSENT:
                responseData = await scheduleAttendanceRetakeForAbsentees({
                    modeBy,
                    _staff_id,
                    _id,
                    _institution_id,
                    isLive,
                    faceAuthentication,
                });
                break;
            case BUZZER:
                responseData = await scheduleAttendanceBuzzer({
                    modeBy,
                    _staff_id,
                    _id,
                    _institution_id,
                    isLive,
                });
                break;
            case SURPRISE_QUIZ:
                responseData = await scheduleAttendanceQuiz({
                    modeBy,
                    _staff_id,
                    _id,
                    _institution_id,
                    scheduleAttendanceId,
                    isLive,
                });
                break;
            default:
                break;
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t(responseData.message),
            responseData.createdAt ? responseData.createdAt : null,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.endSessionRetake = async (req, res) => {
    try {
        const {
            body: { modeBy, _id },
        } = req;

        const scheduleAttendanceResponse = await ScheduleAttendanceModel.findOneAndUpdate(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                modeBy,
                status: RUNNING,
            },
            { $set: { status: COMPLETED } },
        );
        const courseScheduleResponse = await CourseSchedule.updateOne(
            {
                _id: convertToMongoObjectId(_id),
            },
            { $set: { retakeStatus: 0 } },
        );
        if (scheduleAttendanceResponse && courseScheduleResponse) {
            return sendResponseWithRequest(req, res, 200, true, req.t('ATTENDANCE_STOPPED'), null);
        }
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATE_ERROR'), null);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.acceptAttendance = async (req, res) => {
    try {
        const {
            body: { modeBy, _id, _student_id },
        } = req;
        const checkRunningAttendance = await ScheduleAttendanceModel.findOne({
            scheduleId: { $in: [convertToMongoObjectId(_id)] },
            modeBy,
            status: RUNNING,
        });
        if (!checkRunningAttendance)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_RUNNING_ATTENDANCE'),
                null,
            );
        const scheduleAttendanceResponse = await ScheduleAttendanceModel.findOneAndUpdate(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                modeBy,
                status: RUNNING,
            },
            {
                $push: {
                    students: {
                        _student_id: convertToMongoObjectId(_student_id),
                        status: PRESENT,
                        time: new Date(),
                    },
                },
            },
        );
        if (scheduleAttendanceResponse) {
            await CourseSchedule.updateMany(
                {
                    $or: [
                        { _id: convertToMongoObjectId(_id) },
                        { 'merge_with.schedule_id': convertToMongoObjectId(_id) },
                    ],
                    'students._id': convertToMongoObjectId(_student_id),
                },
                {
                    'students.$[i].status': PRESENT,
                    'students.$[i].mode': 'auto',
                    'students.$[i].time': timestampNow(),
                },
                {
                    arrayFilters: [{ 'i._id': convertToMongoObjectId(_student_id) }],
                },
            );
            const getCourseSchedule = await CourseSchedule.findOne(
                {
                    _id: convertToMongoObjectId(_id),
                },
                { socket_port: 1 },
            );
            sendStudentAttendanceToStaff({
                _student_id,
                socket_port: getCourseSchedule.socket_port,
            });
            return sendResponseWithRequest(req, res, 200, true, req.t('ATTENDANCE_ACCEPTED'), null);
        }
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATE_ERROR'), null);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.getStudentStatus = async (req, res) => {
    try {
        const {
            body: { modeBy, _id, _student_id },
            headers: { _institution_calendar_id, _institution_id },
        } = req;

        const scheduleAttendanceResponse = await ScheduleAttendanceModel.findOne(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                modeBy,
                status: RUNNING,
            },
            { students: 1, isLive: 1, quizDetails: 1, faceAuthentication: 1 },
        ).lean();

        const courseSchedule = await CourseSchedule.findOne(
            {
                _id: convertToMongoObjectId(_id),
            },
            {
                uuid: 1,
                'students._id': 1,
                'students.status': 1,
                'students.primaryStatus': 1,
                _program_id: 1,
                year_no: 1,
                level_no: 1,
                term: 1,
                rotation_count: 1,
            },
        ).lean();
        if (!scheduleAttendanceResponse)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_RUNNING_ATTENDANCE'),
                null,
            );
        const { labelName: warningData, restrictCourseAccess } = await checkRestrictCourse({
            _institution_id,
            _institution_calendar_id,
            programId: courseSchedule._program_id,
            courseId: _id,
            yearNo: courseSchedule.year_no,
            levelNo: courseSchedule.level_no,
            term: courseSchedule.term,
            rotationCount: courseSchedule.rotation_count ? courseSchedule.rotation_count : null,
            userId: _student_id,
        });
        if (restrictCourseAccess) {
            return sendResponseWithRequest(req, res, 200, true, req.t('COURSE_RESTRICTED'), null);
        }
        const checkStudentAvailability = scheduleAttendanceResponse.students.find(
            (element) => element._student_id.toString() === _student_id.toString(),
        );
        const lmsStatus = [LEAVE_TYPE.LEAVE, LEAVE_TYPE.ONDUTY, LEAVE_TYPE.PERMISSION];
        const checkPrimaryStatus = courseSchedule.students.find(
            (studentId) =>
                studentId._id.toString() === _student_id.toString() &&
                !lmsStatus.includes(studentId.status.toString()),
        );
        if (!checkPrimaryStatus)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_RUNNING_ATTENDANCE'),
                null,
            );
        return sendResponseWithRequest(req, res, 200, true, req.t('AVAILABLE_STATUS'), {
            status: !!checkStudentAvailability,
            ...(checkPrimaryStatus &&
                modeBy === RETAKE_ABSENT && {
                    primaryStatus: checkPrimaryStatus.primaryStatus,
                }),
            uuid: courseSchedule.uuid,
            scheduleAttendanceId: scheduleAttendanceResponse._id,
            isLive: scheduleAttendanceResponse.isLive,
            ...(scheduleAttendanceResponse.quizDetails &&
                scheduleAttendanceResponse.quizDetails.quiz && {
                    quiz: scheduleAttendanceResponse.quizDetails.quiz,
                }),
            faceAuthentication:
                typeof scheduleAttendanceResponse.faceAuthentication !== 'undefined'
                    ? scheduleAttendanceResponse.faceAuthentication
                    : true,
        });
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.attendanceReport = async (req, res) => {
    try {
        const {
            body: { _id, status },
        } = req;

        const scheduleAttendance = await ScheduleAttendanceModel.find(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                status: { $exists: true },
            },
            {
                scheduleId: 1,
                modeBy: 1,
                status: 1,
                'students.status': 1,
                'students.primaryStatus': 1,
                'students._student_id': 1,
                'students.time': 1,
                createdAt: 1,
                isCompared: 1,
                _staff_id: 1,
                updatedAt: 1,
                updateTime: 1,
                attendanceCondition: 1,
            },
        )
            .populate({ path: '_staff_id', select: { name: 1, user_id: 1 } })
            .sort({ createdAt: -1 })
            .lean();
        const courseScheduleAttendance = await CourseSchedule.findOne(
            {
                _id: convertToMongoObjectId(_id),
            },
            {
                students: 1,
                staffs: 1,
                sessionDetail: 1,
                merge_status: 1,
                merge_with: 1,
            },
        )
            .populate({ path: 'students.tardisId', select: { name: 1, short_code: 1 } })
            .populate('merge_with.schedule_id')
            .lean();
        const students = courseScheduleAttendance.students.filter(
            (studentElement) => studentElement.status !== EXCLUDE,
        );
        const staffs = courseScheduleAttendance.staffs;
        let scheduleIds = [courseScheduleAttendance._id];
        if (courseScheduleAttendance.merge_status) {
            mergeSchedules = courseScheduleAttendance.merge_with.map(
                (element) => element.schedule_id,
            );
            scheduleIds = scheduleIds.concat(mergeSchedules);
            for (csData of courseScheduleAttendance.merge_with) {
                for (studentsList of csData.schedule_id.students) {
                    if (
                        studentsList.status !== EXCLUDE &&
                        students.findIndex(
                            (element) => element._id.toString() === studentsList._id.toString(),
                        ) === -1
                    ) {
                        students.push(studentsList);
                    }
                }
                for (staffList of csData.schedule_id.staffs) {
                    if (
                        staffs.findIndex(
                            (element) =>
                                element._staff_id.toString() === staffList._staff_id.toString(),
                        ) === -1
                    ) {
                        staffs.push(staffList);
                    }
                }
            }
        }

        const studentIds = students.map((element) => element._id);
        const staffIds = staffs.map((element) => element._staff_id);
        const usersList = studentIds.concat(staffIds);
        const getUserIds = await user
            .find(
                {
                    _id: { $in: usersList },
                },
                {
                    isActive: 1,
                    user_id: 1,
                },
            )
            .lean();
        const disciplinaryRemarkComments = await disciplinaryRemarksSchema
            .find(
                {
                    scheduleId: convertToMongoObjectId(_id),
                    studentId: { $in: studentIds.map(convertToMongoObjectId) },
                    isDeleted: false,
                    isActive: true,
                },
                { comment: 1, studentId: 1, tardisId: 1 },
            )
            .populate({ path: 'tardisId', select: { name: 1, short_code: 1 } })
            .lean();
        let filteredStudents = [];
        for (const studentElement of students) {
            const matchedStudent = getUserIds.find(
                (userElement) =>
                    userElement._id.toString() === studentElement._id.toString() &&
                    userElement.isActive,
            );
            if (matchedStudent) {
                studentElement.user_id = matchedStudent.user_id;
                const matchedRemark = disciplinaryRemarkComments.find(
                    (remarkCommentElement) =>
                        remarkCommentElement.studentId.toString() ===
                            studentElement._id.toString() &&
                        studentElement?.tardisId?._id?.toString() ===
                            remarkCommentElement.tardisId?._id?.toString(),
                );
                if (matchedRemark) {
                    studentElement.comment = matchedRemark.comment;
                    studentElement.remarkId = matchedRemark._id;
                    studentElement.tardisId = { ...matchedRemark.tardisId };
                }
                filteredStudents.push(studentElement);
            }
        }
        staffs.forEach((element) => {
            const getUserId = getUserIds.find(
                (elementEntry) => elementEntry._id.toString() === element._staff_id.toString(),
            );
            element.user_id = getUserId.user_id;
        });
        if (status) {
            filteredStudents = filteredStudents.filter((element) => element.status === status);
        }
        courseScheduleAttendance.students = filteredStudents;
        courseScheduleAttendance.staffs = staffs;
        const scheduleAnomalyStudents = await scheduleAnomalySchema.distinct('studentId', {
            isDeleted: false,
            scheduleId: convertToMongoObjectId(_id),
        });
        return sendResponseWithRequest(req, res, 200, true, req.t('DATA_RETRIEVED'), {
            isCompared:
                scheduleAttendance && scheduleAttendance.length && scheduleAttendance[0].isCompared
                    ? scheduleAttendance[0].isCompared
                    : null,
            attendanceCondition:
                scheduleAttendance &&
                scheduleAttendance.length &&
                scheduleAttendance[0].attendanceCondition
                    ? scheduleAttendance[0].attendanceCondition
                    : null,
            scheduleAttendance,
            courseScheduleAttendance,
            scheduleAnomalyStudents,
        });
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.submitQuiz = async (req, res) => {
    try {
        const {
            body: { _id, _staff_id, questions, quizTitle },
        } = req;

        const courseScheduleData = await CourseSchedule.findOne({
            _id: convertToMongoObjectId(_id),
        });
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_SCHEDULES_FOUND'),
                null,
            );
        let scheduleIds = [courseScheduleData._id];
        let mergeSchedules = [];
        if (courseScheduleData.merge_status) {
            mergeSchedules = courseScheduleData.merge_with.map((element) => element.schedule_id);
            scheduleIds = scheduleIds.concat(mergeSchedules);
        }
        const createAttendance = await ScheduleAttendanceModel.create({
            modeBy: SURPRISE_QUIZ,
            _staff_id,
            scheduleId: scheduleIds,
            'quizDetails.quiz': {
                ...(quizTitle && {
                    quizTitle,
                }),
                questions,
            },
        });
        if (!createAttendance)
            return sendResponseWithRequest(req, res, 200, true, req.t('ERROR'), {});
        return sendResponseWithRequest(req, res, 200, true, req.t('QUESTIONS_ADDED'), {
            scheduleAttendanceId: createAttendance._id,
            quiz: createAttendance.quizDetails.quiz,
        });
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.getQuestions = async (req, res) => {
    try {
        const {
            body: { _id },
        } = req;

        const courseScheduleData = await CourseSchedule.findOne({
            _id: convertToMongoObjectId(_id),
        }).lean();
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_SCHEDULES_FOUND'),
                null,
            );

        const getRunningQuiz = await ScheduleAttendanceModel.findOne({
            status: RUNNING,
            modeBy: SURPRISE_QUIZ,
            scheduleId: { $in: [convertToMongoObjectId(_id)] },
        }).lean();
        if (!getRunningQuiz)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_RUNNING_ATTENDANCE'),
                {},
            );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('DATA_RETRIEVED'),
            getRunningQuiz.quizDetails.quiz,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.studentSubmitQuiz = async (req, res) => {
    try {
        const {
            body: { _id, questions, _student_id },
        } = req;

        const courseScheduleData = await ScheduleAttendanceModel.findOne({
            scheduleId: { $in: [convertToMongoObjectId(_id)] },
            modeBy: SURPRISE_QUIZ,
            status: RUNNING,
        }).lean();
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_RUNNING_ATTENDANCE'),
                null,
            );

        const addStudentResponse = await ScheduleAttendanceModel.updateOne(
            {
                modeBy: SURPRISE_QUIZ,
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                status: RUNNING,
            },
            {
                $push: {
                    // commenting because, we are not storing student answer details in to DB
                    /*   'quizDetails.studentsResponse': {
                        _student_id: convertToMongoObjectId(_student_id),
                        questions,
                    }, */
                    students: {
                        _student_id: convertToMongoObjectId(_student_id),
                        status: PRESENT,
                        time: new Date(),
                    },
                },
            },
        );
        if (!addStudentResponse)
            return sendResponseWithRequest(req, res, 200, true, req.t('UPDATE_ERROR'), {});
        const courseSchedules = await CourseSchedule.findOne(
            {
                _id: convertToMongoObjectId(_id),
            },
            { socket_port: 1 },
        ).lean();
        sendStudentResponseToStaff({
            _student_id,
            questions,
            _staff_id: courseSchedules.socket_port,
        });
        return sendResponseWithRequest(req, res, 200, true, req.t('QUESTIONS_ADDED'), {});
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.submittedQuizResult = async (req, res) => {
    try {
        const {
            body: { _id },
        } = req;

        const courseScheduleData = await CourseSchedule.findOne({
            _id: convertToMongoObjectId(_id),
        }).lean();
        if (!courseScheduleData)
            return sendResponse(res, 200, true, req.t('THERE_IS_NO_SCHEDULES_FOUND'), null);

        const getRunningQuiz = await ScheduleAttendanceModel.findOne(
            {
                modeBy: SURPRISE_QUIZ,
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
            },
            {
                quizDetails: 1,
                modeBy: 1,
                status: 1,
            },
        ).lean();
        if (!getRunningQuiz)
            return sendResponse(res, 200, true, req.t('THERE_IS_NO_RUNNING_ATTENDANCE'), {});
        return sendResponse(res, 200, true, req.t('DATA_RETRIEVED'), {
            // commenting because, we are not storing student answer details in to DB
            /*  studentSubmittedQuestions: getRunningQuiz.quizDetails.studentsResponse, */
            quiz: getRunningQuiz.quizDetails.quiz,
        });
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.attendanceStudentUpdate = async (req, res) => {
    try {
        const {
            body: { userIds, _id, scheduleAttendanceIds },
        } = req;

        const courseScheduleData = await CourseSchedule.findOne(
            {
                _id: convertToMongoObjectId(_id),
            },
            { students: 1, merge_status: 1, merge_with: 1 },
        )
            .populate('merge_with.schedule_id')
            .lean();
        if (!courseScheduleData)
            return sendResponse(res, 200, true, req.t('THERE_IS_NO_SCHEDULES_FOUND'), null);
        const bulkUpdate = [];
        if (courseScheduleData.merge_status) {
            for (const mergeWith of courseScheduleData.merge_with) {
                for (const studentElement of mergeWith.schedule_id.students) {
                    const checkStudent = userIds.find(
                        (userIdElement) =>
                            userIdElement._student_id.toString() === studentElement._id.toString(),
                    );
                    if (checkStudent) {
                        studentElement.status = checkStudent.status;
                    }
                }
                bulkUpdate.push({
                    updateOne: {
                        filter: {
                            isDeleted: false,
                            _id: convertToMongoObjectId(mergeWith.schedule_id._id),
                        },
                        update: {
                            $set: { students: mergeWith.schedule_id.students },
                        },
                    },
                });
            }
        }

        for (const studentElement of courseScheduleData.students) {
            const checkStudent = userIds.find(
                (userIdElement) =>
                    userIdElement._student_id.toString() === studentElement._id.toString(),
            );
            if (checkStudent) {
                studentElement.status = checkStudent.status;
            }
        }
        bulkUpdate.push({
            updateOne: {
                filter: {
                    isDeleted: false,
                    _id: convertToMongoObjectId(_id),
                },
                update: {
                    $set: { students: courseScheduleData.students },
                },
            },
        });
        const doc = await CourseSchedule.bulkWrite(bulkUpdate);
        const attendanceIds = scheduleAttendanceIds.map((element) =>
            convertToMongoObjectId(element),
        );
        const comparedIds = await ScheduleAttendanceModel.updateMany(
            {
                _id: { $in: attendanceIds },
            },
            { $set: { isCompared: true } },
            { multi: true },
        );
        if (!doc) return sendResponse(res, 404, false, 'Unable to update', 'Unable to update');
        return sendResponse(res, 200, true, 'Updated successfully', 'Updated successfully');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.getStudentAnsweredQuestions = async (req, res) => {
    try {
        const {
            params: { _student_id, _id, scheduleAttendanceId },
        } = req;
        const getAnsweredQuiz = await ScheduleAttendanceModel.findOne(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                _id: convertToMongoObjectId(scheduleAttendanceId),
            },
            {
                quizDetails: 1,
                modeBy: 1,
                status: 1,
            },
        ).lean();
        if (!getAnsweredQuiz && !getAnsweredQuiz.quizDetails)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_QUIZ_DETAILS_FOUND'),
                {},
            );
        // commenting because, we are not storing student answer details in to DB
        /*  const getParticularStudentAnswers = getAnsweredQuiz.quizDetails.studentsResponse.find(
            (element) => element._student_id.toString() === _student_id.toString(),
        ); */
        return sendResponseWithRequest(req, res, 200, true, req.t('DATA_RETRIEVED'), {
            // commenting because, we are not storing student answer details in to DB
            /* studentResponse: getParticularStudentAnswers, */
            quiz: getAnsweredQuiz.quizDetails.quiz,
        });
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.getStudentAttendanceReport = async (req, res) => {
    try {
        const {
            params: { _student_id, _id },
        } = req;
        const getScheduledAttendance = await ScheduleAttendanceModel.find(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
                // status: COMPLETED,
            },
            {
                quizDetails: 1,
                scheduleId: 1,
                modeBy: 1,
                status: 1,
                _staff_id: 1,
                students: 1,
                createdAt: 1,
                isCompared: 1,
            },
        ).lean();

        if (!getScheduledAttendance)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('THERE_IS_NO_SCHEDULES_FOUND'),
                {},
            );

        getScheduledAttendance.forEach((element) => {
            const checkStudent = element.students.find(
                (elementEntry) => elementEntry._student_id.toString() === _student_id.toString(),
            );
            element.students = checkStudent;
        });
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('DATA_RETRIEVED'),
            getScheduledAttendance,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.updateQuiz = async (req, res) => {
    try {
        const {
            body: { questions, quizTitle },
            params: { scheduleAttendanceId },
        } = req;

        const scheduleAttendanceData = await ScheduleAttendanceModel.findOne({
            _id: convertToMongoObjectId(scheduleAttendanceId),
        });
        if (!scheduleAttendanceData)
            return sendResponse(res, 200, true, req.t('THERE_IS_NO_SCHEDULES_FOUND'), null);

        const updateAttendance = await ScheduleAttendanceModel.updateOne(
            {
                _id: convertToMongoObjectId(scheduleAttendanceId),
            },
            {
                $set: {
                    'quizDetails.quiz.quizTitle': quizTitle,
                    'quizDetails.quiz.questions': questions,
                },
            },
        );
        if (!updateAttendance) return sendResponse(res, 200, true, req.t('ERROR'), {});
        const getAttendanceData = await ScheduleAttendanceModel.findOne({
            _id: convertToMongoObjectId(scheduleAttendanceId),
        });
        return sendResponse(res, 200, true, req.t('QUESTIONS_ADDED'), {
            scheduleAttendanceId: getAttendanceData._id,
            quiz: getAttendanceData.quizDetails.quiz,
        });
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.scheduleStudentFaceAnomalyAdd = async (req, res) => {
    try {
        const {
            body: { scheduleId, faceData, studentId },
            headers: { _institution_id },
        } = req;
        const institutionData = await institutionSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(SERVICES.REACT_APP_INSTITUTION_ID),
                },
                { manualAnomaly: 1 },
            )
            .lean();
        let pureMode = false;
        if (institutionData && !institutionData.manualAnomaly) {
            const scheduleData = await CourseSchedule.findOne(
                { _id: convertToMongoObjectId(scheduleId) },
                { mode: 1 },
            ).lean();
            if (scheduleData && scheduleData.mode && scheduleData.mode === REMOTE) pureMode = true;
        } else pureMode = true;
        const studentScheduleAnomaly = await scheduleAnomalySchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            scheduleId: convertToMongoObjectId(scheduleId),
            studentId: convertToMongoObjectId(studentId),
            faceData,
            pure: pureMode,
        });
        if (studentScheduleAnomaly)
            return sendResponseWithRequest(req, res, 200, true, 'Face Anomaly Stored');
        return sendResponseWithRequest(req, res, 410, false, 'Face Anomaly Not Stored');
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.scheduleStudentFaceAnomaly = async (req, res) => {
    try {
        const {
            query: { scheduleId, studentId },
            headers: { _institution_id },
        } = req;
        const studentScheduleAnomaly = await scheduleAnomalySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                scheduleId: convertToMongoObjectId(scheduleId),
                studentId: convertToMongoObjectId(studentId),
            },
            {
                faceData: 1,
            },
        );
        if (studentScheduleAnomaly)
            return sendResponse(res, 200, true, 'Schedule Face Anomaly', studentScheduleAnomaly);
        return sendResponse(res, 410, false, 'Face Anomaly Not Stored');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.updateManualAttendance = async (req, res) => {
    try {
        const {
            body: { _id, _staff_id, manualType, manualAttendanceStatus, students },
            headers: { _institution_id },
        } = req;
        const updateStudents = [];
        if (students.length) {
            students.filter((studentElement) => {
                if (studentElement.status === PRESENT || studentElement.status === ABSENT) {
                    updateStudents.push({
                        _student_id: studentElement._id,
                        status: manualAttendanceStatus,
                        time: new Date(),
                    });
                } else {
                    updateStudents.push({
                        _student_id: studentElement._id,
                        status: studentElement.status,
                        time: new Date(),
                    });
                }
            });
        }
        let manualAttendanceData;
        if (manualType === 'single') {
            manualAttendanceData = await ScheduleAttendanceModel.findOne(
                {
                    modeBy: MANUAL,
                    scheduleId: [_id],
                },
                {
                    _id: -1,
                },
            ).lean();
            if (manualAttendanceData) {
                const deleteManualAttendance = await ScheduleAttendanceModel.findByIdAndDelete({
                    _id: convertToMongoObjectId(manualAttendanceData._id),
                });
            }
        }
        const updateAttendance = await ScheduleAttendanceModel.create({
            modeBy: MANUAL,
            _staff_id: convertToMongoObjectId(_staff_id),
            scheduleId: [_id],
            students: updateStudents,
            status: COMPLETED,
            updateTime: new Date(),
        });

        manualAttendanceData = await ScheduleAttendanceModel.findOne(
            {
                modeBy: MANUAL,
                scheduleId: [_id],
            },
            {
                _id: -1,
                students: 1,
            },
        ).lean();
        const courseScheduleData = await CourseSchedule.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(_id),
                isDeleted: false,
                isActive: true,
            },
            {
                students: 1,
            },
        ).lean();
        const updateCourseStudent = [];
        if (manualAttendanceData.students.length && courseScheduleData.students.length) {
            manualAttendanceData.students.filter((manualElement) => {
                const courseData = courseScheduleData.students.find(
                    (courseElement) =>
                        courseElement._id.toString() === manualElement._student_id.toString(),
                );
                if (courseData) {
                    courseData.status = manualElement.status;
                    updateCourseStudent.push(courseData);
                }
            });
        }
        if (updateCourseStudent && updateCourseStudent.length) {
            const updateCourseSchedule = await CourseSchedule.updateOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    $set: { students: updateCourseStudent },
                },
            );
        }
        if (!updateAttendance) return sendResponse(res, 200, true, req.t('ERROR'));
        return sendResponseWithRequest(req, res, 200, true, req.t('DATA_UPDATED'));
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.updateChangingManualAttendance = async (req, res) => {
    try {
        const {
            body: { scheduleId, manualAttendanceId, students, staffId },
        } = req;
        const courseScheduleData = await CourseSchedule.findById(
            {
                _id: convertToMongoObjectId(scheduleId),
            },
            { status: 1 },
        );

        if (!courseScheduleData.status) {
            sendResponseWithRequest(req, res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'));
        }
        if (courseScheduleData.status === PENDING)
            sendResponseWithRequest(req, res, 200, false, req.t('SESSION_NOT_YET_STARTED'));
        if (courseScheduleData.status === ONGOING || courseScheduleData.status === COMPLETED) {
            const updateAttendanceData = await ScheduleAttendanceModel.updateOne(
                {
                    _id: convertToMongoObjectId(manualAttendanceId),
                    scheduleId: { $in: scheduleId },
                },
                {
                    $set: {
                        _staff_id: convertToMongoObjectId(staffId),
                        updateTime: new Date(),
                        students,
                        isCompared: manualAttendanceId,
                    },
                },
            );
            const updateCourseStudent = [];
            const courseScheduleData = await CourseSchedule.findOne(
                {
                    _id: convertToMongoObjectId(scheduleId),
                },
                {
                    students: 1,
                },
            );
            if (students.length && courseScheduleData.students.length) {
                students.filter((studentElement) => {
                    const courseData = courseScheduleData.students.find(
                        (courseElement) =>
                            courseElement._id.toString() === studentElement._student_id.toString(),
                    );
                    if (courseData) {
                        courseData.status = studentElement.status;
                        updateCourseStudent.push(courseData);
                    }
                });
            }
            if (updateCourseStudent && updateCourseStudent.length) {
                const updateCourseSchedule = await CourseSchedule.updateOne(
                    {
                        _id: convertToMongoObjectId(scheduleId),
                        isDeleted: false,
                        isActive: true,
                    },
                    {
                        $set: { students: updateCourseStudent },
                    },
                );
            }
            if (!updateAttendanceData)
                return sendResponseWithRequest(req, res, 200, true, req.t('ERROR'));
            return sendResponseWithRequest(req, res, 200, true, req.t('DATA_UPDATED'));
        }
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH', error.toString()),
        );
    }
};

exports.anomalyStudentScheduleList = async (req, res) => {
    try {
        const {
            query: { studentId },
            headers: { _institution_id },
        } = req;
        console.time('studentFind');
        const studentFind = await user.findOne(
            { user_id: studentId },
            { _id: 1, gender: 1, name: 1 },
        );
        console.timeEnd('studentFind');
        if (!studentFind) return sendResponse(res, 404, false, 'User Not Found');
        console.time('studentScheduleAnomaly');
        const studentScheduleAnomaly = await scheduleAnomalySchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    studentId: convertToMongoObjectId(studentFind._id),
                },
                {
                    scheduleId: 1,
                    pure: 1,
                },
            )
            .populate({
                path: 'scheduleId',
                select: {
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    status: 1,
                    type: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    title: 1,
                    'staffs.staff_name': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                },
            })
            .lean();
        console.timeEnd('studentScheduleAnomaly');
        if (!studentScheduleAnomaly)
            return sendResponse(res, 410, false, 'Face Anomaly Not Stored');
        const studentSchedule = [];
        for (scheduleElement of studentScheduleAnomaly) {
            if (scheduleElement.scheduleId && scheduleElement.scheduleId._id) {
                const scheduleIndex = studentSchedule.findIndex(
                    (studentScheduleElement) =>
                        studentScheduleElement.scheduleId.toString() ===
                        scheduleElement.scheduleId._id.toString(),
                );
                if (scheduleIndex === -1) {
                    studentSchedule.push({
                        // studentId: studentFind._id,
                        // studentGender: studentFind.gender,
                        scheduleId: scheduleElement.scheduleId._id.toString(),
                        ...scheduleElement.scheduleId,
                        faceData: [scheduleElement._id],
                        pure: scheduleElement.pure ? scheduleElement.pure : false,
                    });
                } else {
                    studentSchedule[scheduleIndex].faceData.push(scheduleElement._id);
                }
            }
        }
        return sendResponse(res, 200, true, 'Schedule Face Anomaly', {
            studentId: studentFind._id,
            studentGender: studentFind.gender,
            studentName: studentFind.name,
            studentSchedule,
        });
    } catch (error) {
        console.log(error);
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.studentFaceAnomaly = async (req, res) => {
    try {
        const {
            query: { faceId, anomalyPassKey },
        } = req;
        const institutionData = await institutionSchema.findOne(
            {
                _id: convertToMongoObjectId(SERVICES.REACT_APP_INSTITUTION_ID),
            },
            { anomalyPassKey: 1 },
        );
        if (
            !institutionData ||
            !anomalyPassKey ||
            (institutionData &&
                institutionData.anomalyPassKey &&
                institutionData.anomalyPassKey.toString() !== anomalyPassKey.toString())
        ) {
            return sendResponse(res, 410, false, 'Face Anomaly PassKey Not Matching');
        }
        const studentScheduleAnomaly = await scheduleAnomalySchema
            .findOne({ _id: convertToMongoObjectId(faceId) }, { faceData: 1 })
            .lean();
        if (studentScheduleAnomaly)
            return sendResponse(res, 200, true, 'Student Face Anomaly', studentScheduleAnomaly);
        return sendResponse(res, 410, false, 'Face Anomaly Not Found');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.studentFaceAnomalyPure = async (req, res) => {
    try {
        const {
            body: { /* scheduleId, */ studentId },
        } = req;
        const studentScheduleAnomaly = await scheduleAnomalySchema.updateMany(
            {
                // scheduleId: convertToMongoObjectId(scheduleId),
                studentId: convertToMongoObjectId(studentId),
            },
            { $set: { pure: true } },
        );
        if (studentScheduleAnomaly)
            return sendResponse(res, 200, true, 'Schedule Face Anomaly Purified');
        return sendResponse(res, 410, false, 'Schedule Face Anomaly Not Purified');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.anomalyStudentScheduleDayWise = async (req, res) => {
    try {
        const {
            query: { filterDate },
            headers: { _institution_id },
        } = req;
        const filterDateKey = new Date(filterDate);
        filterDateKey.setHours(0, 0, 0, 0);
        const startOfDay = filterDateKey.toISOString();
        filterDateKey.setHours(23, 59, 59, 999);
        const endOfDay = filterDateKey.toISOString();
        console.time('studentScheduleAnomaly');
        const studentScheduleAnomaly = await scheduleAnomalySchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    createdAt: {
                        $gte: startOfDay,
                        $lte: endOfDay,
                    },
                },
                {
                    scheduleId: 1,
                    pure: 1,
                    studentId: 1,
                    createdAt: 1,
                },
            )
            .populate({
                path: 'studentId',
                select: {
                    name: 1,
                    gender: 1,
                    email: 1,
                    user_id: 1,
                },
            })
            .populate({
                path: 'scheduleId',
                select: {
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                    status: 1,
                    type: 1,
                    term: 1,
                    year_no: 1,
                    level_no: 1,
                    program_name: 1,
                    course_name: 1,
                    course_code: 1,
                    title: 1,
                    'staffs.staff_name': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                },
            })
            .lean();
        console.timeEnd('studentScheduleAnomaly');
        if (!studentScheduleAnomaly)
            return sendResponse(res, 410, false, 'Face Anomaly Not Stored');
        const studentSchedule = [];
        for (scheduleElement of studentScheduleAnomaly) {
            if (scheduleElement.scheduleId && scheduleElement.scheduleId._id) {
                const scheduleIndex = studentSchedule.findIndex(
                    (studentScheduleElement) =>
                        studentScheduleElement.scheduleId.toString() ===
                        scheduleElement.scheduleId._id.toString(),
                );
                if (scheduleIndex === -1) {
                    studentSchedule.push({
                        scheduleId: scheduleElement.scheduleId._id.toString(),
                        createdAt: scheduleElement.createdAt,
                        ...scheduleElement.scheduleId,
                        studentDatas: [
                            {
                                ...scheduleElement.studentId,
                                faceData: [scheduleElement._id],
                                pure: scheduleElement.pure ? scheduleElement.pure : false,
                            },
                        ],
                    });
                } else {
                    const studentIdex = studentSchedule[scheduleIndex].studentDatas.findIndex(
                        (studentElement) =>
                            studentElement.user_id.toString() === scheduleElement.studentId.user_id,
                    );
                    if (studentIdex !== -1) {
                        studentSchedule[scheduleIndex].studentDatas[studentIdex].faceData.push(
                            scheduleElement._id,
                        );
                        if (scheduleElement.pure)
                            studentSchedule[scheduleIndex].studentDatas[studentIdex].pure =
                                scheduleElement.pure;
                    } else {
                        studentSchedule[scheduleIndex].studentDatas.push({
                            ...scheduleElement.studentId,
                            faceData: [scheduleElement._id],
                            pure: scheduleElement.pure ? scheduleElement.pure : false,
                        });
                    }
                }
            }
        }
        return sendResponse(res, 200, true, 'Schedule Face Anomaly', studentSchedule);
    } catch (error) {
        console.log(error);
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.studentFaceAnomalyLock = async (req, res) => {
    try {
        const {
            body: { faceId },
        } = req;
        const studentScheduleAnomaly = await scheduleAnomalySchema.updateOne(
            {
                _id: convertToMongoObjectId(faceId),
            },
            { $set: { pure: false } },
        );
        console.log(studentScheduleAnomaly);
        if (studentScheduleAnomaly) return sendResponse(res, 200, true, 'Schedule Restricted');
        return sendResponse(res, 410, false, 'Schedule Restricted');
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.uploadScheduleAttachment = async (req, res) => {
    try {
        const { file } = req.body;
        const name = file.split('/').pop();
        sendResponse(res, 200, true, 'DATA_RETRIEVED', {
            url: file,
            signedUrl: await getSignedURL(file),
            name,
        });
    } catch (error) {
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};
