const { Schema, model } = require('mongoose');
const { GUEST_USER } = require('../utility/constants');

const guestUserSchema = new Schema(
    {
        name: {
            first: { type: String, required: true },
            last: { type: String },
        },
        email: { type: String, required: true },
        mobile: { type: Number },
        isActive: { type: Boolean, required: true },
        password: { type: String },
        module: [{ type: String }],
        userType: {
            type: String,
            required: true,
        },
        isDeleted: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(GUEST_USER, guestUserSchema);
