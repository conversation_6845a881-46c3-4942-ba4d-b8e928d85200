const sessionRatingsSchema = require('../../models/sessionRatings');
const { groupBy } = require('lodash');
const { convertToMongoObjectId } = require('../../utility/common');
const { getAverageOfRatings } = require('./sessionRating.service');
const { getPaginationValues } = require('../../utility/pagination');

const createSessionRating = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            programId,
            courseId,
            year,
            level,
            term,
            scheduleId,
            feedBack,
            rating,
            studentId,
            staffId,
            rotationCount,
        } = body;
        const sessionRating = await sessionRatingsSchema.create({
            institutionId: _institution_id,
            institutionCalendarId,
            programId,
            courseId,
            year,
            level,
            term,
            scheduleId,
            feedBack,
            rating,
            studentId,
            staffId,
            rotationCount,
        });
        if (!sessionRating) return { statusCode: 410, message: 'Create Session Rating Failed' };
        return { statusCode: 200, message: 'Created Session Rating' };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const studentScheduleRatingData = async ({ query = {} }) => {
    try {
        const { scheduleId, studentId } = query;
        const ratingData = await sessionRatingsSchema
            .findOne(
                {
                    isDeleted: false,
                    scheduleId,
                    studentId,
                },
                { rating: 1, feedBack: 1 },
            )
            .lean();
        if (!ratingData) return { statusCode: 404, message: 'Rating Data Not Found' };

        return { statusCode: 200, message: 'Student Rating Data', data: ratingData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const updateSessionRating = async ({ query = {}, body = {} }) => {
    try {
        const { ratingId } = query;
        const { feedBack, rating } = body;
        const updatedRating = await sessionRatingsSchema.updateOne(
            { isDeleted: false, _id: convertToMongoObjectId(ratingId) },
            { $set: { ...(feedBack && { feedBack }), ...(rating && { rating }) } },
        );
        if (!updatedRating) return { statusCode: 410, message: 'Update Session Rating Failed' };
        return { statusCode: 200, message: 'Updated Session Rating' };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getSessionRatingsData = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { scheduleId } = query;
        const sessionRatingsData = await sessionRatingsSchema
            .find(
                {
                    institutionId: _institution_id,
                    scheduleId: convertToMongoObjectId(scheduleId),
                    isDeleted: false,
                },
                {
                    feedBack: 1,
                    rating: 1,
                },
            )
            .lean();
        return { statusCode: 200, message: 'Session Ratings Data', data: sessionRatingsData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const calculateSessionRatings = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            courseId,
            scheduleId,
            staffId,
            rating,
            rotationCount,
            programId,
            year,
            level,
            term,
        } = query;
        const ratingFilter = Number(rating);
        const sessionRatingsData = await sessionRatingsSchema
            .find(
                {
                    isDeleted: false,
                    institutionId: _institution_id,
                    ...(courseId && { courseId }),
                    ...(scheduleId && { scheduleId }),
                    ...(staffId && { staffId: convertToMongoObjectId(staffId) }),
                    ...(ratingFilter &&
                        ratingFilter >= 1 &&
                        ratingFilter <= 5 && { rating: ratingFilter }),
                    ...(programId && { programId }),
                    ...(year && { year }),
                    ...(level && { level }),
                    ...(term && { term }),
                    ...(rotationCount && { rotationCount }),
                    ...(institutionCalendarId && { institutionCalendarId }),
                },
                {
                    feedBack: 1,
                    rating: 1,
                },
            )
            .lean();
        const ratingsCount = sessionRatingsData?.length || 0;
        if (!ratingsCount) {
            return {
                statusCode: 404,
                message: 'Session Ratings Data Not Found',
                data: sessionRatingsData,
            };
        }
        let sumOfRatings = 0;
        let sumOfFeedBack = 0;
        sessionRatingsData.forEach((ratingElement) => {
            sumOfRatings += ratingElement?.rating || 0;
            sumOfFeedBack += ratingElement?.feedBack ? 1 : 0;
        });
        const average = getAverageOfRatings({ sumOfRatings, ratingsCount });

        return {
            statusCode: 200,
            message: 'Session Ratings Data',
            data: { average, ratingsCount, sumOfFeedBack },
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const getSessionFeedbacks = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            courseId,
            scheduleId,
            staffId,
            programId,
            year,
            level,
            term,
            rotationCount,
            rating,
        } = query;
        const { limit, skip, pageNo } = getPaginationValues({
            pageNo: query.pageNo,
            limit: query.limit,
        });
        const ratingFilter = Number(rating);
        const filter = {
            isDeleted: false,
            institutionId: _institution_id,
            feedBack: { $ne: null },
            ...(courseId && { courseId }),
            ...(scheduleId && { scheduleId }),
            ...(staffId && { staffId: convertToMongoObjectId(staffId) }),
            ...(programId && { programId }),
            ...(year && { year }),
            ...(level && { level }),
            ...(term && { term }),
            ...(rotationCount && { rotationCount }),
            ...(institutionCalendarId && { institutionCalendarId }),
            ...(ratingFilter && ratingFilter >= 1 && ratingFilter <= 5 && { rating: ratingFilter }),
        };
        const totalFeedbacks = await sessionRatingsSchema.countDocuments(filter);
        const feedbacks = await sessionRatingsSchema
            .find(filter, { feedBack: 1, rating: 1 })
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean();
        return {
            statusCode: 200,
            message: 'Session Feedback List',
            data: {
                feedbacks,
                totalFeedbacks,
                currentPage: pageNo,
                totalPages: Math.ceil(totalFeedbacks / limit),
            },
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const calculateMultipleSchedulesRating = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { scheduleIds = [] } = body;
        const sessionRatingsData = await sessionRatingsSchema
            .find(
                {
                    isDeleted: false,
                    institutionId: _institution_id,
                    scheduleId: { $in: scheduleIds.map(convertToMongoObjectId) },
                },
                {
                    feedBack: 1,
                    rating: 1,
                    scheduleId: 1,
                },
            )
            .lean();
        if (!sessionRatingsData?.length) {
            return {
                statusCode: 404,
                message: 'Schedule Ratings Data Not Found',
                data: sessionRatingsData,
            };
        }
        const groupedScheduleRatings = groupBy(sessionRatingsData, 'scheduleId');
        const data = [];
        for (const [scheduleId, schedules] of Object.entries(groupedScheduleRatings)) {
            const ratingsCount = schedules?.length || 0;
            if (ratingsCount) {
                let sumOfRatings = 0;
                let sumOfFeedBack = 0;
                schedules.forEach((ratingElement) => {
                    sumOfRatings += ratingElement?.rating || 0;
                    sumOfFeedBack += ratingElement?.feedBack ? 1 : 0;
                });
                const average = getAverageOfRatings({ sumOfRatings, ratingsCount });
                data.push({ average, ratingsCount, sumOfFeedBack, scheduleId });
            }
        }

        return {
            statusCode: 200,
            message: 'Session Ratings Data',
            data,
        };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

module.exports = {
    createSessionRating,
    studentScheduleRatingData,
    updateSessionRating,
    getSessionRatingsData,
    calculateSessionRatings,
    getSessionFeedbacks,
    calculateMultipleSchedulesRating,
};
