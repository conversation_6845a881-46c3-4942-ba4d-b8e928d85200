const assignmentCategoryModel = require('./assignment_category.model');
const { convertToMongoObjectId } = require('../../../utility/common');
const { getPaginationValues } = require('../../../utility/pagination');

const getAssignmentCategory = async ({ headers = {}, query = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _course_id } = params;
        const { searchKey } = query;
        const { limit, pageNo, skip } = getPaginationValues(query);
        let dbQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _course_id: convertToMongoObjectId(_course_id),
        };
        if (searchKey && searchKey.length) {
            dbQuery = {
                ...dbQuery,
                $or: [{ category: { $regex: searchKey, $options: 'i' } }],
            };
        }
        const assignmentCategory = await assignmentCategoryModel
            .find(dbQuery)
            .sort({ category: 1 })
            .lean();
        return { statusCode: 200, data: assignmentCategory };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAssignmentCategory = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const { category, _course_id } = body;
        const assignmentCategory = await assignmentCategoryModel.create({
            category,
            _course_id,
            _institution_id,
        });
        return { statusCode: 200, data: assignmentCategory };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editAssignmentCategory = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const { category } = body;

        const assignmentCategory = await assignmentCategoryModel.findByIdAndUpdate(
            { _id: id },
            { category },
            { new: true },
        );
        return { statusCode: 200, data: assignmentCategory };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteAssignmentCategory = async ({ headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;

        const assignmentCategory = await assignmentCategoryModel.findByIdAndDelete({ _id: id });
        return { statusCode: 200, data: assignmentCategory };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getAssignmentCategory,
    addAssignmentCategory,
    editAssignmentCategory,
    deleteAssignmentCategory,
};
