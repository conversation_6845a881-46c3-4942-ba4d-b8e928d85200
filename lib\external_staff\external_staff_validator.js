const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
exports.addExternalStaffValidation = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        body: Joi.object({
            _program_id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('PROGRAM_ID_MUST_BE_OBJECTID');
                }),
            _course_id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('COURSE_ID_MUST_BE_OBJECTID');
                }),
            _institution_calendar_id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('INSTITUTION_CALENDER_ID_MUST_BE_OBJECTID');
                }),
            _infra_id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('INFRA_ID_MUST_BE_OBJECTID');
                }),
            name: Joi.string().required(),
            email: Joi.string().required(),
            mobileNo: Joi.number()
                .required()
                .error(() => {
                    return req.t('MOBILE_NO_MUST_BE_NUMBER');
                }),
        }).unknown(true),
    });

    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.editExternalStaffValidation = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            _id: Joi.string()
                .length(24)
                .optional()
                .error(() => {
                    return req.t('ID_MUST_BE_OBJECTID');
                }),
            name: Joi.string().optional().allow(''),
            email: Joi.string().optional().allow(''),
            mobileNo: Joi.number()
                .optional()
                .allow('')
                .error(() => {
                    return req.t('MOBILE_NO_MUST_BE_NUMBER');
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.getExternalStaffValidation = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.deleteExternalStaffValidation = (req, res, next) => {
    const schema = Joi.object({
        params: Joi.object({
            _id: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        params: req.params,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
