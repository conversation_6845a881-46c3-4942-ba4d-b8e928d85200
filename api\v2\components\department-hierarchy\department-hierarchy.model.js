const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../../utility/constants');

const departmentHierarchySchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _department_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DEPARTMENT,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM,
        },
        hierarchy: [
            {
                name: {
                    type: String,
                    required: true,
                },
                isActive: {
                    type: Boolean,
                    default: false,
                },
                stage: {
                    type: Number,
                },
            },
        ],
        type: {
            type: String,
            enum: [constant.COLLEGE, constant.PROGRAM, constant.DEPARTMENT],
        },
    },
    { timestamps: true },
);

module.exports = departmentHierarchySchema;
