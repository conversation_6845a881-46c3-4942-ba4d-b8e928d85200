const express = require('express');
const route = express.Router();
const session_order = require('./session_order_controller');
const validater = require('./session_order_validator');
route.post('/list', session_order.list_values);
route.get('/:id', validater.session_order_id, session_order.list_id);
route.get('/', session_order.list);
route.post('/', validater.session_order, session_order.insert);
route.put('/:id', validater.session_order_id, validater.session_order_update, session_order.update);
route.delete('/:id', validater.session_order_id, session_order.delete);
route.get('/program/:id', validater.session_order_id, session_order.list_program);
route.get('/course/:id', validater.session_order_id, session_order.list_course);

module.exports = route;