const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const {
    calendarBasedCleanService,
    programInputCleanService,
} = require('./automationService.controller');

router.delete('/calendarBasedCleanService', catchAsync(calendarBasedCleanService));
router.delete('/programInputCleanService', catchAsync(programInputCleanService));
module.exports = router;
