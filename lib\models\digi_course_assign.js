let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let digi_course_assign = new Schema({
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM
    },
    program_name: String,
    _curriculum_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DIGI_CURRICULUM
    },
    curriculum_name: String,
    _year_id: {
        type: Schema.Types.ObjectId
    },
    year: String,
    _level_id: {
        type: Schema.Types.ObjectId
    },
    level_no: {
        type: String
    },
    _course_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DIGI_CURRICULUM
    },
    course_name: String,
    course_duration: {
        start_week: Number,
        end_week: Number,
        total: Number
    },
    course_shared_with: [{
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: constant.PROGRAM
        },
        program_name: String,
        _curriculum_id: {
            type: Schema.Types.ObjectId,
            ref: constant.DIGI_CURRICULUM
        },
        curriculum_name: String,
        _year_id: {
            type: Schema.Types.ObjectId
        },
        year: String,
        _level_id: {
            type: Schema.Types.ObjectId
        },
        level_no: String
    }],
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.DIGI_COURSE_ASSIGN, digi_course_assign);