let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let digi_course_group = new Schema({
    _institution_id: {
        type: Schema.Types.ObjectId,
        ref: constant.INSTITUTION
    },
    _curriculum_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DIGI_CURRICULUM
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DIGI_CURRICULUM
    },
    _year_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DIGI_CURRICULUM
    },
    _level_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DIGI_CURRICULUM
    },
    group_no: Number,
    group_name: String,
    course_id: {
        type: Schema.Types.ObjectId
    },
    course_name: String,
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.DIGI_COURSE_GROUP, digi_course_group);