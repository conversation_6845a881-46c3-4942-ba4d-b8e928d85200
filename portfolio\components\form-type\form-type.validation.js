const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const createFormTypeSchema = Joi.object({
    body: Joi.object({
        name: Joi.string().required(),
        code: Joi.string().required(),
    }),
}).unknown(true);

const updateFormTypeSchema = Joi.object({
    query: Joi.object({
        formTypeId: objectId.required().label('FORM_TYPE_ID_REQUIRED'),
    }),
    body: Joi.object({
        name: Joi.string().optional(),
        code: Joi.string().optional(),
    }),
}).unknown(true);

const deleteFormTypeSchema = Joi.object({
    query: Joi.object({
        formTypeId: objectId.required().label('FORM_TYPE_ID_REQUIRED'),
    }),
}).unknown(true);

module.exports = {
    createFormTypeSchema,
    updateFormTypeSchema,
    deleteFormTypeSchema,
};
