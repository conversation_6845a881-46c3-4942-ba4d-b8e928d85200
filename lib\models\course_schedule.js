const mongoose = require('mongoose');
const { Schema } = mongoose;

const {
    EXTERNAL_STAFF,
    INSTITUTION,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_DEPARTMENT_SUBJECT,
    DIGI_SESSION_ORDER,
    D<PERSON>I_COURSE,
    INFRASTRUCTURE_MANAGEMENT,
    USER,
    COURSE_SCHEDULE,
    SCHEDULE_TYPES: { SCHEDULE, REGULAR, EVENT, SUPPORT_SESSION },
    TIME_GROUP_BOOKING_TYPE: { ONSITE, REMOTE },
    GENDER: { MALE, FEMALE, BOTH },
    AM,
    PM,
    STUDENT_GROUP,
    PENDING,
    ONGOING,
    COMPLETED,
    PRESENT,
    ABSENT,
    LEAVE,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    REMOTE_PLATFORM: { ZOOM, TEAMS },
    SESSION_MODE: { START, STOP, CLOS<PERSON>, <PERSON>O<PERSON>, <PERSON><PERSON><PERSON>, AUTO },
    MI<PERSON>ED,
    DS_STATUS_NOT_STARTED,
    SCHEDULE_ATTENDANCE_REASONS,
    <PERSON><PERSON><PERSON><PERSON><PERSON>_SESSION_TARDIS,
    EXCLUDE,
    EVENT_MODE: { OFFLINE },
    LIVE,
} = require('../utility/constants');

const course_schedule = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        type: { type: String, enum: [SCHEDULE, REGULAR, EVENT, SUPPORT_SESSION] },
        sub_type: {
            type: String,
        },
        title: {
            type: String,
            trim: true,
        },
        _program_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        program_name: {
            type: String,
            // required: true,
        },
        _course_id: {
            type: Schema.Types.ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        course_name: {
            type: String,
        },
        course_code: {
            type: String,
        },
        term: {
            type: String,
            required: true,
        },
        year_no: {
            type: String,
            required: true,
        },
        level_no: {
            type: String,
            required: true,
        },
        session: {
            _session_id: {
                type: Schema.Types.ObjectId,
                ref: DIGI_SESSION_ORDER,
            },
            s_no: { type: Number },
            delivery_symbol: { type: String },
            delivery_no: { type: Number },
            session_type: { type: String },
            session_topic: { type: String },
        },
        mode: { type: String, enum: [ONSITE, REMOTE] },
        schedule_date: { type: Date },
        start: {
            hour: {
                type: Number,
            },
            minute: {
                type: Number,
            },
            format: {
                type: String,
                enum: [AM, PM],
            },
        },
        end: {
            hour: {
                type: Number,
            },
            minute: {
                type: Number,
            },
            format: {
                type: String,
                enum: [AM, PM],
            },
        },
        _student_group_id: {
            type: Schema.Types.ObjectId,
            ref: STUDENT_GROUP,
        },
        rotation: {
            type: String,
            default: 'no',
        },
        rotation_count: {
            type: Number,
        },
        student_groups: [
            {
                group_id: {
                    type: Schema.Types.ObjectId,
                },
                gender: {
                    type: String,
                    enum: [MALE, FEMALE, BOTH],
                },
                group_no: Number,
                group_name: String,
                delivery_symbol: String,
                session_group: [
                    {
                        session_group_id: {
                            type: Schema.Types.ObjectId,
                        },
                        group_no: Number,
                        group_name: String,
                    },
                ],
                students: [
                    {
                        _student_id: {
                            type: Schema.Types.ObjectId,
                        },
                        academic_no: { type: String },
                        name: {
                            first: {
                                type: String,
                                trim: true,
                            },
                            middle: {
                                type: String,
                                trim: true,
                            },
                            last: {
                                type: String,
                                trim: true,
                            },
                            family: {
                                type: String,
                                trim: true,
                            },
                        },
                        gender: {
                            type: String,
                            enum: [MALE, FEMALE, BOTH],
                        },
                    },
                ],
                total_students: {
                    type: Number,
                },
                selected_students: {
                    type: Number,
                },
            },
        ],
        subjects: [
            {
                _subject_id: {
                    type: Schema.Types.ObjectId,
                    ref: DIGI_DEPARTMENT_SUBJECT,
                },
                subject_name: { type: String },
            },
        ],
        _infra_id: {
            type: Schema.Types.ObjectId,
            ref: INFRASTRUCTURE_MANAGEMENT,
        },
        infra_name: { type: String },
        substitute_staffs: [
            {
                _assigned_to: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                _substitute_staff_id: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                substitute_staff_name: {
                    first: {
                        type: String,
                        // required: true,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
            },
        ],
        staffs: [
            {
                _staff_id: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                staff_name: {
                    first: {
                        type: String,
                        // required: true,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
                // feedback entries
                feedBack: {
                    rating: {
                        type: String,
                    },
                    comments: {
                        type: String,
                    },
                },
                duration: {
                    type: String,
                    trim: true,
                },
                // attendance status only leave is true
                leave: {
                    type: Boolean,
                },
                user_id: String,
                status: {
                    type: String,
                    enum: [PRESENT, ABSENT, LEAVE, PENDING, ONDUTY, PERMISSION],
                    default: PENDING,
                },
                mode: {
                    type: String,
                    enum: [START, STOP, CLOSE, JOIN],
                },
                time: Date,
            },
        ],
        topic: { type: String },
        _topic_id: {
            type: Schema.Types.ObjectId,
        },
        merge_with: [
            {
                schedule_id: {
                    type: Schema.Types.ObjectId,
                    ref: COURSE_SCHEDULE,
                },
                session_id: {
                    type: Schema.Types.ObjectId,
                },
            },
        ],
        merge_status: {
            type: Boolean,
            default: false,
        },
        session_ids: [
            {
                type: Schema.Types.ObjectId,
            },
        ],
        uuid: {
            type: String,
        },
        socket_port: {
            type: String,
        },
        students: [
            {
                // studentId
                _id: {
                    type: Schema.Types.ObjectId,
                    ref: USER,
                },
                duration: {
                    type: String,
                    trim: true,
                },
                percentage: {
                    type: String,
                    trim: true,
                },
                name: {
                    first: {
                        type: String,
                        // required: true,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
                feedBack: {
                    rating: {
                        type: Number,
                    },
                    comments: {
                        type: String,
                    },
                },
                // attendance status only leave is true
                leave: {
                    type: Boolean,
                },
                user_id: String,
                join_url: String,
                tardisId: {
                    type: Schema.Types.ObjectId,
                    ref: GLOBAL_SESSION_TARDIS,
                },
                lateExclude: Boolean,
                status: {
                    type: String,
                    enum: [PRESENT, ABSENT, LEAVE, PENDING, ONDUTY, PERMISSION, EXCLUDE],
                    default: PENDING,
                },
                primaryStatus: {
                    type: String,
                    enum: [PRESENT, ABSENT, LEAVE, PENDING, ONDUTY, PERMISSION],
                    default: PENDING,
                },
                imageUrl: { type: String },
                mode: {
                    type: String,
                    enum: [START, STOP, CLOSE, JOIN, MANUAL, AUTO],
                },
                reason: String,
                reasonForLate: String,
                reasonIds: [
                    {
                        _id: {
                            type: Schema.Types.ObjectId,
                            ref: SCHEDULE_ATTENDANCE_REASONS,
                        },
                    },
                ],
                updated_by: {
                    _staff_id: {
                        type: Schema.Types.ObjectId,
                        ref: USER,
                    },
                    staff_name: {
                        first: {
                            type: String,
                            trim: true,
                        },
                        middle: {
                            type: String,
                            trim: true,
                        },
                        last: {
                            type: String,
                            trim: true,
                        },
                        family: {
                            type: String,
                            trim: true,
                        },
                    },
                },
                zoomDuration: {
                    type: String,
                },
                time: Date,
                primaryTime: Date,
                isRestricted: Boolean,
            },
        ],
        status: {
            type: String,
            default: PENDING,
            enum: [PENDING, ONGOING, COMPLETED, MISSED],
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        sessionDetail: {
            // attendance socket event id
            attendanceEventId: String,
            // if staff retake this session it will be save is true
            retake: {
                type: Boolean,
                default: false,
            },
            attendance_mode: {
                type: String,
                default: PENDING,
                enum: [PENDING, ONGOING, COMPLETED],
            },
            start_time: Date,
            stop_time: Date,
            startBy: {
                type: Schema.Types.ObjectId,
                ref: USER,
            },
            mode: { type: String, enum: ['M', 'A'] },
        },
        zoomUuid: {
            type: String,
        },
        zoomRecord: {
            type: Boolean,
            default: false,
        },
        zoomDuration: {
            type: Boolean,
            default: false,
        },
        scheduleStartDateAndTime: { type: Date },
        scheduleEndDateAndTime: { type: Date },
        zoomDetail: {
            // zoom Details
            zoomMeetingId: String,
            zoomUuid: String,
            passCode: String,
            zoomStartUrl: String,
            zoomTotalDuration: String,
            meetingStatus: {
                type: String,
                default: DS_STATUS_NOT_STARTED,
            },
        },
        remotePlatform: { default: TEAMS, type: String, enum: [null, ZOOM, TEAMS] },
        teamsDetail: {
            // teams Details
            teamMeetingId: String,
            threadId: String,
            teamsUserId: String,
            teamsStartUrl: String,
            teamsTotalDuration: String,
            meetingStatus: {
                type: String,
                default: DS_STATUS_NOT_STARTED,
            },
            teamsRecord: {
                type: Boolean,
                default: false,
            },
        },
        teamsDuration: {
            type: Boolean,
            default: false,
        },
        retakeStatus: {
            type: Number,
            default: 0,
        },
        isLive: {
            type: Boolean,
            default: false,
        },
        faceAuthentication: {
            type: Boolean,
            default: true,
        },
        attendanceTakingStaff: [
            {
                staffId: { type: Schema.Types.ObjectId, ref: USER },
                staffName: {
                    first: {
                        type: String,
                        trim: true,
                    },
                    middle: {
                        type: String,
                        trim: true,
                    },
                    last: {
                        type: String,
                        trim: true,
                    },
                    family: {
                        type: String,
                        trim: true,
                    },
                },
            },
        ],
        manualType: { type: String },
        scheduleBy: { type: String },
        isMissedToComplete: {
            type: Boolean,
            default: false,
        },
        lateConfigRange: { type: Number },
        //outside campus
        outsideCampus: { type: Boolean, default: false },
        // campusDetails: {
        //     mobile: { type: Number },
        //     email: { type: String },
        //     byDevice: { type: String },
        // },
        campusOtp: { type: Number },
        externalStaffs: [
            {
                type: Schema.Types.ObjectId,
                ref: EXTERNAL_STAFF,
            },
        ],
        scheduleStartFrom: { type: String },
        classLeaders: [{ type: Schema.Types.ObjectId, ref: USER }],
        selfAttendance: { type: Boolean, default: false },
        classModeType: { type: String, enum: [LIVE, OFFLINE], default: LIVE }, //for offline attendance purpose we added
        offlineModeTriggeredBy: {
            type: Schema.Types.ObjectId,
            ref: USER,
        },
        startWithOutFace: { type: Boolean, default: false },
        startWithOutFaceStaffId: { type: Schema.Types.ObjectId, ref: USER },
    },
    { timestamps: true },
);
module.exports = mongoose.model(COURSE_SCHEDULE, course_schedule);
