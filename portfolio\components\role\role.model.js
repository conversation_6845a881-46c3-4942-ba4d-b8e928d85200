const { Schema, model } = require('mongoose');

const { PORTFOLIO_ROLE, STUDENT, STAFF } = require('../../common/utils/constants');

const roleSchema = new Schema(
    {
        name: { type: String, required: true, trim: true, unique: true },
        type: { type: String, required: true, enum: [STUDENT, STAFF] },
        isDefault: { type: Boolean, default: false },
    },
    { timestamps: true },
);

roleSchema.index({ name: 1 });

module.exports = model(PORTFOLIO_ROLE, roleSchema);
