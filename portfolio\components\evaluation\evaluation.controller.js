const EvaluationService = require('./evaluation.service');

const { checkIfTrue } = require('../../common/utils/common.util');

const assignFacultyForEvaluation = async ({
    body: {
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        componentId,
        childrenId,
        role,
        evaluators,
        deliveryTypes = [],
    },
    headers: { user_id: userId } = {},
}) => {
    const evaluation = await EvaluationService.createOrUpdateEvaluatorAssignment({
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        componentId,
        childrenId,
        userId,
        role,
        evaluators,
        deliveryTypes,
    });
    return { message: 'FACULTY_ASSIGNED_FOR_EVALUATION_SUCCESSFULLY', data: evaluation };
};

const getStudentForEvaluator = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        componentId,
        childrenId,
        year,
        level,
        rotation,
        rotationCount,
        term,
    },
    headers: { user_id: userId } = {},
}) => {
    const students = await EvaluationService.getStudentForEvaluator({
        programId,
        courseId,
        institutionCalendarId,
        portfolioId,
        componentId,
        childrenId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        userId,
    });

    return { message: 'STUDENTS_FOR_EVALUATOR_RETRIEVED_SUCCESSFULLY', data: students };
};

const getStudentResponseForEvaluator = async ({
    query: { responseId, isPages = false },
    headers: { user_id: userId } = {},
}) => {
    const responses = await EvaluationService.getStudentResponseForEvaluator({
        responseId,
        isPages: checkIfTrue(isPages),
        userId,
    });

    return { message: 'STUDENT_RESPONSE_RETRIEVED_SUCCESSFULLY', data: responses };
};

const updateStudentMarks = async ({
    body: {
        programId,
        courseId,
        institutionCalendarId,
        componentId,
        childrenId,
        formId,
        scheduleId,
        studentIds,
        marks,
        rubrics,
        globalRubrics,
    },
    headers: { user_id: userId } = {},
}) => {
    const { message, students, isError } = await EvaluationService.updateStudentMarks({
        programId,
        courseId,
        institutionCalendarId,
        componentId,
        childrenId,
        formId,
        scheduleId,
        studentIds,
        userId,
        marks,
        rubrics,
        globalRubrics,
    });

    return { message, data: { isError, students } };
};

const getComponentsByEvaluator = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        rotation,
        rotationCount,
    } = {},
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getComponentsByEvaluator({
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        rotation,
        rotationCount,
        userId,
    });

    return { statusCode: 200, data: result };
};

const rejectStudentSubmission = async ({
    query: { componentId, childrenId },
    body: { studentIds = [], scheduleIds = [], isApproved = false, reason },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.rejectStudentSubmission({
        componentId,
        childrenId,
        scheduleIds,
        studentIds,
        isApproved,
        userId,
        reason,
    });

    return { statusCode: 200, data: 'STUDENT_SUBMISSION_REJECTED_SUCCESSFULLY' };
};

const getEvaluatorListByCourse = async ({
    query: { programId, courseId, institutionCalendarId, childrenId } = {},
}) => {
    const evaluators = await EvaluationService.getEvaluatorListByCourse({
        programId,
        courseId,
        institutionCalendarId,
        childrenId,
    });

    return { statusCode: 200, data: evaluators };
};

const completeEvaluation = async ({
    body: { componentId, childrenId, scheduleId, studentIds },
    headers: { user_id: userId } = {},
}) => {
    await EvaluationService.completeEvaluation({
        componentId,
        childrenId,
        scheduleId,
        studentIds,
        userId,
    });

    return { statusCode: 200, data: 'EVALUATION_COMPLETED_SUCCESSFULLY' };
};

const getRubricsOrMarksForEvaluation = async ({
    query: { portfolioId, componentId, childrenId, formId },
    headers: { user_id: userId } = {},
}) => {
    const result = await EvaluationService.getRubricsOrMarksForEvaluation({
        portfolioId,
        componentId,
        childrenId,
        formId,
        userId,
    });

    return { statusCode: 200, data: result };
};

const getEvaluatorsForCourseAdmin = async ({ query: { portfolioId, componentId, childrenId } }) => {
    const evaluators = await EvaluationService.getEvaluatorsForCourseAdmin({
        portfolioId,
        componentId,
        childrenId,
    });

    return { statusCode: 200, data: evaluators };
};

const getPortfolioInsights = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId },
}) => {
    const insights = await EvaluationService.getPortfolioInsights({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
    });

    return { statusCode: 200, data: insights };
};

const getEvaluationCount = async ({
    query: { portfolioId, componentId, childrenId, scheduleId, studentId },
}) => {
    const count = await EvaluationService.getEvaluationCount({
        portfolioId,
        componentId,
        childrenId,
        scheduleId,
        studentId,
    });

    return { statusCode: 200, data: count };
};

module.exports = {
    assignFacultyForEvaluation,
    getStudentForEvaluator,
    getStudentResponseForEvaluator,
    updateStudentMarks,
    getComponentsByEvaluator,
    rejectStudentSubmission,
    getEvaluatorListByCourse,
    completeEvaluation,
    getRubricsOrMarksForEvaluation,
    getEvaluatorsForCourseAdmin,
    getPortfolioInsights,
    getEvaluationCount,
};
