const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const notification = new Schema(
    {
        users: [
            {
                _id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.USER,
                },
                isViewed: {
                    type: Boolean,
                    default: false,
                },
                push_status: {
                    type: String,
                },
                redirect: {
                    type: Schema.Types.ObjectId,
                    ref: constant.COURSE_SCHEDULE,
                },
            },
        ],
        rotation: {
            type: String,
            default: 'no',
        },
        rotation_count: {
            type: Number,
        },
        title: {
            type: String,
        },
        description: {
            type: String,
        },
        buttonAction: {
            type: String,
        },
        ClickAction: {
            type: String,
        },
        calendarStartDate: {
            type: Date,
        },
        calendarEndDate: {
            type: Date,
        },
        calendarStatus: {
            type: Boolean,
        },
        courseId: {
            type: Schema.Types.ObjectId,
        },
        sessionId: {
            type: Schema.Types.ObjectId,
        },
        scheduleId: {
            type: Schema.Types.ObjectId,
        },
        programId: {
            type: Schema.Types.ObjectId,
        },
        institutionCalendarId: {
            type: Schema.Types.ObjectId,
        },
        yearNo: {
            type: String,
        },
        levelNo: {
            type: String,
        },
        term: {
            type: String,
        },
        mergeStatus: {
            type: String,
        },
        mergeType: {
            type: String,
        },
        notificationType: {
            type: String,
        },
        notificationPeriod: {
            type: String,
        },
        staffStartWithExam: {
            type: String,
        },
        activityId: {
            type: Schema.Types.ObjectId,
        },
        channelId: {
            type: String,
        },
        discussionId: {
            type: Schema.Types.ObjectId,
        },
        adminCourse: {
            type: String,
        },
        groupName: {
            type: String,
        },
        courseName: {
            type: String,
        },
        courseCode: {
            type: String,
        },
        programName: {
            type: String,
        },
        type: {
            type: String,
        },
        assignmentId: {
            type: Schema.Types.ObjectId,
        },
        assignmentData: {
            startDate: {
                type: Date,
            },
            endDate: {
                type: Date,
            },
            dueDate: {
                type: Date,
            },
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isNewActivity: {
            type: Boolean,
            default: false,
        },
        correctionType: {
            type: String,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(constant.APP_NOTIFICATIONS, notification);
