const { convertToMongoObjectId, response_function, clone } = require('../utility/common');
const {
    STUDENT_CRITERIA_MANIPULATION,
    STUDENT_GROUP,
    COURSE_SCHEDULE,
    SCHEDULE_TYPES: { REGULAR },
    COMPLETED,
    ABSENT,
    LEAVE,
    PRESENT,
    LMS,
    LEAVE_TYPE: { PERMISSION },
} = require('../utility/constants');
const { logger } = require('../utility/util_keys');

const { scheduleDateFormateChange } = require('../utility/common_functions');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const studentGroupCollection = require('mongoose').model(STUDENT_GROUP);
const courseScheduleCollection = require('mongoose').model(COURSE_SCHEDULE);
const lmsCollection = require('mongoose').model(LMS);
const { updateStudentGroupFlatCacheData } = require('../student_group/student_group_services');

const {
    get,
    get_list,
    bulk_write,
    update_condition_array_filter,
} = require('../base/base_controller');
const { lmsStudentAbsenceList } = require('./studentRegister.service');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
exports.updateStudentCriteria = async (req, res) => {
    try {
        const {
            institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotationCount,
            absencePercentage,
            studentIds,
            reason,
            updatedBy,
        } = req.body;
        const requestedParams = {
            institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotationCount,
            absencePercentage,
            studentIds,
            reason,
            updatedBy,
        };
        logger.info(
            requestedParams,
            'StudentRegister -> updateStudentCriteria -> Students Criterial update based on course Updating start',
        );

        if (studentIds === undefined || (studentIds !== undefined && studentIds.length === 0)) {
            const query = {
                // _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.term': term,
                'groups.level': levelNo,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            };
            const objs = {
                $set: {
                    'groups.$[i].courses.$[j].student_absence_percentage': absencePercentage,
                },
            };
            const filter = {
                arrayFilters: [
                    {
                        'i.term': term,
                        'i.level': levelNo,
                    },
                    { 'j._course_id': convertToMongoObjectId(courseId) },
                ],
            };
            await update_condition_array_filter(studentGroupCollection, query, objs, filter);
            updateStudentGroupFlatCacheData();
            await updateStudentGroupRedisKey({
                courseId,
                level: levelNo,
                batch: term,
            });
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        'Student Criteria updated Storing',
                        'Student Criteria updated Storing',
                    ),
                );
        }

        console.time('userCourseSurveyData');
        const userCourseSurveyData = await get_list(studentCriteriaCollection, {
            institutionCalendarId,
            programId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotationCount,
            studentId: {
                $in: studentIds.map((studentElement) => convertToMongoObjectId(studentElement)),
            },
        });
        if (!userCourseSurveyData.status) userCourseSurveyData.data = [];
        console.timeEnd('userCourseSurveyData');
        // return res.send(userCourseSurveyData);
        const bulkWriteObject = [];
        for (studentElement of studentIds) {
            const studentAbsence = userCourseSurveyData.data.find(
                (absenceElement) =>
                    absenceElement.studentId.toString() === studentElement.toString(),
            );
            if (studentAbsence)
                bulkWriteObject.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(studentAbsence._id),
                        },
                        update: {
                            $set: {
                                absencePercentage,
                                reason,
                                updatedBy,
                            },
                        },
                    },
                });
            else
                bulkWriteObject.push({
                    insertOne: {
                        document: {
                            isDeleted: false,
                            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                            programId: convertToMongoObjectId(programId),
                            courseId: convertToMongoObjectId(courseId),
                            yearNo,
                            levelNo,
                            term,
                            rotationCount,
                            absencePercentage,
                            studentId: convertToMongoObjectId(studentElement),
                            reason,
                            updatedBy,
                        },
                    },
                });
        }
        if (bulkWriteObject.length)
            if (!(await bulk_write(studentCriteriaCollection, bulkWriteObject)).status)
                return res
                    .status(410)
                    .send(
                        response_function(
                            res,
                            410,
                            false,
                            'Unable to Add Absence',
                            'Unable to Add Absence',
                        ),
                    );
        return res
            .status(200)
            .send(
                response_function(
                    res,
                    200,
                    true,
                    'Student Criteria updated Storing',
                    'Student Criteria updated Storing',
                ),
            );
    } catch (error) {
        logger.error(
            error,
            'StudentRegister -> updateStudentCriteria -> Students Criterial update based on course Updating Error',
        );
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Internal Server Issue', error.toString()));
    }
};

exports.criteriaManipulatedStudentList = async (req, res) => {
    try {
        const {
            params: {
                institutionCalendarId,
                programId,
                courseId,
                levelNo,
                term,
                gender,
                rotationCount,
                absencePercentage,
            },
        } = req;
        let {
            query: { studentIds },
        } = req;
        console.time('lmsStudentSetting');
        const lmsStudentSetting = await get(
            lmsCollection,
            { isDeleted: false },
            { student_warning_absence_calculation: 1 },
        );
        console.timeEnd('lmsStudentSetting');
        if (!lmsStudentSetting.status)
            return res.status(200).send(response_function(res, 200, true, 'No data', 'No data'));
        const warningAbsenceData = lmsStudentAbsenceList({ lmsStudentSetting });
        if (!warningAbsenceData || warningAbsenceData.length === 0)
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        'No Setting data found',
                        'No Setting data found',
                    ),
                );
        if (warningAbsenceData[0] && warningAbsenceData[0].absence_percentage)
            warningAbsenceData[0].absence_percentage = absencePercentage;
        if (warningAbsenceData[1] && warningAbsenceData[1].warning)
            warningAbsenceData[1].warning = 'Final Warning';
        console.time('studentGroupData');
        const studentGroupData = await get(
            studentGroupCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.term': term,
                'groups.level': levelNo,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {
                'groups.level': 1,
                'groups.term': 1,
                'groups.courses': 1,
                'groups.students.name': 1,
                'groups.students._student_id': 1,
                'groups.students.academic_no': 1,
                'groups.students.gender': 1,
            },
        );
        console.timeEnd('studentGroupData');
        if (!studentGroupData.status)
            return res
                .status(200)
                .send(response_function(res, 200, true, 'Student group not found', []));
        const groupData = studentGroupData.data.groups.findIndex(
            (ele) => ele.term === term && ele.level.toString() === levelNo.toString(),
        );
        if (groupData === -1)
            return res
                .status(200)
                .send(response_function(res, 200, true, 'Year Level data not found', []));
        let studentData = clone(studentGroupData.data.groups[groupData].students);
        const courseData = studentGroupData.data.groups[groupData].courses.findIndex(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );
        if (courseData === -1)
            return res
                .status(200)
                .send(response_function(res, 200, true, 'Course not found', 'Course not found'));
        const courseGenderData = studentGroupData.data.groups[groupData].courses[
            courseData
        ].setting.filter((ele) =>
            rotationCount
                ? parseInt(ele._group_no) === parseInt(rotationCount) && ele.gender === gender
                : ele.gender === gender,
        );
        let courseStudents = [];
        if (courseGenderData.length)
            for (courseSettingElement of courseGenderData)
                if (courseSettingElement && courseSettingElement.session_setting)
                    for (settingElement of courseSettingElement.session_setting) {
                        for (groupElement of settingElement.groups) {
                            courseStudents = [...courseStudents, ...groupElement._student_ids];
                        }
                    }
        courseStudents = [
            ...new Set(courseStudents.map((studentElement) => studentElement.toString())),
        ];
        if (studentIds && studentIds.length) {
            if (!Array.isArray) studentIds = [studentIds];
            courseStudents = courseStudents.filter((studentElement) =>
                studentIds.includes(studentElement),
            );
        }
        if (courseStudents.length === 0)
            return res
                .status(200)
                .send(response_function(res, 200, true, 'Student records not found', []));
        console.time('scheduleData');
        const scheduleData = await get_list(
            courseScheduleCollection,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                level_no: levelNo,
                type: REGULAR,
                term,
                rotation_count: rotationCount,
                'students._id': {
                    $in: courseStudents.map((studentElement) =>
                        convertToMongoObjectId(studentElement),
                    ),
                },
            },
            {
                _id: 1,
                students: 1,
                status: 1,
                'session._session_id': 1,
                'session.session_type': 1,
            },
        );
        if (!scheduleData.status) scheduleData.data = [];
        console.timeEnd('scheduleData');
        studentData = clone(
            studentData.filter((ele) =>
                courseStudents.find((ele2) => ele2.toString() === ele._student_id.toString()),
            ),
        );
        console.time('studentCriteriaData');
        const studentCriteriaData = await get_list(studentCriteriaCollection, {
            programId,
            courseId,
            // yearNo,
            levelNo,
            term,
            rotationCount,
            studentId: {
                $in: courseStudents.map((studentElement) => convertToMongoObjectId(studentElement)),
            },
        });
        if (!studentCriteriaData.status) studentCriteriaData.data = [];
        console.timeEnd('studentCriteriaData');
        const denialStudents = [];
        for (studentElement of studentData) {
            const studentSchedule = scheduleData.data.filter(
                (ele) =>
                    ele &&
                    ele.students.find(
                        (ele2) => ele2._id.toString() === studentElement._student_id.toString(),
                    ),
            );
            const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
            const absentSchedules = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === LEAVE ||
                            ele2.status === PERMISSION),
                ),
            );
            studentElement.session_attended = completedSchedule.filter((ele) =>
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement._student_id.toString() &&
                        ele2.status === PRESENT,
                ),
            ).length;
            studentElement.session_conducted = completedSchedule.length;
            studentElement.total_session = studentSchedule.length;
            const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
            studentElement.absence = denialPercentage.toFixed(2);

            const studentManipulation = studentCriteriaData.data.find(
                (absenceElement) =>
                    absenceElement.studentId.toString() === studentElement._student_id.toString(),
            );
            const studentWarningAbsence = clone(warningAbsenceData);
            let manipulationStatus = false;
            studentElement.manipulationPercentage = 0;
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].absence_percentage &&
                studentWarningAbsence[0].absence_percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].absence_percentage = studentManipulation.absencePercentage;
                manipulationStatus = !!studentManipulation.absencePercentage;
                studentElement.manipulationPercentage = studentManipulation.absencePercentage;
            }
            const warningData = studentWarningAbsence.find(
                (ele) =>
                    ele.absence_percentage &&
                    parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
            );
            studentElement.manipulationStatus = manipulationStatus;
            studentElement.warning = warningData ? warningData.warning : '';
            if (studentElement.warning !== '') denialStudents.push(studentElement);
        }
        return res
            .status(200)
            .send(response_function(res, 200, true, 'Student Register', denialStudents));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
