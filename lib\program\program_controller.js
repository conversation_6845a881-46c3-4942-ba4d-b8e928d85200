const constant = require('../utility/constants');
var program = require('mongoose').model(constant.PROGRAM);
var department = require('mongoose').model(constant.DEPARTMENT);
var subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
var institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const program_formate = require('./program_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        { $unwind: '$institution' },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits }
    ];
    let doc = await base_control.get_aggregate(program, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "program list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ program_formate.program(doc.data));
        // common_files.list_all_response(res, 200, true, "program list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* program_formate.program(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        { $unwind: '$institution' },
    ];
    let doc = await base_control.get_aggregate(program, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "program details", /* doc.data */program_formate.program_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let institution_ids = [];
    let status, datas;
    await req.body.data.forEach(element => {
        if (institution_ids.indexOf(element._institution_id) == -1) {
            institution_ids.push(element._institution_id);
        }
    });
    let checks = await base_control.check_id(institution, { _id: { $in: institution_ids }, 'isDeleted': false });
    if (checks.status) {
        req.body.data.forEach(async (doc, index) => {
            let objects = {
                _institution_id: doc._institution_id,
                name: doc.name,
                level: doc.level,
                level_no: doc.level_no,
                degree: doc.degree,
                no: doc.no,
                theory_credit: doc.theory_credit,
                practicals_credit: doc.practicals_credit,
                clinic_credit: doc.clinic_credit,
                total_credit: (Number(doc.theory_credit) + Number(doc.practicals_credit) + Number(doc.clinic_credit)),
                interim: doc.interim
            };
            let object_id = doc.id;
            if (doc.id == '' && doc.id.length == 0) {
                let doc = await base_control.insert(program, objects);
                if (doc.status) {
                    status = true;
                    datas = doc;
                } else {
                    datas = doc;
                    status = false;
                    common_files.com_response(res, 500, false, "Error", doc.data);
                }
            }
            else {
                let doc = await base_control.update(program, object_id, objects);
                // if (doc.status) {
                status = true;
                datas = doc;
                /* } else {
                    datas = doc;
                    status = false;
                    common_files.com_response(res, 500, false, "Error", doc.data);
                } */
            }
            if (req.body.data.length == index + 1) {
                if (status) {
                    common_files.com_response(res, 201, true, "program Added successfully", datas);
                } else {
                    common_files.com_response(res, 500, false, "Errors ", 'Error');
                }
            }
        });
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._institution_id != undefined) {
        checks = await base_control.check_id(institution, { _id: { $in: req.body._institution_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(program, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "program update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(program, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "program deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }
        let doc = await base_control.get_list(program, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "program List", program_formate.program_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_institution = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.INSTITUTION, localField: '_institution_id', foreignField: '_id', as: 'institution' } },
        { $unwind: '$institution' },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_institution_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(program, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "program list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ program_formate.program(doc.data));
        // common_files.list_all_response(res, 200, true, "program list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* program_formate.program(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.program_list = async (req, res) => {
    let program_no = null;
    let program_no_ara = [];
    let program_cri = [];
    let program_data = await base_control.get_list_sort(program, { 'isDeleted': false }, {}, { name: -1 });
    if (program_data.status) {
        program_data.data.forEach(main_element => {
            program_no = main_element.no;
            program_data.data.forEach(element => {
                if (program_no_ara.indexOf(program_no) == -1) {
                    if (program_no == element.no) {
                        let ver = element.name.substring((element.name.length - 3), (element.name.length));
                        let pro_name = element.name.substring(0, (element.name.length - 4));
                        program_cri.push({ _id: element._id, name: pro_name, program_no: program_no, version: ver, term: element.interim });
                        program_no_ara.push(element.no);
                    }
                }
            });
        });
        // console.log(program_data.data[program_data.data.findIndex(i => i.no == program_no)]);
        common_files.com_response(res, 200, true, "Program List", program_cri);
    } else {
        common_files.com_response(res, 500, false, "Error Unable to find Program list", "Error Unable to find Program list");
    }
};

exports.program_department_subject = async (req, res) => {
    try {
        let program_no_ara = [];
        let program_data = await base_control.get_list_sort(program, { 'isDeleted': false }, {}, { name: -1 });
        let department_data = await base_control.get_list(department, { 'isDeleted': false }, {});
        let subject_data = await base_control.get_list(subject, { 'isDeleted': false }, {});
        let department_list = [], subject_list = [], program_list = [];
        for (depart of department_data.data) {
            subject_list = [];
            if (depart._division_id.length != 0) {
                for (division_ids of depart._division_id) {
                    let sub = (subject_data.data.filter(item => (division_ids).toString() == (item._master_id).toString()));
                    sub.forEach(element => {
                        subject_list.push({
                            subject_name: element.title,
                            _subject_id: element._id
                        });
                    });
                }
            } else if (depart._subject_id.length != 0) {
                let sub = (subject_data.data.filter(item => (depart._id).toString() == (item._master_id).toString()));
                sub.forEach(element => {
                    subject_list.push({
                        subject_name: element.title,
                        _subject_id: element._id
                    });
                });
            }
            department_list.push({
                _program_id: depart._program_id,
                _department_id: depart._id,
                department_name: depart.department_title,
                subject: subject_list
            });
        }
        for (pro of program_data.data) {
            let ver = pro.name.substring((pro.name.length - 3), (pro.name.length));
            let pro_name = pro.name.substring(0, (pro.name.length - 4));
            program_list.push({
                _id: pro._id,
                name: pro_name,
                program_no: pro.no,
                version: ver,
                term: pro.interim,
                department: (department_list.filter(item => (item._program_id).toString() == (pro._id).toString()))
            });
        }
        let pds = [];
        for (main_element of program_list) {
            let dep = [];
            if (program_no_ara.indexOf(main_element.program_no) == -1) {
                let pros = program_list.filter(item => (item.program_no) == main_element.program_no);
                pros.forEach(element => {
                    dep = dep.concat(element.department);
                });
                pds.push({
                    _id: main_element._id,
                    name: main_element.name,
                    program_no: main_element.program_no,
                    version: main_element.version,
                    term: main_element.term,
                    department: dep
                });
                program_no_ara.push(main_element.program_no);
            }
        }
        return res.status(200).send(common_files.response_function(res, 200, true, "Program Department Subject List", pds));
    } catch (error) {
        console.log(error);
        return res.status(500).send(common_files.response_function(res, 500, false, "Error Catch", error.toString()));
    }
};