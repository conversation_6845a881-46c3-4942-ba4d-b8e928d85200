const { AM, PM } = require('../../utility/enums');

const session = {
    start: {
        hour: {
            type: Number,
        },
        minute: {
            type: Number,
        },
        format: {
            type: String,
            enum: [AM, PM],
        },
    },
    end: {
        hour: {
            type: Number,
        },
        minute: {
            type: Number,
        },
        format: {
            type: String,
            enum: [AM, PM],
        },
    },
};

const language = [
    {
        code: {
            type: String,
        },
        name: {
            type: String,
        },
        isDefault: {
            type: Boolean,
            default: false,
        },
    },
];

const version = [
    {
        mode: {
            type: Number,
        },
        isDefault: {
            type: Boolean,
            default: false,
        },
    },
];

module.exports = {
    session,
    language,
    version,
};
