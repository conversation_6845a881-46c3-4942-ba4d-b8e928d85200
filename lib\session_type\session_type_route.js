const express = require('express');
const route = express.Router();
const session_type = require('./session_type_controller');
const validater = require('./session_type_validator');
route.get('/get_delivery_types', session_type.get_delivery_types);
route.post('/list', session_type.list_values);
route.get('/:id', validater.session_type_id, session_type.list_id);
route.get('/', session_type.list);
route.post('/', validater.session_type, session_type.insert);
route.put('/:id', validater.session_type_id, validater.session_type_update, session_type.update);
route.delete('/:id', validater.session_type_id, session_type.delete);
route.get('/program/:id', validater.session_type_id, session_type.list_program);
route.get('/list_symbol_per_session/:id', validater.session_type_id, session_type.list_symbol_per_session);


module.exports = route;