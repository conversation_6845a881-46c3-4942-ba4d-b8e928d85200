const express = require('express');
const { getStudentInprogressCourse, getStudentCompletedCourse } = require('./report_controller');
const { getCourseValidator } = require('./report_validation');
const route = express.Router();
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.get(
    '/get-inprogress-course',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    getCourseValidator,
    getStudentInprogressCourse,
);
route.get(
    '/get-completed-course',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    getCourseValidator,
    getStudentCompletedCourse,
);

module.exports = route;
