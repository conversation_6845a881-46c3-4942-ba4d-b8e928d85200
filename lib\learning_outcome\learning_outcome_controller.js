const { response_function, convertToMongoObjectId, clone } = require('../utility/common');
const {
    STUDENT_SESSION_SURVEY,
    COURSE_SCHEDULE,
    PROGRAM_REPORT_SETTINGS,
    SLO,
    CLO,
} = require('../utility/constants');
const {
    allCompletedActivities,
    allQuestions,
    allCourseList,
    allSessionOrderDatas,
} = require('../../service/cache.service');
const {
    courseYetToRate,
    courseCLOLists,
    selfEvaluationClo,
    selfEvaluationOutcomeSloWise,
} = require('../digi_class/survey/survey.service');
const {
    studentGroupList,
    allSLOList,
    activityQuestionList,
    comparisonReportSummary,
    comparisonReportSlo,
    comparisonReportClo,
    selfEvaluationSurveySessionCloSlo,
    mapSLORatingsWithCLO,
    calculateMarks,
} = require('./learning_outcome_services');
const StudentSessionSurvey = require('../models/student_session_survey');
const { logger } = require('../utility/util_keys');
const { get_list, get } = require('../base/base_controller');
const surveyCollection = require('mongoose').model(STUDENT_SESSION_SURVEY);
const courseScheduleCollection = require('mongoose').model(COURSE_SCHEDULE);
const programReportSettings = require('mongoose').model(PROGRAM_REPORT_SETTINGS);
// const {
//     studentGroupList,
//     activityQuestionList,
//     comparisonReportSummary,
//     comparisonReportSlo,
//     comparisonReportClo,
// } = require('./learning_outcome_services');
// const { courseCLOLists } = require('../digi_class/survey/survey.service');

const selfEvaluationSurveyReportForStudents = async (students, course) => {
    let rotationCount;
    if (course.rotationCount) {
        rotationCount = course.rotationCount;
    }
    const studentQuery = students.map((student) => {
        const studentQuery = {
            _institution_calendar_id: convertToMongoObjectId(course.institutionCalendarId),
            _program_id: convertToMongoObjectId(course.programId),
            _course_id: convertToMongoObjectId(course.courseId),
            _user_id: convertToMongoObjectId(student.studentId),
            // yearNo: convertToMongoObjectId(course.institutionCalendarId),
            levelNo: course.levelNo,
            term: course.term,
        };
        if (rotationCount) {
            studentQuery.rotationCount = rotationCount;
        }
        return studentQuery;
    });
    const surveyQuery = {
        isDeleted: false,
        isActive: true,
        slos: { $exists: true, $not: { $size: 0 } },
    };
    if (studentQuery.length) {
        surveyQuery.$or = studentQuery;
    }
    const studentSurvey = await StudentSessionSurvey.find(surveyQuery, { slos: 1, _user_id: 1 });
    students.forEach((student) => {
        const studentSessionSurvey = studentSurvey.filter(
            (studentSurveyEntry) =>
                studentSurveyEntry._user_id.toString() === student.studentId.toString(),
        );
        let pointRating = 0;
        let percentageRating = 0;
        if (studentSessionSurvey.length) {
            const studentSessionSlos = studentSessionSurvey
                .map((studentSessionSurveyEntry) => studentSessionSurveyEntry.slos)
                .flat();
            const studentSession = studentSessionSlos
                .map((studentSession) => studentSession.sloRating)
                .filter((sessionSloRating) => sessionSloRating && sessionSloRating !== null);
            // in rating point format
            pointRating = studentSession.reduce((a, b) => a + b, 0) / studentSessionSlos.length;
            // in rating percentage format
            percentageRating = (pointRating / 5) * 100;
        }
        student.selfEvaluationRating = {
            pointRating,
            percentageRating,
        };
    });
    return students;
};

const quizReportForStudents = async (students, course) => {
    if (course.rotationCount) {
        rotationCount = course.rotationCount;
    }
    const allActivities = await allCompletedActivities(course.institutionCalendarId);
    const getAllQuestions = await allQuestions();
    students.forEach((student) => {
        const studentAttendedActivities = allActivities.filter((activity) =>
            course.rotationCount
                ? activity._institution_calendar_id.toString() ===
                      course.institutionCalendarId.toString() &&
                  activity._program_id.toString() === course.programId.toString() &&
                  activity.courseId.toString() === course.courseId.toString() &&
                  activity.level_no === course.levelNo &&
                  activity.term === course.term &&
                  activity.rotation_count.toString() === course.rotationCount.toString() &&
                  activity.studentCompletedQuiz.find(
                      (studentCompletedQuizEntry) =>
                          studentCompletedQuizEntry.toString() === student.studentId.toString(),
                  )
                : activity._institution_calendar_id.toString() ===
                      course.institutionCalendarId.toString() &&
                  activity._program_id.toString() === course.programId.toString() &&
                  activity.courseId.toString() === course.courseId.toString() &&
                  activity.level_no === course.levelNo &&
                  activity.term === course.term &&
                  activity.studentCompletedQuiz.find(
                      (studentCompletedQuizEntry) =>
                          studentCompletedQuizEntry.toString() === student.studentId.toString(),
                  ),
        );
        let pointRating = 0;
        let percentageRating = 0;
        if (studentAttendedActivities.length) {
            const studentTotalQuestions = studentAttendedActivities
                .map((activity) => activity.questions)
                .flat();
            const studentCorrectAnsweredQuestions = studentAttendedActivities
                .filter((studentAttendedActivity) => {
                    const { students } = studentAttendedActivity;
                    let correctAnswered = students.find(
                        (studentEntry) =>
                            studentEntry._studentId.toString() === student.studentId.toString(),
                    );
                    if (correctAnswered) {
                        correctAnswered = correctAnswered.questions.filter((question) => {
                            const { _questionId, _optionId } = question;
                            const activityQuestion = getAllQuestions.find(
                                (getAllQuestion) =>
                                    getAllQuestion._id.toString() === _questionId.toString(),
                            );
                            if (activityQuestion) {
                                const { options } = activityQuestion;
                                if (
                                    options.length &&
                                    options.find(
                                        (option) =>
                                            _optionId &&
                                            option._id.toString() === _optionId.toString() &&
                                            option.answer,
                                    )
                                )
                                    return true;
                            }
                            return false;
                        });
                        studentAttendedActivity.correctQuestions = correctAnswered;
                        return studentAttendedActivity;
                    }
                    return false;
                })
                .map((activity) => activity.correctQuestions)
                .flat();
            // in rating point format
            pointRating = studentCorrectAnsweredQuestions.length / studentTotalQuestions.length;
            // in rating percentage format
            percentageRating = (pointRating / 5) * 100;
        }
        student.quizRating = {
            pointRating,
            percentageRating,
        };
    });
    return students;
};

// Get individualReport -> level wise students
exports.individualReport = async (req, res) => {
    try {
        const {
            params: { userId },
            query: {
                institutionCalendarId,
                programId,
                courseId,
                term,
                levelNo,
                rotation,
                rotationCount,
            },
        } = req;
        const studentGroups = await studentGroupList(
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
        );
        let students = studentGroups.studentWithGroupList.map((studentGroup) => {
            const { students, group_name } = studentGroup;
            const studentMapped = students.map((student) => {
                return {
                    studentId: student._student_id,
                    name: student.name,
                    academicNo: student.academic_no,
                    groupName: group_name,
                };
            });
            studentGroup.students = studentMapped;
            return studentGroup;
        });
        students = students.map((student) => student.students).flat();

        //selfEvaluationSurveyReport
        students = await selfEvaluationSurveyReportForStudents(students, {
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
            institutionCalendarId,
        });
        // quizReport
        students = await quizReportForStudents(students, {
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
            institutionCalendarId,
        });

        return res.status(200).send(response_function(res, 200, true, 'Student List', students));
    } catch (error) {
        console.log('error', error);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Internal Server Issue', error.toString()));
    }
};

exports.comparisonReport = async (req, res) => {
    const {
        params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term, tab },
        query: { rotation, rotationCount },
    } = req;
    try {
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            tab,
            rotation,
            rotationCount,
        };
        logger.info(
            requestedParams,
            'Survey -> comparisonReport -> Course Comparison Report %s start',
            tab,
        );
        const { studentWithGroupList, studentWithOutGroupList } = await studentGroupList(
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
        );
        const studentIds = studentWithOutGroupList.map((studentElement) =>
            studentElement._student_id.toString(),
        );
        const response = {};
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        ).session_flow_data;
        // console.time('courseSchedule');
        // const courseScheduleDatas = await get_list(
        //     courseScheduleCollection,
        //     {
        //         // isDeleted: false,
        //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //         _program_id: convertToMongoObjectId(programId),
        //         _course_id: convertToMongoObjectId(courseId),
        //         year_no: yearNo.toString(),
        //         level_no: levelNo.toString(),
        //         // term,
        //         // status: { $in: [COMPLETED, MISSED] },
        //         rotation_count: rotationCount,
        //     },
        //     {
        //         isActive: 1,
        //         status: 1,
        //         'students._id': 1,
        //     },
        // );
        // console.timeEnd('courseSchedule');

        console.time('surveyCollection');
        const courseSurveyData = await get_list(
            StudentSessionSurvey,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, _user_id: 1, slos: 1 },
        );
        console.timeEnd('surveyCollection');
        courseSurveyData.data = courseSurveyData.status ? courseSurveyData.data : [];
        console.time('activityQuestionList');
        const activityList = await activityQuestionList({
            institutionCalendarId,
            courseId,
            term,
            rotationCount,
        });
        console.timeEnd('activityQuestionList');

        // User Survey Yet to Rate
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
            program_name: courseData.administration.program_name,
            yearNo,
            levelNo,
            term,
            rotationCount,
            studentCount: studentIds.length,
        };
        switch (tab) {
            case 'session':
                response.sessionRating = await comparisonReportSummary({
                    studentWithGroupList,
                    courseSessionOrder,
                    userSurveyData: courseSurveyData.data,
                    activityList,
                    studentIds,
                });
                break;
            case SLO:
                response.sloRating = await comparisonReportSlo({
                    studentWithGroupList,
                    courseSessionOrder,
                    userSurveyData: courseSurveyData.data,
                    activityList,
                    studentIds,
                });
                response.courseData.sloCount =
                    response.sloRating && response.sloRating.ratingReport
                        ? response.sloRating.ratingReport.length
                        : 0;
                break;
            case CLO:
                {
                    const courseClo = await courseCLOLists({ courseData });
                    response.cloRating = await comparisonReportClo({
                        studentWithGroupList,
                        courseSessionOrder,
                        userSurveyData: courseSurveyData.data,
                        activityList,
                        studentIds,
                        courseClo,
                    });
                }
                break;
            default:
                break;
        }
        logger.info('Survey -> comparisonReport -> Course Comparison Report %s End', tab);
        return res
            .status(200)
            .send(response_function(res, 200, true, 'Course Self Evaluation Survey', response));
    } catch (error) {
        logger.error(error, 'Survey -> comparisonReport -> Course Comparison Report %s Error', tab);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Internal Server Issue', error.toString()));
    }
};

exports.allSLOAverage = async (req, res) => {
    const {
        params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term, tab },
        query: { rotation, rotationCount },
    } = req;
    try {
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            tab,
            rotation,
            rotationCount,
        };
        logger.info(
            requestedParams,
            'Survey -> allSLOaverage ->  SLO outcome analysis %s start',
            tab,
        );
        const response = {};
        let start;
        let end;
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        ).session_flow_data;
        console.time('courseSchedule');
        const courseScheduleDatas = await get_list(
            courseScheduleCollection,
            {
                // isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                year_no: yearNo.toString(),
                level_no: levelNo.toString(),
                // term,
                // status: { $in: [COMPLETED, MISSED] },
                rotation_count: rotationCount,
            },
            {
                isActive: 1,
                status: 1,
                'students._id': 1,
            },
        );
        console.timeEnd('courseSchedule');
        const studentIds = courseScheduleDatas.status
            ? [
                  ...new Set(
                      courseScheduleDatas.data
                          .map((scheduleElement) =>
                              scheduleElement.students
                                  .map((studentElement) => studentElement._id.toString())
                                  .flat(),
                          )
                          .flat(),
                  ),
              ]
            : [];
        console.time('surveyCollection');
        const courseSurveyData = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, _user_id: 1, slos: 1 },
        );
        const studentGroups = await studentGroupList(
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
        );
        const otherSurveyData = clone(courseSurveyData.data);
        const programReportSetting = await programReportSettings.findOne(
            { _program_id: convertToMongoObjectId(programId) },
            { selfEvaluationSurvey: 1 },
        );
        if (
            programReportSetting &&
            programReportSetting.selfEvaluationSurvey &&
            programReportSetting.selfEvaluationSurvey.limit
        ) {
            start = programReportSetting.selfEvaluationSurvey.limit.from;
            end = programReportSetting.selfEvaluationSurvey.limit.to;
        } else {
            start = 1;
            end = 5;
        }
        console.timeEnd('surveyCollection');
        courseSurveyData.data = courseSurveyData.status ? courseSurveyData.data : [];
        const studentSessionSurvey = courseSurveyData.data;
        const userSurveyData = courseSurveyData.data.filter(
            (surveyElement) => surveyElement._user_id.toString() === userId,
        );
        // User Survey Yet to Rate
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
            program_name: courseData.administration.program_name,
            yearNo,
            levelNo,
            term,
            rotationCount,
            StudentCount: studentGroups.studentWithOutGroupList
                ? studentGroups.studentWithOutGroupList.length
                : 0,
        };

        const sloList = await allSLOList({
            courseSessionOrder,
            userSurveyData,
            otherSurveyData,
            studentIds,
        });

        const sloReport = [];
        let ratingOverAllSummary = [];
        for (slos of sloList) {
            let sloTotal = 0;
            let sumOfRating = 0;
            let ratingOne = 0;
            let ratingZero = 0;
            let ratingTwo = 0;
            let ratingThree = 0;
            let ratingFour = 0;
            let ratingFive = 0;
            let ratingSummary = [];

            for (const studentSurvey of studentSessionSurvey) {
                const getSingleSloIdList = studentSurvey.slos.find(
                    (studentSurvey) => studentSurvey._slo_id.toString() === slos._slo_id.toString(),
                );
                if (getSingleSloIdList) {
                    sloTotal++;
                    sumOfRating += getSingleSloIdList.sloRating;

                    if (getSingleSloIdList.sloRating === 0 && start === 0) {
                        ratingZero++;
                        if (!ratingSummary.find((ratingNOEntry) => ratingNOEntry.ratingNo === 0)) {
                            ratingSummary.push({
                                ratingNo: 0,
                                ratingCount: ratingZero,
                            });
                        } else {
                            ratingSummaryIndex = ratingSummary.findIndex(
                                (obj) => obj.ratingNo == 0,
                            );

                            ratingSummary[ratingSummaryIndex].ratingCount = ratingZero;
                        }
                    }

                    if (getSingleSloIdList.sloRating === 1) {
                        ratingOne++;
                        if (!ratingSummary.find((ratingNOEntry) => ratingNOEntry.ratingNo === 1)) {
                            ratingSummary.push({
                                ratingNo: 1,
                                ratingCount: ratingOne,
                            });
                        } else {
                            ratingSummaryIndex = ratingSummary.findIndex(
                                (obj) => obj.ratingNo == 1,
                            );

                            ratingSummary[ratingSummaryIndex].ratingCount = ratingOne;
                        }
                    }

                    if (getSingleSloIdList.sloRating === 2) {
                        ratingTwo++;
                        if (!ratingSummary.find((ratingNOEntry) => ratingNOEntry.ratingNo === 2)) {
                            ratingSummary.push({
                                ratingNo: 2,
                                ratingCount: ratingTwo,
                            });
                        } else {
                            ratingSummaryIndex = ratingSummary.findIndex(
                                (obj) => obj.ratingNo == 2,
                            );

                            ratingSummary[ratingSummaryIndex].ratingCount = ratingTwo;
                        }
                    }

                    if (getSingleSloIdList.sloRating === 3) {
                        ratingThree++;
                        if (!ratingSummary.find((ratingNOEntry) => ratingNOEntry.ratingNo === 3)) {
                            ratingSummary.push({
                                ratingNo: 3,
                                ratingCount: ratingThree,
                            });
                        } else {
                            ratingSummaryIndex = ratingSummary.findIndex(
                                (obj) => obj.ratingNo == 3,
                            );

                            ratingSummary[ratingSummaryIndex].ratingCount = ratingThree;
                        }
                    }

                    if (getSingleSloIdList.sloRating === 4) {
                        ratingFour++;
                        if (!ratingSummary.find((ratingNOEntry) => ratingNOEntry.ratingNo === 4)) {
                            ratingSummary.push({
                                ratingNo: 4,
                                ratingCount: ratingFour,
                            });
                        } else {
                            ratingSummaryIndex = ratingSummary.findIndex(
                                (obj) => obj.ratingNo == 4,
                            );

                            ratingSummary[ratingSummaryIndex].ratingCount = ratingFour;
                        }
                    }
                    if (getSingleSloIdList.sloRating === 5 && start !== 0) {
                        ratingFive++;
                        if (!ratingSummary.find((ratingNOEntry) => ratingNOEntry.ratingNo === 5)) {
                            ratingSummary.push({
                                ratingNo: 5,
                                ratingCount: ratingFive,
                            });
                        } else {
                            ratingSummaryIndex = ratingSummary.findIndex(
                                (obj) => obj.ratingNo == 5,
                            );

                            ratingSummary[ratingSummaryIndex].ratingCount = ratingFive;
                        }
                    }
                }
            }

            ratingSummary = ratingSummary.map(function (ratingSummaryEntry) {
                return {
                    ratingNo: ratingSummaryEntry.ratingNo,
                    ratingCount: ratingSummaryEntry.ratingCount,
                    percentage: (ratingSummaryEntry.ratingCount / sloTotal) * 100,
                };
            });
            ratingOverAllSummary.push(ratingSummary);
            sloReport.push({
                _slo_id: slos._slo_id,
                name: slos.name,
                no: slos.no,
                countOfRating: sloTotal,
                sumOfRating,
                avgValue: sumOfRating / sloTotal,
                percentage: (sumOfRating / sloTotal / 5) * 100,
                ratingSummary,
            });
        }

        const getOverAllAverage = { ratingSummary: [] };
        let overallSumRating = 0;
        for (sloReports of sloReport) {
            overallSumRating += sloReports.avgValue;
            getOverAllAverage.avgValue = overallSumRating / sloReport.length;
            getOverAllAverage.percentage = (overallSumRating / sloReport.length / 5) * 100;
        }
        ratingOverAllSummary = ratingOverAllSummary.flat();
        for (incremental = start; incremental <= end; incremental++) {
            const ratingBasedSummary = ratingOverAllSummary.filter(
                (ratingEntry) => ratingEntry.ratingNo === incremental,
            );

            const ratingsSum = ratingBasedSummary.reduce((n, { percentage }) => n + percentage, 0);

            getOverAllAverage.ratingSummary.push({
                ratingCount: incremental,
                percentage: ratingsSum / ratingBasedSummary.length,
            });
        }
        //    console.log('ratingOverAllSummary', ratingOverAllSummary.flat());
        response.getOverAllAverage = getOverAllAverage;
        response.sloReport = sloReport;

        logger.info(requestedParams, 'Survey -> allSLOaverage -> SLO outcome analysis %s End', tab);
        return res
            .status(200)
            .send(response_function(res, 200, true, ' SLO outcome analysis', response));
    } catch (error) {
        logger.error(error, 'Survey -> allSLOaverage ->SLO outcome analysis %s Error', tab);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Internal Server Issue', error.toString()));
    }
};

// Get selfEvaluationSurveyReport-level wise students
exports.selfEvaluationSurveyReport = async (req, res) => {
    try {
        const {
            query: {
                institutionCalendarId,
                programId,
                courseId,
                term,
                levelNo,
                rotationCount,
                tab,
            },
        } = req;
        const studentGroups = await studentGroupList(
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
        );
        let students = studentGroups.studentWithGroupList.map((studentGroup) => {
            const { students, group_name } = studentGroup;
            const studentMapped = students.map((student) => {
                return {
                    studentId: student._student_id,
                    name: student.name,
                    academicNo: student.academic_no,
                    groupName: group_name,
                };
            });
            studentGroup.students = studentMapped;
            return studentGroup;
        });
        students = students.map((student) => student.students).flat();
        //selfEvaluationSurveyReport
        const reports = await selfEvaluationSurveySessionCloSlo(students, {
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
            institutionCalendarId,
            tab,
        });

        return res
            .status(200)
            .send(response_function(res, 200, true, 'Self evaluation survey report', reports));
    } catch (error) {
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Internal Server Issue', error.toString()));
    }
};

exports.selfEvaluationSurveyCLO = async (req, res) => {
    try {
        const {
            params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term },
            query: { rotation, rotationCount },
            headers: { _institution_id },
        } = req;

        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
        };
        logger.info(requestedParams, 'Survey -> selfEvaluation -> selfEvaluationSurveyCLO');
        const response = {};
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );

        const query = {
            _institution_id,
            _program_id: programId,
        };
        const selfEvaluationSurveyData = await get(programReportSettings, query, {
            _id: 1,
            selfEvaluationSurvey: 1,
        });
        if (!selfEvaluationSurveyData.status)
            return res
                .status(200)
                .send(response_function(res, 200, true, 'Scale points data not found', {}));

        //scale points
        const limit = selfEvaluationSurveyData.data.selfEvaluationSurvey.limit;
        console.log(limit);
        const scalePoints = selfEvaluationSurveyData.data.selfEvaluationSurvey.scalePoints;
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        ).session_flow_data;
        console.time('courseSchedule');
        const courseScheduleDatas = await get_list(
            courseScheduleCollection,
            {
                // isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                year_no: yearNo.toString(),
                level_no: levelNo.toString(),
                // term,
                // status: { $in: [COMPLETED, MISSED] },
                rotation_count: rotationCount,
            },
            {
                isActive: 1,
                status: 1,
                'students._id': 1,
            },
        );
        console.timeEnd('courseSchedule');
        const studentIds = courseScheduleDatas.status
            ? [
                  ...new Set(
                      courseScheduleDatas.data
                          .map((scheduleElement) =>
                              scheduleElement.students
                                  .map((studentElement) => studentElement._id.toString())
                                  .flat(),
                          )
                          .flat(),
                  ),
              ]
            : [];

        console.time('surveyCollection');
        const courseSurveyData = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, _user_id: 1, slos: 1 },
        );
        console.timeEnd('surveyCollection');
        courseSurveyData.data = courseSurveyData.status ? courseSurveyData.data : [];
        /* const userSurveyData = courseSurveyData.data.filter(
                (surveyElement) =>
                    surveyElement._user_id.toString() === eleStudents._student_id.toString(),
            ); */
        const userSurveyData = courseSurveyData.data;
        const otherSurveyData = clone(courseSurveyData.data); /* courseSurveyData.data.filter(
                    (surveyElement) => surveyElement._user_id.toString() !== userId,
                ); */
        // User Survey Yet to Rate
        //const yetToRate = await courseYetToRate({ courseSessionOrder, userSurveyData });
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
            program_name: courseData.administration.program_name,
            yearNo,
            levelNo,
            term,
            rotationCount,
            //yetToRate,
        };
        const courseClo = await courseCLOLists({ courseData });
        //return res.send(courseClo);

        const sloRatingsData = await selfEvaluationOutcomeSloWise({
            courseSessionOrder,
            userSurveyData,
        });
        const clo = await mapSLORatingsWithCLO(courseClo, sloRatingsData);
        //return res.send(sloRatingsData);
        await calculateMarks(clo);
        clo.forEach((eleClo) => {
            const ratingSummary = [];
            for (let i = limit.from; i <= limit.to; i++) {
                if (limit.from == 0) {
                    const scalePointSlo = eleClo.slos.filter(
                        (eleSlos) =>
                            eleSlos && eleSlos.userRating !== null && eleSlos.userRating - 1 === i,
                    );
                    if (scalePointSlo.length) {
                        ratingSummary.push({
                            ratingNo: i,
                            ratingCount: scalePointSlo.length,
                            percentage: Math.round((scalePointSlo.length / eleClo.sloCount) * 100),
                        });
                    }
                } else {
                    const scalePointSlo = eleClo.slos.filter(
                        (eleSlos) =>
                            eleSlos && eleSlos.userRating !== null && eleSlos.userRating === i,
                    );
                    if (scalePointSlo.length) {
                        ratingSummary.push({
                            ratingNo: i,
                            ratingCount: scalePointSlo.length,
                            percentage: Math.round((scalePointSlo.length / eleClo.sloCount) * 100),
                        });
                    }
                }
            }
            //scalePoints.forEach((eleScalePoints) => {});
            eleClo.ratingSummary = ratingSummary;
            //delete eleClo.slos;
        });

        //response.scalePoints = scalePoints;
        response.cloRatingReport = clo;
        return res
            .status(200)
            .send(response_function(res, 200, true, 'Self Evaluation Clo List', response));
    } catch (error) {
        console.log('error', error);
        return res
            .status(500)
            .send(response_function(res, 500, false, 'Internal Server Issue', error.toString()));
    }
};
