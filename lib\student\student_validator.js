// const middleware = exports;
const Joi = require('joi');
const common_files = require('../../utility/common');
const constant = require('../../utility/constants');

exports.student = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _program_id: Joi.string().alphanum().length(24).error(error => {

                return error;
            }),
            reg_id: Joi.string().alphanum().min(3).max(10).error(error => {

                return error;
            }),
            name: Joi.object().keys({
                first: Joi.string().alphanum().min(3).max(20).error(error => {
                    return error;
                }),
                middle: Joi.string().alphanum().min(1).max(20).error(error => {
                    return error;
                }),
                last: Joi.string().alphanum().min(1).max(20).error(error => {
                    return error;
                }),
                family: Joi.string().alphanum().min(3).max(20).error(error => {
                    return error;
                }),
            }).unknown(true),
            gender: Joi.string().valid(constant.GENDER.MALE, constant.GENDER.FEMALE).error(error => {

                return error;
            }),
            password: Joi.string().min(4).max(20).error(error => {

                return error;
            }),
            mail: Joi.string().email().error(error => {

                return error;
            }),
            mobile: Joi.number().min(7).error(error => {

                return error;
            }),
            nationality: Joi.string().alphanum().length(24).error(error => {
                return error;
            }),
            nationality_id: Joi.string().min(3).max(20).error(error => {
                return error;
            }),
            face_reg_status: Joi.boolean().error(error => {

                return error;
            }),
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.student_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {

                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}