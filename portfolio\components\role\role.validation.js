const { Joi } = require('../../common/middlewares/validation');
const { STUDENT, STAFF } = require('../../common/utils/constants');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const createRoleSchema = Joi.object({
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().valid(STUDENT, STAFF).required(),
    }),
}).unknown(true);

const updateRoleSchema = Joi.object({
    query: Joi.object({
        roleId: objectIdRQSchema,
    }),
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().valid(STUDENT, STAFF).required(),
    }),
}).unknown(true);

const deleteRoleSchema = Joi.object({
    query: Joi.object({
        roleId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    createRoleSchema,
    updateRoleSchema,
    deleteRoleSchema,
};
