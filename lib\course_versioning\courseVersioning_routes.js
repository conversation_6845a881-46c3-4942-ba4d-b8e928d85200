const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
//controllers
const {
    createCourseVersioning,
    viewCourseDetails,
    editCourseDetails,
    deleteCourseDetails,
    deleteCourse,
    programCourseList,
    checkDuplicateVersionName,
    getVersionedCourseDetails,
} = require('./courseVersioning_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
//routers
router.post('/createCourseVersioning/:defaultCourseId', catchAsync(createCourseVersioning));
router.get(
    '/viewCourseDetails/:courseId',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    catchAsync(viewCourseDetails),
);
router.get(
    '/programCourseList',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    catchAsync(programCourseList),
);
router.put(
    '/editCourseDetails/:courseId',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    catchAsync(editCourseDetails),
);
// router.delete('/deleteCourseDetails', catchAsync(deleteCourseDetails));
router.delete(
    '/deleteCourse',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    catchAsync(deleteCourse),
);
router.get(
    '/checkDuplicateVersionName',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    catchAsync(checkDuplicateVersionName),
);
router.get(
    '/getVersionedCourseDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(getVersionedCourseDetails),
);
module.exports = router;
