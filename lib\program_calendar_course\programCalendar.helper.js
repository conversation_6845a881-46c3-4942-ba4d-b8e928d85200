const programSchema = require('../models/digi_programs');
const programCalendarSchema = require('../models/program_calendar');
const curriculumSchema = require('../models/digi_curriculum');
const { convertToMongoObjectId } = require('../utility/common');

const {
    DS_LEVEL_KEY,
    DS_YEAR_KEY,
    DS_TERM_KEY,
    DS_CURRICULUM_KEY,
    STUDENT_GROUP_MODE: { ROTATION },
} = require('../utility/constants');
const commonFilter = { isActive: true, isDeleted: false };

const nameCodeConvertor = ({ inputData, type }) => {
    switch (type) {
        case DS_LEVEL_KEY:
            return `L${inputData.length === 7 ? inputData.slice(-1) : inputData.slice(-2)}`;
        case DS_YEAR_KEY:
            return `Y${inputData.slice(-1)}`;
        case DS_TERM_KEY:
            return `${inputData.slice(0, 1).toUpperCase()}T`;
        case DS_CURRICULUM_KEY:
            return `${inputData.slice(-3)}`;
        case ROTATION:
            return `RG${inputData}`;
        default:
            return '';
    }
};

const programCalendarCodeAddingService = async ({ body = {} }) => {
    try {
        const { institutionCalendarIds, programIds, dbUpdate } = body;
        const programData = await programSchema
            .find(
                {
                    ...commonFilter,
                    ...(programIds && {
                        _id: { $in: programIds },
                    }),
                },
                { code: 1, term: 1 },
            )
            .lean();
        const programCalendarData = await programCalendarSchema
            .find(
                {
                    ...commonFilter,
                    ...(institutionCalendarIds && {
                        _institution_calendar_id: { $in: institutionCalendarIds },
                    }),
                    ...(programIds && {
                        _program_id: { $in: programIds },
                    }),
                    'level.termCode': { $exists: false },
                },
                {
                    // _program_id: 1,
                    // 'level.term': 1,
                    // 'level.year': 1,
                    // 'level.level_no': 1,
                    // 'level.curriculum': 1,
                    // 'level.rotation_course.rotation_count': 1,
                    level: 1,
                    updatedAt: 1,
                },
            )
            .lean();
        const curriculumData = await curriculumSchema
            .find(
                {
                    ...commonFilter,
                    ...(programIds && {
                        _program_id: { $in: programIds },
                    }),
                },
                { curriculum_name: 1, _program_id: 1, year_level: 1 },
            )
            .lean();
        const programBulkUpdate = [];
        const curriculumBulkUpdate = [];
        const programCalendarBulkUpdate = [];
        programCalendarData.forEach((programCalendarElement) => {
            programCalendarElement.level.forEach((levelElement) => {
                levelElement.yearCode = nameCodeConvertor({
                    inputData: levelElement.year,
                    type: DS_YEAR_KEY,
                });
                levelElement.levelCode = nameCodeConvertor({
                    inputData: levelElement.level_no,
                    type: DS_LEVEL_KEY,
                });
                levelElement.termCode = nameCodeConvertor({
                    inputData: levelElement.term,
                    type: DS_TERM_KEY,
                });
                levelElement.curriculumCode = nameCodeConvertor({
                    inputData: levelElement.curriculum,
                    type: DS_CURRICULUM_KEY,
                });
                if (levelElement.rotation_course.length) {
                    levelElement.rotation_course.forEach((rotationCourseElement) => {
                        rotationCourseElement.rotationCode = nameCodeConvertor({
                            inputData: rotationCourseElement.rotation_count,
                            type: ROTATION,
                        });
                    });
                }
            });
            programCalendarBulkUpdate.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(programCalendarElement._id),
                    },
                    update: {
                        $set: {
                            level: programCalendarElement.level,
                            updatedAt: programCalendarElement.updatedAt,
                        },
                    },
                },
            });
        });
        curriculumData.forEach((curriculumElement) => {
            curriculumElement.year_level.forEach((yearElement) => {
                yearElement.yearCode = nameCodeConvertor({
                    inputData: yearElement.y_type,
                    type: DS_YEAR_KEY,
                });
                yearElement.levels.forEach((levelElement) => {
                    levelElement.levelCode = nameCodeConvertor({
                        inputData: levelElement.level_name,
                        type: DS_LEVEL_KEY,
                    });
                });
            });
            curriculumBulkUpdate.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(curriculumElement._id),
                    },
                    update: {
                        $set: {
                            year_level: curriculumElement.year_level,
                            curriculumCode: nameCodeConvertor({
                                inputData: curriculumElement.curriculum_name,
                                type: DS_CURRICULUM_KEY,
                            }),
                            programCode: programData.find(
                                (programElement) =>
                                    String(programElement._id) ===
                                    String(curriculumElement._program_id),
                            )?.code,
                            updatedAt: curriculumElement.updatedAt,
                        },
                    },
                },
            });
        });
        programData.forEach((programElement) => {
            programElement.term.forEach((termElement) => {
                termElement.termCode = nameCodeConvertor({
                    inputData: termElement.term_name,
                    type: DS_TERM_KEY,
                });
            });
            programBulkUpdate.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(programElement._id),
                    },
                    update: {
                        $set: {
                            term: programElement.term,
                            updatedAt: programElement.updatedAt,
                        },
                    },
                },
            });
        });
        const dbUpdateObjects = {};
        if (dbUpdate) {
            dbUpdateObjects.program = await programSchema.bulkWrite(programBulkUpdate);
            dbUpdateObjects.curriculum = await curriculumSchema.bulkWrite(curriculumBulkUpdate);
            dbUpdateObjects.programCalendar = await programCalendarSchema.bulkWrite(
                programCalendarBulkUpdate,
            );
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                dbUpdateObjects,
                programCalendarData,
                programCalendarBulkUpdate,
                curriculumData,
                curriculumBulkUpdate,
                programData,
                programBulkUpdate,
            },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = { programCalendarCodeAddingService };
