const express = require('express');
const router = express.Router();
const { validate } = require('../../../middleware/validation');

const {
    ioUserRegistor,
    ioChannelCreate,
    getIoChannels,
    deleteChannel,
    sendNotification,
    addMembersToChannel,
    removeMembersFromChannel,
    getCourseDoc,
    getCourseAct,
    deleteMessage,
    createCourseChannels,
    getLocalLogger,
    deleteAllChannels,
    getSubjects,
    removeUserIoToken,
    blockUser,
    unBlockUser,
    getBlockedUsers,
} = require('./chat_controller');
const {
    validateGetCourseAct,
    validateGetCourseDoc,
    validateIoUserRegistor,
    validateIoChannelCreate,
    validateGetIoChannels,
    validateDeleteChannels,
    validateCreateCourseChannels,
    validateGetSubjects,
    validateRemoveUserIoToken,
    validateBlockUser,
} = require('./chat_validation');

router.post('/io-user-register', validateIoUserRegistor, ioUserRegistor);
router.post('/create-course-channels', validateCreateCourseChannels, createCourseChannels);
router.post('/io-channel-create', validateIoChannelCreate, ioChannelCreate);
router.get('/get-io-channels', validateGetIoChannels, getIoChannels);
router.delete('/delete-io-channel', validateDeleteChannels, deleteChannel);
router.post('/add-members-to-channel', addMembersToChannel);
router.post('/remove-members-from-channel', removeMembersFromChannel);
router.post('/send-offline-notification', sendNotification);
router.get('/get-course-doc', validateGetCourseDoc, getCourseDoc);
router.get('/get-course-act', validateGetCourseAct, getCourseAct);
router.post('/get-subjects', validateGetSubjects, getSubjects);
router.delete('/delete-message', deleteMessage);
router.delete('/delete-all-channels', deleteAllChannels);
router.get('/logs', getLocalLogger);
router.post('/remove-user-io-token', validateRemoveUserIoToken, removeUserIoToken);
router.post('/block-user', validateBlockUser, blockUser);
router.post('/unblock-user', validateBlockUser, unBlockUser);
router.post('/get-blocked-user', getBlockedUsers);

router.get('/test-env', (req, res) => {
    if (process.env !== 'production') res.send(process.env);
});

router.get('/load-cpu', (req, res) => {
    let num = 200;
    if (num === 0 || num === 1) return 1;
    for (let i = num - 1; i >= 1; i--) {
        num *= i;
    }
    console.log(num);
    res.write('yes');
    res.end();
});

router.get('/load-cpu-memory', (req, res) => {
    let mine = [];
    for (let i = 0 - 1; i <= 5000; i++) {
        console.log(i, 'inside map ');
        mine.push(i + 'added hello string' + 1233123);
        mine.map(function (num) {
            return num * 10;
        });
        if (i == 2999) {
            console.log('done reseonse 3000');
        }
    }
    mine = mine.slice(4900, mine.length - 1);
    console.log('done reseonse ');
    res.send(JSON.stringify(mine));
});

router.get('/load-cpu-memory-hello', (req, res) => {
    const mine = [];
    for (let i = 0 - 1; i <= 1000; i++) {
        mine.push(i + 'hello world' + 1233123);
        mine.map(function (num) {
            // console.log(i, 'inside map ', num);
            return num * 10;
        });
        if (i == 2999) {
            console.log('done reseonse 100');
        }
    }

    console.log('done reseonse ');
    res.send(JSON.stringify(mine));
});

router.get('/burst-cpu-memory', (req, res) => {
    const bursts = [];
    let data = [];

    console.time('burst-cpu-memory');
    for (let index = 0; index <= req.query.burst; index++) {
        bursts.push(`index = ${index}`);
        data = bursts.map((/* burst */) => +req.query.burst * 10);
    }
    console.timeEnd('burst-cpu-memory');

    res.send({ msg: `burst-cpu-memory = ${req.query.burst}`, data, bursts });
});

module.exports = router;
