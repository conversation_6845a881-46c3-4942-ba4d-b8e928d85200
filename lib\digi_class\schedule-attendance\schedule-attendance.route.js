const express = require('express');
const route = express.Router();
const controller = require('./schedule-attendance.controller');
const validator = require('./schedule-attendance.validator');
const scheduleService = require('./schedule-attendance.service');

const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

route.post(
    '/session_retake',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validator.sessionRetake,
    controller.sessionRetake,
);
route.post(
    '/end_session_retake',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validator.endSessionRetake,
    controller.endSessionRetake,
);
route.post(
    '/accept_attendance',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validator.acceptAttendance,
    controller.acceptAttendance,
);
route.post(
    '/get_student_status',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validator.getStudentStatus,
    controller.getStudentStatus,
);
route.post(
    '/attendance_report',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validator.attendanceReport,
    controller.attendanceReport,
);
route.post(
    '/submit_quiz',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validator.submitQuiz,
    controller.submitQuiz,
);
route.post(
    '/get-questions',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validator.getQuestions,
    controller.getQuestions,
);
route.post(
    '/student_submit_quiz',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validator.studentSubmitQuiz,
    controller.studentSubmitQuiz,
);
route.post(
    '/submitted-quiz-result',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validator.submittedQuizResult,
    controller.submittedQuizResult,
);
route.post(
    '/update-student-attendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validator.updateStudentAttendance,
    controller.attendanceStudentUpdate,
);
route.get(
    '/get-student-answered-questions/:_student_id/:_id/:scheduleAttendanceId',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validator.getStudentAnsweredQuestions,
    controller.getStudentAnsweredQuestions,
);
route.get(
    '/student-wise-attendance/:_id/:_student_id',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    validator.StudentAttendanceReport,
    controller.getStudentAttendanceReport,
);
route.put(
    '/update_quiz/:scheduleAttendanceId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validator.updateQuiz,
    controller.updateQuiz,
);
route.post(
    '/scheduleStudentFaceAnomalyAdd',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.scheduleStudentFaceAnomalyAdd,
);
route.get(
    '/scheduleStudentFaceAnomaly',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.scheduleStudentFaceAnomaly,
);
route.get(
    '/anomalyStudentScheduleList',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.anomalyStudentScheduleList,
);
route.get(
    '/anomalyStudentScheduleDayWise',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.anomalyStudentScheduleDayWise,
);
route.get(
    '/anomalyStudentFace',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.studentFaceAnomaly,
);
route.put(
    '/studentFaceAnomalyPure',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.studentFaceAnomalyPure,
);
route.put(
    '/studentFaceAnomalyLock',
    // [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.studentFaceAnomalyLock,
);
route.post(
    '/updateManualAttendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.updateManualAttendance,
);
route.put(
    '/updateChangingManualAttendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.updateChangingManualAttendance,
);

route.post(
    '/uploadScheduleAttachment',
    scheduleService.uploadDocument,
    controller.uploadScheduleAttachment,
);

module.exports = route;
