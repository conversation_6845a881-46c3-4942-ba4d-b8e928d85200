const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    institutionCalendarList,
    saveConfigure,
    getConfigureSetting,
    createCalendar,
} = require('./qapcSetting.controller');

const validator = require('./qapcSetting.validator');
const { validate } = require('../../../middleware/validation');
const { userPolicyAuthentication } = require('../../../middleware/policy.middleware');
//qapc configure setting
router.post(
    '/saveConfigure',
    [userPolicyAuthentication(['q360:academic_year:view', 'q360:academic_year:edit'])],
    validate(validator.saveConfigureValidator),
    catchAsync(saveConfigure),
);
router.get(
    '/getConfigureSetting',
    [userPolicyAuthentication(['q360:academic_year:view'])],
    validate(validator.getConfigureValidator),
    catchAsync(getConfigureSetting),
);
//create new calender
router.get(
    '/institutionCalendarList',
    [userPolicyAuthentication(['q360:academic_year:view'])],
    validate(validator.getConfigureValidator),
    catchAsync(institutionCalendarList),
);
router.post(
    '/createCalendar',
    [userPolicyAuthentication(['q360:academic_year:view', 'q360:academic_year:edit'])],
    validate(validator.createCalendarValidator),
    catchAsync(createCalendar),
);
module.exports = router;
