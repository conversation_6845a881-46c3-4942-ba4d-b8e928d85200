const Joi = require('joi');
const { UNIVERSITY, COLLEGE } = require('../../utility/enums');
const { ACTIVE, ARCHIVED } = require('../../utility/constants');

const institutionIdvalidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const updateInstituteLogoValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            logo: Joi.string().error(() => {
                return 'INSTITUTE_LOGO';
            }),
        }),
    })
    .unknown(true);

const addInstitutionValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            name: Joi.string()
                .min(1)
                .max(150)
                .required()
                .error(() => {
                    return 'INSTITUTE_NAME_VALIDATION';
                }),
            type: Joi.string()
                .valid(UNIVERSITY, COLLEGE)
                .required()
                .error(() => {
                    return 'INSTITUTE_TYPE_VALIDATION';
                }),
            code: Joi.string()
                .min(1)
                .max(20)
                .required()
                .error(() => {
                    return 'INSTITUTE_CODE_VALIDATION';
                }),
            accreditation: Joi.array()
                .items(
                    Joi.object().keys({
                        accreditationAgencyName: Joi.string().error(
                            () => 'ACCREDITATION_AGENCY_NAME',
                        ),
                        accreditationType: Joi.string().error(() => 'ACCREDITATION_TYPE'),
                        accreditationNumber: Joi.number().error(() => 'ACCREDITATION_NUMBER'),
                        accreditationValue: Joi.string().error(() => 'ACCREDITATION_VALUE'),
                        validityStart: Joi.string().error(() => {
                            return error;
                        }),
                        validityEnd: Joi.string().error(() => 'VALIDITY_END'),
                        others: Joi.string().error(() => 'OTHERS'),
                    }),
                )
                .error(() => {
                    return 'ACCREDITATION_VALIDATION';
                }),
            noOfColleges: Joi.number()
                .integer()
                .min(0)
                .error(() => 'NO_OF_COLLEGES_VALIDATION'),
            address: Joi.string()
                .min(1)
                .max(150)
                .required()
                .error(() => 'ADDRESS_VALIDATION'),
            country: Joi.string()
                .required()
                .error(() => 'COUNTRY_VALIDATION'),
            countryId: Joi.number()
                .integer()
                .required()
                .error(() => 'COUNTRY_ID_VALIDATION'),
            state: Joi.string()
                .required()
                .error(() => 'STATE_VALIDATION'),
            stateId: Joi.number()
                .integer()
                .required()
                .error(() => 'STATE_ID_VALIDATION'),
            district: Joi.string()
                .required()
                .error(() => 'DISTRICT_VALIDATION'),
            city: Joi.string()
                .required()
                .error(() => 'CITY_VALIDATION'),
            zipCode: Joi.string()
                .min(1)
                .max(25)
                .required()
                .error(() => 'ZIPCODE_VALIDATION'),
            isUniversity: Joi.boolean()
                .required()
                .error(() => 'ISUNIVERSITY_VALIDATION'),
            parentInstitute: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'PARENT_INSTITUTE_VALIDATION';
                }),
            logo: Joi.string().error(() => {
                return 'INSTITUTE_LOGO';
            }),
        }),
    })
    .unknown(true);

const updateInstitutionValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            name: Joi.string()
                .min(1)
                .max(150)
                .required()
                .error(() => {
                    return 'INSTITUTE_NAME_VALIDATION';
                }),
            type: Joi.string()
                .valid(UNIVERSITY, COLLEGE)
                .required()
                .error(() => {
                    return 'INSTITUTE_TYPE_VALIDATION';
                }),
            code: Joi.string()
                .min(1)
                .max(20)
                .required()
                .error(() => {
                    return 'INSTITUTE_CODE_VALIDATION';
                }),
            accreditation: Joi.array()
                .items(
                    Joi.object().keys({
                        accreditationAgencyName: Joi.string().error(
                            () => 'ACCREDITATION_AGENCY_NAME',
                        ),
                        accreditationType: Joi.string().error(() => 'ACCREDITATION_TYPE'),
                        accreditationNumber: Joi.number().error(() => 'ACCREDITATION_NUMBER'),
                        accreditationValue: Joi.string().error(() => 'ACCREDITATION_VALUE'),
                        validityStart: Joi.string().error(() => {
                            return error;
                        }),
                        validityEnd: Joi.string().error(() => 'VALIDITY_END'),
                        others: Joi.string().error(() => 'OTHERS'),
                    }),
                )
                .error(() => {
                    return 'ACCREDITATION_VALIDATION';
                }),
            noOfColleges: Joi.number()
                .integer()
                .min(0)
                .error(() => 'NO_OF_COLLEGES_VALIDATION'),
            address: Joi.string()
                .min(1)
                .max(150)
                .required()
                .error(() => 'ADDRESS_VALIDATION'),
            country: Joi.string()
                .required()
                .error(() => 'COUNTRY_VALIDATION'),
            countryId: Joi.number()
                .integer()
                .required()
                .error(() => 'COUNTRY_ID_VALIDATION'),
            state: Joi.string()
                .required()
                .error(() => 'STATE_VALIDATION'),
            stateId: Joi.number()
                .integer()
                .required()
                .error(() => 'STATE_ID_VALIDATION'),
            district: Joi.string()
                .required()
                .error(() => 'DISTRICT_VALIDATION'),
            city: Joi.string()
                .required()
                .error(() => 'CITY_VALIDATION'),
            zipCode: Joi.string()
                .min(1)
                .max(25)
                .required()
                .error(() => 'ZIPCODE_VALIDATION'),
            isUniversity: Joi.boolean()
                .required()
                .error(() => 'ISUNIVERSITY_VALIDATION'),
            parentInstitute: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'PARENT_INSTITUTE_VALIDATION';
                }),
            logo: Joi.string().error(() => {
                return 'INSTITUTE_LOGO';
            }),
        }),
    })
    .unknown(true);

const addOrEditInstituteDescriptionValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            description: Joi.string().error(() => 'INSTITUTE_DESCRIPTION_VALIDATION'),
            media: Joi.string().error(() => 'INSTITUTE_DESCRIPTION_MEDIA'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const addOrEditInstituteAccreditationValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            accreditation: Joi.array()
                .items(
                    Joi.object().keys({
                        accreditationAgencyName: Joi.string().error(
                            () => 'ACCREDITATION_AGENCY_NAME',
                        ),
                        accreditationType: Joi.string().error(() => 'ACCREDITATION_TYPE'),
                        accreditationNumber: Joi.number().error(() => 'ACCREDITATION_NUMBER'),
                        accreditationValue: Joi.string().error(() => 'ACCREDITATION_VALUE'),
                        validityStart: Joi.string().error(() => {
                            return error;
                        }),
                        validityEnd: Joi.string().error(() => 'VALIDITY_END'),
                        others: Joi.string().error(() => 'OTHERS'),
                    }),
                )
                .error(() => {
                    return 'ACCREDITATION_VALIDATION';
                }),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const addInstitutePortfolioValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            title: Joi.string().error(() => 'PORTFOLIO_TITLE_VALIDATION'),
            description: Joi.string().error(() => 'PORTFOLIO_DESCRIPTION_VALIDATION'),
            media: Joi.string().error(() => 'INSTITUTE_PORTFOLIO_MEDIA'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const editInstitutePorfolioValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            portfolioId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PORTFOLIO_ID_VALIDATION';
                }),
            title: Joi.string().error(() => 'PORTFOLIO_TITLE_VALIDATION'),
            description: Joi.string().error(() => 'PORTFOLIO_DESCRIPTION_VALIDATION'),
            media: Joi.string().error(() => 'INSTITUTE_PORTFOLIO_MEDIA'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const deleteInstitutePortfolioValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            portfolioId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PORTFOLIO_ID_VALIDATION';
                }),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const listInstitutesValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            pageNo: Joi.number()
                .integer()
                .required()
                .error(() => 'PAGENO_REQUIRED'),
            limit: Joi.number()
                .integer()
                .required()
                .error(() => 'LIMIT_REQUIRED'),
            search: Joi.string().error(() => 'INSTITUTION_SEARCH_VALIDATION'),
            sort: Joi.boolean().error(() => 'INSTITUTION_SORT_VALIDATION'),
            status: Joi.string()
                .valid(ACTIVE, ARCHIVED)
                .error(() => 'INSTITUTION_STATUS_VALIDATION'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

module.exports = {
    addInstitutionValidator,
    updateInstitutionValidator,
    institutionIdvalidator,
    addOrEditInstituteDescriptionValidator,
    addInstitutePortfolioValidator,
    editInstitutePorfolioValidator,
    deleteInstitutePortfolioValidator,
    listInstitutesValidator,
    addOrEditInstituteAccreditationValidator,
    updateInstituteLogoValidator,
};
