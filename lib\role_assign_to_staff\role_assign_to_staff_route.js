const express = require('express');
const route = express.Router();
const role_assign_to_staff_controller = require('./role_assign_to_staff_controller');
const validator = require('./role_assign_to_staff_validator');



route.post('/',validator.role_assign_to_staff_Validator,role_assign_to_staff_controller.insert);
route.put('/:id',validator.role_assign_to_staff_Validator_update,role_assign_to_staff_controller.update);
route.delete('/:id',validator.role_assign_to_staff_id,role_assign_to_staff_controller.delete);


module.exports = route;