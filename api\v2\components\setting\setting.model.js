const mongoose = require('mongoose');
const { Schema } = mongoose;

const { session, language, version } = require('./setting.schema');
const { SETTINGS, INSTITUTION } = require('../../utility/constants');
const {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
    JPG,
    PNG,
    PDF,
    SVG,
    DOC,
    COMMON,
    INDEPENDENT,
    DAILY,
    WEEKLY,
    CUSTOM,
} = require('../../utility/enums');

const { getConnectionString } = require('../../config/db.config');

const settingsSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
        },
        _parent_id: {
            type: Schema.Types.ObjectId,
            default: null,
        },
        globalConfiguration: {
            basicDetails: {
                timeZone: {
                    type: String,
                },
                language,
                isIndependentHours: {
                    type: Boolean,
                    default: false,
                },
                isGenderSegregation: {
                    type: Boolean,
                    default: false,
                },
                session,
                workingDays: [
                    {
                        name: {
                            type: String,
                            enum: [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY],
                        },
                        session,
                        isActive: {
                            type: Boolean,
                            default: true,
                        },
                    },
                ],
                breaks: [
                    {
                        name: {
                            type: String,
                            required: true,
                        },
                        session,
                        workingDays: [
                            {
                                type: String,
                                enum: [
                                    MONDAY,
                                    TUESDAY,
                                    WEDNESDAY,
                                    THURSDAY,
                                    FRIDAY,
                                    SATURDAY,
                                    SUNDAY,
                                ],
                            },
                        ],
                    },
                ],
                emailIdConfiguration: {
                    displayName: { type: String },
                    fromEmail: { type: String },
                    toEmail: { type: String },
                    userName: { type: String },
                    password: { type: String },
                    smtpClient: { type: String },
                    portNumber: { type: Number },
                    ttl_ssl: { type: Boolean },
                    server: { type: String },
                },
                eventType: [
                    {
                        name: {
                            type: String,
                            required: true,
                        },
                        isLeave: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
                privacySettings: [
                    {
                        name: {
                            type: String,
                            required: true,
                        },
                        isActive: { type: Boolean, default: true },
                    },
                ],
                vaccineLabelDetails: {
                    isActive: { type: Boolean, default: true },
                },
                vaccineConfiguration: [
                    {
                        categoryName: { type: String },
                        isActive: { type: Boolean, default: true },
                        isMandatory: { type: Boolean, default: true },
                        allowMixedVaccine: { type: Boolean, default: false },
                        vaccineDetails: [
                            {
                                vaccineNumber: { type: String },
                                vaccineName: { type: String },
                                vaccineType: { type: String },
                                antigenName: { type: String },
                                companyName: { type: String },
                                brandName: { type: String },
                                noOfDosage: { type: Number },
                                noOfBooster: { type: Number },
                                dosageDetails: [
                                    { labelName: { type: String }, days: { type: Number } },
                                ],
                                boosterDetails: [
                                    { labelName: { type: String }, days: { type: Number } },
                                ],
                            },
                        ],
                    },
                ],
                departmentHierarchyStructure: [
                    {
                        name: {
                            type: String,
                        },
                        isActive: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
            },
            programInput: {
                labelConfiguration: [
                    {
                        language: {
                            type: String,
                        },
                        labels: [
                            {
                                name: {
                                    type: String,
                                },
                                defaultInput: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                            },
                        ],
                    },
                ],
                programType: [
                    {
                        name: {
                            type: String,
                        },
                        code: {
                            type: String,
                        },
                    },
                ],
                curriculumNaming: [
                    {
                        mode: {
                            type: Number,
                        },
                        isDefault: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
                creditHours: [
                    {
                        mode: {
                            type: String,
                        },
                        isDefault: {
                            type: Boolean,
                            default: false,
                        },
                        withoutCreditHour: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
                programDurationFormat: [
                    {
                        format: {
                            type: String,
                        },
                        defaultInput: {
                            type: String,
                        },
                        isDefault: {
                            type: Boolean,
                            default: false,
                        },
                        withoutLevel: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
            },
            independentCourseInput: {
                creditHours: [
                    {
                        mode: {
                            type: String,
                        },
                        isDefault: {
                            type: Boolean,
                            default: false,
                        },
                    },
                ],
            },
            staffUserManagement: {
                designationConfiguration: {
                    designations: [
                        {
                            designationName: {
                                type: String,
                            },
                            isActive: { type: Boolean, default: true },
                        },
                    ],
                },
                labelFieldConfiguration: [
                    {
                        language: {
                            type: String,
                        },
                        basicDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                defaultValue: {
                                    type: String,
                                },
                                allowToChange: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        profileDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        addressDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        contactDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                    },
                ],
                biometricConfiguration: [
                    {
                        labelName: { type: String },
                        isActive: { type: Boolean, default: true },
                    },
                ],
                mailConfiguration: [
                    {
                        labelName: { type: String },
                        labelBody: { type: String },
                        isActive: { type: Boolean, default: true },
                    },
                ],
                documentConfiguration: {
                    documentFormat: {
                        type: [String],
                        enum: [JPG, PNG, PDF, SVG, DOC],
                    },
                    documentSize: {
                        type: String,
                        enum: [COMMON, INDEPENDENT],
                    },
                    documentMaximumSize: {
                        type: Number,
                    },
                    chooseDocuments: [
                        {
                            documentCategory: {
                                type: String,
                            },
                            allowMultipleDocuments: { type: Boolean, default: true },
                            documentCategoryDescription: {
                                type: String,
                            },
                            document: [
                                {
                                    labelName: {
                                        type: String,
                                    },
                                    size: {
                                        type: Number,
                                    },
                                    isActive: {
                                        type: Boolean,
                                        default: true,
                                    },
                                    isMandatory: {
                                        type: Boolean,
                                        default: false,
                                    },
                                },
                            ],
                        },
                    ],
                    notificationMail: {
                        label: { type: String },
                        time: { type: String },
                        labelBody: { type: String },
                    },

                    allowRemainderMail: {
                        isMandatoryDocuments: { type: Boolean, default: true },
                        recurring: {
                            type: String,
                            enum: [DAILY, WEEKLY, CUSTOM],
                        },
                        weeks: [
                            {
                                labelName: { type: String },
                                isActive: { type: Boolean, default: false },
                            },
                        ],
                        recurringInterval: { type: Number },
                        time: { type: String },
                    },
                },
                mailSettingsConfiguration: {
                    mailExpiration: { type: Number },
                    remainderSection: {
                        isActive: { type: Boolean, default: true },
                        recurring: {
                            type: String,
                            enum: [DAILY, WEEKLY, CUSTOM],
                        },
                        weeks: [
                            {
                                labelName: { type: String },
                                isActive: { type: Boolean, default: false },
                            },
                        ],
                        recurringInterval: { type: Number },
                        time: { type: String },
                    },
                    invalidProfileSection: {
                        isActive: { type: Boolean, default: true },
                        recurring: {
                            type: String,
                            enum: [DAILY, WEEKLY, CUSTOM],
                        },
                        weeks: [
                            {
                                labelName: { type: String },
                                isActive: { type: Boolean, default: false },
                            },
                        ],
                        recurringInterval: { type: Number },
                        time: { type: String },
                    },
                },
                vaccineLabelDetails: {
                    isActive: { type: Boolean, default: true },
                },
            },
            studentUserManagement: {
                designationConfiguration: {
                    designations: [
                        {
                            designationName: {
                                type: String,
                            },
                            isActive: { type: Boolean, default: true },
                        },
                    ],
                },
                labelFieldConfiguration: [
                    {
                        language: {
                            type: String,
                        },
                        basicDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                defaultValue: {
                                    type: String,
                                },
                                allowToChange: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        profileDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        addressDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        contactDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                        otherContactDetails: [
                            {
                                name: {
                                    type: String,
                                },
                                translatedInput: {
                                    type: String,
                                },
                                isActive: { type: Boolean, default: true },
                                isMandatory: { type: Boolean, default: true },
                                isCompulsory: { type: Boolean, default: false },
                                mappingKey: { type: String },
                            },
                        ],
                    },
                ],
                biometricConfiguration: [
                    {
                        labelName: { type: String },
                        isActive: { type: Boolean, default: true },
                    },
                ],
                mailConfiguration: [
                    {
                        labelName: { type: String },
                        labelBody: { type: String },
                        isActive: { type: Boolean, default: true },
                    },
                ],
                documentConfiguration: {
                    documentFormat: {
                        type: [String],
                        enum: [JPG, PNG, PDF, SVG, DOC],
                    },
                    documentSize: {
                        type: String,
                        enum: [COMMON, INDEPENDENT],
                    },
                    documentMaximumSize: {
                        type: Number,
                    },
                    chooseDocuments: [
                        {
                            documentCategory: {
                                type: String,
                            },
                            allowMultipleDocuments: { type: Boolean, default: true },
                            documentCategoryDescription: {
                                type: String,
                            },
                            document: [
                                {
                                    labelName: {
                                        type: String,
                                    },
                                    size: {
                                        type: Number,
                                    },
                                    isActive: {
                                        type: Boolean,
                                        default: true,
                                    },
                                    isMandatory: {
                                        type: Boolean,
                                        default: false,
                                    },
                                },
                            ],
                        },
                    ],
                    notificationMail: {
                        label: { type: String },
                        time: { type: String },
                        labelBody: { type: String },
                    },

                    allowRemainderMail: {
                        isMandatoryDocuments: { type: Boolean, default: true },
                        recurring: {
                            type: String,
                            enum: [DAILY, WEEKLY, CUSTOM],
                        },
                        weeks: [
                            {
                                labelName: { type: String },
                                isActive: { type: Boolean, default: false },
                            },
                        ],
                        recurringInterval: { type: Number },
                        time: { type: String },
                    },
                },
                mailSettingsConfiguration: {
                    mailExpiration: { type: Number },
                    remainderSection: {
                        isActive: { type: Boolean, default: true },
                        recurring: {
                            type: String,
                            enum: [DAILY, WEEKLY, CUSTOM],
                        },
                        weeks: [
                            {
                                labelName: { type: String },
                                isActive: { type: Boolean, default: false },
                            },
                        ],
                        recurringInterval: { type: Number },
                        time: { type: String },
                    },
                    invalidProfileSection: {
                        isActive: { type: Boolean, default: true },
                        recurring: {
                            type: String,
                            enum: [DAILY, WEEKLY, CUSTOM],
                        },
                        weeks: [
                            {
                                labelName: { type: String },
                                isActive: { type: Boolean, default: false },
                            },
                        ],
                        recurringInterval: { type: Number },
                        time: { type: String },
                    },
                },
                vaccineLabelDetails: {
                    isActive: { type: Boolean, default: true },
                },
            },
            departmentSubject: [
                { labelName: { type: String }, isActive: { type: Boolean, default: true } },
            ],
            departmentManagement: {
                departmentTypes: [{ labelName: { type: String } }],
            },
        },
    },
    { timestamps: true },
);

module.exports = settingsSchema;
