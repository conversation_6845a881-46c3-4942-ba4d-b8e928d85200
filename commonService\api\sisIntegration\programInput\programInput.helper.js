const courseSessionOrderSchemas = require('../../../../lib/models/digi_session_order');
const digiCourseSchemas = require('../../../../lib/models/digi_course');
const { convertToMongoObjectId, query: commonQuery } = require('../../../utility/common');
const moment = require('moment');
const {
    SERVICES: { REACT_APP_INSTITUTION_ID },
} = require('../../../../lib/utility/util_keys');

const createScheduleDeliveryArray = async ({
    courseId,
    courseData,
    courseScheduleSettingWithMultiStaffSetting,
    existingSessionOrder = [],
}) => {
    const courseDetails = await digiCourseSchemas
        .findOne(
            {
                isDeleted: false,
                isActive: true,
                _id: convertToMongoObjectId(courseId),
            },
            {
                'credit_hours._session_id': 1,
                'credit_hours.delivery_type._delivery_id': 1,
                'credit_hours.delivery_type.delivery_type': 1,
                'credit_hours.delivery_type.delivery_symbol': 1,
                'credit_hours.delivery_type.duration': 1,
                'administration._subject_id': 1,
                'administration.subject_name': 1,
            },
        )
        .lean();
    const sessionDeliveryMap = new Map();
    courseDetails.credit_hours.forEach((creditHoursElement) => {
        creditHoursElement.delivery_type.forEach((deliveryElement) => {
            if (sessionDeliveryMap.has(deliveryElement.delivery_symbol)) return;
            sessionDeliveryMap.set(deliveryElement.delivery_symbol, {
                _session_id: convertToMongoObjectId(creditHoursElement._session_id),
                delivery_type: deliveryElement.delivery_type,
                _delivery_id: convertToMongoObjectId(deliveryElement._delivery_id),
                delivery_symbol: deliveryElement.delivery_symbol,
                duration: deliveryElement.duration,
            });
        });
    });
    const courseSubjects = [
        {
            subject_name: courseDetails.administration.subject_name,
            _subject_id: courseDetails.administration._subject_id,
        },
    ];
    const scheduleDeliveryArray = [];
    const firstGroupArray = []; // New array for first groups
    const startDate = moment(courseData.start_date);
    const endDate = moment(courseData.end_date);

    // Define days of week starting from Sunday
    const daysOfWeek = [
        'sunday',
        'monday',
        'tuesday',
        'wednesday',
        'thursday',
        'friday',
        'saturday',
    ];

    // Track processed delivery codes to ensure we only take first group once per delivery
    const processedDeliveryCodes = new Set();

    for (const setting of courseScheduleSettingWithMultiStaffSetting) {
        const { deliveryCode, classNo, settingOccurrence } = setting;

        // Calculate total weeks between start and end date
        const totalWeeks = Math.ceil(endDate.diff(startDate, 'weeks', true));

        // For each occurrence in settingOccurrence
        for (const occurrence of settingOccurrence) {
            const daysWithY = [];

            // Find days marked with 'Y' and sort them according to daysOfWeek
            for (const day in occurrence) {
                if (occurrence[day] === 'Y') {
                    daysWithY.push({
                        day,
                        start: occurrence.start,
                        end: occurrence.end,
                        startDateTime: occurrence.startDateTime,
                        endDateTime: occurrence.endDateTime,
                    });
                }
            }

            // Sort days according to daysOfWeek array (Sunday first)
            daysWithY.sort((a, b) => {
                const indexA = daysOfWeek.indexOf(a.day);
                const indexB = daysOfWeek.indexOf(b.day);
                return indexA - indexB;
            });

            // Create schedule objects for each week
            for (let week = 1; week <= totalWeeks; week++) {
                for (const day of daysWithY) {
                    // Calculate the date for this week and day
                    const weekStartDate = moment(startDate).add(week - 1, 'weeks');
                    const targetDayIndex = daysOfWeek.indexOf(day.day);
                    const currentDayIndex = weekStartDate.day();
                    const daysToAdd = (targetDayIndex - currentDayIndex + 7) % 7;
                    const targetDate = moment(weekStartDate).add(daysToAdd, 'days');

                    // Only add if the date is within course date range
                    if (targetDate.isBetween(startDate, endDate, 'day', '[]')) {
                        // Create start and end datetime for this specific date
                        const startDateTime = moment(
                            targetDate.format('YYYY-MM-DD') +
                                ' ' +
                                moment(day.startDateTime).format('HH:mm:ss'),
                        );
                        const endDateTime = moment(
                            targetDate.format('YYYY-MM-DD') +
                                ' ' +
                                moment(day.endDateTime).format('HH:mm:ss'),
                        );

                        const scheduleObject = {
                            deliveryCode,
                            classNo,
                            week,
                            day: day.day,
                            start: day.start,
                            end: day.end,
                            startDateTime: startDateTime.format(),
                            endDateTime: endDateTime.format(),
                            schedule_date: targetDate.format(),
                        };

                        scheduleDeliveryArray.push(scheduleObject);

                        // Add to firstGroupArray if this is the first group for this delivery code
                        if (!processedDeliveryCodes.has(deliveryCode)) {
                            firstGroupArray.push({
                                deliveryCode,
                                classNo,
                                count: 0,
                            });
                            processedDeliveryCodes.add(deliveryCode);
                        }
                    }
                }
            }
        }
    }
    const filteredGroups = scheduleDeliveryArray
        .filter((item) => item.startDateTime < item.endDateTime)
        .sort((a, b) => new Date(a.startDateTime) - new Date(b.startDateTime));

    // Calculate groups with maximum schedules for each delivery code
    const deliveryGroupCounts = {};
    const maxScheduleGroups = new Set();

    // Count schedules for each delivery code and class number combination
    filteredGroups.forEach((item) => {
        const key = `${item.deliveryCode}_${item.classNo}`;
        if (!deliveryGroupCounts[key]) {
            deliveryGroupCounts[key] = {
                count: 0,
                deliveryCode: item.deliveryCode,
                classNo: item.classNo,
            };
        }
        deliveryGroupCounts[key].count++;
    });

    // Find the maximum count for each delivery code
    const maxCountsByDelivery = {};
    Object.values(deliveryGroupCounts).forEach((group) => {
        if (
            !maxCountsByDelivery[group.deliveryCode] ||
            maxCountsByDelivery[group.deliveryCode].count < group.count
        ) {
            maxCountsByDelivery[group.deliveryCode] = group;
        }
    });

    // Create set of groups with maximum schedules
    Object.values(maxCountsByDelivery).forEach((group) => {
        maxScheduleGroups.add(`${group.deliveryCode}_${group.classNo}`);
    });

    // Filter groups with maximum schedules
    const filteredMaxGroups = filteredGroups.filter((item) =>
        maxScheduleGroups.has(`${item.deliveryCode}_${item.classNo}`),
    );
    const courseSessionOrderData = [];
    let sessionNo = 1;
    filteredMaxGroups.forEach((scheduleElement) => {
        const sessionDeliveryIndex = firstGroupArray.findIndex(
            (item) => item.deliveryCode === scheduleElement.deliveryCode,
        );
        firstGroupArray[sessionDeliveryIndex].count++;
        const sessionDelivery = sessionDeliveryMap.get(scheduleElement.deliveryCode);
        const existingSessionDelivery = existingSessionOrder.find(
            (item) =>
                item.delivery_symbol === scheduleElement.deliveryCode &&
                item.delivery_no === firstGroupArray[sessionDeliveryIndex].count,
        );
        courseSessionOrderData.push({
            _id: existingSessionDelivery?._id || convertToMongoObjectId(scheduleElement._id),
            week: existingSessionDelivery?.week || [],
            slo: existingSessionDelivery?.slo || [],
            s_no: sessionNo,
            ...sessionDelivery,
            delivery_no: firstGroupArray[sessionDeliveryIndex].count,
            delivery_topic: `${sessionDelivery.delivery_type} ${firstGroupArray[sessionDeliveryIndex].count}`,
            subjects: courseSubjects,
        });
        sessionNo++;
    });
    return {
        scheduleDeliveryArray,
        allGroups: filteredGroups,
        maxScheduleGroups: filteredMaxGroups,
        firstGroupArray,
        courseSessionOrderData,
    };
};

const getCourseSessionOrderData = async ({
    courseId,
    programId,
    courseData,
    courseScheduleSettingWithMultiStaffSetting,
}) => {
    const sessionOrderProjection = {
        // 'session_flow_data.delivery_symbol': 1,
        // 'session_flow_data._session_id': 1,
        // 'session_flow_data.delivery_topic': 1,
        // 'session_flow_data.delivery_type': 1,
        // 'session_flow_data.subjects._subject_id': 1,
        // 'session_flow_data.subjects.subject_name': 1,
        // 'session_flow_data._delivery_id': 1,
        // 'session_flow_data.delivery_no': 1,
        // 'session_flow_data.s_no': 1,
        // 'session_flow_data._id': 1,
        session_flow_data: 1,
        synced: 1,
    };

    try {
        const courseSessionOrderQuery = {
            ...commonQuery,
            _course_id: convertToMongoObjectId(courseId),
            _program_id: convertToMongoObjectId(programId),
            _institution_id: convertToMongoObjectId(REACT_APP_INSTITUTION_ID),
        };

        const existingSessionOrder = await courseSessionOrderSchemas
            .findOne(courseSessionOrderQuery, sessionOrderProjection)
            .lean();
        if (existingSessionOrder && existingSessionOrder?.synced) {
            return existingSessionOrder;
        }
        if (existingSessionOrder) {
            if (existingSessionOrder?.synced) {
                return { session_flow_data: existingSessionOrder.session_flow_data };
            }
            const { courseSessionOrderData: updatedSessionFlowData } =
                await createScheduleDeliveryArray({
                    courseId,
                    courseData,
                    courseScheduleSettingWithMultiStaffSetting,
                    existingSessionOrder: existingSessionOrder?.session_flow_data || [],
                });
            // TODO: Uncomment this when we want to update the existing course session order only Topic Change
            // Update delivery_topic in session_flow_data
            // const updatedSessionFlowData = existingSessionOrder.session_flow_data.map(
            //     (sessionElement) => ({
            //         ...sessionElement,
            //         delivery_topic: `${sessionElement.delivery_type} ${sessionElement.delivery_no}`,
            //     }),
            // );
            await courseSessionOrderSchemas.updateOne(courseSessionOrderQuery, {
                $set: { isDeleted: true, isActive: false },
            });
            const createdOrder = await courseSessionOrderSchemas
                .create({
                    ...courseSessionOrderQuery,
                    session_flow_data: updatedSessionFlowData,
                    synced: true,
                })
                .then((createdOrder) =>
                    courseSessionOrderSchemas
                        .findOne({ _id: createdOrder._id }, sessionOrderProjection)
                        .lean(),
                );
            return createdOrder;
        }
        return { session_flow_data: [] };

        //! Important: This function is called only when the course session order is not present in the database
        /*
        const { courseSessionOrderData } = await createScheduleDeliveryArray({
            courseId,
            courseData,
            courseScheduleSettingWithMultiStaffSetting,
        });
        // TODO: Uncomment this when we want to update the existing course session order as well
        await courseSessionOrderSchemas.updateMany(courseSessionOrderQuery, {
            $set: { isDeleted: true, isActive: false },
        });

        // Create and return the new record
        const courseSessionData = await courseSessionOrderSchemas
            .create({
                ...courseSessionOrderQuery,
                session_flow_data: courseSessionOrderData,
            })
            .then((createdOrder) =>
                courseSessionOrderSchemas
                    .findOne({ _id: createdOrder._id }, sessionOrderProjection)
                    .lean(),
            );

        return courseSessionData; */
    } catch (error) {
        console.error('Error in getCourseSessionOrderData:', error);
        throw error instanceof Error ? error : new Error(error);
    }
};

module.exports = {
    getCourseSessionOrderData,
    createScheduleDeliveryArray,
};
