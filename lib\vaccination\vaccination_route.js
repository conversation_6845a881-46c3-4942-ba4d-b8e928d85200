const express = require('express');
const route = express.Router();

// controller;
const { insert, update, list, deleteVaccination } = require('./vaccination_controller');

const { validate } = require('../../middleware/validation');

const {
    getAddVaccinationValidate: { body: getAddVaccinationValidateSchema },
} = require('./vaccination_validator');

route.post('/', validate([{ schema: getAddVaccinationValidateSchema, property: 'body' }]), insert);
route.put(
    '/:id',
    validate([{ schema: getAddVaccinationValidateSchema, property: 'body' }]),
    update,
);
route.delete('/:id', deleteVaccination);
route.get('/', list);

module.exports = route;
