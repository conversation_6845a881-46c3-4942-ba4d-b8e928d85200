const Joi = require('joi');
const { ACADEMIC, ADMIN, ADD, REMOVE } = require('../../utility/constants');

const addDepartmentValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            departmentName: Joi.string()
                .min(3)
                .max(150)
                .required()
                .error(() => {
                    return 'DEPARTMENT_NAME_REQUIRED';
                }),
            _parent_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'INSTITUTE_PARENT_ID';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'ID_VALIDATION';
                }),
            programName: Joi.string().error((error) => {
                return error;
            }),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTE_ID_REQUIRED'),
            type: Joi.string()
                .required()
                .error(() => 'DEPARTMENT_SUBJECT_TYPE_REQUIRED'),
        }),
    })
    .unknown(true);

const getDepartmentValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'DEPARTMENT_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const getDepartmentsValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            limit: Joi.number().integer(),
            pageNo: Joi.number().integer(),
            programId: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'PROGRAM_ID_REQUIRED'),
            type: Joi.string()
                .valid(ACADEMIC, ADMIN)
                .required()
                .error(() => 'DEPARTMENT_SUBJECT_TYPE_REQUIRED'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'DEPARTMENT_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const addSubjectsValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            subjects: Joi.array()
                .items(
                    Joi.object().keys({
                        subjectName: Joi.string()
                            .required()
                            .error(() => 'SUBJECT_NAME'),
                    }),
                )
                .required()
                .error(() => 'SUBJECTS_REQUIRED'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'DEPARTMENT_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const getProgramsDeptsValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'INSTITUTION_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const editDepartmentValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            departmentName: Joi.string()
                .required()
                .error(() => 'DEPARTMENT_NAME_REQUIRED'),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'PROGRAM_ID_REQUIRED'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'DEPARTMENT_ID_REQUIRED'),
        }),
    })
    .unknown(true);

const shareDepartmentValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            departmentId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return "'DEPARTMENT_ID_REQUIRED'";
                }),
            status: Joi.string()
                .valid(ADD, REMOVE)
                .required()
                .error((error) => {
                    return error;
                }),
            sharedPrograms: Joi.array().items(
                Joi.object().keys({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'PROGRAM_ID_REQUIRED';
                        }),
                    programName: Joi.string()
                        .required()
                        .error(() => 'PROGRAM_NAME_REQUIRED'),
                }),
            ),
        }),
    })
    .unknown(true);

const shareSubjectsValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            subjectId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'SUBJECT_ID_REQUIRED';
                }),
            departmentId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'DEPARTMENT_ID_REQUIRED';
                }),
            status: Joi.string()
                .valid(ADD, REMOVE)
                .required()
                .error((error) => {
                    return error;
                }),
            sharedProgramsAndDepartments: Joi.array().items(
                Joi.object().keys({
                    _program_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'PROGRAM_ID_REQUIRED';
                        }),
                    programName: Joi.string()
                        .required()
                        .error(() => 'PROGRAM_NAME_REQUIRED'),
                    _department_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error(() => {
                            return 'DEPARTMENT_ID_REQUIRED';
                        }),
                    departmentName: Joi.string()
                        .required()
                        .error(() => 'DEPARTMENT_NAME_REQUIRED'),
                }),
            ),
        }),
    })
    .unknown(true);

const deleteDepartmentValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'DEPARTMENT_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const editSubjectValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            subjectId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'SUBJECT_ID_REQUIRED';
                }),
            departmentId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'DEPARTMENT_ID_REQUIRED';
                }),
            subjectName: Joi.string()
                .required()
                .error(() => 'SUBJECT_NAME_REQUIRED'),
        }),
    })
    .unknown(true);

const deleteSubjectValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            subjectId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'SUBJECT_ID_REQUIRED';
                }),
            departmentId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'DEPARTMENT_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const courseDepartmentListValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const deliveringSubjectsValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const instituteDepartmentsValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        query: Joi.object().keys({
            type: Joi.string()
                .valid(ACADEMIC, ADMIN)
                .required()
                .error(() => 'DEPARTMENT_SUBJECT_TYPE_REQUIRED'),
            showShared: Joi.string()
                .required()
                .error(() => 'SHOW_SHARED_REQUIRED'),
            searchKey: Joi.string().error(() => 'SEARCH_KEY'),
            pageNo: Joi.number()
                .integer()
                .required()
                .error(() => 'PAGENO_REQUIRED'),
            limit: Joi.number()
                .integer()
                .required()
                .error(() => 'LIMIT_REQUIRED'),
        }),
        body: Joi.object().keys({
            filterProgramIds: Joi.array().items(Joi.string().alphanum().length(24)),
            filterSharedWithIds: Joi.array().items(Joi.string().alphanum().length(24)),
            filterSubjectIds: Joi.array().items(Joi.string().alphanum().length(24)),
            filterDepartmentIds: Joi.array().items(Joi.string().alphanum().length(24)),
            filterSharedFromIds: Joi.array().items(Joi.string().alphanum().length(24)),
        }),
    })
    .unknown(true);

const editDepartmentRestructureValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            departmentName: Joi.string()
                .required()
                .error(() => 'DEPARTMENT_NAME_REQUIRED'),
            programName: Joi.string().error(() => 'PROGRAM_NAME_REQUIRED'),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => 'PROGRAM_ID_REQUIRED'),
            isDepartmentAssignedOrShared: Joi.boolean()
                .required()
                .error(() => 'ISDEPARTMENTASSIGNED_KEY_REQUIRED'),
            type: Joi.string().error(() => 'DEPARTMENT_SUBJECT_TYPE_REQUIRED'),
        }),
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => 'DEPARTMENT_ID_REQUIRED'),
        }),
    })
    .unknown(true);

module.exports = {
    addDepartmentValidator,
    getDepartmentValidator,
    getDepartmentsValidator,
    addSubjectsValidator,
    shareSubjectsValidator,
    editDepartmentValidator,
    shareDepartmentValidator,
    deleteDepartmentValidator,
    editSubjectValidator,
    deleteSubjectValidator,
    getProgramsDeptsValidator,
    courseDepartmentListValidator,
    deliveringSubjectsValidator,
    instituteDepartmentsValidator,
    editDepartmentRestructureValidator,
};
