const common_files = require('../utility/common');
const constant = require('../utility/constants');
const role = require('mongoose').model(constant.ROLE);
const permission = require('mongoose').model(constant.PERMISSION);
const privilege = require('mongoose').model(constant.PRIVILEGE);
const role_set = require('mongoose').model(constant.ROLE_SET);
const user = require('mongoose').model(constant.USER);
const permission_set = require('mongoose').model(constant.PERMISSION_SET);
const base_control = require('../base/base_controller');
// const program_formate = require('./role_management_formate');
const ObjectId = common_files.convertToMongoObjectId;

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false } },
        {
            $lookup: {
                from: constant.PROGRAM_CALENDAR,
                localField: '_program_calendar_id',
                foreignField: '_id',
                as: 'program_calendar',
            },
        },
        { $unwind: '$program_calendar' },
        { $skip: skips },
        { $limit: limits },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('ROLE_MANAGEMENT_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            /* doc.data */ program_formate.role_management(doc.data),
        );
        // common_files.list_all_response(res, 200, true, "role_management list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* program_formate.role_management(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.course_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const course_id = ObjectId(req.params.course);
    const aggre = [
        { $match: { _id: calendar_id, isDeleted: false } },
        { $unwind: '$level' },
        { $unwind: '$level.course' },
        { $match: { 'level.course._course_id': course_id } },
        // { $project: { _id: 0, courses: 1, 'level.course': 1 } },
        {
            $lookup: {
                from: constant.COURSE,
                localField: 'level.course._course_id',
                foreignField: '_id',
                as: 'level.course.course_details',
            },
        },
        {
            $lookup: {
                from: constant.CALENDAR_EVENT,
                localField: 'level.course._event_id',
                foreignField: '_id',
                as: 'level.course.event_details',
            },
        },
        // { $unwind: '$program_calendar' },
        { $unwind: '$level.course.course_details' },
        {
            $group: {
                _id: '$_id',
                level: { $first: '$level.level_no' },
                course_name: { $first: '$level.course.course_details.courses_name' },
                course_type: { $first: '$level.course.course_details.model' },
                start_date: { $first: '$level.course.start_date' },
                end_date: { $first: '$level.course.end_date' },
                color_code: { $first: '$level.course.color_code' },
                events: { $first: '$level.course.event_details' },
            },
        },
        {
            $project: {
                'events.isDeleted': 0,
                'events.isActive': 0,
                'events.event_calendar': 0,
                'events._calendar_id': 0,
                'events.review': 0,
                'events.createdAt': 0,
                'events.updatedAt': 0,
                'events.__v': 0,
            },
        },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'role_management details',
            doc.data /* program_formate.role_management_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.rotation_course_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const course_id = ObjectId(req.params.course);
    console.log(req.params.rotation_no);
    const aggre = [
        { $match: { _id: calendar_id, isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$level.rotation_course', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.rotation': 'yes' } },
        { $match: { 'level.rotation_course.rotation_count': parseInt(req.params.rotation_no) } },
        { $unwind: { path: '$level.rotation_course.course', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.rotation_course.course._course_id': course_id } },
        {
            $lookup: {
                from: constant.COURSE,
                localField: 'level.rotation_course.course._course_id',
                foreignField: '_id',
                as: 'course_details',
            },
        },
        {
            $lookup: {
                from: constant.CALENDAR_EVENT,
                localField: 'level.rotation_course.course._event_id',
                foreignField: '_id',
                as: 'event_details',
            },
        },
        { $unwind: { path: '$course_details', preserveNullAndEmptyArrays: true } },
        {
            $group: {
                _id: '$_id',
                level: { $first: '$level.level_no' },
                course_name: { $first: '$course_details.courses_name' },
                course_type: { $first: '$course_details.model' },
                start_date: { $first: '$level.rotation_course.course.start_date' },
                end_date: { $first: '$level.rotation_course.course.end_date' },
                color_code: { $first: '$level.rotation_course.course.color_code' },
                events: { $first: '$event_details' },
            },
        },
        {
            $project: {
                'events.isDeleted': 0,
                'events.isActive': 0,
                'events.event_calendar': 0,
                'events._calendar_id': 0,
                'events.review': 0,
                'events.createdAt': 0,
                'events.updatedAt': 0,
                'events.__v': 0,
            },
        },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'Program_calendar Rotation Course details',
            doc.data /* program_formate.role_management_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to find course',
            'Error Unable to find course : ' + doc.data,
        );
    }
};

exports.course_list_level_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false },
        { 'level.level_no': 1, 'level._program_id': 1 },
    );
    if (calendar_check.status) {
        const program_id =
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no == req.params.level_no)
            ]._program_id;
        const doc = await base_control.get_list(
            course,
            { _program_id: program_id, study_level: req.params.level_no },
            { model: 1, courses_name: 1 },
        );
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                'Program_calendar Course list',
                doc.data /* program_formate.role_management_ID(doc.data[0]) */,
            );
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error id not match',
            'Check Parshing reference ID',
        );
    }
};

exports.course_list_year_get = async (req, res) => {
    const calendar_id = ObjectId(req.params.calendar);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false },
        { 'level.year': 1, 'level.level_no': 1, 'level._program_id': 1 },
    );
    const program_id = [];
    calendar_check.data.level.forEach((element) => {
        if (element.year == req.params.year_no) {
            console.log(program_id.indexOf(element._program_id));
            if (program_id.indexOf(element._program_id.toString()) == -1) {
                program_id.push(element._program_id.toString());
            }
        }
    });
    const doc = await base_control.get_list(
        course,
        { _program_id: { $in: program_id }, study_year: req.params.year_no },
        { level_no: '$study_level', study_level: 1, model: 1, courses_name: 1 },
    );
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'Program_calendar Course list',
            doc.data /* program_formate.role_management_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.insert = async (req, res) => {
    const calendar_id = ObjectId(req.body._calendar_id);
    const calendar_check = await base_control.get(
        program_calendar,
        { _id: calendar_id, isDeleted: false },
        { 'level.level_no': 1, 'level.rotation': 1, 'level.rotation_count': 1 },
    );
    const course_checks = await base_control.check_id(course, {
        _id: { $in: req.body._course_id },
        isDeleted: false,
    });
    const event_checks = await base_control.check_id(calendar_event, {
        _id: { $in: req.body._event_id },
        isDeleted: false,
    });
    if (calendar_check.status && course_checks.status && event_checks.status) {
        let objs = {};
        let cond = {};
        let filter = {};
        let doc = { status: false, data: {} };
        if (
            calendar_check.data.level[
                calendar_check.data.level.findIndex((i) => i.level_no == req.body.level_no)
            ].rotation == 'yes'
        ) {
            if (
                req.body.rotation_count != 0 &&
                calendar_check.data.level[
                    calendar_check.data.level.findIndex((i) => i.level_no == req.body.level_no)
                ].rotation_count >= req.body.rotation_count
            ) {
                objs = {
                    $push: {
                        'level.$.rotation_course.$[i].course': [
                            {
                                _course_id: req.body._course_id,
                                start_date: req.body.start_date,
                                end_date: req.body.end_date,
                                _event_id: req.body._event_id,
                                color_code: req.body.color_code,
                            },
                        ],
                    },
                };
                filter = { arrayFilters: [{ 'i.rotation_count': req.body.rotation_count }] };
                cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
                doc = await base_control.update_condition_array_filter(
                    program_calendar,
                    cond,
                    objs,
                    filter,
                );
            } else {
                doc = { status: false, data: 'Rotation Count Missmatch' };
            }
        } else {
            objs = {
                $push: {
                    'level.$.course': {
                        _course_id: req.body._course_id,
                        start_date: req.body.start_date,
                        end_date: req.body.end_date,
                        _event_id: req.body._event_id,
                        color_code: req.body.color_code,
                    },
                },
            };
            cond = { _id: req.body._calendar_id, 'level.level_no': req.body.study_level };
            doc = await base_control.update_condition(program_calendar, cond, objs);
        }
        if (doc.status) {
            common_files.com_response(
                res,
                201,
                true,
                'Program_calendar Course Added successfully in Rotation Area',
                doc.data,
            );
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error id not match',
            'Check Parshing reference ID',
        );
    }
};

exports.update = async (req, res) => {
    let checks = { status: true };
    if (req.body._event_id != undefined) {
        checks = await base_control.check_id(calendar_event, {
            _id: { $in: req.body._event_id },
            isDeleted: false,
        });
    }
    if (checks.status) {
        const objs = {};
        if (req.body.start_date != undefined && req.body.start_date.length != 0) {
            Object.assign(objs, { 'level.$[i].course.$.start_date': req.body.start_date });
        }
        if (req.body.end_date != undefined && req.body.end_date.length != 0) {
            Object.assign(objs, { 'level.$[i].course.$.end_date': req.body.end_date });
        }
        if (req.body._event_id != undefined && req.body._event_id.length != 0) {
            Object.assign(objs, { 'level.$[i].course.$._event_id': req.body._event_id });
        }
        if (req.body.color_code != undefined && req.body.color_code.length != 0) {
            Object.assign(objs, { 'level.$[i].course.$.color_code': req.body.color_code });
        }
        const cond = { _id: req.body._calendar_id, 'level.course._course_id': req.body._course_id };
        const filter = { arrayFilters: [{ 'i.level_no': req.body.level_no }] };
        const doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $set: objs },
            filter,
        );
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                'role_management update successfully',
                doc.data,
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                'Unable to Edit Course retry some other time',
                doc.data,
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error id not match',
            'Check Parshing reference ID',
        );
    }
};

exports.delete = async (req, res) => {
    let doc = {};
    let cond = {};
    let objs = {};
    let filter = {};
    if (req.body.rotation_no == undefined) {
        cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
        objs = { 'level.$.course': { _course_id: req.body._course_id } };
        doc = await base_control.update_condition(program_calendar, cond, { $pull: objs });
    } else {
        cond = { _id: req.body._calendar_id, 'level.level_no': req.body.level_no };
        filter = { arrayFilters: [{ 'i.rotation_count': parseInt(req.body.rotation_no) }] };
        objs = { 'level.$.rotation_course.$[i].course': { _course_id: req.body._course_id } };
        doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $pull: objs },
            filter,
        );
    }

    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            'Program_calendar Course deleted successfully',
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.delete_event = async (req, res) => {
    let doc = {};
    let cond = {};
    let objs = {};
    let filter = {};
    if (req.body.rotation_no == undefined) {
        cond = { _id: req.body._calendar_id, 'level.course._event_id': req.body._event_id };
        objs = { 'level.$[i].course.$[j]._event_id': { $in: req.body._event_id } };
        filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no },
                { 'j._course_id': req.body._course_id },
            ],
        };
        doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $pull: objs },
            filter,
        );
    } else {
        cond = {
            _id: req.body._calendar_id,
            'level.rotation_course.course._event_id': req.body._event_id,
        };
        objs = {
            'level.$[i].rotation_course.$[k].course.$[j]._event_id': { $in: req.body._event_id },
        };
        filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no },
                { 'k.rotation_count': req.body.rotation_no },
                { 'j._course_id': req.body._course_id },
            ],
        };
        doc = await base_control.update_condition_array_filter(
            program_calendar,
            cond,
            { $pull: objs },
            filter,
        );
    }
    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            'Program_calendar Course Event deleted successfully',
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.list_values = async (req, res) => {
    let proj;
    const query = { isDeleted: false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach((element) => {
                proj = proj + ', ' + element + ' : 1';
            });
            proj += '}';
        } else {
            proj = {};
        }
        const doc = await base_control.get_list(role_management, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                'role_management List',
                program_formate.role_management_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, 'Error', doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error parse field in body',
            'Error parse field in body',
        );
    }
};

exports.add_role = async (req, res) => {
    const objs = {
        name: req.body.name,
        description: req.body.description,
        code: req.body.code,
    };
    const doc = await base_control.insert(role, objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, 'Role Added successfully', doc.responses);
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to add Role',
            'Error Unable to add Role',
        );
    }
};

exports.add_permission = async (req, res) => {
    const objs = {
        name: req.body.name,
        description: req.body.description,
        code: req.body.code,
    };
    const doc = await base_control.insert(permission, objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, 'Permission Added successfully', doc.responses);
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to add Permission',
            'Error Unable to add Permission',
        );
    }
};

exports.add_privilege = async (req, res) => {
    const objs = {
        name: req.body.name,
        description: req.body.description,
        code: req.body.code,
    };
    const doc = await base_control.insert(privilege, objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, 'Privilege Added successfully', doc.responses);
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to add Privilege',
            'Error Unable to add Privilege',
        );
    }
};

exports.add_permisssion_set = async (req, res) => {
    const objs = {
        _permission_id: req.body._permission_id,
        _privilege_id: req.body._privilege_id,
        description: req.body.description,
        code: req.body.code,
    };
    const doc = await base_control.insert(permission_set, objs);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'Permission SET Added successfully',
            doc.responses,
        );
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to add Permission SET',
            'Error Unable to add Permission SET',
        );
    }
};

exports.add_role_set = async (req, res) => {
    const objs = {
        _role_id: req.body._role_id,
        _permission_id: req.body._permission_id,
        description: req.body.description,
        code: req.body.code,
    };
    const doc = await base_control.insert(role_set, objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, 'Role SET Added successfully', doc.responses);
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to add Role SET',
            'Error Unable to add Role SET',
        );
    }
};

exports.role_get = async (req, res) => {
    const role_id = ObjectId(req.params.role_id);
    const aggre = [
        { $match: { _role_id: role_id, isDeleted: false } },
        {
            $lookup: {
                from: constant.PERMISSION_SET,
                localField: '_permission_id',
                foreignField: '_permission_id',
                as: 'permission',
            },
        },
        { $unwind: { path: '$permission', preserveNullAndEmptyArrays: true } },
        {
            $lookup: {
                from: constant.PRIVILEGE,
                localField: 'permission._privilege_id',
                foreignField: '_id',
                as: 'permission.privilege',
            },
        },
        // { $unwind: { path: '$permission', preserveNullAndEmptyArrays: true } },
    ];
    const doc = await base_control.get_aggregate(role_set, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            'Role Details details',
            doc.data /* program_formate.role_management_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, 'Error', doc.data);
    }
};

exports.assign_role = async (req, res) => {
    const objs = {
        role: req.body.role,
    };
    const doc = await base_control.update(user, ObjectId(req.body._user_id), objs);
    if (doc.status) {
        common_files.com_response(res, 200, true, 'Role Assigned successfully', doc.responses);
    } else {
        common_files.com_response(
            res,
            500,
            false,
            'Error Unable to Assigned Role',
            'Error Unable to Assigned Role',
        );
    }
};
