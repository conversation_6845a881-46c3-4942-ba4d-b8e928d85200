const mongoose = require('mongoose');
const {
    INSTITUTION,
    GL<PERSON><PERSON>L_SESSION_SETTINGS,
    ABSENT,
    PRESENT,
    EXCLUDE,
    DS_INSTITUTION_KEY,
    DS_PROGRAM_KEY,
    DIGI_PROGRAM,
    WARNING_CONFIG_TYPE,
} = require('../utility/constants');
const ObjectId = mongoose.Schema.Types.ObjectId;

const sessionSchema = new mongoose.Schema({
    sessionName: String,
    sessionStart: Date,
    sessionEnd: Date,
});
const sessionDocumentDetails = new mongoose.Schema({
    type: { type: String, enum: [DS_INSTITUTION_KEY, DS_PROGRAM_KEY] },
    content: { type: String },
    url: [{ type: String }],
});
const settingsSchema = new mongoose.Schema(
    {
        _institution_id: {
            type: ObjectId,
            ref: INSTITUTION,
            required: true,
        },
        basicDetails: {
            isSameTimeForEveryDay: {
                type: Boolean,
                default: true,
            },
            scheduleForNonWorkingDays: {
                type: Boolean,
                default: false,
            },
            sessions: [sessionSchema],
            workingDays: [
                {
                    day: {
                        type: String,
                    },
                    isActive: {
                        type: Boolean,
                        default: true,
                    },
                    sessions: [sessionSchema],
                },
            ],
            staffFacial: { type: Boolean },
            studentFacial: { type: Boolean },
            selfRegistrationDocument: { type: Boolean },
            schedulePermission: {
                create: { type: Boolean, default: false },
                edit: { type: Boolean, default: false },
            },
            userFacialRegister: { type: Boolean, default: true },
        },
        sessionStatusDetails: {
            isEnabled: {
                type: Boolean,
                default: false,
            },
            activeToInactive: {
                type: String,
                enum: [ABSENT, PRESENT, EXCLUDE],
            },
            inactiveToActive: {
                type: String,
                enum: [ABSENT, PRESENT, EXCLUDE],
            },
        },
        scheduleAttendanceConfig: {
            staffCaptcha: { type: Boolean, default: false },
            studentCaptcha: { type: Boolean, default: false },
        },
        institutionDocumentDetails: sessionDocumentDetails,
        programDocumentDetails: [
            {
                ...sessionDocumentDetails.obj,
                programId: { type: ObjectId, ref: DIGI_PROGRAM },
            },
        ],
        disciplinaryRemarks: {
            studentVisibility: {
                type: Boolean,
                default: true,
            },
            commentVisibility: {
                type: Boolean,
                default: true,
            },
        },
        warningMode: {
            type: String,
            enum: [WARNING_CONFIG_TYPE.COURSE_BASED, WARNING_CONFIG_TYPE.COMPREHENSIVE],
            default: WARNING_CONFIG_TYPE.COURSE_BASED,
        },
        deviceControl: {
            staffDeviceControl: { type: Boolean, default: false },
            studentDeviceControl: { type: Boolean, default: false },
            message: { type: String, default: 'Login from different device is not allowed' },
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
        handout: { type: Boolean, default: false },
        sessionView: { type: Boolean, default: false },
    },
    { timeStamps: true },
);

const settingsModel = mongoose.model(GLOBAL_SESSION_SETTINGS, settingsSchema);

module.exports = settingsModel;
