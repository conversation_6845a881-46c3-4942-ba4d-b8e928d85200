const {
    model,
    Schema,
    Types: { ObjectId },
} = require('mongoose');
const { DIGI_SURVEY_EXTERNAL_USERS } = require('../../utility/constants');

const digiSurveyExternalUserSchema = new Schema(
    {
        name: { type: String, trim: true },
        email: {
            type: String,
            required: true,
            unique: true,
            sparse: true,
        },
        isDeleted: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(DIGI_SURVEY_EXTERNAL_USERS, digiSurveyExternalUserSchema);
