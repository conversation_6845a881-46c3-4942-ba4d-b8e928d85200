const { convertToMongoObjectId, query } = require('./common');
const {
    // DIGI_PROGRAM,
    ROLE_ASSIGN,
    DIGI_COURSE,
    // COURSE_SCHEDULE,
    STUDENT_GROUP,
    COURSE_STUDENT,
    COURSE_COORDINATOR,
    INSTITUTION_CALENDAR,
    PR<PERSON><PERSON><PERSON>_CALENDAR,
    PUBLISHED,
    REDIS_FOLDERS: { USER_COURSES },
    SESSION_MODE: { AUTO, MANUAL },
    LATE_CONFIG,
    COMPLETED,
    PRESENT,
    STUDENT_WISE,
    COURSE_WISE,
    SESSION_WISE,
    STUDENT_WARNING_REDIS,
    SUMMARY,
    SLO,
    CLO,
    PLO,
    DC_STAFF,
    DC_STUDENT,
    LMS_STUDENT_SETTING_CALENDAR,
    DELAYED,
    LMS_PENDING,
    LEAVE_APPLICATION_CRITERIA: { SESSION, DAY },
} = require('./constants');
const {
    logger,
    SERVICES: { LATE_ABSENT_CONFIGURATION, COURSE_RESTRICTION },
    USER_PERMISSION_MODULE_NAMES,
} = require('./util_keys');

// const programSchema = require('mongoose').model(DIGI_PROGRAM);
const roleAssignSchema = require('mongoose').model(ROLE_ASSIGN);
const courseSchema = require('mongoose').model(DIGI_COURSE);
const institutionCalendarSchema = require('mongoose').model(INSTITUTION_CALENDAR);
const programCalendarSchema = require('mongoose').model(PROGRAM_CALENDAR);
const lmsSettingSchema = require('../lmsStudentSetting/lmsStudentSetting.model');
const studentWarningRecordSchema = require('../models/student_warning_records');
const {
    getWarningSettingBasedCalendar,
} = require('../lmsStudentSetting/lmsStudentSetting.controller');
const { redisClient } = require('../../config/redis-connection');
const lmsLateConfigSettingSchema = require('../lmsAttendanceConfig/lmsAttendanceConfig.model');
const lateConfigManagementSchema = require('../lmsAttendanceConfig/lateConfigManagement.model');
const cloudFunctionAxios = require('../../cloud-function/cloud-function.axios');
const { luaScript } = require('../../service/redisCache.service');

const getActiveInstitutionCalendars = async ({ _institution_id }) => {
    try {
        const institutionCalendars = await institutionCalendarSchema
            .find(
                {
                    ...query,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    status: PUBLISHED,
                },
                { calendar_name: 1 },
            )
            .lean()
            .sort({ _id: -1 });
        return {
            institutionCalendars,
            activeCalendarIds: institutionCalendars.map((calendarElement) => calendarElement._id),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserRoleProgramList = async ({
    _institution_id,
    user_id,
    role_id,
    institutionCalendarId,
}) => {
    try {
        let roleAssignData;
        let courseCoordinatorData;
        // let courseScheduleData;
        let isCourseAdmin = false;
        let isProgramAdmin = false;
        let userProgramIds = [];
        const userCourseIds = [];
        roleAssignData = await roleAssignSchema
            .findOne(
                {
                    ...query,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _user_id: convertToMongoObjectId(user_id),
                },
                {
                    'roles.role_name': 1,
                    'roles._id': 1,
                    'roles._role_id': 1,
                    'roles.isAdmin': 1,
                    'roles.program._program_id': 1,
                },
            )
            .lean();
        if (roleAssignData && roleAssignData.roles) {
            const userRoleData = roleAssignData.roles.find(
                (roleElement) =>
                    roleElement._role_id.toString() === role_id ||
                    roleElement._id.toString() === role_id,
            );
            userProgramIds =
                userRoleData && userRoleData.isAdmin
                    ? userRoleData.program.map((programElement) => programElement._program_id)
                    : [];
            isProgramAdmin = userRoleData && userRoleData.isAdmin;
            isCourseAdmin = userRoleData && userRoleData.role_name === COURSE_COORDINATOR;
        }
        if (isCourseAdmin) {
            courseCoordinatorData = await courseSchema
                .find(
                    {
                        ...query,
                        _institution_id: convertToMongoObjectId(_institution_id),
                        'coordinators._institution_calendar_id': { $in: institutionCalendarId },
                        'coordinators._user_id': convertToMongoObjectId(user_id),
                    },
                    {
                        _program_id: 1,
                        'coordinators._institution_calendar_id': 1,
                        'coordinators._user_id': 1,
                        'coordinators.term': 1,
                        'coordinators.year': 1,
                        'coordinators.level_no': 1,
                        course_name: 1,
                        versionNo: 1,
                        versioned: 1,
                        versionName: 1,
                        versionedFrom: 1,
                        versionedCourseIds: 1,
                    },
                )
                .lean();
            for (courseElement of courseCoordinatorData) {
                const courseCoordinators = courseElement.coordinators.filter(
                    (coordinatorElement) =>
                        institutionCalendarId.find(
                            (institutionCalendarIdElement) =>
                                institutionCalendarIdElement.toString() ===
                                coordinatorElement._institution_calendar_id.toString(),
                        ) && coordinatorElement._user_id.toString() === user_id,
                );
                for (const courseCoordinatorElement of courseCoordinators) {
                    userCourseIds.push({
                        _institution_calendar_id: convertToMongoObjectId(
                            courseCoordinatorElement._institution_calendar_id,
                        ),
                        _program_id: courseElement._program_id,
                        _course_id: courseElement._id,
                        term: courseCoordinatorElement.term,
                        year_no: courseCoordinatorElement.year,
                        level_no: courseCoordinatorElement.level_no,
                        course_name: courseElement.course_name,
                        versionNo: courseElement.versionNo || 1,
                        versioned: courseElement.versioned || false,
                        versionName: courseElement.versionName || '',
                        versionedFrom: courseElement.versionedFrom || null,
                    });
                    userProgramIds.push(courseElement._program_id.toString());
                }
            }
            roleAssignData = undefined;
        }
        userProgramIds = [...new Set(userProgramIds)];
        // if (!isCourseAdmin && !isProgramAdmin)
        //     courseScheduleData = await courseScheduleSchema
        //         .distinct('_program_id', {
        //             ...query,
        //             _institution_id: convertToMongoObjectId(_institution_id),
        //             _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //             type: 'regular',
        //             'staffs._staff_id': convertToMongoObjectId(user_id),
        //         })
        //         .lean();
        return { userProgramIds, userCourseIds, isCourseAdmin, isProgramAdmin };
        // roleAssignData,
        // role_id,
        // courseCoordinatorData,
        // courseScheduleData,
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
// lms new settings
const lmsNewSetting = async ({
    _institution_id,
    _institution_calendar_id,
    leaveApplicationCriteria = DAY,
}) => {
    try {
        let warningAbsenceData;
        const lmsSettingData = await lmsSettingSchema
            .findOne(
                {
                    isDeleted: false,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType: 'leave',
                    leaveApplicationCriteria,
                },
                {
                    leaveCalculation: 1,
                    'warningConfig._id': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.message': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.restrictCourseAccess': 1,
                    'warningConfig.notificationToStudent': 1,
                    'warningConfig.notificationToStaff': 1,
                },
            )
            .lean();
        if (_institution_calendar_id) {
            const warningConfigForCalendar = await getWarningSettingBasedCalendar({
                _institution_calendar_id,
            });
            if (lmsSettingData && warningConfigForCalendar && warningConfigForCalendar.length) {
                lmsSettingData.warningConfig = warningConfigForCalendar;
            }
        }
        if (
            lmsSettingData &&
            lmsSettingData.warningConfig.length &&
            lmsSettingData.warningConfig.filter((warningElement) => warningElement.isActive)
                .length !== 0
        ) {
            const denialLabel =
                lmsSettingData.warningConfig[lmsSettingData.warningConfig.length - 1].labelName;
            warningAbsenceData = lmsSettingData.warningConfig.filter(
                (warningElement) => warningElement.isActive,
            );
            warningAbsenceData = warningAbsenceData.sort((firstElement, secondElement) => {
                let comparison = 0;
                if (parseInt(firstElement.percentage) > parseInt(secondElement.percentage)) {
                    comparison = -1;
                } else if (parseInt(firstElement.percentage) < parseInt(secondElement.percentage)) {
                    comparison = 1;
                }
                return comparison;
            });
            const denialWarning =
                warningAbsenceData[0].labelName.toLocaleLowerCase() ===
                denialLabel.toLocaleLowerCase()
                    ? denialLabel
                    : '';
            const finaleWarning =
                warningAbsenceData[0].labelName.toLocaleLowerCase() ===
                    denialLabel.toLocaleLowerCase() && warningAbsenceData.length > 1
                    ? warningAbsenceData[1].labelName
                    : warningAbsenceData[0].labelName.toLocaleLowerCase() !==
                      denialLabel.toLocaleLowerCase()
                    ? warningAbsenceData[0].labelName
                    : warningAbsenceData[0].labelName.toLocaleLowerCase() ===
                          denialLabel.toLocaleLowerCase() && warningAbsenceData.length === 1
                    ? ''
                    : '';
            if (COURSE_RESTRICTION !== 'true') {
                warningAbsenceData[0].restrictCourseAccess = false;
            }
            return {
                warningAbsenceData,
                finaleWarning,
                denialWarning,
                denialLabel,
                leaveCalculation:
                    lmsSettingData && lmsSettingData.leaveCalculation
                        ? lmsSettingData.leaveCalculation
                        : 'sessions',
            };
        }
        return {
            warningAbsenceData: [
                { labelName: 'Denial', percentage: 101, message: 'Denial Warning', colorCode: '' },
            ],
            finaleWarning: '',
            denialWarning: 'Denial',
            denialLabel: 'Denial',
            leaveCalculation:
                lmsSettingData && lmsSettingData.leaveCalculation
                    ? lmsSettingData.leaveCalculation
                    : 'sessions',
        };
    } catch (error) {
        logger.error(error, 'Internal Server Issue');
        throw new Error(error);
    }
};

const lmsNewSettingWithOptions = async ({
    _institution_id,
    _institution_calendar_id,
    project,
    sort,
}) => {
    const key = `${LMS_STUDENT_SETTING_CALENDAR}-${_institution_id?.toString()}-${_institution_calendar_id?.toString()}`;
    const data = await redisClient?.Client?.get(key);
    if (data && JSON.parse(data)) {
        return JSON.parse(data);
    }
    const lmsSettingData = await lmsSettingSchema.findOne({
        isDeleted: false,
        _institution_id: convertToMongoObjectId(_institution_id),
        classificationType: 'leave',
    });
    if (COURSE_RESTRICTION !== 'true') {
        lmsSettingData.warningConfig[0].restrictCourseAccess = false;
    }
    lmsSettingData.warningConfig = lmsSettingData.warningConfig.filter(
        (warningElement) => warningElement.isActive,
    );
    if (lmsSettingData.warningConfig.length) {
        if (sort) {
            lmsSettingData.warningConfig = lmsSettingData.warningConfig.sort(
                (firstElement, secondElement) => {
                    let comparison = 0;
                    if (parseInt(firstElement.percentage) > parseInt(secondElement.percentage)) {
                        comparison = -1;
                    } else if (
                        parseInt(firstElement.percentage) < parseInt(secondElement.percentage)
                    ) {
                        comparison = 1;
                    }
                    return comparison;
                },
            );
        }
        await redisClient?.Client?.set(key, JSON.stringify(lmsSettingData));
        return lmsSettingData;
    }
    return { warningConfig: [] };
};

const getWarningDataFromRedis = async (keyMap = new Map()) => {
    const keys = Array.from(keyMap)
        .flat(2)
        .filter((keyElement) => keyElement);
    if (keys.length) {
        const redisData = await redisClient.Client.mget(keys);
        if (keys.length === redisData.length) {
            const msetObject = {};
            for (const [i, warningRedisKey] of keys.entries()) {
                if (redisData[i]) {
                    keyMap.set(warningRedisKey, JSON.parse(redisData[i]));
                } else {
                    const [redisKey, remaining] = warningRedisKey.split(':');
                    const [
                        institutionCalendarId,
                        programId,
                        courseId,
                        yearNo,
                        levelNo,
                        term,
                        rotationCount,
                    ] = remaining.split('_');
                    const dataFromDB = await studentWarningRecordSchema
                        .findOne(
                            {
                                institutionCalendarId:
                                    convertToMongoObjectId(institutionCalendarId),
                                programId: convertToMongoObjectId(programId),
                                courseId: convertToMongoObjectId(courseId),
                                yearNo,
                                levelNo,
                                term,
                                ...(rotationCount !== 'null' && { rotationCount }),
                            },
                            { warnings: 1 },
                        )
                        .lean();
                    if (dataFromDB && dataFromDB.warnings) {
                        msetObject[warningRedisKey] = JSON.stringify(dataFromDB.warnings);
                        keyMap.set(warningRedisKey, dataFromDB.warnings);
                    }
                }
            }
            if (Object.keys(msetObject).length) await redisClient.Client.mset(msetObject);
            return keyMap;
        }
    }
    return new Map();
};

const checkRestrictCourse = async ({
    _institution_id,
    _institution_calendar_id,
    programId,
    courseId,
    yearNo,
    levelNo,
    term,
    rotationCount,
    userId,
    warningAbsenceData = [],
}) => {
    try {
        if (!warningAbsenceData.length) {
            const { warningConfig } = await lmsNewSettingWithOptions({
                _institution_id,
                _institution_calendar_id,
                project: {
                    'warningConfig._id': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.restrictCourseAccess': 1,
                    'warningConfig.notificationToStudent.setType': 1,
                },
                sort: true,
            });
            warningAbsenceData = warningConfig;
        }
        const key = `${STUDENT_WARNING_REDIS}:${_institution_calendar_id.toString()}_${programId.toString()}_${courseId.toString()}_${yearNo}_${levelNo}_${term}_${
            rotationCount || null
        }`;
        let matchedWarning = '';
        const warnings = await getWarningDataFromRedis(new Map().set(key, 0));
        if (warnings.size) {
            if (warnings.has(key)) {
                const warningObj = warnings.get(key) ?? {};
                for (const warningId in warningObj) {
                    if (warningObj[warningId].includes(userId.toString())) {
                        matchedWarning = warningId;
                        break;
                    }
                }
            }
        }
        return (
            warningAbsenceData.find(
                (warningElement) => warningElement._id.toString() === matchedWarning,
            ) ?? {}
        );
    } catch (error) {
        logger.error('Error in checkRestrictCourse' + error.toString());
        return { labelName: '', restrictCourseAccess: false };
    }
};

const filterStudentsForCourseRestriction = async ({
    _institution_id,
    _institution_calendar_id,
    programId,
    courseId,
    yearNo,
    levelNo,
    term,
    rotationCount,
    userIds,
}) => {
    if (COURSE_RESTRICTION === 'true') {
        const { warningConfig } = await lmsNewSettingWithOptions({
            _institution_calendar_id,
            _institution_id,
            project: {
                'warningConfig._id': 1,
                'warningConfig.labelName': 1,
                'warningConfig.restrictCourseAccess': 1,
                'warningConfig.notificationToStudent.setType': 1,
            },
            sort: true,
        });
        if (warningConfig && warningConfig.length && warningConfig[0].restrictCourseAccess) {
            const denialWarningId = warningConfig[0]?._id;
            const key = `${STUDENT_WARNING_REDIS}:${_institution_calendar_id.toString()}_${programId.toString()}_${courseId.toString()}_${yearNo}_${levelNo}_${term}_${
                rotationCount || null
            }`;
            const warningsMap = await getWarningDataFromRedis(new Map().set(key, 0));
            const warnings = warningsMap.get(key) ?? {};
            const denialWarningStudents = warnings[denialWarningId.toString()] ?? [];
            return userIds.filter(
                (stIdElement) => !denialWarningStudents.includes(stIdElement.toString()),
            );
        }
    }
    return userIds;
};

const getWarningsAndRestriction = (warningConfig = []) => {
    const restrictCourseAccess = warningConfig[0]?.restrictCourseAccess;
    const warningLabels = warningConfig.reduce((acc, curr) => {
        if (curr._id && !acc[curr._id.toString()]) {
            acc[curr._id] = {
                labelName: curr.labelName,
                restrictCourseAccess: curr.restrictCourseAccess,
            };
        }
        return acc;
    }, {});
    return { warningLabels, restrictCourseAccess };
};

const updateWarningsInRecord = async (updatedWarnings = []) => {
    if (updatedWarnings.length) {
        const result = updatedWarnings.reduce(
            (acc, curr) => {
                if (!acc.institutionCalendarId.includes(curr.institutionCalendarId)) {
                    acc.institutionCalendarId.push(curr.institutionCalendarId);
                }
                if (!acc.programId.includes(curr.programId)) {
                    acc.programId.push(curr.programId);
                }
                if (!acc.courseId.includes(curr.courseId)) {
                    acc.courseId.push(curr.courseId);
                }
                if (!acc.yearNo.includes(curr.yearNo)) {
                    acc.yearNo.push(curr.yearNo);
                }
                if (!acc.levelNo.includes(curr.levelNo)) {
                    acc.levelNo.push(curr.levelNo);
                }
                if (!acc.term.includes(curr.term)) {
                    acc.term.push(curr.term);
                }
                return acc;
            },
            {
                institutionCalendarId: [],
                programId: [],
                courseId: [],
                yearNo: [],
                levelNo: [],
                term: [],
            },
        );
        const { institutionCalendarId, programId, courseId, yearNo, levelNo, term } = result;
        const studentWarningRecords = await studentWarningRecordSchema
            .find({
                institutionCalendarId: { $in: institutionCalendarId },
                programId: { $in: programId.map((pmElement) => convertToMongoObjectId(pmElement)) },
                courseId: { $in: courseId.map((ceElement) => convertToMongoObjectId(ceElement)) },
                yearNo: { $in: yearNo },
                levelNo: { $in: levelNo },
                term: { $in: term },
            })
            .lean();
        const bulkUpdate = [];
        for (const warningElement of updatedWarnings) {
            const {
                institutionCalendarId,
                programId,
                courseId,
                yearNo,
                levelNo,
                term,
                rotationCount,
                userId,
                warningId,
            } = warningElement;

            const currentRecord = studentWarningRecords.find((studentRecordElement) =>
                studentRecordElement.institutionCalendarId === institutionCalendarId &&
                studentRecordElement.programId.toString() === programId.toString() &&
                studentRecordElement.courseId.toString() === courseId.toString() &&
                studentRecordElement.yearNo === yearNo &&
                studentRecordElement.levelNo === levelNo &&
                studentRecordElement.term === term &&
                rotationCount
                    ? rotationCount === studentRecordElement.rotationCount
                    : true,
            );
            if (currentRecord && currentRecord.warnings) {
                const warningObj = currentRecord.warnings;
                for (warningKey in warningObj) {
                    if (warningObj[warningKey]?.includes(userId.toString())) {
                        warningObj[warningKey] = warningObj[warningKey]?.filter(
                            (stdIdElement) => stdIdElement !== userId.toString(),
                        ); //to remove from existing warning
                    }
                }
                //to update the new warning
                // before updating check for already updated the same
                const isAlreadyDocInserted = bulkUpdate.find((obj) => {
                    const {
                        institutionCalendarId: _institutionCalendarId,
                        programId: _programId,
                        courseId: _courseId,
                        yearNo: _yearNo,
                        levelNo: _levelNo,
                        term: _term,
                        rotationCount: _rotationCount = 0,
                    } = obj.updateOne.filter;

                    return (
                        _institutionCalendarId.toString() === institutionCalendarId &&
                        _programId.toString() === programId &&
                        _courseId.toString() === courseId &&
                        _yearNo === yearNo &&
                        _levelNo === levelNo &&
                        _term === term &&
                        (rotationCount ? rotationCount === _rotationCount : true)
                    );
                });
                if (isAlreadyDocInserted) {
                    const alreadyUpdatedWarnings =
                        isAlreadyDocInserted.updateOne.update.$set.warnings ?? {};
                    const isAlreadyPushedForSameWarning =
                        alreadyUpdatedWarnings[warningId.toString()] ?? [];
                    alreadyUpdatedWarnings[warningId.toString()] = [
                        ...isAlreadyPushedForSameWarning,
                        userId.toString(),
                    ];
                } else {
                    const existingWarningIds = warningObj[warningId.toString()] ?? [];
                    if (existingWarningIds.length) {
                        warningObj[warningId.toString()] = [
                            ...existingWarningIds,
                            userId.toString(),
                        ];
                    } else {
                        warningObj[warningId.toString()] = [userId.toString()];
                    }
                    bulkUpdate.push({
                        updateOne: {
                            filter: {
                                institutionCalendarId,
                                programId: convertToMongoObjectId(programId),
                                courseId: convertToMongoObjectId(courseId),
                                yearNo,
                                levelNo,
                                term,
                                ...(rotationCount && { rotationCount }),
                            },
                            update: {
                                $set: {
                                    warnings: warningObj,
                                },
                            },
                        },
                    });
                }
            } else {
                // user don't have  any warnings earlier, so insert the new one

                // before inserting new one, check if already inserted for the same course
                const isAlreadyDocInserted = bulkUpdate.find((obj) => {
                    const {
                        institutionCalendarId: _institutionCalendarId,
                        programId: _programId,
                        courseId: _courseId,
                        yearNo: _yearNo,
                        levelNo: _levelNo,
                        term: _term,
                        rotationCount: _rotationCount = 0,
                    } = Object.values(obj)[0];

                    return (
                        _institutionCalendarId.toString() === institutionCalendarId &&
                        _programId.toString() === programId &&
                        _courseId.toString() === courseId &&
                        _yearNo === yearNo &&
                        _levelNo === levelNo &&
                        _term === term &&
                        (rotationCount ? rotationCount === _rotationCount : true)
                    );
                });
                if (isAlreadyDocInserted) {
                    const alreadyInsertedWarnings =
                        isAlreadyDocInserted.insertOne.document.warnings ?? {};
                    const isAlreadyPushedForSameWarning =
                        alreadyInsertedWarnings[warningId.toString()] ?? [];
                    alreadyInsertedWarnings[warningId.toString()] = [
                        ...isAlreadyPushedForSameWarning,
                        userId.toString(),
                    ];
                } else {
                    bulkUpdate.push({
                        insertOne: {
                            document: {
                                institutionCalendarId:
                                    convertToMongoObjectId(institutionCalendarId),
                                programId: convertToMongoObjectId(programId),
                                courseId: convertToMongoObjectId(courseId),
                                yearNo,
                                levelNo,
                                term,
                                ...(rotationCount && { rotationCount }),
                                warnings: { [warningId]: [userId.toString()] },
                            },
                        },
                    });
                }
            }
            await redisClient.Client.del(
                `${STUDENT_WARNING_REDIS}:${institutionCalendarId.toString()}_${programId.toString()}_${courseId.toString()}_${yearNo}_${levelNo}_${term}_${
                    rotationCount || null
                }`,
            );
        }
        if (bulkUpdate.length) await studentWarningRecordSchema.bulkWrite(bulkUpdate);
    }
};

const multiCourseWarningsForStudent = async ({
    keysMap = new Map(),
    warningConfig = [],
    userId,
}) => {
    const allCourseWarnings = await getWarningDataFromRedis(keysMap);
    const { warningLabels } = getWarningsAndRestriction(warningConfig);
    for (const [key, warnings] of allCourseWarnings) {
        for (const warningId in warnings) {
            if (warnings[warningId].includes(userId.toString())) {
                allCourseWarnings.set(key, warningLabels[warningId]);
                break;
            }
        }
    }
    return allCourseWarnings;
};

const updateStudentGroupRedisKey = async ({ courseId, level, batch, rotationCount }) => {
    try {
        const deleteStudentGroupKeys = [
            `${STUDENT_GROUP}:*-*-${level || '*'}-${batch || '*'}-*`,
            `${STUDENT_GROUP}:*-${level || '*'}-${batch || '*'}-${courseId || '*'}-*`,
            `${STUDENT_GROUP}:*-${level || '*'}-${batch || '*'}-${courseId || '*'}-*-${
                rotationCount || '*'
            }`,
            `${COURSE_STUDENT}:*-${level || '*'}-${batch || '*'}-${courseId || '*'}-*`,
            `${COURSE_STUDENT}:*-${level || '*'}-${batch || '*'}-${courseId || '*'}-*-*`,
        ];
        const result = await redisClient.Client.eval(
            luaScript,
            0,
            JSON.stringify(deleteStudentGroupKeys),
        );
        console.log(`Deleted ${result} keys.`);
        return result;
    } catch (err) {
        return null;
    }
};

const updateProgramCalendarRedisData = async ({
    _id,
    term,
    level_no,
    _institution_calendar_id,
}) => {
    if (!_institution_calendar_id) {
        const programCalendarDoc = await programCalendarSchema.findOne(
            {
                _id,
                'level.term': term,
                'level.level_no': level_no,
            },
            { _institution_calendar_id: 1, 'level.$': 1 },
        );
        if (programCalendarDoc) {
            const key = `${PROGRAM_CALENDAR}:${programCalendarDoc.level[0]._program_id.toString()}-${programCalendarDoc._institution_calendar_id.toString()}`;
            await redisClient.Client.del(key);
        }
    } else {
        const calendarKeyPattern = `${PROGRAM_CALENDAR}:*-${_institution_calendar_id.toString()}`;
        const keys = await redisClient.Client.keys(calendarKeyPattern);
        for (const key of keys) {
            await redisClient.Client.del(key);
        }
    }
};

const updateUserCourseRedisKey = async ({ studentIds }) => {
    for (const studentId of studentIds) {
        const userCourseRedisKey = `${USER_COURSES}:${studentId}-*`;
        const keys = await redisClient.Client.keys(userCourseRedisKey);
        for (const key of keys) {
            await redisClient.Client.del(key);
        }
    }
};

const getAutoRange = ({
    lateDurationRange,
    lateConfig,
    studentLateAbsent,
    primaryTime,
    scheduleStartDateAndTime,
    sortedStudentSessions,
    scheduleIndex,
}) => {
    try {
        const timeDifferenceInMilliseconds =
            new Date(primaryTime) - new Date(scheduleStartDateAndTime);
        const diff = timeDifferenceInMilliseconds / (1000 * 60);
        lateDurationRange.forEach((rangeItem) => {
            if (
                (!rangeItem.endRange && rangeItem.startRange < diff) ||
                (rangeItem.endRange && rangeItem.startRange < diff && rangeItem.endRange >= diff)
            ) {
                const lateLabel = rangeItem.lateLabel;
                if (scheduleIndex) {
                    sortedStudentSessions[scheduleIndex].lateLabel = lateLabel;
                }
                if (!lateConfig.hasOwnProperty(lateLabel)) {
                    lateConfig[lateLabel] = {
                        noOfLate: rangeItem.noOfLate,
                        noOfAbsent: rangeItem.noOfAbsent,
                        studentLate: 1,
                    };
                } else {
                    lateConfig[lateLabel].studentLate += 1;
                }
            }
        });
        return { lateConfig, studentLateAbsent };
    } catch (error) {
        const lateConfig = [];
        const studentLateAbsent = 0;
        console.log(error);
        return { lateConfig, studentLateAbsent };
    }
};

const getManualRange = ({
    manualLateData,
    lateConfig,
    studentLateAbsent,
    tardisId,
    sortedStudentSessions,
    scheduleIndex,
}) => {
    try {
        const rangeItem = manualLateData[tardisId.toString()];
        if (rangeItem) {
            const lateLabel = rangeItem.lateLabel;
            if (scheduleIndex) {
                sortedStudentSessions[scheduleIndex].lateLabel = lateLabel;
            }
            if (!lateConfig.hasOwnProperty(lateLabel)) {
                lateConfig[lateLabel] = {
                    noOfLate: rangeItem.noOfLate,
                    noOfAbsent: rangeItem.noOfAbsent,
                    studentLate: 1,
                };
            } else {
                lateConfig[lateLabel].studentLate += 1;
            }
        }
        return { lateConfig, studentLateAbsent };
    } catch (error) {
        const lateConfig = [];
        const studentLateAbsent = 0;
        return { lateConfig, studentLateAbsent };
    }
};

const getLateAutoAndManualRange = async ({ _institution_id }) => {
    let lateDurationRange = [];
    let manualLateRange = [];
    const manualLateData = {};
    try {
        if (LATE_ABSENT_CONFIGURATION === 'true') {
            const lateConfigurations = await lmsLateConfigSettingSchema
                .find({ _institution_id, mode: LATE_CONFIG }, { lateConfig: 1 })
                .sort({ createdAt: -1 })
                .lean();
            if (
                lateConfigurations &&
                lateConfigurations.length &&
                lateConfigurations[0].lateConfig &&
                lateConfigurations[0].lateConfig.lateType === AUTO
            ) {
                lateDurationRange = lateConfigurations.reduce((result, { _id, lateConfig }) => {
                    if (lateConfig.lateType === AUTO) {
                        result.push({ _id, ...lateConfig.range });
                    }
                    return result;
                }, []);
            } else if (
                lateConfigurations &&
                lateConfigurations.length &&
                lateConfigurations[0].lateConfig &&
                lateConfigurations[0].lateConfig.lateType === MANUAL
            ) {
                manualLateRange = lateConfigurations.reduce((result, { _id, lateConfig }) => {
                    if (lateConfig.lateType === MANUAL) {
                        result.push({ _id, ...lateConfig.range });
                    }
                    return result;
                }, []);
                for (const manualLateRangeElement of manualLateRange) {
                    if (!manualLateData.hasOwnProperty(manualLateRangeElement._id.toString())) {
                        manualLateData[manualLateRangeElement._id.toString()] =
                            manualLateRangeElement;
                    }
                }
            }
        }
        return { lateDurationRange, manualLateRange, manualLateData };
    } catch (error) {
        console.log(error);
        return { lateDurationRange, manualLateRange, manualLateData };
    }
};

const checkExclude = async ({
    _institution_id,
    _institution_calendar_id,
    programId,
    yearNo,
    levelNo,
    term,
    courseId,
    rotation,
    rotationCount,
}) => {
    let lateSchema = [];
    const lateExcludeManagement = {};
    try {
        if (LATE_ABSENT_CONFIGURATION === 'true') {
            lateSchema = await lateConfigManagementSchema
                .find({
                    _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    ...(programId && { programId: convertToMongoObjectId(programId) }),
                    ...(levelNo && { levelNo }),
                    ...(term && { term }),
                    courseId: Array.isArray(courseId)
                        ? { $in: courseId.map(convertToMongoObjectId) }
                        : convertToMongoObjectId(courseId),
                    ...(rotationCount && { rotationCount }),
                    isActive: true,
                    isDeleted: false,
                    excludeStatus: true,
                })
                .lean();
            for (const lateSchemaElement of lateSchema) {
                const {
                    _institution_calendar_id,
                    programId,
                    yearNo,
                    levelNo,
                    term,
                    courseId,
                    rotationCount,
                    excludeStatus,
                    studentId,
                    sessionId,
                } = lateSchemaElement;
                let courseKey;
                let studentKey;
                let sessionKey;
                if (lateSchemaElement.typeWiseUpdate === COURSE_WISE) {
                    courseKey = `${_institution_calendar_id.toString()}-${programId.toString()}-${levelNo}-${term}-${courseId.toString()}`;
                    if (rotationCount) {
                        courseKey += `-${rotationCount}`;
                    }
                    lateExcludeManagement[courseKey] = excludeStatus;
                } else if (lateSchemaElement.typeWiseUpdate === STUDENT_WISE) {
                    studentKey = `${_institution_calendar_id.toString()}-${programId.toString()}-${levelNo}-${term}-${courseId.toString()}-${studentId.toString()}`;
                    if (rotationCount) {
                        studentKey += `-${rotationCount}`;
                    }
                    lateExcludeManagement[studentKey] = excludeStatus;
                } else if (lateSchemaElement.typeWiseUpdate === SESSION_WISE) {
                    sessionKey = `${_institution_calendar_id.toString()}-${programId.toString()}-${levelNo}-${term}-${courseId.toString()}-${studentId.toString()}-${sessionId.toString()}`;
                    if (rotationCount) {
                        sessionKey += `-${rotationCount}`;
                    }
                    lateExcludeManagement[sessionKey] = excludeStatus;
                }
            }
        }
        return {
            isLateExclude: lateSchema.length > 0,
            lateExcludeManagement,
        };
    } catch (error) {
        console.log(error);
        return {
            isLateExclude: lateSchema.length > 0,
            lateExcludeManagement,
        };
    }
};

const checkLateExcludeConfigurationForCourseOrStudent = ({
    _institution_calendar_id,
    programId,
    levelNo,
    term,
    courseId,
    studentId,
    sessionId,
    rotationCount,
    lateExcludeManagement,
}) => {
    let lateExclude = false;
    try {
        if (
            LATE_ABSENT_CONFIGURATION === 'true' &&
            _institution_calendar_id &&
            programId &&
            levelNo &&
            term &&
            courseId
        ) {
            let key;
            if (sessionId && studentId) {
                key = `${_institution_calendar_id.toString()}-${programId.toString()}-${levelNo}-${term}-${courseId.toString()}-${studentId.toString()}-${sessionId.toString()}`;
            } else if (studentId) {
                key = `${_institution_calendar_id.toString()}-${programId.toString()}-${levelNo}-${term}-${courseId.toString()}-${studentId.toString()}`;
            } else {
                key = `${_institution_calendar_id.toString()}-${programId.toString()}-${levelNo}-${term}-${courseId.toString()}`;
            }
            if (rotationCount) {
                key += `-${rotationCount}`;
            }
            if (lateExcludeManagement[key]) {
                lateExclude = true;
            }
        }
    } catch (error) {
        console.log(error);
    }
    return {
        lateExclude,
    };
};

const getLateConfigAndStudentLateAbsent = ({
    lateDurationRange = [],
    manualLateRange = [],
    manualLateData,
    scheduleData,
    studentElement,
    lateExcludeManagement,
}) => {
    try {
        let lateConfig = {};
        let studentLateAbsent = 0;
        if (lateDurationRange.length) {
            scheduleData.forEach((scheduleElement) => {
                const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: scheduleElement._institution_calendar_id,
                    programId: scheduleElement._program_id,
                    levelNo: scheduleElement.level_no,
                    term: scheduleElement.term,
                    courseId: scheduleElement._course_id,
                    studentId: studentElement._student_id,
                    rotationCount: scheduleElement.rotation_count,
                    sessionId: scheduleElement.session._session_id,
                    lateExcludeManagement,
                }).lateExclude;
                if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students &&
                    !lateExludeForSessions
                ) {
                    scheduleElement.students.forEach((filteredStudent) => {
                        if (
                            filteredStudent &&
                            filteredStudent._id &&
                            studentElement._student_id &&
                            filteredStudent._id.toString() ===
                                studentElement._student_id.toString() &&
                            filteredStudent.status === PRESENT &&
                            filteredStudent.primaryTime !== undefined &&
                            !filteredStudent.lateExclude
                        ) {
                            const {
                                lateConfig: updatedLateConfig,
                                studentLateAbsent: updatedStudentLateAbsent,
                            } = getAutoRange({
                                lateDurationRange,
                                lateConfig,
                                studentLateAbsent,
                                primaryTime: filteredStudent.primaryTime,
                                scheduleStartDateAndTime:
                                    scheduleElement.sessionDetail &&
                                    scheduleElement.sessionDetail.start_time
                                        ? scheduleElement.sessionDetail.start_time
                                        : scheduleElement.scheduleStartDateAndTime,
                            });
                            lateConfig = updatedLateConfig;
                            studentLateAbsent = updatedStudentLateAbsent;
                        }
                    });
                }
            });
        } else if (manualLateRange.length) {
            scheduleData.forEach((scheduleElement) => {
                const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id: scheduleElement._institution_calendar_id,
                    programId: scheduleElement._program_id,
                    levelNo: scheduleElement.level_no,
                    term: scheduleElement.term,
                    courseId: scheduleElement._course_id,
                    studentId: studentElement._student_id,
                    rotationCount: scheduleElement.rotation_count,
                    sessionId: scheduleElement.session._session_id,
                    lateExcludeManagement,
                }).lateExclude;
                if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students &&
                    !lateExludeForSessions
                ) {
                    scheduleElement.students.forEach((filteredStudent) => {
                        if (
                            filteredStudent &&
                            filteredStudent._id &&
                            studentElement._student_id &&
                            filteredStudent._id.toString() ===
                                studentElement._student_id.toString() &&
                            filteredStudent.status === PRESENT &&
                            filteredStudent.tardisId &&
                            !filteredStudent.lateExclude
                        ) {
                            const {
                                lateConfig: updatedLateConfig,
                                studentLateAbsent: updatedStudentLateAbsent,
                            } = getManualRange({
                                manualLateData,
                                lateConfig,
                                studentLateAbsent,
                                tardisId: filteredStudent.tardisId,
                            });
                            lateConfig = updatedLateConfig;
                            studentLateAbsent = updatedStudentLateAbsent;
                        }
                    });
                }
            });
        }
        for (const lateLabel in lateConfig) {
            if (lateConfig.hasOwnProperty(lateLabel)) {
                if (lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate) {
                    const lateCount = Math.floor(
                        lateConfig[lateLabel].studentLate / lateConfig[lateLabel].noOfLate,
                    );
                    studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                    if (!lateConfig[lateLabel].studentAbsent) {
                        lateConfig[lateLabel].studentAbsent = 0;
                    }
                    lateConfig[lateLabel].studentAbsent +=
                        lateCount * lateConfig[lateLabel].noOfAbsent;
                }
            }
        }
        lateConfig = Object.entries(lateConfig).map(([labelName, config]) => ({
            labelName,
            ...config,
        }));
        return { lateConfig, studentLateAbsent };
    } catch (error) {
        console.log(error);
        const lateConfig = [];
        const studentLateAbsent = 0;
        return { lateConfig, studentLateAbsent };
    }
};

const getLateConfigAndStudentLateAbsentForStaffExport = ({
    lateDurationRange = [],
    manualLateRange = [],
    manualLateData,
    scheduleData,
    studentElement,
}) => {
    try {
        let lateConfig = {};
        let studentLateAbsent = 0;
        if (lateDurationRange.length) {
            scheduleData.forEach((scheduleElement) => {
                if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students
                ) {
                    scheduleElement.students.forEach((filteredStudent) => {
                        if (
                            filteredStudent &&
                            filteredStudent._id._id &&
                            studentElement._student_id &&
                            filteredStudent._id._id.toString() ===
                                studentElement._student_id.toString() &&
                            filteredStudent.status === PRESENT &&
                            filteredStudent.primaryTime !== undefined &&
                            !filteredStudent.lateExclude
                        ) {
                            const {
                                lateConfig: updatedLateConfig,
                                studentLateAbsent: updatedStudentLateAbsent,
                            } = getAutoRange({
                                lateDurationRange,
                                lateConfig,
                                studentLateAbsent,
                                primaryTime: filteredStudent.primaryTime,
                                scheduleStartDateAndTime:
                                    scheduleElement.sessionDetail &&
                                    scheduleElement.sessionDetail.start_time
                                        ? scheduleElement.sessionDetail.start_time
                                        : scheduleElement.scheduleStartDateAndTime,
                            });
                            lateConfig = updatedLateConfig;
                            studentLateAbsent = updatedStudentLateAbsent;
                        }
                    });
                }
            });
        } else if (manualLateRange.length) {
            scheduleData.forEach((scheduleElement) => {
                scheduleElement.students.forEach((filteredStudent) => {
                    if (
                        filteredStudent &&
                        filteredStudent._id._id &&
                        studentElement._student_id &&
                        filteredStudent._id._id.toString() ===
                            studentElement._student_id.toString() &&
                        filteredStudent.status === PRESENT &&
                        filteredStudent.tardisId &&
                        !filteredStudent.lateExclude
                    ) {
                        const {
                            lateConfig: updatedLateConfig,
                            studentLateAbsent: updatedStudentLateAbsent,
                        } = getManualRange({
                            manualLateData,
                            lateConfig,
                            studentLateAbsent,
                            tardisId: filteredStudent.tardisId,
                        });
                        lateConfig = updatedLateConfig;
                        studentLateAbsent = updatedStudentLateAbsent;
                    }
                });
            });
        }
        for (const lateLabel in lateConfig) {
            if (lateConfig.hasOwnProperty(lateLabel)) {
                if (lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate) {
                    const lateCount = Math.floor(
                        lateConfig[lateLabel].studentLate / lateConfig[lateLabel].noOfLate,
                    );
                    studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                    if (!lateConfig[lateLabel].studentAbsent) {
                        lateConfig[lateLabel].studentAbsent = 0;
                    }
                    lateConfig[lateLabel].studentAbsent +=
                        lateCount * lateConfig[lateLabel].noOfAbsent;
                }
            }
        }
        lateConfig = Object.entries(lateConfig).map(([labelName, config]) => ({
            labelName,
            ...config,
        }));
        return { lateConfig, studentLateAbsent };
    } catch (error) {
        console.log(error);
        const lateConfig = [];
        const studentLateAbsent = 0;
        return { lateConfig, studentLateAbsent };
    }
};

const getConfigAndStudentLateAbsentForSingleStudent = ({
    lateDurationRange,
    manualLateRange,
    manualLateData,
    sortedStudentSessions,
    lateExcludeManagement,
    _institution_calendar_id,
    programId,
    levelNo,
    term,
    courseId,
    rotationCount,
}) => {
    try {
        let lateConfig = {};
        let studentLateAbsent = 0;
        if (lateDurationRange.length) {
            sortedStudentSessions.forEach((scheduleElement, scheduleIndex) => {
                const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id,
                    programId,
                    levelNo,
                    term,
                    courseId,
                    studentId: scheduleElement?.students[0]?._id || scheduleElement?.students?._id,
                    rotationCount,
                    sessionId: scheduleElement?.sessionId || scheduleElement?.session?._session_id,
                    lateExcludeManagement,
                }).lateExclude;
                if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students &&
                    scheduleElement.students.length &&
                    scheduleElement.students[0].status === PRESENT &&
                    scheduleElement.students[0].primaryTime !== undefined &&
                    !scheduleElement.students[0].lateExclude &&
                    !lateExludeForSessions
                ) {
                    const {
                        lateConfig: updatedLateConfig,
                        studentLateAbsent: updatedStudentLateAbsent,
                    } = getAutoRange({
                        lateDurationRange,
                        lateConfig,
                        studentLateAbsent,
                        primaryTime: scheduleElement.students[0].primaryTime,
                        scheduleStartDateAndTime:
                            scheduleElement.sessionDetail &&
                            scheduleElement.sessionDetail.start_time
                                ? scheduleElement.sessionDetail.start_time
                                : scheduleElement.scheduleStartDateAndTime,
                        sortedStudentSessions,
                        scheduleIndex,
                    });
                    lateConfig = updatedLateConfig;
                    studentLateAbsent = updatedStudentLateAbsent;
                } else if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students &&
                    scheduleElement.students.status === PRESENT &&
                    scheduleElement.students.primaryTime !== undefined &&
                    !scheduleElement.students.lateExclude &&
                    !lateExludeForSessions
                ) {
                    const {
                        lateConfig: updatedLateConfig,
                        studentLateAbsent: updatedStudentLateAbsent,
                    } = getAutoRange({
                        lateDurationRange,
                        lateConfig,
                        studentLateAbsent,
                        primaryTime: scheduleElement.students.primaryTime,
                        scheduleStartDateAndTime:
                            scheduleElement.sessionDetail &&
                            scheduleElement.sessionDetail.start_time
                                ? scheduleElement.sessionDetail.start_time
                                : scheduleElement.scheduleStartDateAndTime,
                        sortedStudentSessions,
                        scheduleIndex,
                    });
                    lateConfig = updatedLateConfig;
                    studentLateAbsent = updatedStudentLateAbsent;
                }
            });
        } else if (manualLateRange.length) {
            sortedStudentSessions.forEach((scheduleElement, scheduleIndex) => {
                const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id,
                    programId,
                    levelNo,
                    term,
                    courseId,
                    studentId: scheduleElement.students[0]?._id || scheduleElement.students?._id,
                    rotationCount,
                    sessionId: scheduleElement.sessionId || scheduleElement.session._session_id,
                    lateExcludeManagement,
                }).lateExclude;
                if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students &&
                    scheduleElement.students.length &&
                    scheduleElement.students[0].status === PRESENT &&
                    scheduleElement.students[0].tardisId &&
                    !scheduleElement.students[0].lateExclude &&
                    !lateExludeForSessions
                ) {
                    const {
                        lateConfig: updatedLateConfig,
                        studentLateAbsent: updatedStudentLateAbsent,
                    } = getManualRange({
                        manualLateData,
                        lateConfig,
                        studentLateAbsent,
                        tardisId: scheduleElement.students[0].tardisId,
                        sortedStudentSessions,
                        scheduleIndex,
                    });
                    lateConfig = updatedLateConfig;
                    studentLateAbsent = updatedStudentLateAbsent;
                } else if (
                    scheduleElement &&
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.students &&
                    scheduleElement.students.length &&
                    scheduleElement.students.status === PRESENT &&
                    scheduleElement.students.tardisId &&
                    !scheduleElement.students.lateExclude &&
                    !lateExludeForSessions
                ) {
                    const {
                        lateConfig: updatedLateConfig,
                        studentLateAbsent: updatedStudentLateAbsent,
                    } = getManualRange({
                        manualLateData,
                        lateConfig,
                        studentLateAbsent,
                        tardisId: scheduleElement.students.tardisId,
                        sortedStudentSessions,
                        scheduleIndex,
                    });
                    lateConfig = updatedLateConfig;
                    studentLateAbsent = updatedStudentLateAbsent;
                }
            });
        }
        for (const lateLabel in lateConfig) {
            if (lateConfig.hasOwnProperty(lateLabel)) {
                if (lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate) {
                    const lateCount = Math.floor(
                        lateConfig[lateLabel].studentLate / lateConfig[lateLabel].noOfLate,
                    );
                    studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                    if (!lateConfig[lateLabel].studentAbsent) {
                        lateConfig[lateLabel].studentAbsent = 0;
                    }
                    lateConfig[lateLabel].studentAbsent +=
                        lateCount * lateConfig[lateLabel].noOfAbsent;
                }
            }
        }
        lateConfig = Object.entries(lateConfig).map(([labelName, config]) => ({
            labelName,
            ...config,
        }));
        return { lateConfig, studentLateAbsent };
    } catch (error) {
        console.log(error);
        const lateConfig = [];
        const studentLateAbsent = 0;
        return { lateConfig, studentLateAbsent };
    }
};

const getLateLabelForSchedule = ({
    lateDurationRange,
    manualLateRange,
    manualLateData,
    schedule,
    student_data,
    lateExcludeManagement,
    _institution_calendar_id,
    programId,
    levelNo,
    term,
    courseId,
    rotationCount,
}) => {
    try {
        let lateLabel = null;
        const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id,
            programId,
            levelNo,
            term,
            courseId,
            studentId: student_data._id,
            rotationCount,
            sessionId:
                schedule && schedule.session && schedule.session._session_id
                    ? schedule.session._session_id
                    : undefined,
            lateExcludeManagement,
        }).lateExclude;
        if (lateDurationRange.length) {
            if (
                schedule &&
                schedule.status === COMPLETED &&
                student_data &&
                student_data.status === PRESENT &&
                student_data.primaryTime !== undefined &&
                !student_data.lateExclude &&
                !lateExludeForSessions
            ) {
                const timeDifferenceInMilliseconds =
                    new Date(student_data.primaryTime) -
                    new Date(
                        schedule.sessionDetail && schedule.sessionDetail.start_time
                            ? schedule.sessionDetail.start_time
                            : schedule.scheduleStartDateAndTime,
                    );
                const diff = timeDifferenceInMilliseconds / (1000 * 60);
                lateLabel =
                    lateDurationRange.find((rangeItem) => {
                        return (
                            (rangeItem.endRange &&
                                rangeItem.startRange < diff &&
                                rangeItem.endRange >= diff) ||
                            (!rangeItem.endRange && rangeItem.startRange < diff)
                        );
                    }) || null;
            }
        }
        if (manualLateRange.length) {
            if (
                schedule &&
                schedule.status === COMPLETED &&
                student_data &&
                student_data.status === PRESENT &&
                student_data.tardisId &&
                !student_data.lateExclude &&
                !lateExludeForSessions
            ) {
                lateLabel = manualLateData[filteredStudent.tardisId.toString()];
            }
        }
        return {
            lateLabel,
        };
    } catch (error) {
        console.log(error);
        const lateLabel = null;
        return { lateLabel };
    }
};

const changePresentScheduleBasedLateConfigure = ({
    lateDurationRange,
    manualLateRange,
    manualLateData,
    courseSchedules,
    lateExcludeManagement,
}) => {
    try {
        if (lateDurationRange.length) {
            for (const courseSchedulesElement of courseSchedules) {
                courseSchedulesElement.data.forEach((studentElement, studentIndex) => {
                    let studentLateAbsent = 0;
                    let lateConfig = {};
                    studentElement.schedules.forEach((scheduleElement) => {
                        if (
                            scheduleElement &&
                            scheduleElement.status === COMPLETED &&
                            scheduleElement.students &&
                            scheduleElement.students.status === PRESENT &&
                            scheduleElement.students.primaryTime !== undefined &&
                            !scheduleElement.students.lateExclude
                        ) {
                            const { LateExclude } = checkLateExcludeConfigurationForCourseOrStudent(
                                {
                                    _institution_calendar_id:
                                        scheduleElement._institution_calendar_id,
                                    programId: scheduleElement.programId,
                                    courseId: scheduleElement.courseId,
                                    levelNo: scheduleElement.levelNo,
                                    term: scheduleElement.term,
                                    rotationCount: scheduleElement.rotationCount,
                                    lateExcludeManagement,
                                },
                            ).lateExclude;
                            const LateExcludeForStudent =
                                checkLateExcludeConfigurationForCourseOrStudent({
                                    _institution_calendar_id:
                                        scheduleElement._institution_calendar_id,
                                    programId: scheduleElement.programId,
                                    courseId: scheduleElement.courseId,
                                    levelNo: scheduleElement.levelNo,
                                    term: scheduleElement.term,
                                    rotationCount: scheduleElement.rotationCount,
                                    studentId:
                                        scheduleElement.students?._id ||
                                        scheduleElement.students[0]._id,
                                    lateExcludeManagement,
                                }).lateExclude;
                            const lateExludeForSessions =
                                checkLateExcludeConfigurationForCourseOrStudent({
                                    _institution_calendar_id:
                                        scheduleElement._institution_calendar_id,
                                    programId: scheduleElement.programId,
                                    courseId: scheduleElement.courseId,
                                    levelNo: scheduleElement.levelNo,
                                    term: scheduleElement.term,
                                    rotationCount: scheduleElement.rotationCount,
                                    studentId:
                                        scheduleElement.students?._id ||
                                        scheduleElement.students[0]._id,
                                    sessionId: scheduleElement.session._session_id,
                                    lateExcludeManagement,
                                }).lateExclude;
                            if (!LateExcludeForStudent && !LateExclude && !lateExludeForSessions) {
                                const {
                                    lateConfig: updatedLateConfig,
                                    studentLateAbsent: updatedStudentLateAbsent,
                                } = getAutoRange({
                                    lateDurationRange,
                                    lateConfig,
                                    studentLateAbsent,
                                    primaryTime: scheduleElement.students.primaryTime,
                                    scheduleStartDateAndTime:
                                        scheduleElement.scheduleStartDateAndTime,
                                });
                                lateConfig = updatedLateConfig;
                                studentLateAbsent = updatedStudentLateAbsent;
                            }
                        }
                    });
                    for (const lateLabel in lateConfig) {
                        if (lateConfig.hasOwnProperty(lateLabel)) {
                            if (
                                lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate
                            ) {
                                const lateCount = Math.floor(
                                    lateConfig[lateLabel].studentLate /
                                        lateConfig[lateLabel].noOfLate,
                                );
                                studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                                if (!lateConfig[lateLabel].studentAbsent) {
                                    lateConfig[lateLabel].studentAbsent = 0;
                                }
                                lateConfig[lateLabel].studentAbsent +=
                                    lateCount * lateConfig[lateLabel].noOfAbsent;
                            }
                        }
                    }
                    if (studentLateAbsent) {
                        courseSchedulesElement.data[studentIndex].presentedSchedulesWithoutOnDuty -=
                            studentLateAbsent;
                        courseSchedulesElement.data[studentIndex].leavedSchedules +=
                            studentLateAbsent;
                        if (
                            courseSchedulesElement.data[studentIndex]
                                .presentedSchedulesWithoutOnDuty < 0
                        ) {
                            courseSchedulesElement.data[
                                studentIndex
                            ].presentedSchedulesWithoutOnDuty = 0;
                        }
                        if (
                            courseSchedulesElement.data[studentIndex].leavedSchedules >
                            courseSchedulesElement.data[studentIndex].totalCompletedSchedules
                        ) {
                            courseSchedulesElement.data[studentIndex].leavedSchedules =
                                courseSchedulesElement.data[studentIndex].totalCompletedSchedules;
                        }
                        courseSchedulesElement.data[studentIndex].presentPercentage =
                            ((courseSchedulesElement.data[studentIndex]
                                .presentedSchedulesWithoutOnDuty +
                                courseSchedulesElement.data[studentIndex]
                                    .presentedSchedulesBasedOnDuty) /
                                courseSchedulesElement.data[studentIndex].totalCompletedSchedules) *
                            100;
                        courseSchedulesElement.data[studentIndex].absentPercentage =
                            (courseSchedulesElement.data[studentIndex].leavedSchedules /
                                courseSchedulesElement.data[studentIndex].totalSchedules) *
                            100;
                    }
                });
            }
        } else if (manualLateRange.length) {
            for (const courseSchedulesElement of courseSchedules) {
                courseSchedulesElement.data.forEach((studentElement, studentIndex) => {
                    let studentLateAbsent = 0;
                    let lateConfig = {};
                    studentElement.schedules.forEach((scheduleElement) => {
                        if (
                            scheduleElement &&
                            scheduleElement.status === COMPLETED &&
                            scheduleElement.students &&
                            scheduleElement.students.status === PRESENT &&
                            scheduleElement.students.tardisId &&
                            !scheduleElement.students.lateExclude
                        ) {
                            const { LateExclude } = checkLateExcludeConfigurationForCourseOrStudent(
                                {
                                    _institution_calendar_id:
                                        scheduleElement._institution_calendar_id,
                                    programId: scheduleElement.programId,
                                    courseId: scheduleElement.courseId,
                                    levelNo: scheduleElement.levelNo,
                                    term: scheduleElement.term,
                                    rotationCount: scheduleElement.rotationCount,
                                    lateExcludeManagement,
                                },
                            ).lateExclude;
                            const LateExcludeForStudent =
                                checkLateExcludeConfigurationForCourseOrStudent({
                                    _institution_calendar_id:
                                        scheduleElement._institution_calendar_id,
                                    programId: scheduleElement.programId,
                                    courseId: scheduleElement.courseId,
                                    levelNo: scheduleElement.levelNo,
                                    term: scheduleElement.term,
                                    rotationCount: scheduleElement.rotationCount,
                                    studentId: scheduleElement.students[0]._id,
                                    lateExcludeManagement,
                                }).lateExclude;
                            const lateExludeForSessions =
                                checkLateExcludeConfigurationForCourseOrStudent({
                                    _institution_calendar_id:
                                        scheduleElement._institution_calendar_id,
                                    programId: scheduleElement.programId,
                                    courseId: scheduleElement.courseId,
                                    levelNo: scheduleElement.levelNo,
                                    term: scheduleElement.term,
                                    rotationCount: scheduleElement.rotationCount,
                                    studentId: scheduleElement.students[0]._id,
                                    sessionId: scheduleElement.session._session_id,
                                    lateExcludeManagement,
                                }).lateExclude;
                            if (!LateExcludeForStudent && !LateExclude && !lateExludeForSessions) {
                                const {
                                    lateConfig: updatedLateConfig,
                                    studentLateAbsent: updatedStudentLateAbsent,
                                } = getManualRange({
                                    lateDurationRange,
                                    lateConfig,
                                    studentLateAbsent,
                                    tardisId: scheduleElement.students.tardisId,
                                });
                                lateConfig = updatedLateConfig;
                                studentLateAbsent = updatedStudentLateAbsent;
                            }
                        }
                    });
                    for (const lateLabel in lateConfig) {
                        if (lateConfig.hasOwnProperty(lateLabel)) {
                            if (
                                lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate
                            ) {
                                const lateCount = Math.floor(
                                    lateConfig[lateLabel].studentLate /
                                        lateConfig[lateLabel].noOfLate,
                                );
                                studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                                if (!lateConfig[lateLabel].studentAbsent) {
                                    lateConfig[lateLabel].studentAbsent = 0;
                                }
                                lateConfig[lateLabel].studentAbsent +=
                                    lateCount * lateConfig[lateLabel].noOfAbsent;
                            }
                        }
                    }
                    if (studentLateAbsent) {
                        courseSchedulesElement.data[studentIndex].presentedSchedulesWithoutOnDuty -=
                            studentLateAbsent;
                        courseSchedulesElement.data[studentIndex].leavedSchedules +=
                            studentLateAbsent;
                        if (
                            courseSchedulesElement.data[studentIndex]
                                .presentedSchedulesWithoutOnDuty < 0
                        ) {
                            courseSchedulesElement.data[
                                studentIndex
                            ].presentedSchedulesWithoutOnDuty = 0;
                        }
                        if (
                            courseSchedulesElement.data[studentIndex].leavedSchedules >
                            courseSchedulesElement.data[studentIndex].totalCompletedSchedules
                        ) {
                            courseSchedulesElement.data[studentIndex].leavedSchedules =
                                courseSchedulesElement.data[studentIndex].totalCompletedSchedules;
                        }
                        courseSchedulesElement.data[studentIndex].presentPercentage =
                            ((courseSchedulesElement.data[studentIndex]
                                .presentedSchedulesWithoutOnDuty +
                                courseSchedulesElement.data[studentIndex]
                                    .presentedSchedulesBasedOnDuty) /
                                courseSchedulesElement.data[studentIndex].totalCompletedSchedules) *
                            100;
                        courseSchedulesElement.data[studentIndex].absentPercentage =
                            (courseSchedulesElement.data[studentIndex].leavedSchedules /
                                courseSchedulesElement.data[studentIndex].totalSchedules) *
                            100;
                    }
                });
            }
        }
    } catch (error) {
        console.log(error);
        return null;
    }
};

const changePresentScheduleBasedLateConfigureForSingleStudent = ({
    lateDurationRange,
    manualLateRange,
    manualLateData,
    courseSchedules,
    lateExcludeManagement,
}) => {
    try {
        if (lateDurationRange.length && courseSchedules.length) {
            const { LateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id:
                    courseSchedules[0].schedules?._institution_calendar_id ||
                    courseSchedules[0].schedules[0]?._institution_calendar_id ||
                    courseSchedules[0].allSchedules[0]?._institution_calendar_id,
                programId:
                    courseSchedules[0].schedules?.programId ||
                    courseSchedules[0].schedules[0]?.programId ||
                    courseSchedules[0].allSchedules[0]?.programId,
                courseId:
                    courseSchedules[0].schedules?.courseId ||
                    courseSchedules[0].schedules[0]?.courseId ||
                    courseSchedules[0].allSchedules[0]?.courseId,
                levelNo:
                    courseSchedules[0].schedules?.levelNo ||
                    courseSchedules[0].schedules[0]?.levelNo ||
                    courseSchedules[0].allSchedules[0]?.levelNo,
                term:
                    courseSchedules[0].schedules?.term ||
                    courseSchedules[0].schedules[0]?.term ||
                    courseSchedules[0].allSchedules[0]?.term,
                rotationCount:
                    courseSchedules[0].schedules?.rotationCount ||
                    courseSchedules[0].schedules[0]?.rotationCount ||
                    courseSchedules[0].allSchedules[0]?.rotationCount,
                lateExcludeManagement,
            });
            courseSchedules.forEach((courseSchedulesElement, studentIndex) => {
                let studentLateAbsent = 0;
                let lateConfig = {};
                const LateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id:
                        courseSchedulesElement.schedules?._institution_calendar_id ||
                        courseSchedulesElement.schedules[0]?._institution_calendar_id ||
                        courseSchedulesElement.allSchedules[0]?._institution_calendar_id,
                    programId:
                        courseSchedulesElement.schedules?.programId ||
                        courseSchedulesElement.schedules[0]?.programId ||
                        courseSchedulesElement.allSchedules[0]?.programId,
                    courseId:
                        courseSchedulesElement.schedules?.courseId ||
                        courseSchedulesElement.schedules[0]?.courseId ||
                        courseSchedulesElement.allSchedules[0]?.courseId,
                    levelNo:
                        courseSchedulesElement.schedules?.levelNo ||
                        courseSchedulesElement.schedules[0]?.levelNo ||
                        courseSchedulesElement.allSchedules[0]?.levelNo,
                    term:
                        courseSchedulesElement.schedules?.term ||
                        courseSchedulesElement.schedules[0]?.term ||
                        courseSchedulesElement.allSchedules[0]?.term,
                    rotationCount:
                        courseSchedulesElement.schedules?.rotationCount ||
                        courseSchedulesElement.schedules[0]?.rotationCount ||
                        courseSchedulesElement.allSchedules[0]?.rotationCount,
                    studentId:
                        courseSchedulesElement?.schedules?.students?._id ||
                        courseSchedulesElement.schedules[0]?.students?._id ||
                        courseSchedulesElement.allSchedules[0]?.students?._id,
                    lateExcludeManagement,
                }).lateExclude;
                const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id:
                        courseSchedulesElement.schedules?._institution_calendar_id ||
                        courseSchedulesElement.schedules[0]?._institution_calendar_id ||
                        courseSchedulesElement.allSchedules[0]?._institution_calendar_id,
                    programId:
                        courseSchedulesElement.schedules?.programId ||
                        courseSchedulesElement.schedules[0]?.programId ||
                        courseSchedulesElement.allSchedules[0]?.programId,
                    courseId:
                        courseSchedulesElement.schedules?.courseId ||
                        courseSchedulesElement.schedules[0]?.courseId ||
                        courseSchedulesElement.allSchedules[0]?.courseId,
                    levelNo:
                        courseSchedulesElement.schedules?.levelNo ||
                        courseSchedulesElement.schedules[0]?.levelNo ||
                        courseSchedulesElement.allSchedules[0]?.levelNo,
                    term:
                        courseSchedulesElement.schedules?.term ||
                        courseSchedulesElement.schedules[0]?.term ||
                        courseSchedulesElement.allSchedules[0]?.term,
                    rotationCount:
                        courseSchedulesElement.schedules?.rotationCount ||
                        courseSchedulesElement.schedules[0]?.rotationCount ||
                        courseSchedulesElement.allSchedules[0]?.rotationCount,
                    studentId:
                        courseSchedulesElement?.schedules?.students?._id ||
                        courseSchedulesElement.schedules[0]?.students?._id ||
                        courseSchedulesElement.allSchedules[0]?.students?._id,
                    sessionId:
                        courseSchedulesElement?.schedules?.session?._session_id ||
                        courseSchedulesElement.schedules[0]?.session?._session_id ||
                        courseSchedulesElement.allSchedules[0]?.session?._session_id,
                    lateExcludeManagement,
                }).lateExclude;
                const getStudentLateAbsent = ({ schedules }) => {
                    schedules.forEach((scheduleElement) => {
                        if (
                            scheduleElement &&
                            scheduleElement.status === COMPLETED &&
                            scheduleElement.students &&
                            scheduleElement.students.status === PRESENT &&
                            scheduleElement.students.primaryTime !== undefined &&
                            !scheduleElement.students.lateExclude
                        ) {
                            const {
                                lateConfig: updatedLateConfig,
                                studentLateAbsent: updatedStudentLateAbsent,
                            } = getAutoRange({
                                lateDurationRange,
                                lateConfig,
                                studentLateAbsent,
                                primaryTime: scheduleElement?.students?.primaryTime,
                                scheduleStartDateAndTime:
                                    scheduleElement?.sessionDetail &&
                                    scheduleElement?.sessionDetail?.start_time
                                        ? scheduleElement.sessionDetail.start_time
                                        : scheduleElement.scheduleStartDateAndTime,
                            });
                            lateConfig = updatedLateConfig;
                            studentLateAbsent = updatedStudentLateAbsent;
                        }
                    });
                    for (const lateLabel in lateConfig) {
                        if (lateConfig.hasOwnProperty(lateLabel)) {
                            if (
                                lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate
                            ) {
                                const lateCount = Math.floor(
                                    lateConfig[lateLabel].studentLate /
                                        lateConfig[lateLabel].noOfLate,
                                );
                                studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                                if (!lateConfig[lateLabel].studentAbsent) {
                                    lateConfig[lateLabel].studentAbsent = 0;
                                }
                                lateConfig[lateLabel].studentAbsent +=
                                    lateCount * lateConfig[lateLabel].noOfAbsent;
                            }
                        }
                    }
                    return { studentLateAbsent };
                };
                if (!LateExclude && !LateExcludeForStudent && !lateExludeForSessions) {
                    if (courseSchedulesElement?.allSchedules?.length) {
                        const { studentLateAbsent: updatedStudentLateAbsent } =
                            getStudentLateAbsent({
                                schedules: courseSchedulesElement.allSchedules,
                            });
                        studentLateAbsent = updatedStudentLateAbsent;
                    } else {
                        const { studentLateAbsent: updatedStudentLateAbsent } =
                            getStudentLateAbsent({
                                schedules: courseSchedulesElement.schedules,
                            });
                        studentLateAbsent = updatedStudentLateAbsent;
                    }
                }
                if (studentLateAbsent) {
                    if (courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty) {
                        courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty -=
                            studentLateAbsent;
                        if (courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty < 0) {
                            courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty = 0;
                        }
                        courseSchedules[studentIndex].presentPercentage =
                            ((courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty +
                                courseSchedules[studentIndex].presentedSchedulesBasedOnDuty) /
                                courseSchedules[studentIndex].totalCompletedSchedules) *
                            100;
                    }
                    if (courseSchedules[studentIndex].leavedSchedules) {
                        courseSchedules[studentIndex].leavedSchedules += studentLateAbsent;
                        if (
                            courseSchedules[studentIndex].leavedSchedules >
                            courseSchedules[studentIndex].totalCompletedSchedules
                        ) {
                            courseSchedules[studentIndex].leavedSchedules =
                                courseSchedules[studentIndex].totalCompletedSchedules;
                        }
                        courseSchedules[studentIndex].absentPercentage =
                            (courseSchedules[studentIndex].leavedSchedules /
                                courseSchedules[studentIndex].totalSchedules) *
                            100;
                    }
                    if (courseSchedules[studentIndex].totalLeave) {
                        courseSchedules[studentIndex].totalLeave += studentLateAbsent;
                        if (
                            courseSchedules[studentIndex].totalLeave >
                            courseSchedules[studentIndex].totalDays
                        ) {
                            courseSchedules[studentIndex].totalLeave =
                                courseSchedules[studentIndex].totalDays;
                        }
                        courseSchedules[studentIndex].absentPercentage =
                            (courseSchedules[studentIndex].totalLeave /
                                courseSchedules[studentIndex].totalDays) *
                            100;
                    }
                }
            });
        } else if (manualLateRange.length && courseSchedules.length) {
            const { LateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: courseSchedules[0].schedules._institution_calendar_id,
                programId: courseSchedules[0].schedules.programId,
                courseId: courseSchedules[0].schedules.courseId,
                levelNo: courseSchedules[0].schedules.levelNo,
                term: courseSchedules[0].schedules.term,
                rotationCount: courseSchedules[0].schedules.rotationCount,
                lateExcludeManagement,
            });
            courseSchedules.forEach((courseSchedulesElement, studentIndex) => {
                let studentLateAbsent = 0;
                let lateConfig = {};
                const getStudentLateAbsent = ({ schedules }) => {
                    schedules.forEach((scheduleElement) => {
                        if (
                            scheduleElement &&
                            scheduleElement.status === COMPLETED &&
                            scheduleElement.students &&
                            scheduleElement.students.status === PRESENT &&
                            scheduleElement.students.tardisId &&
                            !scheduleElement.students.lateExclude
                        ) {
                            const {
                                lateConfig: updatedLateConfig,
                                studentLateAbsent: updatedStudentLateAbsent,
                            } = getManualRange({
                                lateDurationRange,
                                lateConfig,
                                studentLateAbsent,
                                tardisId: scheduleElement.students.tardisId,
                            });
                            lateConfig = updatedLateConfig;
                            studentLateAbsent = updatedStudentLateAbsent;
                        }
                    });
                    for (const lateLabel in lateConfig) {
                        if (lateConfig.hasOwnProperty(lateLabel)) {
                            if (
                                lateConfig[lateLabel].studentLate >= lateConfig[lateLabel].noOfLate
                            ) {
                                const lateCount = Math.floor(
                                    lateConfig[lateLabel].studentLate /
                                        lateConfig[lateLabel].noOfLate,
                                );
                                studentLateAbsent += lateCount * lateConfig[lateLabel].noOfAbsent;
                                if (!lateConfig[lateLabel].studentAbsent) {
                                    lateConfig[lateLabel].studentAbsent = 0;
                                }
                                lateConfig[lateLabel].studentAbsent +=
                                    lateCount * lateConfig[lateLabel].noOfAbsent;
                            }
                        }
                    }
                    return { studentLateAbsent };
                };
                const LateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id:
                        courseSchedulesElement.schedules._institution_calendar_id,
                    programId: courseSchedulesElement.schedules.programId,
                    courseId: courseSchedulesElement.schedules.courseId,
                    levelNo: courseSchedulesElement.schedules.levelNo,
                    term: courseSchedulesElement.schedules.term,
                    rotationCount: courseSchedulesElement.schedules.rotationCount,
                    studentId: courseSchedulesElement.schedules.students._id,
                    lateExcludeManagement,
                }).lateExclude;
                const lateExludeForSessions = checkLateExcludeConfigurationForCourseOrStudent({
                    _institution_calendar_id:
                        courseSchedulesElement.schedules._institution_calendar_id,
                    programId: courseSchedulesElement.schedules.programId,
                    courseId: courseSchedulesElement.schedules.courseId,
                    levelNo: courseSchedulesElement.schedules.levelNo,
                    term: courseSchedulesElement.schedules.term,
                    rotationCount: courseSchedulesElement.schedules.rotationCount,
                    studentId: courseSchedulesElement.schedules.students._id,
                    sessionId: courseSchedulesElement.schedules.session._session_id,
                    lateExcludeManagement,
                }).lateExclude;
                if (!LateExclude && !LateExcludeForStudent && !lateExludeForSessions) {
                    if (courseSchedulesElement.allSchedules.length) {
                        const { studentLateAbsent: updatedStudentLateAbsent } =
                            getStudentLateAbsent({
                                schedules: courseSchedulesElement.allSchedules,
                            });
                        studentLateAbsent = updatedStudentLateAbsent;
                    } else {
                        const { studentLateAbsent: updatedStudentLateAbsent } =
                            getStudentLateAbsent({
                                schedules: courseSchedulesElement.schedules,
                            });
                        studentLateAbsent = updatedStudentLateAbsent;
                    }
                }
                if (studentLateAbsent) {
                    if (courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty) {
                        courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty -=
                            studentLateAbsent;
                        if (courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty < 0) {
                            courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty = 0;
                        }
                        courseSchedules[studentIndex].presentPercentage =
                            ((courseSchedules[studentIndex].presentedSchedulesWithoutOnDuty +
                                courseSchedules[studentIndex].presentedSchedulesBasedOnDuty) /
                                courseSchedules[studentIndex].totalCompletedSchedules) *
                            100;
                    }
                    if (courseSchedules[studentIndex].leavedSchedules) {
                        courseSchedules[studentIndex].leavedSchedules += studentLateAbsent;
                        if (
                            courseSchedules[studentIndex].leavedSchedules >
                            courseSchedules[studentIndex].totalCompletedSchedules
                        ) {
                            courseSchedules[studentIndex].leavedSchedules =
                                courseSchedules[studentIndex].totalCompletedSchedules;
                        }
                        courseSchedules[studentIndex].absentPercentage =
                            (courseSchedules[studentIndex].leavedSchedules /
                                courseSchedules[studentIndex].totalSchedules) *
                            100;
                    }
                    if (courseSchedules[studentIndex].totalLeave) {
                        courseSchedules[studentIndex].totalLeave += studentLateAbsent;
                        if (
                            courseSchedules[studentIndex].totalLeave >
                            courseSchedules[studentIndex].totalDays
                        ) {
                            courseSchedules[studentIndex].totalLeave =
                                courseSchedules[studentIndex].totalDays;
                        }
                        courseSchedules[studentIndex].absentPercentage =
                            (courseSchedules[studentIndex].totalLeave /
                                courseSchedules[studentIndex].totalDays) *
                            100;
                    }
                }
            });
        }
    } catch (error) {
        console.log(error);
        return null;
    }
};

const activityReportFromCloudFunction = async ({
    institutionCalendarId,
    programId,
    courseId,
    levelNo,
    term,
    rotation_count,
    userId,
    isClear,
}) => {
    try {
        const tabs = [`${SUMMARY}`, `${SLO}`, `${CLO}`, `${PLO}`, 'session'];
        let userType = [DC_STAFF];
        if (userId) {
            userType = [DC_STUDENT];
        }
        const summaryPromise = [SUMMARY].map((endpoint) => {
            let url = `/activity_report/${institutionCalendarId}/${programId}/${courseId}/${levelNo}/${term}/${userType}/${endpoint}`;
            const queryParams = [];
            if (rotation_count !== undefined) {
                queryParams.push(`rotation_count=${rotation_count}`);
            }
            if (userId !== undefined) {
                queryParams.push(`userId=${userId}`);
            }
            if (isClear) {
                queryParams.push(`isClear=${isClear}`);
            }
            if (queryParams.length > 0) {
                url += `?${queryParams.join('&')}`;
            }
            const promise = cloudFunctionAxios({
                url,
                method: 'GET',
            });
            // Await only for SUMMARY tab, others will run in the background
            if (endpoint === `${SUMMARY}`) {
                return promise;
            }
            // Return the promise without await for other tabs
            return promise.then(() => null).catch(() => null);
        });

        // Await the completion of the SUMMARY tab
        const summaryResult = await Promise.all(summaryPromise);
        // Run other tabs in the background
        tabs.filter((tab) => tab !== `${SUMMARY}`).map((tab) => {
            let url = `/activity_report/${institutionCalendarId}/${programId}/${courseId}/${levelNo}/${term}/${userType}/${tab}`;
            const queryParams = [];
            if (rotation_count !== undefined) {
                queryParams.push(`rotation_count=${rotation_count}`);
            }
            if (userId !== undefined) {
                queryParams.push(`userId=${userId}`);
            }
            if (isClear) {
                queryParams.push(`isClear=${isClear}`);
            }
            if (queryParams.length > 0) {
                url += `?${queryParams.join('&')}`;
            }
            return cloudFunctionAxios({
                url,
                method: 'GET',
            })
                .then(() => null)
                .catch(() => null);
        });

        // Don't await for other tabs
        return summaryResult;
        // const promises = tabs.map((endpoint) =>
        //     userType.map((type) => {
        //         let url = `/activity_report/${institutionCalendarId}/${programId}/${courseId}/${levelNo}/${term}/${type}/${endpoint}`;
        //         const queryParams = [];
        //         if (rotation_count !== undefined) {
        //             queryParams.push(`rotation_count=${rotation_count}`);
        //         }
        //         if (userId !== undefined) {
        //             queryParams.push(`userId=${userId}`);
        //         }
        //         if (isClear) {
        //             queryParams.push(`isClear=${isClear}`);
        //         }
        //         if (queryParams.length > 0) {
        //             url += `?${queryParams.join('&')}`;
        //         }
        //         const promise = cloudFunctionAxios({
        //             url,
        //             method: 'GET',
        //         });
        //         // Await only for SUMMARY tab, others will run in the background
        //         if (endpoint === `${SUMMARY}`) {
        //             return promise;
        //         }
        //         // Return the promise without await for other tabs
        //         return promise.then(() => null).catch(() => null);
        //     }),
        // );
        // const result = await Promise.allSettled(promises.flat());
    } catch (error) {
        console.log(error);
        return null;
    }
};

const getUserPermissionModuleList = async () => {
    if (USER_PERMISSION_MODULE_NAMES && Object.keys(USER_PERMISSION_MODULE_NAMES).length) {
        return Object.values(USER_PERMISSION_MODULE_NAMES);
    }
    return [];
};

const activateRedisTimer = async ({ moduleName, uniqueId, documentStatus, timeInSeconds }) => {
    await redisClient.Client.set(
        `${moduleName}:${uniqueId}-${documentStatus}`,
        JSON.stringify({ type: moduleName }),
        'EX',
        timeInSeconds,
    );
};

const deleteRedisTimer = async ({ moduleName, uniqueId, documentStatus }) => {
    await redisClient.Client.del(`${moduleName}:${uniqueId}-${documentStatus}`);
};

const getExpireTimeInSeconds = ({ endDateAndTime = new Date(), startDateAndTime = new Date() }) => {
    return Math.floor((endDateAndTime - startDateAndTime) / 1000);
};

const middlewareValidator = (schema) => (req, res, next) => {
    try {
        const dataToValidate = {};
        const { children = {} } = schema.describe();
        if (children.header) {
            dataToValidate.headers = req.headers;
        }
        if (children.query) {
            dataToValidate.query = req.query;
        }
        if (children.body) {
            dataToValidate.body = req.body;
        }
        const { error } = schema.validate(dataToValidate);
        if (error) {
            const errors = error.details.map((d) => d.message).join(',');
            return res.status(400).json({ errors });
        }
        next();
    } catch (err) {
        next(err);
    }
};

const getChangedDelayedStatusIntoPending = ({ lmsApplications }) => {
    return lmsApplications.map((applicationElement) => {
        if (applicationElement.approvalStatus === DELAYED) {
            return {
                ...applicationElement,
                approvalStatus: LMS_PENDING,
            };
        }
        return applicationElement;
    });
};

module.exports = {
    getUserRoleProgramList,
    getActiveInstitutionCalendars,
    lmsNewSetting,
    updateStudentGroupRedisKey,
    updateProgramCalendarRedisData,
    updateUserCourseRedisKey,
    checkRestrictCourse,
    filterStudentsForCourseRestriction,
    lmsNewSettingWithOptions,
    updateWarningsInRecord,
    multiCourseWarningsForStudent,
    getLateAutoAndManualRange,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    getLateConfigAndStudentLateAbsent,
    getLateConfigAndStudentLateAbsentForStaffExport,
    getLateLabelForSchedule,
    getConfigAndStudentLateAbsentForSingleStudent,
    changePresentScheduleBasedLateConfigure,
    changePresentScheduleBasedLateConfigureForSingleStudent,
    getWarningDataFromRedis,
    activityReportFromCloudFunction,
    getAutoRange,
    getUserPermissionModuleList,
    activateRedisTimer,
    getExpireTimeInSeconds,
    deleteRedisTimer,
    middlewareValidator,
    getChangedDelayedStatusIntoPending,
};
