const { sendResponse } = require('../../utility/common');
const { getInprogressCourses, getCompletedCourses, validate } = require('./report_service');

/**
 * get in progress course of given student
 * @param {query} req
 * @param {*} res
 * @returns response
 */
exports.getStudentInprogressCourse = async (req, res) => {
    try {
        const { institutionCalendarId, studentId } = req.query;
        const result = await getInprogressCourses(studentId, institutionCalendarId);
        if (!result.status) return sendResponse(res, 200, false, result.message, []);
        return sendResponse(res, 200, true, req.t('COURSE_LIST'), result.data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), []);
    }
};

/**
 * get completed course of given student
 * @param {query} req
 * @param {*} res
 * @returns response
 */
exports.getStudentCompletedCourse = async (req, res) => {
    try {
        const { institutionCalendarId, studentId } = req.query;
        const { _institution_id } = req.headers;
        const result = await getCompletedCourses(studentId, institutionCalendarId, _institution_id);
        if (!result.status) return sendResponse(res, 200, false, result.message, []);
        return sendResponse(res, 200, true, req.t('COURSE_LIST'), result.data);
    } catch (error) {
        return sendResponse(res, 500, false, req.t('SERVER_ERROR'), []);
    }
};
