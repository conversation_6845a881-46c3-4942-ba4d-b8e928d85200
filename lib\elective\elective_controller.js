// const elective = require('./elective_model');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const elective_formate = require('./elective_formate');
const ObjectId = require('mongodb').ObjectID;
const constant = require('../utility/constants');
var department = require('mongoose').model(constant.DEPARTMENT);
var department_division = require('mongoose').model(constant.DEPARTMENT_DIVISIONS);
var department_subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
var credit_master = require('mongoose').model(constant.CREDIT_HOURS_MASTER);
var course = require('mongoose').model(constant.COURSE);
var session_order = require('mongoose').model(constant.SESSION_ORDER);
var program = require('mongoose').model(constant.PROGRAM);

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { 'model': 'elective' } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_administration_department_id', foreignField: '_id', as: 'administration_department' } },
        { $unwind: { path: '$administration_department', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_administration_division_id', foreignField: '_id', as: 'administration_division' } },
        { $unwind: { path: '$administration_division', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_administration_subject_id', foreignField: '_id', as: 'administration_subject' } },
        { $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_participating_department_id', foreignField: '_id', as: 'participating_department' } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_participating_division_id', foreignField: '_id', as: 'participating_division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(course, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "elective list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ elective_formate.elective(doc.data));
        // common_files.list_all_response(res, 200, true, "elective list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* elective_formate.elective(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $match: { 'model': 'elective' } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_administration_department_id', foreignField: '_id', as: 'administration_department' } },
        { $unwind: { path: '$administration_department', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_administration_division_id', foreignField: '_id', as: 'administration_division' } },
        { $unwind: { path: '$administration_division', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_administration_subject_id', foreignField: '_id', as: 'administration_subject' } },
        { $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_participating_department_id', foreignField: '_id', as: 'participating_department' } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_participating_division_id', foreignField: '_id', as: 'participating_division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
        { $sort: { updatedAt: -1 } }
    ];
    let doc = await base_control.get_aggregate(elective, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "elective details", /* doc.data */ elective_formate.elective_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert_topic_allocation = async (req, res) => {

    let topics_ids = [], subject_ids = [], staff_ids = [], elective_student_group_ids = [];
    let course_check = { status: false }, topic_check = { status: false }, subject_check = { status: false }, staff_check = { status: true }, student_group_check = { status: true };

    let docs = { status: false }, doc_update = { status: false }, status = false, datas = null;

    if (req.body.data.length != 0) {
        req.body.data.forEach(body_data => {
            /* if (body_data._topics_id != undefined && body_data._topics_id.length != 0) {
                if (topics_ids.indexOf(body_data._topics_id) == -1) {
                    topics_ids.push(ObjectId(body_data._topics_id));
                }
            } */
            if (body_data._subject_id != undefined && body_data._subject_id.length != 0) {
                if (subject_ids.indexOf(body_data._subject_id) == -1) {
                    subject_ids.push(body_data._subject_id);
                }
            }

            /* if (body_data._staff_id != undefined && body_data._staff_id.length != 0) {
                if (staff_ids.indexOf(body_data._staff_id) == -1) {
                    staff_ids.push(body_data._staff_id);
                    // staff_ids.push(ObjectID(body_data._staff_id));
                }
            } */

            if (body_data._elective_student_group_id != undefined && body_data.length != 0) {
                if (elective_student_group_ids.indexOf(body_data._elective_student_group_id) == -1) {
                    elective_student_group_ids.push(body_data._elective_student_group_id);
                    // elective_student_group_ids.push(ObjectID(body_data._elective_student_group_id));
                }
            }
        });
    }

    if (req.body._course_id != undefined && req.body._course_id.length != 0) {
        course_check = await base_control.check_id(course, { _id: req.body._course_id, 'isDeleted': false });
        if (course_check.status) {
            if (topics_ids.length != 0) {
                topic_check = await base_control.check_id(course, { _id: req.body._course_id, 'elective._id': { $in: topics_ids }, 'isDeleted': false });
                // console.log(topic_check);
            }
        }
    }
    if (subject_ids.length != 0) {
        subject_check = await base_control.check_id(department_subject, { _id: { $in: subject_ids }, 'isDeleted': false });
        // console.log(subject_check, subject_ids);
    }

    /* if (staff_ids.length != 0) {
        staff_check = await base_control.check_id(staff, { _id: { $in: staff_ids }, 'isDeleted': false });
    } */

    /* 
    if (elective_student_group_ids.length != 0) {
        student_group_check = await base_control.check_id(student_group, { _id: { $in: elective_student_group_ids }, 'isDeleted': false });
    } */
    console.log(course_check.status/* , topic_check.status */, subject_check.status, /* staff_check.status, */ student_group_check.status);

    if (course_check.status && /* topic_check.status && */ subject_check.status /* && staff_check.status */ && student_group_check.status) {
        let fresh_push = [];
        req.body.data.forEach(async (body_data, index) => {
            if (body_data.id != '' && body_data.id.length == 24) {
                let condition = { _id: req.body._course_id, 'elective._id': ObjectId(body_data.id) };
                let parsing_obj = {
                    $set: {
                        'elective.$._subject_id': body_data._subject_id,
                        'elective.$._staff_id': body_data._staff_id,
                        'elective.$._elective_student_group_id': body_data._elective_student_group_id
                    }
                };
                // console.log(condition, parsing_obj);
                docs = await base_control.update_condition(course, condition, parsing_obj);
                // console.log(docs);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            } else {
                fresh_push.push({
                    topics: body_data.topics,
                    _subject_id: body_data._subject_id,
                    _elective_student_group_id: body_data._elective_student_group_id
                });
                status = true;
            }
        });

        if (status) {
            if (fresh_push.length != 0) {
                let condition = { _id: req.body._course_id };
                let elective_topics = {
                    $push: {
                        elective: {
                            $each: fresh_push
                        }
                    }
                };
                docs = await base_control.update_condition(course, condition, elective_topics);
                if (docs.status) {
                    common_files.com_response(res, 201, true, "Elective topic allocation is successfully", docs.data);
                } else {
                    common_files.com_response(res, 500, false, "Error", docs);
                }
            } else {
                common_files.com_response(res, 201, true, "Elective topic allocation is successfully", datas.data);
            }
        } else {
            common_files.com_response(res, 500, false, "Error", datas.data);
        }
        /* if (req.body.data.length == index + 1) {
            if (status) {
                common_files.com_response(res, 201, true, "Elective topic allocation is successfully", docs.data);
            } else {
                common_files.com_response(res, 500, false, "Error", docs);
            }
        }
    */
        //docs = await base_control.insert(elective, objects);
        // if (docs.status) {
        //     common_files.com_response(res, 201, true, "Elective topic allocation is successfully", docs.data);
        // } else {
        //     common_files.com_response(res, 500, false, "Error ", docs.data);
        // }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}
/* 
exports.update = async (req, res) => {
    let department_check = { status: true }, division_check = { status: true }, subject_check = { status: true };
    let part_department_check = { status: true }, part_division_check = { status: true }, part_subject_check = { status: true };

    if (req.body._administration_department_id != undefined) {
        department_check = await base_control.check_id(department, { _id: { $in: req.body._administration_department_id }, 'isDeleted': false });
    }
    if (req.body._administration_division_id != undefined) {
        division_check = await base_control.check_id(department_division, { _id: { $in: req.body._administration_division_id }, 'isDeleted': false });
    }
    if (req.body._administration_subject_id != undefined) {
        subject_check = await base_control.check_id(department_subject, { _id: { $in: req.body._administration_subject_id }, 'isDeleted': false });
    }
    if (req.body._participating_department_id != undefined) {
        part_department_check = await base_control.check_id(department, { _id: { $in: req.body._participating_department_id }, 'isDeleted': false });
    }
    if (req.body._participating_division_id != undefined) {
        part_division_check = await base_control.check_id(department_division, { _id: { $in: req.body._participating_division_id }, 'isDeleted': false });
    }
    if (req.body._participating_subject_id != undefined) {
        part_subject_check = await base_control.check_id(department_subject, { _id: { $in: req.body._participating_subject_id }, 'isDeleted': false });
    }
    // console.log(department_check.status, division_check.status, subject_check.status, part_department_check.status, part_division_check.status, part_subject_check.status);
    if (department_check.status && division_check.status && subject_check.status && part_department_check.status && part_division_check.status && part_subject_check.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(elective, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "elective update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(elective, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "elective deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
} */

exports.list_values = async (req, res) => {
    let proj, query = {
        'isDeleted': false
    };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else {
            proj = {};
        }

        let doc = await base_control.get_list(elective, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "elective List", elective_formate.elective_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_program = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $match: { 'model': 'elective' } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_administration_department_id', foreignField: '_id', as: 'administration_department' } },
        { $unwind: { path: '$administration_department', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_administration_division_id', foreignField: '_id', as: 'administration_division' } },
        { $unwind: { path: '$administration_division', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_administration_subject_id', foreignField: '_id', as: 'administration_subject' } },
        { $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_participating_department_id', foreignField: '_id', as: 'participating_department' } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_participating_division_id', foreignField: '_id', as: 'participating_division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id), 'model': 'elective' };
    let doc = await base_control.get_aggregate_with_id_match(course, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "elective list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ elective_formate.elective(doc.data));
        // common_files.list_all_response(res, 200, true, "elective list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* elective_formate.elective(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_elective_program_level = async (req, res) => {
    let id = req.params.id;
    // console.log(req.params.level);
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'study_level': parseInt(req.params.level) } },
        { $match: { 'model': 'elective' } },
        { $match: { 'isDeleted': false } },
        { $sort: { updatedAt: -1 } },
        { $project: { _id: 1, courses_name: 1 } }
    ];
    console.log(aggre);
    let doc = await base_control.get_aggregate(course, aggre);
    // console.log(doc);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "elective list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ elective_formate.elective(doc.data));
        common_files.com_response(res, 200, true, "level wise elective list", doc.data /* elective_formate.elective(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.elective_session_order_insert = async (req, res) => {
    let docs = { status: false };
    let status, datas;
    let checks = await base_control.check_id(program, { _id: req.body._program_id, 'isDeleted': false });
    let checks1 = await base_control.check_id(course, { _id: req.body._course_id, 'isDeleted': false });
    // console.log(checks, checks1);
    if (checks.status && checks1.status) {
        await req.body.data.forEach(async (doc, index) => {
            let objects = {
                s_no: doc.s_no,
                delivery_symbol: doc.delivery_symbol,
                delivery_no: doc.delivery_no,
                session_name: doc.session_name,
                contact_hours: doc.contact_hours,
                _course_id: ObjectId(req.body._course_id),
                _program_id: ObjectId(req.body._program_id)
            };
            // console.log(objects);
            if (doc.id == '' && doc.id.length == 0) {
                docs = await base_control.insert(session_order, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            } else {
                docs = await base_control.update(session_order, doc.id, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            }
            if (req.body.data.length == index + 1) {
                if (status) {
                    let aggre = [
                        { $match: { '_course_id': ObjectId(req.body._course_id), '_program_id': ObjectId(req.body._program_id) } },
                        { $match: { 'isDeleted': false } }
                    ];
                    let session_orders_data = await base_control.get_aggregate(session_order, aggre);
                    common_files.com_response(res, 201, true, "session type Added successfully", session_orders_data);
                } else {
                    common_files.com_response(res, 500, false, "Error", docs.data);
                }
            }
        });
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let course_id = ObjectId(req.params.course_id);
    let topic_id = ObjectId(req.params.topic_id);
    let opout = await base_control.update_push_pull(course, course_id, { $pull: { 'elective': { _id: topic_id } } });
    console.log(opout);
    if (opout.status) {
        common_files.com_response(res, 201, true, "Elective topic removed successfully", 'Elective topic removed successfully');
    } else {
        common_files.com_response(res, 500, false, "Error", opout.data);
    }
}