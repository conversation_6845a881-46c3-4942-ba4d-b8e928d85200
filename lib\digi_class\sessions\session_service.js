const moment = require('moment');
const multer = require('multer');
const constant = require('../../utility/constants');
const CourseSchedule = require('mongoose').model(constant.COURSE_SCHEDULE);
const user = require('../../models/user');
const common_files = require('../../utility/common');
const base_control = require('../../base/base_controller');
const { convertToMongoObjectId } = require('../../utility/common');
const {
    createZoomMeeting,
    createZoomUser,
    getZoomUserList,
    getZoomMeetingParticipantsList,
} = require('../../../service/zoom.service');
const { logger } = require('../../utility/util_keys');
const { COMPLETED, ONGOING, TIME_GROUP_BOOKING_TYPE, PENDING } = require('../../utility/constants');
const { timestampNow } = require('../../utility/common_functions');
const { getSchedules } = require('../dashboard/dashboard_service');
const { axiosCall } = require('../../utility/common');
const { dsGetAllWithSortAsJSON } = require('../../base/base_controller');
const { uploadOutsideFile } = require('../../utility/file_upload');
const fileUpload = uploadOutsideFile.fields([{ name: 'file', maxCount: 1 }]);
const { getS3SignedUrl } = require('../../../service/aws.service');
const zoomMeetingCreation = async (req, res) => {
    try {
        const zoomMeetingRequest = {
            zoomUserId: req.zoomUserId,
            topic: req.topic,
            duration: req.duration,
            agenda: req.agenda,
            start_time: req.start_time,
        };
        const createMeeting = await createZoomMeeting(zoomMeetingRequest, res);

        await base_control.update_condition(
            CourseSchedule,
            { _id: req._id },
            {
                $set: {
                    'zoomDetails.meetingUuid': createMeeting.uuid,
                    'zoomDetails.startUrl': createMeeting.start_url,
                    'zoomDetails.joinUrl': createMeeting.join_url,
                },
            },
        );

        return createMeeting;
    } catch (error) {
        throw new Error(error);
    }
};

const zoomMeetingUpdate = async (staffId, courseScheduleId, startTime, endTime) => {
    try {
        const staffDetails = await user.findOne({ _id: staffId }, {});

        const scheduleStartTime = startTime.hour + ':' + startTime.minute + ' ' + startTime.format;
        const sessionStartTime = moment(scheduleStartTime, ['h:mm A']);
        const scheduleEndTime = endTime.hour + ':' + endTime.minute + ' ' + endTime.format;
        const sessionEndTime = moment(scheduleEndTime, ['h:mm A']);
        const difference = moment.duration(sessionEndTime.diff(sessionStartTime));
        const duration = parseInt(difference.asMinutes());

        let meetingDatas;
        let meetingUid;
        if (!staffDetails.zoomDetails.userId) {
            const request = {
                email: staffDetails.email,
                first_name: staffDetails.name.first,
                last_name: staffDetails.name.last,
            };
            const zoomUserResponse = await createZoomUser(request);
            let findUserId;
            if (zoomUserResponse.code === 1005) {
                // if already exists the users
                const getZoomUsers = await getZoomUserList({});

                findUserId = getZoomUsers.users.find(({ email }) => email === staffDetails.email);
                await base_control.update(user, staffDetails._id, {
                    'zoomDetails.userId': findUserId.id,
                });
            }
            let zoomUserId;
            if (zoomUserResponse.id) {
                zoomUserId = zoomUserResponse.id;
            } else {
                zoomUserId = findUserId.id;
            }
            const zoomMeetingRequest = {
                zoomUserId,
                duration,
            };

            const meetingResponse = await createZoomMeeting(zoomMeetingRequest);

            await base_control.update(user, staffDetails._id, {
                'zoomDetails.userId': zoomUserId,
                'zoomDetails.startUrl': meetingResponse.start_url,
                'zoomDetails.joinUrl': meetingResponse.join_url,
                'zoomDetails.meetingId': meetingResponse.id,
                'zoomDetails.meetingUuid': meetingResponse.uuid,
            });
            meetingDatas = {
                'zoomDetails.startUrl': meetingResponse.start_url,
                'zoomDetails.joinUrl': meetingResponse.join_url,
                'zoomDetails.meetingId': meetingResponse.id,
                'zoomDetails.meetingUuid': meetingResponse.uuid,
            };
            meetingUid = meetingResponse.uuid;
        } else {
            const zoomUserId = staffDetails.zoomDetails.userId;
            const zoomMeetingSchedule = {
                zoomUserId,
                duration,
            };
            const meetingResponseForSchedule = await createZoomMeeting(zoomMeetingSchedule);

            meetingDatas = {
                'zoomDetails.startUrl': staffDetails.zoomDetails.startUrl,
                'zoomDetails.joinUrl': staffDetails.zoomDetails.joinUrl,
                'zoomDetails.meetingId': staffDetails.zoomDetails.meetingId,
                'zoomDetails.meetingUuid': meetingResponseForSchedule.uuid,
            };

            meetingUid = meetingResponseForSchedule.uuid;
        }
        await base_control.update(CourseSchedule, courseScheduleId, { zoomUuid: meetingUid });

        return meetingDatas;
    } catch (error) {
        throw new Error(error);
    }
};

const zoomMeetingEnd = async (courseScheduleId) => {
    try {
        const courseDetails = await CourseSchedule.findOne(
            { _id: convertToMongoObjectId(courseScheduleId) },
            {},
        );
        if (courseDetails) {
            const studentIds = courseDetails.students.map((student) => student._id);

            const studentDetails = await user.find({ _id: { $in: studentIds } }, {});

            const participantResponse = await getZoomMeetingParticipantsList({
                meetingId: courseDetails.zoomUuid,
            });

            if (studentDetails && participantResponse.participants) {
                const studentDuration = studentDetails.map((student) => {
                    const getParticipants = participantResponse.participants.find(
                        (element) => element.user_email === student.email,
                    );
                    let duration = 0;
                    if (getParticipants) {
                        duration = getParticipants.duration;
                    }
                    return {
                        updateOne: {
                            filter: {
                                'students._id': student._id,
                            },
                            update: {
                                $set: {
                                    'students.duration': duration,
                                },
                            },
                        },
                    };
                });

                await CourseSchedule.bulkWrite(studentDuration);
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};

const makeTitleAndMessage = (courseSchedule) => {
    try {
        let title = 'Make your Attendance';
        if (courseSchedule.course_code) {
            title += '(' + courseSchedule.course_code + ')';
        }
        let message;
        if (courseSchedule.course_name) {
            message = courseSchedule.course_name + ' : ';
        }
        if (courseSchedule.session && courseSchedule.session.session_type) {
            message +=
                courseSchedule.session.session_type +
                ' : ' +
                courseSchedule.session.delivery_symbol +
                courseSchedule.session.delivery_no;
        } else message += courseSchedule.title + ' : ' + courseSchedule.sub_type;
        if (courseSchedule.infra) message += ' : Room - ' + courseSchedule.infra;
        return { title, message };
    } catch (error) {
        throw new Error(error);
    }
};

const getSchedule = async ({ scheduleId }) => {
    try {
        const query = {
            _id: convertToMongoObjectId(scheduleId),
            isDeleted: false,
            isActive: true,
        };
        return await CourseSchedule.findOne(query);
    } catch (error) {
        throw new Error(error);
    }
};

const sendResponse = (res, code, status, message, data) => {
    return res.status(code).send(common_files.response_function(res, code, status, message, data));
};

const updateWithFilter = base_control.update_condition_array_filter;

const endSession = async (_id) => {
    try {
        const query = {
            _id,
            isActive: true,
            isDeleted: false,
        };
        await zoomMeetingEnd(_id);
        let userIds = [];
        const courseSchedule = await CourseSchedule.findOne(query, {});
        if (!courseSchedule)
            return logger.info('There is no session found while end session ' + _id.toString());
        const { merge_status, merge_with, mode, sessionDetail, status } = courseSchedule;
        const { students, staffs } = courseSchedule;
        if (mode !== TIME_GROUP_BOOKING_TYPE.REMOTE && sessionDetail.attendance_mode === ONGOING)
            return logger.info("Attendance is ongoing, you can`t' stop" + _id.toString());
        if (status === PENDING) logger.info('Session not yet start');
        const socketIds = [];
        if (!merge_status) {
            students.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                students[index].status = status;
            });
            staffs.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                staffs[index].status = status;
            });
            const studentIds = students.map((student) => student._id.toString());
            const staffIds = staffs.map((staff) => staff._staff_id.toString());
            userIds = studentIds.concat(staffIds);
            const objs = {
                $set: {
                    'sessionDetail.attendance_mode': COMPLETED,
                    'sessionDetail.stop_time': timestampNow(),
                    students,
                    staffs,
                    status: COMPLETED,
                },
            };
            userIds = userIds.map((userId) => convertToMongoObjectId(userId));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    socketEventId: 1,
                },
            );
            const users = userResult.data;
            if (users.length) {
                const sendSocketData = [];
                for (const user of users) {
                    courseSchedule.status = COMPLETED;
                    const data = JSON.stringify({ notificationCount, sessions: courseSchedule });
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            const filter = { arrayFilters: [{ 'sessionDetails.attendance_mode': ONGOING }] };
            const doc = await updateWithFilter(CourseSchedule, query, objs, filter);
            if (doc.status) return logger.info('Session completed successfully');
            return logger.error('Unable to end session #####');
        }
        if (merge_status) {
            const scheduleIds = merge_with.map((mw) => convertToMongoObjectId(mw.schedule_id));
            scheduleIds.push(convertToMongoObjectId(_id));
            query._id = { $in: scheduleIds };
            const CourseSchedules = (await dsGetAllWithSortAsJSON(CourseSchedule, query, {})).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            CourseSchedules.forEach((cSchedule) => {
                const { _id, staffs, students } = cSchedule;
                students.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    students[index].status = status;
                });
                staffs.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    staffs[index].status = status;
                });
                const studentIds = students.map((student) => student._id.toString());
                const staffIds = staffs.map((staff) => staff._staff_id.toString());
                userIds.push(studentIds.concat(staffIds));
                bulk.find({ _id: convertToMongoObjectId(_id) }).updateOne({
                    $set: {
                        'sessionDetail.attendance_mode': COMPLETED,
                        'sessionDetail.stop_time': timestampNow(),
                        students,
                        staffs,
                        status: COMPLETED,
                    },
                });
            });
            // eslint-disable-next-line prefer-spread
            userIds = [].concat.apply([], userIds);
            userIds = userIds.map((userId) => convertToMongoObjectId(userId));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                { socketEventId: 1 },
            );
            const users = userResult.data;
            if (users.length) {
                const sendSocketData = [];
                for (const user of users) {
                    courseSchedule.status = COMPLETED;
                    const data = JSON.stringify({ sessions: courseSchedule });
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            bulk.execute((error) => {
                if (!error) return logger.info('Session completed successfully');
                return logger.error('Unable to End Session ' + error.toString());
            });
        }
    } catch (error) {
        throw new Error(error);
    }
};
const uploadDocument = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
        }
        next();
    });
};
const getOutSideSignedUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getS3SignedUrl({
        bucket: process.env.AWS_BUCKET_OUTSIDE_CAMPUS,
        key: `outside/` + fileName,
    });
    return signedUrl;
};

module.exports = {
    sendResponse,
    getSchedule,
    updateWithFilter,
    zoomMeetingCreation,
    zoomMeetingUpdate,
    zoomMeetingEnd,
    makeTitleAndMessage,
    endSession,
    uploadDocument,
    getOutSideSignedUrl,
};
