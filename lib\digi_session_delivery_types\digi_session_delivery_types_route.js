const express = require('express');
const route = express.Router();
const session_delivery_types = require('./digi_session_delivery_types_controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
    defaultProgramInputPolicy,
} = require('../../middleware/policy.middleware');
const validator = require('./digi_session_delivery_types_validator');

//Session Types
route.post(
    '/import_session_type',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    session_delivery_types.import_session_type,
);
route.post(
    '/data_check_import_session_type',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    session_delivery_types.data_check_session_type,
);
route.post(
    '/import_delivery_type',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    session_delivery_types.import_delivery_type,
);
route.post(
    '/data_check_delivery_type',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    session_delivery_types.data_check_delivery_type,
);
route.post(
    '/',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.insert,
    session_delivery_types.insert,
);
route.put('/:id', validator.update, session_delivery_types.update_session_type);
route.get('/delivery_list', validator.program_id, session_delivery_types.delivery_list);
route.get(
    '/:program_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    validator.program_id,
    session_delivery_types.list,
);
route.delete('/:id', validator.id, session_delivery_types.delete_session_type);

//Delivery Types
route.post(
    '/add_delivery_type/:session_type_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.VIEW])],
    validator.insert_delivery_type,
    session_delivery_types.insert_delivery_type,
);
route.put(
    '/update_delivery_type/:session_type_id/:delivery_type_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.session_type_id,
    validator.delivery_type_id,
    validator.insert_delivery_type,
    session_delivery_types.update_delivery_type,
);
route.delete(
    '/delete_delivery_type/:session_type_id/:delivery_type_id',
    [userPolicyAuthentication([...defaultProgramInputPolicy.EDIT])],
    validator.session_type_id,
    validator.delivery_type_id,
    session_delivery_types.delete_delivery_type,
);

route.post(
    '/sessionTypeDeliveryCourseChange',
    session_delivery_types.sessionTypeDeliveryCourseChange,
);
module.exports = route;
