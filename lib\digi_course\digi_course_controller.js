const constant = require('../utility/constants');
const digi_course = require('mongoose').model(constant.DIGI_COURSE);
const digi_course_group = require('mongoose').model(constant.DIGI_COURSE_GROUP);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const digi_curriculum = require('mongoose').model(constant.DIGI_CURRICULUM);
const digi_session_delivery_types = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const digi_session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
const department_subject = require('mongoose').model(constant.DIGI_DEPARTMENT_SUBJECT);
const institution = require('mongoose').model(constant.INSTITUTION);
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
const course_schedule = require('mongoose').model(constant.COURSE_SCHEDULE);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const {
    allCourseList,
    clearItem,
    allProgramCalendarDatas,
    allSessionOrderDatas,
} = require('../../service/cache.service');
const { updateStudentGroupFlatCacheData } = require('../student_group/student_group_services');
const { convertToMongoObjectId, clone } = require('../utility/common');
const { changeCourseName } = require('../lmsWarning/lmsWarning.controller');
const { getSignedURL } = require('../utility/common_functions');
const { updateStudentGroupRedisKey } = require('../utility/utility.service');
// Updating Course Flat Caching Data
const updateCourseFlatCacheData = async () => {
    clearItem('allCourses');
    await allCourseList();
};

// Updating Program Calendar Flat Caching Data
const updateProgramCalendarFlatCacheData = async () => {
    clearItem('allProgramCalendar');
    await allProgramCalendarDatas();
};

// Updating Session Flow Flat Caching Data
const updateSessionFlowFlatCacheData = async () => {
    clearItem('allSessionOrders');
    await allSessionOrderDatas();
};

async function insert(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //check duplicate
        if (req.body.course_code && req.body.course_code.length != 0) {
            const course_data = await base_control.get(
                digi_course,
                {
                    _program_id: convertToMongoObjectId(req.body._program_id),
                    _curriculum_id: convertToMongoObjectId(req.body._curriculum_id),
                    course_code: req.body.course_code,
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            );
            if (course_data.status)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            true,
                            req.t('DUPLICATE_COURSE_CODE'),
                            [],
                        ),
                    );
        }
        req.body._institution_id = req.headers._institution_id;
        // set curriculum framework to course start
        const curriculumQuery = { _id: ObjectId(req.body._curriculum_id) };
        const curriculumData = await digi_curriculum
            .findOne(curriculumQuery, {
                framework: 1,
                curriculum_name: 1,
                _program_id: 1,
                program_name: 1,
            })
            .lean();
        const courseAssignedLevels =
            req.body.course_type === 'standard'
                ? req.body.course_occurring
                : req.body.course_type === 'selective'
                ? req.body.course_recurring
                : [];
        const courseAssignedDetails = [];
        if (courseAssignedLevels.length) {
            for (const courseAssignedLevelElement of courseAssignedLevels) {
                courseAssignedDetails.push({
                    _curriculum_id: curriculumData._id,
                    curriculum_name: curriculumData.curriculum_name,
                    _program_id: curriculumData._program_id,
                    program_name: curriculumData.program_name,
                    _year_id: courseAssignedLevelElement._year_id,
                    year: courseAssignedLevelElement.year_no,
                    _level_id: courseAssignedLevelElement._level_id,
                    level_no: courseAssignedLevelElement.level_no,
                });
            }
        }
        req.body.course_assigned_details = courseAssignedDetails;
        req.body.framework =
            curriculumData && curriculumData.framework ? curriculumData.framework : { domains: [] };
        // set curriculum framework to course end
        const doc = await base_control.insert(digi_course, req.body);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('COURSE_ADDED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ADD_COURSE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function update(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        /* let course_data = await base_control.get(digi_course, { _id: ObjectId(req.params._id), _program_id: ObjectId(req.params.program_id) }, {});
            if (!course_data.status) return res.status(404).send(common_files.response_function(res, 404, false, "Course data not found", 'Course data not found')); */

        delete req.body.course_type;
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(digi_course, query, req.body);
        updateCourseFlatCacheData();
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('UNABLE_TO_UPDATE_COURSE'),
                        [],
                    ),
                );

        //Need to change Program calendar course name too
        if (
            (req.body.course_name && req.body.course_name.length != 0) ||
            (req.body.course_code && req.body.course_code.length != 0)
        ) {
            const program_calendar_data = await base_control.get_list(
                program_calendar,
                { 'level.course._course_id': ObjectId(req.params.id) },
                {},
            );
            if (program_calendar_data.status) {
                const bulk_data = [];
                for (pc_element of program_calendar_data.data) {
                    for (level_element of pc_element.level) {
                        for (course_element of level_element.course)
                            if (course_element._course_id.toString() === req.params.id.toString())
                                bulk_data.push({
                                    updateOne: {
                                        filter: {
                                            _id: ObjectId(pc_element._id),
                                        },
                                        update: {
                                            $set: {
                                                'level.$[i].course.$[j].courses_name':
                                                    req.body.course_name,
                                                'level.$[i].course.$[j].courses_number':
                                                    req.body.course_code,
                                            },
                                        },
                                        arrayFilters: [
                                            { 'i._id': level_element._id },
                                            { 'j._id': course_element._id },
                                        ],
                                    },
                                });
                    }
                }
                const changes = await base_control.bulk_write(program_calendar, bulk_data);
                updateProgramCalendarFlatCacheData();
                if (!changes.status)
                    console.log('Unable to change course name in program calendar');
                console.log('Course name changed in program calendar');
            }
            // Student Group Data update
            const studentGroupData = await base_control.get_list(
                student_group,
                { 'groups.courses._course_id': ObjectId(req.params.id) },
                {},
            );
            if (studentGroupData.status) {
                const bulk_data = [];
                for (sgElement of studentGroupData.data) {
                    for (groupsElement of sgElement.groups) {
                        for (courseElement of groupsElement.courses)
                            if (courseElement._course_id.toString() === req.params.id.toString())
                                bulk_data.push({
                                    updateOne: {
                                        filter: {
                                            _id: ObjectId(sgElement._id),
                                        },
                                        update: {
                                            $set: {
                                                'groups.$[i].courses.$[j].course_name':
                                                    req.body.course_name,
                                                'groups.$[i].courses.$[j].course_no':
                                                    req.body.course_code,
                                            },
                                        },
                                        arrayFilters: [
                                            { 'i._id': groupsElement._id },
                                            { 'j._id': courseElement._id },
                                        ],
                                    },
                                });
                    }
                }
                const changes = await base_control.bulk_write(student_group, bulk_data);
                updateStudentGroupFlatCacheData();
                await updateStudentGroupRedisKey({
                    courseId: req.params.id,
                });
                if (!changes.status) console.log('Unable to change course name in student group');
                console.log('Course name changed in student group');
            }
            // Course Schedule Update
            console.log(
                await base_control.update_push_pull_many(
                    course_schedule,
                    { _course_id: ObjectId(req.params.id) },
                    {
                        $set: {
                            course_name: req.body.course_name,
                            course_code: req.body.course_code,
                        },
                    },
                ),
            );
            await changeCourseName({
                courseId: ObjectId(req.params.id),
                course_name: req.body.course_name,
                course_code: req.body.course_code,
            });
        }
        return res
            .status(201)
            .send(
                common_files.response_function(
                    res,
                    201,
                    true,
                    req.t('COURSE_UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function delete_course(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const obj = { isDeleted: true };
        const doc = await base_control.update(digi_course, query, obj);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('COURSE_DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_DELETE_COURSE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function list(req, res) {
    try {
        const query = { isDeleted: false };
        const doc = await base_control.get_list(digi_course, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSES_LIST'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function program_course_list(req, res) {
    try {
        const query = {
            isDeleted: false,
            $or: [
                {
                    _program_id: convertToMongoObjectId(req.params.program_id),
                    _curriculum_id: convertToMongoObjectId(req.params.curriculum_id),
                },
                {
                    'course_assigned_details.course_shared_with._program_id':
                        convertToMongoObjectId(req.params.program_id),
                    'course_assigned_details.course_shared_with._curriculum_id':
                        convertToMongoObjectId(req.params.curriculum_id),
                },
            ],
        };
        const doc = await base_control.get_list(digi_course, query, {
            course_code: 1,
            course_name: 1,
            _program_id: 1,
            _curriculum_id: 1,
            duration: 1,
            course_type: 1,
            'course_assigned_details.level_no': 1,
            'course_assigned_details.course_shared_with._program_id': 1,
            'course_assigned_details.course_shared_with.level_no': 1,
            'course_assigned_details.course_shared_with._curriculum_id': 1,
            course_occurrence: 1,
            course_recurring: 1,
            versionNo: 1,
            versioned: 1,
            versioning: 1,
            isDeleted: 1,
            isActive: 1,
        });
        const course_data = [];
        const courseVersioned = [];

        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(res, 200, false, req.t('COURSE_NOT_FOUND'), []),
                );
        for (element of doc.data) {
            if (
                element._program_id.toString() == req.params.program_id.toString() &&
                element._curriculum_id.toString() == req.params.curriculum_id.toString()
            ) {
                if (element.versioned)
                    courseVersioned.push({ ...element.toObject(), shared_with_others: false });
                else course_data.push({ ...element.toObject(), shared_with_others: false });
            } else {
                for (sub_element of element.course_assigned_details) {
                    // console.log(sub_element.course_shared_with)
                    // console.log(sub_element.course_shared_with.findIndex(i => ((i._program_id).toString() == (req.params.program_id).toString() && (i._curriculum_id).toString() == (req.params.curriculum_id).toString())))
                    if (
                        sub_element.course_shared_with.findIndex(
                            (i) =>
                                i._program_id.toString() == req.params.program_id.toString() &&
                                i._curriculum_id.toString() == req.params.curriculum_id.toString(),
                        ) != -1
                    )
                        if (element.versioned)
                            courseVersioned.push({
                                ...element.toObject(),
                                shared_with_others: false,
                            });
                        else
                            course_data.push({
                                ...element.toObject(),
                                shared_with_others: true,
                            });
                }
            }
        }
        for (courseVersionElement of courseVersioned) {
            if (
                !course_data.find(
                    (courseElement) =>
                        courseElement._id.toString() === courseVersionElement._id.toString() ||
                        (courseElement.versioned &&
                            courseElement.versioned === true &&
                            courseElement.versioning.find(
                                (versionElement) =>
                                    versionElement._course_id.toString() ===
                                    courseVersionElement._id.toString(),
                            )),
                )
            )
                course_data.push({
                    ...courseVersionElement /* .toObject() */,
                    shared_with_others: false,
                });
        }
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('COURSE_FOUND'), course_data),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function insert_session_flow(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        req.body._institution_id = req.headers._institution_id;
        const doc = await base_control.insert(digi_session_order, req.body);
        updateSessionFlowFlatCacheData();
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('UNABLE_TO_ADD_SESSION_FLOW'),
                        [],
                    ),
                );
        if (req.body.isActive != undefined && req.body.isActive === false) {
            await base_control.update(digi_course, ObjectId(req.body._course_id), {
                isActive: false,
            });
        }
        updateCourseFlatCacheData();
        return res
            .status(201)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    201,
                    true,
                    req.t('SESSION_FLOW_ADDED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function update_session_flow(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        // max hours update
        if (req.body.max_hours != undefined && req.body.max_hours !== '') {
            const query = { _id: ObjectId(req.params.id) };
            await base_control.update(digi_session_order, query, {
                max_hours: Number(req.body.max_hours),
            });
        }

        //Course schedule list
        const courseScheduleList = await base_control.get_list(
            course_schedule,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                _program_id: ObjectId(req.body._program_id),
                _course_id: ObjectId(req.body._course_id),
                isDeleted: false,
            },
            { _id: 1, session: 1 },
        );
        if (!courseScheduleList.status) courseScheduleList.data = [];

        //Session Order
        const session_order = await base_control.get(
            digi_session_order,
            { _id: ObjectId(req.params.id) },
            {},
        );
        if (!session_order.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('SESSION_FLOW_NOT_FOUND'),
                        req.t('SESSION_FLOW_NOT_FOUND'),
                    ),
                );

        const bulk = [];
        const cs_bulk = [];
        const sess_flow = [];
        let session_status_change = 0;
        for (session_flow_data of req.body.session_flow_data) {
            if (session_flow_data._id) {
                //Check in Course schedule already session is scheduled, if session scheduled, we should not allow to changes subject
                const session_data = session_order.data.session_flow_data.find(
                    (eleSO) => eleSO._id.toString() === session_flow_data._id.toString(),
                );
                //Course schedule check
                const course_schedule_filtered_data = courseScheduleList.data.filter(
                    (eleCSL) =>
                        eleCSL._id &&
                        eleCSL.session._session_id &&
                        eleCSL.session._session_id.toString() === session_flow_data._id.toString(),
                );
                //console.log('Course schedule check: ', csInd);

                if (course_schedule_filtered_data.length > 0) {
                    //After Course Scheduled. Subject, Delivery type, We should not let them to change
                    //Subjects
                    if (session_data.subjects.length === session_flow_data.subjects.length) {
                        let i = 0;
                        session_flow_data.subjects.forEach((eleSubject) => {
                            const subjInd = session_data.subjects.findIndex(
                                (ele) =>
                                    ele._subject_id.toString() ===
                                    eleSubject._subject_id.toString(),
                            );
                            if (subjInd != -1) i++;
                        });
                        if (session_data.subjects.length != i) {
                            session_status_change = 1;
                            continue; //If he is updating the same subject no issue, if he change the subject, we should not let them do it
                        }
                    } else {
                        session_status_change = 1;
                        continue; //If he is updating the same subject no issue, if he change the subject, we should not let them do it
                    }

                    //Delivery Type
                    if (
                        session_data.delivery_type.toString() !=
                        session_flow_data.delivery_type.toString()
                    ) {
                        session_status_change = 1;
                        continue;
                    }

                    //If delivery topic change We have to update in Course schedule
                    for (ele_course_schedule_filtered_data of course_schedule_filtered_data) {
                        cs_bulk.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(ele_course_schedule_filtered_data._id),
                                },
                                update: {
                                    $set: {
                                        'session.session_topic': session_flow_data.delivery_topic,
                                    },
                                },
                            },
                        });
                    }
                }
                /////

                //if week present we have to update. week field will not present in edit course --> session flow and edit course--> sessionflow
                //Week will come in session flow edit. week will not come in edit course sessionflow
                if (session_flow_data.week) {
                    bulk.push({
                        updateOne: {
                            filter: {
                                _id: ObjectId(req.params.id),
                                'session_flow_data._id': ObjectId(session_flow_data._id),
                            },
                            update: {
                                $set: {
                                    'session_flow_data.$._session_id':
                                        session_flow_data._session_id,
                                    'session_flow_data.$.s_no': session_flow_data.s_no,
                                    'session_flow_data.$.delivery_type':
                                        session_flow_data.delivery_type,
                                    'session_flow_data.$._delivery_id':
                                        session_flow_data._delivery_id,
                                    'session_flow_data.$.delivery_symbol':
                                        session_flow_data.delivery_symbol,
                                    'session_flow_data.$.delivery_no':
                                        session_flow_data.delivery_no,
                                    'session_flow_data.$.delivery_topic':
                                        session_flow_data.delivery_topic,
                                    'session_flow_data.$.subjects': session_flow_data.subjects,
                                    'session_flow_data.$.duration': session_flow_data.duration,
                                    'session_flow_data.$.week': session_flow_data.week,
                                    'session_flow_data.$._module_id': session_flow_data._module_id,
                                    'session_flow_data.$.sessionData':
                                        session_flow_data.sessionData,
                                    'session_flow_data.$.sessionDocumentDetails':
                                        session_flow_data.sessionDocumentDetails,
                                    ...(typeof session_flow_data.weekNo === 'number' && {
                                        'session_flow_data.$.weekNo': session_flow_data.weekNo,
                                    }),
                                },
                                upsert: true,
                            },
                        },
                    });
                } else {
                    bulk.push({
                        updateOne: {
                            filter: {
                                _id: ObjectId(req.params.id),
                                'session_flow_data._id': ObjectId(session_flow_data._id),
                            },
                            update: {
                                $set: {
                                    'session_flow_data.$._session_id':
                                        session_flow_data._session_id,
                                    'session_flow_data.$.s_no': session_flow_data.s_no,
                                    'session_flow_data.$.delivery_type':
                                        session_flow_data.delivery_type,
                                    'session_flow_data.$._delivery_id':
                                        session_flow_data._delivery_id,
                                    'session_flow_data.$.delivery_symbol':
                                        session_flow_data.delivery_symbol,
                                    'session_flow_data.$.delivery_no':
                                        session_flow_data.delivery_no,
                                    'session_flow_data.$.delivery_topic':
                                        session_flow_data.delivery_topic,
                                    'session_flow_data.$.subjects': session_flow_data.subjects,
                                    'session_flow_data.$.duration': session_flow_data.duration,
                                    'session_flow_data.$._module_id': session_flow_data._module_id,
                                    'session_flow_data.$.sessionData':
                                        session_flow_data.sessionData,
                                    'session_flow_data.$.sessionDocumentDetails':
                                        session_flow_data.sessionDocumentDetails,
                                },
                                upsert: true,
                            },
                        },
                    });
                }
            } else {
                sess_flow.push(session_flow_data);
            }
        }
        //additional session flow data update
        bulk.push({
            updateOne: {
                filter: {
                    _id: ObjectId(req.params.id),
                },
                update: {
                    $set: { additional_session_flow_data: req.body.additional_session_flow_data },
                },
            },
        });
        //return res.send(bulk);
        //Updating existing data
        if (bulk.length > 0) doc = await digi_session_order.bulkWrite(bulk);
        //Update session topic in Course schedule
        //console.log(cs_bulk);
        if (cs_bulk.length > 0) doc = await course_schedule.bulkWrite(cs_bulk);

        //Pushing new data
        if (sess_flow.length > 0) {
            const query = { _id: ObjectId(req.params.id) };
            await base_control.update_condition(digi_session_order, query, {
                $addToSet: {
                    session_flow_data: sess_flow,
                },
            });
        }
        //Pulling data
        if (req.body.session_flow_deleted_ids.length > 0) {
            const query = { _id: ObjectId(req.params.id) };
            await base_control.update_condition(digi_session_order, query, {
                $pull: {
                    session_flow_data: { _id: { $in: req.body.session_flow_deleted_ids } },
                },
            });
        }
        updateSessionFlowFlatCacheData();
        let message = '';
        let edit_status = 0;
        if (session_status_change == 1) {
            message = req.t('COURSE_IS_SCHEDULED_YOU_CAN_NOT_CHANGE_THE_SUBJECT_DELIVERY_TYPE');
        } else {
            edit_status = 1;
            message = req.t('UPDATED_SUCCESSFULLY');
        }

        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(req, 200, true, message, { edit_status }),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function list_id(req, res) {
    try {
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.get(digi_course, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Not found', []));
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_DETAILS'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function list_id_session_flow(req, res) {
    try {
        const query = { ...common_files.query, _course_id: ObjectId(req.params.course_id) };
        const doc = await base_control.get(digi_session_order, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('NOT_FOUND'),
                        [],
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('SESSION_FLOW_DETAILS'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function program_level_list(req, res) {
    try {
        const query = { _program_id: ObjectId(req.params.program_id) };
        const doc = await base_control.get_list(digi_curriculum, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        //let year_level_data = doc.data.year_level
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_LEVEL_LIST'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function program_curriculum_year_level_list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const aggre = [
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                },
            },
            {
                $lookup: {
                    from: 'digi_curriculums',
                    localField: '_id',
                    foreignField: '_program_id',
                    as: 'curriculum_data',
                },
            },
        ];

        const doc = await base_control.get_aggregate(program, aggre);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));

        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_CURRICULUM_YEAR_LEVEL_LIST'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function program_session_delivery_type_list(req, res) {
    try {
        const query = {
            _program_id: ObjectId(req.params.program_id),
            isDeleted: false,
        };
        const doc = await base_control.get_list(digi_session_delivery_types, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));

        const session_delivery_types_arr = [];
        for (let i = 0; i < doc.data.length; i++) {
            const delivery_type_list = doc.data[i].delivery_types.filter(
                (ele) => ele.isDeleted == false,
            );
            const obj = {
                _id: doc.data[i]._id,
                isActive: doc.data[i].isActive,
                isDeleted: doc.data[i].isDeleted,
                _program_id: doc.data[i]._program_id,
                program_name: doc.data[i].program_name,
                session_name: doc.data[i].session_name,
                session_symbol: doc.data[i].session_symbol,
                contact_hour_per_credit_hour: doc.data[i].contact_hour_per_credit_hour,
                session_duration: doc.data[i].session_duration,
                delivery_types: delivery_type_list,
                createdAt: doc.data[i].createdAt,
                updatedAt: doc.data[i].updatedAt,
            };
            session_delivery_types_arr.push(obj);
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('PROGRAM_SESSION_AND_DELIVERY_TYPE_LIST'),
                    session_delivery_types_arr,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function insert_draft(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //check duplicate
        const course_data = await base_control.get(
            digi_course,
            {
                _program_id: convertToMongoObjectId(req.body._program_id),
                course_code: req.body.course_code,
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (course_data.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        true,
                        req.t('DUPLICATE_COURSE_CODE'),
                        [],
                    ),
                );

        const doc = await base_control.insert(digi_course, req.body);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('COURSE_DRAFTED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_DRAFT_COURSE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function assign_course(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Level duration validation
        const curriculum_check = await base_control.get(
            digi_curriculum,
            { _id: ObjectId(req.body.course_assigned_details._curriculum_id) },
            { year_level: 1 },
        );
        if (!curriculum_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );

        const year_ind = curriculum_check.data.year_level.findIndex(
            (i) => i._id.toString() == req.body.course_assigned_details._year_id.toString(),
        );
        if (year_ind == -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('YEAR_NOT_FOUND'),
                        req.t('YEAR_NOT_FOUND'),
                    ),
                );

        const level_ind = curriculum_check.data.year_level[year_ind].levels.findIndex(
            (i) => i._id.toString() == req.body.course_assigned_details._level_id.toString(),
        );
        if (level_ind == -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('LEVEL_NOT_FOUND'),
                        req.t('LEVEL_NOT_FOUND'),
                    ),
                );

        const level_start = parseInt(
            curriculum_check.data.year_level[year_ind].levels[level_ind].start_week,
        );
        const level_end = parseInt(
            curriculum_check.data.year_level[year_ind].levels[level_ind].end_week,
        );
        const course_start = parseInt(req.body.course_assigned_details.course_duration.start_week);
        const course_end = parseInt(req.body.course_assigned_details.course_duration.end_week);
        if (
            !(
                (course_start - level_start) * (course_start - level_end) <= 0 &&
                (course_end - level_start) * (course_end - level_end) <= 0
            )
        )
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('COURSE_START_END_WEEK_IS_NOT_IN_BETWEEN_OF_LEVEL_START_END_WEEK'),
                        req.t('COURSE_START_END_WEEK_IS_NOT_IN_BETWEEN_OF_LEVEL_START_END_WEEK'),
                    ),
                );
        const courseAssignedDetailQuery = {
            _id: ObjectId(req.params.id),
        };
        let arrayFilters = [];
        let updateQuery = {};
        if (req.body.course_assigned_details.isManualAssign === true) {
            updateQuery = {
                $push: { course_assigned_details: req.body.course_assigned_details },
            };
        } else {
            updateQuery = {
                $set: {
                    'course_assigned_details.$[index].isConfigured':
                        req.body.course_assigned_details.isConfigured,
                    'course_assigned_details.$[index].course_duration':
                        req.body.course_assigned_details.course_duration,
                    'course_assigned_details.$[index].course_shared_with':
                        req.body.course_assigned_details.course_shared_with,
                },
            };
            arrayFilters = [
                {
                    'index._level_id': ObjectId(req.body.course_assigned_details._level_id),
                    'index._year_id': ObjectId(req.body.course_assigned_details._year_id),
                },
            ];
        }
        const { framework } = curriculum_check.data;
        if (framework && framework.name) {
            updateQuery.$set.framework = { framework };
        }
        const updatedResult = await digi_course.updateOne(courseAssignedDetailQuery, updateQuery, {
            arrayFilters,
        });
        updateCourseFlatCacheData();
        if (updatedResult.modifiedCount)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('COURSE_ASSIGNED_SUCCESSFULLY'),
                        [],
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ASSIGN_COURSE'),
                    [],
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function assign_course_edit(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const obj = {
            $set: {
                'course_assigned_details.$[i]._program_id': req.body._program_id,
                'course_assigned_details.$[i].program_name': req.body.program_name,
                'course_assigned_details.$[i]._curriculum_id': req.body._curriculum_id,
                'course_assigned_details.$[i].curriculum_name': req.body.curriculum_name,
                'course_assigned_details.$[i]._year_id': req.body._year_id,
                'course_assigned_details.$[i].year': req.body.year,
                'course_assigned_details.$[i]._level_id': req.body._level_id,
                'course_assigned_details.$[i].level_no': req.body.level_no,
                'course_assigned_details.$[i].course_duration': req.body.course_duration,
                'course_assigned_details.$[i].course_shared_with': req.body.course_shared_with,
                'course_assigned_details.$[i].isConfigured': req.body.isConfigured,
            },
        };
        const filter = {
            arrayFilters: [{ 'i._id': req.params.assigned_course_details_id }],
        };
        console.log(obj);
        doc = await base_control.update_condition_array_filter(digi_course, query, obj, filter);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('COURSE_ASSIGN_EDITED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_EDIT_COURSE_ASSIGN'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function assign_course_delete(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        // Need to check Content Mapping before unassign course
        const course_check = await base_control.get(
            digi_course,
            { _id: ObjectId(req.params.id) },
            {},
        );
        if (!course_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        req.t('COURSE_NOT_FOUND'),
                    ),
                );
        let clo_datas = [];
        if (course_check.data.framework && course_check.data.framework.domains) {
            for (domain_element of course_check.data.framework.domains) {
                clo_datas = clo_datas.concat(
                    domain_element.clo.filter((i) => i.isDeleted === false),
                );
            }
        }
        if (clo_datas.length !== 0)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('COURSE_ASSIGNED_TO_FRAMEWORK_CAN_NOT_BE_DELETED'),
                        req.t('COURSE_ASSIGNED_TO_FRAMEWORK_CAN_NOT_BE_DELETED'),
                    ),
                );
        const obj = {
            $pull: {
                course_assigned_details: {
                    _id: ObjectId(req.params.assigned_course_details_id),
                },
            },
        };
        const doc = await base_control.update_condition(
            digi_course,
            { _id: ObjectId(req.params.id) },
            obj,
        );
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('COURSE_DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_DELETE_COURSE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function course_assigned_list(req, res) {
    try {
        const query_CG = { isActive: true };
        const course_group_data = await base_control.get_list(digi_course_group, query_CG, {});
        let cg_data = [];
        const grouped_data = [];
        const ungrouped_data = [];
        if (course_group_data.status) cg_data = course_group_data.data;
        const curriculum_data = await base_control.get_list(
            digi_curriculum,
            {
                /* _program_id: ObjectId(req.params.program_id), _id: ObjectId(req.params.curriculum_id), */ isDeleted: false,
            },
            { _program_id: 1, year_level: 1 },
        );
        if (!curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );
        curriculum_data.data = clone(curriculum_data.data);
        const current_loc = curriculum_data.data.findIndex(
            (i) =>
                i._program_id.toString() == ObjectId(req.params.program_id).toString() &&
                i._id.toString() == ObjectId(req.params.curriculum_id),
        );
        if (current_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );
        const year_loc = curriculum_data.data[current_loc].year_level.findIndex(
            (i) => i._id.toString() == req.params.year_id.toString(),
        );
        if (year_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_YEAR_NOT_FOUND'),
                        req.t('CURRICULUM_YEAR_NOT_FOUND'),
                    ),
                );
        const level_loc = curriculum_data.data[current_loc].year_level[year_loc].levels.findIndex(
            (i) => i._id.toString() == req.params.level_id.toString(),
        );
        if (level_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_LEVEL_NOT_FOUND'),
                        req.t('CURRICULUM_LEVEL_NOT_FOUND'),
                    ),
                );
        const query = {
            course_assigned_details: { $exists: true },
            isDeleted: false,
            isActive: true,
            $or: [
                {
                    _program_id: convertToMongoObjectId(req.params.program_id),
                    _curriculum_id: convertToMongoObjectId(req.params.curriculum_id),
                },
                {
                    'course_assigned_details.course_shared_with._program_id':
                        convertToMongoObjectId(req.params.program_id),
                    'course_assigned_details.course_shared_with._curriculum_id':
                        convertToMongoObjectId(req.params.curriculum_id),
                },
                {
                    ...(curriculum_data.data[current_loc].year_level[year_loc] &&
                        curriculum_data.data[current_loc].year_level[year_loc]._pre_requisite_id &&
                        curriculum_data.data[current_loc].year_level[year_loc]._pre_requisite_id
                            .length && {
                            _curriculum_id: convertToMongoObjectId(
                                curriculum_data.data[current_loc].year_level[year_loc]
                                    ._pre_requisite_id,
                            ),
                        }),
                },
            ],
        };
        const doc = await digi_course
            .find(query, {
                course_name: 1,
                course_type: 1,
                course_code: 1,
                credit_hours: 1,
                course_duration: 1,
                isDeleted: 1,
                isActive: 1,
                course_assigned_details: 1,
                // versioning: 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            })
            .lean();
        if (!doc || !doc.length)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        [],
                    ),
                );
        const assigned_course_data = [];
        let program_id = ObjectId(req.params.program_id);
        let curriculum_id = ObjectId(req.params.curriculum_id);
        let year_id = ObjectId(req.params.year_id);
        let level_id = ObjectId(req.params.level_id);
        if (curriculum_data.data[current_loc].year_level[year_loc]._pre_requisite_id) {
            const loc = curriculum_data.data.findIndex(
                (i) =>
                    i._id.toString() ==
                    curriculum_data.data[current_loc].year_level[
                        year_loc
                    ]._pre_requisite_id.toString(),
            );
            program_id = ObjectId(curriculum_data.data[loc]._program_id);
            curriculum_id = ObjectId(
                curriculum_data.data[current_loc].year_level[year_loc]._pre_requisite_id,
            );
            year_id = ObjectId(curriculum_data.data[loc].year_level[year_loc]._id);
            level_id = ObjectId(
                curriculum_data.data[loc].year_level[year_loc].levels[level_loc]._id,
            );
        }
        // console.log('Program ', program_id, '\n', 'Curriculum ', curriculum_id, '\n', 'Year ', year_id, '\n', 'Level ', level_id)
        // return res.send(doc.data)

        // Course Versioned Data Filter
        // const courseVersioned = [];
        // for (element of doc.data) {
        //     if (element.versioned) courseVersioned.push({ ...element.toObject() });
        //     else courseList.push(element);
        // }
        // for (courseVersionElement of courseVersioned) {
        //     if (
        //         !courseList.find(
        //             (courseElement) =>
        //                 courseElement._id.toString() === courseVersionElement._id.toString() ||
        //                 (courseElement.versioned &&
        //                     courseElement.versioned === true &&
        //                     courseElement.versioning.find(
        //                         (versionElement) =>
        //                             versionElement._course_id.toString() ===
        //                             courseVersionElement._id.toString(),
        //                     )),
        //         )
        //     )
        //         courseList.push({
        //             ...courseVersionElement /* .toObject() */,
        //         });
        // }
        const courseList = [...doc];
        for (element of courseList) {
            const data = element.course_assigned_details.filter(
                (ele) =>
                    ele._program_id &&
                    ele._curriculum_id &&
                    ele._year_id &&
                    ele._level_id &&
                    ele._program_id.toString() == program_id.toString() &&
                    ele._curriculum_id.toString() == curriculum_id.toString() &&
                    ele._year_id.toString() == year_id.toString() &&
                    ele._level_id.toString() == level_id.toString() &&
                    ele.isDeleted == false,
            );
            if (data.length > 0) {
                for (let j = 0; j < data.length; j++) {
                    const assignedCourseDetail = {
                        master_course_id: element._id,
                        course_name: element.course_name,
                        course_type: element.course_type,
                        course_code: element.course_code,
                        credit_hours: element.credit_hours,
                        // versioning: element.versioning,
                        // versionNo: element.versionNo,
                        versionName: element.versionName || 'default',
                        versionedFrom: element.versionedFrom || null,
                        versionedCourseIds: element.versionedCourseIds || [],
                        versionNo: element.versionNo || 1,
                        versioned: element.versioned || false,
                        course_duration: data[j].course_duration,
                        isDeleted: data[j].isDeleted,
                        isActive: data[j].isActive,
                        _id: data[j]._id,
                        _program_id: data[j]._program_id,
                        program_name: data[j].program_name,
                        _curriculum_id: data[j]._curriculum_id,
                        _year_id: data[j]._year_id,
                        year: data[j].year,
                        _level_id: data[j]._level_id,
                        level_no: data[j].level_no,
                        course_shared_with: data[j].course_shared_with,
                        shared_with_others: false,
                    };
                    if (data[j].hasOwnProperty('isConfigured')) {
                        assignedCourseDetail.isConfigured = data[j].isConfigured;
                    }
                    assigned_course_data.push(assignedCourseDetail);
                }
            } else {
                for (sub_element of element.course_assigned_details) {
                    if (
                        sub_element.course_shared_with.findIndex(
                            (i) =>
                                i._program_id.toString() == program_id.toString() &&
                                i._curriculum_id.toString() == curriculum_id.toString() &&
                                i._level_id.toString() == level_id.toString(),
                        ) != -1
                    ) {
                        const courseDataDetail = {
                            master_course_id: element._id,
                            course_name: element.course_name,
                            course_type: element.course_type,
                            course_code: element.course_code,
                            credit_hours: element.credit_hours,
                            // versioning: element.versioning,
                            versionNo: element.versionNo || 1,
                            versionName: element.versionName || '',
                            versionedFrom: element.versionedFrom || '',
                            versionedCourseIds: element.versionedCourseIds || [],
                            versioned: element.versioned || false,
                            course_duration: sub_element.course_duration,
                            isDeleted: element.isDeleted,
                            isActive: element.isActive,
                            _id: sub_element._id,
                            _program_id: sub_element._program_id,
                            program_name: sub_element.program_name,
                            _curriculum_id: sub_element._curriculum_id,
                            _year_id: sub_element._year_id,
                            year: sub_element.year,
                            _level_id: sub_element._level_id,
                            level_no: sub_element.level_no,
                            course_shared_with: sub_element.course_shared_with,
                            shared_with_others: true,
                        };
                        if (sub_element.hasOwnProperty('isConfigured')) {
                            courseDataDetail.isConfigured = sub_element.isConfigured;
                        }
                        assigned_course_data.push(courseDataDetail);
                    }
                }
            }
        }
        for (let k = 0; k < assigned_course_data.length; k++) {
            const ind = cg_data.findIndex((ele) =>
                ele.course_id.equals(assigned_course_data[k].master_course_id),
            );
            if (ind == -1) {
                ungrouped_data.push(assigned_course_data[k]);
            } else {
                const obj2 = {
                    master_course_id: assigned_course_data[k].master_course_id,
                    course_name: assigned_course_data[k].course_name,
                    course_type: assigned_course_data[k].course_type,
                    course_code: assigned_course_data[k].course_code,
                    credit_hours: assigned_course_data[k].credit_hours,
                    course_duration: assigned_course_data[k].course_duration,
                    // credit_hours: assigned_course_data[k].credit_hours,
                    isDeleted: assigned_course_data[k].isDeleted,
                    isActive: assigned_course_data[k].isActive,
                    _id: assigned_course_data[k]._id,
                    _program_id: assigned_course_data[k]._program_id,
                    program_name: assigned_course_data[k].program_name,
                    _curriculum_id: assigned_course_data[k]._curriculum_id,
                    _year_id: assigned_course_data[k]._year_id,
                    year: assigned_course_data[k].year,
                    _level_id: assigned_course_data[k]._level_id,
                    level_no: assigned_course_data[k].level_no,
                    course_shared_with: assigned_course_data[k].course_shared_with,
                    // versioning: assigned_course_data[k].versioning,
                    versionNo: assigned_course_data[k].versionNo || 1,
                    versionName: assigned_course_data[k].versionName || '',
                    versionedFrom: assigned_course_data[k].versionedFrom || '',
                    versionedCourseIds: assigned_course_data[k].versionedCourseIds || [],
                    versioned: assigned_course_data[k].versioned || false,
                    group_id: cg_data[ind]._id,
                    group_name: cg_data[ind].group_name,
                };
                if (assigned_course_data[k].hasOwnProperty('isConfigured')) {
                    obj2.isConfigured = assigned_course_data[k].isConfigured;
                }
                grouped_data.push(obj2);
            }
        }
        return res.status(200).send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('ASSIGNED_COURSES_LIST'),
                {
                    assigned_course_data,
                    ungrouped_data,
                    grouped_data,
                },
            ),
        );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function get_course_assigned_details(req, res) {
    try {
        const query = { _id: ObjectId(req.params.course_id), isDeleted: false };
        const doc = await base_control.get(digi_course, query, {
            course_assigned_details: 1,
            credit_hours: 1,
            administration: 1,
            allow_editing: 1,
            isDeleted: 1,
            isActive: 1,
            _curriculum_id: 1,
            course_name: 1,
            course_code: 1,
            duration: 1,
            _program_id: 1,
            course_recurring: 1,
            participating: 1,
            course_type: 1,
            achieve_target: 1,
            versionNo: 1,
            versioned: 1,
            // versioning: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        });
        const ind = doc.data.course_assigned_details.findIndex((ele) =>
            ele._id.equals(req.params.id),
        );
        if (ind == -1)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('NOT_FOUND'),
                        [],
                    ),
                );

        const creditHours = [];
        const sessionFlowData = await base_control.get(
            session_order,
            { ...common_files.query, _course_id: ObjectId(req.params.course_id) },
            {},
        );
        if (sessionFlowData.status) {
            let sessionDelivery = sessionFlowData.data.session_flow_data.map((ele) => {
                return {
                    _session_id: ele._session_id,
                    delivery_type: ele.delivery_type,
                    _delivery_id: ele._delivery_id,
                    delivery_symbol: ele.delivery_symbol,
                };
            });
            sessionDelivery = sessionDelivery.filter(
                (item, index) =>
                    sessionDelivery.findIndex(
                        (ele) => ele._delivery_id.toString() === item._delivery_id.toString(),
                    ) === index,
            );
            doc.data.credit_hours.forEach((element) => {
                const deliveryData = element.delivery_type.filter(
                    (item) =>
                        sessionDelivery.findIndex(
                            (ele) => ele._delivery_id.toString() === item._delivery_id.toString(),
                        ) !== -1,
                );
                if (deliveryData.length)
                    creditHours.push({
                        _id: element._id,
                        _session_id: element._session_id,
                        delivery_type: deliveryData,
                        credit_hours: element.credit_hours,
                        duration_split: element.duration_split,
                        type_name: element.type_name,
                        contact_hours: element.contact_hours,
                        duration_per_contact_hour: element.duration_per_contact_hour,
                        duration: element.duration,
                        type_symbol: element.type_symbol,
                    });
            });
        }
        const course_assigned_details = doc.data.course_assigned_details[ind];
        const arr_obj = {
            administration: doc.data.administration,
            allow_editing: doc.data.allow_editing,
            isDeleted: doc.data.isDeleted,
            isActive: doc.data.isActive,
            _id: doc.data._id,
            _curriculum_id: doc.data._curriculum_id,
            // credit_hours: doc.data.credit_hours,
            credit_hours: creditHours.length !== 0 ? creditHours : doc.data.credit_hours,
            course_name: doc.data.course_name,
            course_code: doc.data.course_code,
            duration: doc.data.duration,
            _program_id: doc.data._program_id,
            course_recurring: doc.data.course_recurring,
            participating: doc.data.participating,
            course_type: doc.data.course_type,
            achieve_target: doc.data.achieve_target,
            versionNo: doc.data.versionNo || 1,
            versioned: doc.data.versioned || false,
            versionName: doc.data.versionName || '',
            versionedFrom: doc.data.versionedFrom || null,
            versionedCourseIds: doc.data.versionedCourseIds || [],
            course_assigned_details,
        };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('ASSIGNED_COURSES_LIST'),
                    arr_obj,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function course_assigned_list_map_view(req, res) {
    try {
        //digi_course_group
        // const query_CG = { isActive: true };
        // const course_group_data = await base_control.get_list(digi_course_group, query_CG, {});
        // let cg_data = [];
        // const grouped_data = [];
        // const ungrouped_data = [];
        // if (course_group_data.status) cg_data = course_group_data.data;

        const query = { isDeleted: false, isActive: true };
        const doc = await base_control.get_list(digi_course, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(res, 200, false, req.t('COURSE_NOT_FOUND'), []),
                );
        const curriculum_data = await base_control.get_list(
            digi_curriculum,
            {
                /* _program_id: ObjectId(req.params.program_id), _id: ObjectId(req.params.curriculum_id), */ isDeleted: false,
            },
            { _program_id: 1, year_level: 1 },
        );
        if (!curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );

        const current_loc = curriculum_data.data.findIndex(
            (i) =>
                i._program_id.toString() == ObjectId(req.params.program_id).toString() &&
                i._id.toString() == ObjectId(req.params.curriculum_id),
        );
        if (current_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );
        const year_loc = curriculum_data.data[current_loc].year_level.findIndex(
            (i) => i._id.toString() == req.params.year_id.toString(),
        );
        if (year_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('CURRICULUM_YEAR_NOT_FOUND'),
                        req.t('CURRICULUM_YEAR_NOT_FOUND'),
                    ),
                );
        const level_loc = curriculum_data.data[current_loc].year_level[year_loc].levels.findIndex(
            (i) => i._id.toString() == req.params.level_id.toString(),
        );
        if (level_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('CURRICULUM_LEVEL_NOT_FOUND'),
                        req.t('CURRICULUM_LEVEL_NOT_FOUND'),
                    ),
                );
        let program_id = ObjectId(req.params.program_id);
        let curriculum_id = ObjectId(req.params.curriculum_id);
        let year_id = ObjectId(req.params.year_id);
        let level_id = ObjectId(req.params.level_id);
        if (curriculum_data.data[current_loc].year_level[year_loc]._pre_requisite_id) {
            const loc = curriculum_data.data.findIndex(
                (i) =>
                    i._id.toString() ==
                    curriculum_data.data[current_loc].year_level[
                        year_loc
                    ]._pre_requisite_id.toString(),
            );
            program_id = ObjectId(curriculum_data.data[loc]._program_id);
            curriculum_id = ObjectId(
                curriculum_data.data[current_loc].year_level[year_loc]._pre_requisite_id,
            );
            year_id = ObjectId(curriculum_data.data[loc].year_level[year_loc]._id);
            level_id = ObjectId(
                curriculum_data.data[loc].year_level[year_loc].levels[level_loc]._id,
            );
        }

        const assigned_course_data = [];
        for (let i = 0; i < doc.data.length; i++) {
            // let data = doc.data[i].course_assigned_details.filter(ele => (ele._program_id == req.params.program_id && ele._program_id == req.params.program_id && ele._curriculum_id == req.params.curriculum_id && ele._year_id == req.params.year_id && ele._level_id == req.params.level_id && ele.isDeleted == false))
            const data = doc.data[i].course_assigned_details.filter(
                (ele) =>
                    ele._program_id.toString() == program_id.toString() &&
                    ele._curriculum_id.toString() == curriculum_id.toString() &&
                    ele._year_id.toString() == year_id.toString() &&
                    ele._level_id.toString() == level_id.toString() &&
                    ele.isDeleted == false,
            );

            if (data.length > 0) {
                for (let j = 0; j < data.length; j++) {
                    const obj = {
                        master_course_id: doc.data[i]._id,
                        course_name: doc.data[i].course_name,
                        course_type: doc.data[i].course_type,
                        course_code: doc.data[i].course_code,
                        //credit_hours: doc.data[i].credit_hours,
                        //course_duration: data[j].course_duration,
                        start_week: data[j].course_duration.start_week,
                        end_week: data[j].course_duration.end_week,
                        total: data[j].course_duration.total,
                        isDeleted: data[j].isDeleted,
                        isActive: data[j].isActive,
                        _id: data[j]._id,
                        //_program_id: data[j]._program_id,
                        //program_name: data[j].program_name,
                        //_curriculum_id: data[j]._curriculum_id,
                        //_year_id: data[j]._year_id,
                        //year: data[j].year,
                        //_level_id: data[j]._level_id,
                        //level_no: data[j].level_no,
                        //course_shared_with: data[j].course_shared_with
                    };
                    assigned_course_data.push(obj);
                }
            }
        }
        const assigned_course_data_map_view = assigned_course_data;

        assigned_course_data_map_view.sort(function (a, b) {
            return a.start_week - b.start_week;
        });
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('ASSIGNED_COURSES_LIST'),
                    assigned_course_data_map_view,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function course_list_recurring_level(req, res) {
    try {
        const query = {
            isDeleted: false,
            isActive: true,
            _program_id: ObjectId(req.params.program_id),
            _curriculum_id: ObjectId(req.params.curriculum_id),
        };
        const doc = await base_control.get_list(digi_course, query, {
            _id: 1,
            course_name: 1,
            course_type: 1,
            course_recurring: 1,
            course_assigned_details: 1,
            versionNo: 1,
            versioned: 1,
            versionName: 1,
            versionedFrom: 1,
            versionedCourseIds: 1,
        });
        console.log('doc', doc);
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));
        const course_list = [];
        doc.data.forEach((element) => {
            if (
                element.course_type == constant.COURSE_TYPE.STANDARD &&
                element.course_assigned_details.length == 0
            ) {
                course_list.push(element);
            } else if (
                element.course_recurring.findIndex(
                    (i) => i._level_id.toString() == req.params.level_id.toString(),
                ) != -1
            ) {
                if (element.course_assigned_details.length != 0) {
                    if (
                        element.course_assigned_details.findIndex(
                            (i) => i._level_id.toString() == req.params.level_id.toString(),
                        ) == -1
                    )
                        course_list.push(element);
                } else course_list.push(element);
            }
        });
        // console.log(course_list.length, ' ', doc.data.length)
        /* let recurring_level_courses = []
            for (let i = 0; i < doc.data.length; i++) {
                let ind = doc.data[i].course_recurring.findIndex(ele => ele._level_id.equals(req.params.level_id));
                if (ind != -1)
                    recurring_level_courses.push(doc.data[i]);
            } */
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSES_LIST_WITH_RECURRING_LEVEL'),
                    course_list,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function curriculum_wise_course_list(req, res) {
    try {
        const query = {
            isDeleted: false,
            isActive: true,
            _curriculum_id: ObjectId(req.params.curriculum_id),
        };
        const doc = await base_control.get_list(digi_course, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('COURSES_LIST_BY_CURRICULUM_ID'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function program_list_by_course_type(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        let query = {};
        if (req.params.course_type == constant.COURSE_TYPE.STANDARD)
            query = {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
                _id: { $nin: [req.params.program_id] },
            };
        else
            query = {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            };
        const doc = await base_control.get_list(program, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('PROGRAM_LIST'), doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
const checkDuplicateCourseCodeInExcel = function (course_excel_data, course_code) {
    const course_code_arr = course_excel_data.map((ele) => ele.Course_Code.toLowerCase());
    let flag = 0;
    const first_ind = course_code_arr.indexOf(course_code.trim().toLowerCase());
    const last_ind = course_code_arr.lastIndexOf(course_code.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const checkDuplicateCourseNameInExcel = function (course_excel_data, course_name) {
    const course_name_arr = course_excel_data.map((ele) => ele.Course_Name.trim().toLowerCase());
    let flag = 0;
    const first_ind = course_name_arr.indexOf(course_name.trim().toLowerCase());
    const last_ind = course_name_arr.lastIndexOf(course_name.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const check_shared_subject = function (
    department_subject_data,
    subject_arr,
    program_data,
    program,
) {
    if (subject_arr.length == 3) {
        let obj = {};
        let flag = 0;
        for (department of department_subject_data.data) {
            const dept_ind = department.shared_with.findIndex(
                (ele) => ele.program_id.toString() == program._id.toString(),
            );
            if (dept_ind != -1) {
                const sub_ind = department.subject.findIndex(
                    (ele) => ele.subject_name.toLowerCase() == subject_arr[0].trim().toLowerCase(),
                );
                if (sub_ind != -1) {
                    flag = 1;
                    obj = {
                        _program_id: department.program_id,
                        program_name: department.program_name,
                        _department_id: department._id,
                        department_name: department.department_name,
                        _subject_id: department.subject[sub_ind]._id,
                        subject_name: department.subject[sub_ind].subject_name,
                    };
                    return { status: true, data: obj };
                }
            } else {
                const dept_data = department_subject_data.data.filter(
                    (ele) =>
                        ele.department_name.toLowerCase() == subject_arr[1].trim().toLowerCase(),
                );
                if (dept_data.length > 0) {
                    for (department of dept_data) {
                        for (subject of department.subject) {
                            const subj_shared_ind = subject.shared_with.findIndex(
                                (ele) =>
                                    ele.program_id.toString() == program._id.toString() &&
                                    ele.department_id.toString() == dept._id,
                            );
                            if (subj_shared_ind != -1) {
                                flag = 1;
                                obj = {
                                    _program_id: department.program_id,
                                    program_name: department.program_name,
                                    _department_id: department._id,
                                    department_name: department.department_name,
                                    _subject_id: subject._id,
                                    subject_name: subject.subject_name,
                                };
                                return { status: true, data: obj };
                            }
                        }
                    }
                }
            }
        }
        if (flag == 0) {
            //Subject sharing check
            //subject_arr[1].trim().toLowerCase()
            for (department of department_subject_data.data) {
                //console.log(department.subject)
                const subj_ind = department.subject.findIndex(
                    (ele) => ele.subject_name.toLowerCase() == subject_arr[0].trim().toLowerCase(),
                );
                //console.log("subj_ind", subj_ind)
                if (subj_ind != -1) {
                    const shared_ind = department.subject[subj_ind].shared_with.findIndex(
                        (ele) => ele.program_id.toString() == program._id.toString(),
                    );
                    //console.log("shared_ind", shared_ind)
                    if (shared_ind != -1) {
                        //console.log((department.subject[subj_ind].shared_with[shared_ind].department_name).toLowerCase() + "==" + subject_arr[1].trim().toLowerCase())
                        if (
                            department.subject[subj_ind].shared_with[
                                shared_ind
                            ].department_name.toLowerCase() == subject_arr[1].trim().toLowerCase()
                        ) {
                            obj = {
                                _program_id:
                                    department.subject[subj_ind].shared_with[shared_ind].program_id,
                                program_name:
                                    department.subject[subj_ind].shared_with[shared_ind]
                                        .program_name,
                                _department_id:
                                    department.subject[subj_ind].shared_with[shared_ind]
                                        .department_id,
                                department_name:
                                    department.subject[subj_ind].shared_with[shared_ind]
                                        .department_name,
                                _subject_id: department.subject[subj_ind]._id,
                                subject_name: department.subject[subj_ind].subject_name,
                            };
                            return { status: true, data: obj };
                        }
                    }
                }
            }

            return { status: false, data: {} };
        }
    } else if (subject_arr.length == 2) {
        let obj = {};
        let flag = 0;
        for (department of department_subject_data.data) {
            const dept_ind = department.shared_with.findIndex(
                (ele) => ele.program_id.toString() == program._id.toString(),
            );
            if (dept_ind != -1) {
                const sub_ind = department.subject.findIndex(
                    (ele) => ele.subject_name.toLowerCase() == subject_arr[0].trim().toLowerCase(),
                );
                if (sub_ind != -1) {
                    flag = 1;
                    obj = {
                        _program_id: department.program_id,
                        program_name: department.program_name,
                        _department_id: department._id,
                        department_name: department.department_name,
                        _subject_id: department.subject[sub_ind]._id,
                        subject_name: department.subject[sub_ind].subject_name,
                    };
                    return { status: true, data: obj };
                }
            } else {
                const dept_data = department_subject_data.data.filter(
                    (ele) =>
                        ele.department_name.toLowerCase() == subject_arr[1].trim().toLowerCase(),
                );
                if (dept_data.length > 0) {
                    for (department of dept_data) {
                        for (subject of department.subject) {
                            const subj_shared_ind = subject.shared_with.findIndex(
                                (ele) =>
                                    ele.program_id.toString() == program._id.toString() &&
                                    ele.department_id.toString() == dept._id,
                            );
                            if (subj_shared_ind != -1) {
                                flag = 1;
                                obj = {
                                    _program_id: department.program_id,
                                    program_name: department.program_name,
                                    _department_id: department._id,
                                    department_name: department.department_name,
                                    _subject_id: subject._id,
                                    subject_name: subject.subject_name,
                                };
                                return { status: true, data: obj };
                            }
                        }
                    }
                }
            }
        }
        if (flag == 0) return { status: false, data: {} };
    } else if (subject_arr.length == 1) {
        let obj = {};
        let flag = 0;
        for (department of department_subject_data.data) {
            const dept_ind = department.shared_with.findIndex(
                (ele) => ele.program_id.toString() == program._id.toString(),
            );
            //console.log(dept_ind)
            if (dept_ind != -1) {
                const sub_ind = department.subject.findIndex(
                    (ele) => ele.subject_name.toLowerCase() == subject_arr[0].trim().toLowerCase(),
                );
                console.log('shared sub ind', sub_ind);
                if (sub_ind != -1) {
                    flag = 1;
                    obj = {
                        _program_id: department.program_id,
                        program_name: department.program_name,
                        _department_id: department._id,
                        department_name: department.department_name,
                        _subject_id: department.subject[sub_ind]._id,
                        subject_name: department.subject[sub_ind].subject_name,
                    };
                    console.log('shared subj obn: ' + obj);
                    return { status: true, data: obj };
                }
            } else {
                const sub_ind = department.subject.findIndex(
                    (ele) => ele.subject_name.toLowerCase() == subject_arr[0].trim().toLowerCase(),
                );
                if (sub_ind != -1) {
                    const shared_sub_ind = department.subject[sub_ind].shared_with.findIndex(
                        (ele) => ele.program_id.toString() == program._id.toString(),
                    );
                    if (shared_sub_ind != -1) {
                        flag = 1;
                        obj = {
                            _program_id: department.program_id,
                            program_name: department.program_name,
                            _department_id: department._id,
                            department_name: department.department_name,
                            _subject_id: department.subject[sub_ind]._id,
                            subject_name: department.subject[sub_ind].subject_name,
                        };
                        return { status: true, data: obj };
                    }
                }
            }
        }
        if (flag == 0) return { status: false, data: {} };
    } else {
        return { status: false, data: {} };
    }
};
const validate_data_check = async function (req, res, data, collection_datas, duplicate_in_excel) {
    const message = [];
    const obj = {};
    let program = '';
    let curr_data = '';
    if (duplicate_in_excel) message.push(req.t('DUPLICATE_IN_EXCEL'));

    const {
        program_data,
        curriculum_data,
        course_data,
        // session_delivery_types_data,
        // dept_subj_data,
    } = collection_datas;
    let { dept_subj_data } = collection_datas;
    const department_subject_data = dept_subj_data;
    //return res.send(department_subject_data)
    //Program Check
    const program_ind = program_data.data.findIndex(
        (ele) =>
            ele.code.toLowerCase() == data.Program_Code.trim().toLowerCase() &&
            ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase(),
    );
    if (program_ind == -1) message.push(req.t('CHECK_PROGRAM_NAME_AND_CODE'));
    else {
        program = program_data.data[program_ind];
        //Curriculum Check
        const curriculum_ind = curriculum_data.data.findIndex(
            (ele) =>
                ele.curriculum_name.toLowerCase() == data.Curriculum_Name.trim().toLowerCase() &&
                ele._program_id.toString() == program_data.data[program_ind]._id.toString(),
        );
        if (curriculum_ind == -1) message.push(req.t('CHECK_CURRICULUM'));
        else {
            //Curriculum Data
            curr_data = curriculum_data.data[curriculum_ind];

            //Year and Level Data
            const year_level_arr = [];
            for (year of curr_data.year_level) {
                for (lvl of year.levels) {
                    year_level_arr.push({
                        year_id: year._id,
                        year: year.y_type,
                        level_id: lvl._id,
                        level_name: lvl.level_name,
                        start_week: lvl.start_week,
                        end_week: lvl.end_week,
                    });
                }
            }

            //Year check
            const year_ind = year_level_arr.findIndex(
                (ele) => ele.year.toLowerCase() == data.Year.trim().toLowerCase(),
            );
            if (year_ind == -1) message.push(req.t('YEAR_NOT_FOUND'));

            //Level Check
            const level_arr = data.Level.toString().split(',');
            for (let i = 0; i < level_arr.length; i++) {
                const level_ind = year_level_arr.findIndex(
                    (ele) => ele.level_name.toLowerCase() == level_arr[i].trim().toLowerCase(),
                );
                if (level_ind == -1) message.push(req.t('LEVEL_NOT_FOUND'));
            }

            //start week 5
            //Duration in weeks 30

            //level start week  15
            //level end week  20

            //Start Week
            const start_week_arr = data.Start_Week.toString().split(',');
            if (start_week_arr.length == 0) message.push(req.t('CHECK_START_WEEK'));
            else {
                if (level_arr.length == start_week_arr.length && level_arr.length != 0) {
                    for (let i = 0; i < start_week_arr.length; i++) {
                        const level_ind = year_level_arr.findIndex(
                            (ele) =>
                                ele.level_name.toLowerCase() == level_arr[i].trim().toLowerCase(),
                        );
                        if (level_ind != -1) {
                            const start_week = start_week_arr[i];
                            const end_week =
                                Number(start_week) + Number(data.Duration_in_weeks) - 1;
                            const level_start_week = Number(year_level_arr[level_ind].start_week);
                            const level_end_week = Number(year_level_arr[level_ind].end_week);
                            if (start_week >= level_start_week && end_week <= level_end_week) {
                                //console.log("Validation success " + year_level_arr[level_ind].level_name)
                            } else
                                message.push(
                                    req.t('COURSE_START_AND_END_WEEK_ARE_NOT_MATCH_WITH_LEVEL') +
                                        year_level_arr[level_ind].level_name,
                                );
                        }
                    }
                }
            }

            //Course Type
            const arr_course_type = [constant.COURSE_TYPE.SELECTIVE, constant.COURSE_TYPE.STANDARD];
            const check_course_type = arr_course_type.includes(data.Course_Type.trim());
            const course_recurring_data = [];
            const course_occurring_data = [];
            if (!check_course_type)
                message.push(
                    'Course type must be ' +
                        constant.COURSE_TYPE.STANDARD +
                        ' or ' +
                        constant.COURSE_TYPE.SELECTIVE,
                );
            else {
                if (data.Course_Type == constant.COURSE_TYPE.SELECTIVE) {
                    //course recurring check
                    course_recurring_arr = data.Course_Recurring_at.toString().split(',');
                    for (let i = 0; i < course_recurring_arr.length; i++) {
                        const recurring_level_ind = year_level_arr.findIndex(
                            (ele) =>
                                ele.level_name.toLowerCase() ==
                                course_recurring_arr[i].trim().toLowerCase(),
                        );
                        if (recurring_level_ind == -1)
                            message.push(
                                req.t('COURSE_RECURRING_LEVEL_NOT_FOUND') + course_recurring_arr[i],
                            );
                        else {
                            course_recurring_data.push({
                                _year_id: year_level_arr[recurring_level_ind].year_id,
                                year_no: year_level_arr[recurring_level_ind].year,
                                _level_id: year_level_arr[recurring_level_ind].level_id,
                                level_no: year_level_arr[recurring_level_ind].level_name,
                                isAssigned: true,
                            });
                        }
                    }

                    //Check Level Fall in Course recurring Levels
                    for (let i = 0; i < level_arr.length; i++) {
                        let flag = 0;
                        for (let j = 0; j < course_recurring_arr.length; j++) {
                            if (
                                level_arr[i].trim().toLowerCase() ==
                                course_recurring_arr[j].trim().toLowerCase()
                            ) {
                                flag = 1;
                                break;
                            }
                        }
                        if (flag == 0)
                            message.push(
                                req.t('LEVEL') +
                                    level_arr[i] +
                                    req.t('IS_NOT_PRESENT_IN_COURSE_RECURRING_LEVEL'),
                            );
                    }
                } else {
                    //course ocurring check
                    course_occurring_arr = data.Level.toString().split(',');
                    for (let i = 0; i < course_occurring_arr.length; i++) {
                        const occurring_level_ind = year_level_arr.findIndex(
                            (ele) =>
                                ele.level_name.toLowerCase() ==
                                course_occurring_arr[i].trim().toLowerCase(),
                        );
                        if (occurring_level_ind == -1)
                            message.push(
                                req.t('COURSE_OCCURRING_LEVEL_NOT_FOUND') + course_occurring_arr[i],
                            );
                        else {
                            course_occurring_data.push({
                                _year_id: year_level_arr[occurring_level_ind].year_id,
                                year_no: year_level_arr[occurring_level_ind].year,
                                _level_id: year_level_arr[occurring_level_ind].level_id,
                                level_no: year_level_arr[occurring_level_ind].level_name,
                                isAssigned: true,
                            });
                        }
                    }

                    //Check Level Fall in Course recurring Levels
                    for (let i = 0; i < level_arr.length; i++) {
                        let flag = 0;
                        for (let j = 0; j < course_occurring_arr.length; j++) {
                            if (
                                level_arr[i].trim().toLowerCase() ==
                                course_occurring_arr[j].trim().toLowerCase()
                            ) {
                                flag = 1;
                                break;
                            }
                        }
                        if (flag == 0)
                            message.push(
                                req.t('LEVEL') +
                                    level_arr[i] +
                                    req.t('IS_NOT_PRESENT_IN_COURSE_OCCURRING_LEVEL'),
                            );
                    }
                }
            }

            //course check in DB
            if (course_data.status) {
                const course_code_ind = course_data.data.findIndex(
                    (ele) =>
                        ele.course_code.toLowerCase() == data.Course_Code.trim().toLowerCase() &&
                        ele._program_id.toString() ==
                            program_data.data[program_ind]._id.toString() &&
                        ele._curriculum_id.toString() ==
                            curriculum_data.data[curriculum_ind]._id.toString(),
                );
                const course_name_ind = course_data.data.findIndex(
                    (ele) =>
                        ele.course_name.toLowerCase() == data.Course_Name.trim().toLowerCase() &&
                        ele._program_id.toString() ==
                            program_data.data[program_ind]._id.toString() &&
                        ele._curriculum_id.toString() ==
                            curriculum_data.data[curriculum_ind]._id.toString(),
                );
                if (course_code_ind != -1 || course_name_ind != -1)
                    message.push(req.t('COURSE_CODE_ALREADY_EXIST'));
                if (course_name_ind != -1) message.push(req.t('COURSE_NAME_ALREADY_EXIST'));
            }
            //Delivering Subject check
            const delivering_subject_arr = [];

            dept_subj_data = dept_subj_data.data.filter(
                (ele) => ele.program_id.toString() == program_data.data[program_ind]._id.toString(),
            );
            const del_subj_arr = data.Delivering_Subjects.toString().split(',');
            if (del_subj_arr.length == 0) message.push(req.t('DELIVERING_SUBJECTS_IS_EMPTY'));
            else {
                for (let i = 0; i < del_subj_arr.length; i++) {
                    const arr = del_subj_arr[i].split('/');
                    console.log(arr);
                    if (arr.length == 3) {
                        const dept_ind = dept_subj_data.findIndex(
                            (ele) =>
                                ele.department_name.toLowerCase() == arr[1].trim().toLowerCase(),
                        );
                        if (dept_ind == -1) {
                            const shared_status = check_shared_subject(
                                department_subject_data,
                                arr,
                                program_data,
                                program,
                            );
                            if (!shared_status.status)
                                message.push(req.t('PARTICIPATING_DEPARTMENT_NOT_FOUND') + arr[1]);
                            else {
                                delivering_subject_arr.push(shared_status.data);
                            }
                        } else {
                            const subj_ind = dept_subj_data[dept_ind].subject.findIndex(
                                (ele) =>
                                    ele.subject_name.toLowerCase() == arr[0].trim().toLowerCase(),
                            );
                            if (subj_ind == -1) {
                                const shared_status = check_shared_subject(
                                    department_subject_data,
                                    arr,
                                    program_data,
                                    program,
                                );
                                if (!shared_status.status)
                                    message.push(req.t('PARTICIPATING_SUBJECT_NOT_FOUND') + arr[0]);
                                else {
                                    delivering_subject_arr.push(shared_status.data);
                                }
                            } else {
                                delivering_subject_arr.push({
                                    _program_id: dept_subj_data[dept_ind].program_id,
                                    program_name: dept_subj_data[dept_ind].program_name,
                                    _department_id: dept_subj_data[dept_ind]._id,
                                    department_name: dept_subj_data[dept_ind].department_name,
                                    _subject_id: dept_subj_data[dept_ind].subject[subj_ind]._id,
                                    subject_name:
                                        dept_subj_data[dept_ind].subject[subj_ind].subject_name,
                                });
                            }
                        }
                    }
                    if (arr.length == 2) {
                        const dept_ind = dept_subj_data.findIndex(
                            (ele) =>
                                ele.department_name.toLowerCase() == arr[1].trim().toLowerCase(),
                        );
                        if (dept_ind == -1) {
                            const shared_status = check_shared_subject(
                                department_subject_data,
                                arr,
                                program_data,
                                program,
                            );
                            if (!shared_status.status)
                                message.push(req.t('PARTICIPATING_DEPARTMENT_NOT_FOUND') + arr[1]);
                            else {
                                delivering_subject_arr.push(shared_status.data);
                            }
                        } else {
                            const subj_ind = dept_subj_data[dept_ind].subject.findIndex(
                                (ele) =>
                                    ele.subject_name.toLowerCase() == arr[0].trim().toLowerCase(),
                            );
                            if (subj_ind == -1) {
                                const shared_status = check_shared_subject(
                                    department_subject_data,
                                    arr,
                                    program_data,
                                    program,
                                );
                                if (!shared_status.status)
                                    message.push(req.t('PARTICIPATING_SUBJECT_NOT_FOUND') + arr[1]);
                                else {
                                    delivering_subject_arr.push(shared_status.data);
                                }
                            } else {
                                delivering_subject_arr.push({
                                    _program_id: dept_subj_data[dept_ind].program_id,
                                    program_name: dept_subj_data[dept_ind].program_name,
                                    _department_id: dept_subj_data[dept_ind]._id,
                                    department_name: dept_subj_data[dept_ind].department_name,
                                    _subject_id: dept_subj_data[dept_ind].subject[subj_ind]._id,
                                    subject_name:
                                        dept_subj_data[dept_ind].subject[subj_ind].subject_name,
                                });
                            }
                        }
                    }
                    if (arr.length == 1) {
                        let subj_flag = 0;
                        for (dept of dept_subj_data) {
                            if (
                                dept.program_id.toString() ==
                                program_data.data[program_ind]._id.toString()
                            ) {
                                const subj_ind = dept.subject.findIndex(
                                    (ele) =>
                                        ele.subject_name.toLowerCase() ==
                                        arr[0].trim().toLowerCase(),
                                );
                                if (subj_ind != -1) {
                                    subj_flag = 1;
                                    delivering_subject_arr.push({
                                        _program_id: dept.program_id,
                                        program_name: dept.program_name,
                                        _department_id: dept._id,
                                        department_name: dept.department_name,
                                        _subject_id: dept.subject[subj_ind]._id,
                                        subject_name: dept.subject[subj_ind].subject_name,
                                    });
                                    break;
                                }
                            }
                        }
                        if (subj_flag == 0) {
                            const shared_status = check_shared_subject(
                                department_subject_data,
                                arr,
                                program_data,
                                program,
                            );
                            if (!shared_status.status)
                                message.push(req.t('PARTICIPATING_SUBJECT_NOT_FOUND') + arr[0]);
                            else {
                                delivering_subject_arr.push(shared_status.data);
                            }
                        }
                    }
                }
            }

            //////////////////////////
            //Administrating subject dept program Check
            const admin_subj_obj = data.Administrating_Subject_Dept_Prog;
            const arr = admin_subj_obj.toString().split('/');
            let administrating_subject_obj = {};

            if (arr.length == 0) message.push(req.t('ADMINISTRATING_SUBJECT_IS_EMPTY'));
            else {
                if (arr.length == 3) {
                    console.log(arr[1].toLowerCase(), arr[2].toLowerCase());
                    const dept_ind = dept_subj_data.findIndex(
                        (ele) => ele.department_name.toLowerCase() == arr[1].trim().toLowerCase(),
                    );
                    if (dept_ind == -1) {
                        const shared_status = check_shared_subject(
                            department_subject_data,
                            arr,
                            program_data,
                            program,
                        );
                        if (!shared_status.status)
                            message.push(req.t('ADMINISTRATING_DEPARTMENT_NOT_FOUND') + arr[1]);
                        else administrating_subject_obj = shared_status.data;
                    } else {
                        const subj_ind = dept_subj_data[dept_ind].subject.findIndex(
                            (ele) => ele.subject_name.toLowerCase() == arr[0].trim().toLowerCase(),
                        );
                        if (subj_ind == -1) {
                            const shared_status = check_shared_subject(
                                department_subject_data,
                                arr,
                                program_data,
                                program,
                            );
                            if (!shared_status.status)
                                message.push(req.t('ADMINISTRATING_SUBJECT_NOT_FOUND') + arr[0]);
                            else administrating_subject_obj = shared_status.data;
                        } else {
                            administrating_subject_obj = {
                                _program_id: dept_subj_data[dept_ind].program_id,
                                program_name: dept_subj_data[dept_ind].program_name,
                                _department_id: dept_subj_data[dept_ind]._id,
                                department_name: dept_subj_data[dept_ind].department_name,
                                _subject_id: dept_subj_data[dept_ind].subject[subj_ind]._id,
                                subject_name:
                                    dept_subj_data[dept_ind].subject[subj_ind].subject_name,
                            };
                        }
                    }
                }
                //return res.send(dept_subj_data);
                if (arr.length == 2) {
                    const dept_ind = dept_subj_data.findIndex(
                        (ele) => ele.department_name.toLowerCase() == arr[1].trim().toLowerCase(),
                    );
                    if (dept_ind == -1) {
                        const shared_status = check_shared_subject(
                            department_subject_data,
                            arr,
                            program_data,
                            program,
                        );
                        if (!shared_status.status)
                            message.push(req.t('ADMINISTRATING_DEPARTMENT_NOT_FOUND') + arr[1]);
                        else administrating_subject_obj = shared_status.data;
                    } else {
                        const subj_ind = dept_subj_data[dept_ind].subject.findIndex(
                            (ele) => ele.subject_name.toLowerCase() == arr[0].trim().toLowerCase(),
                        );
                        if (subj_ind == -1) {
                            const shared_status = check_shared_subject(
                                department_subject_data,
                                arr,
                                program_data,
                                program,
                            );
                            if (!shared_status.status)
                                message.push(req.t('ADMINISTRATING_SUBJECT_NOT_FOUND') + arr[0]);
                            else administrating_subject_obj = shared_status.data;
                        } else {
                            administrating_subject_obj = {
                                _program_id: dept_subj_data[dept_ind].program_id,
                                program_name: dept_subj_data[dept_ind].program_name,
                                _department_id: dept_subj_data[dept_ind]._id,
                                department_name: dept_subj_data[dept_ind].department_name,
                                _subject_id: dept_subj_data[dept_ind].subject[subj_ind]._id,
                                subject_name:
                                    dept_subj_data[dept_ind].subject[subj_ind].subject_name,
                            };
                        }
                    }
                }
                if (arr.length == 1) {
                    let subj_flag = 0;
                    for (dept of dept_subj_data) {
                        if (
                            dept.program_id.toString() ==
                            program_data.data[program_ind]._id.toString()
                        ) {
                            const subj_ind = dept.subject.findIndex(
                                (ele) =>
                                    ele.subject_name.toLowerCase() == arr[0].trim().toLowerCase(),
                            );
                            if (subj_ind != -1) {
                                subj_flag = 1;
                                administrating_subject_obj = {
                                    _program_id: dept.program_id,
                                    program_name: dept.program_name,
                                    _department_id: dept._id,
                                    department_name: dept.department_name,
                                    _subject_id: dept.subject[subj_ind]._id,
                                    subject_name: dept.subject[subj_ind].subject_name,
                                };
                                break;
                            }
                        }
                    }
                    if (subj_flag == 0) {
                        const shared_status = check_shared_subject(
                            department_subject_data,
                            arr,
                            program_data,
                            program,
                        );
                        if (!shared_status.status)
                            message.push(req.t('ADMINISTRATING_SUBJECT_NOT_FOUND') + arr[0]);
                        else administrating_subject_obj = shared_status.data;
                    }
                }
            }

            //Theory_Credit_Hours check
            if (!Number.isInteger(data.Theory_Credit_Hours))
                message.push(req.t('CHECK_THEORY_CREDIT_HOURS_SHOULD_BE_INTEGER'));

            //Theory_Contact_hours_per_credit_hour check
            if (!Number.isInteger(data.Theory_Contact_hours_per_credit_hour))
                message.push(req.t('CHECK_THEORY_CONTACT_HOURS_PER_CREDIT_HOUR_SHOULD_BE_INTEGER'));

            //Theory_duration_in_minutes check
            if (!Number.isInteger(data.Theory_duration_in_minutes))
                message.push(req.t('CHECK_THEORY_DURATION_IN_MINUTES_SHOULD_BE_INTEGER'));

            //Practical_Credit_Hours check
            if (!Number.isInteger(data.Practical_Credit_Hours))
                message.push(req.t('CHECK_PRACTICAL_CREDIT_HOURS_SHOULD_BE_INTEGER'));

            if (!Number.isInteger(data.Practical_Contact_hours_per_credit_hour))
                message.push(
                    req.t('CHECK_PRACTICAL_CONTACT_HOURS_PER_CREDIT_HOUR_SHOULD_BE_INTEGER'),
                );

            //Practical_duration_in_minutes check
            if (!Number.isInteger(data.Practical_duration_in_minutes))
                message.push(req.t('CHECK_PRACTICAL_DURATION_IN_MINUTES_SHOULD_BE_INTEGER'));

            //Practical_Credit_Hours check
            if (!Number.isInteger(data.Clinical_Credit_Hours))
                message.push(req.t('CHECK_CLINICAL_CREDIT_HOURS_SHOULD_BE_INTEGER'));

            //Clinical_Contact_hours_per_credit_hour check
            if (!Number.isInteger(data.Clinical_Contact_hours_per_credit_hour))
                message.push(req.t('CHECK_CLINICAL_SHOULD_BE_INTEGER'));

            //Clinical_duration_in_minutes check
            if (!Number.isInteger(data.Clinical_duration_in_minutes))
                message.push(req.t('CHECK_CLINICAL_DURATION_IN_MINUTES_SHOULD_BE_INTEGER'));

            //Allow_to_edit_credit_hours_while_scheduling
            if (
                data.Allow_to_edit_credit_hours_while_scheduling != false &&
                data.Allow_to_edit_credit_hours_while_scheduling != true
            )
                message.push(
                    req.t('CHECK_ALLOW_TO_EDIT_CREDIT_HOURS_WHILE_SCHEDULING_SHOULD_BE_BOOLEAN'),
                );

            //Allow_to_edit_credit_hours_while_scheduling
            if (
                data.Should_Achieve_target_credit_hours != false &&
                data.Should_Achieve_target_credit_hours != true
            )
                message.push(req.t('CHECK_SHOULD_ACHIEVE_TARGET_CREDIT_HOURS_SHOULD_BE_BOOLEAN'));

            obj.course_recurring = course_recurring_data;
            obj.course_occurring = course_occurring_data;
            obj.participating = delivering_subject_arr;
            obj.administration = administrating_subject_obj;
        }
    }
    return { message, data: obj, other_datas: { program, curr_data } };
};
/* const field_empty_validation = function (import_data, optional_field) {
    const validation_data = [];
    for (data of import_data) {
        const message = [];
        for (const key in data) {
            if (optional_field.includes(key)) continue;
            if (data[key].toString() == '') message.push(key + ' is required');
        }
        if (message.length > 0) validation_data.push({ data, message });
    }
    if (validation_data.length == 0) return { status: false, message: validation_data };
    return { status: true, message: validation_data };
}; */
const field_empty_validation = function (import_data, optional_field) {
    const validation_data = [];
    for (data of import_data) {
        const message = [];
        for (const key in data) {
            if (optional_field.includes(key)) continue;
            if (data[key].toString() == '') message.push(key + ' is required');
        }
        if (message.length > 0) validation_data.push({ data, message });
    }
    if (validation_data.length == 0) return { status: false, message: validation_data };
    return { status: true, message: validation_data };
};
async function data_check_import_course(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Curriculum List
        const query_curriculum = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const curriculum_data = await base_control.get_list(digi_curriculum, query_curriculum, {});

        if (!curriculum_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        [],
                    ),
                );
        // Course List
        const query_course = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const course_data = await base_control.get_list(digi_course, query_course, {});
        if (!course_data.status) {
            course_data.data = [];
        }

        // Department and subject list
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        //let query = { isDeleted: false };
        const dept_subj_data = await base_control.get_list(department_subject, query, {});
        if (!dept_subj_data.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        [],
                    ),
                );

        //Session and Delivery type List
        const query_sess_del_type = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const session_delivery_type_data = await base_control.get_list(
            digi_session_delivery_types,
            query_sess_del_type,
            {},
        );
        if (!session_delivery_type_data.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'),
                        [],
                    ),
                );

        const collection_datas = {
            program_data,
            curriculum_data,
            course_data,
            dept_subj_data,
            session_delivery_type_data,
        };
        //return res.send(dept_subj_data)
        //Empty Validation
        const optional_field = [];
        //Optional fields
        for (data of req.body.course) {
            if (data.Course_Type.trim().toLowerCase() == constant.COURSE_TYPE.STANDARD)
                optional_field.push('Course_Recurring_at');
            else if (data.Course_Type.trim().toLowerCase() == constant.COURSE_TYPE.SELECTIVE)
                optional_field.push('Level');
        }
        const empty_validation_check = field_empty_validation(req.body.course, optional_field);
        //return res.send(empty_validation_check)
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.course) {
            const course_code_dup_status = checkDuplicateCourseCodeInExcel(
                req.body.course,
                data.Course_Code,
            );
            const course_name_dup_status = checkDuplicateCourseNameInExcel(
                req.body.course,
                data.Course_Name,
            );
            if (course_code_dup_status || course_name_dup_status) {
                //Duplicate in Excel
                validation = await validate_data_check(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

const localDataCheckCourse = async (req, res) => {
    try {
        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.response_function(res, 409, false, req.t('PROGRAM_NOT_FOUND'), []),
                );

        // Curriculum List
        const query_curriculum = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const curriculum_data = await base_control.get_list(digi_curriculum, query_curriculum, {});
        // if (!curriculum_data.status)
        //     return res
        //         .status(409)
        //         .send(common_files.response_function(res, 409, false, 'Course not found', []));

        // Course List
        const query_course = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const course_data = await base_control.get_list(digi_course, query_course, {});
        // if (!course_data.status)
        //     return res
        //         .status(409)
        //         .send(common_files.response_function(res, 409, false, 'Curriculum not found', []));

        // Department and subject list
        const query = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false };
        //let query = { isDeleted: false };
        const dept_subj_data = await base_control.get_list(department_subject, query, {});
        if (!dept_subj_data.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        [],
                    ),
                );

        //Session and Delivery type List
        //let query_sess_del_type = { isDeleted: false };
        const query_sess_del_type = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const session_delivery_type_data = await base_control.get_list(
            digi_session_delivery_types,
            query_sess_del_type,
            {},
        );
        if (!session_delivery_type_data.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        false,
                        req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'),
                        [],
                    ),
                );

        const collection_datas = {
            program_data,
            curriculum_data,
            course_data,
            dept_subj_data,
            session_delivery_type_data,
        };
        const valid_data = [];
        const invalid_data = [];
        const import_data = [];
        let pgm = [];
        const curriculum = [];
        const collections_error_message = [];
        for (data of req.body.course) {
            const message = [];
            let validation = '';
            const course_code_dup_status = checkDuplicateCourseCodeInExcel(
                req.body.course,
                data.Course_Code,
            );
            const course_name_dup_status = checkDuplicateCourseNameInExcel(
                req.body.course,
                data.Course_Name,
            );
            if (course_code_dup_status || course_name_dup_status) {
                //Duplicate in Excel
                validation = await validate_data_check(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message });

            import_data.push(validation.data);
            pgm = validation.other_datas.program;
            curriculum.push(validation.other_datas.curr_data);
        }
        const d = {
            valid_data,
            invalid_data,
            other_error_message: collections_error_message,
            import_data,
            other_datas: { pgm, curriculum },
        };
        if (invalid_data.length > 0) return { status: false, data: d };
        return { status: true, data: d };
    } catch (error) {
        return error;
    }
};
async function import_course_with_assign(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //data check
        const data_check_status = await localDataCheckCourse(req, res);
        //return res.send(data_check_status);
        if (!data_check_status.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        data_check_status.data,
                    ),
                );

        //Session and Delivery type List
        const query_sess_del_type = { isDeleted: false };
        let session_delivery_type_data = await base_control.get_list(
            digi_session_delivery_types,
            query_sess_del_type,
            {},
        );
        if (!session_delivery_type_data.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'),
                        [],
                    ),
                );
        const course_obj = [];
        const curriculum_arr = data_check_status.data.other_datas.curriculum;
        const program = data_check_status.data.other_datas.pgm;
        const import_data = data_check_status.data.import_data;
        session_delivery_type_data = session_delivery_type_data.data.filter(
            (ele) => ele._program_id.toString() == program._id.toString(),
        );
        if (session_delivery_type_data.length == 0)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'),
                        [],
                    ),
                );
        for (let i = 0; i < req.body.course.length; i++) {
            //Level ID From Curriculum
            const curr_ind = curriculum_arr.findIndex(
                (ele) =>
                    ele.curriculum_name.toLowerCase() ==
                    req.body.course[i].Curriculum_Name.toLowerCase(),
            );
            if (curr_ind == -1)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('CURRICULUM_NOT_FOUND'),
                            [],
                        ),
                    );
            const curriculum = curriculum_arr[curr_ind];
            const level_arr = [];
            for (year of curriculum.year_level) {
                for (lvl of year.levels) {
                    level_arr.push({
                        year_id: year._id,
                        year: year.y_type,
                        level_id: lvl._id,
                        level_name: lvl.level_name,
                    });
                }
            }

            //Session and Delivery type Data
            //Credit Hours
            const credit_hours = [];

            if (Number.isInteger(req.body.course[i].Theory_Credit_Hours)) {
                //if (data.Theory_Credit_Hours != 0) {
                const session_ind = session_delivery_type_data.findIndex(
                    (ele) => ele.session_name == 'Theory',
                );
                if (session_ind != -1) {
                    //if (req.body.course[i].Theory_Credit_Hours != 0) {
                    const del_type_arr = [];
                    for (del_type_data of session_delivery_type_data[session_ind].delivery_types) {
                        const obj = {
                            _delivery_id: del_type_data._id,
                            delivery_type: del_type_data.delivery_name,
                            delivery_symbol: del_type_data.delivery_symbol,
                            duration: del_type_data.delivery_duration,
                            isActive: true,
                        };
                        del_type_arr.push(obj);
                    }

                    const theory_obj = {
                        type_name: 'Theory',
                        type_symbol: 'T',
                        credit_hours: req.body.course[i].Theory_Credit_Hours,
                        contact_hours: req.body.course[i].Theory_Contact_hours_per_credit_hour,
                        duration_split: true,
                        duration: req.body.course[i].Theory_duration_in_minutes,
                        duration_per_contact_hour: req.body.course[i].Theory_per_contact_hour,
                        _session_id: session_delivery_type_data[session_ind]._id,
                        delivery_type: del_type_arr,
                    };
                    credit_hours.push(theory_obj);
                    //}
                } /* else
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(res, 409, false, 'Theory not found', []),
                        ); */
                //}
            } else
                return res
                    .status(409)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            409,
                            false,
                            req.t('CHECK_THEORY_CREDIT_HOURS'),
                            [],
                        ),
                    );

            if (Number.isInteger(req.body.course[i].Practical_Credit_Hours)) {
                //if (req.body.course[i].Practical_Credit_Hours != 0) {
                const session_ind = session_delivery_type_data.findIndex(
                    (ele) => ele.session_name == 'Practical',
                );
                if (session_ind != -1) {
                    //if (req.body.course[i].Practical_Credit_Hours != 0) {
                    const del_type_arr = [];
                    for (del_type_data of session_delivery_type_data[session_ind].delivery_types) {
                        const obj = {
                            _delivery_id: del_type_data._id,
                            delivery_type: del_type_data.delivery_name,
                            delivery_symbol: del_type_data.delivery_symbol,
                            duration: del_type_data.delivery_duration,
                            isActive: true,
                        };
                        del_type_arr.push(obj);
                    }

                    const practicals_obj = {
                        type_name: 'Practical',
                        type_symbol: 'P',
                        credit_hours: req.body.course[i].Practical_Credit_Hours,
                        contact_hours: req.body.course[i].Practical_Contact_hours_per_credit_hour,
                        duration_split: true,
                        duration: req.body.course[i].Practical_duration_in_minutes,
                        duration_per_contact_hour: req.body.course[i].Practical_per_contact_hour,
                        _session_id: session_delivery_type_data[session_ind]._id,
                        delivery_type: del_type_arr,
                    };
                    credit_hours.push(practicals_obj);
                    //}
                } /* else
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                'Practical not found',
                                [],
                            ),
                        ); */
                //}
            } else
                return res
                    .status(409)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            409,
                            false,
                            req.t('CHECK_PRACTICAL_CREDIT_HOURS'),
                            [],
                        ),
                    );

            if (Number.isInteger(req.body.course[i].Clinical_Credit_Hours)) {
                //if (req.body.course[i].Clinical_Credit_Hours != 0) {
                const session_ind = session_delivery_type_data.findIndex(
                    (ele) => ele.session_name == 'Clinical',
                );
                if (session_ind != -1) {
                    //if (req.body.course[i].Clinical_Credit_Hours != 0) {
                    const del_type_arr = [];
                    for (del_type_data of session_delivery_type_data[session_ind].delivery_types) {
                        const obj = {
                            _delivery_id: del_type_data._id,
                            delivery_type: del_type_data.delivery_name,
                            delivery_symbol: del_type_data.delivery_symbol,
                            duration: del_type_data.delivery_duration,
                            isActive: true,
                        };
                        del_type_arr.push(obj);
                    }

                    const clinical_obj = {
                        type_name: 'Clinical',
                        type_symbol: 'C',
                        credit_hours: req.body.course[i].Clinical_Credit_Hours,
                        contact_hours: req.body.course[i].Clinical_Contact_hours_per_credit_hour,
                        duration_split: true,
                        duration: req.body.course[i].Clinical_duration_in_minutes,
                        duration_per_contact_hour: req.body.course[i].Clinical_per_contact_hour,
                        _session_id: session_delivery_type_data[session_ind]._id,
                        delivery_type: del_type_arr,
                    };
                    credit_hours.push(clinical_obj);
                    //}
                } /* else
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                'Clinical not found',
                                [],
                            ),
                        ); */
                //}
            } else
                return res
                    .status(409)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            409,
                            false,
                            req.t('CHECK_CLINICAL_CREDIT_HOURS'),
                            [],
                        ),
                    );
            const allow_editing =
                req.body.course[i].Allow_to_edit_credit_hours_while_scheduling != false;

            //Add Course Structure
            const obj = {
                insertOne: {
                    document: {
                        _institution_id: ObjectId(req.headers._institution_id),
                        _program_id: program._id,
                        _curriculum_id: curriculum._id,
                        framework: curriculum.framework.name
                            ? curriculum.framework
                            : { domains: [] },
                        course_name: req.body.course[i].Course_Name.trim(),
                        isActive: true,
                        course_code: req.body.course[i].Course_Code.trim(),
                        duration: req.body.course[i].Duration_in_weeks,
                        course_occurring:
                            import_data[i].course_occurring !== undefined
                                ? import_data[i].course_occurring
                                : undefined,
                        course_recurring:
                            import_data[i].course_recurring !== undefined
                                ? import_data[i].course_recurring
                                : undefined,
                        participating: import_data[i].participating,
                        administration: import_data[i].administration,
                        course_type: req.body.course[i].Course_Type,
                        credit_hours,
                        allow_editing,
                        achieve_target: req.body.course[i].Should_Achieve_target_credit_hours,
                    },
                },
            };

            //Import course assign Structure
            const course_assign_obj_local = [];
            const level_data = req.body.course[i].Level;
            let level_d = [];
            if (req.body.course[i].Course_Type === 'selective') {
                level_d = import_data[i].course_recurring.map(
                    (reccurElement) => reccurElement.level_no,
                );
            } else {
                level_d = level_data.toString().split(',');
            }
            const start_week_arr = req.body.course[i].Start_Week.toString().split(',');
            const course_dur = req.body.course[i].Duration_in_weeks;
            //console.log(level_arr)
            for (let i = 0; i < level_d.length; i++) {
                const level_ind = level_arr.findIndex(
                    (ele) => ele.level_name.toLowerCase() == level_d[i].trim().toLowerCase(),
                );
                if (level_ind == -1)
                    return res
                        .status(409)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                409,
                                false,
                                req.t('LEVEL_NOT_FOUND'),
                                [],
                            ),
                        );

                /* let course_duration = {
                    start_week: Number(start_week_arr[i]),
                    end_week: Number(start_week_arr[i]) + Number(course_dur),
                    total: Number(start_week_arr[i]) + Number(course_dur) - Number(start_week_arr[i])
                } */
                const course_duration = {
                    start_week: Number(start_week_arr[0]),
                    end_week: Number(start_week_arr[0]) + Number(course_dur) - 1,
                    total: Number(course_dur),
                };

                const obj_course_assign = {
                    _program_id: program._id,
                    program_name: program.name,
                    _curriculum_id: curriculum._id,
                    curriculum_name: curriculum.curriculum_name,
                    _year_id: level_arr[level_ind].year_id,
                    year: level_arr[level_ind].year,
                    _level_id: level_arr[level_ind].level_id,
                    level_no: level_arr[level_ind].level_name,
                    isConfigured: true,
                    course_duration, //Data needed
                    course_shared_with: [], // Data needed
                };
                course_assign_obj_local.push(obj_course_assign);
            }
            obj.insertOne.document.course_assigned_details = course_assign_obj_local;
            course_obj.push(obj);
        }
        const doc = await base_control.bulk_write(digi_course, course_obj);
        updateCourseFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('COURSE_IMPORTED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_IMPORT_COURSE'),
                    doc.data,
                ),
            );
        //return res.send({ course_obj })
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
}
async function validate_data_check_session_flow(
    req,
    res,
    data,
    collection_datas,
    //duplicate_in_excel,
    duplicateDeliverySymbolDeliveryNoStatus,
) {
    const message = [];
    const obj = {};

    const {
        program_data,
        curriculum_data,
        course_data,
        session_delivery_types_data,
        // dept_subj_data,
    } = collection_datas;
    // const { dept_subj_data } = collection_datas;

    let program = '';
    let course = '';

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) => ele.name.toLowerCase() == data.Program_Name.toLowerCase(),
    );
    if (program_name_ind == -1) message.push(req.t('CHECK_PROGRAM_NAME'));
    else {
        program = program_data.data[program_name_ind];
        //Curriculum Check
        const curriculum_ind = curriculum_data.data.findIndex(
            (ele) =>
                ele.curriculum_name.toLowerCase() == data.Curriculum_Name.toLowerCase() &&
                ele._program_id.toString() == program_data.data[program_name_ind]._id.toString(),
        );
        if (curriculum_ind == -1) message.push(req.t('CHECK_CURRICULUM'));
        else {
            //course check
            const course_ind = course_data.data.findIndex((courseDataElement) => {
                const courseCodeMatch =
                    courseDataElement.course_code.toLowerCase() === data.Course_Code.toLowerCase();
                const curriculumIdMatch =
                    courseDataElement._curriculum_id.toString() ===
                    curriculum_data.data[curriculum_ind]._id.toString();
                if (courseDataElement.versionName) {
                    const versionNameMatch =
                        courseDataElement.versionName.trim().toLowerCase() ===
                        data.Version_Name.trim().toLowerCase();
                    return courseCodeMatch && curriculumIdMatch && versionNameMatch;
                }
                return courseCodeMatch && curriculumIdMatch;
            });
            if (course_ind == -1) {
                message.push(req.t('CHECK_COURSE_CODE'));
            } else {
                course = course_data.data[course_ind];
                //Year Level validation
                //Level ID From Curriculum
                const level_year_arr = [];
                for (year of curriculum_data.data[curriculum_ind].year_level) {
                    for (lvl of year.levels) {
                        level_year_arr.push({
                            year_id: year._id,
                            year_name: year.y_type,
                            level_id: lvl._id,
                            level_name: lvl.level_name,
                        });
                    }
                }

                //Year and Level Check
                const year_ind = level_year_arr.findIndex(
                    (ele) => ele.year_name.toLowerCase() == data.Year.toLowerCase(),
                );
                if (year_ind == -1) message.push(req.t('CHECK_YEAR'));

                const level_arr = data.Level.split(',');
                const new_level_arr = [];
                for (let i = 0; i < level_arr.length; i++) {
                    const level_ind = level_year_arr.findIndex(
                        (ele) => ele.level_name.toLowerCase() == level_arr[i].toLowerCase(),
                    );
                    if (level_ind == -1) message.push(req.t('CHECK_LEVEL') + level_arr[i]);
                    else
                        new_level_arr.push({
                            level_id: level_year_arr[level_ind].level_id,
                            level_no: level_year_arr[level_ind].level_name,
                        });
                }

                //Delivery Symbol and Delivery Number
                if (duplicateDeliverySymbolDeliveryNoStatus)
                    message.push(
                        req.t('DELIVERY_SYMBOL_AND_DELIVERY_NUMBER_DUPLICATE') +
                            data.Delivery_Symbol +
                            ' ' +
                            data.Delivery_Number,
                    );

                //s_no
                if (!Number.isInteger(data.s_no))
                    message.push(req.t('S_NO_NUMBER_SHOULD_BE_INTEGER'));

                //Week
                const week = [];
                const delivery_weeks = data.Delivery_Weeks.toString().split(',');
                if (level_arr.length != delivery_weeks.length) {
                    message.push(req.t('LEVELS_AND_DELIVERY_WEEKS_MISMATCH'));
                } else {
                    if (
                        new_level_arr.length == level_arr.length &&
                        new_level_arr.length == delivery_weeks.length
                    ) {
                        for (let i = 0; i < new_level_arr.length; i++) {
                            week.push({
                                level_id: new_level_arr[i].level_id,
                                level_no: new_level_arr[i].level_no,
                                week_no: delivery_weeks[i],
                            });
                        }
                    }
                }
                obj.week = week;

                //Session and Delivery type Check
                const new_session_delivery_types_data = session_delivery_types_data.data.filter(
                    (ele) =>
                        ele._program_id.toString() ==
                        program_data.data[program_name_ind]._id.toString(),
                );
                let flag = 0;
                for (sess_data of new_session_delivery_types_data) {
                    const del_type_ind = sess_data.delivery_types.findIndex(
                        (ele) =>
                            ele.delivery_name.toLowerCase() == data.Delivery_Type.toLowerCase() &&
                            ele.delivery_symbol.toLowerCase() == data.Delivery_Symbol.toLowerCase(),
                    );
                    if (del_type_ind != -1) {
                        flag = 1;
                        obj._session_id = sess_data._id;
                        obj.delivery_type = sess_data.delivery_types[del_type_ind].delivery_name;
                        obj._delivery_id = sess_data.delivery_types[del_type_ind]._id;
                        obj.delivery_symbol =
                            sess_data.delivery_types[del_type_ind].delivery_symbol;
                        obj.delivery_no = sess_data.delivery_types[del_type_ind].delivery_no;
                        obj.delivery_topic = sess_data.delivery_types[del_type_ind].delivery_topic;
                        break;
                    }
                }
                if (flag == 0) message.push(req.t('CHECK_DELIVERY_TYPE_AND_SYMBOL'));

                if (flag == 1) {
                    let delTypeFlag = 0;
                    for (const eleCreditHours of course_data.data[course_ind].credit_hours) {
                        const delTypeInd = eleCreditHours.delivery_type.findIndex(
                            (ele) =>
                                ele.delivery_type.toLowerCase() ===
                                    data.Delivery_Type.toLowerCase() &&
                                ele.delivery_symbol.toLowerCase() ===
                                    data.Delivery_Symbol.toLowerCase(),
                        );
                        if (delTypeInd != -1) {
                            delTypeFlag = 1;
                            if (eleCreditHours.credit_hours === 0) {
                                message.push(
                                    req.t('CREDIT_HOURS_FOR_SESSION_TYPE_IS') +
                                        eleCreditHours.credit_hours +
                                        req.t('SO_YOU_CAN_NOT_PUSH_DELIVERY_TYPE'),
                                );
                            }
                            break;
                        }
                    }
                }

                //SLO
                const slo = [];

                //Delivery Number Validation
                if (!Number.isInteger(data.Delivery_Number))
                    message.push(req.t('CHECK_DELIVERY_NUMBER_SHOULD_BE_INTEGER'));
                else {
                    const desc_arr = data.slo_description.split(',');
                    for (let i = 0; i < desc_arr.length; i++) {
                        slo.push({
                            no: data.Delivery_Number.toString() + '.' + (i + 1),
                            name: desc_arr[i],
                        });
                    }
                }
                obj.slo = slo;

                //Delivery Topics
                if (data.Delivery_Topic == '') message.push(req.t('CHECK_DELIVERY_TOPIC'));

                //Subject check
                const subjects = [];
                const subject_arr = data.Subject.split(',');
                if (course_ind != -1) {
                    for (let i = 0; i < subject_arr.length; i++) {
                        const participate_ind = course.participating.findIndex(
                            (ele) =>
                                ele.subject_name.toLowerCase() ==
                                subject_arr[i].trim().toLowerCase(),
                        );
                        if (participate_ind == -1)
                            message.push(req.t('SUBJECT_NOT_FOUND') + subject_arr[i]);
                        else {
                            subj_flag = 1;
                            subjects.push({
                                subject_name: course.participating[participate_ind].subject_name,
                                _subject_id: course.participating[participate_ind]._subject_id,
                            });
                        }
                    }
                    obj.subjects = subjects;
                }

                /*  //Subject
             dept_subj_data = dept_subj_data.data.filter(
                 (ele) =>
                     ele.program_id.toString() == program_data.data[program_name_ind]._id.toString(),
             );
             let subj_ind = -1;
             const subjects = [];
             const subject_arr = data.Subject.split(',');
             for (let i = 0; i < subject_arr.length; i++) {
                 let subj_flag = 0;
                 for (dept of dept_subj_data) {
                     subj_ind = dept.subject.findIndex(
                         (ele) => ele.subject_name.toLowerCase() == subject_arr[i].toLowerCase(),
                     );
                     if (subj_ind != -1) {
                         subj_flag = 1;
                         subjects.push({
                             subject_name: dept.subject[subj_ind].subject_name,
                             _subject_id: dept.subject[subj_ind]._id,
                         });
                         break;
                     }
                 }
                 if (subj_flag == 0) message.push('Subject not found: ' + subject_arr[i]);
             }
             obj.subjects = subjects; */
                //Duration Min
                if (!Number.isInteger(data.Duration_Min))
                    message.push(req.t('CHECK_DURATION_MINUTES'));
            }
        }
    }
    return { message, data: obj, other_datas: { program, course } };
}
async function data_check_import_session_flow(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Curriculum List
        const query_curriculum = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const curriculum_data = await base_control.get_list(digi_curriculum, query_curriculum, {});
        if (!curriculum_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        [],
                    ),
                );

        // Course List
        //let query_course = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
        const query_course = { isDeleted: false };
        const course_data = await base_control.get_list(digi_course, query_course, {});
        if (!course_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        [],
                    ),
                );
        //return res.send(course_data);

        // Session Delivery Type List
        //let query_session_delivery_types = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
        const query_session_delivery_types = { isDeleted: false };
        const session_delivery_types_data = await base_control.get_list(
            digi_session_delivery_types,
            query_session_delivery_types,
            {},
        );
        if (!session_delivery_types_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'),
                        [],
                    ),
                );

        //Department and subject data
        //let query_department_subject = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
        const query_department_subject = { isDeleted: false };
        const dept_subj_data = await base_control.get_list(
            department_subject,
            query_department_subject,
            {},
        );
        if (!dept_subj_data.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DEPARTMENT_NOT_FOUND'),
                        [],
                    ),
                );

        // const collection_datas = {
        //     program_data,
        //     curriculum_data,
        //     course_data,
        //     session_delivery_types_data,
        //     dept_subj_data,
        // };

        // //Empty Validation
        // const optional_field = [];
        // const empty_validation_check = field_empty_validation(
        //     req.body.session_flow,
        //     optional_field,
        // );
        // if (empty_validation_check.status)
        //     return res
        //         .status(200)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 200,
        //                 false,
        //                 'Required field validation failed',
        //                 empty_validation_check.message,
        //             ),
        //         );

        const collection_datas = {
            program_data,
            curriculum_data,
            course_data,
            session_delivery_types_data,
            dept_subj_data,
        };

        //Empty Validation
        const optional_field = ['slo_description'];
        const empty_validation_check = field_empty_validation(
            req.body.session_flow,
            optional_field,
        );
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.session_flow) {
            const session_flow_duplicate_status = false;
            //Check duplicate in Delivery symbol and Delivery no
            const filteredArr = req.body.session_flow.filter(
                (ele) =>
                    ele &&
                    ele.Delivery_Symbol.toString() === data.Delivery_Symbol.toString() &&
                    ele.Delivery_Number.toString() === data.Delivery_Number.toString(),
            );
            let duplicateDeliverySymbolDeliveryNoStatus = false;
            if (filteredArr.length > 1) duplicateDeliverySymbolDeliveryNoStatus = true;
            if (session_flow_duplicate_status) {
                //Duplicate in Excel
                validation = await validate_data_check_session_flow(
                    req,
                    res,
                    data,
                    collection_datas,
                    //(duplicate_in_excel = true),
                    duplicateDeliverySymbolDeliveryNoStatus,
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check_session_flow(
                    req,
                    res,
                    data,
                    collection_datas,
                    //(duplicate_in_excel = false),
                    duplicateDeliverySymbolDeliveryNoStatus,
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });

            //return res.send({ course_code_dup_status, course_name_dup_status })
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const local_data_check_session_flow = async function (req, res) {
    const collections_error_message = [];
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status) collections_error_message.push(req.t('INSTITUTION_NOT_FOUND'));

    //Program List
    const query_program = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const program_data = await base_control.get_list(program, query_program, {});
    if (!program_data.status) collections_error_message.push(req.t('PROGRAM_NOT_FOUND'));

    // Curriculum List
    const query_curriculum = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const curriculum_data = await base_control.get_list(digi_curriculum, query_curriculum, {});
    if (!curriculum_data.status) collections_error_message.push(req.t('CURRICULUM_NOT_FOUND'));

    // Course List
    //let query_course = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
    const query_course = { isDeleted: false };
    const course_data = await base_control.get_list(digi_course, query_course, {});
    if (!course_data.status) collections_error_message.push(req.t('COURSE_NOT_FOUND'));

    //return res.send(course_data);

    // Session Delivery Type List
    //let query_session_delivery_types = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
    const query_session_delivery_types = { isDeleted: false };
    const session_delivery_types_data = await base_control.get_list(
        digi_session_delivery_types,
        query_session_delivery_types,
        {},
    );
    if (!session_delivery_types_data.status)
        collections_error_message.push(req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'));

    //Department and subject data
    //let query_department_subject = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
    const query_department_subject = { isDeleted: false };
    const dept_subj_data = await base_control.get_list(
        department_subject,
        query_department_subject,
        {},
    );
    if (!dept_subj_data.status) collections_error_message.push(req.t('DEPARTMENT_NOT_FOUND'));

    const collection_datas = {
        program_data,
        curriculum_data,
        course_data,
        session_delivery_types_data,
        dept_subj_data,
    };

    const valid_data = [];
    const invalid_data = [];
    const import_data = [];
    let pgm = [];
    let course = [];
    for (data of req.body.session_flow) {
        const session_flow_duplicate_status = false;
        if (session_flow_duplicate_status) {
            //Duplicate in Excel
            validate = await validate_data_check_session_flow(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = true),
            );
        } else {
            //No Duplicate in Excel
            validate = await validate_data_check_session_flow(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = false),
            );
        }
        if (validate.message.length > 0) invalid_data.push({ data, message: validate.message });
        else valid_data.push({ data, message: validate.message });

        import_data.push(validate.data);
        pgm = validate.other_datas.program;
        course = validate.other_datas.course;

        //return res.send({ course_code_dup_status, course_name_dup_status })
    }
    const d = {
        valid_data,
        invalid_data,
        other_error_message: collections_error_message,
        import_data,
        other_datas: { pgm, course },
    };
    if (invalid_data.length > 0) return { status: false, data: d };
    return { status: true, data: d };
};
async function import_session_flow(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const data_check = await local_data_check_session_flow(req, res);
        if (!data_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                    ),
                );
        //return res.send(data_check);

        let obj = {};
        const session_flow_arr = [];
        for (let i = 0; i < req.body.session_flow.length; i++) {
            obj = {
                _session_id: data_check.data.import_data[i]._session_id,
                s_no: req.body.session_flow[i].s_no,
                delivery_type: req.body.session_flow[i].Delivery_Type.trim(),
                _delivery_id: data_check.data.import_data[i]._delivery_id,
                delivery_symbol: req.body.session_flow[i].Delivery_Symbol.trim(),
                delivery_no: req.body.session_flow[i].Delivery_Number,
                delivery_topic: req.body.session_flow[i].Delivery_Topic.trim(),
                subjects: data_check.data.import_data[i].subjects,
                duration: req.body.session_flow[i].Duration_Min,
                week: data_check.data.import_data[i].week,
                slo:
                    req.body.session_flow[i].slo_description.trim() == ''
                        ? undefined
                        : data_check.data.import_data[i].slo,
            };
            session_flow_arr.push(obj);
        }
        /* let session_flow_obj = {
                insertOne: {
                    document: {
                        "session_flow": session_flow_arr,
                        "_course_id": data_check.data.other_datas.course._id,
                        "_program_id": data_check.data.other_datas.pgm._id
                    }
                }
            } */
        const session_flow_obj = {
            _institution_id: ObjectId(req.headers._institution_id),
            session_flow_data: session_flow_arr,
            _course_id: data_check.data.other_datas.course._id,
            _program_id: data_check.data.other_datas.pgm._id,
        };

        //Check session flow already exist for course

        /* const session_flow_check = await base_control.get(
            digi_session_order,
            { _course_id: data_check.data.other_datas.course._id },
            { _id: 1 },
        );
        if (session_flow_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        'Session flow already exist for this course',
                        'Session flow already exist for this course',
                    ),
                ); */

        //return res.send(session_flow_obj);
        const doc = await base_control.insert(digi_session_order, session_flow_obj);
        updateSessionFlowFlatCacheData();
        //let doc = await base_control.bulk_write(digi_session_order, session_flow_obj);
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('SESSION_FLOW_IMPORTED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_IMPORT_SESSION_FLOW'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
}
async function validate_data_check_slo(
    req,
    res,
    data,
    collection_datas,
    // duplicate_in_excel,
) {
    const message = [];
    const obj = {};

    const { program_data, course_data, session_flow_data } = collection_datas;

    let program = '';
    let course = '';
    let session_flow = '';

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) => ele.name.toLowerCase() == data.Program_Name.toLowerCase(),
    );
    if (program_name_ind == -1) message.push('Check program name');
    else {
        program = program_data.data[program_name_ind];

        //course check
        const course_ind = course_data.data.findIndex(
            (ele) => ele.course_code.toLowerCase() == data.Course_Code.toLowerCase(),
        );
        if (course_ind == -1) {
            message.push(req.t('CHECK_COURSE_CODE'));
        } else {
            course = course_data.data[course_ind];
            //Session flow Delivery symbol and delivery number check
            let session_flow_ind = -1;
            if (course_ind != -1)
                session_flow_ind = session_flow_data.data.findIndex(
                    (ele) =>
                        ele._course_id.toString() === course_data.data[course_ind]._id.toString(),
                );
            if (session_flow_ind == -1)
                message.push(req.t('SESSION_FLOW_NOT_FOUND_FOR_THIS_COURSE'));
            else session_flow = session_flow_data.data[session_flow_ind];

            if (session_flow.session_flow_data.length > 0) {
                const session_flow_data_ind = session_flow.session_flow_data.findIndex(
                    (ele) =>
                        ele._id &&
                        ele.delivery_symbol.toString() === data.Delivery_Symbol.toString() &&
                        ele.delivery_no.toString() === data.Delivery_Number.toString(),
                );
                if (session_flow_data_ind == -1)
                    message.push(req.t('DELIVERY_SYMBOL_AND_DELIVERY_NUMBER_NOT_FOUND'));
            } else message.push(req.t('SESSION_FLOW_DATA_NOT_FOUND'));

            const slo = [];
            //Delivery Number Validation
            if (!Number.isInteger(data.Delivery_Number))
                message.push(req.t('CHECK_DELIVERY_NUMBER_SHOULD_BE_INTEGER'));
            else {
                const desc_arr = data.slo_description.split('/');
                for (let i = 0; i < desc_arr.length; i++) {
                    slo.push({
                        no: data.Delivery_Number.toString() + '.' + (i + 1),
                        name: desc_arr[i],
                    });
                }
            }
            obj.slo = slo;
        }
    }
    return { message, data: obj, other_datas: { program, course } };
}
async function data_check_slo(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Course List
        const query_course = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const course_data = await base_control.get_list(digi_course, query_course, {});
        if (!course_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('COURSE_NOT_FOUND'),
                        [],
                    ),
                );

        // Session flow list
        const query_session_flow = {
            ...common_files.query,
            _institution_id: ObjectId(req.headers._institution_id),
        };
        const session_flow_data = await base_control.get_list(
            digi_session_order,
            query_session_flow,
            {},
        );
        if (!session_flow_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('SESSION_FLOW_NOT_FOUND'),
                        [],
                    ),
                );

        const collection_datas = {
            program_data,
            course_data,
            session_flow_data,
        };

        //Empty Validation
        const optional_field = [];
        const empty_validation_check = field_empty_validation(req.body.slo, optional_field);
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.slo) {
            const session_flow_duplicate_status = false;
            if (session_flow_duplicate_status) {
                //Duplicate in Excel
                validation = await validate_data_check_slo(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check_slo(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });

            //return res.send({ course_code_dup_status, course_name_dup_status })
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const local_data_check_slo = async function (req, res) {
    const collections_error_message = [];
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status) collections_error_message.push(req.t('INSTITUTION_NOT_FOUND'));

    //Program List
    const query_program = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const program_data = await base_control.get_list(program, query_program, {});
    if (!program_data.status) collections_error_message.push(req.t('PROGRAM_NOT_FOUND'));

    // Course List
    const query_course = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };

    const course_data = await base_control.get_list(digi_course, query_course, {});
    if (!course_data.status) collections_error_message.push(req.t('COURSE_NOT_FOUND'));

    // Session flow list
    const query_session_flow = {
        ...common_files.query,
        _institution_id: ObjectId(req.headers._institution_id),
    };
    const session_flow_data = await base_control.get_list(
        digi_session_order,
        query_session_flow,
        {},
    );
    if (!session_flow_data.status)
        return res
            .status(409)
            .send(
                common_files.response_function(
                    res,
                    409,
                    false,
                    req.t('SESSION_FLOW_NOT_FOUND'),
                    [],
                ),
            );

    const collection_datas = {
        program_data,
        course_data,
        session_flow_data,
    };

    const valid_data = [];
    const invalid_data = [];
    const import_data = [];
    let pgm = [];
    let course = [];
    for (data of req.body.slo) {
        const session_flow_duplicate_status = false;
        if (session_flow_duplicate_status) {
            //Duplicate in Excel
            validate = await validate_data_check_slo(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = true),
            );
        } else {
            //No Duplicate in Excel
            validate = await validate_data_check_slo(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = false),
            );
        }
        if (validate.message.length > 0) invalid_data.push({ data, message: validate.message });
        else valid_data.push({ data, message: validate.message });

        import_data.push(validate.data);
        pgm = validate.other_datas.program;
        course = validate.other_datas.course;

        //return res.send({ course_code_dup_status, course_name_dup_status })
    }
    const d = {
        valid_data,
        invalid_data,
        other_error_message: collections_error_message,
        import_data,
        other_datas: { pgm, course },
    };
    if (invalid_data.length > 0) return { status: false, data: d };
    return { status: true, data: d };
};
async function import_slo(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const data_check = await local_data_check_slo(req, res);
        //return res.send(data_check);
        if (!data_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                    ),
                );
        //Session flow data
        const course_details = data_check.data.other_datas.course;
        const import_data = data_check.data.import_data;
        const session_flow_data = await base_control.get(
            digi_session_order,
            { ...common_files.query, _course_id: ObjectId(course_details._id) },
            {},
        );
        const bulk_data = [];
        let i = 0;
        for (const slo of req.body.slo) {
            const session_flow_ind = session_flow_data.data.session_flow_data.findIndex(
                (ele) =>
                    ele.delivery_symbol.toString() === slo.Delivery_Symbol.toString() &&
                    ele.delivery_no.toString() === slo.Delivery_Number.toString(),
            );
            if (session_flow_ind != -1) {
                bulk_data.push({
                    updateOne: {
                        filter: {
                            _course_id: ObjectId(course_details._id),
                        },
                        update: {
                            $set: {
                                'session_flow_data.$[i].slo': import_data[i].slo,
                            },
                        },
                        arrayFilters: [
                            {
                                'i._id':
                                    session_flow_data.data.session_flow_data[session_flow_ind]._id,
                            },
                        ],
                    },
                });
            }
            i++;
        }
        if (bulk_data.length > 0) await base_control.bulk_write(digi_session_order, bulk_data);
        updateSessionFlowFlatCacheData();
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('SUCCESSFULLY_IMPORTED'),
                    req.t('SUCCESSFULLY_IMPORTED'),
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function courseVersion(req, res) {
    try {
        const { id } = req.params;
        const courseQuery = {
            isDeleted: false,
            $or: [
                {
                    _id: convertToMongoObjectId(id),
                },
                {
                    'versioning._course_id': { $in: convertToMongoObjectId(id) },
                },
            ],
        };
        const courseDetails = await digi_course
            .find(courseQuery, { __v: 0, createdAt: 0, updatedAt: 0 })
            .lean();
        // Version Details Adding
        const currentCourse = courseDetails.find(
            (courseElement) => courseElement._id.toString() === id.toString(),
        );
        let courseVersions = [];
        let courseVersionNo = 2;
        if (currentCourse && currentCourse.versioning && currentCourse.versioning.length) {
            courseVersions = currentCourse.versioning;
            courseVersionNo = currentCourse.versioning.length + 1;
        } else {
            courseVersions.push({
                _course_id: convertToMongoObjectId(id),
                versionNo: 1,
            });
        }

        const courseVersionObjects = {
            ...currentCourse,
            ...req.body,
            ...{ versionNo: courseVersionNo, versioning: courseVersions },
        };
        delete courseVersionObjects._id;
        // New Version Course Insert
        const courseVersioned = await base_control.insert(digi_course, courseVersionObjects);
        if (!courseVersioned.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_ADD_COURSE'),
                        courseVersioned,
                    ),
                );

        // Versioned Course Session Order Insert
        const masterCourseSessionOrderObject = await digi_session_order
            .findOne(
                { ...common_files.query, _course_id: convertToMongoObjectId(id) },
                { __v: 0, createdAt: 0, updatedAt: 0 },
            )
            .lean();
        if (masterCourseSessionOrderObject && masterCourseSessionOrderObject.session_flow_data) {
            await base_control.insert(digi_session_order, {
                _course_id: convertToMongoObjectId(courseVersioned.responses._id),
                _program_id: convertToMongoObjectId(masterCourseSessionOrderObject._program_id),
                _institution_id: convertToMongoObjectId(
                    masterCourseSessionOrderObject._institution_id,
                ),
                session_flow_data: masterCourseSessionOrderObject.session_flow_data,
            });
        }
        courseVersions.push({
            _course_id: convertToMongoObjectId(courseVersioned.responses._id),
            versionNo: courseVersionNo,
        });

        // Updating Course Version Object In All matching courseMaster Id
        const courseVersionUpdate = await base_control.update_push_pull_many(
            digi_course,
            courseQuery,
            { versioning: courseVersions, versioned: true },
        );
        updateCourseFlatCacheData();
        updateSessionFlowFlatCacheData();
        if (courseVersionUpdate.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('COURSE_ADDED_SUCCESSFULLY'),
                        req.t('COURSE_ADDED_SUCCESSFULLY'),
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(res, 200, false, req.t('UNABLE_TO_ADD_COURSE'), []),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
const getSessionText = async (req, res) => {
    try {
        const { courseId, sessionId } = req.query;
        const { _institution_id } = req.headers;
        const sessionDetails = await digi_session_order
            .findOne(
                {
                    ...common_files.query,
                    _institution_id: ObjectId(_institution_id),
                    _course_id: ObjectId(courseId),
                    'session_flow_data._id': ObjectId(sessionId),
                },
                { session_flow_data: { $elemMatch: { _id: ObjectId(sessionId) } } },
            )
            .lean();
        const sessionDataDetails = {
            sessionData: '',
            slo: [],
            sessionDocumentDetails: [],
        };
        if (
            sessionDetails &&
            sessionDetails.session_flow_data &&
            sessionDetails.session_flow_data[0]
        ) {
            const sessionContentAndDocumentDetails = sessionDetails.session_flow_data[0];
            const {
                sessionData = '',
                slo = [],
                sessionDocumentDetails = [],
            } = sessionContentAndDocumentDetails;
            sessionDataDetails.sessionData = sessionData;
            sessionDataDetails.slo = slo;
            sessionDataDetails.sessionDocumentDetails = sessionDocumentDetails;
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('Session Content and Document Details'),
                    sessionDataDetails,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
};
const uploadSessionDocument = async (req, res) => {
    try {
        const {
            body: { file },
        } = req;
        const fileName = file.split('/').pop();
        const responseData = { fileName, url: file, uploadedAt: new Date() };
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('Session Documents Added Successfully'),
                    responseData,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
const getSessionDocumentSignedUrl = async (req, res) => {
    try {
        const {
            query: { url },
        } = req;
        const signedUrl = await getSignedURL(url);
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('Signed url'),
                    signedUrl,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
};
module.exports = {
    insert,
    insert_draft,
    update,
    list,
    program_course_list,
    delete_course,
    program_level_list,
    program_curriculum_year_level_list,
    program_session_delivery_type_list,
    insert_session_flow,
    update_session_flow,
    list_id_session_flow,
    list_id,
    assign_course,
    assign_course_edit,
    assign_course_delete,
    course_assigned_list,
    course_assigned_list_map_view,
    get_course_assigned_details,
    course_list_recurring_level,
    curriculum_wise_course_list,
    program_list_by_course_type,
    import_course_with_assign,
    data_check_import_course,
    import_session_flow,
    data_check_import_session_flow,
    data_check_slo,
    import_slo,
    courseVersion,
    getSessionText,
    uploadSessionDocument,
    getSessionDocumentSignedUrl,
};
