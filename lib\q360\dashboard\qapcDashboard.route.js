const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    createCategoryItems,
    getDashboardCategorySettings,
    updateCategoryItems,
    getQADashboard,
    getIncorporateSection,
} = require('./qapcDashboard.controller');
const validator = require('./qapcDashboard.validator');
const { validate } = require('../../../middleware/validation');

router.post(
    '/createCategoryItems',
    validate(validator.createCategoryItemsValidator),
    catchAsync(createCategoryItems),
);
router.get(
    '/getDashboardCategorySettings',
    validate(validator.dashboardCategorySettingsValidator),
    catchAsync(getDashboardCategorySettings),
);
router.put(
    '/updateCategoryItems',
    validate(validator.updateCategoryItemsValidator),
    catchAsync(updateCategoryItems),
);
router.get(
    '/getQADashboard',
    validate(validator.getQADashboardValidator),
    catchAsync(getQADashboard),
);
router.post('/getIncorporateSection', catchAsync(getIncorporateSection));

module.exports = router;
