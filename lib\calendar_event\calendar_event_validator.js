// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.institution = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    event_calendar: Joi.string()
                        .valid(constant.CALENDAR.INSTITUTION, constant.CALENDAR.PROGRAM)
                        .required(),
                    event_type: Joi.string()
                        .valid(
                            constant.EVENT_TYPE.HOLIDAY,
                            constant.EVENT_TYPE.ORIENTATION,
                            constant.EVENT_TYPE.TRAINING,
                            constant.EVENT_TYPE.EXAM,
                            constant.EVENT_TYPE.GENERAL,
                        )
                        .required(),
                    event_whom: Joi.string().valid(
                        constant.EVENT_WHOM.STAFF,
                        constant.EVENT_WHOM.STUDENT,
                        constant.EVENT_WHOM.BOTH,
                    ),
                    event_name: Joi.object().keys({
                        first_language: Joi.string().min(1).max(2500).allow(''),
                        second_language: Joi.string().min(1).max(2500).allow(''),
                    }),
                    event_description: Joi.object().keys({
                        first_language: Joi.string().min(1).max(2500).allow(''),
                        second_language: Joi.string().min(1).max(2500).allow(''),
                    }),
                    event_date: Joi.date().required(),
                    // start_time: Joi.string().regex(/^([0-9]{2})\:([0-9]{2})\:([0-9]{2})$/).error(error => {
                    //     return error;
                    // }),
                    // end_time: Joi.string().regex(/^([0-9]{2})\:([0-9]{2})$/).error(error => {
                    //     return error;
                    // }),
                    start_time: Joi.date().required(),
                    end_time: Joi.date().required(),
                    end_date: Joi.date().required(),
                    _infrastructure_id: Joi.string().alphanum().length(24).allow(''),
                    _calendar_id: Joi.string().alphanum().length(24).required(),
                    // review: Joi.object().keys({
                    //     _reviewer_ids: Joi.array().items(Joi.string().alphanum().length(24)).error(error => {
                    //         return error;
                    //     }),
                    //     expire: Joi.object().keys({
                    //         expire_date: Joi.date().error(error => {
                    //             return error;
                    //         }),
                    //         expire_time: Joi.date().error(error => {
                    //             return error;
                    //         })
                    //     })
                    // })
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            res,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_update_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                id: Joi.string().alphanum().length(24).required(),
            }),
            body: Joi.object()
                .keys({
                    event_type: Joi.string().min(1).max(20),
                    event_name: Joi.object().keys({
                        first_language: Joi.string().min(1).max(250),
                        second_language: Joi.string().allow('').min(1).max(250),
                    }),
                    event_description: Joi.object().keys({
                        first_language: Joi.string().min(1).max(2500),
                        second_language: Joi.string().allow('').min(1).max(2500),
                    }),
                    event_date: Joi.date(),
                    start_time: Joi.date(),
                    end_time: Joi.date(),
                    end_date: Joi.date(),
                    _infrastructure_id: Joi.string().alphanum().length(24),
                    _calendar_id: Joi.string().alphanum().length(24),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_add_reviewer = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                id: Joi.string().alphanum().length(24).required(),
            }),
            body: Joi.object()
                .keys({
                    _reviewer_id: Joi.array().items(Joi.string().alphanum().length(24)).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.remove_reviewer = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string().alphanum().length(24).required(),
                    reviewer: Joi.string().alphanum().length(24).required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_add_reviewer_review = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    reviewer_type: Joi.string().valid(constant.DEAN, constant.REVIEWER).required(),
                    _event_id: Joi.string().alphanum().length(24).required(),
                    _reviewer_id: Joi.string().alphanum().length(24).required(),
                    review: Joi.boolean().required(),
                    review_comment: Joi.string().min(1).allow(''),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};

exports.institution_event_with_filter = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                id: Joi.string().alphanum().length(24).required(),
            }),
            query: Joi.object()
                .keys({
                    start_date: Joi.date().required(),
                    end_date: Joi.date().required(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.comResponseWithRequest(
            req,
            422,
            false,
            req.t('VALIDATION_ERROR'),
            errorMessage,
        );
    }
    next();
};
