const { Joi } = require('../../common/middlewares/validation');
const { objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const getUserCoursesSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema.required(),
        type: Joi.string().valid('student', 'staff').required(),
    }),
}).unknown(true);

const getStudentGroupSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
    }),
}).unknown(true);

const getFacultyListSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema,
        programId: objectIdRQSchema,
    }),
}).unknown(true);

const getCoursesByProgramSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema.required(),
    }),
}).unknown(true);

const getPortfolioSchema = Joi.object({
    query: Joi.object({
        programId: objectIdRQSchema.required(),
        courseId: objectIdRQSchema.required(),
        institutionCalendarId: objectIdRQSchema.required(),
    }),
}).unknown(true);

const getDeliveryTypesSchema = Joi.object({
    query: Joi.object({
        courseId: objectIdRQSchema.required(),
    }),
}).unknown(true);

const getCourseDetailsSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectIdRQSchema.required(),
    }),
}).unknown(true);

module.exports = {
    getUserCoursesSchema,
    getStudentGroupSchema,
    getFacultyListSchema,
    getCoursesByProgramSchema,
    getPortfolioSchema,
    getDeliveryTypesSchema,
    getCourseDetailsSchema,
};
