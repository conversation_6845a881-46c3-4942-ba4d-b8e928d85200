const multer = require('multer');
const {
    uploadInstituteMedia,
    uploadInstituteLogo: uploadInstituteFileLogo,
} = require('../../utility/file-upload');
const keys = require('../../utility/util_keys');
const { getS3SignedUrl } = require('../../services/aws.service');
const logoUpload = uploadInstituteFileLogo.fields([{ name: 'logo', maxCount: 1 }]);
const mediaUpload = uploadInstituteMedia.fields([{ name: 'media', maxCount: 1 }]);
const { response_function } = require('../../utility/common');

const uploadInstituteLogo = (req, res, next) => {
    logoUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        }
        if (err) {
            console.log('>>>upload issue', err);
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    });
};

const uploadInstituteMediaFile = (req, res, next) => {
    mediaUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getPresignedUrlsForInstitutes = async (doc) => {
    let document = { ...doc };
    if (doc._doc) {
        document = { ...doc._doc };
    }
    if (document.logo) {
        const fileName = document.logo.split('/').pop();
        const url = await getS3SignedUrl({
            bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
            key: `logos/` + fileName,
        });
        document.presignedLogoURL = url;
    }
    if (document.instituteDescription && document.instituteDescription.mediaURL) {
        const fileName = document.instituteDescription.mediaURL.split('/').pop();
        const url = await getS3SignedUrl({
            bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
            key: `institute_${document._id}/` + fileName,
        });
        document.instituteDescription.presignedMediaURL = url;
    }
    if (document.portfolio && document.portfolio.length) {
        const promises = document.portfolio.map(async (portfolio) => {
            if (portfolio.mediaURL) {
                const fileName = portfolio.mediaURL.split('/').pop();
                const url = await getS3SignedUrl({
                    bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
                    key: `institute_${document._id}/` + fileName,
                });
                return url;
            }
        });

        const presignedURLS = await Promise.all(promises);
        document.portfolio = document.portfolio.map((portfolio, index) => {
            if (presignedURLS[index]) {
                if (portfolio._doc) {
                    portfolio = { ...portfolio._doc, presignedMediaURL: presignedURLS[index] };
                } else {
                    portfolio = { ...portfolio, presignedMediaURL: presignedURLS[index] };
                }
            }
            return portfolio;
        });
    }
    return document;
};

module.exports = {
    uploadInstituteLogo,
    uploadInstituteMediaFile,
    getPresignedUrlsForInstitutes,
};
