const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
exports.studentGroupingValidation = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            institutionCalendarId: objectId
                .required()
                .error(() => new Error('Institution Calendar Code Invalid')),
            programId: objectId.required().error(() => new Error('Program Code Invalid')),
            term: Joi.string()
                .required()
                .error(() => new Error('Term Code Invalid')),
            year: Joi.string()
                .required()
                .error(() => new Error('Year Code Invalid')),
            level: Joi.string()
                .required()
                .error(() => new Error('Level Code Invalid')),
            courseId: objectId.required().error(() => new Error('Course Code Invalid')),
            studentIds: Joi.array()
                .items(Joi.string())
                .optional()
                .error(() => new Error('Student Code Invalid')),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        return res.status(422).send({
            status_code: 422,
            status: false,
            message: req.t('VALIDATION_ERROR'),
            data: error.message,
        });
    }
    next();
};

exports.classCourseDetailsValidation = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            courseDetails: Joi.array().items(
                Joi.object({
                    subject: Joi.string().required(),
                    catalogNbr: Joi.string().required(),
                    startDate: Joi.string().required(),
                    endDate: Joi.string().required(),
                    classNbr: Joi.number().required(),
                    strm: Joi.string().required(),
                }),
            ),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(422).send({
            status_code: 422,
            status: false,
            message: req.t('VALIDATION_ERROR'),
            data: errors,
        });
    }
    next();
};

exports.staffDurationBasedAvailabilityValidation = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            staffIds: Joi.array()
                .items(Joi.string())
                .required()
                .error((error) => {
                    return Error('Staff IDs are required');
                }),
            startDateAndTime: Joi.string()
                .required()
                .error((error) => {
                    return Error('Start Date and Time are required');
                }),
            endDateAndTime: Joi.string()
                .required()
                .error((error) => {
                    return Error('End Date and Time are required');
                }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        return res.status(422).send({
            status_code: 422,
            status: false,
            message: req.t('VALIDATION_ERROR'),
            data: error.message,
        });
    }
    next();
};
