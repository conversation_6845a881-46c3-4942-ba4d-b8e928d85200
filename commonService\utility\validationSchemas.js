const Joi = require('joi');

// common joi validations schema
const objectIdSchema = Joi.string().alphanum().length(24);
const objectIdRQSchema = Joi.string().alphanum().length(24).required();
const stringSchema = Joi.string().trim().allow('');
const stringRQSchema = Joi.string().trim().required();
const numberSchema = Joi.number();
const numberRQSchema = Joi.number().required();
const booleanSchema = Joi.boolean();
const booleanRQSchema = Joi.boolean().required();
const dateSchema = Joi.string().isoDate();
const dateRQSchema = Joi.string().isoDate().required();
const nonIsoDateFormat = Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/);

module.exports = {
    objectIdSchema,
    objectIdRQSchema,
    stringSchema,
    stringRQSchema,
    numberSchema,
    numberRQSchema,
    booleanSchema,
    booleanRQSchema,
    dateSchema,
    dateRQSchema,
    nonIsoDateFormat,
};
