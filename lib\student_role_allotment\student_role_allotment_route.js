const express = require("express");
const route = express.Router();
const student_role_allotment_controller = require('../student_role_allotment/student_role_allotment_controller')

route.post("/", student_role_allotment_controller.create);
route.get("/", student_role_allotment_controller.read);
route.get("/:filter/:value", student_role_allotment_controller.getFilter);
route.put("/:_id", student_role_allotment_controller.update);
route.delete("/:_id", student_role_allotment_controller.delete);

module.exports = route;