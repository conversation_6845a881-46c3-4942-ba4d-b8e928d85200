let constant = require('../utility/constants');
var course = require('mongoose').model(constant.COURSE);
var department = require('mongoose').model(constant.DEPARTMENT);
var department_division = require('mongoose').model(constant.DEPARTMENT_DIVISIONS);
var department_subject = require('mongoose').model(constant.DEPARTMENT_SUBJECT);
var credit_master = require('mongoose').model(constant.CREDIT_HOURS_MASTER);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const course_formate = require('./course_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: '_administration_department_id',
                foreignField: '_id',
                as: 'administration_department'
            }
        },
        {
            $unwind: {
                path: '$administration_department',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_DIVISIONS,
                localField: '_administration_division_id',
                foreignField: '_id',
                as: 'administration_division'
            }
        },
        {
            $unwind: {
                path: '$administration_division',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: '_administration_subject_id',
                foreignField: '_id',
                as: 'administration_subject'
            }
        },
        {
            $unwind: {
                path: '$administration_subject',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: '_participating_department_id',
                foreignField: '_id',
                as: 'participating_department'
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_DIVISIONS,
                localField: '_participating_division_id',
                foreignField: '_id',
                as: 'participating_division'
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: '_participating_subject_id',
                foreignField: '_id',
                as: 'participating_subject'
            }
        },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc = await base_control.get_aggregate(course, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "course list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ course_formate.course(doc.data));
        // common_files.list_all_response(res, 200, true, "course list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* course_formate.course(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: '_administration_department_id',
                foreignField: '_id',
                as: 'administration_department'
            }
        },
        { $unwind: { path: '$administration_department', preserveNullAndEmptyArrays: true } },
        {
            $lookup: {
                from: constant.DEPARTMENT_DIVISIONS,
                localField: '_administration_division_id',
                foreignField: '_id',
                as: 'administration_division'
            }
        },
        {
            $unwind: {
                path: '$administration_division',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: '_administration_subject_id',
                foreignField: '_id',
                as: 'administration_subject'
            }
        },
        {
            $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: '_participating_department_id',
                foreignField: '_id',
                as: 'participating_department'
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_DIVISIONS,
                localField: '_participating_division_id',
                foreignField: '_id',
                as: 'participating_division'
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: '_participating_subject_id',
                foreignField: '_id',
                as: 'participating_subject'
            }
        }

    ];
    let doc = await base_control.get_aggregate(course, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "course details", /* doc.data */ course_formate.course_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    // console.log(req.body);
    const objs = req.body;
    // console.log(objs);
    let department_id = [], division_id = [], subject_id = [];
    let status, datas;
    let department_check = { status: true }, division_check = { status: true }, subject_check = { status: true };
    let docs = { status: true }, doc_update = { status: true };

    if (req.body._participating_department_id != undefined) {
        if (req.body._participating_department_id.length != 0) {
            department_id = [].concat(req.body._participating_department_id);
        }
    }
    if (req.body._participating_division_id != undefined) {
        if (req.body._participating_division_id.length != 0) {
            division_id = [].concat(req.body._participating_division_id);
        }
    }
    if (req.body._participating_subject_id != undefined) {
        if (req.body._participating_subject_id.length != 0) {
            subject_id = [].concat(req.body._participating_subject_id);
        }
    }

    // console.log(department_id, division_id, subject_id);

    if (req.body._administration_department_id != undefined && req.body._administration_department_id.length != 0) {
        if (department_id.indexOf(req.body._administration_department_id) == -1) {
            department_id.push(req.body._administration_department_id);
        }
    }
    if (req.body._administration_division_id != undefined && req.body._administration_division_id.length != 0) {
        if (req.body._administration_division_id != 0) {
            if (division_id.indexOf(req.body._administration_division_id) == -1) {
                division_id.push(req.body._administration_division_id);
            }
        }
    }
    if (req.body._administration_subject_id != undefined && req.body._administration_subject_id.length != 0) {
        if (subject_id.indexOf(req.body._administration_subject_id) == -1) {
            subject_id.push(req.body._administration_subject_id);
        }
    }
    // console.log(department_id, division_id, subject_id);
    // console.log(department_id.length, division_id.length, subject_id.length);

    if (department_id.length != 0) {
        department_check = await base_control.check_id(department, { _id: { $in: department_id }, 'isDeleted': false });
    }
    if (division_id.length != 0) {
        division_check = await base_control.check_id(department_division, { _id: { $in: division_id }, 'isDeleted': false });
    }
    if (subject_id.length != 0) {
        subject_check = await base_control.check_id(department_subject, { _id: { $in: subject_id }, 'isDeleted': false });
    }
    // console.log(department_check.status, division_check.status, subject_check.status);
    //
    //      Check Program ID is Valid or not
    //
    if (department_check.status && division_check.status && subject_check.status) {

        let objects = {
            courses_name: objs.courses_name,
            study_year: objs.study_year,
            study_level: objs.study_level,
            order: objs.order,
            courses_number: objs.courses_number,
            duration: objs.duration,
            model: objs.model,
            // _administration_department_id: objs._administration_department_id,
            // _administration_division_id: objs._administration_division_id,
            // _administration_subject_id: objs._administration_subject_id,
            // _participating_department_id: objs._participating_department_id,
            // _participating_division_id: objs._participating_division_id,
            // _participating_subject_id: objs._participating_subject_id,
            // elective: {
            //     topics: objs.elective.topics
            // },
            theory_credit: objs.theory_credit,
            practical_credit: objs.practical_credit,
            clinical_credit: objs.clinical_credit,
            _program_id: ObjectId(objs._program_id)
        };
        // console.log(objects);
        if (objs._administration_department_id != undefined) {
            if (objs._administration_department_id.length != 0) {
                objects._administration_department_id = objs._administration_department_id;
            }
        }
        if (objs._administration_division_id != undefined) {
            if (objs._administration_division_id.length != 0) {
                objects._administration_division_id = objs._administration_division_id;
            }
        }
        if (objs._administration_subject_id != undefined) {
            if (objs._administration_subject_id.length != 0) {
                objects._administration_subject_id = objs._administration_subject_id;
            }
        }
        // console.log(objects);
        // console.log(objs._administration_department_id, objs._administration_division_id, objs._administration_subject_id);
        // console.log(objs._administration_department_id.length, objs._administration_division_id.length, objs._administration_subject_id.length);

        if (objs.model == 'elective' && objs.elective != undefined) {
            if (objs.elective.length != 0) {
                let ele = [];
                objs.elective.forEach(elective_obj => {
                    if (elective_obj.topics != undefined) {
                        if (ele.indexOf(elective_obj.topics) == -1) {
                            ele.push({ topics: elective_obj.topics });
                            // objects.elective = { topics: elective_obj.topics };
                        }
                    }
                });
                objects.elective = ele;
            }
        }

        if (objs._participating_department_id != undefined) {
            if (objs._participating_department_id.length != 0) {
                objects._participating_department_id = objs._participating_department_id;
            }
        }
        if (objs._participating_division_id != undefined) {
            if (objs._participating_division_id.level != 0) {
                objects._participating_division_id = objs._participating_division_id;
            }
        }
        if (objs._participating_subject_id != undefined) {
            if (objs._participating_subject_id.level != 0) {
                objects._participating_subject_id = objs._participating_subject_id;
            }
        }

        // console.log(objs);
        if (req.body.id == '' && req.body.id.length == 0) {
            docs = await base_control.insert(course, objects);
            if (docs.status) {
                status = true;
                datas = docs;
            } else {
                datas = docs;
                status = false;
            }
        } else {
            docs = await base_control.update(course, req.body.id, objects);
            if (docs.status && doc_update) {
                status = true;
                datas = docs;
            } else {
                datas = docs;
                status = false;
            }
        }
        if (status) {
            common_files.com_response(res, 201, true, "course Added successfully", datas);
        } else {
            common_files.com_response(res, 500, false, "Error ", datas);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let department_check = { status: true }, division_check = { status: true }, subject_check = { status: true };
    let part_department_check = { status: true }, part_division_check = { status: true }, part_subject_check = { status: true };

    if (req.body._administration_department_id != undefined) {
        department_check = await base_control.check_id(department, { _id: { $in: req.body._administration_department_id }, 'isDeleted': false });
    }
    if (req.body._administration_division_id != undefined) {
        division_check = await base_control.check_id(department_division, { _id: { $in: req.body._administration_division_id }, 'isDeleted': false });
    }
    if (req.body._administration_subject_id != undefined) {
        subject_check = await base_control.check_id(department_subject, { _id: { $in: req.body._administration_subject_id }, 'isDeleted': false });
    }
    if (req.body._participating_department_id != undefined) {
        part_department_check = await base_control.check_id(department, { _id: { $in: req.body._participating_department_id }, 'isDeleted': false });
    }
    if (req.body._participating_division_id != undefined) {
        part_division_check = await base_control.check_id(department_division, { _id: { $in: req.body._participating_division_id }, 'isDeleted': false });
    }
    if (req.body._participating_subject_id != undefined) {
        part_subject_check = await base_control.check_id(department_subject, { _id: { $in: req.body._participating_subject_id }, 'isDeleted': false });
    }
    // console.log(department_check.status, division_check.status, subject_check.status, part_department_check.status, part_division_check.status, part_subject_check.status);
    if (department_check.status && division_check.status && subject_check.status && part_department_check.status && part_division_check.status && part_subject_check.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(course, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "course update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(course, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "course deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = {
        'isDeleted': false
    };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else {
            proj = {};
        }

        let doc = await base_control.get_list(course, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "course List", course_formate.course_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.course_complete = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $match: { '_program_id': ObjectId(id) } },
        { $lookup: { from: constant.CREDIT_HOURS_INDIVIDUAL, localField: 'year', foreignField: 'year', as: 'individuals' } },
        { $unwind: { path: '$individuals', preserveNullAndEmptyArrays: true } },
        {
            $group: {
                _id: '$_id',
                'study_year': {
                    $first: '$year'
                },
                'no_course': {
                    $first: '$course_no'
                },
                'theory_credit_hours': {
                    $sum: '$individuals.theory_credit_hours'
                },
                'pratical_credit_hours': {
                    $sum: '$individuals.pratical_credit_hours'
                },
                'clinical_credit_hours': {
                    $sum: '$individuals.clinical_credit_hours'
                }
            }
        },
        { $addFields: { 'total_credit_hours': { $sum: ['$theory_credit_hours', '$pratical_credit_hours', '$clinical_credit_hours'] } } },
        { $lookup: { from: constant.COURSE, localField: 'study_year', foreignField: 'study_year', as: 'course' } },
        { $unwind: { path: '$course', preserveNullAndEmptyArrays: true } },
        { $addFields: { 'course_total_credit_hours': { $sum: ['$course.theory_credit', '$course.practical_credit', '$course.clinical_credit'] } } },
        {
            $group: {
                _id: '$study_year',
                'study_year': {
                    $first: '$study_year'
                },
                'no_course': {
                    $first: '$no_course'
                },
                'theory_credit_hours': {
                    $first: '$theory_credit_hours'
                },
                'pratical_credit_hours': {
                    $first: '$pratical_credit_hours'
                },
                'clinical_credit_hours': {
                    $first: '$clinical_credit_hours'
                },
                'total_credit_hours': {
                    $first: '$total_credit_hours'
                },
                'course_total_credit_hours': {
                    $first: '$course_total_credit_hours'
                },
                'course': {
                    $push: '$course'
                },
                'id': {
                    $first: '$_id'
                }
            }
        },
        { $sort: { study_year: 1 } },
        { $addFields: { status: { $eq: ['$total_credit_hours', '$course_total_credit_hours'] } } }
    ];
    let doc = await base_control.get_aggregate(credit_master, aggre);
    // console.log(doc.data.length);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "course complete details", /* doc.data */course_formate.course_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "course complete details", doc.data /* course_formate.course_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_program = async (req, res) => {
    let id = req.params.id;
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_administration_department_id', foreignField: '_id', as: 'administration_department' } },
        { $unwind: { path: '$administration_department', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_administration_division_id', foreignField: '_id', as: 'administration_division' } },
        { $unwind: { path: '$administration_division', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_administration_subject_id', foreignField: '_id', as: 'administration_subject' } },
        { $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT, localField: '_participating_department_id', foreignField: '_id', as: 'participating_department' } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_participating_division_id', foreignField: '_id', as: 'participating_division' } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
        { $sort: { updatedAt: -1 } },
        { $skip: skips }, { $limit: limits },
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(course, aggre, doc_aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "course list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ course_formate.course(doc.data));
        // common_files.list_all_response(res, 200, true, "course list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* course_formate.course(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_course_program_level = async (req, res) => {
    let id = req.params.id;
    // console.log(req.params.level);
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'study_level': parseInt(req.params.level) } },
        { $match: { 'isDeleted': false } },
        { $sort: { updatedAt: -1 } },
        { $project: { _id: 1, courses_name: 1 } }
    ];
    let doc = await base_control.get_aggregate(course, aggre);
    // console.log(doc);
    if (doc.status) {
        // common_files.list_all_response(res, 200, true, "course list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ course_formate.course(doc.data));
        common_files.com_response(res, 200, true, "level wise course list", doc.data /* course_formate.course(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_subjects = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_administration_subject_id', foreignField: '_id', as: 'administration_subject' } },
        { $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true } },
        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
        { $sort: { updatedAt: -1 } },
    ];
    let doc = await base_control.get_aggregate(course, aggre);
    // console.log(doc.data[0].participating_subject);
    let subject_array = []
    subject_array = doc.data[0].participating_subject;
    // console.log(subject_array.length);
    subject_array.push(doc.data[0].administration_subject);
    // console.log(subject_array);
    // console.log(subject_array.length);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "course details", /* doc.data */ course_formate.course_ID(doc.data[0]));
        common_files.com_response(res, 200, true, "course details", subject_array /* course_formate.course_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.delete_department = async (req, res) => {
    let id = ObjectId(req.params.id);
    let sub_id = ObjectId(req.params.sub_id);

    let department_aggre = [
        { $match: { '_id': ObjectId(sub_id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $addFields: { division_subject: '$division._subject_id' } },
        // { $addFields: { divi_check: { $concatArrays: ['$division_subject'] } } },
        { $unwind: { path: '$division_subject', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$division_subject', preserveNullAndEmptyArrays: true } },
        // { $project: { _division_id: 1, 'division._subject_id': 1, division_subject: 1 } }
        {
            $group: {
                _id: '$_id',
                _division_id: { $first: '$_division_id' },
                subject: { $addToSet: '$division_subject' }
            }
        }
    ];
    let department_docs_get = await base_control.get_aggregate(department, department_aggre);
    // console.log(department_docs_get.data);

    // let division_ids = [], subject_ids = [];
    // department_docs_get.data[0]._division_id.forEach(element => {
    //     if (division_ids.indexOf(ObjectId(element)) == -1) {
    //         division_ids.push(ObjectId(element));
    //     }
    // });
    // department_docs_get.data[0].subject.forEach(element => {
    //     if (subject_ids.indexOf(ObjectId(element)) == -1) {
    //         subject_ids.push(ObjectId(element));
    //     }
    // });
    // let pulling_datas = { $pull: { _participating_department_id: sub_id, _participating_division_id: { $in: division_ids }, _participating_subject_id: { $in: subject_ids } } };

    let pulling_datas = { $pull: { _participating_department_id: sub_id, _participating_division_id: { $in: department_docs_get.data[0]._division_id }, _participating_subject_id: { $in: department_docs_get.data[0].subject } } };
    // console.log(pulling_datas);
    // let deleteing = { status: false };
    let deleteing = await base_control.update_push_pull(course, id, pulling_datas);
    // let deleteing = await base_control.update_push_pull(course, id, { $pull: { _participating_department_id: sub_id } });
    // console.log(deleteing);
    if (deleteing.status) {
        let aggre = [
            { $match: { '_id': ObjectId(id) } },
            { $match: { 'isDeleted': false } },
            { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_participating_division_id', foreignField: '_id', as: 'participating_division' } },
            { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
            { $project: { 'participating_division._id': 1, 'participating_division.title': 1, 'participating_subject._id': 1, 'participating_subject.title': 1 } }
        ];
        let doc = await base_control.get_aggregate(course, aggre);
        if (doc.status) {
            common_files.com_response(res, 201, true, "course department deleted successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error Unable to delete department", deleteing.data);
        // common_files.com_response(res, 500, false, "Error Unable to delete department", department_docs_get.data);
    }
}

exports.delete_division = async (req, res) => {
    let id = req.params.id;
    let sub_id = req.params.sub_id;

    let department_aggre = [
        { $match: { '_division_id': ObjectId(sub_id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.DEPARTMENT_DIVISIONS, localField: '_division_id', foreignField: '_id', as: 'division' } },
        { $match: { '_division_id': ObjectId(sub_id) } },
        { $addFields: { division_subject: '$division._subject_id' } },
        // { $addFields: { divi_check: { $concatArrays: ['$division_subject'] } } },
        { $unwind: { path: '$division_subject', preserveNullAndEmptyArrays: true } },
        { $unwind: { path: '$division_subject', preserveNullAndEmptyArrays: true } },
        // { $project: { _division_id: 1, 'division._subject_id': 1, division_subject: 1 } }
        {
            $group: {
                _id: '$_id',
                _division_id: { $first: '$_division_id' },
                subject: { $addToSet: '$division_subject' }
            }
        }
    ];
    let department_docs_get = await base_control.get_aggregate(department, department_aggre);
    let pulling_datas = { $pull: { _participating_division_id: sub_id, _participating_subject_id: { $in: department_docs_get.data[0].subject } } };

    // let deleteing = await base_control.update_push_pull(course, id, { $pull: { _participating_division_id: sub_id } });
    let deleteing = await base_control.update_push_pull(course, id, pulling_datas);
    if (deleteing.status) {
        let aggre = [
            { $match: { '_id': ObjectId(id) } },
            { $match: { 'isDeleted': false } },
            { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: '_participating_subject_id', foreignField: '_id', as: 'participating_subject' } },
            { $project: { 'participating_subject._id': 1, 'participating_subject.title': 1 } }
        ];
        let doc = await base_control.get_aggregate(course, aggre);
        if (doc.status) {
            common_files.com_response(res, 201, true, "course division deleted successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error Unable to delete division", deleteing.data);
    }
}


exports.list_elective_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },

        { $lookup: { from: constant.DEPARTMENT_SUBJECT, localField: 'elective._subject_id', foreignField: '_id', as: 'subject' } },
        { $unwind: { path: '$elective', preserveNullAndEmptyArrays: true } },
        {
            $addFields: {
                'elective.subject': {
                    $filter: {
                        input: '$subject',
                        as: 'sub',
                        cond: {
                            $eq: ['$elective._subject_id', '$$sub._id']
                        }
                    }
                }
            }
        },
        { $unwind: { path: '$elective.subject', preserveNullAndEmptyArrays: true } },
        {
            $group: {
                _id: '$_id',
                _participating_department_id: {
                    $first: '$_participating_department_id'
                },
                _participating_division_id: {
                    $first: '$_participating_division_id'
                },
                _participating_subject_id: {
                    $first: '$_participating_subject_id'
                },
                isDeleted: {
                    $first: '$isDeleted'
                },
                isActive: {
                    $first: '$isActive'
                },
                courses_name: {
                    $first: '$courses_name'
                },
                study_year: {
                    $first: '$study_year'
                },
                study_level: {
                    $first: '$study_level'
                },
                order: {
                    $first: '$order'
                },
                courses_number: {
                    $first: '$courses_number'
                },
                duration: {
                    $first: '$duration'
                },
                model: {
                    $first: '$model'
                },
                theory_credit: {
                    $first: '$theory_credit'
                },
                practical_credit: {
                    $first: '$practical_credit'
                },
                clinical_credit: {
                    $first: '$clinical_credit'
                },
                _program_id: {
                    $first: '$_program_id'
                },
                _administration_department_id: {
                    $first: '$_administration_department_id'
                },
                _administration_division_id: {
                    $first: '$_administration_division_id'
                },
                _administration_subject_id: {
                    $first: '$_administration_subject_id'
                },
                elective: {
                    $push: '$elective'
                },
                subject: {
                    $first: '$subject'
                }
            }
        },

        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: '_administration_department_id',
                foreignField: '_id',
                as: 'administration_department'
            }
        },
        { $unwind: { path: '$administration_department', preserveNullAndEmptyArrays: true } },
        {
            $lookup: {
                from: constant.DEPARTMENT_DIVISIONS,
                localField: '_administration_division_id',
                foreignField: '_id',
                as: 'administration_division'
            }
        },
        {
            $unwind: {
                path: '$administration_division',
                preserveNullAndEmptyArrays: true
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: '_administration_subject_id',
                foreignField: '_id',
                as: 'administration_subject'
            }
        },
        {
            $unwind: { path: '$administration_subject', preserveNullAndEmptyArrays: true }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT,
                localField: '_participating_department_id',
                foreignField: '_id',
                as: 'participating_department'
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_DIVISIONS,
                localField: '_participating_division_id',
                foreignField: '_id',
                as: 'participating_division'
            }
        },
        {
            $lookup: {
                from: constant.DEPARTMENT_SUBJECT,
                localField: '_participating_subject_id',
                foreignField: '_id',
                as: 'participating_subject'
            }
        }

    ];
    let doc = await base_control.get_aggregate(course, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "course details", /* doc.data */ course_formate.course_ID(doc.data[0]));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_course_program_level_type = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'study_level': parseInt(req.params.level) } },
        { $match: { 'model': req.params.type } },
        { $match: { 'isDeleted': false } },
        { $sort: { updatedAt: -1 } },
        { $project: { _id: 1, courses_name: 1 } }
    ];
    let doc = await base_control.get_aggregate(course, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "level wise course list", doc.data);
    } else {
        common_files.com_response(res, 200, false, "No course found", doc.data);
    }
};