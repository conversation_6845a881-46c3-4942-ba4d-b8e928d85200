const router = require('express').Router();
const catchAsync = require('../utility/catch-async');

const {
    addNewDisciplinaryRemarks,
    updateDisciplinaryRemarks,
    deleteDisciplinaryRemarks,
    getStudentsDisciplinaryRemarks,
    getScheduleDetails,
    uploadDisciplinaryRemarkFiles,
    sendRemarksMail,
    generateSignedURLs,
    getMailSentDetails,
} = require('./disciplinaryRemarks.controller');
const {
    addNewDisciplinaryRemarksValidator,
    updateDisciplinaryRemarksValidator,
    deleteDisciplinaryRemarksValidator,
    getStudentsDisciplinaryRemarksValidator,
    getScheduleDetailsValidator,
    fileUploadValidator,
    sendRemarkMailValidator,
    getSignedUrlsValidator,
    getMailDetailsValidator,
} = require('./disciplinaryRemarks.validator');

router.post('/', addNewDisciplinaryRemarksValidator, catchAsync(addNewDisciplinaryRemarks));
router.put('/', updateDisciplinaryRemarksValidator, catchAsync(updateDisciplinaryRemarks));
router.delete('/', deleteDisciplinaryRemarksValidator, catchAsync(deleteDisciplinaryRemarks));
router.get(
    '/students_all_remarks',
    getStudentsDisciplinaryRemarksValidator,
    catchAsync(getStudentsDisciplinaryRemarks),
);
router.post('/userRemarkFiles', fileUploadValidator, catchAsync(uploadDisciplinaryRemarkFiles));
router.get('/schedule_details', getScheduleDetailsValidator, catchAsync(getScheduleDetails));
router.post('/sendMail', sendRemarkMailValidator, catchAsync(sendRemarksMail));
router.post('/generateSignedURLs', getSignedUrlsValidator, catchAsync(generateSignedURLs));
router.get('/mailDetails', getMailDetailsValidator, catchAsync(getMailSentDetails));

module.exports = router;
