const { getCourseSessionOrder } = require('../../../service/cache.service');
const { get, get_list, get_list_populate } = require('../../base/base_controller');
const {
    convertToMongoObjectId,
    clone,
    cs,
    convertToUtcFormat,
    convertToUtcEndFormat,
    convertToUtcTimeFormat,
} = require('../../utility/common');
const {
    STUDENTS,
    STUDENT_SESSION_SURVEY,
    DIGI_COURSE,
    DIGI_SESSION_ORDER,
    COURSE_SCHEDULE,
    PROGRAM_CALENDAR,
    PRESENT,
    ABSENT,
    LEAVE,
    COMPLETED,
    PENDING,
    QUESTION,
    SCHEDULE_TYPES: { EVENT, SUPPORT_SESSION, REGULAR },
    ACTIVITIES,
    STUDENT_GROUP,
    GENDER: { MALE, FEMALE, BOTH },
} = require('../../utility/constants');
const { PM, QUIZ } = require('../../utility/enums');

const surveyCollection = require('mongoose').model(STUDENT_SESSION_SURVEY);
const course = require('mongoose').model(DIGI_COURSE);
const session_orders = require('mongoose').model(DIGI_SESSION_ORDER);
const CourseSchedule = require('mongoose').model(COURSE_SCHEDULE);
const ProgramCalendar = require('mongoose').model(PROGRAM_CALENDAR);
const Course = require('mongoose').model(DIGI_COURSE);
const question = require('mongoose').model(QUESTION);
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const activity = require('mongoose').model(ACTIVITIES);
const student_group = require('mongoose').model(STUDENT_GROUP);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const { getQuestionAnswered } = require('../../reports_analytics/reports_analytics_services');

const { logger } = require('../../utility/util_keys');

const getSessionSLOSurvey = async ({
    institutionCalendarId,
    programId,
    userId,
    courseId,
    sessionId,
    yearNo,
    levelNo,
    term,
    rotation,
    rotationCount,
}) => {
    try {
        return await get(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _user_id: convertToMongoObjectId(userId),
                _course_id: convertToMongoObjectId(courseId),
                _session_order_id: convertToMongoObjectId(sessionId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { slos: 1, feedBack: 1, ratingValue: 1 },
        );
    } catch (error) {
        logger.error(error, 'Survey -> getSessionWiseSLO -> Course Session wise SLO Survey Error');
        throw new Error(error);
    }
};

const getSessionWiseSLO = async ({ courseSessionData, userCourseSurveyData, mode }) => {
    try {
        delete courseSessionData.subjects;
        delete courseSessionData.duration;
        delete courseSessionData.week;
        for (sessionElement of courseSessionData.slo) {
            sessionElement._slo_id = sessionElement._id;
            const rating =
                userCourseSurveyData.status &&
                userCourseSurveyData.data.slos &&
                userCourseSurveyData.data.slos.find(
                    (sloElement) => sloElement._slo_id.toString() === sessionElement._id.toString(),
                );
            sessionElement.sloRating =
                rating && rating.sloRating !== null
                    ? rating.sloRating
                    : mode && mode === 'mock'
                    ? Math.floor(Math.random() * 5 + 1)
                    : null;
        }
        return {
            ...courseSessionData,
            feedBack:
                userCourseSurveyData.status && userCourseSurveyData.data.feedBack
                    ? userCourseSurveyData.data.feedBack
                    : '',
        };
    } catch (error) {
        logger.error(error, 'Survey -> getSessionWiseSLO -> Course Session wise SLO Survey Error');
        throw new Error(error);
    }
};

const getScheduleSessionWiseSLO = async ({ courseSessionData, userCourseSurveyDatas, mode }) => {
    try {
        for (courseSessionElement of courseSessionData) {
            delete courseSessionElement.subjects;
            delete courseSessionElement.duration;
            delete courseSessionElement.week;
            const userSurvey = userCourseSurveyDatas.data.find(
                (surveyElement) =>
                    surveyElement._session_order_id.toString() ===
                    courseSessionElement._id.toString(),
            );
            for (sessionElement of courseSessionElement.slo) {
                sessionElement._slo_id = sessionElement._id;
                const rating =
                    userSurvey &&
                    userSurvey.slos &&
                    userSurvey.slos.find(
                        (sloElement) =>
                            sloElement._slo_id.toString() === sessionElement._id.toString(),
                    );
                sessionElement.sloRating =
                    rating && rating.sloRating !== null
                        ? rating.sloRating
                        : mode && mode === 'mock'
                        ? Math.floor(Math.random() * 5 + 1)
                        : null;
            }
            courseSessionElement.feedBack =
                userSurvey && userSurvey.feedBack ? userSurvey.feedBack : '';
        }
        return courseSessionData;
    } catch (error) {
        logger.error(error, 'Survey -> getSessionWiseSLO -> Course Session wise SLO Survey Error');
        throw new Error(error);
    }
};

const getStudentAttendedSession = (courseSchedules, studentId) => {
    let attended = 0;
    courseSchedules.forEach((cSchedule) => {
        if (!cSchedule.students) return;
        if (
            cSchedule.type === REGULAR &&
            cSchedule.students.find(
                (student) =>
                    student.status === 'present' &&
                    student._id &&
                    studentId &&
                    cs(student._id) === cs(studentId),
            )
        ) {
            attended++;
        }
    });
    return attended;
};
const getRatingByCourses = async (
    courseIds,
    staffId,
    year,
    level,
    term,
    rotation,
    rotationCount,
) => {
    try {
        let courseQuery;
        let yearQuery;
        let levelQuery;
        let rotationQuery;
        let rotationCountQuery;
        if (courseIds && courseIds.length === 24) {
            courseQuery = [courseIds];
            yearQuery = [year];
            levelQuery = [level];
            termQuery = [term];
            rotationQuery = [rotation];

            rotationCountQuery = [rotationCount];
        } else {
            courseQuery = courseIds;
            yearQuery = year;
            levelQuery = level;
            rotationQuery = rotation;
            termQuery = term;
            rotationCountQuery = rotationCount;
        }
        const fbQuery = {
            _course_id: { $in: courseQuery },
            year_no: { $in: yearQuery },
            level_no: { $in: levelQuery },
            term: { $in: termQuery },
            $or: [
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'sessionDetail.startBy': convertToMongoObjectId(staffId),
                },
                {
                    'staffs._staff_id': convertToMongoObjectId(staffId),
                    'staffs.status': PRESENT,
                },
            ],
            students: { $exists: true, $type: 'array', $ne: [] },
        };

        const fbProject = {
            _course_id: 1,
            students: 1,
            year_no: 1,
            level_no: 1,
            staffs: 1,
            _id: 1,
            term: 1,
            rotation_count: 1,
            rotation: 1,
            session: 1,
        };
        let courseSchedules = await CourseSchedule.find(fbQuery, fbProject);
        courseSchedules = courseSchedules.filter((courseSchedule) =>
            courseSchedule.staffs.find(
                (staff) =>
                    courseSchedule.session &&
                    courseSchedule.session._session_id &&
                    staff._staff_id.toString() === staffId.toString() &&
                    staff.status === PRESENT,
            ),
        );

        const feedBacks = [];

        if (courseQuery && courseQuery.length) {
            let i = 0;
            courseQuery.map((courseId) => {
                const courseDetails = courseSchedules.filter((courseSchedule) => {
                    if (courseSchedule._course_id.toString() === courseId.toString()) {
                        return true;
                    }
                    return false;
                });
                let sumOfRatings = 0;
                let count = 0;
                let levelOfCourseId;
                let yearOfCourseId;
                const schedulePush = [];
                let rotation_count;
                let rotation;
                courseDetails.map((courseDetail) => {
                    const { students, level_no, year_no, _id, term } = courseDetail;

                    rotation_count = courseDetail.rotation_count ? courseDetail.rotation_count : '';
                    rotation = courseDetail.rotation ? courseDetail.rotation : '';
                    termOfCourseId = term;
                    levelOfCourseId = level_no;
                    yearOfCourseId = year_no;
                    sumOfRatings += students
                        .filter((student) => student.feedBack && student.feedBack.rating)
                        .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                        .reduce((a, b) => a + b, 0);
                    count += students.filter(
                        (student) => student.feedBack && student.feedBack.rating,
                    ).length;
                    students.map((student) => {
                        if (student.feedBack && student.feedBack.rating) {
                            schedulePush.push(_id);
                        }
                    });
                });

                if (count) {
                    feedBacks.push({
                        _course_id: courseId,
                        level_no: levelOfCourseId,
                        year_no: yearOfCourseId,
                        term: termOfCourseId,
                        totalFeedback: count,
                        avgRating: count ? (sumOfRatings / count).toFixed(1) : 0,
                        sessionCount: schedulePush.length,
                        rotation,
                        rotationCount: rotation_count,
                    });
                }
                i++;
            });
        }
        return feedBacks;
    } catch (error) {
        throw new Error(error);
    }
};
const getSessions = (
    courseSchedules,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    userId,
    rotation,
    rotation_count,
) => {
    try {
        let courseSessions = [];
        const courseSchedulesEntry =
            rotation === 'yes'
                ? courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation &&
                          rotation_count &&
                          rotation_count === courseSchedule.rotation_count,
                  )
                : courseSchedules.filter(
                      (courseSchedule) =>
                          cs(courseSchedule._course_id) === cs(_course_id) &&
                          cs(_institution_calendar_id) ===
                              cs(courseSchedule._institution_calendar_id) &&
                          cs(_program_id) === cs(courseSchedule._program_id) &&
                          year_no === courseSchedule.year_no &&
                          level_no === courseSchedule.level_no &&
                          term === courseSchedule.term &&
                          rotation === courseSchedule.rotation,
                  );

        const isExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter((courseSession) => {
                if (
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    courseSession._session_id &&
                    sessionIdOrType &&
                    courseSession._session_id.toString() === sessionIdOrType.toString()
                ) {
                    if (
                        mergeStatus &&
                        scheduleId.toString() === courseSession.scheduleId.toString()
                    ) {
                        return true;
                    }
                    if (!mergeStatus) {
                        return true;
                    }
                }
                return false;
            }).length;

        const isSessionTypeExist = (mergeStatus, sessionIdOrType, scheduleId) =>
            courseSessions.filter(
                (courseSession) =>
                    courseSession.merge_status.toString() === mergeStatus.toString() &&
                    courseSession &&
                    !courseSession._session_id &&
                    courseSession.session_topic === sessionIdOrType &&
                    courseSession.scheduleId &&
                    scheduleId.toString() === courseSession.scheduleId.toString(),
            ).length;

        courseSchedulesEntry.forEach((courseSchedule) => {
            if (
                courseSchedule.session &&
                !isExist(
                    courseSchedule.merge_status,
                    courseSchedule.session._session_id,
                    courseSchedule._id,
                )
            ) {
                const { _id, session, merge_status, merge_with, student_groups, status } =
                    courseSchedule;
                session.merge_status = merge_status;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (session && !merge_status) {
                    session.status = status;
                    session.student_groups = student_groups;
                    session.scheduleId = _id;
                    courseSessions.push(session);
                } else {
                    const mergedSessions = [];
                    const mergedStudents = [];
                    merge_with.forEach((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                    mergeWith.schedule_id.toString() &&
                                courseScheduleEntry.session &&
                                courseScheduleEntry.session._session_id.toString() ===
                                    mergeWith.session_id.toString(),
                        );
                        if (sessionDetails) {
                            mergeWith.session = {
                                _session_id: sessionDetails.session._session_id,
                                s_no: sessionDetails.session.s_no,
                                delivery_symbol: sessionDetails.session.delivery_symbol,
                                delivery_no: sessionDetails.session.delivery_no,
                                topic: sessionDetails.topic,
                            };
                            if (sessionDetails.students && sessionDetails.students.length) {
                                mergedStudents.push(sessionDetails.students);
                            }
                        }
                        mergedSessions.push(mergeWith);
                    });
                    const mergedScheduleIds = merge_with.map((mergeWith) =>
                        mergeWith.schedule_id.toString(),
                    );
                    let sessionMerged = merge_with.filter((mergeWith) => mergeWith.session_id);
                    sessionMerged = sessionMerged.map((mergeWith) =>
                        mergeWith.session_id.toString(),
                    );
                    courseSchedule.merge_with = mergedSessions;
                    if (mergedStudents.length) {
                        if (courseSchedule.students && courseSchedule.students.length)
                            mergedStudents.push(courseSchedule.students);
                        // eslint-disable-next-line prefer-spread
                        const concatMergedStudents = [].concat.apply([], mergedStudents);
                        //Sort the result to show beedback first then only we can identify using find if merge is scheduled
                        const sortFeedbackBasedStudents = concatMergedStudents.sort(function (
                            a,
                            b,
                        ) {
                            return b.feedBack ? 1 : -1;
                        });
                        // duplicate student removed
                        const students = sortFeedbackBasedStudents.reduce((acc, current) => {
                            const x = acc.find(
                                (item) => item._id.toString() === current._id.toString(),
                            );
                            if (!x) {
                                return acc.concat([current]);
                            }
                            return acc;
                        }, []);
                        courseSchedule.students = students;
                    }
                    const duplicateMergedSession = courseSessions.find(
                        (courseSession) =>
                            courseSession._session_id &&
                            sessionMerged.includes(courseSession._session_id.toString()) &&
                            courseSession.merge_status.toString() ===
                                courseSchedule.merge_status.toString() &&
                            mergedScheduleIds.includes(courseSession.scheduleId.toString()),
                    );

                    const mergedSessionSchedules = courseSchedulesEntry.filter((courseSchedule) =>
                        mergedScheduleIds.find(
                            (mergedScheduleId) =>
                                mergedScheduleId.toString() === courseSchedule._id.toString(),
                        ),
                    );
                    let mergedSessionScheduleStudentGroup = mergedSessionSchedules.map(
                        (mergedSessionSchedule) => mergedSessionSchedule.student_groups,
                    );
                    mergedSessionScheduleStudentGroup.push(student_groups);
                    // eslint-disable-next-line prefer-spread
                    mergedSessionScheduleStudentGroup = [].concat.apply(
                        [],
                        mergedSessionScheduleStudentGroup,
                    );
                    courseSchedule.student_groups = mergedSessionScheduleStudentGroup;
                    session.scheduleId = _id;
                    if (!duplicateMergedSession) courseSessions.push(session);
                }
            }
            // splitted to session type based
            const sessionTypes = [EVENT, SUPPORT_SESSION];
            if (
                courseSchedule.type &&
                sessionTypes.includes(courseSchedule.type) &&
                !isSessionTypeExist(
                    courseSchedule.merge_status,
                    courseSchedule.title,
                    courseSchedule._id,
                )
            ) {
                const { type, merge_status, student_groups, merge_with } = courseSchedule;
                if (merge_status) {
                    session.merge_with = merge_with;
                }
                if (type) {
                    courseSessions.push({
                        scheduleId: courseSchedule._id,
                        session_type: courseSchedule.type,
                        session_topic: courseSchedule.title,
                        merge_status,
                        student_groups,
                    });
                }
                if (merge_status) {
                    const mergedSessions = merge_with.map((mergeWith) => {
                        const sessionDetails = courseSchedulesEntry.find(
                            (courseScheduleEntry) =>
                                courseScheduleEntry._id.toString() ===
                                mergeWith.schedule_id.toString(),
                        );
                        mergeWith.session = {
                            session_type: sessionDetails.type,
                            session_topic: sessionDetails.title,
                        };
                        return mergeWith;
                    });
                    courseSchedule.merge_with = mergedSessions;
                    courseSessions.push(session);
                }
            }
        });

        let totalSessions = 0;
        let completedSessions = 0;
        let warningCount = 0;
        let presentCount = 0;
        let leaveCount = 0;
        let absentCount = 0;
        // sort by session
        let courseSessionsRegular = courseSessions.filter(
            (courseSession) => courseSession._session_id,
        );
        const courseSessionsOthers = courseSessions.filter(
            (courseSession) => !courseSession._session_id,
        );
        courseSessionsRegular = courseSessionsRegular.sort((a, b) =>
            a.s_no && b.s_no && a.s_no > b.s_no ? 1 : -1,
        );
        courseSessions = courseSessionsRegular.concat(courseSessionsOthers);
        courseSessions.forEach((courseSession) => {
            const { merge_status: courseSessionMergeStatus, scheduleId } = courseSession;
            courseSession.documentCount = 0; // To Do
            courseSession.activityCount = 0; // To Do
            let schedules = courseSession._session_id
                ? clone(courseSchedulesEntry).filter((courseSchedule) => {
                      if (
                          courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          if (
                              courseSessionMergeStatus &&
                              scheduleId.toString() === courseSchedule._id.toString()
                          ) {
                              return true;
                          }
                      } else if (
                          !courseSessionMergeStatus &&
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSchedule.session &&
                          courseSession._session_id &&
                          courseSchedule.session._session_id.toString() ===
                              courseSession._session_id.toString()
                      ) {
                          return true;
                      }
                  })
                : clone(courseSchedulesEntry).filter(
                      (courseSchedule) =>
                          courseSchedule.merge_status.toString() ===
                              courseSessionMergeStatus.toString() &&
                          courseSession.session_topic === courseSchedule.title &&
                          courseSchedule._id &&
                          courseSchedule._id.toString() === scheduleId.toString(),
                  );
            schedules = schedules.map((schedule) => {
                const {
                    start: { hour: startHour, minute: startMinute, format: startFormat },
                    end: { hour: endHour, minute: endMinute, format: endFormat },
                    schedule_date,
                } = schedule;

                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const endHours = endFormat === PM && endHour !== 12 ? endHour + 12 : endHour;
                const startDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    startHours,
                    startMinute,
                    0,
                );
                const endDateAndTime = convertToUtcTimeFormat(
                    schedule_date,
                    endHours,
                    endMinute,
                    0,
                );
                schedule.start_date = startDateAndTime;
                schedule.end_date = endDateAndTime;
                return schedule;
            });
            courseSession.absentCount = 0;
            courseSession.leaveCount = 0;
            courseSession.warningCount = 0;
            courseSession.presentCount = 0;
            courseSession.totalSchedules = 0;
            schedules.forEach((cSchedule) => {
                if (
                    cSchedule.session &&
                    cSchedule.session._session_id &&
                    cSchedule.status === COMPLETED &&
                    cSchedule.students &&
                    cSchedule.students.length
                ) {
                    cSchedule.students.forEach((student) => {
                        if (cs(student._id) === cs(userId)) {
                            if (student.status === 'absent' || student.status === 'pending')
                                courseSession.absentCount++;
                            if (student.status === 'present') {
                                courseSession.presentCount++;
                                //form adding merge sessions count
                                if (cSchedule.merge_status) {
                                    courseSession.presentCount += cSchedule.merge_with.length;
                                }
                            }
                            if (
                                student.status === 'leave' ||
                                student.status === 'on_duty' ||
                                student.status === 'permission'
                            )
                                courseSession.leaveCount++;
                            if (student.status === 'leave') courseSession.warningCount++; //To Do
                        }
                    });
                }
                cSchedule.staffs.forEach((staff) => {
                    if (cSchedule.status === COMPLETED && cs(staff._staff_id) === cs(userId)) {
                        if (staff.status === 'absent' || staff.status === 'pending')
                            courseSession.absentCount++;
                        if (staff.status === 'present') {
                            courseSession.presentCount++;
                            //form adding merge sessions count
                            if (cSchedule.merge_status) {
                                courseSession.presentCount += cSchedule.merge_with.length;
                            }
                        }
                        if (
                            staff.status === 'leave' ||
                            staff.status === 'on_duty' ||
                            staff.status === 'permission'
                        )
                            courseSession.leaveCount++;
                        if (staff.status === 'leave') courseSession.warningCount++;
                    }
                });
                if (cSchedule.isActive === true) courseSession.totalSchedules++;
            });
            warningCount += courseSession.warningCount;
            presentCount += courseSession.presentCount;
            leaveCount += courseSession.leaveCount;
            absentCount += courseSession.absentCount;
            courseSession.schedules = schedules;
        });

        totalSessions = courseSessions.filter(
            (courseSession) =>
                courseSession._session_id &&
                courseSession.schedules.find((schedule) => schedule.isActive),
        ).length;

        //form adding merge sessions count
        totalSessions += courseSessions
            .filter((courseSession) => courseSession.merge_status)
            .filter((mergeSessionEntry) => mergeSessionEntry.merge_with).length;
        const groupNames = [];

        for (const courseSession of courseSessions) {
            if (courseSession.student_groups) {
                for (const studentGroup of courseSession.student_groups) {
                    if (
                        studentGroup.group_name &&
                        !groupNames.find((groupName) => groupName === studentGroup.group_name)
                    ) {
                        groupNames.push(studentGroup.group_name);
                    }
                }
            }
        }

        const uniqueSessionTypes = [
            ...new Set(
                courseSessions
                    .map((type) => {
                        if (type._session_id) return type.session_type;
                    })
                    .filter((item) => item),
            ),
        ];
        const sessDetails = courseSessions.map((courseSession) => {
            const groupNamesArray = [];

            if (courseSession.student_groups) {
                courseSession.student_groups.forEach((groupName) => {
                    groupNamesArray.push(groupName.group_name);
                });
            }
            courseSession.groupNames = groupNamesArray;
            return courseSession;
        });
        const courseSessionDetails = groupNames.map((groupName) => {
            const totalGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );
            const completedGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === COMPLETED &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const pendingGroup = sessDetails.filter(
                (sessionData) =>
                    sessionData.status &&
                    sessionData.status === PENDING &&
                    sessionData.groupNames.find((groupNameEntry) => groupNameEntry === groupName) &&
                    sessionData._session_id &&
                    sessionData.schedules.find((schedule) => schedule.isActive),
            );

            const courseSessions = uniqueSessionTypes.map((uniqueSessionType) => {
                const completedCount = completedGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === COMPLETED &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                const pendingCount = pendingGroup.filter(
                    (sessionData) =>
                        sessionData.status &&
                        sessionData.status === PENDING &&
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;
                const totalCount = totalGroup.filter(
                    (sessionData) =>
                        sessionData._session_id &&
                        sessionData.schedules.find((schedule) => schedule.isActive) &&
                        sessionData.session_type === uniqueSessionType,
                ).length;

                return {
                    deliveryName: uniqueSessionType,
                    totalCount,
                    completedCount,
                    pendingCount,
                };
            });
            const totalGroups = totalGroup.length;
            return {
                studentGroupName: groupName.split('-').splice(-2).join('-'),
                totalSessions: totalGroups,
                completedSessions: completedGroup.length,
                pendingSessions: pendingGroup.length,
                courseSessions,
            };
        });

        courseSessions.forEach((courseSession) => {
            if (courseSession._session_id) {
                // {
                //     if (
                //         courseSession.schedules.length ===
                //         courseSession.schedules.filter((schedule) => schedule.status === COMPLETED)
                //             .length
                //     )
                //         completedSessions++;
                // } else
                const courseSchedules = courseSchedulesEntry.filter(
                    (courseSchedule) =>
                        courseSchedule.isActive === true &&
                        courseSchedule.session &&
                        courseSchedule.session._session_id &&
                        courseSession._session_id &&
                        courseSchedule.session._session_id.toString() ===
                            courseSession._session_id.toString(),
                );
                const sessionCompleted = courseSchedules.filter(
                    (courseSchedule) => courseSchedule.status === COMPLETED,
                );
                if (
                    sessionCompleted.length &&
                    courseSchedules.length &&
                    sessionCompleted.length === courseSchedules.length
                ) {
                    completedSessions++;
                    //form adding merge sessions count
                    completedSessions += sessionCompleted
                        .filter((courseSession) => courseSession.merge_status)
                        .filter((mergeSessionEntry) => mergeSessionEntry.merge_with).length;
                }
            }
        });
        const attendedSessions = getStudentAttendedSession(courseSchedulesEntry, userId);

        return {
            courseSessionDetails,
            warningCount,
            presentCount,
            totalSessions,
            completedSessions,
            courseSessions,
            attendedSessions,
            leaveCount,
            absentCount,
        };
    } catch (error) {
        throw new Error(error);
    }
};
const getCourseIdsWithSessions = async (
    staffId,
    _course_id,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
) => {
    try {
        const csQuery = {
            $or: [
                { 'staffs._staff_id': convertToMongoObjectId(staffId) },
                { 'students._id': convertToMongoObjectId(staffId) },
            ],
            schedule_date: { $exists: true },
            isDeleted: false,
        };
        if (_course_id) {
            csQuery._course_id = convertToMongoObjectId(_course_id);
            csQuery._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
            csQuery._program_id = convertToMongoObjectId(_program_id);
            csQuery.year_no = year_no;
            csQuery.level_no = level_no;
            csQuery.term = term;
            if (rotation) {
                csQuery.rotation = rotation;
            }
            if (rotation_count) {
                csQuery.rotation_count = rotation_count;
            }
        }
        const csProject = {
            _course_id: 1,
            session: 1,
            student_groups: 1,
            year_no: 1,
            level_no: 1,
            _program_id: 1,
            end: 1,
            start: 1,
            schedule_date: 1,
            mode: 1,
            subjects: 1,
            _infra_id: 1,
            infra_id: '$_infra_id',
            infra_name: 1,
            staffs: 1,
            status: 1,
            sessionDetail: 1,
            students: 1,
            uuid: 1,
            socket_port: 1,
            _institution_calendar_id: 1,
            rotation: 1,
            rotation_count: 1,
            program_name: 1,
            course_name: 1,
            course_code: 1,
            type: 1,
            title: 1,
            merge_status: 1,
            merge_with: 1,
            topic: 1,
            sub_type: 1,
            isActive: 1,
            isDeleted: 1,
            term: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            zoomDetail: 1,
        };
        let courseSchedule = await CourseSchedule.find(csQuery, csProject)
            .sort({ 'sessions.delivery_symbol': 1, 'sessions.delivery_no': 1 })
            .populate({
                path: '_infra_id',
                select: { building_name: 1, floor_no: 1, room_no: 1, name: 1, zone: 1 },
            })
            .lean();
        let mergedSchedules = courseSchedule.filter(
            (courseScheduleEntry) => courseScheduleEntry.merge_status,
        );
        mergedSchedules = mergedSchedules.map((mergedSchedule) => mergedSchedule.merge_with);
        // eslint-disable-next-line no-sequences
        mergedSchedules = mergedSchedules.reduce((r, e) => (r.push(...e), r), []);
        const mergedScheduleIds = mergedSchedules.map((mergedSchedule) =>
            convertToMongoObjectId(mergedSchedule.schedule_id),
        );
        mergedSchedules = await CourseSchedule.find({ _id: { $in: mergedScheduleIds } }, csProject);

        courseSchedule = courseSchedule.map((schedule) => {
            let sumOfRatings = 0;
            let count = 0;
            let mergedStudents = [];
            const { students, _infra_id, staffs, merge_status, merge_with } = schedule;
            const startBy = staffs.find(
                (staff) => staff._staff_id.toString() === staffId.toString(),
            );
            mergedStudents = mergedStudents.concat(students);
            if (merge_status) {
                const mergedWithSchedules = merge_with.map((mergeWith) => mergeWith.schedule_id);
                mergedWithSchedules.forEach((mergedWithSchedule) => {
                    const mergedWithScheduleStudents = mergedSchedules.find(
                        (mergedSchedule) =>
                            mergedSchedule._id.toString() === mergedWithSchedule.toString(),
                    );
                    if (mergedWithScheduleStudents) {
                        mergedStudents = mergedStudents.concat(mergedWithScheduleStudents.students);
                    }
                });
            }

            if (mergedStudents && mergedStudents.length && startBy && startBy.status === PRESENT) {
                sumOfRatings += mergedStudents
                    .filter((student) => student.feedBack && student.feedBack.rating)
                    .map((feedBackEntry) => parseFloat(feedBackEntry.feedBack.rating))
                    .reduce((a, b) => a + b, 0);

                count += mergedStudents.filter(
                    (student) => student.feedBack && student.feedBack.rating,
                ).length;
            }
            if (count) {
                schedule.feedBack = {
                    _session_id: schedule._id,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (sumOfRatings / count).toFixed(1) : 0,
                };
            }
            if (_infra_id) {
                let infraName = _infra_id.name + ',' + _infra_id.floor_no;
                if (_infra_id.zone.length) {
                    infraName += ',' + _infra_id.zone.toString();
                }
                infraName += ',' + _infra_id.room_no + ',' + _infra_id.building_name;
                schedule.infra_name = infraName;
            }
            return schedule;
        });

        const courseIds = [...new Set(courseSchedule.map((schedule) => cs(schedule._course_id)))];
        // Getting Course
        const programCalendarData = await ProgramCalendar.find(
            {
                // _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                // _program_id: convertToMongoObjectId(_program_id),
            },
            {
                _institution_calendar_id: 1,
                _program_id: 1,
                'level.level_no': 1,
                'level.term': 1,
                'level.year': 1,
                'level.end_date': 1,
                'level.start_date': 1,
                'level.course._course_id': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        );

        const splittedCourses = [];
        for (const courseId of courseIds) {
            const filterCourseSchedule = courseSchedule.filter(
                (courseScheduleEntry) => cs(courseScheduleEntry._course_id) === cs(courseId),
            );
            // different calendar and year and level based courses
            const courses = filterCourseSchedule.reduce((acc, current) => {
                const x = acc.find(
                    (item) =>
                        item._institution_calendar_id.toString() ===
                            current._institution_calendar_id.toString() &&
                        item._program_id.toString() === current._program_id.toString() &&
                        item.year_no === current.year_no &&
                        item.level_no === current.level_no &&
                        item.term === current.term &&
                        ((item.rotation.toString() === 'yes' &&
                            current.rotation_count &&
                            item.rotation_count.toString() === current.rotation_count.toString()) ||
                            item.rotation.toString() === 'no'),
                );

                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            if (courses.length) {
                for (const course of courses) {
                    const {
                        _institution_calendar_id,
                        _program_id,
                        year_no,
                        level_no,
                        rotation,
                        rotation_count,
                        _course_id,
                        term,
                    } = course;
                    if (programCalendarData) {
                        const { level } = programCalendarData.find(
                            (calendarElement) =>
                                calendarElement._institution_calendar_id.toString() ===
                                    _institution_calendar_id.toString() &&
                                calendarElement._program_id.toString() === _program_id.toString(),
                        );
                        const levelsEntry = level.find(
                            (levelEntry) =>
                                levelEntry.year === year_no &&
                                levelEntry.level_no === level_no &&
                                levelEntry.term === term,
                        );
                        const { course: coursesEntry, rotation_course } = levelsEntry;
                        let dates;
                        if (rotation === 'yes') {
                            dates = rotation_course
                                .filter(
                                    (rotationCourseEntry) =>
                                        rotationCourseEntry.rotation_count === rotation_count,
                                )
                                .map((rCourse) => rCourse.course)
                                .flat()
                                .find(
                                    (rotationCourse) =>
                                        rotationCourse._course_id.toString() ===
                                        _course_id.toString(),
                                );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }

                            course.rotation_count = rotation_count;
                        } else {
                            dates = coursesEntry.find(
                                (courseEntry) =>
                                    courseEntry._course_id.toString() === _course_id.toString(),
                            );
                            if (dates) {
                                course.end_date = dates.end_date;
                                course.start_date = dates.start_date;
                            }
                        }
                    }
                }
                splittedCourses.push(courses);
            }
        }
        // eslint-disable-next-line prefer-spread
        const mergedCourses = [].concat.apply([], splittedCourses);
        const programIds = [...new Set(courseSchedule.map((schedule) => schedule._program_id))];
        const year = [...new Set(courseSchedule.map((schedule) => schedule.year_no))];
        const level = [...new Set(courseSchedule.map((schedule) => schedule.level_no))];
        const courseTerm = [...new Set(courseSchedule.map((schedule) => schedule.term))];
        const rotations = [...new Set(courseSchedule.map((schedule) => schedule.rotation))];
        const rotationCount = [
            ...new Set(courseSchedule.map((schedule) => schedule.rotation_count)),
        ];
        return {
            courseIds,
            programIds,
            year,
            courseSchedule,
            level,
            rotation: rotations,
            rotationCount,
            courses: mergedCourses,
            term: courseTerm,
        };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const getAllCourses = async (staffId, coordinators) => {
    try {
        const { courseIds, courseSchedule, courses, year, level, rotation, term, rotationCount } =
            await getCourseIdsWithSessions(staffId);
        const feedBackData = await getRatingByCourses(
            courseIds,
            staffId,
            year,
            level,
            term,
            rotation,
            rotationCount,
        );
        const courseIdsResults = courses.map((courseId) => {
            return courseId._course_id;
        });

        const courseTypeResult = await Course.find(
            {
                _id: { $in: courseIdsResults },
            },
            { _id: 1, course_type: 1, 'coordinators._user_id': 1 },
        );
        const adminChkcourses = [];
        if (coordinators === 'true') {
            for (adminCourses of courseTypeResult) {
                const userData = [];
                if (adminCourses.coordinators) {
                    for (courseAdmin of adminCourses.coordinators) {
                        const user = await User.findOne(
                            { _id: courseAdmin._user_id },
                            { name: 1, email: 1 },
                        );
                        userData.push({ name: user.name, email: user.email });
                    }
                }
                adminChkcourses.push({ _course_id: adminCourses._id, coordinatorData: userData });
            }
        }
        const coursesList = courses.map((course) => {
            const {
                _course_id: courseId,
                _institution_calendar_id,
                _program_id,
                program_name,
                course_name,
                course_code,
                level_no,
                year_no,
                term: courseTerm,
                isActive,
                subjects,
                rotation,
                end_date,
                start_date,
                rotation_count,
            } = course;

            courseAdminNames = adminChkcourses.find(
                (adminCourses) => adminCourses._course_id.toString() === courseId.toString(),
            );

            /* const result = getSessions(
                courseSchedule,
                courseId,
                _institution_calendar_id,
                _program_id,
                year_no,
                level_no,
                courseTerm,
                staffId,
                rotation,
                rotation_count,
            );
            const {
                courseSessions,
                courseSessionDetails,
                totalSessions,
                completedSessions,
                attendedSessions,
                presentCount,
                warningCount,
                leaveCount,
                absentCount,
            } = result;
            let feedback;
            let rotationCount;
            if (!course.rotation_count) {
                rotationCount = '';
            } else {
                rotationCount = course.rotation_count;
            }

            const feedBacks = rotationCount
                ? feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === courseId.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation &&
                          feedBackDetail.rotationCount === rotationCount,
                  )
                : feedBackData.find(
                      (feedBackDetail) =>
                          feedBackDetail._course_id.toString() === courseId.toString() &&
                          feedBackDetail.level_no === level_no &&
                          feedBackDetail.year_no === year_no &&
                          feedBackDetail.rotation === rotation,
                  );
            if (feedBacks) feedback = feedBacks;
*/
            let course_type;
            const courseTypes = courseTypeResult.find(
                (courseType) => courseType._id.toString() === courseId.toString(),
            );
            if (courseTypes) course_type = courseTypes.course_type;
            return {
                _id: courseId,
                _institution_calendar_id,
                _program_id,
                program_name,
                course_name,
                course_code,
                courseAdminNames,
                /* courseSessionDetails,
                totalSessions,
                completedSessions,
                attendedSessions,
                leaveCount,
                absentCount,
                warningCount,
                presentCount,
                subjects, */
                year: year_no,
                level: level_no,
                term: courseTerm,
                isActive,
                //feedback,
                rotation,
                end_date,
                start_date,
                rotation_count,
                course_type,
            };
        });
        return coursesList;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
/* const activityQuestionList = async (courseId, term, rotationCount) => {
    try {
        const populate = {
            path: 'quizStartedBy',
            select: {
                name: 1,
                user_id: 1,
            },
        };
        const activityQuery = {
            isDeleted: false,
            quizType: QUIZ,
            courseId: convertToMongoObjectId(courseId),
            status: COMPLETED,
        };
        if (term) activityQuery.term = term;
        if (rotationCount && parseInt(rotationCount) !== 0)
            activityQuery.rotation_count = parseInt(rotationCount);
        const activityList = await get_list_populate(activity, activityQuery, {}, populate);
        if (!activityList.status) activityList.data = [];
        const questionIds = activityList.data
            .map((activityElement) =>
                activityElement.questions
                    .map((questionElement) => questionElement._id.toString())
                    .flat(),
            )
            .flat();
        const questionList = await get_list(
            question,
            {
                _id: { $in: questionIds },
                isDeleted: false,
            },
            {},
        );
        if (!questionList.status) questionList.data = [];
        activityList.data = clone(activityList.data);
        const activityScheduleIds = activityList.data
            .map((activityElement) => activityElement.scheduleIds)
            .flat();
        console.time('activityScheduleDatas');
        const activityScheduleDatas = await get_list(
            CourseSchedule,
            {
                _id: { $in: activityScheduleIds },
                isDeleted: false,
            },
            {
                students: 1,
            },
        );
        console.timeEnd('activityScheduleDatas');
        activityScheduleDatas.data = activityScheduleDatas.status ? activityScheduleDatas.data : [];
        for (activityElement of activityList.data) {
            const questionWithWrongOption = [];
            for (questionElement of activityElement.questions) {
                const questionData = questionList.data.find(
                    (ele) => ele._id.toString() === questionElement._id.toString(),
                );
                if (!questionData) continue;
                const optionData = questionData.options.find((ele2) => ele2.answer);
                const wrongOptionData = questionData.options.find((ele2) => ele2.answer === false);
                if (questionData && optionData) {
                    questionElement.correctOption = optionData._id;
                    questionElement.questionData = questionData;
                }
                questionWithWrongOption.push({
                    _id: convertToMongoObjectId(),
                    _questionId: convertToMongoObjectId(questionElement._id),
                    _optionId: convertToMongoObjectId(wrongOptionData._id),
                });
            }
            for (activityStudentElement of activityElement.students) {
                const missingQuestionDatas = questionWithWrongOption.filter(
                    (wrongElement) =>
                        !activityStudentElement.questions.find(
                            (questionElement) =>
                                wrongElement._questionId.toString() ===
                                questionElement._questionId.toString(),
                        ),
                );
                if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                    activityStudentElement.questions = [
                        ...activityStudentElement.questions,
                        ...missingQuestionDatas,
                    ];
                }
            }
            // const scheduleStudents = activityScheduleDatas.data.find((scheduleELement) =>
            //     activityElement.scheduleIds.find(
            //         (activityScheduleId) =>
            //             activityScheduleId.toString() === scheduleELement._id.toString(),
            //     ),
            // );
            // if (scheduleStudents && scheduleStudents.students)
            //     activityElement.scheduleStudents = scheduleStudents.students;
            // else activityElement.scheduleStudents = [];
            activityElement.scheduleStudents = activityScheduleDatas.data
                .filter((scheduleELement) =>
                    activityElement.scheduleIds.find(
                        (activityScheduleId) =>
                            activityScheduleId.toString() === scheduleELement._id.toString(),
                    ),
                )
                .map((studentElement) => studentElement.students)
                .flat();
        }
        return { activityList, questionList };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
}; */
const activityQuestionListWithActivityData = async (
    courseId,
    term,
    rotationCount,
    activityData,
    scheduleData,
    questionData,
) => {
    try {
        let activityList = activityData.filter(
            (ele) =>
                ele.courseId.toString() === courseId.toString() &&
                ele.term &&
                ele.term.toString() === term.toString(),
        );

        activityList = clone(activityList);
        const activityScheduleIds = activityList
            .map((activityElement) => activityElement.scheduleIds)
            .flat();
        scheduleData.data = scheduleData.status ? scheduleData.data : [];
        //activityScheduleDatas.data = activityScheduleDatas.status ? activityScheduleDatas.data : [];
        const activityScheduleDatas = scheduleData.data.filter(
            (ele) => activityScheduleIds.indexOf(ele._id) === -1,
        );
        for (activityElement of activityList) {
            const questionWithWrongOption = [];
            for (questionElement of activityElement.questions) {
                const questionDatas = questionData.find(
                    (questionListElement) =>
                        questionListElement._id.toString() === questionElement._id.toString(),
                );
                if (!questionDatas) continue;
                const optionData = questionDatas.options.find((ele2) => ele2.answer);
                const wrongOptionData = questionDatas.options.find((ele2) => ele2.answer === false);
                if (questionDatas && optionData) {
                    questionElement.correctOption = optionData._id;
                    questionElement.questionData = questionDatas;
                }
                if (questionDatas && ['SQA', 'MQ'].includes(questionDatas.questionType)) {
                    questionElement.questionData = questionDatas;
                }
                questionWithWrongOption.push({
                    _id: convertToMongoObjectId(),
                    _questionId: convertToMongoObjectId(questionElement._id),
                    _optionId: wrongOptionData && convertToMongoObjectId(wrongOptionData._id),
                });
            }
            for (activityStudentElement of activityElement.students) {
                const missingQuestionDatas = questionWithWrongOption.filter(
                    (wrongElement) =>
                        !activityStudentElement.questions.find(
                            (questionElement) =>
                                wrongElement._questionId.toString() ===
                                questionElement._questionId.toString(),
                        ),
                );
                if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                    activityStudentElement.questions = [
                        ...activityStudentElement.questions,
                        ...missingQuestionDatas,
                    ];
                }
            }
            activityElement.scheduleStudents = activityScheduleDatas
                .filter((scheduleELement) =>
                    activityElement.scheduleIds.find(
                        (activityScheduleId) =>
                            activityScheduleId.toString() === scheduleELement._id.toString(),
                    ),
                )
                .map((studentElement) => studentElement.students)
                .flat();
        }
        return { activityList };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const studentGroupList = async (
    institutionCalendarId,
    programId,
    courseId,
    levelNo,
    term,
    rotationCount,
    res,
) => {
    try {
        const studentGroupData = await get(
            student_group,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.term': term,
                'groups.level': levelNo,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {},
        );
        if (!studentGroupData.status) throw new Error('Student group not found');
        const sgLevel = studentGroupData.data.groups.find(
            (ele) => ele.level.toString() === levelNo.toString() && ele.term === term,
        );
        if (!sgLevel) throw new Error('Student Group Level not found');
        const sgCourseData = sgLevel.courses.find(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );
        const studentWithGroupList = [];
        let studentWithOutGroupList = [];
        const sgCourseSettingData =
            rotationCount && parseInt(rotationCount) !== 0
                ? sgCourseData && sgCourseData.setting
                    ? sgCourseData.setting.filter(
                          (ele) => parseInt(ele._group_no) === parseInt(rotationCount),
                      )
                    : []
                : sgCourseData.setting
                ? sgCourseData && sgCourseData.setting
                : [];
        for (courseSetting of sgCourseSettingData) {
            const group_name =
                courseSetting._group_no !== undefined
                    ? courseSetting.gender === MALE
                        ? 'MG-' + courseSetting._group_no
                        : 'FG-' + courseSetting._group_no
                    : courseSetting.gender === MALE
                    ? 'MG-1'
                    : 'FG-1';
            // const group_name = courseSetting.gender === MALE ? 'MG-1' : 'FG-1';
            const deliveryGroup =
                courseSetting &&
                courseSetting.session_setting &&
                courseSetting.session_setting.length &&
                courseSetting.session_setting[0].groups
                    ? courseSetting.session_setting[0].groups
                    : [];
            const studentIds = deliveryGroup.map((ele) => ele._student_ids).flat();
            const groupStudentData = sgLevel.students
                .filter((ele) =>
                    studentIds.find((ele2) => ele2.toString() === ele._student_id.toString()),
                )
                .map((ele3) => {
                    return {
                        _student_id: ele3._student_id,
                        name: ele3.name,
                        academic_no: ele3.academic_no,
                        gender: ele3.gender,
                    };
                });
            studentWithGroupList.push({
                _id: courseSetting._id,
                gender: courseSetting.gender,
                group_name,
                students: groupStudentData,
            });
            studentWithOutGroupList = studentWithOutGroupList.concat(groupStudentData);
        }
        return {
            studentWithGroupList,
            studentWithOutGroupList,
        } /* { master_group, sgCourseData } */;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const studentGroupListNew = async (
    studentGroupData,
    courseId,
    levelNo,
    term,
    rotationCount,
    userId,
) => {
    try {
        /* const studentGroupData = await get(
            student_group,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'master._program_id': convertToMongoObjectId(programId),
                'groups.term': term,
                'groups.level': levelNo,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {},
        ); */
        if (!studentGroupData) throw new Error('Student group not found');
        /* const sgLevel = studentGroupData.groups.find(
            (ele) => ele.level.toString() === levelNo.toString() && ele.term === term,
        ); */
        let sgLevel = '';
        for (eleStudentGroup of studentGroupData) {
            sgLevel = eleStudentGroup.groups.find(
                (ele) => ele.level.toString() === levelNo.toString() && ele.term === term,
            );
            if (sgLevel) break;
        }

        if (!sgLevel) throw new Error('Student Group Level not found');
        const sgCourseData = sgLevel.courses.find(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );
        const studentWithGroupList = [];
        let studentWithOutGroupList = [];
        const sgCourseSettingData =
            rotationCount && parseInt(rotationCount) !== 0
                ? sgCourseData
                    ? sgCourseData.setting.filter(
                          (ele) => parseInt(ele._group_no) === parseInt(rotationCount),
                      )
                    : sgCourseData.setting
                : sgCourseData
                ? sgCourseData.setting
                : [];
        for (courseSetting of sgCourseSettingData) {
            const group_name =
                courseSetting._group_no !== undefined
                    ? courseSetting.gender === MALE
                        ? 'MG-' + courseSetting._group_no
                        : 'FG-' + courseSetting._group_no
                    : courseSetting.gender === MALE
                    ? 'MG-1'
                    : 'FG-1';
            // const group_name = courseSetting.gender === MALE ? 'MG-1' : 'FG-1';
            const deliveryGroup =
                courseSetting &&
                courseSetting.session_setting &&
                courseSetting.session_setting.length &&
                courseSetting.session_setting[0].groups
                    ? courseSetting.session_setting[0].groups
                    : [];
            const studentIds = deliveryGroup
                .map((ele) => ele._student_ids)
                .flat()
                .filter((studentIdElement) => studentIdElement.toString() === userId.toString());
            const groupStudentData = sgLevel.students
                .filter((ele) =>
                    studentIds.find((ele2) => ele2.toString() === ele._student_id.toString()),
                )
                .map((ele3) => {
                    return {
                        _student_id: ele3._student_id,
                        name: ele3.name,
                        academic_no: ele3.academic_no,
                        gender: ele3.gender,
                    };
                });
            studentWithGroupList.push({
                _id: courseSetting._id,
                gender: courseSetting.gender,
                group_name,
                students: groupStudentData,
            });
            studentWithOutGroupList = studentWithOutGroupList.concat(groupStudentData);
        }
        return {
            studentWithGroupList,
            studentWithOutGroupList,
        } /* { master_group, sgCourseData } */;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const studentActivitySLOReport = async (studentData, activityList, courseSloData) => {
    try {
        for (studentElement of studentData) {
            const studentActivityList = activityList.filter((ele) =>
                ele.students.find(
                    (ele2) => ele2._studentId.toString() === studentElement._student_id.toString(),
                ),
            );
            let studentMark = 0;
            const studentActivities = [];
            const studentActivitySlos = [];
            for (studentActivityElement of studentActivityList) {
                let studentQuestionMarks = 0;
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === studentElement._student_id.toString(),
                );
                const quizSolsData = [];
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    )
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = courseSloData.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const loc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                const loc2 = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSolsData[loc].count++;
                                else
                                    quizSolsData.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                                if (loc2 !== -1) studentActivitySlos[loc2].count++;
                                else
                                    studentActivitySlos.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        mark: 0,
                                        count: 1,
                                    });
                            }
                        }
                }
                if (quizSolsData.length) {
                    for (questionElement of studentActivityReports.questions) {
                        const questionData = studentActivityElement.questions.find(
                            (ele) => ele._id.toString() === questionElement._questionId.toString(),
                        );
                        // if (
                        //     questionData &&
                        //     questionElement &&
                        //     questionElement._optionId &&
                        //     questionData.correctOption &&
                        //     questionData.correctOption.toString() ===
                        //         questionElement._optionId.toString()
                        // ) {
                        const questionAnswered = getQuestionAnswered({
                            questionData,
                            questionElement,
                        });
                        if (questionAnswered) {
                            studentQuestionMarks++;
                            for (sloElement of questionData.questionData.sloIds) {
                                const sloLoc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloElement.toString(),
                                );
                                const overAllSloLoc = studentActivitySlos.findIndex(
                                    (ele) => ele._id.toString() === sloElement.toString(),
                                );
                                if (sloLoc !== -1) {
                                    quizSolsData[sloLoc].mark++;
                                    // quizSolsData[sloLoc].count++;
                                }
                                if (overAllSloLoc !== -1) {
                                    studentActivitySlos[overAllSloLoc].mark++;
                                    // studentActivitySlos[overAllSloLoc].count++;
                                }
                            }
                        }
                    }
                    studentMark +=
                        (studentQuestionMarks / studentActivityElement.questions.length) * 100;
                    for (SloElement of quizSolsData) {
                        SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                    }
                    studentActivities.push({
                        _id: studentActivityElement._id,
                        name: studentActivityElement.name,
                        startTime: studentActivityElement.startTime,
                        endTime: studentActivityElement.endTime,
                        quizType: studentActivityElement.quizType,
                        questionCount: studentActivityElement.questions.length,
                        noQuizAnswered: studentActivityElement.questions.length,
                        mark:
                            (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                        sloData: clone(quizSolsData),
                    });
                }
            }
            // studentElement.mark =
            //     studentActivityList.length !== 0 ? studentMark / studentActivitySlos.length : null;
            studentElement.studentActivities = clone(studentActivities);
            let studentSloMark = 0;
            for (SloElement of studentActivitySlos) {
                SloElement.mark = (SloElement.mark / SloElement.count) * 100;
                studentSloMark += SloElement.mark;
            }
            studentElement.mark =
                studentActivitySlos.length !== 0
                    ? studentSloMark / studentActivitySlos.length
                    : null;
            //studentElement.AllActivitySlos = clone(studentActivitySlos);
        }
        return studentData;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
//Get Course SLO
const getCourseSLO = async (courseId) => {
    try {
        // Course Data
        const { data: courseData, status: course_status } = await get(
            course,
            {
                _id: convertToMongoObjectId(courseId),
                isActive: true,
                isDeleted: false,
            },
            {
                _program_id: 1,
                framework: 1,
            },
        );
        if (!course_status) return { data: [], message: 'Course not found', status: false };
        if (!courseData.framework)
            return { data: [], message: 'framework not found', status: false };

        // Session Flow data
        const sessionFlowDetails = await get(
            session_order,
            {
                _course_id: convertToMongoObjectId(courseId),
                isActive: true,
                isDeleted: false,
            },
            {},
        );
        if (!sessionFlowDetails.status)
            return { data: [], message: 'Session flow not found', status: false };
        const slo = [];
        sessionFlowDetails.data.session_flow_data.forEach((eleSFD) => {
            if (eleSFD.slo && eleSFD.slo.length > 0) {
                eleSFD.slo.forEach((eleSLO) => {
                    slo.push({
                        _id: eleSLO._id,
                        no: eleSLO.no,
                        name: eleSLO.name,
                        delivery_symbol: eleSFD.delivery_symbol,
                        s_no: eleSFD.s_no,
                        _session_order_id: eleSFD._id,
                    });
                });
            }
        });

        return { data: slo, message: 'SLO List', status: true };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const getCourseSLOWithSessionOrderData = async (courseId, allSessOrderData, scheduleData, term) => {
    try {
        // Session Flow data
        const sessionFlowDetails = allSessOrderData.find(
            (ele) => ele._course_id.toString() === courseId.toString(),
        );
        if (!sessionFlowDetails)
            return { data: [], message: 'Session flow not found', status: false };
        const slo = [];
        sessionFlowDetails.session_flow_data.forEach((eleSFD) => {
            if (eleSFD.slo && eleSFD.slo.length > 0) {
                eleSFD.slo.forEach((eleSLO) => {
                    if (
                        scheduleData.find(
                            (eleSD) =>
                                eleSD.session._session_id.toString() === eleSFD._id.toString() &&
                                eleSD.term.toString() === term.toString(),
                        )
                    ) {
                        slo.push({
                            _id: eleSLO._id,
                            no: eleSLO.no,
                            name: eleSLO.name,
                            delivery_symbol: eleSFD.delivery_symbol,
                            s_no: eleSFD.s_no,
                            _session_order_id: eleSFD._id,
                        });
                    }
                });
            }
        });

        return { data: slo, message: 'SLO List', status: true };
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};
const getSurvey = async (
    institutionCalendarId,
    programId,
    levelNo,
    term,
    courseId,
    userId,
    surveyData,
) => {
    try {
        /* const surveyDetails = await get_list(
            surveyCollection,
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId, userId),
                _course_id: convertToMongoObjectId(courseId),
                _program_id: convertToMongoObjectId(programId),
                //yearNo: year,
                levelNo,
                term,
                _user_id: convertToMongoObjectId(userId),
                isActive: true,
                isDeleted: false,
            },
            {},
        ); */
        if (!surveyData.status)
            return { data: [], message: 'Session flow not found', status: false };
        const surveyDetails = surveyData.data.filter(
            (ele) =>
                ele._course_id.toString() === courseId.toString() &&
                ele._program_id.toString() === programId.toString() &&
                ele.levelNo.toString() === levelNo.toString() &&
                ele.term.toString() === term.toString(),
        );
        return { data: surveyDetails, message: 'Survey List', status: true };
    } catch (error) {
        console.log(error);
    }
};

// User Survey Yet to Rate Session SLO wise
const courseYetToRate = async ({ courseSessionOrder, userSurveyData }) => {
    try {
        let yetToRate = 0;
        for (sessionElement of courseSessionOrder) {
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionElement._id.toString(),
            );
            if (userSessionSurvey) {
                yetToRate += sessionElement.slo.filter(
                    (sessionSLOElement) =>
                        !userSessionSurvey.slos.find(
                            (userSessionSurveySLOElement) =>
                                userSessionSurveySLOElement.sloRating !== null &&
                                userSessionSurveySLOElement._slo_id.toString() ==
                                    sessionSLOElement._id.toString(),
                        ),
                ).length;
            } else yetToRate += sessionElement.slo.length;
        }
        return yetToRate;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// Self Evaluation Summary
const selfEvaluationSummary = async ({
    courseSessionOrder,
    userSurveyData,
    otherSurveyData,
    studentIds,
}) => {
    try {
        const sessionRating = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_type: sessionOrderElement.delivery_type,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
                delivery_topic: sessionOrderElement.delivery_topic,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            const userRating = {
                sloRating: null,
                slos: [],
            };
            const othersRating = {
                sloRating: 0,
                attendedCount: 0,
                noOfStudent: studentIds.length,
            };
            if (
                userSessionSurvey &&
                userSessionSurvey.slos.length !== 0 &&
                userSessionSurvey.slos.length === sessionOrderElement.slo.length &&
                !userSessionSurvey.slos.find((sloElement) => sloElement.sloRating === null)
            ) {
                userRating.sloRating = userSessionSurvey.slos
                    .map((sloELement) => sloELement.sloRating)
                    .reduce((a, b) => (a !== null && b !== null ? a + b : 0));
                if (userRating.sloRating !== null)
                    userRating.sloRating /= userSessionSurvey.slos.length;
                for (sloElement of userSessionSurvey.slos) {
                    const sessionOrderSlo = sessionOrderElement.slo.find(
                        (slosElement) =>
                            slosElement._id.toString() === sloElement._slo_id.toString(),
                    );
                    userRating.slos.push({
                        sloRating: sloElement.sloRating,
                        sloNo: sessionOrderSlo.no,
                        deliverySymbol: sessionOrderElement.delivery_symbol,
                    });
                }

                // Class Survey Rating
                const otherSessionSurvey = otherSurveyData.filter(
                    (userSurveyElement) =>
                        userSurveyElement._session_order_id.toString() ===
                        sessionOrderElement._id.toString(),
                );
                for (otherSessionSurveyElement of otherSessionSurvey) {
                    if (
                        otherSessionSurveyElement &&
                        otherSessionSurveyElement.slos &&
                        otherSessionSurveyElement.slos.length !== 0 &&
                        otherSessionSurveyElement.slos.length === sessionOrderElement.slo.length &&
                        !otherSessionSurveyElement.slos.find(
                            (sloElement) => sloElement.sloRating === null,
                        )
                    ) {
                        const sloRating = otherSessionSurveyElement.slos
                            .map((sloELement) => sloELement.sloRating)
                            .reduce((a, b) => (a !== null && b !== null ? a + b : 0));
                        if (sloRating !== null)
                            othersRating.sloRating +=
                                sloRating / otherSessionSurveyElement.slos.length;
                        othersRating.attendedCount++;
                    }
                }
                if (othersRating.sloRating !== null)
                    othersRating.sloRating /= othersRating.attendedCount;
            }
            sessionRating.push({ ...objectData, ...{ userRating }, ...{ othersRating } });
        }
        return sessionRating;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// Self Evaluation Slo
const selfEvaluationSlo = async ({
    courseSessionOrder,
    userSurveyData,
    otherSurveyData,
    studentIds,
}) => {
    try {
        const ratingReport = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _session_id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            // Class Survey Rating
            const otherSessionSurvey = otherSurveyData.filter(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            for (sessionSloElement of sessionOrderElement.slo) {
                const userRating =
                    userSessionSurvey &&
                    userSessionSurvey.slos &&
                    userSessionSurvey.slos.length !== 0 &&
                    userSessionSurvey.slos.find(
                        (sloElement) =>
                            sloElement._slo_id.toString() === sessionSloElement._id.toString(),
                    )
                        ? userSessionSurvey.slos.find(
                              (sloElement) =>
                                  sloElement._slo_id.toString() ===
                                  sessionSloElement._id.toString(),
                          ).sloRating
                        : null;
                const ratingObjects = {
                    _slo_id: sessionSloElement._id,
                    no: sessionSloElement.no,
                    name: sessionSloElement.name,
                    userRating: { sloRating: userRating },
                    othersRating: {
                        sloRating: 0,
                        attendedCount: 0,
                        noOfStudent: studentIds.length,
                    },
                };
                if (userRating !== null) {
                    for (otherSessionSurveyElement of otherSessionSurvey) {
                        if (
                            otherSessionSurveyElement &&
                            otherSessionSurveyElement.slos &&
                            otherSessionSurveyElement.slos.length !== 0
                        ) {
                            const otherSloRating = otherSessionSurveyElement.slos.find(
                                (othersSloElement) =>
                                    othersSloElement._slo_id.toString() ===
                                        sessionSloElement._id.toString() &&
                                    othersSloElement.sloRating !== undefined &&
                                    othersSloElement.sloRating !== null,
                            );
                            ratingObjects.othersRating.sloRating +=
                                otherSloRating !== undefined ? otherSloRating.sloRating : 0;
                            ratingObjects.othersRating.attendedCount +=
                                otherSloRating !== undefined ? 1 : 0;
                        }
                    }
                    ratingObjects.othersRating.sloRating /=
                        ratingObjects.othersRating.attendedCount;
                }
                ratingReport.push({
                    ...objectData,
                    ...ratingObjects,
                });
            }
        }
        return ratingReport;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

const courseCLOLists = async ({ courseData }) => {
    try {
        const cloDomains = [];
        const frameWork = {
            name: courseData.framework.name,
            code: courseData.framework.code,
        };
        for (courseFramework of courseData.framework.domains) {
            const clos = [];
            for (cloElement of courseFramework.clo) {
                const slos = [];
                if (cloElement.isDeleted === false && cloElement.isActive) {
                    for (sloElement of cloElement.slos) {
                        if (sloElement.mapped_value === 'TRUE') {
                            slos.push({
                                _id: sloElement.slo_id,
                                delivery_symbol: sloElement.delivery_symbol,
                                delivery_no: sloElement.delivery_no,
                                no: sloElement.no,
                            });
                        }
                    }
                    clos.push({
                        _id: cloElement._id,
                        no: cloElement.no,
                        name: cloElement.name,
                        slos,
                    });
                }
            }
            cloDomains.push({
                _id: courseFramework._id,
                name: courseFramework.name,
                no: courseFramework.no,
                clos,
            });
        }
        return { courseClo: cloDomains, frameWork };
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// Self Evaluation Slo
const selfEvaluationClo = async ({ courseClo, userSurveyData, otherSurveyData, studentIds }) => {
    try {
        userSurveyData = userSurveyData
            .map((userSurveyElement) =>
                userSurveyElement.slos.map((sloElement) => {
                    return {
                        _user_id: userSurveyElement._user_id,
                        ...sloElement.toJSON(),
                    };
                }),
            )
            .flat();
        otherSurveyData = otherSurveyData
            .map((otherSurveyElement) =>
                otherSurveyElement.slos.map((sloElement) => {
                    return {
                        _user_id: otherSurveyElement._user_id,
                        ...sloElement,
                    };
                }),
            )
            .flat();
        for (courseDomainElement of courseClo) {
            for (cloElement of courseDomainElement.clos) {
                const userSloData = userSurveyData.filter((userSurveySloElement) =>
                    cloElement.slos.find(
                        (sloIdElement) =>
                            sloIdElement._id.toString() === userSurveySloElement._slo_id.toString(),
                    ),
                );
                const othersSloData = otherSurveyData.filter((othersSurveySloElement) =>
                    cloElement.slos.find(
                        (sloIdElement) =>
                            sloIdElement._id.toString() ===
                            othersSurveySloElement._slo_id.toString(),
                    ),
                );
                const userRating = {
                    sloRating:
                        userSloData && userSloData.length !== 0
                            ? userSloData
                                  .map((sloELement) => sloELement.sloRating)
                                  .reduce((a, b) => (a !== null && b !== null ? a + b : 0))
                            : null,
                    slos: [],
                };
                if (userRating.sloRating !== null) userRating.sloRating /= userSloData.length;
                cloElement.userRating = userRating;
                const othersRating = {
                    sloRating: 0,
                    attendedCount: 0,
                    noOfStudent: studentIds.length,
                };
                for (sloElement of cloElement.slos) {
                    const sessionOrderSlo = userSloData.find(
                        (slosElement) =>
                            slosElement._slo_id.toString() === sloElement._id.toString(),
                    );
                    userRating.slos.push({
                        sloRating: sessionOrderSlo ? sessionOrderSlo.sloRating : null,
                        sloNo: sloElement.no,
                        deliverySymbol: sloElement.delivery_symbol,
                    });
                }
                if (userRating.sloRating !== null) {
                    const otherStudentIds = [
                        ...new Set(
                            othersSloData.map((othersSloElement) =>
                                othersSloElement._user_id.toString(),
                            ),
                        ),
                    ];
                    othersRating.attendedCount = otherStudentIds.length;
                    const otherUserRatings = [];
                    for (otherStudentIdElement of otherStudentIds) {
                        const otherUserSlos = othersSloData.filter(
                            (othersSloElement) =>
                                othersSloElement._user_id.toString() ===
                                    otherStudentIdElement.toString() &&
                                othersSloElement.sloRating !== undefined &&
                                othersSloElement.sloRating !== null,
                        );
                        otherUserRatings.push({
                            sloRating:
                                otherUserSlos
                                    .map((sloELement) => sloELement.sloRating)
                                    .reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                                otherUserSlos.length,
                            _user_id: otherStudentIdElement.toString(),
                        });
                    }
                    othersRating.sloRating =
                        otherUserRatings.length !== 0
                            ? otherUserRatings
                                  .map((sloELement) => sloELement.sloRating)
                                  .reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                              otherStudentIds.length
                            : null;
                }
                cloElement.othersRating = othersRating;
                delete cloElement.slos;
            }
        }
        return { courseClo };
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// Self Evaluation Outcome Analysis Slo wise
const selfEvaluationOutcomeSloWise = async ({
    courseSessionOrder,
    userSurveyData,
    settingLimit,
}) => {
    try {
        const ratingReport = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _session_id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            for (sessionSloElement of sessionOrderElement.slo) {
                const userRating =
                    userSessionSurvey &&
                    userSessionSurvey.slos &&
                    userSessionSurvey.slos.length !== 0 &&
                    userSessionSurvey.slos.find(
                        (sloElement) =>
                            sloElement._slo_id.toString() === sessionSloElement._id.toString(),
                    )
                        ? userSessionSurvey.slos.find(
                              (sloElement) =>
                                  sloElement._slo_id.toString() ===
                                  sessionSloElement._id.toString(),
                          ).sloRating
                        : null;
                const ratingObjects = {
                    _slo_id: sessionSloElement._id,
                    no: sessionSloElement.no,
                    name: sessionSloElement.name,
                    userRating,
                };
                ratingReport.push({
                    ...objectData,
                    ...ratingObjects,
                });
            }
        }

        const scalePoint = [];
        let scalePointSlos = [];
        const sessionRatingWithoutNull = ratingReport.filter(
            (ratingElement) =>
                ratingElement.userRating !== undefined && ratingElement.userRating !== null,
        );
        const startIncremental = settingLimit !== 5 ? 0 : 1;
        const endIncremental = settingLimit !== 5 ? 4 : 5;
        for (let incremental = startIncremental; incremental <= endIncremental; incremental++) {
            const pointDatas = sessionRatingWithoutNull
                .filter((ratingElement) => incremental === ratingElement.userRating)
                .map((ratingElement) => ratingElement._slo_id);
            const userScaleRating =
                pointDatas.length !== 0
                    ? (pointDatas.length / sessionRatingWithoutNull.length) * 100
                    : 0.0;
            scalePoint.push({
                scalePoint: incremental,
                userScaleRating,
                pointDatas,
            });
            scalePointSlos = [...scalePointSlos, ...pointDatas];
        }
        const ratingCounts = {
            ratedCount: sessionRatingWithoutNull.length,
            sloCount: ratingReport.length,
        };
        return {
            ...ratingCounts,
            scalePoint,
            ratingReport: ratingReport.filter((sloElement) =>
                scalePointSlos.find(
                    (pointSloElement) =>
                        pointSloElement.toString() === sloElement._slo_id.toString(),
                ),
            ),
        };
    } catch (error) {
        logger.error(
            error,
            'Survey -> courseYetToRate -> Course Outcome Analysis Survey Yet to Rate Error',
        );
        throw new Error(error);
    }
};
// Self Evaluation Outcome Analysis Clo wise
const selfEvaluationOutcomeCloWise = async ({ courseSessionOrder, userSurveyData }) => {
    try {
        const ratingReport = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _session_id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            for (sessionSloElement of sessionOrderElement.slo) {
                const userRating =
                    userSessionSurvey &&
                    userSessionSurvey.slos &&
                    userSessionSurvey.slos.length !== 0 &&
                    userSessionSurvey.slos.find(
                        (sloElement) =>
                            sloElement._slo_id.toString() === sessionSloElement._id.toString(),
                    )
                        ? userSessionSurvey.slos.find(
                              (sloElement) =>
                                  sloElement._slo_id.toString() ===
                                  sessionSloElement._id.toString(),
                          ).sloRating
                        : null;
                const ratingObjects = {
                    _slo_id: sessionSloElement._id,
                    no: sessionSloElement.no,
                    name: sessionSloElement.name,
                    userRating,
                };
                ratingReport.push({
                    ...objectData,
                    ...ratingObjects,
                });
            }
        }

        const scalePoint = [];
        const sessionRatingWithoutNull = ratingReport.filter(
            (ratingElement) => ratingElement.userRating && ratingElement.userRating !== null,
        );
        for (let incremental = 1; incremental <= 5; incremental++) {
            const pointDatas = sessionRatingWithoutNull
                .filter((ratingElement) => incremental === ratingElement.userRating)
                .map((ratingElement) => ratingElement._slo_id);
            const userScaleRating =
                pointDatas.length !== 0
                    ? (pointDatas.length / sessionRatingWithoutNull.length) * 100
                    : 0.0;
            scalePoint.push({
                scalePoint: incremental,
                userScaleRating,
                pointDatas,
            });
        }
        const ratingCounts = {
            ratedCount: sessionRatingWithoutNull.length,
            sloCount: ratingReport.length,
        };
        return { ...ratingCounts, scalePoint, ratingReport };
    } catch (error) {
        logger.error(
            error,
            'Survey -> courseYetToRate -> Course Outcome Analysis Survey Yet to Rate Error',
        );
        throw new Error(error);
    }
};

// User Survey Yet to Rate Session SLO wise
const courseSessionWiseYetToRate = async ({ courseSessionOrder, userSurveyData }) => {
    try {
        const yetToRate = [];
        for (sessionElement of courseSessionOrder) {
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionElement._id.toString(),
            );
            const yetToRateCount = userSessionSurvey
                ? sessionElement.slo.filter(
                      (sessionSLOElement) =>
                          !userSessionSurvey.slos.find(
                              (userSessionSurveySLOElement) =>
                                  userSessionSurveySLOElement.sloRating !== null &&
                                  userSessionSurveySLOElement._slo_id.toString() ==
                                      sessionSLOElement._id.toString(),
                          ),
                  ).length
                : sessionElement.slo.length;
            if (yetToRateCount !== 0)
                yetToRate.push({
                    _session_id: sessionElement._id,
                    s_no: sessionElement.s_no,
                    delivery_type: sessionElement.delivery_type,
                    delivery_symbol: sessionElement.delivery_symbol,
                    delivery_no: sessionElement.delivery_no,
                    delivery_topic: sessionElement.delivery_topic,
                    yetToRate: yetToRateCount,
                });
        }
        return yetToRate;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// Get Activity List based on Course wise
const activityQuestionList = async ({ userId, courseId, term, rotationCount }) => {
    try {
        const activityQuery = {
            isDeleted: false,
            quizType: QUIZ,
            courseId: convertToMongoObjectId(courseId),
            status: COMPLETED,
            studentCompletedQuiz: { $in: convertToMongoObjectId(userId) },
        };
        if (term) activityQuery.term = term;
        if (rotationCount && parseInt(rotationCount) !== 0)
            activityQuery.rotation_count = parseInt(rotationCount);
        const activityList = await get_list(activity, activityQuery);
        if (!activityList.status) activityList.data = [];
        const questionIds = activityList.data
            .map((activityElement) =>
                activityElement.questions
                    .map((questionElement) => questionElement._id.toString())
                    .flat(),
            )
            .flat();
        const questionList = await get_list(question, {
            _id: { $in: questionIds },
            isDeleted: false,
        });
        if (!questionList.status) questionList.data = [];
        activityList.data = clone(activityList.data);
        for (activityElement of activityList.data) {
            const questionWithWrongOption = [];
            for (questionElement of activityElement.questions) {
                const questionData = questionList.data.find(
                    (ele) => ele._id.toString() === questionElement._id.toString(),
                );
                if (!questionData) continue;
                const optionData = questionData.options.find((ele2) => ele2.answer);
                const wrongOptionData = questionData.options.find((ele2) => ele2.answer === false);
                if (questionData && optionData) {
                    questionElement.correctOption = optionData._id;
                    questionElement.questionData = questionData;
                }
                if (questionData && ['SQA', 'MQ'].includes(questionData.questionType)) {
                    questionElement.questionData = questionData;
                }
                questionWithWrongOption.push({
                    _id: convertToMongoObjectId(),
                    _questionId: convertToMongoObjectId(questionElement._id),
                    _optionId: wrongOptionData && convertToMongoObjectId(wrongOptionData._id),
                });
            }
            for (activityStudentElement of activityElement.students) {
                const missingQuestionDatas = questionWithWrongOption.filter(
                    (wrongElement) =>
                        !activityStudentElement.questions.find(
                            (questionElement) =>
                                wrongElement._questionId.toString() ===
                                questionElement._questionId.toString(),
                        ),
                );
                if (missingQuestionDatas && missingQuestionDatas.length !== 0) {
                    activityStudentElement.questions = [
                        ...activityStudentElement.questions,
                        ...missingQuestionDatas,
                    ];
                }
            }
        }
        return activityList;
    } catch (error) {
        logger.error(error, 'Survey -> activityQuestionList -> Course Activity List Error');
        throw new Error(error);
    }
};

// Comparison Report Summary
const comparisonReportSummary = async ({
    userId,
    courseSessionOrder,
    userSurveyData,
    activityList,
}) => {
    try {
        const sessionRating = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_type: sessionOrderElement.delivery_type,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
                delivery_topic: sessionOrderElement.delivery_topic,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );
            const selfEvaluationRating = {
                sloRating: null,
                sloRatingPercentage: null,
                slos: [],
            };
            if (
                userSessionSurvey &&
                userSessionSurvey.slos.length !== 0 &&
                userSessionSurvey.slos.length === sessionOrderElement.slo.length &&
                !userSessionSurvey.slos.find((sloElement) => sloElement.sloRating === null)
            ) {
                selfEvaluationRating.sloRating = userSessionSurvey.slos
                    .map((sloELement) => sloELement.sloRating)
                    .reduce((a, b) => (a !== null && b !== null ? a + b : 0));
                if (selfEvaluationRating.sloRating !== null)
                    selfEvaluationRating.sloRating /= userSessionSurvey.slos.length;
                for (sloElement of userSessionSurvey.slos) {
                    const sessionOrderSlo = sessionOrderElement.slo.find(
                        (slosElement) =>
                            slosElement._id.toString() === sloElement._slo_id.toString(),
                    );
                    selfEvaluationRating.slos.push({
                        // rating: sloElement.sloRating,
                        sloRating: sloElement.sloRating,
                        sloRatingPercentage: (sloElement.sloRating / 5) * 100,
                        sloNo: sessionOrderSlo.no,
                        deliverySymbol: sessionOrderElement.delivery_symbol,
                    });
                }
                selfEvaluationRating.sloRatingPercentage =
                    (selfEvaluationRating.sloRating / 5) * 100;
            }

            // Activity Report
            const QuizRating = {
                sloRating: null,
                sloRatingPercentage: null,
                slos: [],
            };
            const quizSolsData = [];
            const sessionActivity = activityList.filter((activityElement) =>
                activityElement.sessionFlowIds.find(
                    (sessionIdElement) =>
                        sessionIdElement._id.toString() === sessionOrderElement._id.toString(),
                ),
            );
            for (studentActivityElement of sessionActivity) {
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === userId.toString(),
                );
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds
                    )
                        for (sloIdElement of activityQuestionElement.questionData.sloIds) {
                            const sloData = sessionOrderElement.slo.find(
                                (ele2) => ele2._id.toString() === sloIdElement.toString(),
                            );
                            if (sloData) {
                                const quizSolObject = {
                                    _id: sloData._id,
                                    sloNo: sloData.no,
                                    deliverySymbol: sessionOrderElement.delivery_symbol,
                                    mark: 0,
                                    count: 1,
                                };
                                const loc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloIdElement.toString(),
                                );
                                if (loc !== -1) quizSolsData[loc].count++;
                                else quizSolsData.push(quizSolObject);
                            }
                        }
                }
                if (quizSolsData.length) {
                    let studentQuestionMarks = 0;
                    for (questionElement of studentActivityReports.questions) {
                        const questionData = studentActivityElement.questions.find(
                            (ele) => ele._id.toString() === questionElement._questionId.toString(),
                        );
                        if (
                            questionData &&
                            questionElement._optionId &&
                            questionData.correctOption.toString() ===
                                questionElement._optionId.toString()
                        ) {
                            studentQuestionMarks++;
                            for (sloElement of questionData.questionData.sloIds) {
                                const sloLoc = quizSolsData.findIndex(
                                    (ele) => ele._id.toString() === sloElement.toString(),
                                );
                                if (sloLoc !== -1) {
                                    quizSolsData[sloLoc].mark++;
                                }
                            }
                        }
                    }
                    // studentActivities.push({
                    //     _id: studentActivityElement._id,
                    //     name: studentActivityElement.name,
                    //     startTime: studentActivityElement.startTime,
                    //     endTime: studentActivityElement.endTime,
                    //     quizType: studentActivityElement.quizType,
                    //     questionCount: studentActivityElement.questions.length,
                    //     noQuizAnswered: studentActivityElement.questions.length,
                    //     mark:
                    //         (studentQuestionMarks / studentActivityElement.questions.length) * 100,
                    //     solData: clone(quizSolsData),
                    // });
                }
                for (quizSolsElement of quizSolsData) {
                    quizSolsElement.rating = quizSolsElement.mark / quizSolsElement.count;
                    quizSolsElement.sloRatingPercentage = quizSolsElement.rating * 100;
                    quizSolsElement.sloRating = quizSolsElement.rating * 5;
                }
            }
            if (quizSolsData.length) {
                const sessionSlos = quizSolsData.map((sloELement) => sloELement.rating);
                if (sessionSlos.length)
                    QuizRating.sloRatingPercentage =
                        (sessionSlos.reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                            sessionSlos.length) *
                        100;
                QuizRating.slos = quizSolsData.map((sloElement) => {
                    return {
                        // rating: sloElement.rating,
                        sloRating: sloElement.sloRating,
                        sloRatingPercentage: sloElement.sloRatingPercentage,
                        sloNo: sloElement.sloNo,
                        deliverySymbol: sloElement.deliverySymbol,
                    };
                });
                QuizRating.sloRating = (QuizRating.sloRatingPercentage / 100) * 5;
            }
            sessionRating.push({ ...objectData, ...{ selfEvaluationRating }, ...{ QuizRating } });
        }

        return sessionRating;
    } catch (error) {
        logger.error(
            error,
            'Survey -> comparisonReportSummary -> Course Comparison Report Summary Error',
        );
        throw new Error(error);
    }
};

// Self Evaluation Slo
const comparisonReportSlo = async ({
    userId,
    courseSessionOrder,
    userSurveyData,
    activityList,
}) => {
    try {
        const ratingReport = [];
        for (sessionOrderElement of courseSessionOrder) {
            const objectData = {
                _session_id: sessionOrderElement._id,
                s_no: sessionOrderElement.s_no,
                delivery_symbol: sessionOrderElement.delivery_symbol,
                delivery_no: sessionOrderElement.delivery_no,
            };
            const userSessionSurvey = userSurveyData.find(
                (userSurveyElement) =>
                    userSurveyElement._session_order_id.toString() ===
                    sessionOrderElement._id.toString(),
            );

            for (sessionSloElement of sessionOrderElement.slo) {
                const userRating =
                    userSessionSurvey &&
                    userSessionSurvey.slos &&
                    userSessionSurvey.slos.length !== 0 &&
                    userSessionSurvey.slos.find(
                        (sloElement) =>
                            sloElement._slo_id.toString() === sessionSloElement._id.toString(),
                    )
                        ? userSessionSurvey.slos.find(
                              (sloElement) =>
                                  sloElement._slo_id.toString() ===
                                  sessionSloElement._id.toString(),
                          ).sloRating
                        : null;

                // Activity Report
                const quizObject = {
                    mark: 0,
                    count: 0,
                    sloRatingPercentage: 0,
                };
                const sessionActivity = activityList.filter((activityElement) =>
                    activityElement.sessionFlowIds.find(
                        (sessionIdElement) =>
                            sessionIdElement._id.toString() === sessionOrderElement._id.toString(),
                    ),
                );
                for (studentActivityElement of sessionActivity) {
                    const studentActivityReports = studentActivityElement.students.find(
                        (ele) => ele._studentId.toString() === userId.toString(),
                    );
                    for (activityQuestionElement of studentActivityElement.questions) {
                        if (
                            activityQuestionElement.questionData &&
                            activityQuestionElement.questionData.sloIds &&
                            activityQuestionElement.questionData.sloIds.find(
                                (sloIdElement) =>
                                    sloIdElement.toString() === sessionSloElement._id.toString(),
                            )
                        )
                            quizObject.count++;
                    }
                    if (quizObject.count) {
                        for (questionElement of studentActivityReports.questions) {
                            const questionData = studentActivityElement.questions.find(
                                (ele) =>
                                    ele._id.toString() === questionElement._questionId.toString(),
                            );
                            if (
                                questionData &&
                                questionElement._optionId &&
                                questionData.correctOption.toString() ===
                                    questionElement._optionId.toString() &&
                                questionData.questionData.sloIds.find(
                                    (sloIdElement) =>
                                        sloIdElement.toString() ===
                                        sessionSloElement._id.toString(),
                                )
                            )
                                quizObject.mark++;
                        }
                    }
                }
                quizObject.sloRatingPercentage =
                    quizObject.count !== null ? (quizObject.mark / quizObject.count) * 100 : null;
                const ratingObjects = {
                    _slo_id: sessionSloElement._id,
                    no: sessionSloElement.no,
                    name: sessionSloElement.name,
                    selfEvaluationRating: {
                        sloRating: userRating,
                        sloRatingPercentage: userRating !== null ? (userRating / 5) * 100 : null,
                    },
                    QuizRating: {
                        sloRating:
                            quizObject.sloRatingPercentage !== null
                                ? (quizObject.sloRatingPercentage / 100) * 5
                                : null,
                        sloRatingPercentage: quizObject.sloRatingPercentage,
                    },
                };
                ratingReport.push({
                    ...objectData,
                    ...ratingObjects,
                });
            }
        }
        return ratingReport;
    } catch (error) {
        logger.error(error, 'Survey -> comparisonReportSlo -> Course Comparison Report SLO Error');
        throw new Error(error);
    }
};

// Self Evaluation Slo
const comparisonReportClo = async ({ userId, userSurveyData, activityList, courseClo }) => {
    try {
        userSurveyData = userSurveyData
            .map((userSurveyElement) =>
                userSurveyElement.slos.map((sloElement) => {
                    return {
                        _user_id: userSurveyElement._user_id,
                        ...sloElement.toJSON(),
                    };
                }),
            )
            .flat();
        // Activity Report flow
        const activityFlow = (sloElement) => {
            const quizObject = {
                mark: 0,
                count: 0,
            };
            for (studentActivityElement of activityList) {
                const studentActivityReports = studentActivityElement.students.find(
                    (ele) => ele._studentId.toString() === userId.toString(),
                );
                for (activityQuestionElement of studentActivityElement.questions) {
                    if (
                        activityQuestionElement.questionData &&
                        activityQuestionElement.questionData.sloIds &&
                        activityQuestionElement.questionData.sloIds.find(
                            (sloIdElement) => sloIdElement.toString() === sloElement._id.toString(),
                        )
                    )
                        quizObject.count++;
                }
                if (quizObject.count) {
                    for (questionElement of studentActivityReports.questions) {
                        const questionData = studentActivityElement.questions.find(
                            (ele) => ele._id.toString() === questionElement._questionId.toString(),
                        );
                        if (
                            questionData &&
                            questionElement._optionId &&
                            questionData.correctOption.toString() ===
                                questionElement._optionId.toString() &&
                            questionData.questionData.sloIds.find(
                                (sloIdElement) =>
                                    sloIdElement.toString() === sloElement._id.toString(),
                            )
                        )
                            quizObject.mark++;
                    }
                }
            }
            return quizObject;
        };

        for (courseDomainElement of courseClo.courseClo) {
            for (cloElement of courseDomainElement.clos) {
                const userSloData = userSurveyData.filter(
                    (userSurveySloElement) =>
                        userSurveySloElement.sloRating !== null &&
                        cloElement.slos.find(
                            (sloIdElement) =>
                                sloIdElement._id.toString() ===
                                userSurveySloElement._slo_id.toString(),
                        ),
                );
                const userSloRating =
                    userSloData && userSloData.length !== 0
                        ? userSloData
                              .map((sloELement) => sloELement.sloRating)
                              .reduce((a, b) => (a !== null && b !== null ? a + b : 0)) /
                          userSloData.length
                        : null;
                const userRating = {
                    // sloRating: userSloRating,
                    selfEvaluationRating: {
                        sloRating: userSloRating,
                        sloRatingPercentage:
                            userSloRating !== null ? (userSloRating / 5) * 100 : null,
                    },
                    QuizRating: {
                        sloRating: null,
                        sloRatingPercentage: null,
                        quizObject: {
                            mark: 0,
                            count: 0,
                        },
                    },
                    slos: [],
                };
                for (sloElement of cloElement.slos) {
                    const sessionOrderSlo = userSloData.find(
                        (slosElement) =>
                            slosElement._slo_id.toString() === sloElement._id.toString(),
                    );
                    const quizSlos = activityFlow(sloElement);
                    if (quizSlos.count && quizSlos.count !== 0) {
                        userRating.QuizRating.quizObject.mark +=
                            (quizSlos.mark / quizSlos.count) * 100;
                        userRating.QuizRating.quizObject.count++;
                    }
                    userRating.slos.push({
                        // sloRating: sessionOrderSlo ? sessionOrderSlo.sloRating : null,
                        sloNo: sloElement.no,
                        deliverySymbol: sloElement.delivery_symbol,
                        selfEvaluationRating: {
                            sloRating:
                                sessionOrderSlo && sessionOrderSlo !== null
                                    ? sessionOrderSlo.sloRating
                                    : null,
                            sloRatingPercentage:
                                sessionOrderSlo && sessionOrderSlo !== null
                                    ? (sessionOrderSlo.sloRating / 5) * 100
                                    : null,
                        },
                        // quizObject: quizSlos,
                        QuizRating: {
                            sloRating:
                                quizSlos.count && quizSlos.count !== 0
                                    ? (quizSlos.mark / quizSlos.count) * 5
                                    : null,
                            sloRatingPercentage:
                                quizSlos.count && quizSlos.count !== 0
                                    ? (quizSlos.mark / quizSlos.count) * 100
                                    : null,
                        },
                    });
                }
                const quizClo = activityFlow({ _id: cloElement._id });
                if (quizClo.count && quizClo.count !== 0) {
                    userRating.QuizRating.quizObject.mark += (quizClo.mark / quizClo.count) * 100;
                    userRating.QuizRating.quizObject.count++;
                }
                if (userRating.QuizRating.quizObject.count !== 0) {
                    userRating.QuizRating.sloRatingPercentage =
                        userRating.QuizRating.quizObject.mark /
                        userRating.QuizRating.quizObject.count;
                    userRating.QuizRating.sloRating =
                        (userRating.QuizRating.sloRatingPercentage / 100) * 5;
                    delete userRating.QuizRating.quizObject;
                }
                cloElement.userRating = userRating;
                delete cloElement.slos;
            }
        }
        return courseClo;
    } catch (error) {
        logger.error(error, 'Survey -> courseYetToRate -> Course SLO Survey Yet to Rate Error');
        throw new Error(error);
    }
};

// Survey Rating Changes
const surveyRatingChanges = async ({ userSurveyData, settingLimit }) => {
    try {
        for (surveyElement of userSurveyData) {
            if (surveyElement.ratingValue !== settingLimit)
                for (sloElement of surveyElement.slos) {
                    sloElement.sloRating =
                        surveyElement.ratingValue !== settingLimit
                            ? settingLimit === 4 && surveyElement.ratingValue === 5
                                ? parseInt(sloElement.sloRating) - 1
                                : parseInt(sloElement.sloRating) + 1
                            : sloElement.sloRating;
                    // settingLimit === 4
                    // ? parseInt(sloElement.sloRating) - 1
                    //  : sloElement.sloRating;
                }
        }
        return userSurveyData;
    } catch (error) {
        logger.error(error, 'Survey -> surveyRatingChanges -> User Survey Changes Error');
        throw new Error(error);
    }
};

module.exports = {
    getSessionSLOSurvey,
    getSessionWiseSLO,
    getStudentAttendedSession,
    getRatingByCourses,
    getSessions,
    getCourseIdsWithSessions,
    getAllCourses,
    activityQuestionList,
    studentGroupList,
    studentActivitySLOReport,
    getCourseSLO,
    getSurvey,
    getCourseSLOWithSessionOrderData,
    studentGroupListNew,
    activityQuestionListWithActivityData,
    courseYetToRate,
    selfEvaluationSummary,
    selfEvaluationSlo,
    selfEvaluationClo,
    selfEvaluationOutcomeSloWise,
    courseCLOLists,
    courseSessionWiseYetToRate,
    comparisonReportSummary,
    comparisonReportSlo,
    comparisonReportClo,
    selfEvaluationOutcomeCloWise,
    getScheduleSessionWiseSLO,
    surveyRatingChanges,
};
