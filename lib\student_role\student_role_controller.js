const format = require("./student_role_format");
const functions = require("../utility/common_functions");
const StudentRole = require("./student_role_model");

exports.create = async (req, res) => {
  let studentRole = new StudentRole({
    name: req.body.name,
    parent_id: req.body.parent_id,
  });
  studentRole.save(function (err) {
    if (!err) res.send("Student Role Created successfully");
    else res.send({ message: "Error on create student role", err: err });
  });
};

exports.read = async (req, res) => {
  if(req.params._id) {
    StudentRole.find({ _id: req.params._id}, (err, role) => {
      if (!err) res.send(role);
      else res.send("Error on getting role");
    });
  }
  StudentRole.find({}, (err, role) => {
    if (!err) res.send(format.listToTree(role, req.params._id));
    else res.send("Error on getting role");
  });
};

exports.update = async (req, res) => {
  StudentRole.findByIdAndUpdate(
    req.params._id,
    { $set: req.body },
    (err, role) => {
      if (!err) res.send("Student Role Updated successfully");
      else res.send("Student Role Update faild");
    }
  );
};

exports.delete = async (req, res) => {
  StudentRole.findByIdAndRemove(req.params._id, function (err) {
    if (!err) res.send("Student Role Deleted successfully");
    else res.send("Student Role Delete faild");
  });
};
