const {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
    AM,
    PM,
    ENGLISH,
    LANGUAGE,
    JPG,
    PNG,
    PDF,
    SVG,
    DOC,
    DAILY,
    WEEKLY,
    CUSTOM,
    COMMON,
} = require('../../utility/enums');

const {
    DS_PROGRAM_KEY,
    DS_TERM_KEY,
    DEPARTMENT,
    DS_SUBJECT_KEY,
    DS_COURSE_KEY,
    DS_YEAR_KEY,
    DS_PHASE_KEY,
    DS_LEVEL_KEY,
    PREREQUISITE,
    CREDIT_HOURS_MODE,
    DS_PRE_REQUISITE,
    DS_STANDARD_KEY,
    DS_THEME_KEY,
    DS_SUBTHEME_KEY,
    DS_SELECTIVE_KEY,
    INDEPENDENT_COURSE_CREDIT_HOURS_MODE,
    DS_ONSITE_KEY,
    DS_REMOTE_KEY,
} = require('../../utility/constants');

const departmentHierarchyStructure = () => {
    return [
        {
            name: 'Program Dominant System',
            isActive: true,
        },
        {
            name: 'Department Dominant System',
            isActive: false,
        },
        {
            name: 'Program & Department Mixed System',
            isActive: false,
        },
    ];
};
const mockSessionData = (session = '') => {
    if (session) {
        return session;
    }

    return {
        start: {
            hour: 8,
            minute: 0,
            format: AM,
        },
        end: {
            hour: 5,
            minute: 0,
            format: PM,
        },
    };
};

const mockWorkingDaysData = (session = '') => {
    const workingDays = [];
    const days = [MONDAY, TUESDAY, WEDNESDAY, THURSDAY, FRIDAY, SATURDAY, SUNDAY];

    days.forEach((day) => {
        workingDays.push({
            name: day,
            session: mockSessionData(session),
            isActive: false,
        });
    });

    return workingDays;
};

const mockCurriculumNamingData = () => {
    return [
        {
            mode: 1,
            isDefault: true,
        },
        {
            mode: 2,
            isDefault: false,
        },
        {
            mode: 3,
            isDefault: false,
        },
    ];
};

const mockCreditHoursData = () => {
    return [
        {
            mode: CREDIT_HOURS_MODE.STANDARD,
            isDefault: true,
        },
        {
            mode: CREDIT_HOURS_MODE.DYNAMIC,
            isDefault: false,
        },
    ];
};

const mockProgramDurationFormat = () => {
    return [
        {
            format: DS_YEAR_KEY,
            defaultInput: DS_YEAR_KEY,
            isDefault: true,
            withoutLabel: false,
        },
        {
            format: DS_PHASE_KEY,
            defaultInput: DS_PHASE_KEY,
            isDefault: false,
            withoutLabel: false,
        },
    ];
};
const mockIndependentCourseCreditHoursData = () => {
    return [
        {
            mode: INDEPENDENT_COURSE_CREDIT_HOURS_MODE.STANDARD,
            isDefault: true,
        },
        {
            mode: INDEPENDENT_COURSE_CREDIT_HOURS_MODE.DYNAMIC,
            isDefault: false,
        },
        {
            mode: INDEPENDENT_COURSE_CREDIT_HOURS_MODE.CONTACT,
            isDefault: false,
        },
    ];
};

const defaultLanguage = () => {
    return [
        {
            code: ENGLISH,
            name: LANGUAGE.ENGLISH,
            isDefault: true,
        },
    ];
};
const privacySettings = () => {
    return [
        {
            name: 'Blur Candidates photo other than verification',
            isActive: true,
        },
    ];
};
const getMockLabels = () => {
    return [
        { label: 'Programs', labelEnum: DS_PROGRAM_KEY },
        { label: 'Pre-Requisite Course', labelEnum: DS_PRE_REQUISITE },
        { label: 'Department', labelEnum: DEPARTMENT },
        { label: 'Subject', labelEnum: DS_SUBJECT_KEY },
        { label: 'Course', labelEnum: DS_COURSE_KEY },
        { label: 'Theme', labelEnum: DS_THEME_KEY },
        { label: 'Sub Theme', labelEnum: DS_SUBTHEME_KEY },
        { label: 'Selective', labelEnum: DS_SELECTIVE_KEY },
        { label: 'Standard', labelEnum: DS_STANDARD_KEY },
        { label: 'Year', labelEnum: DS_YEAR_KEY },
        { label: 'Phase', labelEnum: DS_PHASE_KEY },
        { label: 'Level', labelEnum: DS_LEVEL_KEY },
        { label: 'Term', labelEnum: DS_TERM_KEY },
        { label: 'Onsite', labelEnum: DS_ONSITE_KEY },
        { label: 'Remote', labelEnum: DS_REMOTE_KEY },
    ];
};

const getMockGenerateLabel = () => {
    return [{ label: '', labelEnum: '' }];
};

const mockProgramInputConfigurationData = (languageCode = '', isDefault = false) => {
    const labels = getMockLabels();

    const labelConfiguration = [];
    labels.forEach((labelEntry) => {
        labelConfiguration.push({
            name: labelEntry.label,
            defaultInput: labelEntry.labelEnum,
            translatedInput: '',
        });
    });

    if (isDefault) {
        return {
            language: languageCode,
            labels: labelConfiguration,
        };
    }

    return {
        $push: {
            'globalConfiguration.programInput.labelConfiguration': {
                language: languageCode,
                labels: labelConfiguration,
            },
        },
    };
};

const mockLabelFieldConfigurationData = (languageCode) => {
    return {
        language: languageCode,
        basicDetails: [
            {
                name: 'First Name',
                mappingKey: 'firstName',
            },
            {
                name: 'Middle Name',
                mappingKey: 'middleName',
            },
            {
                name: 'Last Name',
                mappingKey: 'lastName',
            },
            {
                name: 'Family Name',
                mappingKey: 'familyName',
            },
            {
                name: 'Gender',
                mappingKey: 'gender',
            },
            {
                name: 'Phone Number',
                isCompulsory: true,
                defaultValue: '+91',
                allowToChange: true,
                mappingKey: 'no',
            },
            {
                name: 'Employee Id',
                mappingKey: 'user_id',
            },
            {
                name: 'Email Id',
                isCompulsory: true,
                mappingKey: 'email',
            },
        ],
        profileDetails: [
            { name: 'Passport Number', mappingKey: 'passportNo' },
            { name: 'Date of Birth', mappingKey: 'dob' },
            { name: 'Nationality', mappingKey: 'nationalityId' },
            { name: 'National Id', mappingKey: '_nationality_id' },
        ],

        addressDetails: [
            { name: 'Building no & Street name', mappingKey: 'buildingStreetName' },
            { name: 'Floor Number', mappingKey: 'floorNo' },
            { name: 'Country', mappingKey: 'country' },
            { name: 'State', mappingKey: 'district' },
            { name: 'City', mappingKey: 'city' },
            { name: 'Zipcode', mappingKey: 'zipCode' },
        ],
        contactDetails: [
            { name: 'Office Number', mappingKey: 'officeRoomNo' },
            { name: 'Office Extension', mappingKey: 'officeExtension' },
        ],
    };
};

const mockStudentLabelFieldConfigurationData = (languageCode) => {
    return {
        language: languageCode,
        basicDetails: [
            {
                name: 'First Name',
                mappingKey: 'firstName',
            },
            {
                name: 'Middle Name',
                mappingKey: 'middleName',
            },
            {
                name: 'Last Name',
                mappingKey: 'lastName',
            },
            {
                name: 'Family Name',
                mappingKey: 'familyName',
            },
            {
                name: 'Gender',
                mappingKey: 'gender',
            },
            {
                name: 'Phone Number',
                isCompulsory: true,
                defaultValue: '+91',
                allowToChange: true,
                mappingKey: 'no',
            },
            {
                name: 'Academic No',
                mappingKey: 'user_id',
            },
            {
                name: 'Programs',
                isCompulsory: true,
                mappingKey: 'program_no',
            },
            {
                name: 'Term',
                isCompulsory: true,
                mappingKey: 'batch',
            },
            {
                name: 'Enrolled Year',
                mappingKey: 'enrollment_year',
            },
            {
                name: 'Email Id',
                isCompulsory: true,
                mappingKey: 'email',
            },
        ],
        profileDetails: [
            { name: 'Passport Number', mappingKey: 'passportNo' },
            { name: 'Date of Birth', mappingKey: 'dob' },
            { name: 'Nationality', mappingKey: 'nationalityId' },
            { name: 'National Id', mappingKey: '_nationality_id' },
        ],

        addressDetails: [
            { name: 'Building no & Street name', mappingKey: 'buildingStreetName' },
            { name: 'Floor Number', mappingKey: 'floorNo' },
            { name: 'Country', mappingKey: 'country' },
            { name: 'State', mappingKey: 'district' },
            { name: 'City', mappingKey: 'city' },
            { name: 'Zipcode', mappingKey: 'zipCode' },
        ],
        otherContactDetails: [
            {
                name: 'Parent/Guardian Phone Number',
                mappingKey: 'parentGuardianPhoneNo',
                isMandatory: true,
            },
            {
                name: 'Parent/Guardian Email id',
                mappingKey: 'parentGuardianEmailId',
                isMandatory: false,
            },
            {
                name: 'Spouse Phone Number',
                mappingKey: 'spouseGuardianPhoneNo',
                isMandatory: false,
            },
        ],
    };
};

const mockBioMetricConfigurationData = () => {
    return [
        { labelName: 'Online verification', isActive: true },
        { labelName: 'Offline verification', isActive: false },
        { labelName: 'Both verification', isActive: false },
    ];
};
const mockMailConfigurationData = (labelName) => {
    const mailContent = [
        {
            labelName: 'Request for verification',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>Greetings!</p>' +
                '<br>' +
                '<p>As per the Dean&apos;s instruction, you are requested to register in DigiClass Application by <a href="Clicking_Here"> Clicking_Here </a></p>' +
                '<br>' +
                '<p>Please click the above link to complete it.</p>' +
                '<br>' +
                '<p>your temporary password is Temp_Password</p>' +
                '<br>' +
                '<p>You are requested to complete the Registration Process on or before the Date</p>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
        {
            labelName: 'Profile moved to invalid',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>Greetings!</p>' +
                '<br>' +
                '<p>Your Profile has been successfully validated and Registered in DigiClass</p>' +
                '<br>' +
                '<p>The following Documents have been Flagged as In-Complete / Invalid.</p>' +
                '<br>' +
                '<p>Flagged_Documents' +
                '</p>' +
                '<br>' +
                '<p>Please Clicking_Here to Login into DigiClass Application</p>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
        {
            labelName: 'Profile validation successful',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>Greetings!</p>' +
                '<br>' +
                '<p>Your Profile has been successfully validated and Registered in DigiClass.</p>' +
                '<br>' +
                '<p>Flagged_Documents' +
                '<br>' +
                '<p>Please Clicking_Here to Login into DigiClass Application</p>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
        {
            labelName: 'Expired mail',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>Greetings!</p>' +
                '<br>' +
                '<p>Your Profile has been expired</p>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
        {
            labelName: 'Inactivated',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>Greetings!</p>' +
                '<br>' +
                '<p>Your Profile has been Inactivated</p>' +
                '<br>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
        {
            labelName: 'Back to active',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>Greetings!</p>' +
                '<br>' +
                '<p>Your Profile has been activated</p>' +
                '<br>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
        {
            labelName: 'Forgot password',
            labelBody:
                '<p>Dear User_Name,</p>' +
                '<br>' +
                '<p>To reset your password <a href="Clicking_Here"> Clicking_Here </a> </p>' +
                '<br>' +
                '<p>Please click the above link to complete it.</p>' +
                '<br>' +
                '<p>Best Regards</p>' +
                '<br>' +
                '<p>College_Name</p>',
            isActive: true,
        },
    ];
    if (!labelName) return mailContent;
    const result = [];
    mailContent.find((obj) => {
        if (obj.labelName === labelName) {
            result.push(obj);
        }
    });
    return result[0].labelBody;
};
const mockDepartmentSubjectConfigurationData = () => {
    return [
        { labelName: 'Mode 1', isActive: true },
        { labelName: 'Mode 2', isActive: false },
    ];
};

const mockdepartmentManagementConfigurationData = () => {
    return {
        departmentTypes: [{ labelName: 'Academic' }, { labelName: 'Administrative' }],
    };
};
const mockDocumentConfigurationData = () => {
    return {
        documentFormat: [JPG, PNG, PDF, SVG, DOC],
        documentSize: COMMON,
        documentMaximumSize: 6,
        chooseDocuments: [
            {
                documentCategory: 'Id proof',
                document: [
                    {
                        labelName: 'Resident / National Id',
                        size: 20,
                        isMandatory: true,
                    },
                    {
                        labelName: 'Passport',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
            {
                documentCategory: 'Educational Details',
                document: [
                    {
                        labelName: 'Degree Certificate',
                        size: 20,
                        isMandatory: false,
                    },
                ],
            },
            {
                documentCategory: 'Address Details',
                document: [
                    {
                        labelName: 'Postal Address',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
            {
                documentCategory: 'Employee Details',
                document: [
                    {
                        labelName: 'Employee ID',
                        size: 20,
                        isMandatory: true,
                    },
                    {
                        labelName: 'Appointment Order',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
            {
                documentCategory: 'Medicine Details',
                document: [
                    {
                        labelName: 'Vaccination certificate',
                        size: 20,
                        isMandatory: false,
                    },
                ],
            },
        ],
        allowRemainderMail: {
            isMandatoryDocument: true,
            recurring: DAILY,
            recurringInterval: 2,
            time: '9:00 AM',
        },
        notificationMail: {
            label: 'Mandatory Documentation Notification Mail',
            time: '6:00 AM',
            labelBody: '',
        },
    };
};

const mockStudentDocumentConfigurationData = () => {
    return {
        documentFormat: [JPG, PNG, PDF, SVG, DOC],
        documentSize: COMMON,
        documentMaximumSize: 6,
        chooseDocuments: [
            {
                documentCategory: 'Id proof',
                document: [
                    {
                        labelName: 'Resident / National Id',
                        size: 20,
                        isMandatory: true,
                    },
                    {
                        labelName: 'Passport',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
            {
                documentCategory: 'Admission Document',
                document: [
                    {
                        labelName: 'Degree Certificate',
                        size: 20,
                        isMandatory: false,
                    },
                ],
            },
            {
                documentCategory: 'Address Details',
                document: [
                    {
                        labelName: 'Postal Address',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
            {
                documentCategory: 'UG Certificate',
                document: [
                    {
                        labelName: 'Employee ID',
                        size: 20,
                        isMandatory: true,
                    },
                    {
                        labelName: 'Appointment Order',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
            {
                documentCategory: 'COVID-19 Vaccination Details',
                document: [
                    {
                        labelName: 'Health Passport',
                        size: 20,
                        isMandatory: true,
                    },
                    {
                        labelName: 'Vaccination certificate',
                        size: 20,
                        isMandatory: true,
                    },
                    {
                        labelName: 'Vaccination-QR code',
                        size: 20,
                        isMandatory: true,
                    },
                ],
            },
        ],
        allowRemainderMail: {
            isMandatoryDocument: true,
            recurring: DAILY,
            recurringInterval: 2,
            time: '9:00 AM',
        },
        notificationMail: {
            label: 'Mandatory Documentation Notification Mail',
            time: '6:00 AM',
            labelBody: '',
        },
    };
};

const mockMailSettingsConfigurationData = () => {
    return {
        mailExpiration: 3,
        remainderSection: {
            isActive: true,
            recurring: DAILY,
            weeks: [
                { labelName: MONDAY, isActive: false },
                { labelName: TUESDAY, isActive: false },
                { labelName: WEDNESDAY, isActive: false },
                { labelName: THURSDAY, isActive: false },
                { labelName: FRIDAY, isActive: false },
                { labelName: SATURDAY, isActive: false },
                { labelName: SUNDAY, isActive: false },
            ],
            recurringInterval: 2,
            time: '9:00 AM',
        },
        invalidProfileSection: {
            isActive: true,
            recurring: DAILY,
            weeks: [
                { labelName: MONDAY, isActive: false },
                { labelName: TUESDAY, isActive: false },
                { labelName: WEDNESDAY, isActive: false },
                { labelName: THURSDAY, isActive: false },
                { labelName: FRIDAY, isActive: false },
                { labelName: SATURDAY, isActive: false },
                { labelName: SUNDAY, isActive: false },
            ],
            recurringInterval: 2,
            time: '9:00 AM',
        },
    };
};
const mockGlobalConfigurationData = (_institution_id = '', _parent_id = '') => {
    const session = mockSessionData();
    return {
        ...(_institution_id && { _institution_id }),
        ...(_parent_id && { _parent_id }),
        'globalConfiguration.basicDetails.isIndependentHours': false,
        'globalConfiguration.basicDetails.isGenderSegregation': false,
        'globalConfiguration.basicDetails.session.start': session.start,
        'globalConfiguration.basicDetails.session.end': session.end,
        'globalConfiguration.basicDetails.workingDays': mockWorkingDaysData(),
        'globalConfiguration.basicDetails.language': defaultLanguage(),
        'globalConfiguration.basicDetails.privacySettings': privacySettings(),
        'globalConfiguration.basicDetails.departmentHierarchyStructure':
            departmentHierarchyStructure(),
        'globalConfiguration.programInput.curriculumNaming': mockCurriculumNamingData(),
        'globalConfiguration.programInput.creditHours': mockCreditHoursData(),
        'globalConfiguration.programInput.programDurationFormat': mockProgramDurationFormat(),
        'globalConfiguration.programInput.labelConfiguration': mockProgramInputConfigurationData(
            ENGLISH,
            true,
        ),
        'globalConfiguration.independentCourseInput.creditHours':
            mockIndependentCourseCreditHoursData(),
        'globalConfiguration.staffUserManagement.labelFieldConfiguration':
            mockLabelFieldConfigurationData(ENGLISH),
        'globalConfiguration.staffUserManagement.biometricConfiguration':
            mockBioMetricConfigurationData(),
        'globalConfiguration.staffUserManagement.mailConfiguration': mockMailConfigurationData(),
        'globalConfiguration.staffUserManagement.documentConfiguration':
            mockDocumentConfigurationData(),
        'globalConfiguration.staffUserManagement.mailSettingsConfiguration':
            mockMailSettingsConfigurationData(),
        'globalConfiguration.departmentSubject': mockDepartmentSubjectConfigurationData(),
        'globalConfiguration.departmentManagement': mockdepartmentManagementConfigurationData(),
        'globalConfiguration.studentUserManagement.labelFieldConfiguration':
            mockStudentLabelFieldConfigurationData(ENGLISH),
        'globalConfiguration.studentUserManagement.biometricConfiguration':
            mockBioMetricConfigurationData(),
        'globalConfiguration.studentUserManagement.mailConfiguration': mockMailConfigurationData(),
        'globalConfiguration.studentUserManagement.documentConfiguration':
            mockStudentDocumentConfigurationData(),
        'globalConfiguration.studentUserManagement.mailSettingsConfiguration':
            mockMailSettingsConfigurationData(),
    };
};

module.exports = {
    mockGlobalConfigurationData,
    mockWorkingDaysData,
    mockSessionData,
    mockProgramInputConfigurationData,
    getMockGenerateLabel,
    mockLabelFieldConfigurationData,
    mockStudentLabelFieldConfigurationData,
    privacySettings,
    mockMailConfigurationData,
};
