const express = require('express');
const route = express.Router();
const digi_college = require('./digi_college_controller');
const validator = require('./digi_college_validator');

const file_upload = require('../utility/file_upload');
const multer = require('multer');

const logo_upload = file_upload.uploadfile2.fields([
    { name: 'logo', maxCount: 1 }
]);

route.post('/', (req, res, next) => {
    logo_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        } if (err) {
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    })
}, validator.insert_college, digi_college.insert);
route.put('/:id', (req, res, next) => {
    logo_upload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send('Multer Error in uploading');
        } if (err) {
            return res.status(500).send('Unknown Error occurred uploading');
        }
        next();
    })
}, validator.update_college, digi_college.update);
route.put('/archive_college/:id', validator.id, digi_college.archive_college);
route.get('/:id', validator.id, digi_college.list_id);
route.get('/college_list/:university_id', validator.university_id, digi_college.list);
route.get('/archived_college_list/:university_id', validator.university_id, digi_college.archived_college_list);
route.delete('/:id', validator.id, digi_college.delete_college);


module.exports = route;