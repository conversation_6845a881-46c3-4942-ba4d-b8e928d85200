const statesSchema = require('./states.model');
const { getModel } = require('../../utility/common');
const { STATES } = require('../../utility/constants');

const getStates = async ({ query = {}, headers = {} }) => {
    const { tenantURL } = headers;
    const statesModel = getModel(tenantURL, STATES, statesSchema);
    const { countryId } = query;
    const allStates = await statesModel.find({ countryId }).lean();
    return { statusCode: 200, data: allStates };
};

module.exports = { getStates };
