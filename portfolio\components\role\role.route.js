const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const RoleController = require('./role.controller');
const { createRoleSchema, updateRoleSchema, deleteRoleSchema } = require('./role.validation');

router.post('/', validate(createRoleSchema), catchAsync(RoleController.createRole));

router.get('/', catchAsync(RoleController.getRoles));

router.put('/', validate(updateRoleSchema), catchAsync(RoleController.updateRole));

router.delete('/', validate(deleteRoleSchema), catchAsync(RoleController.deleteRole));

module.exports = router;
