let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let session_order = new Schema({
    s_no: {
        type: Number,
        required: true
    },
    delivery_symbol: {
        type: String,
        required: true
    },
    delivery_no: {
        type: Number,
        required: true
    },
    session_name: {
        type: String,
        required: true
    },
    _subject_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SUBJECT
    }],
    contact_hours: {
        type: Number,
        required: true
    },
    week: {
        type: Number
    },
    _course_id: {
        type: Schema.Types.ObjectId,
        ref: constant.COURSE,
        required: true
    },
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.SESSION_ORDER, session_order);