const { convertToMongoObjectId, clone } = require('../utility/common');
const {
    INSTITUTION,
    // PROGRAM_CALENDAR,
    // DIGI_COURSE,
    // USER,
    // ROLE,
    ROLE_ASSIGN,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    USER,
    PART_TIME,
    FULL_TIME,
    <PERSON>O<PERSON><PERSON>_CALENDAR,
    STUDENT_GROUP,
    DIGI_SESSION_ORDER,
    DIGI_SESSION_DELIVERY_TYPES,
    DIGI_DEPARTMENT_SUBJECT,
    DIGI_COURSE,
    ACADEMIC,
    ADMINISTRATION,
    BOTH,
    COURSE_SCHEDULE,
    COMPLETED,
    PRIMARY,
    PRESENT,
    LMS,
    ABSENT,
    LEAVE,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    REPORT_ANALYTICS: { PROGRAM_LIST, PLO, CREDIT_HOURS, NO_STUDENT, NO_STAFF },
    STUDENT_CRITERIA_MANIPULATION,
    GENDER,
} = require('../utility/constants');
const { SCHEDULE_SESSION_BASED_REPORT } = require('../utility/util_keys');
const { courseSessionOrderFilterBasedSchedule } = require('../utility/common_functions');
const { get_list } = require('../base/base_controller');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
// Get Program List
exports.dashboardProgramList = async ({ userProgram, curriculumList }) => {
    const programs = [];
    for (element of userProgram) {
        let curriculum_list = [];
        let years = [];
        if (curriculumList)
            curriculum_list = curriculumList.filter(
                (i) => i._program_id.toString() == element._id.toString(),
            );
        const curriculumDatas = curriculum_list.map((curriculumElement) => {
            years = [
                ...years,
                ...curriculumElement.year_level.map((yearElement) => yearElement.y_type).flat(),
            ];
            return {
                _id: curriculumElement._id,
                curriculum_name: curriculumElement.curriculum_name,
                _program_id: curriculumElement._program_id,
            };
        });
        years = [...new Set(years)];
        programs.push({
            ...element,
            curriculum: curriculumDatas,
            no_year: Math.max(...years.map((year) => parseInt(year.split('year')[1]))),
        });
    }
    return programs;
};

// Get Program wise PLO
exports.dashboardPLO = async ({ userProgram, curriculumList }) => {
    const programs = [];
    for (element of userProgram) {
        let curriculum_list = [];
        if (curriculumList)
            curriculum_list = curriculumList.filter(
                (i) => i._program_id.toString() == element._id.toString(),
            );
        const curriculumData = curriculum_list.map((curriculumElement) => {
            let plo_count = 0;
            for (const domain of curriculumElement.framework.domains) {
                for (const plo of domain.plo) {
                    if (plo && plo.isDeleted === false) plo_count++;
                }
            }
            return {
                curriculum_name: curriculumElement.curriculum_name,
                plo_count,
            };
        });
        programs.push({
            program_id: element._id,
            program_name: element.name,
            program_code: element.code,
            curriculums: curriculumData,
        });
    }
    return programs;
};

const studentAttendanceReport = (
    courseStudentIds,
    courseScheduled,
    warningAbsenceData,
    studentCriteriaData,
) => {
    const studentData = [];
    for (studentElement of courseStudentIds) {
        const studentSchedule = courseScheduled.filter(
            (ele) =>
                ele &&
                ele.students.find((ele2) => ele2._id.toString() === studentElement.toString()),
        );
        // const completedSchedule = studentSchedule.filter((ele) => ele.status === COMPLETED);
        const absentSchedules = studentSchedule.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement.toString() &&
                        (ele2.status === ABSENT ||
                            ele2.status === PERMISSION ||
                            ele2.status === LEAVE),
                ),
        );
        const denialPercentage = (absentSchedules.length / studentSchedule.length) * 100;
        studentElement.absence = denialPercentage.toFixed(2);
        const studentWarningAbsence = clone(warningAbsenceData);
        if (studentCriteriaData.status != false) {
            const studentManipulation = studentCriteriaData.data.find(
                (absenceElement) =>
                    absenceElement.studentId.toString() === studentElement.toString(),
            );
            studentElement.manipulationStatus = false;
            studentElement.manipulationPercentage = 0;
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                studentWarningAbsence[0] &&
                studentWarningAbsence[0].absence_percentage &&
                studentWarningAbsence[0].absence_percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].absence_percentage = studentManipulation.absencePercentage;
                studentElement.manipulationStatus = true;
                studentElement.manipulationPercentage = studentManipulation.absencePercentage;
            }
        }
        const warningData = studentWarningAbsence.find(
            (ele) =>
                ele.absence_percentage &&
                parseFloat(denialPercentage) >= parseFloat(ele.absence_percentage),
        );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.warning : '',
        });
    }
    return studentData;
};

const courseCreditContactHours = (
    sessionDeliveryTypes,
    _program_id,
    courseScheduled,
    sessionOrderData,
    credit_hours,
    studentId,
) => {
    const credit_hoursData = [];
    const sessionDeliveryTypesData = sessionDeliveryTypes.filter(
        (ele) => ele._program_id.toString() === _program_id.toString(),
    );
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        const deliveryCredit = credit_hours.find(
            (ele) =>
                ele.type_name === sessionTypeElement.session_name &&
                ele.type_symbol === sessionTypeElement.session_symbol,
        );
        let deliveryTypeData = [];
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                            students: ele2.students,
                        };
                    }),
            ];
        }
        let sessionCompletedHours = 0;
        let endSchedule = 0;
        let scheduleSessionCount = 0;
        let attendanceCount = 0;
        // let endSchedule = [];
        if (deliveryTypeData.length) {
            // Altering Based on Scheduled Session SG wise
            for (sessionOrderElement of sessionOrderData) {
                const sessionSchedule = deliveryTypeData.filter(
                    (ele) => ele._session_id.toString() === sessionOrderElement._id.toString(),
                );
                if (sessionSchedule.length > 0) scheduleSessionCount++;
                if (
                    sessionSchedule.length > 0 &&
                    sessionSchedule.length ===
                        sessionSchedule.filter((ele) => ele.status === COMPLETED).length
                ) {
                    if (studentId) {
                        attendanceCount += sessionSchedule.filter(
                            (ele2) =>
                                ele2 &&
                                ele2.students &&
                                ele2.students.find(
                                    (ele) =>
                                        ele &&
                                        ele._id.toString() === studentId.toString() &&
                                        (ele.status === PRESENT || ele.status === ONDUTY),
                                ),
                        ).length;
                    }
                    endSchedule++;
                    sessionCompletedHours += sessionOrderElement.duration;
                }
            }
        }
        const courseDeliveryWiseDurationPerContactHours =
            deliveryCredit &&
            deliveryCredit.duration_per_contact_hour &&
            parseInt(deliveryCredit.duration_per_contact_hour)
                ? parseInt(deliveryCredit.duration_per_contact_hour)
                : 60;
        const sessionCompletedCredit =
            sessionCompletedHours !== 0
                ? sessionCompletedHours / courseDeliveryWiseDurationPerContactHours
                : 0;

        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            credit_hours:
                deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
            completed_credit_hours:
                sessionCompletedCredit /
                    (deliveryCredit && deliveryCredit.contact_hours
                        ? parseInt(deliveryCredit.contact_hours)
                        : parseInt(sessionTypeElement.contact_hour_per_credit_hour)) || 0,
            completed_contact_hours: sessionCompletedCredit || 0,
            contact_hours:
                parseInt(
                    deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
                ) *
                (deliveryCredit && deliveryCredit.contact_hours
                    ? parseInt(deliveryCredit.contact_hours)
                    : parseInt(sessionTypeElement.contact_hour_per_credit_hour)),
            // no_of_sessions: deliveryTypeData.length,
            no_of_sessions: scheduleSessionCount,
            completed_sessions: endSchedule,
            present_count: attendanceCount,
            // completed_sessions: endSchedule.length,
        });
    }
    return credit_hoursData;
};

// Get Program wise PLO
exports.dashboardCreditHoursCalculation = async ({
    userProgram,
    programCalendarList,
    sessionDeliveryTypeList,
    sessionOrderList,
    scheduleData,
}) => {
    const programs = [];
    for (element of userProgram) {
        //Credit Hours Calculation
        let theory = 0;
        let practical = 0;
        let clinical = 0;
        let theoryCompleted = 0;
        let practicalCompleted = 0;
        let clinicalCompleted = 0;
        const pcData = programCalendarList.find(
            (idElement) => idElement._program_id.toString() === element._id.toString(),
        );
        if (pcData) {
            const programSessionDeliveryTypes = sessionDeliveryTypeList.filter(
                (ele) => ele._program_id.toString() === element._id.toString(),
            );
            for (levelElement of pcData.level) {
                if (levelElement.rotation === 'no') {
                    for (courseElement of levelElement.course) {
                        const theoryCredit = courseElement.credit_hours.find(
                            (ele) => ele.type_name === 'Theory',
                        );
                        const practicalCredit = courseElement.credit_hours.find(
                            (ele) => ele.type_name === 'Practical',
                        );
                        const clinicalCredit = courseElement.credit_hours.find(
                            (ele) => ele.type_name === 'Clinical',
                        );
                        theory += theoryCredit ? theoryCredit.credit_hours : 0;
                        practical += practicalCredit ? practicalCredit.credit_hours : 0;
                        clinical += clinicalCredit ? clinicalCredit.credit_hours : 0;

                        // Credit Hour Calculation
                        const courseSessionOrder = sessionOrderList.find(
                            (ele) =>
                                ele._course_id.toString() === courseElement._course_id.toString(),
                        );
                        const courseSessionOrderData = courseSessionOrder
                            ? courseSessionOrder.session_flow_data
                            : [];
                        const courseScheduled = clone(
                            scheduleData.filter(
                                (ele) =>
                                    ele._course_id.toString() ===
                                        courseElement._course_id.toString() &&
                                    ele.term === levelElement.term &&
                                    ele.level_no === levelElement.level_no,
                            ),
                        );
                        // * Session Schedule Based Report Course Session Order filter based on Schedule
                        let sessionOrderData = courseSessionOrderData
                            ? clone(courseSessionOrderData)
                            : { session_flow_data: [] };
                        if (
                            SCHEDULE_SESSION_BASED_REPORT &&
                            SCHEDULE_SESSION_BASED_REPORT === 'true'
                        ) {
                            sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                courseSessionFlow: courseSessionOrderData,
                                courseSchedule: courseScheduled,
                            }).session_flow_data;
                        }
                        const credit_hours = courseCreditContactHours(
                            programSessionDeliveryTypes,
                            element._id.toString(),
                            courseScheduled,
                            sessionOrderData,
                            courseElement.credit_hours,
                        );
                        // credit_hours
                        const completedTheoryCredit = credit_hours.find(
                            (ele) => ele.type_name === 'Theory',
                        );
                        const completedPracticalCredit = credit_hours.find(
                            (ele) => ele.type_name === 'Practical',
                        );
                        const completedClinicalCredit = credit_hours.find(
                            (ele) => ele.type_name === 'Clinical',
                        );
                        theoryCompleted += completedTheoryCredit
                            ? completedTheoryCredit.completed_credit_hours
                            : 0;
                        practicalCompleted += completedPracticalCredit
                            ? completedPracticalCredit.completed_credit_hours
                            : 0;
                        clinicalCompleted += completedClinicalCredit
                            ? completedClinicalCredit.completed_credit_hours
                            : 0;
                    }
                } else if (
                    levelElement.rotation_course[0] &&
                    levelElement.rotation_course[0].course
                ) {
                    for (courseElement of levelElement.rotation_course[0].course) {
                        const theoryCredit = courseElement.credit_hours.find(
                            (ele) => ele.type_name === 'Theory',
                        );
                        const practicalCredit = courseElement.credit_hours.find(
                            (ele) => ele.type_name === 'Practical',
                        );
                        const clinicalCredit = courseElement.credit_hours.find(
                            (ele) => ele.type_name === 'Clinical',
                        );
                        theory += theoryCredit ? theoryCredit.credit_hours : 0;
                        practical += practicalCredit ? practicalCredit.credit_hours : 0;
                        clinical += clinicalCredit ? clinicalCredit.credit_hours : 0;

                        // Credit Hour Calculation
                        const courseSessionOrder = sessionOrderList.find(
                            (ele) =>
                                ele._course_id.toString() === courseElement._course_id.toString(),
                        );
                        const courseSessionOrderData = courseSessionOrder
                            ? courseSessionOrder.session_flow_data
                            : [];
                        const courseScheduled = clone(
                            scheduleData.filter(
                                (ele) =>
                                    ele._course_id.toString() ===
                                        courseElement._course_id.toString() &&
                                    ele.term === levelElement.term &&
                                    ele.level_no === levelElement.level_no,
                            ),
                        );
                        // * Session Schedule Based Report Course Session Order filter based on Schedule
                        let sessionOrderData = courseSessionOrderData
                            ? clone(courseSessionOrderData)
                            : { session_flow_data: [] };
                        if (
                            SCHEDULE_SESSION_BASED_REPORT &&
                            SCHEDULE_SESSION_BASED_REPORT === 'true'
                        ) {
                            sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                courseSessionFlow: courseSessionOrderData,
                                courseSchedule: courseScheduled,
                            }).session_flow_data;
                        }
                        const credit_hours = courseCreditContactHours(
                            programSessionDeliveryTypes,
                            element._id.toString(),
                            courseScheduled,
                            sessionOrderData,
                            courseElement.credit_hours,
                        );
                        // credit_hours
                        const completedTheoryCredit = credit_hours.find(
                            (ele) => ele.type_name === 'Theory',
                        );
                        const completedPracticalCredit = credit_hours.find(
                            (ele) => ele.type_name === 'Practical',
                        );
                        const completedClinicalCredit = credit_hours.find(
                            (ele) => ele.type_name === 'Clinical',
                        );
                        theoryCompleted += completedTheoryCredit
                            ? completedTheoryCredit.completed_credit_hours
                            : 0;
                        practicalCompleted += completedPracticalCredit
                            ? completedPracticalCredit.completed_credit_hours
                            : 0;
                        clinicalCompleted += completedClinicalCredit
                            ? completedClinicalCredit.completed_credit_hours
                            : 0;
                    }
                }
            }
        }
        programs.push({
            program_id: element._id,
            program_name: element.name,
            program_code: element.code,
            theory,
            practical,
            clinical,
            // Todo Need to check All schedule Based on SG
            theory_complete: theoryCompleted,
            practical_complete: practicalCompleted,
            clinical_complete: clinicalCompleted,
        });
    }
    return programs;
};

// Get Program wise PLO
exports.dashboardNoStudent = async ({ institutionCalendarId, userProgram, studentGroupData }) => {
    const programs = [];
    for (element of userProgram) {
        const sgStudents = [];
        const sgProgramData = studentGroupData
            ? studentGroupData.filter(
                  (ele) =>
                      ele.master._program_id.toString() === element._id.toString() &&
                      ele._institution_calendar_id.toString() === institutionCalendarId,
              )
            : [];
        for (sgElement of sgProgramData) {
            for (sgLevel of sgElement.groups) {
                let studentIds = [];
                const courseBasedStudentIds = [];
                const courseBasedStudentCount = {
                    male: 0,
                    female: 0,
                    count: 0,
                };
                for (sgCourse of sgLevel.courses) {
                    let courseBasedStudentId = [];
                    if (sgLevel.rotation === 'yes') {
                        const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                        for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                            for (sgSetting of sgCourse.setting) {
                                if (
                                    sgSetting &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups
                                )
                                    for (sgSession of sgSetting.session_setting[0].groups) {
                                        studentIds = [...studentIds, ...sgSession._student_ids];
                                        courseBasedStudentId = [
                                            ...courseBasedStudentId,
                                            ...sgSession._student_ids,
                                        ];
                                    }
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                            }
                        }
                    } else {
                        for (sgSetting of sgCourse.setting) {
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            )
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                    courseBasedStudentId = [
                                        ...courseBasedStudentId,
                                        ...sgSession._student_ids,
                                    ];
                                }
                            studentIds = studentIds.filter(
                                (ele, index) =>
                                    studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                        }
                    }
                    courseBasedStudentIds.push({
                        courseId: sgCourse._course_id,
                        studentIds: courseBasedStudentId /* .filter(
                            (courseBasedStudentIdElement, index) =>
                                courseBasedStudentId.findIndex(
                                    (studentIdElement) =>
                                        studentIdElement.toString() ===
                                        courseBasedStudentIdElement.toString(),
                                ) === index,
                        ) */,
                    });
                }
                let studentDatas = sgLevel.students.filter((ele) =>
                    studentIds.find((ele2) => ele2.toString() === ele._student_id.toString()),
                );
                studentDatas = studentDatas.filter(
                    (ele, index) =>
                        studentDatas.findIndex(
                            (ele2) => ele2._student_id.toString() === ele._student_id.toString(),
                        ) === index,
                );
                for (courseStudentElement of courseBasedStudentIds) {
                    let courseStudents = sgLevel.students.filter((studentElement) =>
                        courseStudentElement.studentIds.find(
                            (studentIdElement) =>
                                studentIdElement.toString() ===
                                studentElement._student_id.toString(),
                        ),
                    );
                    courseStudents = courseStudents.filter(
                        (courseStudentElement, index) =>
                            courseStudents.findIndex(
                                (studentIdElement) =>
                                    studentIdElement._student_id.toString() ===
                                    courseStudentElement._student_id.toString(),
                            ) === index,
                    );
                    courseBasedStudentCount.male += courseStudents.filter(
                        (studentElement) => studentElement.gender === 'male',
                    ).length;
                    courseBasedStudentCount.female += courseStudents.filter(
                        (studentElement) => studentElement.gender === 'female',
                    ).length;
                    courseBasedStudentCount.count += courseStudents.length;
                }
                const loc = sgStudents.findIndex((ele) => ele && ele.term === sgLevel.term);
                if (loc === -1) {
                    sgStudents.push({
                        term: sgLevel.term,
                        students: studentDatas,
                        male_count: studentDatas.filter((ele) => ele.gender === 'male').length,
                        female_count: studentDatas.filter((ele) => ele.gender === 'female').length,
                        count: studentDatas.length,
                        courseStudents: courseBasedStudentCount,
                        // courseBasedStudentIds,
                    });
                } else {
                    let sgStudentDatas = [
                        ...sgStudents[loc].students,
                        ...sgLevel.students.filter((ele) =>
                            studentIds.find(
                                (ele2) => ele2.toString() === ele._student_id.toString(),
                            ),
                        ),
                    ];
                    sgStudentDatas = sgStudentDatas.filter(
                        (ele, index) =>
                            sgStudentDatas.findIndex(
                                (ele2) =>
                                    ele2._student_id.toString() === ele._student_id.toString(),
                            ) === index,
                    );
                    sgStudents[loc].students = sgStudentDatas;
                    sgStudents[loc].male_count = sgStudentDatas.filter(
                        (ele) => ele.gender === 'male',
                    ).length;
                    sgStudents[loc].female_count = sgStudentDatas.filter(
                        (ele) => ele.gender === 'female',
                    ).length;
                    sgStudents[loc].count = sgStudentDatas.length;
                    sgStudents[loc].courseStudents.male += courseBasedStudentCount.male;
                    sgStudents[loc].courseStudents.female += courseBasedStudentCount.female;
                    sgStudents[loc].courseStudents.count += courseBasedStudentCount.count;
                    // sgStudents[loc].courseBasedStudentIds = [
                    //     ...sgStudents[loc].courseBasedStudentIds,
                    //     ...courseBasedStudentIds,
                    // ];
                }
            }
        }
        sgStudents.forEach((ele) => {
            delete ele.students;
        });
        programs.push({
            program_id: element._id,
            program_name: element.name,
            program_code: element.code,
            student_term: sgStudents,
        });
    }
    return programs;
};

// Get Program wise Summary
exports.dashboardNoStaff = async ({ userProgram, staffData }) => {
    const programs = [];
    for (element of userProgram) {
        const programStaffs = [];
        for (staffElement of staffData) {
            if (
                staffElement.academic_allocation.find(
                    (allocationElement) =>
                        // allocationElement.allocation_type.toString() === PRIMARY &&
                        allocationElement._program_id.toString() === element._id.toString(),
                )
            ) {
                programStaffs.push(staffElement);
            }
        }
        programs.push({
            program_id: element._id,
            program_name: element.name,
            program_code: element.code,
            male_count: programStaffs.filter((ele) => ele.gender === 'male').length,
            female_count: programStaffs.filter((ele) => ele.gender === 'female').length,
            part_time_count: programStaffs.filter(
                (ele) => ele.employment.user_employment_type === PART_TIME,
            ).length,
            full_time_count: programStaffs.filter(
                (ele) => ele.employment.user_employment_type === FULL_TIME,
            ).length,
        });
    }
    return programs;
};

// Get Staff Count
exports.programWiseSummary = async ({ userProgram, programStudentGroupData }) => {
    const yearLevel = [];
    let LevelStudents = [];
    const programStudents = [];
    for (sgElement of programStudentGroupData) {
        for (sgLevel of sgElement.groups) {
            const sgGenderStudents = [];
            const courseBasedStudentCount = {
                male: 0,
                female: 0,
            };
            for (sgCourse of sgLevel.courses) {
                if (sgLevel.rotation === 'yes') {
                    const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                    for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                        for (sgSetting of sgCourse.setting) {
                            if (sgSetting.gender === BOTH) {
                                let studentIds = [];
                                if (
                                    sgSetting &&
                                    sgSetting._group_no.toString() === i.toString() &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups &&
                                    sgSetting._group_no.toString() === i.toString()
                                ) {
                                    studentIds = [
                                        ...studentIds,
                                        ...sgSetting.session_setting[0].groups
                                            .map((groupElement) => groupElement._student_ids)
                                            .flat(),
                                    ];
                                }
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                const maleStudentIds = studentIds.filter((studentIdElement) =>
                                    sgLevel.students.find(
                                        (studentElement) =>
                                            studentElement.gender === GENDER.MALE &&
                                            studentElement._student_id.toString() ===
                                                studentIdElement.toString(),
                                    ),
                                );
                                const femaleStudentIds = studentIds.filter((studentIdElement) =>
                                    sgLevel.students.find(
                                        (studentElement) =>
                                            studentElement.gender === GENDER.FEMALE &&
                                            studentElement._student_id.toString() ===
                                                studentIdElement.toString(),
                                    ),
                                );
                                courseBasedStudentCount.male += maleStudentIds.length;
                                courseBasedStudentCount.female += femaleStudentIds.length;
                                if (sgGenderStudents.length) {
                                    const maleLoc = sgGenderStudents.findIndex(
                                        (ele) =>
                                            ele &&
                                            ele.gender === GENDER.MALE &&
                                            ele.level === sgLevel.level &&
                                            ele.term === sgLevel.term,
                                    );
                                    const femaleLoc = sgGenderStudents.findIndex(
                                        (ele) =>
                                            ele &&
                                            ele.gender === GENDER.FEMALE &&
                                            ele.level === sgLevel.level &&
                                            ele.term === sgLevel.term,
                                    );
                                    if (maleLoc !== -1) {
                                        sgGenderStudents[maleLoc].studentIds = [
                                            ...maleStudentIds,
                                            ...sgGenderStudents[maleLoc].studentIds,
                                        ];
                                        sgGenderStudents[maleLoc].studentIds = sgGenderStudents[
                                            maleLoc
                                        ].studentIds.filter(
                                            (ele, index) =>
                                                sgGenderStudents[maleLoc].studentIds.findIndex(
                                                    (ele2) => ele2.toString() === ele.toString(),
                                                ) === index,
                                        );
                                        sgGenderStudents[maleLoc].count =
                                            sgGenderStudents[maleLoc].studentIds.length;
                                    } else {
                                        sgGenderStudents.push({
                                            level: sgLevel.level,
                                            term: sgLevel.term,
                                            gender: GENDER.MALE,
                                            studentIds: maleStudentIds,
                                            count: maleStudentIds.length,
                                        });
                                    }
                                    // Female
                                    if (femaleLoc !== -1) {
                                        sgGenderStudents[femaleLoc].studentIds = [
                                            ...femaleStudentIds,
                                            ...sgGenderStudents[femaleLoc].studentIds,
                                        ];
                                        sgGenderStudents[femaleLoc].studentIds = sgGenderStudents[
                                            femaleLoc
                                        ].studentIds.filter(
                                            (ele, index) =>
                                                sgGenderStudents[femaleLoc].studentIds.findIndex(
                                                    (ele2) => ele2.toString() === ele.toString(),
                                                ) === index,
                                        );
                                        sgGenderStudents[femaleLoc].count =
                                            sgGenderStudents[femaleLoc].studentIds.length;
                                    } else {
                                        sgGenderStudents.push({
                                            level: sgLevel.level,
                                            term: sgLevel.term,
                                            gender: GENDER.FEMALE,
                                            studentIds: femaleStudentIds,
                                            count: femaleStudentIds.length,
                                        });
                                    }
                                } else {
                                    sgGenderStudents.push(
                                        {
                                            level: sgLevel.level,
                                            term: sgLevel.term,
                                            gender: GENDER.MALE,
                                            studentIds: maleStudentIds,
                                            count: maleStudentIds.length,
                                        },
                                        {
                                            level: sgLevel.level,
                                            term: sgLevel.term,
                                            gender: GENDER.FEMALE,
                                            studentIds: femaleStudentIds,
                                            count: femaleStudentIds.length,
                                        },
                                    );
                                }
                            } else {
                                const loc = sgGenderStudents.findIndex(
                                    (ele) =>
                                        ele &&
                                        ele.gender === sgSetting.gender &&
                                        ele.level === sgLevel.level &&
                                        ele.term === sgLevel.term,
                                );
                                let studentIds = [];
                                if (
                                    sgSetting &&
                                    sgSetting._group_no.toString() === i.toString() &&
                                    sgSetting.session_setting &&
                                    sgSetting.session_setting.length &&
                                    sgSetting.session_setting[0].groups &&
                                    sgSetting._group_no.toString() === i.toString()
                                ) {
                                    studentIds = [
                                        ...studentIds,
                                        ...sgSetting.session_setting[0].groups
                                            .map((groupElement) => groupElement._student_ids)
                                            .flat(),
                                    ];
                                }
                                studentIds = studentIds.filter(
                                    (ele, index) =>
                                        studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                if (sgSetting.gender === GENDER.MALE)
                                    courseBasedStudentCount.male += studentIds.length;
                                if (sgSetting.gender === GENDER.FEMALE)
                                    courseBasedStudentCount.female += studentIds.length;
                                if (loc === -1)
                                    sgGenderStudents.push({
                                        level: sgLevel.level,
                                        term: sgLevel.term,
                                        gender: sgSetting.gender,
                                        studentIds,
                                        count: studentIds.length,
                                    });
                                else {
                                    sgGenderStudents[loc].studentIds = [
                                        ...studentIds,
                                        ...sgGenderStudents[loc].studentIds,
                                    ];
                                    sgGenderStudents[loc].studentIds = sgGenderStudents[
                                        loc
                                    ].studentIds.filter(
                                        (ele, index) =>
                                            sgGenderStudents[loc].studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents[loc].count =
                                        sgGenderStudents[loc].studentIds.length;
                                }
                            }
                        }
                    }
                } else {
                    for (sgSetting of sgCourse.setting) {
                        if (sgSetting.gender === BOTH) {
                            let studentIds = [];
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            ) {
                                studentIds = [
                                    ...studentIds,
                                    ...sgSetting.session_setting[0].groups
                                        .map((groupElement) => groupElement._student_ids)
                                        .flat(),
                                ];
                            }
                            studentIds = studentIds.filter(
                                (ele, index) =>
                                    studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                            const maleStudentIds = studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === GENDER.MALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            );
                            const femaleStudentIds = studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === GENDER.FEMALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            );
                            courseBasedStudentCount.male += maleStudentIds.length;
                            courseBasedStudentCount.female += femaleStudentIds.length;
                            if (sgGenderStudents.length) {
                                const maleLoc = sgGenderStudents.findIndex(
                                    (ele) =>
                                        ele &&
                                        ele.gender === GENDER.MALE &&
                                        ele.level === sgLevel.level &&
                                        ele.term === sgLevel.term,
                                );
                                const femaleLoc = sgGenderStudents.findIndex(
                                    (ele) =>
                                        ele &&
                                        ele.gender === GENDER.FEMALE &&
                                        ele.level === sgLevel.level &&
                                        ele.term === sgLevel.term,
                                );
                                if (maleLoc !== -1) {
                                    sgGenderStudents[maleLoc].studentIds = [
                                        ...maleStudentIds,
                                        ...sgGenderStudents[maleLoc].studentIds,
                                    ];
                                    sgGenderStudents[maleLoc].studentIds = sgGenderStudents[
                                        maleLoc
                                    ].studentIds.filter(
                                        (ele, index) =>
                                            sgGenderStudents[maleLoc].studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents[maleLoc].count =
                                        sgGenderStudents[maleLoc].studentIds.length;
                                } else {
                                    sgGenderStudents.push({
                                        level: sgLevel.level,
                                        term: sgLevel.term,
                                        gender: GENDER.MALE,
                                        studentIds: maleStudentIds,
                                        count: maleStudentIds.length,
                                    });
                                }
                                // Female
                                if (femaleLoc !== -1) {
                                    sgGenderStudents[femaleLoc].studentIds = [
                                        ...femaleStudentIds,
                                        ...sgGenderStudents[femaleLoc].studentIds,
                                    ];
                                    sgGenderStudents[femaleLoc].studentIds = sgGenderStudents[
                                        femaleLoc
                                    ].studentIds.filter(
                                        (ele, index) =>
                                            sgGenderStudents[femaleLoc].studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents[femaleLoc].count =
                                        sgGenderStudents[femaleLoc].studentIds.length;
                                } else {
                                    sgGenderStudents.push({
                                        level: sgLevel.level,
                                        term: sgLevel.term,
                                        gender: GENDER.FEMALE,
                                        studentIds: femaleStudentIds,
                                        count: femaleStudentIds.length,
                                    });
                                }
                            } else {
                                sgGenderStudents.push(
                                    {
                                        level: sgLevel.level,
                                        term: sgLevel.term,
                                        gender: GENDER.MALE,
                                        studentIds: maleStudentIds,
                                        count: maleStudentIds.length,
                                    },
                                    {
                                        level: sgLevel.level,
                                        term: sgLevel.term,
                                        gender: GENDER.FEMALE,
                                        studentIds: femaleStudentIds,
                                        count: femaleStudentIds.length,
                                    },
                                );
                            }
                        } else {
                            const loc = sgGenderStudents.findIndex(
                                (ele) =>
                                    ele &&
                                    ele.gender === sgSetting.gender &&
                                    ele.level === sgLevel.level &&
                                    ele.term === sgLevel.term,
                            );
                            let studentIds = [];
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups
                            ) {
                                studentIds = [
                                    ...studentIds,
                                    ...sgSetting.session_setting[0].groups
                                        .map((groupElement) => groupElement._student_ids)
                                        .flat(),
                                ];
                            }
                            studentIds = studentIds.filter(
                                (ele, index) =>
                                    studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                            if (sgSetting.gender === GENDER.MALE)
                                courseBasedStudentCount.male += studentIds.length;
                            if (sgSetting.gender === GENDER.FEMALE)
                                courseBasedStudentCount.female += studentIds.length;
                            if (loc === -1)
                                sgGenderStudents.push({
                                    level: sgLevel.level,
                                    term: sgLevel.term,
                                    gender: sgSetting.gender,
                                    studentIds,
                                    count: studentIds.length,
                                });
                            else {
                                sgGenderStudents[loc].studentIds = [
                                    ...studentIds,
                                    ...sgGenderStudents[loc].studentIds,
                                ];
                                sgGenderStudents[loc].studentIds = sgGenderStudents[
                                    loc
                                ].studentIds.filter(
                                    (ele, index) =>
                                        sgGenderStudents[loc].studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                sgGenderStudents[loc].count =
                                    sgGenderStudents[loc].studentIds.length;
                            }
                        }
                    }
                }
            }
            for (levelStudentElement of sgGenderStudents) {
                const genderLoc = programStudents.findIndex(
                    (stdElement) =>
                        stdElement.gender === levelStudentElement.gender &&
                        stdElement.term === levelStudentElement.term,
                );
                const genderCourseStudentCount =
                    levelStudentElement.gender === GENDER.MALE
                        ? courseBasedStudentCount.male
                        : courseBasedStudentCount.female;
                levelStudentElement.courseStudents = genderCourseStudentCount;
                if (genderLoc === -1)
                    programStudents.push({
                        gender: levelStudentElement.gender,
                        term: levelStudentElement.term,
                        studentIds: levelStudentElement.studentIds,
                        count: levelStudentElement.studentIds.length,
                        courseStudents: genderCourseStudentCount,
                    });
                else {
                    programStudents[genderLoc].studentIds = [
                        ...levelStudentElement.studentIds,
                        ...programStudents[genderLoc].studentIds,
                    ];
                    programStudents[genderLoc].studentIds = programStudents[
                        genderLoc
                    ].studentIds.filter(
                        (ele, index) =>
                            programStudents[genderLoc].studentIds.findIndex(
                                (ele2) => ele2.toString() === ele.toString(),
                            ) === index,
                    );
                    programStudents[genderLoc].count = programStudents[genderLoc].studentIds.length;
                    programStudents[genderLoc].courseStudents += genderCourseStudentCount;
                }
            }
            LevelStudents.push({
                year: sgElement.master.year,
                level: sgLevel.level,
                term: sgLevel.term,
                students: sgGenderStudents.map((studentElement) => {
                    return {
                        gender: studentElement.gender,
                        count: studentElement.count,
                        courseStudents: studentElement.courseStudents,
                    };
                }),
            });
        }
        yearLevel.push({
            year: sgElement.master.year,
            level: sgElement.groups.map((levelElement) => {
                return { level: levelElement.level, term: levelElement.term };
            }),
        });
    }
    LevelStudents = LevelStudents.sort((a, b) => {
        let comparison = 0;
        if (parseInt(a.level.split('Level')[1]) > parseInt(b.level.split('Level')[1])) {
            comparison = 1;
        } else if (parseInt(a.level.split('Level')[1]) < parseInt(b.level.split('Level')[1])) {
            comparison = -1;
        }
        return comparison;
    });

    const response = {
        program_name: userProgram.name || '',
        program_no: userProgram.code || '',
        active_student: 0,
        students: programStudents.map((programStudentElement) => {
            return {
                gender: programStudentElement.gender,
                term: programStudentElement.term,
                count: programStudentElement.count,
                courseStudents: programStudentElement.courseStudents,
            };
        }),
        inactive: 0,
        yearLevel: yearLevel.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.year.split('year')[1]) > parseInt(b.year.split('year')[1])) {
                comparison = 1;
            } else if (parseInt(a.year.split('year')[1]) < parseInt(b.year.split('year')[1])) {
                comparison = -1;
            }
            return comparison;
        }),
        LevelStudents,
    };
    return response;
};

exports.programWiseYearLevelCourseData = async ({
    userProgram,
    levelStudentGroupData,
    programCalendarDataList,
    programCourseData,
    programSessionDeliveryTypeData,
    sessionOrderList,
    programCourseScheduleData,
    lmsData,
    programId,
}) => {
    const LevelCourses = [];
    const studentCriteriaData = await get_list(studentCriteriaCollection, { programId });
    const lmsClonedData = clone(lmsData);
    for (sgLevel of levelStudentGroupData.groups) {
        for (sgCourse of sgLevel.courses) {
            if (sgLevel.rotation === 'yes') {
                const rotationCounts = sgCourse.setting.map((ele) => ele._group_no);
                for (let i = 1; i <= Math.max(...rotationCounts); i++) {
                    const sgGenderStudents = [];
                    for (sgSetting of sgCourse.setting) {
                        if (sgSetting.gender === BOTH) {
                            let studentIds = [];
                            if (
                                sgSetting &&
                                sgSetting._group_no.toString() === i.toString() &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups &&
                                sgSetting._group_no.toString() === i.toString()
                            ) {
                                studentIds = [
                                    ...studentIds,
                                    ...sgSetting.session_setting[0].groups
                                        .map((groupElement) => groupElement._student_ids)
                                        .flat(),
                                ];
                            }
                            studentIds = studentIds.filter(
                                (ele, index) =>
                                    studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                            const maleStudentIds = studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === GENDER.MALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            );
                            const femaleStudentIds = studentIds.filter((studentIdElement) =>
                                sgLevel.students.find(
                                    (studentElement) =>
                                        studentElement.gender === GENDER.FEMALE &&
                                        studentElement._student_id.toString() ===
                                            studentIdElement.toString(),
                                ),
                            );
                            if (sgGenderStudents.length) {
                                const maleLoc = sgGenderStudents.findIndex(
                                    (ele) => ele && ele.gender === GENDER.MALE,
                                );
                                const femaleLoc = sgGenderStudents.findIndex(
                                    (ele) => ele && ele.gender === GENDER.FEMALE,
                                );
                                if (maleLoc !== -1) {
                                    sgGenderStudents[maleLoc].studentIds = [
                                        ...maleStudentIds,
                                        ...sgGenderStudents[maleLoc].studentIds,
                                    ];
                                    sgGenderStudents[maleLoc].studentIds = sgGenderStudents[
                                        maleLoc
                                    ].studentIds.filter(
                                        (ele, index) =>
                                            sgGenderStudents[maleLoc].studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents[maleLoc].count =
                                        sgGenderStudents[maleLoc].studentIds.length;
                                } else {
                                    sgGenderStudents.push({
                                        gender: GENDER.MALE,
                                        studentIds: maleStudentIds,
                                        count: maleStudentIds.length,
                                    });
                                }
                                // Female
                                if (maleLoc !== -1) {
                                    sgGenderStudents[femaleLoc].studentIds = [
                                        ...femaleStudentIds,
                                        ...sgGenderStudents[femaleLoc].studentIds,
                                    ];
                                    sgGenderStudents[femaleLoc].studentIds = sgGenderStudents[
                                        femaleLoc
                                    ].studentIds.filter(
                                        (ele, index) =>
                                            sgGenderStudents[femaleLoc].studentIds.findIndex(
                                                (ele2) => ele2.toString() === ele.toString(),
                                            ) === index,
                                    );
                                    sgGenderStudents[femaleLoc].count =
                                        sgGenderStudents[femaleLoc].studentIds.length;
                                } else {
                                    sgGenderStudents.push({
                                        gender: GENDER.FEMALE,
                                        studentIds: femaleStudentIds,
                                        count: femaleStudentIds.length,
                                    });
                                }
                            } else {
                                sgGenderStudents.push(
                                    {
                                        gender: GENDER.MALE,
                                        studentIds: maleStudentIds,
                                        count: maleStudentIds.length,
                                    },
                                    {
                                        gender: GENDER.FEMALE,
                                        studentIds: femaleStudentIds,
                                        count: femaleStudentIds.length,
                                    },
                                );
                            }
                        } else {
                            let studentIds = [];
                            if (
                                sgSetting &&
                                sgSetting.session_setting &&
                                sgSetting.session_setting.length &&
                                sgSetting.session_setting[0].groups &&
                                sgSetting._group_no.toString() === i.toString()
                            )
                                for (sgSession of sgSetting.session_setting[0].groups) {
                                    studentIds = [...studentIds, ...sgSession._student_ids];
                                }
                            studentIds = studentIds.filter(
                                (ele, index) =>
                                    studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                            const loc = sgGenderStudents.findIndex(
                                (ele) => ele && ele.gender === sgSetting.gender,
                            );
                            if (loc === -1)
                                sgGenderStudents.push({
                                    gender: sgSetting.gender,
                                    studentIds,
                                    count: studentIds.length,
                                });
                            else {
                                sgGenderStudents[loc].studentIds = [
                                    ...studentIds,
                                    ...sgGenderStudents[loc].studentIds,
                                ];
                                sgGenderStudents[loc].studentIds = sgGenderStudents[
                                    loc
                                ].studentIds.filter(
                                    (ele, index) =>
                                        sgGenderStudents[loc].studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                sgGenderStudents[loc].count =
                                    sgGenderStudents[loc].studentIds.length;
                            }
                        }
                    }
                    // To do Need to calculate based on input with DC
                    const sessionData = { no_session: 0, completed_session: 0 };
                    // if (programSessionOrderData) {
                    const sessionOrderData = sessionOrderList.find(
                        (ele) => ele._course_id.toString() === sgCourse._course_id.toString(),
                    );
                    if (sessionOrderData) {
                        sessionData.no_session = sessionOrderData.session_flow_data.length;
                        sessionData.completed_session = 0;
                    }
                    // }
                    const courseVersionDetails = programCourseData.find(
                        (courseListElement) =>
                            courseListElement._id.toString() === sgCourse._course_id.toString(),
                    );
                    LevelCourses.push({
                        level: sgLevel.level,
                        term: sgLevel.term,
                        curriculum: sgLevel.curriculum,
                        rotation: sgLevel.rotation,
                        rotation_count: i,
                        _course_id: sgCourse._course_id,
                        course_name: sgCourse.course_name,
                        versionNo: courseVersionDetails.versionNo
                            ? courseVersionDetails.versionNo
                            : 1,
                        versioned: courseVersionDetails.versioned
                            ? courseVersionDetails.versioned
                            : false,
                        versionName: courseVersionDetails.versionName
                            ? courseVersionDetails.versionName
                            : '',
                        versionedFrom: courseVersionDetails.versionedFrom
                            ? courseVersionDetails.versionedFrom
                            : null,
                        versionedCourseIds: courseVersionDetails.versionedCourseIds
                            ? courseVersionDetails.versionedCourseIds
                            : [],
                        course_no: sgCourse.course_no,
                        course_type: sgCourse.course_type,
                        students: sgGenderStudents,
                        // To do Need to calculate based on input in LMS
                        final_warning: 0,
                        denial: 0,
                        ...sessionData,
                        student_absence_percentage: sgCourse.student_absence_percentage,
                    });
                }
            } else {
                const sgGenderStudents = [];
                for (sgSetting of sgCourse.setting) {
                    if (sgSetting.gender === BOTH) {
                        let studentIds = [];
                        if (
                            sgSetting &&
                            sgSetting.session_setting &&
                            sgSetting.session_setting.length &&
                            sgSetting.session_setting[0].groups
                        ) {
                            studentIds = [
                                ...studentIds,
                                ...sgSetting.session_setting[0].groups
                                    .map((groupElement) => groupElement._student_ids)
                                    .flat(),
                            ];
                        }
                        studentIds = studentIds.filter(
                            (ele, index) =>
                                studentIds.findIndex(
                                    (ele2) => ele2.toString() === ele.toString(),
                                ) === index,
                        );
                        const maleStudentIds = studentIds.filter((studentIdElement) =>
                            sgLevel.students.find(
                                (studentElement) =>
                                    studentElement.gender === GENDER.MALE &&
                                    studentElement._student_id.toString() ===
                                        studentIdElement.toString(),
                            ),
                        );
                        const femaleStudentIds = studentIds.filter((studentIdElement) =>
                            sgLevel.students.find(
                                (studentElement) =>
                                    studentElement.gender === GENDER.FEMALE &&
                                    studentElement._student_id.toString() ===
                                        studentIdElement.toString(),
                            ),
                        );
                        if (sgGenderStudents.length) {
                            const maleLoc = sgGenderStudents.findIndex(
                                (ele) => ele && ele.gender === GENDER.MALE,
                            );
                            const femaleLoc = sgGenderStudents.findIndex(
                                (ele) => ele && ele.gender === GENDER.FEMALE,
                            );
                            if (maleLoc !== -1) {
                                sgGenderStudents[maleLoc].studentIds = [
                                    ...maleStudentIds,
                                    ...sgGenderStudents[maleLoc].studentIds,
                                ];
                                sgGenderStudents[maleLoc].studentIds = sgGenderStudents[
                                    maleLoc
                                ].studentIds.filter(
                                    (ele, index) =>
                                        sgGenderStudents[maleLoc].studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                sgGenderStudents[maleLoc].count =
                                    sgGenderStudents[maleLoc].studentIds.length;
                            } else {
                                sgGenderStudents.push({
                                    gender: GENDER.MALE,
                                    studentIds: maleStudentIds,
                                    count: maleStudentIds.length,
                                });
                            }
                            // Female
                            if (femaleLoc !== -1) {
                                sgGenderStudents[femaleLoc].studentIds = [
                                    ...femaleStudentIds,
                                    ...sgGenderStudents[femaleLoc].studentIds,
                                ];
                                sgGenderStudents[femaleLoc].studentIds = sgGenderStudents[
                                    femaleLoc
                                ].studentIds.filter(
                                    (ele, index) =>
                                        sgGenderStudents[femaleLoc].studentIds.findIndex(
                                            (ele2) => ele2.toString() === ele.toString(),
                                        ) === index,
                                );
                                sgGenderStudents[femaleLoc].count =
                                    sgGenderStudents[femaleLoc].studentIds.length;
                            } else {
                                sgGenderStudents.push({
                                    gender: GENDER.FEMALE,
                                    studentIds: femaleStudentIds,
                                    count: femaleStudentIds.length,
                                });
                            }
                        } else {
                            sgGenderStudents.push(
                                {
                                    gender: GENDER.MALE,
                                    studentIds: maleStudentIds,
                                    count: maleStudentIds.length,
                                },
                                {
                                    gender: GENDER.FEMALE,
                                    studentIds: femaleStudentIds,
                                    count: femaleStudentIds.length,
                                },
                            );
                        }
                    } else {
                        let studentIds = [];
                        if (
                            sgSetting &&
                            sgSetting.session_setting &&
                            sgSetting.session_setting.length &&
                            sgSetting.session_setting[0].groups
                        )
                            for (sgSession of sgSetting.session_setting[0].groups) {
                                studentIds = [...studentIds, ...sgSession._student_ids];
                            }
                        studentIds = studentIds.filter(
                            (ele, index) =>
                                studentIds.findIndex(
                                    (ele2) => ele2.toString() === ele.toString(),
                                ) === index,
                        );
                        const loc = sgGenderStudents.findIndex(
                            (ele) => ele && ele.gender === sgSetting.gender,
                        );
                        if (loc === -1)
                            sgGenderStudents.push({
                                gender: sgSetting.gender,
                                studentIds,
                                count: studentIds.length,
                            });
                        else {
                            sgGenderStudents[loc].studentIds = [
                                ...studentIds,
                                ...sgGenderStudents[loc].studentIds,
                            ];
                            sgGenderStudents[loc].studentIds = sgGenderStudents[
                                loc
                            ].studentIds.filter(
                                (ele, index) =>
                                    sgGenderStudents[loc].studentIds.findIndex(
                                        (ele2) => ele2.toString() === ele.toString(),
                                    ) === index,
                            );
                            sgGenderStudents[loc].count = sgGenderStudents[loc].studentIds.length;
                        }
                    }
                }
                // To do Need to calculate based on input with DC
                const sessionData = { no_session: 0, completed_session: 0 };
                // if (programSessionOrderData) {
                const sessionOrderData = sessionOrderList.find(
                    (ele) => ele._course_id.toString() === sgCourse._course_id.toString(),
                );
                if (sessionOrderData) {
                    sessionData.no_session = sessionOrderData.session_flow_data.length;
                    sessionData.completed_session = 0;
                }
                // }
                const courseVersionDetails = programCourseData.find(
                    (courseListElement) =>
                        courseListElement._id.toString() === sgCourse._course_id.toString(),
                );
                LevelCourses.push({
                    level: sgLevel.level,
                    term: sgLevel.term,
                    curriculum: sgLevel.curriculum,
                    rotation: sgLevel.rotation,
                    _course_id: sgCourse._course_id,
                    course_name: sgCourse.course_name,
                    versionNo:
                        courseVersionDetails && courseVersionDetails.versionNo
                            ? courseVersionDetails.versionNo
                            : 1,
                    versioned:
                        courseVersionDetails && courseVersionDetails.versioned
                            ? courseVersionDetails.versioned
                            : false,
                    versionName:
                        courseVersionDetails && courseVersionDetails.versionName
                            ? courseVersionDetails.versionName
                            : '',
                    versionedFrom:
                        courseVersionDetails && courseVersionDetails.versionedFrom
                            ? courseVersionDetails.versionedFrom
                            : null,
                    versionedCourseIds:
                        courseVersionDetails && courseVersionDetails.versionedCourseIds
                            ? courseVersionDetails.versionedCourseIds
                            : [],
                    course_no: sgCourse.course_no,
                    course_type: sgCourse.course_type,
                    students: sgGenderStudents,
                    // To do Need to calculate based on input in LMS
                    final_warning: 0,
                    denial: 0,
                    ...sessionData,
                    student_absence_percentage: sgCourse.student_absence_percentage,
                });
            }
        }
    }

    // Program Calendar Data Adding
    const pcLevel = clone(programCalendarDataList.level);
    delete pcLevel.events;
    const programStudents = [];
    const students = [];
    let denial_count = 0;
    let final_warning_count = 0;
    let lmsTemp;
    const courseBasedStudentCount = {
        male: 0,
        female: 0,
    };
    // Rotation Flow
    if (pcLevel.rotation === 'yes') {
        const rotationCourses = [];
        for (rotationCourse of pcLevel.rotation_course) {
            const courseLists = [];
            for (pcCourse of rotationCourse.course) {
                const courseData = LevelCourses.find(
                    (ele) =>
                        ele.level === pcLevel.level_no &&
                        ele.term === pcLevel.term &&
                        ele.rotation_count === rotationCourse.rotation_count &&
                        ele._course_id.toString() === pcCourse._course_id.toString(),
                );
                if (courseData) {
                    for (stdElement of courseData.students) {
                        if (stdElement.gender === GENDER.MALE)
                            courseBasedStudentCount.male += stdElement.studentIds.length;
                        if (stdElement.gender === GENDER.FEMALE)
                            courseBasedStudentCount.female += stdElement.studentIds.length;
                        const loc = students.findIndex(
                            (ele) =>
                                ele &&
                                ele.gender === stdElement.gender &&
                                ele.term === pcLevel.term,
                        );
                        if (loc === -1) {
                            students.push({
                                term: pcLevel.term,
                                gender: stdElement.gender,
                                studentIds: stdElement.studentIds,
                                count: stdElement.count,
                            });
                        } else {
                            let std = [...students[loc].studentIds, ...stdElement.studentIds];
                            std = clone(
                                std.filter(
                                    (ele4, index) =>
                                        std.findIndex(
                                            (ele3) => ele3.toString() === ele4.toString(),
                                        ) === index,
                                ),
                            );
                            students[loc].studentIds = std;
                            students[loc].count = std.length;
                        }
                    }
                    const courseSessionOrderData = sessionOrderList.find(
                        (ele) => ele._course_id.toString() === pcCourse._course_id.toString(),
                    )
                        ? sessionOrderList.find(
                              (ele) => ele._course_id.toString() === pcCourse._course_id.toString(),
                          ).session_flow_data
                        : [];
                    const courseScheduled = clone(
                        programCourseScheduleData.filter(
                            (ele) =>
                                ele.rotation &&
                                ele.rotation === 'yes' &&
                                ele.rotation_count === rotationCourse.rotation_count &&
                                ele._course_id.toString() === pcCourse._course_id.toString() &&
                                ele.term === pcLevel.term &&
                                ele.level_no === pcLevel.level_no,
                        ),
                    );

                    // * Session Schedule Based Report Course Session Order filter based on Schedule
                    let sessionOrderData = courseSessionOrderData
                        ? clone(courseSessionOrderData)
                        : { session_flow_data: [] };
                    if (SCHEDULE_SESSION_BASED_REPORT && SCHEDULE_SESSION_BASED_REPORT === 'true') {
                        sessionOrderData = courseSessionOrderFilterBasedSchedule({
                            courseSessionFlow: courseSessionOrderData,
                            courseSchedule: scheduleData.data,
                        }).session_flow_data;
                        pcCourse.no_session = sessionOrderData.length;
                    }

                    //Warning Calculations
                    lmsTemp = lmsData;
                    const courseStudentIds = courseData.students
                        .map((ele) => ele.studentIds)
                        .flat();
                    if (
                        lmsTemp.warningAbsenceData[0] &&
                        courseData.student_absence_percentage &&
                        courseData.student_absence_percentage !== 0
                    ) {
                        lmsTemp.warningAbsenceData[0].absence_percentage =
                            courseData.student_absence_percentage;
                    } else {
                        lmsTemp = lmsClonedData;
                    }
                    const studentReport = studentAttendanceReport(
                        courseStudentIds,
                        courseScheduled,
                        lmsTemp.warningAbsenceData,
                        studentCriteriaData,
                    );

                    courseData.denial = studentReport.filter(
                        (ele) => ele.warning === lmsTemp.denialWarning,
                    ).length;
                    courseData.final_warning = lmsTemp.finaleWarning
                        ? studentReport.filter((ele) => ele.warning === lmsTemp.finaleWarning)
                              .length
                        : 0;
                    const sCourse = clone(
                        programCourseData.find(
                            (uCourse) =>
                                uCourse._id.toString() === courseData._course_id.toString(),
                        ),
                    );
                    // Credit Hour Calculation
                    const credit_hours = courseCreditContactHours(
                        programSessionDeliveryTypeData,
                        programId,
                        courseScheduled,
                        sessionOrderData,
                        sCourse && sCourse.credit_hours
                            ? sCourse.credit_hours
                            : pcCourse.credit_hours,
                    );
                    pcCourse.credit_hours = credit_hours;
                    pcCourse = { ...courseData, ...pcCourse };
                    pcCourse.students.forEach((courseElement) => {
                        delete courseElement.studentIds;
                    });
                    final_warning_count += courseData.final_warning;
                    denial_count += courseData.denial;

                    // pcData[levelIndex].rotation_course[rotationCourseIndex].course[
                    //     courseIndex
                    // ].program_name = departmentSubject.data.find(
                    //     (ele) =>
                    //         ele.program_id.toString() === pcLevel._program_id.toString(),
                    // ).program_name;
                    pcCourse._program_id = programId;
                    // Shared Details Adding
                    pcCourse.course_shared =
                        sCourse._program_id.toString() !== programId.toString();

                    pcCourse.course_shared_program = pcCourse.course_shared
                        ? sCourse.course_assigned_details.find(
                              (cad) =>
                                  cad._program_id.toString() === sCourse._program_id.toString(),
                          ).program_name
                        : userProgram.name;
                    pcCourse.completed_session = credit_hours
                        .map((ele) => ele.completed_sessions)
                        .reduce((accumulator, current) => accumulator + current);
                    // pcCourse.completed_session = courseScheduled.filter(
                    //     (ele) => ele && ele.status && ele.status === COMPLETED,
                    // ).length;
                    courseLists.push(pcCourse);
                }
            }
            rotationCourse.course = courseLists;
            rotationCourses.push(rotationCourse);
        }
        pcLevel.rotation_course = rotationCourses;
    } else {
        const courseLists = [];
        for (pcCourse of pcLevel.course) {
            const courseData = LevelCourses.find(
                (ele) =>
                    ele.level === pcLevel.level_no &&
                    ele.term === pcLevel.term &&
                    ele._course_id.toString() === pcCourse._course_id.toString(),
            );
            if (courseData) {
                for (stdElement of courseData.students) {
                    if (stdElement.gender === GENDER.MALE)
                        courseBasedStudentCount.male += stdElement.studentIds.length;
                    if (stdElement.gender === GENDER.FEMALE)
                        courseBasedStudentCount.female += stdElement.studentIds.length;
                    const loc = students.findIndex(
                        (ele) =>
                            ele && ele.gender === stdElement.gender && ele.term === pcLevel.term,
                    );
                    if (loc === -1) {
                        students.push({
                            term: pcLevel.term,
                            gender: stdElement.gender,
                            studentIds: stdElement.studentIds,
                            count: stdElement.count,
                        });
                    } else {
                        let std = [...students[loc].studentIds, ...stdElement.studentIds];
                        std = clone(
                            std.filter(
                                (ele4, index) =>
                                    std.findIndex((ele3) => ele3.toString() === ele4.toString()) ===
                                    index,
                            ),
                        );
                        students[loc].studentIds = std;
                        students[loc].count = std.length;
                    }
                }
                // Credit Hour Calculation
                const courseSessionOrderData = sessionOrderList.find(
                    (ele) => ele._course_id.toString() === pcCourse._course_id.toString(),
                )
                    ? sessionOrderList.find(
                          (ele) => ele._course_id.toString() === pcCourse._course_id.toString(),
                      ).session_flow_data
                    : [];
                const courseScheduled = clone(
                    programCourseScheduleData.filter(
                        (ele) =>
                            ele._course_id.toString() === pcCourse._course_id.toString() &&
                            ele.term === pcLevel.term &&
                            ele.level_no === pcLevel.level_no,
                    ),
                );

                // * Session Schedule Based Report Course Session Order filter based on Schedule
                let sessionOrderData = courseSessionOrderData
                    ? clone(courseSessionOrderData)
                    : { session_flow_data: [] };
                if (SCHEDULE_SESSION_BASED_REPORT && SCHEDULE_SESSION_BASED_REPORT === 'true') {
                    sessionOrderData = courseSessionOrderFilterBasedSchedule({
                        courseSessionFlow: courseSessionOrderData,
                        courseSchedule: courseScheduled,
                    }).session_flow_data;
                    pcCourse.no_session = sessionOrderData.length;
                }

                //Warning Calculations
                lmsTemp = lmsData;
                const courseStudentIds = courseData.students.map((ele) => ele.studentIds).flat();
                if (
                    lmsTemp.warningAbsenceData[0] &&
                    courseData.student_absence_percentage &&
                    courseData.student_absence_percentage !== 0
                ) {
                    lmsTemp.warningAbsenceData[0].absence_percentage =
                        courseData.student_absence_percentage;
                } else {
                    lmsTemp = lmsClonedData;
                }
                const studentReport = studentAttendanceReport(
                    courseStudentIds,
                    courseScheduled || [],
                    lmsTemp.warningAbsenceData,
                    studentCriteriaData,
                );
                courseData.denial = studentReport.filter(
                    (ele) => ele.warning === lmsTemp.denialWarning,
                ).length;
                courseData.final_warning = lmsTemp.finaleWarning
                    ? studentReport.filter((ele) => ele.warning === lmsTemp.finaleWarning).length
                    : 0;

                const sCourse = clone(
                    programCourseData.find(
                        (uCourse) => uCourse._id.toString() === courseData._course_id.toString(),
                    ),
                );
                const credit_hours = courseCreditContactHours(
                    programSessionDeliveryTypeData,
                    programId,
                    courseScheduled,
                    sessionOrderData,
                    sCourse && sCourse.credit_hours ? sCourse.credit_hours : pcCourse.credit_hours,
                );
                pcCourse.credit_hours = credit_hours;
                pcCourse = { ...courseData, ...pcCourse };
                pcCourse.students.forEach((courseElement) => {
                    delete courseElement.studentIds;
                });
                final_warning_count += courseData.final_warning;
                denial_count += courseData.denial;
                pcCourse._program_id = programId;
                // Shared Details Adding
                pcCourse.course_shared = sCourse
                    ? sCourse._program_id.toString() !== programId.toString()
                    : false;
                pcCourse.course_shared_program = sCourse
                    ? pcCourse.course_shared
                        ? sCourse.course_assigned_details.find(
                              (cad) =>
                                  cad._program_id.toString() === sCourse._program_id.toString(),
                          ).program_name
                        : userProgram.name
                    : '';
                pcCourse.completed_session = credit_hours
                    .map((ele) => ele.completed_sessions)
                    .reduce((accumulator, current) => accumulator + current);
                // pcCourse.completed_session = courseScheduled.filter(
                //     (ele) => ele && ele.status && ele.status === COMPLETED,
                // ).length;
                courseLists.push(pcCourse);
            }
        }
        pcLevel.course = courseLists;
    }
    students.forEach((ele) => {
        const proLoc = programStudents.findIndex(
            (ele2) => ele2.term === pcLevel.term && ele2.gender === ele.gender,
        );
        const genderCourseStudentCount =
            ele.gender === GENDER.MALE
                ? courseBasedStudentCount.male
                : courseBasedStudentCount.female;
        ele.courseStudents = genderCourseStudentCount;
        if (proLoc === -1) {
            programStudents.push({
                term: pcLevel.term,
                gender: ele.gender,
                studentIds: ele.studentIds,
                count: ele.count,
            });
        } else {
            let std = [...programStudents[proLoc].studentIds, ...ele.studentIds];
            std = clone(
                std.filter(
                    (ele4, index) =>
                        std.findIndex((ele3) => ele3.toString() === ele4.toString()) === index,
                ),
            );
            programStudents[proLoc].studentIds = std;
            programStudents[proLoc].count = std.length;
        }
        // Removing Due to Data Load
        delete ele.studentIds;
    });
    pcLevel.students = students;
    pcLevel.final_warning = final_warning_count;
    pcLevel.denial = denial_count;
    let activeStd = 0;
    let programStudentIds = [];
    programStudents.forEach((ele) => {
        activeStd += ele.count;
        programStudentIds = [...programStudentIds, ...ele.studentIds];
        delete ele.studentIds;
    });
    return pcLevel;
};
