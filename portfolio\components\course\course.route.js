const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const CourseController = require('./course.controller');
const {
    getUserCoursesSchema,
    getStudentGroupSchema,
    getFacultyListSchema,
    getCoursesByProgramSchema,
    getPortfolioSchema,
    getDeliveryTypesSchema,
} = require('./course.validation');

router.get(
    '/user',
    validate(getUserCoursesSchema),
    catchAsync(CourseController.getListOfCoursesByUser),
);

router.get(
    '/student-group',
    validate(getStudentGroupSchema),
    catchAsync(CourseController.getStudentGroupsByCourse),
);

router.get(
    '/faculty',
    validate(getFacultyListSchema),
    catchAsync(CourseController.getFacultyListByCourse),
);

router.get('/program', catchAsync(CourseController.getPrograms));

router.get(
    '/',
    validate(getCoursesByProgramSchema),
    catchAsync(CourseController.getCoursesByProgram),
);

router.get('/portfolio', validate(getPortfolioSchema), catchAsync(CourseController.getPortfolio));

router.get(
    '/delivery-type',
    validate(getDeliveryTypesSchema),
    catchAsync(CourseController.getDeliveryTypesByCourse),
);

module.exports = router;
