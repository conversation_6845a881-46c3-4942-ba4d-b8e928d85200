const Joi = require('joi');
const {
    objectIdRQSchema,
    stringSchema,
    booleanSchema,
    objectIdSchema,
} = require('../utility/validationSchemas');

exports.addTypeValidator = Joi.object({
    announcementType: Joi.array().items(
        Joi.object({
            name: stringSchema,
        }).unknown(true),
    ),
    priorityType: Joi.array().items(
        Joi.object({
            name: stringSchema,
            colorCode: stringSchema,
        }).unknown(true),
    ),
    tagAnnouncementType: booleanSchema,
    priorityAnnouncement: booleanSchema,
}).unknown(true);

exports.deleteTypeValidator = Joi.object({
    announcementTypeId: objectIdSchema,
    priorityTypeId: objectIdSchema,
    announcementSettingId: objectIdRQSchema,
}).unknown(true);
