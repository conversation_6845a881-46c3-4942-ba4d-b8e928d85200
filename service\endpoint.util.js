const httpMethods = {
    post: 'POST',
    get: 'GET',
    put: 'PUT',
    patch: 'PATCH',
    delete: 'DELETE',
};

const encryptDecryptApplicable = {
    request: 'REQUEST',
    response: 'RESPONSE',
};

const versionRoute = '/api/v1';

const encryptedRoutes = () => {
    const commonRouteWithoutParamsRoutes = [
        {
            route: '/digi_program',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_program/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/course_schedule_setting/:programId',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_course_group',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_course_group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar_event',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar_event/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar_event',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar_event/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar_event/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/institution_calendar/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_session_delivery_types/:program_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/role',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_impact_mapping_type',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_content_mapping_type',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_content_mapping_type/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_content_mapping_type',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.request],
        },
        {
            route: '/digi_content_mapping_type',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.request],
        },
        {
            route: '/digi_impact_mapping_type',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.request],
        },
        {
            route: '/digi_impact_mapping_type/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.request],
        },
        {
            route: '/digi_impact_mapping_type/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/role',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.response],
        },
        {
            route: '/role',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/role',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/role/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/module',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_session_delivery_types',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_course',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_course/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructures',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructures',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructure_management',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructure_management/:_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructure_management',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructure_management/:_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/infrastructure_management/:_id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/time_group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/time_group/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/user/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/time_group',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/time_group/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/time_group/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_curriculum',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_curriculum/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_curriculum/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_program',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_program/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_department_subject/:program_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_framework',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/digi_framework',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_framework/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/digi_department_subject',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/country',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/vaccination',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/dashboard/:userId/:institutionCalendar/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/module',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/course_schedule',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/course_schedule/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: '/course_schedule/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: '/program_calendar_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: '/program_calendar_course',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiFrameWorkRouter = [
        {
            route: 'filter_curriculum_year_program_standard_range_settings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_framework/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const selfRegisterRoutes = [
        {
            route: 'verifyEmail',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'verifyUserOTP',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getUserDetails',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        // {
        //     route: '/updateUserDetails',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        // },
        {
            route: 'checkUserPassword',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'programName',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'dataForApproval',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateUserDetailInApprovalFlow',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'deleteUser',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/userRegistration/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const roleRoutes = [
        {
            route: 'role_list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'listRoleBasedOnTheRoleAndPermission',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'roleUser/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/role/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const roleAssignRoutes = [
        {
            route: 'role_assign/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'role_assign',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'role_assign/department_list',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcAdminUserRoutes = [
        // { route: 'authLogin', method: httpMethods.post,applicable: [encryptDecryptApplicable.response], },
        // { route: 'refresh_token', method: httpMethods.post,applicable: [encryptDecryptApplicable.response], },
        {
            route: 'signup',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        // {
        //     route: 'forget',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        {
            route: 'set_password',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'register_mobile',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: ':user_type/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: ':id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: ':id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'profile_update',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'user_employment',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'send_mobile_otp',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'change_mobile',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'authLoggedIn/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'staff_profile_details/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'user_details/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'user_search/:user_type/:tab/:text',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_all/:user_type/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'user_img_get/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_all_user',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getProgramBasedCourseList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'user_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'active_inactive',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'user_mail_push',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'user_academic_allocation',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'single_insert',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'import',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'otp_verify',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'forget_send_otp',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'forget_set_password',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/user/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiDepartmentSubjectRoutes = [
        {
            route: 'import_department_subject',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_department_subject',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'share_department/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'share_subject/:department_id/:subject_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_department/:program_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'subject',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'subject/:department_id/:subject_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'subject/:department_id/:subject_id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'withOutShare/:program_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_department_subject/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcUserRoutes = [
        {
            route: 'authLogin',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'authLoggedIn',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'forget',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'logout',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'otp_verify',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'set_password',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'token',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/user/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcRoutes = [
        {
            route: 'userCourses/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-courses/:staffId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course/:userId/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-course-session-tab/:userId/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-schedule/:scheduleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userCourseSessionDetails/:userId/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'scheduleDetailsWithSession/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userCalendars/:userId/:userType',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'staffCourseExport',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'courseExport',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getstudentList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userLevelComprehensiveWarning',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userLevelComprehensiveAttendance',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-schedule-by-date/:userId/:date',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course-session-flow-slo/:courseId/:sessionId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-session-schedule/:courseId/:sessionId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/course_session/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcSessionRoutes = [
        {
            route: 'sessions/session_start',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/session_close',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/student_attendance',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/session_status/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/student_session_status/:id/:student',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/session_report/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/session_reset/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/session_change_attendance',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/session_attendance_change',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/session_retake',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/session_stop',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessions/session_close_with_attendance',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/session_comment',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/scheduleStart',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/campusOtpVerification',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'sessions/campusStudentList/:_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        // {
        //     route: 'sessions/campusImageUrl',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.request],
        // },
        // {
        //     route: 'sessions/generateUrl',
        //     method: httpMethods.get,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        {
            route: 'sessions/updateStudentStatus',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcDashboardRoutes = [
        {
            route: 'getSessions',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getRating',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getManualStaffDetail',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/dashboard/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcScheduleMultiDeviceAttendanceRoutes = [
        {
            route: 'scheduleStart',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'scheduleStartInTab',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'scheduleStudentAttendance',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'scheduleCloseWithAttendance/:scheduleId',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'scheduleDetails/:scheduleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/schedule-multi-device-attendance/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcScheduleAttendanceRoutes = [
        {
            route: 'session_retake',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'end_session_retake',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'accept_attendance',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get_student_status',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'attendance_report',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'submit_quiz',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'get-questions',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'student_submit_quiz',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'scheduleStudentFaceAnomalyAdd',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'get-student-answered-questions/:_student_id/:_id/:scheduleAttendanceId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student-wise-attendance/:_id/:_student_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateManualAttendance',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'updateChangingManualAttendance',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/schedule-attendance/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));
    const institutionCalendar = [
        {
            route: 'list_calendar',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'calendars',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/institution_calendar/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));
    const faceReRegisterRoutes = [
        {
            route: 'reRegisterFaceList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userBasedScheduleList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'faceRegisterApproval',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        // {
        //     route: 'faceGet',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        // },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/faceReRegister/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcChatV2Routes = [
        {
            route: 'create-course-channels',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getNewChatCount',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-channels',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'channel-msg',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'group-info',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        // {
        //     route: 'sendMessageWithAttachment',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.request],
        // },
        // {
        //     route: 'generateSignedUrl',
        //     method: httpMethods.get,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        {
            route: 'getChannelsList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'createPrivateChannel',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getChatProfile',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'deleteChat/:channelId',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'forwardMessage',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/digichat_v2/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const announcementRoutes = [
        {
            route: 'receiveNotificationCount',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'newCreateAnnouncement',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'userFilterType',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'listBoardAnnouncement',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'singleCourseList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'individualStudentList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'individualStaffList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'digiconnectSpecificUser',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getUserSetting',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getSingleAnnouncementDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'editNewAnnouncement',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'revokeAnnouncement',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'immediatePublish',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'newListManageAnnouncement',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userViewedNotification',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getUserSelectedProgram',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'deleteAnnouncement',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'announcementUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'programList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/announcement/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const announcementSettingRoutes = [
        {
            route: 'typeList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'addTypes',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/announcementSetting/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcAnnouncementRoutes = [
        {
            route: 'receiveNotificationCount',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateViewedNotification',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'singleListAnnouncement',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userViewNotification',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'typeList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/announcement/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const infraStructureRoutes = [
        {
            route: 'edit_infrastructure/:_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'delete_infrastructure/:_id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_location',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/infrastructures/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const courseScheduleSettingRouter = [
        {
            route: 'remote-scheduling/list/:programId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/course_schedule_setting/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiProgramRoutes = [
        {
            route: 'programListWithType',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program_department_list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_program',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.request],
        },
        {
            route: 'sidebar/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program_department_list_along_share',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'import_program',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_program/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiMappingRoutes = [
        {
            route: 'program/:id/:curriculumId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_mapping/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsStudentSettingRoutes = [
        {
            route: 'getUserCalendarList/:userId/:userType/:isWarning',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getSetting',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getLeavePoliceForStudent',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        // {
        //     route: 'getUserCalendarList',
        //     method: httpMethods.get,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        {
            route: 'createCategory',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'editProgramLevel',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateWarningConfig',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'deleteProgramLevel',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'removeCategory',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'addLevels',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'addProgramLevel',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'editCategoryName',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'editLevel',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'removeLevel',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'deleteLeavePolicy',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'divideGenderSegregation',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateCategoryStatus',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateTypes',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'createTypes',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'editLeavePolicy',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'add-warning-denial-configuration',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'addLeavePolicy',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'termsAndCondition',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateCourseOrComprehensive',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        // {
        //     route: 'upload',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.response],
        // },
    ].map((route) => ({
        route: `${versionRoute}/lmsStudentSetting/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsWarningRoutes = [
        {
            route: 'courseWarning',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'courseWarningForCalendar',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'studentAcknowledge',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sendManualMail',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'staffAutoWarning',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/lmsWarning/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcDocumentRoutes = [
        {
            route: 'document/:userId/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/:userId/:courseId/:sessionId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/:userId',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/:id/:userId',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'document/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/getById/:_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/userDocuments/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'document/select-sessions/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'notification/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'notification/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const activityRoutes = [
        // {
        //     route: 'activities',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        {
            route: 'activities/select-sessions/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities/:id/get-students',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities/exportActivity/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities/surveyExportActivity/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities/activitySurveyExport',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const activityV2Routes = [
        {
            route: 'activities_v2',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id/started',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id/stop',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id/answer',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id/accept-question-response',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/getById/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/get-session-schedule/:courseId/:sessionId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/question-bank',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response, encryptDecryptApplicable.request],
        },
        {
            route: 'activities_v2/question-bank/questions',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'activities_v2/get-questions',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'activities_v2/save-student-marks',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/publish-student-marks',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/exportActivity/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/select-sessions/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'activities_v2/:id/view-result',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const taxonomyRoutes = [
        {
            route: 'taxonomy',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const globalSessionRoutes = [
        {
            route: 'create-institution-session-setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'get-institution-session-setting',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'lateConfig',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'lateConfig',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-tardis',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'disciplinary_remarks_visibility',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'disciplinary_remarks_visibility',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'update-tardis',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'delete-tardis',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'getGlobalSessionStatusDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateGlobalSessionStatusDetails',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'updateSchedulePermission',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/global-session-settings/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcDiscussionRoutes = [
        {
            route: 'discussions/list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/create',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/getLists',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/getReplyLists',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/reply',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/like',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/viewAttachment',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/getStudentDiscussionLists',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/reply',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'discussions/reply/like',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'disciplinary_remarks',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'disciplinary_remarks',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'disciplinary_remarks',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const programReportSettingRoutes = [
        {
            route: 'program-report-setting',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program-report-setting',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const reportAnalyticsRoutes = [
        {
            route: 'activity_report/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:userType/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'dashboard/:institutionCalendarId/:userId/:roleId/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'dashboard/:institutionCalendarId/:userId/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program/:institutionCalendarId/:programId/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_list/:institutionCalendarId/:programId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'department_subject_list/:institutionCalendarId/:programId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'department_subject/:institutionCalendarId/:programId/:departmentId/:subjectId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program_staff_list/:institutionCalendarId/:programId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'staff_details/:institutionCalendarId/:programId/:staffId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_overview/:institutionCalendarId/:programId/:courseId/:levelNo/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course-attendance-log/:institutionCalendarId/:programId/:courseId/:term/:scheduleType/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course-attendance-log-export/:institutionCalendarId/:programId/:courseId/:term/:scheduleType/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_sessions/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_details/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_attendance_report/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:userId/:userType',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'staff_details/:institutionCalendarId/:programId/:courseId/:term/:type/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/reports_analytics/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsStudentRoutes = [
        {
            route: 'listData',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getGlobalSessionSettings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'listSchedule',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'listApprovalLevelWiseStatus',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'revokeApproval',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'staffApproval',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'studentListForStaffApprover',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateAgree',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'statusUpdate',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'apply',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/lmsStudent/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsDenialRoutes = [
        {
            route: 'get-programs',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-program-levels',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-courses',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-students-denial',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-student-stats',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateDenialPercentage',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'listDenialData',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'denialManagementList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'studentsList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'check-denial-access',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/lmsDenial/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsStaffRoutes = [
        {
            route: 'permissions/:to',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'lms_approver',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/lms/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcCourseAdminRoutes = [
        {
            route: 'course/:userId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course/:userId/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-courses/:staffId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course-session-flow/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course-session-flow-slo/:courseId/:sessionId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'feedback/:studentId/:scheduleId',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/course-admin/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcSurveyRoutes = [
        {
            route: 'sessionSurvey/:userId/:institutionCalendarId/:programId/:courseId/:sessionId/:yearNo/:levelNo/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'scheduleSessionSurvey/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCourses',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'selfEvaluation/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'comparisonReport/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'yetToRate/:userId/:institutionCalendarId/:programId/:courseId/:yearNo/:levelNo/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sessionSurvey',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'scheduleSessionSurvey',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'courseSessionMock',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/survey/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const institutionCalendarEventRouter = [
        {
            route: 'list_event/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'add_reviewer/:id',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'add_reviewer_review',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'send_reviewer_notification/:id',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'send_notification',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'list_calendar_event_reviewer/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'remove_reviewer/:id/:reviewer',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list_id_reviewer/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/institution_calendar_event/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const programCalendarReviewRoutes = [
        {
            route: 'staff_student_calendar_view',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/program_calendar_review/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const dcAdminDashboardRoutes = [
        {
            route: 'leave_list/:user_id/:inst_cal_id/:role_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'monitoringCourses/:userId/:institutionCalendar/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'monitoringCourseDetails/:userId/:institutionCalendar/:roleId/:programId/:courseId/:term/:level_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'configure_settings/:user_id/:inst_cal_id/:role_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'programWise/:userId/:institutionCalendar/:roleId/:tab',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/dashboard/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const studentGroupRoutes = [
        {
            route: 'program_ic_list_dashboard',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'dashboard/:program/:institution',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_group_lists/:_id/:level/:batch/:course',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'individual_courses_list/:_id/:level/:batch/:gender',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'individual_course_grouped_list_filter/:_id/:level/:batch/:gender/:course',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_course_setting/:_id/:level/:batch/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'individual_data_check',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_import',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'individual_course_group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get_student_courses_year_two/:academic_no/:_id/:level/:batch/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'add_student_to_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'excess_count_change',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_publish',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_group_export',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_global_search',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_global_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'student_group_change',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_export',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'group_list_filter/:_id/:level/:batch/:gender/:group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_grouped_list_filter/:_id/:level/:batch/:gender/:group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'foundation_get_course_setting/:_id/:level/:batch/:_course_id/:gender',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'setting_get',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'import',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'student_search/:academic_no/:id/:batch/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'add_student_group',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'group_publish',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sub_course_grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_student_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'group_change',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_setting_get/:_id/:level/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_get_student/:academic_no/:_id/:level/:batch',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_bulk_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_get_student_course/:academic_no/:_id/:level/:batch/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_add_student_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_course_student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_group_export/:_id/:level/:batch/:course/:fyd_group/:gender/:delivery/:d_group_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_get_clone_group/:_id/:batch/:level/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_clone',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'individual_course_grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'add_student_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get_student/:academic_no/:id/:level/:batch/:course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/student_group/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsReviewRoutes = [
        {
            route: 'students_attendance_sheet/:institutionCalendarId/:programId/:courseId/:year_no/:levelNo/:term/:type/:user_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_course_students_register/:institutionCalendarId/:programId/:courseId/:levelNo/:term/:gender',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program_course_list/:institutionCalendarId/:userId/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'attendance_history/:scheduleId/:studentId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'reason_update/:schedule_id/:student_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/lms_review/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const sessionReport = [
        {
            route: 'attendanceReport/:sessionDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'programCourseList/:sessionDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'attendanceReportExport/:sessionDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'scheduleDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'userProgramList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/session-report/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const disciplinaryRemarks = [
        {
            route: 'students_all_remarks',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'mailDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'schedule_details',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sendMail',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/disciplinary_remarks/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const lmsAttendanceConfigRoutes = [
        {
            route: 'getLateExcludeStatus',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        // {
        //     route: 'attendanceConfig',
        //     method: httpMethods.get,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        // {
        //     route: 'getLateExcludeStatus',
        //     method: httpMethods.get,
        //     applicable: [encryptDecryptApplicable.response],
        // },
        {
            route: 'getAttendanceConfig',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateLateExclude',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'attendanceAddRow',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/lmsAttendanceConfig/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const courseCoordinatorRoutes = [
        {
            route: 'course_coordinator/coordinators/:institution_calendar/:program',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_coordinator/program_list/:userId/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_coordinator',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_coordinator/publish',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const extraCurricularAndBreakRoutes = [
        {
            route: 'extraCurricular-breakTiming/:programId/:instCalId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'extraCurricular-breakTiming',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'extraCurricular-breakTiming/:id/:programId',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'extraCurricular-breakTiming/enable-disable/:id/:programId',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'extraCurricular-breakTiming/:id/:programId',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/course-schedule-settings/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const courseScheduleRoutes = [
        {
            route: 'user_session_list/:instCalId/:userId/:date',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_list/:institution_calendar/:programId/:userId/:roleId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'schedule_list/:programId/:instCalId/:courseId/:term/:level_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'listManualAttendanceDetails/:programId/:courseId/:levelNo',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_advanced_setting_manage_course/:programId/:courseId/:levelNo',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'classLeader',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'available_list/:programId/:instCalId/:courseId/:term/:level_no/:_session_id/:start_time/:end_time',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'cancel_session/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'merge_schedule/:programId/:instCalId/:courseId/:term/:level_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'merge_session',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getStudentGroupBasedDeliveryTypes',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'support_session',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'support_session/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'event',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'event/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'level_time_table/:programId/:instCalId/:term/:level_no/:startDate/:endDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'manage_course/:programId/:instCalId/:courseId/:term/:level_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_time_table/:programId/:instCalId/:courseId/:term/:level_no/:startDate/:endDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'manage_topic/:course_id/:level_no/:program_id/:_institution_calendar_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'lecture_setting_delete/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'manage_course_delivery/:programId/:instCalId/:courseId/:term/:level_no/:deliveryId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'lecture_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'manage_course/assign_day_time/:id',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'manage_course/update/:id/:day_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'manage_course/remove/:id/:day_id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'update_advanced_setting_manage_course/:programId/:courseId/:levelNo',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'auto_assign_course_delivery/:programId/:instCalId/:courseId/:term/:level_no/:deliveryId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'auto_schedule',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateManualAttendance/:programId/:courseId/:levelNo',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'listManualAttendanceStaff',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'manage_topic',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'manage_topic/:id/:course_id/:level_no/:program_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'manage_topic/:id/:course_id/:level_no/:program_id/',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'publish',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/course_schedule/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const courseVersionRoutes = [
        {
            route: 'getVersionedCourseDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'programCourseList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'viewCourseDetails/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'checkDuplicateVersionName',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'deleteCourse',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'editCourseDetails/:courseId',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/courseVersioning/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiCourseGroup = [
        {
            route: 'ungroup',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_course_group/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentRoutes = [
        {
            route: 'list-assignment',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student-list/:assignmentId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'extend-student-date',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'get-assignment/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'add-assignment',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'update-assignment/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'update-submission-due/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'update-studentData',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'revert-releasedGrade',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'update-MultipleStudentData',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'delete-assignment',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'list-session/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-instructor',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_student_group/:_institution_calendar_id/:_program_id/:_course_id/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_student_course_group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-student-assignment',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-student-course',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'update-status',
            method: httpMethods.patch,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'extend-due',
            method: httpMethods.patch,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'list-staff-assignment',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-mark-update',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get-program-calendar',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'duplicate',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'extend-assignment-date',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentAnswerRouters = [
        {
            route: 'create',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'update/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-answer/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentCategoryRouters = [
        {
            route: 'list-category/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'add-category',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'edit-category/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'delete-category/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-category/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentCommentsRouters = [
        {
            route: 'list/:assignmentId/:studentId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'update',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-comments/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentGroupRouters = [
        {
            route: 'create-assignment-group',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'update-assignment-group/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'get-assignment-group/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-assignment-group/:_assignment_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-group/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentCourseRoutes = [
        {
            route: 'list-courses',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-subjects',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-courses-for-staff',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get-clo-slo',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get-taxonomy',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-course/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentPromptRoutes = [
        {
            route: 'assignment-prompt',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-prompt/lists-without-answer',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-prompt',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-prompt/without-answer',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-prompt/lists',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-prompt/deleteRubrics',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentPromptAnswerRouters = [
        {
            route: 'create',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'bulk-create',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'get/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'update/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-prompt-answer/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentReportRouters = [
        {
            route: 'assignment-details',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-types',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'assignment-type-list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'create',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'update-publishedData',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'Publish-individual',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'report-data',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'unPublish',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'list-student-report',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'framework-details',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'outcome-assignment',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rubric-report/assignments-list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rubric-report/prompts-list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rubric-report/all-students',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'delete-report',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-report/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentRubricRouters = [
        {
            route: 'create',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'update/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'listAllAssignmentRubrics',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'listAssignmentRubrics',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-rubrics/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentSettingRouters = [
        {
            route: 'program-settings',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'program-settings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course-settings',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course-settings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'subject-settings',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'subject-settings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-settings/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const assignmentProgramInputRouters = [
        {
            route: 'role-based-modules',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list-programs',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/assignment-program/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiCurriculumRoutes = [
        {
            route: 'get_curriculum_by_program_id/:program_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_pre_requisite',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_curriculum',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'archive_curriculum/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_curriculum/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiSessionDeliveryTypeRoutes = [
        {
            route: 'add_delivery_type/:session_type_id',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_import_session_type',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_delivery_type',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'update_delivery_type/:session_type_id/:delivery_type_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'delete_delivery_type/:session_type_id/:delivery_type_id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'import_session_type',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'import_delivery_type',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_session_delivery_types/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const digiCourseRoutes = [
        {
            route: 'sessionOrderModules/:programId/:courseId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sessionOrderModules/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'sessionOrderModules',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessionOrderModules/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sessionOrderModules/:id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_assigned_list/:program_id/:curriculum_id/:year_id/:level_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_list_recurring_level/:program_id/:curriculum_id/:level_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_course_assigned_details/:id/:course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program_curriculum_year_level_list',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'list_id_session_flow/:course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'assign_course/:id',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'assign_course_edit/:id/:assigned_course_details_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_import_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_import_session_flow',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check_slo',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'session_flow_add',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'session_flow_update/:id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'assign_course_delete/:id/:assigned_course_details_id',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'import_course_with_assign',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'import_session_flow',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getSessionText',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'import_slo',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request],
        },
        {
            route: 'generateSignedUrl',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digi_course/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const userModulePermissionRoutes = [
        {
            route: 'getUserPermissionModuleList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getModuleUsers',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getUserModuleExistingPermissions',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'createUserModulePermissions',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getSingleProgramCalendarDetails/:programId',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/userModulePermission/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const studentGroupGenderMergeRoutes = [
        {
            route: 'dashboard/:program/:institution',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'program_ic_list_dashboard',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_group_lists/:_id/:level/:batch/:course',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_global_search/:program/:institution/:academic_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_global_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'group_publish',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_publish',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'add_student_group',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'student_search/:academic_no/:id/:batch/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'import',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'group_list_filter/:_id/:level/:batch/:gender/:group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'setting_get/:id/:level/:batch',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'group_change',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'excess_count_change',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'data_check',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'sub_course_grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_grouped_list_filter/:_id/:level/:batch/:gender/:group',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_student_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'foundation_get_course_setting/:_id/:level/:batch/:_course_id/:gender',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_export/:_id/:level/:batch',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_group_export/:_id/:level/:batch/:course/:fyd_group/:gender/:delivery/:d_group_no',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_import',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'individual_data_check',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'individual_courses_list/:_id/:level/:batch/:gender',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'individual_course_grouped_list_filter/:_id/:level/:batch/:gender/:course',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'get_course_setting/:_id/:level/:batch/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'individual_course_group_setting',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get_student_courses_year_two/:academic_no/:_id/:level/:batch/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'add_student_to_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_setting_get/:_id/:level/:term',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_get_student/:academic_no/:_id/:level/:batch',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_bulk_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_get_student_course/:academic_no/:_id/:level/:batch/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_add_student_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_course_student_delete',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_get_clone_group/:_id/:batch/:level/:_course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'fyd_course_clone',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'individual_course_grouping',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'add_student_course',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'get_student/:academic_no/:id/:level/:batch/:course_id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/student_group_gender_merge/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));
    const formApproverRoutes = [
        {
            route: 'qualityAssuranceApproverList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'formApplicationList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'approverUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateApproverBy',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'levelRoleUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'approverCategoryList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'formInitiatorUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'addComment',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/formApprover/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const formInitiatorRoutes = [
        {
            route: 'qapcLogUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCreateForm',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'createNewForm',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateCreateNewForm',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'singleFormList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateFormStatus',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'settingTag',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'referenceDocumentAcademicYear',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'conclusionPhase',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'searchReferenceDocument',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'searchDocumentList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCategoryAndForm',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getTermAndAttempt',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'formProgramList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getIncorporateSection',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'createIncorporate',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getIncorporate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'todoMissed',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'selectedFormList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'publishedFormInitiator',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'toDoCreatedFormList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'categoryList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/formInitiator/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const userGlobalSearchRoutes = [
        {
            route: 'getUserDropDownList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getUserCourseDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/userGlobalSearch/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const programCalenderRoutes = [
        {
            route: 'landing/:program/:calendar',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'dashboard/:id',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/program_calendar/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const sessionReportRoutes = [
        {
            route: 'attendanceReport/:sessionDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'attendanceReportExport/:sessionDate',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/session-report/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const tagReportRoutes = [
        {
            route: 'getTagMasterSettings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'manageTagMasterGroupFamilySettings',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updatedTagMasterSettings',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updatedOverAllTagMasterSettings/:_id',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'deleteTagMasterSettings',
            method: httpMethods.delete,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/globalConfiguration/tagMaster/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const programCalendarCourseRoutes = [
        {
            route: 'course_list/:id/:batch/:level',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/program_calendar_course/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const programCalendarEventRoutes = [
        {
            route: 'list_event_date_filter/:id/:level_no/:course/:start/:end',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'course_event_insert',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'rotation_course_event_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'course_event_edit',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/program_calendar_event/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const sessionStatusRoute = [
        {
            route: 'getCourseSessionStatusDetail',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'updateCourseSessionStatusDetail/:sessionStatusId',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/session-status-management/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const q360Route = [
        {
            route: 'institutionCalendarList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getConfigureSetting',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'createCalendar',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'saveConfigure',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/qapcSetting_v2/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const q360CategoryRoute = [
        {
            route: 'getProgramList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'programDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCurriculum',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getInstitution',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'roleUserList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'termList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCategoryForm',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'categoryTypeForm',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getApprovalHierarchy',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'singleFormOccurrence',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'concludingPhase',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'formConfigureList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'createDuplicateForm',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'createGroups',
            method: httpMethods.post,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'updateCategoryForm',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'resetFormCourseSetting',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'unPublishedForm',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'editConfigure',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        // {
        //     route: 'createDuplicateForm',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        // },
        // {
        //     route: 'saveConfigure',
        //     method: httpMethods.post,
        //     applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        // },
    ].map((route) => ({
        route: `${versionRoute}/qapcCategoryForm_v2/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const calendarRoutes = [
        {
            route: 'dateBasedCalendarList',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getSingleSchedule',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/digiclass/calendar/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    const courseSettingRoutes = [
        {
            route: 'courseWise-facial-setting',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
        {
            route: 'getCourseWise-facial-settings',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCourseWise-userDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCourseWise-staffDetails',
            method: httpMethods.get,
            applicable: [encryptDecryptApplicable.response],
        },
        {
            route: 'getCourseWise-updateCourseWiseDeliveryType',
            method: httpMethods.put,
            applicable: [encryptDecryptApplicable.request, encryptDecryptApplicable.response],
        },
    ].map((route) => ({
        route: `${versionRoute}/courseSettings/${route.route}`,
        method: route.method,
        applicable: route.applicable,
    }));

    return [
        ...commonRouteWithoutParamsRoutes,
        ...selfRegisterRoutes,
        ...dcAdminUserRoutes,
        ...dcUserRoutes,
        ...dcRoutes,
        ...dcSessionRoutes,
        ...dcDashboardRoutes,
        ...dcScheduleMultiDeviceAttendanceRoutes,
        ...dcScheduleAttendanceRoutes,
        ...dcChatV2Routes,
        ...announcementRoutes,
        ...dcAnnouncementRoutes,
        ...dcCourseAdminRoutes,
        ...dcSurveyRoutes,
        ...digiProgramRoutes,
        ...lmsStudentSettingRoutes,
        ...lmsWarningRoutes,
        ...lmsStudentRoutes,
        ...lmsDenialRoutes,
        ...dcDocumentRoutes,
        ...announcementSettingRoutes,
        ...activityV2Routes,
        ...taxonomyRoutes,
        ...globalSessionRoutes,
        ...activityRoutes,
        ...dcDiscussionRoutes,
        ...programReportSettingRoutes,
        ...reportAnalyticsRoutes,
        ...institutionCalendarEventRouter,
        ...programCalendarReviewRoutes,
        ...dcAdminDashboardRoutes,
        ...studentGroupRoutes,
        ...lmsReviewRoutes,
        ...lmsAttendanceConfigRoutes,
        ...courseCoordinatorRoutes,
        ...extraCurricularAndBreakRoutes,
        ...courseScheduleRoutes,
        ...courseVersionRoutes,
        ...digiDepartmentSubjectRoutes,
        ...assignmentRoutes,
        ...assignmentAnswerRouters,
        ...assignmentCategoryRouters,
        ...assignmentCommentsRouters,
        ...assignmentGroupRouters,
        ...assignmentCourseRoutes,
        ...assignmentPromptRoutes,
        ...assignmentPromptAnswerRouters,
        ...assignmentReportRouters,
        ...assignmentRubricRouters,
        ...assignmentSettingRouters,
        ...assignmentProgramInputRouters,
        ...faceReRegisterRoutes,
        ...roleRoutes,
        ...roleAssignRoutes,
        ...digiCurriculumRoutes,
        ...digiSessionDeliveryTypeRoutes,
        ...digiCourseRoutes,
        ...digiFrameWorkRouter,
        ...infraStructureRoutes,
        ...lmsStaffRoutes,
        ...institutionCalendar,
        ...courseScheduleSettingRouter,
        ...digiCourseGroup,
        ...digiMappingRoutes,
        ...disciplinaryRemarks,
        ...sessionReport,
        ...userModulePermissionRoutes,
        ...studentGroupGenderMergeRoutes,
        ...formApproverRoutes,
        ...formInitiatorRoutes,
        ...userGlobalSearchRoutes,
        ...programCalenderRoutes,
        ...sessionReportRoutes,
        ...tagReportRoutes,
        ...programCalendarCourseRoutes,
        ...programCalendarEventRoutes,
        ...sessionStatusRoute,
        ...q360Route,
        ...q360CategoryRoute,
        ...calendarRoutes,
        ...courseSettingRoutes,
    ];
};

const isEncryptedRoute = ({ url, urlMethod, applicableTo }) => {
    return encryptedRoutes().some(({ route, method, applicable }) => {
        const routeRegex = new RegExp(
            `^${route.toLowerCase().replace(/:\w+/g, '[^/]+')}/?$`,
            // `^${route.toLowerCase().replace(/:\w+/g, '[^/]+')}(?:/[^/]+)*$`,
        );
        return (
            routeRegex.test(url.toLowerCase()) &&
            method === urlMethod &&
            applicable.find((applicableElement) => applicableElement === applicableTo)
        );
    });
};

const publicRoutes = {
    DC_ADMIN_LOGIN: '/user/authLogin',
    LOGIN: '/digiclass/user/authLogin',
    FORGOT_PASSWORD: '/digiclass/user/forget',
    FORGOT_PASSWORD_SEND_OTP: '/user/forget_send_otp',
    SIGNUP: '/user/signup',
    SELF_SIGNUP: '/userRegistration/verifyEmail',
    DEFAULT_ROUTES: 'default-routes',
    IP_ADDRESS: 'ip-address',
};

module.exports = { isEncryptedRoute, encryptedRoutes, encryptDecryptApplicable, publicRoutes };
