/**
 * Returning LMS Data
 * @returns {Promise<Assigned Role Get>}
 */

const { convertToMongoObjectId: ObjectId } = require('./common');

function lms_data() {
    const data = {
        name: 'Leave Management',
        _id: '5ff8355bbb30e4097a9cae97',
        pages: [
            {
                name: 'Approve Leave',
                _id: '5ff8355bbb30e42b5d9caef9',
                url: '',
                actions: [
                    {
                        name: 'View',
                        _id: '5ff8355bbb30e4b3289caefa',
                    },
                ],
                tabs: [
                    {
                        name: 'Permission',
                        _id: '5ff8355bbb30e441d29caefb',
                        url: '',
                        actions: [
                            {
                                name: 'View',
                                _id: '5ff8355bbb30e453729caefc',
                            },
                            {
                                name: 'Export',
                                _id: '5ff8355bbb30e4e35c9caefd',
                            },
                            {
                                name: 'Review Pending',
                                _id: '5ff8355bbb30e43b199caefe',
                            },
                            {
                                name: 'Reviewed',
                                _id: '5ff8355bbb30e4a1589caeff',
                            },
                            {
                                name: 'Rejected',
                                _id: '5ff8355bbb30e4425b9caf00',
                            },
                            {
                                name: 'Action',
                                _id: '5ff8355bbb30e4ff149caf01',
                            },
                            {
                                name: 'Profile View',
                                _id: '5ff8355bbb30e44fb49caf02',
                            },
                        ],
                        subTabs: [
                            {
                                name: 'Review',
                                _id: '5ff8355bbb30e4291e9caf03',
                                url: '',
                                actions: [
                                    {
                                        name: 'Reject',
                                        _id: '5ff8355bbb30e4e9a49caf04',
                                    },
                                    {
                                        name: 'Complete Review',
                                        _id: '5ff8355bbb30e449319caf05',
                                    },
                                    {
                                        name: 'Comment',
                                        _id: '5ff8355bbb30e4c5339caf06',
                                    },
                                    {
                                        name: 'Subsitute Staff',
                                        _id: '5ff8355bbb30e4d4199caf07',
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        name: 'Leave',
                        _id: '5ff8355bbb30e45d5d9caf09',
                        url: '',
                        actions: [
                            {
                                name: 'Export',
                                _id: '5ff8355bbb30e432499caf0a',
                            },
                            {
                                name: 'Review Pending',
                                _id: '5ff8355bbb30e45a559caf0b',
                            },
                            {
                                name: 'Reviewed',
                                _id: '5ff8355bbb30e470e99caf0c',
                            },
                            {
                                name: 'Rejected',
                                _id: '5ff8355bbb30e4f5919caf0d',
                            },
                            {
                                name: 'Action',
                                _id: '5ff8355bbb30e4bdd49caf0e',
                            },
                            {
                                name: 'Profile View',
                                _id: '5ff8355bbb30e4f7219caf0f',
                            },
                        ],
                        subTabs: [
                            {
                                name: 'Review',
                                _id: '5ff8355bbb30e41d679caf10',
                                url: '',
                                actions: [
                                    {
                                        name: 'Reject',
                                        _id: '5ff8355bbb30e440069caf11',
                                    },
                                    {
                                        name: 'Complete Review',
                                        _id: '5ff8355bbb30e4232f9caf12',
                                    },
                                    {
                                        name: 'Comment',
                                        _id: '5ff8355bbb30e497c49caf13',
                                    },
                                    {
                                        name: 'Subsitute Staff',
                                        _id: '5ff8355bbb30e4682f9caf14',
                                    },
                                    {
                                        name: 'Payment',
                                        _id: '5ff8355bbb30e49ec49caf15',
                                    },
                                ],
                            },
                        ],
                    },
                    {
                        name: 'On Duty',
                        _id: '5ff8355bbb30e478979caf16',
                        url: '',
                        actions: [
                            {
                                name: 'Export',
                                _id: '5ff8355bbb30e4f6a99caf17',
                            },
                            {
                                name: 'Review Pending',
                                _id: '5ff8355bbb30e4d9669caf18',
                            },
                            {
                                name: 'Reviewed',
                                _id: '5ff8355bbb30e40f279caf19',
                            },
                            {
                                name: 'Rejected',
                                _id: '5ff8355bbb30e421589caf1a',
                            },
                            {
                                name: 'Action',
                                _id: '5ff8355bbb30e4f0069caf1b',
                            },
                            {
                                name: 'Profile View',
                                _id: '5ff8355bbb30e4a5869caf1c',
                            },
                        ],
                        subTabs: [
                            {
                                name: 'Review',
                                _id: '5ff8355bbb30e4d9609caf1d',
                                url: '',
                                actions: [
                                    {
                                        name: 'Reject',
                                        _id: '5ff8355bbb30e46bc59caf1e',
                                    },
                                    {
                                        name: 'Complete Review',
                                        _id: '5ff8355bbb30e444139caf1f',
                                    },
                                    {
                                        name: 'Comment',
                                        _id: '5ff8355bbb30e468bb9caf20',
                                    },
                                    {
                                        name: 'Subsitute Staff',
                                        _id: '5ff8355bbb30e42d929caf21',
                                    },
                                    {
                                        name: 'Payment',
                                        _id: '5ff8355bbb30e4ffe29caf22',
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
            {
                _id: ObjectId('6005ae55c3cfcb1b141d0070'),
                name: 'Report Absence',
                url: '',
                actions: [
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0071'),
                        name: 'View',
                    },
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0072'),
                        name: 'Export',
                    },
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0073'),
                        name: 'Search',
                    },
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0074'),
                        name: 'Add',
                    },
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0075'),
                        name: 'Edit',
                    },
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0076'),
                        name: 'Profile View',
                    },
                    {
                        _id: ObjectId('6005ae55c3cfcb1b141d0077'),
                        name: 'Suspend',
                    },
                ],
                tabs: [],
            },
        ],
    };
    return data;
}
/**
 * Returning LMS Data
 * @returns {Promise<Assigned Role Get>}
 */
function course_coordinator() {
    const data = [
        {
            isDeleted: false,
            isActive: true,
            _id: '609663b4351a764414c8559d',
            name: 'Schedule Management',
            pages: [
                {
                    _id: '609663b4351a764414c855b4',
                    name: 'Course Scheduling',
                    url: '',
                    actions: [
                        {
                            _id: '609663b4351a764414c855b5',
                            name: 'List View',
                        },
                    ],
                    tabs: [
                        {
                            _id: '609663b4351a764414c855b6',
                            name: 'Schedule',
                            url: '',
                            actions: [
                                {
                                    _id: '609663b4351a764414c855b7',
                                    name: 'Course View',
                                },
                                {
                                    _id: '609663b4351a764414c855b8',
                                    name: 'Export',
                                },
                                {
                                    _id: '609663b4351a764414c855b9',
                                    name: 'Publish',
                                },
                            ],
                            subTabs: [
                                {
                                    _id: '609663b4351a764414c855ba',
                                    name: 'Course Schedule',
                                    url: '',
                                    actions: [
                                        {
                                            _id: '609663b4351a764414c855bb',
                                            name: 'View',
                                        },
                                        {
                                            _id: '609663b4351a764414c855bc',
                                            name: 'Add',
                                        },
                                        {
                                            _id: '609663b4351a764414c855bd',
                                            name: 'Edit',
                                        },
                                        {
                                            _id: '609663b4351a764414c855be',
                                            name: 'Delete',
                                        },
                                        {
                                            _id: '609663b4351a764414c855bf',
                                            name: 'Cancel/Re-Assign',
                                        },
                                        {
                                            _id: '609663b4351a764414c855c0',
                                            name: 'Merge',
                                        },
                                    ],
                                },
                                {
                                    _id: '609663b4351a764414c855c1',
                                    name: 'Course TimeTable',
                                    url: '',
                                    actions: [
                                        {
                                            _id: '609663b4351a764414c855c2',
                                            name: 'View',
                                        },
                                        {
                                            _id: '609663b4351a764414c855c3',
                                            name: 'Export',
                                        },
                                    ],
                                },
                                {
                                    _id: '609663b4351a764414c855c4',
                                    name: 'Manage Course',
                                    url: '',
                                    actions: [
                                        {
                                            _id: '609663b4351a764414c855c5',
                                            name: 'View',
                                        },
                                        {
                                            _id: '609663b4351a764414c855c6',
                                            name: 'Session Default Settings View',
                                        },
                                        {
                                            _id: '609663b4351a764414c855c7',
                                            name: 'Session Default Settings Add',
                                        },
                                        {
                                            _id: '609663b4351a764414c855c8',
                                            name: 'Session Default Settings Edit',
                                        },
                                        {
                                            _id: '609663b4351a764414c855c9',
                                            name: 'Session Default Settings Delete',
                                        },
                                        {
                                            _id: '609663b4351a764414c855ca',
                                            name: 'Manage Topic View',
                                        },
                                        {
                                            _id: '609663b4351a764414c855cb',
                                            name: 'Manage Topic Add',
                                        },
                                        {
                                            _id: '609663b4351a764414c855cc',
                                            name: 'Manage Topic Edit',
                                        },
                                        {
                                            _id: '609663b4351a764414c855cd',
                                            name: 'Manage Topic Delete',
                                        },
                                        {
                                            _id: '609663b4351a764414c855ce',
                                            name: 'Advance Settings View',
                                        },
                                        {
                                            _id: '609663b4351a764414c855cf',
                                            name: 'Advance Settings Edit',
                                        },
                                    ],
                                },
                            ],
                        },
                        {
                            _id: '609663b4351a764414c855d0',
                            name: 'TimeTable',
                            url: '',
                            actions: [
                                {
                                    _id: '609663b4351a764414c855d1',
                                    name: 'View',
                                },
                                {
                                    _id: '609663b4351a764414c855d2',
                                    name: 'Export',
                                },
                            ],
                        },
                    ],
                },
            ],
        },
        {
            _id: ObjectId('60e2ac0024f9cc325c4fa2e8'),
            isDeleted: false,
            isActive: true,
            name: 'Reports and Analytics',
            pages: [
                {
                    _id: ObjectId('60e2ac0024f9cc325c4fa301'),
                    name: 'Course Details',
                    tabs: [
                        {
                            _id: ObjectId('60e2ac0024f9cc325c4fa302'),
                            name: 'Overview',
                            actions: [
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa303'),
                                    name: 'View',
                                },
                            ],
                        },
                        {
                            _id: ObjectId('60e2ac0024f9cc325c4fa304'),
                            name: 'Attendance Log',
                            actions: [
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa305'),
                                    name: 'View',
                                },
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa306'),
                                    name: 'Export',
                                },
                            ],
                        },
                        {
                            _id: ObjectId('60e2ac0024f9cc325c4fa307'),
                            name: 'Session Status',
                            actions: [
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa308'),
                                    name: 'View',
                                },
                            ],
                        },
                        {
                            _id: ObjectId('60e2ac0024f9cc325c4fa309'),
                            name: 'Student Details',
                            actions: [
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa30a'),
                                    name: 'View',
                                },
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa30b'),
                                    name: 'Export',
                                },
                            ],
                        },
                        {
                            _id: ObjectId('60e2ac0024f9cc325c4fa30c'),
                            name: 'Staff Details',
                            actions: [
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa30d'),
                                    name: 'View',
                                },
                                {
                                    _id: ObjectId('60e2ac0024f9cc325c4fa30e'),
                                    name: 'Export',
                                },
                            ],
                        },
                    ],
                    actions: [
                        {
                            _id: ObjectId('60e2ac0024f9cc325c4fa2f0'),
                            name: 'Course View',
                        },
                    ],
                },
            ],
        },
    ];
    return data;
}

module.exports = {
    lms_data,
    course_coordinator,
};
