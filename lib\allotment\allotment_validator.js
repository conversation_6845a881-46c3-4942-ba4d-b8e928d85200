const Joi = require('joi');
const common_files = require('../utility/common');

exports.allotment = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            data: Joi.array().items(
                Joi.object().keys({
                    _department_id: Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    }),
                    _division_id: Joi.string().alphanum().length(24).error(error => {
                        return error;
                    }),
                    _admin_course_id: Joi.array().items(Joi.string().alphanum().length(24).required().error(error => {
                        return error;
                    })),
                    participation: Joi.array().items(
                        Joi.object().keys({
                            _subject_id: Joi.string().alphanum().length(24).required().error(error => {
                                return error;
                            }),
                            _participation_course_id: Joi.array().items(Joi.string().alphanum().length(24).allow('').error(error => {
                                return error;
                            })),
                        }).unknown(true)
                    )
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.allotment_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.allotment_year_level = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            year_level: Joi.string().alphanum().valid('year', 'level').required().error(error => {
                return error;
            }),
            input: Joi.number().min(1).max(16).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}