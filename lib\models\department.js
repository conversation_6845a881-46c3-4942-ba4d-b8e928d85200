let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let department = new Schema({
    department_title: {
        type: String,
        required: true
    },
    _division_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_DIVISIONS
    }],
    _subject_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SUBJECT
    }],
    /* _speciality_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SST
    }],
    _topic_id: [{
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SST
    }], */
    _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM,
        required: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.DEPARTMENT, department);