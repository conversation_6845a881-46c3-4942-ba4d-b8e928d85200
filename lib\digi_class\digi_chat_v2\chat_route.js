const router = require('express').Router();
const {
    createCourseChannels,
    createFileMessage,
    generateUrl,
    getUserChannelsList,
    userChannelMessage,
    groupInfo,
    getChannelsList,
    getUserChatCount,
    getUserList,
    createPrivateChannel,
    getChatProfile,
    deleteChat,
    forwardMessage,
} = require('./chat_controller');
const {
    createCourseChannelValidator,
    getChannelMsgValidator,
    getChannelsValidator,
    groupInfoValidator,
    getUserChatCountValidator,
    getChannelListValidator,
    getUserListValidator,
    createPrivateChannelValidator,
    getChatProfileValidator,
    deleteChatValidator,
} = require('./chat_validator');
const { multipleFileUpload } = require('../../utility/file_upload');
const multer = require('multer');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');

router.post(
    '/create-course-channels',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    createCourseChannelValidator,
    createCourseChannels,
);
router.post(
    '/get-channels',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getChannelsValidator,
    getUserChannelsList,
);
router.get(
    '/channel-msg',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getChannelMsgValidator,
    userChannelMessage,
);
router.get(
    '/group-info',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    groupInfoValidator,
    groupInfo,
);
router.post(
    '/sendMessageWithAttachment',
    (req, res, next) => {
        multipleFileUpload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send(`Multer Error in uploading,${err.toString()}`);
            }
            if (err) {
                console.log('AWS error', err);
                return res
                    .status(500)
                    .send(
                        response_function(
                            res,
                            500,
                            false,
                            'Please change file format and upload',
                            err.toString(),
                        ),
                    );
            }
            next();
        });
    },
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    createFileMessage,
);
router.get(
    '/generateSignedUrl',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    generateUrl,
);
//Phase1
router.get(
    '/getNewChatCount',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getUserChatCountValidator,
    getUserChatCount,
);
router.get(
    '/getChannelsList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getChannelListValidator,
    getChannelsList,
);
router.get(
    '/getUserList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getUserListValidator,
    getUserList,
);
router.post(
    '/createPrivateChannel',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    createPrivateChannelValidator,
    createPrivateChannel,
);
router.get(
    '/getChatProfile',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    getChatProfileValidator,
    getChatProfile,
);
router.delete(
    '/deleteChat/:channelId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    deleteChatValidator,
    deleteChat,
);
router.post(
    '/forwardMessage',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    /* createPrivateChannelValidator, */ forwardMessage,
);
module.exports = router;
