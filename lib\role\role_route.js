const express = require('express');
const route = express.Router();
const role = require('./role_controller');
const validator = require('./role_validator');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

route.get(
    '/role_list',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STAFF,
            'user_management:staff_management:registered:assign_role',
        ]),
    ],
    role.role_list,
);
route.get(
    '/listRoleBasedOnTheRoleAndPermission',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_view',
        ]),
    ],
    role.listRoleBasedOnTheRoleAndPermission,
);
route.get('/:id', validator.role_id, role.list_id);
route.get('/', [userPolicyAuthentication([defaultPolicy.DC_STAFF])], role.list);
route.post(
    '/',
    [
        userPolicyAuthentication([
            'roles_and_permissions:dashboard:edit',
            'roles_and_permissions:dashboard:add',
        ]),
    ],
    validator.role,
    role.insert,
);
route.put(
    '/:id',
    [
        userPolicyAuthentication([
            'roles_and_permissions:dashboard:edit',
            'roles_and_permissions:dashboard:module_edit',
        ]),
    ],
    validator.role_id,
    validator.role,
    role.update,
);
route.delete('/:id', validator.role_id, role.delete);

//role users
route.get(
    '/roleUser/:roleId',
    [userPolicyAuthentication(['roles_and_permissions:dashboard:view'])],
    role.listOfRoleUsers,
);

module.exports = route;
