const accreditationTypeSchema = require('./accreditation.model');
const { getModel } = require('../../utility/common');
const { ACCREDITATION_TYPE } = require('../../utility/constants');

const getAccreditation = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const accreditationModel = getModel(tenantURL, ACCREDITATION_TYPE, accreditationTypeSchema);
        const accreditation = await accreditationModel.find({}).sort({ name: 1 }).lean();
        return { statusCode: 200, data: accreditation };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAccreditation = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const { name } = body;
        const accreditationModel = getModel(tenantURL, ACCREDITATION_TYPE, accreditationTypeSchema);
        const accreditation = await accreditationModel.create({ name });
        return { statusCode: 200, data: accreditation };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editAccreditation = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const { id, name } = body;
        const accreditationModel = getModel(tenantURL, ACCREDITATION_TYPE, accreditationTypeSchema);
        const accreditation = await accreditationModel.findByIdAndUpdate(
            { _id: id },
            { name },
            { new: true },
        );
        return { statusCode: 200, data: accreditation };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteAccreditation = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const { id } = params;
        const accreditationModel = getModel(tenantURL, ACCREDITATION_TYPE, accreditationTypeSchema);
        const accreditation = await accreditationModel.findByIdAndDelete({ _id: id });
        return { statusCode: 200, data: accreditation };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = { getAccreditation, addAccreditation, editAccreditation, deleteAccreditation };
