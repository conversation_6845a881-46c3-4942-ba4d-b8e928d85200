const Joi = require('joi');

// update feedback schema
function updateCourseSessionSchema() {
    const schema = {
        body: Joi.object().keys({
            rating: Joi.string().optional(),
            comments: Joi.string().optional(),
        }),
        params: {
            scheduleId: Joi.string().length(24),
            studentId: Joi.string().length(24),
        },
    };
    return schema;
}

// pushStudent
function pushStudentSchema() {
    const schema = {
        body: Joi.object().keys({
            courseId: Joi.string().optional(),
            staffId: Joi.string().optional(),
            sessionId: Joi.string().optional(),
        }),
    };
    return schema;
}
// get courses by staff id
function getCoursesParamSchema() {
    const schema = {
        params: Joi.object().keys({
            staffId: Joi.string().length(24),
        }),
    };
    return schema;
}
module.exports = {
    updateCourseSessionSchema: updateCourseSessionSchema(),
    pushStudentSchema: pushStudentSchema(),
    getCoursesParamSchema: getCoursesParamSchema(),
};
