const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    addDepartment,
    getDepartment,
    getDepartments,
    addSubject,
    editDepartment,
    shareDepartment,
    shareSubjects,
    deleteDepartment,
    editSubject,
    deleteSubject,
    getDashboardStats,
    getProgramWiseDepts,
    courseDepartmentList,
    deliveringSubjectList,
    getInstituteDepartments,
    editDepartmentRestructure,
} = require('./department-subject.controller');
const {
    addDepartmentValidator,
    getDepartmentValidator,
    getDepartmentsValidator,
    addSubjectsValidator,
    shareSubjectsValidator,
    editDepartmentValidator,
    shareDepartmentValidator,
    deleteDepartmentValidator,
    editSubjectValidator,
    getProgramsDeptsValidator,
    deleteSubjectValidator,
    courseDepartmentListValidator,
    deliveringSubjectsValidator,
    instituteDepartmentsValidator,
    editDepartmentRestructureValidator,
} = require('./department-subject.validator');
const { validate } = require('../../utility/input-validation');

router.post('/add-department', validate(addDepartmentValidator), catchAsync(addDepartment));
router.get('/departments/:id', validate(getDepartmentsValidator), catchAsync(getDepartments));
router.get('/department/:id', validate(getDepartmentValidator), catchAsync(getDepartment));
router.post('/add-subjects/:id', validate(addSubjectsValidator), catchAsync(addSubject));
router.patch('/edit-department/:id', validate(editDepartmentValidator), catchAsync(editDepartment));
router.patch('/share-department', validate(shareDepartmentValidator), catchAsync(shareDepartment));
router.patch('/share-subjects', validate(shareSubjectsValidator), catchAsync(shareSubjects));
router.delete(
    '/delete-department/:id',
    validate(deleteDepartmentValidator),
    catchAsync(deleteDepartment),
);
router.patch('/edit-subject', validate(editSubjectValidator), catchAsync(editSubject));
router.delete('/delete-subject', validate(deleteSubjectValidator), catchAsync(deleteSubject));
router.get('/dashboard-stats/:id', catchAsync(getDashboardStats));
router.get(
    '/programs-and-departments/:id',
    validate(getProgramsDeptsValidator),
    catchAsync(getProgramWiseDepts),
);
router.get(
    '/course-departments/:_program_id/:_institution_id',
    validate(courseDepartmentListValidator),
    catchAsync(courseDepartmentList),
);
router.get(
    '/delivering-subject-list/independent-course/:id',
    validate(deliveringSubjectsValidator),
    catchAsync(deliveringSubjectList),
);
router.post(
    '/institute-departments/:id',
    validate(instituteDepartmentsValidator),
    catchAsync(getInstituteDepartments),
);
router.post(
    '/edit-institute-department/:id',
    validate(editDepartmentRestructureValidator),
    catchAsync(editDepartmentRestructure),
);

module.exports = router;
