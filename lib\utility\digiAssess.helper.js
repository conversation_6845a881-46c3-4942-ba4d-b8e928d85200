const digiAssessAxios = require('./digiAssess.axios');

const getConcatYearLevelCode = ({ yearLevelCode = '' }) => {
    const updatedYearLevelCode = yearLevelCode.replace(/\s+/g, '');
    return `${updatedYearLevelCode.charAt(0)}${updatedYearLevelCode.charAt(
        updatedYearLevelCode.length - 1,
    )}`.toUpperCase();
};
const getConstructedCurriculumName = ({ curriculumName = '' }) => {
    const splittedCurriculumName = curriculumName.split(' ').filter(Boolean);
    return splittedCurriculumName.length ? splittedCurriculumName[1] : '';
};

exports.getStudentGrade = async ({
    studentDetails = [],
    programId: { code = '' },
    term = '',
    curriculumName = '',
    yearNo = '',
    levelNo = '',
    courseId: { course_code = '' },
    institutionCalendarId: { start_date = '', end_date = '' },
}) => {
    const userAcademicId = studentDetails.map(({ user_id = '' }) => user_id);
    const payload = {
        academicNos: userAcademicId,
        academicYearStart: start_date ? new Date(start_date).toISOString().split('-')[0] : '',
        academicYearEnd: end_date ? new Date(end_date).toISOString().split('-')[0] : '',
        programCode: code,
        termCode: `${term.charAt(0)}T`.toUpperCase(),
        yearCode: getConcatYearLevelCode({ yearLevelCode: yearNo }),
        levelCode: getConcatYearLevelCode({ yearLevelCode: levelNo }),
        curriculumCode: getConstructedCurriculumName({ curriculumName }),
        courseCode: course_code.replace(/\s+/g, ''),
    };
    const studentGradeDetails = await digiAssessAxios
        .post('/student/course-grade', payload)
        .then((gradeDetails) => gradeDetails?.data?.data?.results || [])
        .catch((error) => {
            console.error('Error in fetching student grade ->', error);
            return [];
        });
    return studentGradeDetails;
};
