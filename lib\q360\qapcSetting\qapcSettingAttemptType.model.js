const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { QAPC_SETTING_ATTEMPT_TYPE, INSTITUTION } = require('../../utility/constants');

const qapcSettingAttemptTypeSchema = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        name: { type: String },
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_SETTING_ATTEMPT_TYPE, qapcSettingAttemptTypeSchema);
