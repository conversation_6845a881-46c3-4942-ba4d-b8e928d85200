const Joi = require('joi');
const { comResponse } = require('../../utility/common');

exports.getStatesValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                countryId: Joi.number()
                    .integer()
                    .required()
                    .error((error) => error),
            })
            .unknown(true),
    })
    .unknown(true);
