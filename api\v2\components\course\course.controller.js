const {
    PREREQUISITE,
    COREQUISITE,
    COURSE_TYPE_GET,
    COURSE,
    INSTITUTION,
    COURSE_TYPE,
    DRAFTED,
    ADDED,
    SESSION_ORDER,
    CURRICULUM,
    DS_COURSE_GROUP,
    SESSION_DELIVERY_TYPES,
    PROGRAM,
    TOGGLE_CREDIT_HOURS,
    TOGGLE_PERIOD_WEEK,
    TOGGLE_DELIVERY_DURATION,
} = require('../../utility/constants');
const institutionSchema = require('../institution/institution.model');
const courseSchema = require('./course.model');
const programSchema = require('../program-input/program-input.model');
const { getsignedUrl } = require('./course.util');
const curriculumSchema = require('../curriculum/curriculum.model');
const { convertToMongoObjectId, getModel } = require('../../utility/common');
const { getPaginationValues } = require('../../utility/pagination');
const sessionOrderSchema = require('../session-order/session-order.model');
const courseGroupSchema = require('../course-group/course-group.model');
const sessionDeliverySchema = require('../session-delivery-types/session-delivery-types.model');

const courseAdd = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const {
            _institution_id,
            _curriculum_id,
            _program_id,
            courseCode,
            courseName,
            courseType,
            administration,
            participating,
            courseEditor,
        } = body;
        const institutionCheck = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!institutionCheck)
            return {
                statusCode: 410,
                message: 'INSTITUTION_NOT_FOUND',
            };
        const courseNameCheck = await courseModel
            .find(
                {
                    _curriculum_id: convertToMongoObjectId(_curriculum_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseName: 1,
                },
            )
            .lean();
        const courseCodeCheck = await courseModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseCode: 1,
                },
            )
            .lean();
        let isDuplicateCourseName = false;
        let isDuplicateCourseCode = false;
        if (courseNameCheck.length) {
            courseNameCheck.forEach((course) => {
                if (course.courseName.toLowerCase() === courseName.toLowerCase()) {
                    isDuplicateCourseName = true;
                }
            });
        }
        if (courseCodeCheck.length) {
            courseCodeCheck.forEach((course) => {
                if (course.courseCode.toLowerCase() === courseCode.toLowerCase()) {
                    isDuplicateCourseCode = true;
                }
            });
        }
        if (isDuplicateCourseName) {
            return { statusCode: 404, message: 'DUPLICATE_COURSE_NAME' };
        }
        if (isDuplicateCourseCode) {
            return { statusCode: 404, message: 'DUPLICATE_COURSE_CODE' };
        }
        const createCourse = await courseModel.create({
            _institution_id,
            _curriculum_id,
            _program_id,
            courseType,
            courseCode,
            courseName,
            administration,
            participating,
            courseEditor,
        });
        if (createCourse)
            return {
                statusCode: 201,
                message: 'DS_CREATED',
                data: { _course_id: createCourse._id },
            };
        return {
            statusCode: 500,
            message: 'DS_CREATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseConfigAdd = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const {
            _course_id,
            _curriculum_id,
            isPhaseFlowWithOutLevel,
            isYearLongCourse,
            courseCode,
            courseName,
            courseOccurringYearWise,
            courseRecurringYearWise,
            courseOccurring,
            courseRecurring,
            allowEditing,
            sessionDeliveryType,
            draft,
            preRequisiteCourses,
            coRequisiteCourses,
            courseAssignedDetails,
            achieveTarget,
            duration,
            courseType,
            participating,
            courseEditor,
            administration,
        } = body;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseOccurring: 1,
                    courseRecurring: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    _institution_id: 1,
                    courseAssignedDetails: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const updateQuery = {
            courseCode,
            courseName,
            courseOccurring,
            courseRecurring,
            courseOccurringYearWise,
            courseRecurringYearWise,
            allowEditing,
            sessionDeliveryType,
            preRequisiteCourses,
            coRequisiteCourses,
            isPhaseFlowWithOutLevel,
            isYearLongCourse,
            achieveTarget,
            duration,
            isActive: true,
            isConfigured: true,
            courseType,
            participating,
            courseEditor,
            administration,
        };
        if (courseCode || courseName) {
            const courseNameCheck = await courseModel
                .find(
                    {
                        _id: { $ne: convertToMongoObjectId(_course_id) },
                        _curriculum_id: convertToMongoObjectId(_curriculum_id),
                        _institution_id: convertToMongoObjectId(courseCheck._institution_id),
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        courseCode: 1,
                        courseName: 1,
                    },
                )
                .lean();
            const courseCodeCheck = await courseModel
                .find(
                    {
                        _id: { $ne: convertToMongoObjectId(_course_id) },
                        _institution_id: convertToMongoObjectId(courseCheck._institution_id),
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        courseCode: 1,
                        courseName: 1,
                    },
                )
                .lean();

            let isDuplicateCourseName = false;
            let isDuplicateCourseCode = false;
            if (courseNameCheck.length) {
                courseNameCheck.forEach((course) => {
                    if (course.courseName.toLowerCase() === courseName.toLowerCase()) {
                        isDuplicateCourseName = true;
                    }
                });
            }
            if (courseCodeCheck.length) {
                courseCodeCheck.forEach((course) => {
                    if (course.courseCode.toLowerCase() === courseCode.toLowerCase()) {
                        isDuplicateCourseCode = true;
                    }
                });
            }
            if (courseName && isDuplicateCourseName) {
                return { statusCode: 404, message: 'DUPLICATE_COURSE_NAME' };
            }
            if (courseCode && isDuplicateCourseCode) {
                return { statusCode: 404, message: 'DUPLICATE_COURSE_CODE' };
            }
        }
        const courseAssigningDetails = courseCheck.courseAssignedDetails;
        if (draft) updateQuery.isActive = false;
        const occurringLevelIds = [];
        if (courseOccurring && courseOccurring.length) {
            courseOccurring.forEach((val) => {
                occurringLevelIds.push(val._level_id.toString());
            });
            if (courseAssigningDetails && courseAssigningDetails.length)
                courseAssigningDetails.forEach((assign, index) => {
                    if (!occurringLevelIds.includes(assign._level_id.toString())) {
                        courseAssigningDetails.splice(index, 1);
                    }
                });
        }
        const recurringLevelIds = [];
        if (courseRecurring && courseRecurring.length) {
            courseRecurring.forEach((val) => {
                recurringLevelIds.push(val._level_id.toString());
            });
            if (courseAssigningDetails && courseAssigningDetails.length)
                courseAssigningDetails.forEach((assign, index) => {
                    if (!recurringLevelIds.includes(assign._level_id.toString())) {
                        courseAssigningDetails.splice(index, 1);
                    }
                });
        }
        updateQuery.courseAssignedDetails = courseAssigningDetails;
        //for Selective Course Remove Option Start
        const existingLevelLists = courseCheck.courseOccurring.concat(courseCheck.courseRecurring);
        const existingLevelIds = existingLevelLists.map((element) => element._level_id.toString());

        const requestedLevelLists = courseOccurring.concat(courseRecurring);
        const requestedLevelIds = requestedLevelLists.map((element) =>
            element._level_id.toString(),
        );

        const levelIdDifference = existingLevelIds.filter(
            (element) => !requestedLevelIds.includes(element),
        );

        const levelIds = levelIdDifference.map((element) => convertToMongoObjectId(element));
        const selectiveCheck = await curriculumModel
            .findOne(
                {
                    _id: convertToMongoObjectId(courseCheck._curriculum_id),
                    'selectiveCourseConfiguration._level_id': { $in: levelIds },
                    'selectiveCourseConfiguration._program_id': convertToMongoObjectId(
                        courseCheck._program_id,
                    ),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    selectiveCourseConfiguration: 1,
                },
            )
            .lean();
        const levels = levelIds.map((element) => element.toString());
        if (selectiveCheck && selectiveCheck.selectiveCourseConfiguration) {
            const selectiveLists = selectiveCheck.selectiveCourseConfiguration.filter(
                (element) =>
                    levels.includes(element._level_id.toString()) &&
                    element._program_id.toString() === courseCheck._program_id.toString(),
            );
            const selectiveIds = selectiveLists.map((element) => element._id);
            const selectiveDeletion = await curriculumModel.updateMany(
                {
                    _id: convertToMongoObjectId(courseCheck._curriculum_id),
                },
                {
                    $pull: {
                        selectiveCourseConfiguration: {
                            _id: { $in: selectiveIds },
                        },
                    },
                },
            );
        }
        //for Selective Course Remove Option End

        const courseUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            updateQuery,
        );
        if (courseUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const portfolioAdd = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, file, title, description } = body;

        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const courseUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            {
                $push: {
                    portfolio: {
                        url: file,
                        title,
                        description,
                    },
                },
            },
        );
        if (courseUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const portfolioDelete = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, _portfolio_id } = params;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };

        const portfolioDeletion = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            {
                $pull: {
                    portfolio: {
                        _id: convertToMongoObjectId(_portfolio_id),
                    },
                },
            },
        );
        if (portfolioDeletion)
            return {
                statusCode: 200,
                message: 'DS_DELETED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
//todo: portfolio update
const portfolioUpdate = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { file, title, description } = body;
        const { _course_id, _portfolio_id } = params;

        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_LIST',
            };

        const portfolioQuery = {
            _id: convertToMongoObjectId(_course_id),
            isDeleted: false,
        };
        const portfolioobject = {
            $set: {
                'portfolio.$[i].url': file,
                'portfolio.$[i].title': title,
                'portfolio.$[i].description': description,
            },
        };
        const portfoliofilter = {
            arrayFilters: [
                {
                    'i._id': _portfolio_id,
                },
            ],
        };
        const portfolioUpdation = await courseModel.updateOne(
            portfolioQuery,
            portfolioobject,
            portfoliofilter,
        );
        if (portfolioUpdation)
            return {
                statusCode: 200,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
//todo: portfolio get
const portfolioGet = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id } = params;

        const portfolioQuery = {
            _id: convertToMongoObjectId(_course_id),
            isDeleted: false,
        };

        const courseResult = await courseModel
            .findOne(portfolioQuery, {
                portfolio: 1,
            })
            .lean();
        if (courseResult && courseResult.portfolio && courseResult.portfolio.length > 0) {
            const portfolioLists = courseResult.portfolio.map(async (element) => {
                const portfolioObject = {
                    _id: element._id,
                    title: element.title,
                    url: element.url,
                    description: element.description,
                };
                if (element.url) {
                    portfolioObject.presignedMediaURL = await getsignedUrl(element.url);
                }
                return portfolioObject;
            });
            courseResult.portfolio = await Promise.all(portfolioLists);
        }
        if (courseResult.portfolio && courseResult.portfolio.length > 0)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: courseResult.portfolio,
            };
        return {
            statusCode: 200,
            message: 'DS_NOT_FOUND',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo: portfolio get
const portfolioIdGet = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, _portfolio_id } = params;

        const portfolioQuery = {
            _id: convertToMongoObjectId(_course_id),
            'portfolio._id': convertToMongoObjectId(_portfolio_id),
            isDeleted: false,
        };

        const portfolio = await courseModel
            .findOne(portfolioQuery, {
                portfolio: 1,
            })
            .lean();
        if (portfolio && portfolio.portfolio && portfolio.portfolio.length > 0) {
            const portfolioData = portfolio.portfolio.find(
                (element) => element._id.toString() === _portfolio_id.toString(),
            );

            if (portfolioData) {
                return {
                    statusCode: 200,
                    data: {
                        _id: portfolioData._id,
                        title: portfolioData.title,
                        url: await getsignedUrl(portfolioData.url),
                        description: portfolioData.description,
                    },
                };
            }
        }
        return {
            statusCode: 200,
            message: 'DS_NO_DATA_FOUND',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo: requisites update
const requisiteCoursesAdd = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { courses } = body;
        const { _course_id, type } = params;

        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_LIST',
            };
        let requisitesCourseObject;
        if (COREQUISITE === type)
            requisitesCourseObject = {
                coRequisiteCourses: courses,
            };

        if (PREREQUISITE === type)
            requisitesCourseObject = {
                preRequisiteCourses: courses,
            };

        const requisitesCourseQuery = {
            _id: convertToMongoObjectId(_course_id),
            isDeleted: false,
        };
        const portfolioUpdation = await courseModel.updateOne(
            requisitesCourseQuery,
            requisitesCourseObject,
        );
        if (portfolioUpdation)
            return {
                statusCode: 200,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addCourseTheme = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, courseId, themes } = body;
        const courseThemeDoc = await courseModel.findOneAndUpdate(
            { _id: courseId, _institution_id },
            { $set: { themes } },
            { new: true },
        );
        if (!courseThemeDoc)
            return {
                statusCode: 404,
                message: 'UNABLE_TO_ADD_COURSE_THEME',
            };
        return {
            statusCode: 201,
            message: 'COURSE_THEME_ADDED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listCourseTheme = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, courseId } = params;
        const courseThemeDoc = await courseModel
            .findOne({
                _id: courseId,
                _institution_id,
            })
            .select('themes')
            .lean();
        if (!courseThemeDoc)
            return {
                statusCode: 404,
                message: 'UNABLE_TO_LIST_COURSE_THEMES',
            };
        return {
            statusCode: 201,
            message: 'COURSE_THEMES_LIST',
            data: courseThemeDoc,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseTheme = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, courseId, themeId } = params;
        const courseThemeDoc = await courseModel
            .findOne(
                {
                    _id: courseId,
                    _institution_id,
                },
                {
                    themes: {
                        $elemMatch: {
                            _id: themeId,
                        },
                    },
                },
            )
            .lean();
        if (!courseThemeDoc)
            return {
                statusCode: 404,
                message: 'UNABLE_TO_GET_COURSE_THEME',
            };
        return {
            statusCode: 201,
            message: 'COURSE_THEME',
            data: courseThemeDoc,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getSubCourseTheme = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, courseId, subThemeId } = params;
        const courseThemeDoc = await courseModel.findOne(
            {
                _id: courseId,
                _institution_id,
            },
            {
                themes: {
                    $elemMatch: {
                        'subThemes._id': subThemeId,
                    },
                },
            },
        );
        if (!courseThemeDoc)
            return {
                statusCode: 404,
                message: 'UNABLE_TO_GET_SUB_COURSE_THEME',
            };
        return {
            statusCode: 201,
            message: 'SUB_COURSE_THEME',
            data: courseThemeDoc,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteCourseTheme = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, courseId, themeId } = params;
        const courseThemeDoc = await courseModel.findOneAndUpdate(
            { _id: courseId, _institution_id },
            { $pull: { themes: { _id: convertToMongoObjectId(themeId) } } },
            { new: true },
        );
        if (!courseThemeDoc)
            return {
                statusCode: 404,
                message: 'UNABLE_TO_DELETE_COURSE_THEME',
            };
        return {
            statusCode: 201,
            message: 'COURSE_THEME_DELETED_SUCCESSFULLY',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
//todo: requisites update
const courseGet = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { _course_id, type } = params;
        let courseProject = {};
        if (type === COURSE_TYPE_GET.COURSE) {
            courseProject = {
                _id: 1,
                courseCode: 1,
                courseName: 1,
                courseType: 1,
                administration: 1,
                participating: 1,
                courseEditor: 1,
                _institution_id: 1,
            };
        }
        if (type === COURSE_TYPE_GET.COURSE_CONFIG) {
            courseProject = {
                isDeleted: 0,
                createdAt: 0,
                updatedAt: 0,
                __v: 0,
            };
        }
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                courseProject,
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_COURSE',
            };
        const findQuery = {
            _institution_id: courseCheck._institution_id,
            _course_id,
            isDeleted: false,
        };
        const sessionDocs = await sessionOrderModel
            .find(findQuery, {
                deliveryTopic: 1,
                _deliveryType_id: 1,
                sNo: 1,
                deliveryNumber: 1,
                deliverySymbol: 1,
                theme: 1,
            })
            .sort({ sNo: 1 })
            .lean();
        if (courseCheck && courseCheck.portfolio && courseCheck.portfolio.length > 0) {
            const portfolioLists = courseCheck.portfolio.map(async (element) => {
                const portfolioObject = {
                    _id: element._id,
                    title: element.title,
                    url: element.url,
                    description: element.description,
                };
                if (element.url) {
                    portfolioObject.presignedMediaURL = await getsignedUrl(element.url);
                }
                return portfolioObject;
            });
            courseCheck.portfolio = await Promise.all(portfolioLists);
        }
        if (courseCheck && courseCheck.courseOccurring && courseCheck.courseOccurring.length > 0) {
            courseCheck.courseOccurring.forEach((courseOccur, index) => {
                if (courseCheck.courseAssignedDetails.length) {
                    const courseAssignedData = courseCheck.courseAssignedDetails.find(
                        (element) =>
                            element._level_id.toString() === courseOccur._level_id.toString(),
                    );
                    if (courseAssignedData) {
                        courseCheck.courseOccurring[index].isConfigured = true;
                    }
                } else {
                    courseCheck.courseOccurring[index].isConfigured = false;
                }
            });
        }
        if (courseCheck && courseCheck.courseRecurring && courseCheck.courseRecurring.length > 0) {
            courseCheck.courseRecurring.forEach((courseRecur, index) => {
                if (courseCheck.courseAssignedDetails.length) {
                    const courseAssignedData = courseCheck.courseAssignedDetails.find(
                        (element) =>
                            element._level_id.toString() === courseRecur._level_id.toString(),
                    );
                    if (courseAssignedData) {
                        courseCheck.courseRecurring[index].isConfigured = true;
                    }
                } else {
                    courseCheck.courseRecurring[index].isConfigured = false;
                }
            });
        }
        const themeList = [];
        if (courseCheck.themes && courseCheck.themes.length) {
            for (courseThemeList of courseCheck.themes) {
                themeList.push({
                    _id: courseThemeList._id,
                    color: courseThemeList.color,
                    name: courseThemeList.name,
                    symbol: courseThemeList.symbol,
                    subThemes: courseThemeList.subThemes,
                });
                if (courseThemeList.subThemes && courseThemeList.subThemes.length > 0) {
                    for (courseSubThemeList of courseThemeList.subThemes) {
                        themeList.push({
                            _id: courseSubThemeList._id,
                            color: courseSubThemeList.color,
                            name: courseSubThemeList.name,
                            symbol: courseSubThemeList.symbol,
                        });
                    }
                }
            }
        }
        courseCheck.linkSessions.map((linkSession) => {
            linkSession.sessionOrder.map((sessionOrder) => {
                const sessions = sessionOrder;
                const sessionDetails = sessionDocs.find(
                    (elementEntry) =>
                        elementEntry._id.toString() === sessions._session_id.toString(),
                );
                if (sessionDetails) {
                    if (sessionDetails.theme) {
                        sessions._theme_id = sessionDetails.theme;
                        const themeDetails = themeList.find(
                            (elementEntry) =>
                                elementEntry._id.toString() === sessions._theme_id.toString(),
                        );
                        sessions.themeDetails = themeDetails;
                    }
                    if (sessionDetails.deliveryTopic)
                        sessions.deliveryTopic = sessionDetails.deliveryTopic;
                    if (sessionDetails.sNo) sessions.sNO = sessionDetails.sNo;
                    if (sessionDetails.deliverySymbol)
                        sessions.deliverySymbol = sessionDetails.deliverySymbol;
                    if (sessionDetails.deliveryNumber)
                        sessions.deliveryNo = sessionDetails.deliveryNumber;
                }
                return sessions;
            });
        });
        if (courseCheck)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: courseCheck,
            };
        return {
            statusCode: 500,
            message: 'FAILED_TO_UPDATE',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo: get courses by using curriculam Id
const courseCurriculamWiseGet = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _curriculum_id } = params;
        const courseProject = {
            _id: 1,
            courseCode: 1,
            courseName: 1,
            courseType: 1,
            administration: 1,
            participating: 1,
            courseEditor: 1,
            isActive: 1,
            'courseSharedWith.programName': 1,
            'courseSharedWith.curriculumName': 1,
            'courseSharedWith._curriculum_id': 1,
            'courseSharedWith.year': 1,
            'courseSharedWith.levelNo': 1,
            isConfigured: 1,
            courseRecurring: 1,
            courseOccurring: 1,
            courseAssignedDetails: 1,
        };
        const courseCheck = await courseModel
            .find(
                {
                    $or: [
                        { _curriculum_id: convertToMongoObjectId(_curriculum_id) },
                        {
                            'courseAssignedDetails._curriculum_id':
                                convertToMongoObjectId(_curriculum_id),
                        },
                        {
                            'courseAssignedDetails.courseSharedWith._curriculum_id':
                                convertToMongoObjectId(_curriculum_id),
                            isActive: true,
                        },
                    ],
                    isDeleted: false,
                },
                courseProject,
            )
            .sort({ _id: -1 })
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_COURSE',
            };
        courseCheck.forEach((courseDoc, courseIndex) => {
            if (courseDoc && courseDoc.courseOccurring && courseDoc.courseOccurring.length > 0) {
                courseDoc.courseOccurring.forEach((courseOccur, index) => {
                    if (courseDoc.courseAssignedDetails && courseDoc.courseAssignedDetails.length) {
                        const courseAssignedData = courseDoc.courseAssignedDetails.find(
                            (element) =>
                                element._level_id.toString() === courseOccur._level_id.toString(),
                        );
                        if (courseAssignedData) {
                            courseCheck[courseIndex].courseOccurring[index].isConfigured = true;
                        }
                    } else {
                        courseCheck[courseIndex].courseOccurring[index].isConfigured = false;
                    }
                });
            }
            if (courseDoc && courseDoc.courseRecurring && courseDoc.courseRecurring.length > 0) {
                courseDoc.courseRecurring.forEach((courseRecur, index) => {
                    if (courseDoc.courseAssignedDetails && courseDoc.courseAssignedDetails.length) {
                        const courseAssignedData = courseDoc.courseAssignedDetails.find(
                            (element) =>
                                element._level_id.toString() === courseRecur._level_id.toString(),
                        );
                        if (courseAssignedData) {
                            courseCheck[courseIndex].courseRecurring[index].isConfigured = true;
                        }
                    } else {
                        courseCheck[courseIndex].courseRecurring[index].isConfigured = false;
                    }
                });
            }
            if (courseDoc.courseAssignedDetails && courseDoc.courseAssignedDetails.length) {
                courseDoc.courseAssignedDetails.forEach((courseAssignedElement, assignIndex) => {
                    const sharedWith = courseAssignedElement.courseSharedWith.find(
                        (sharedElement) =>
                            sharedElement._curriculum_id.toString() === _curriculum_id,
                    );
                    if (sharedWith) {
                        if (courseDoc.courseType === COURSE_TYPE.INDEPENDENT) {
                            courseCheck[courseIndex].courseShared = true;
                            courseCheck[courseIndex].courseSharedFrom = COURSE_TYPE.INDEPENDENT;
                        } else {
                            const sharedCourse = { ...courseCheck[courseIndex] };
                            sharedCourse.courseShared = true;
                            sharedCourse.courseSharedFrom = courseAssignedElement.programName;
                            courseCheck.push(sharedCourse);
                        }
                    }
                });
            }
        });

        if (courseCheck)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: courseCheck,
            };
        return {
            statusCode: 500,
            message: 'FAILED_TO_GET_LIST',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const linkSessions = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, id, sessionOrder } = body;
        const findQuery = {
            _id: id,
            _institution_id,
            isDeleted: false,
        };
        const sessionsOrders = [];
        sessionOrder.forEach((session, index) => {
            sessionsOrders.push({
                _session_id: convertToMongoObjectId(session._session_id),
            });
        });
        const linkSession = await courseModel
            .findOneAndUpdate(
                findQuery,
                { $addToSet: { linkSessions: { sessionOrder: sessionsOrders } } },
                { new: true },
            )
            .select('linkSessions courseName');
        if (!linkSession)
            return {
                statusCode: 400,
                message: 'FAILED_TO_LINK_SESSIONS',
            };
        return {
            statusCode: 200,
            message: 'LINKED_SESSIONS',
            data: linkSession,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editLinkSession = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { id, linkedSessionId, _institution_id } = params;
        const { sessionOrder } = body;
        const findQuery = {
            _id: id,
            _institution_id,
            isDeleted: false,
        };
        const sessionsOrders = [];
        sessionOrder.forEach((session, index) => {
            sessionsOrders.push({
                _session_id: convertToMongoObjectId(session._session_id),
            });
        });
        const linkSession = await courseModel
            .findOneAndUpdate(
                findQuery,
                {
                    'linkSessions.$[linkSession].sessionOrder': sessionsOrders,
                },
                {
                    arrayFilters: [
                        {
                            'linkSession._id': convertToMongoObjectId(linkedSessionId),
                        },
                    ],
                    new: true,
                },
            )
            .select('linkSessions courseName');
        if (!linkSession)
            return {
                statusCode: 400,
                message: 'FAILED_TO_UPDATE_LINK_SESSIONS',
            };
        return {
            statusCode: 200,
            message: 'LINK_SESSIONS_UPDATED',
            data: linkSession,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUnlinkedSessions = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { _course_id, _institution_id } = params;
        const findQuery = {
            _institution_id,
            _course_id,
            isDeleted: false,
        };
        const courseDoc = await courseModel
            .findOne({
                _institution_id,
                _id: _course_id,
                isDeleted: false,
            })
            .lean()
            .select('linkSessions themes');
        let sessionDoc = await sessionOrderModel
            .find(findQuery)
            .sort({ sNo: 1 })
            .lean()
            .select(
                '_course_id sNo deliveryNumber deliverySymbol theme deliveryTopic _deliveryType_id',
            );
        if (!sessionDoc) {
            return {
                statusCode: 404,
                message: 'NO_SESSIONS',
            };
        }
        const themeList = [];
        if (courseDoc.themes.length) {
            for (courseThemeList of courseDoc.themes) {
                themeList.push({
                    _id: courseThemeList._id,
                    color: courseThemeList.color,
                    name: courseThemeList.name,
                    symbol: courseThemeList.symbol,
                    subThemes: courseThemeList.subThemes,
                });
                if (courseThemeList.subThemes && courseThemeList.subThemes.length > 0) {
                    for (courseSubThemeList of courseThemeList.subThemes) {
                        themeList.push({
                            _id: courseSubThemeList._id,
                            color: courseSubThemeList.color,
                            name: courseSubThemeList.name,
                            symbol: courseSubThemeList.symbol,
                        });
                    }
                }
            }
        }
        const linkedSessionIds = [];
        if (courseDoc.linkSessions)
            for (const linkSession of courseDoc.linkSessions) {
                for (const sessionOrder of linkSession.sessionOrder) {
                    linkedSessionIds.push(sessionOrder._session_id.toString());
                }
            }
        sessionDoc.forEach((value, index) => {
            if (linkedSessionIds.includes(value._id.toString())) {
                sessionDoc[index].isLinked = true;
            } else {
                sessionDoc[index].isLinked = false;
            }
            sessionDoc[index].sNo = value.sNo;
            sessionDoc[index].deliverySymbol = value.deliverySymbol;
            sessionDoc[index].deliveryNo = value.deliveryNumber;
        });
        sessionDoc = sessionDoc.map((element) => {
            const sessionDetails = element;
            if (element.theme) {
                const themeDetails = themeList.find(
                    (elementEntry) => elementEntry._id.toString() === element.theme.toString(),
                );
                sessionDetails.themeDetails = themeDetails;
            }

            return sessionDetails;
        });

        return {
            statusCode: 200,
            message: 'LIST_UNLINKED_SESSIONS',
            data: sessionDoc,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getlinkedSessions = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, _institution_id } = params;
        const findQuery = {
            _institution_id,
            _course_id,
            isDeleted: false,
        };
        const sessionDocs = await sessionOrderModel
            .find(findQuery, {
                deliveryTopic: 1,
                _deliveryType_id: 1,
                sNo: 1,
                deliveryNumber: 1,
                deliverySymbol: 1,
                theme: 1,
            })
            .sort({ sNo: 1 })
            .lean();
        const courseDoc = await courseModel
            .findOne({
                _institution_id,
                _id: _course_id,
                isDeleted: false,
            })
            .lean()
            .select('linkSessions themes');
        const themeList = [];
        if (courseDoc.themes.length) {
            for (courseThemeList of courseDoc.themes) {
                themeList.push({
                    _id: courseThemeList._id,
                    color: courseThemeList.color,
                    name: courseThemeList.name,
                    symbol: courseThemeList.symbol,
                    subThemes: courseThemeList.subThemes,
                });
                if (courseThemeList.subThemes && courseThemeList.subThemes.length > 0) {
                    for (courseSubThemeList of courseThemeList.subThemes) {
                        themeList.push({
                            _id: courseSubThemeList._id,
                            color: courseSubThemeList.color,
                            name: courseSubThemeList.name,
                            symbol: courseSubThemeList.symbol,
                        });
                    }
                }
            }
        }
        courseDoc.linkSessions.map((linkSession) => {
            linkSession.sessionOrder.map((sessionOrder) => {
                const sessions = sessionOrder;
                const sessionDetails = sessionDocs.find(
                    (elementEntry) =>
                        elementEntry._id.toString() === sessions._session_id.toString(),
                );
                if (sessionDetails) {
                    if (sessionDetails.theme) {
                        sessions._theme_id = sessionDetails.theme;
                        const themeDetails = themeList.find(
                            (elementEntry) =>
                                elementEntry._id.toString() === sessions._theme_id.toString(),
                        );
                        sessions.themeDetails = themeDetails;
                    }
                    if (sessionDetails.deliveryTopic)
                        sessions.deliveryTopic = sessionDetails.deliveryTopic;
                    if (sessionDetails.sNo) sessions.sNo = sessionDetails.sNo;
                    if (sessionDetails.deliverySymbol)
                        sessions.deliverySymbol = sessionDetails.deliverySymbol;
                    if (sessionDetails.deliveryNumber)
                        sessions.deliveryNo = sessionDetails.deliveryNumber;
                }
                return sessions;
            });
        });
        return {
            statusCode: 200,
            message: 'LIST_LINKED_SESSIONS',
            data: courseDoc.linkSessions,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo: get courses by using program Id
const courseProgramWiseGet = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _program_id, _curriculum_id, _course_id } = params;
        const { levelIds, type } = body;
        const courseProject = {
            _id: 1,
            courseName: 1,
            courseCode: 1,
        };
        const levelObjectIds = levelIds.map((i) => convertToMongoObjectId(i));

        let courseQuery = {
            _id: { $nin: [convertToMongoObjectId(_course_id)] },
            $or: [
                {
                    'courseOccurring._level_id': { $in: levelObjectIds },
                    _program_id: convertToMongoObjectId(_program_id),
                    _curriculum_id: convertToMongoObjectId(_curriculum_id),
                },
                {
                    'courseRecurring._level_id': { $in: levelObjectIds },
                    _program_id: convertToMongoObjectId(_program_id),
                    _curriculum_id: convertToMongoObjectId(_curriculum_id),
                },
                {
                    'courseAssignedDetails.courseSharedWith._level_id': {
                        $in: levelObjectIds,
                    },
                    'courseAssignedDetails.courseSharedWith._program_id':
                        convertToMongoObjectId(_program_id),
                    'courseAssignedDetails.courseSharedWith._curriculum_id':
                        convertToMongoObjectId(_curriculum_id),
                },
            ],

            isDeleted: false,
            isActive: true,
        };
        if (type === PREREQUISITE) {
            const prerequisiteCheck = await curriculumModel
                .findOne({ _id: _curriculum_id }, { _id: 1, yearLevel: 1 })
                .lean();
            const preRequisteIds = prerequisiteCheck.yearLevel
                .map((prerequiste) => {
                    if (prerequiste._preRequisite_id) {
                        return convertToMongoObjectId(prerequiste._preRequisite_id);
                    }
                })
                .filter((id) => !!id);
            courseQuery = {
                $or: [
                    {
                        _curriculum_id: {
                            $in: preRequisteIds,
                        },
                        isDeleted: false,
                        isActive: true,
                    },
                    {
                        _id: { $nin: [convertToMongoObjectId(_course_id)] },
                        $or: [
                            {
                                'courseOccurring._level_id': { $in: levelObjectIds },
                                _program_id: convertToMongoObjectId(_program_id),
                                _curriculum_id: convertToMongoObjectId(_curriculum_id),
                            },
                            {
                                'courseRecurring._level_id': { $in: levelObjectIds },
                                _program_id: convertToMongoObjectId(_program_id),
                                _curriculum_id: convertToMongoObjectId(_curriculum_id),
                            },
                            {
                                'courseAssignedDetails.courseSharedWith._level_id': {
                                    $in: levelObjectIds,
                                },
                                'courseAssignedDetails.courseSharedWith._program_id':
                                    convertToMongoObjectId(_program_id),
                                'courseAssignedDetails.courseSharedWith._curriculum_id':
                                    convertToMongoObjectId(_curriculum_id),
                            },
                        ],
                        isDeleted: false,
                        isActive: true,
                    },
                ],
            };
        }
        const courseCheck = await courseModel.find(courseQuery, courseProject).lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_COURSE',
            };
        if (courseCheck)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: courseCheck,
            };
        return {
            statusCode: 500,
            message: 'FAILED_TO_GET_LIST',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectiveCourseConfigAdd = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id } = params;
        const { _year_id, _level_id, noOfSelection, hoursType, selectives } = body;

        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const selectiveCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    'selectiveCourseConfiguration._year_id': convertToMongoObjectId(_year_id),
                    'selectiveCourseConfiguration._level_id': convertToMongoObjectId(_level_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    selectiveCourseConfiguration: 1,
                },
            )
            .lean();
        if (!selectiveCheck) {
            const courseUpdate = await courseModel.updateOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                },
                {
                    $push: {
                        selectiveCourseConfiguration: {
                            _year_id,
                            _level_id,
                            noOfSelection,
                            hoursType,
                            selectives,
                        },
                    },
                },
            );
            if (courseUpdate)
                return {
                    statusCode: 201,
                    message: 'DS_UPDATED',
                };
        }
        const selectiveData = selectiveCheck.selectiveCourseConfiguration.find(
            (selectiveEntry) =>
                selectiveEntry._year_id.toString() === _year_id &&
                selectiveEntry._level_id.toString() === _level_id,
        );

        const selectiveQuery = {
            _id: convertToMongoObjectId(_course_id),
            isDeleted: false,
        };
        const selectiveobject = {
            $set: {
                'selectiveCourseConfiguration.$[i]._year_id': _year_id,
                'selectiveCourseConfiguration.$[i]._level_id': _level_id,
                'selectiveCourseConfiguration.$[i].noOfSelection': noOfSelection,
                'selectiveCourseConfiguration.$[i].hoursType': hoursType,
                'selectiveCourseConfiguration.$[i].selectives': selectives,
            },
        };
        const selectivefilter = {
            arrayFilters: [
                {
                    'i._id': selectiveData._id,
                },
            ],
        };
        const selectiveUpdation = await courseModel.updateOne(
            selectiveQuery,
            selectiveobject,
            selectivefilter,
        );
        if (selectiveUpdation)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const selectiveDelete = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, _selective_id } = params;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };

        const selectiveDeletion = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            {
                $pull: {
                    selectiveCourseConfiguration: {
                        _id: convertToMongoObjectId(_selective_id),
                    },
                },
            },
        );
        if (selectiveDeletion)
            return {
                statusCode: 200,
                message: 'DS_DELETED',
            };
        return {
            statusCode: 500,
            message: 'DS_DELETE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseDelete = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const courseGroupModel = getModel(tenantURL, DS_COURSE_GROUP, courseGroupSchema);
        const { _course_id } = params;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    _curriculum_id: 1,
                    courseRecurring: 1,
                    courseOccurring: 1,
                    _program_id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };

        const courseDeletion = await courseModel.updateOne(
            { _id: convertToMongoObjectId(_course_id) },
            { $set: { isDeleted: true, isActive: false } },
        );
        const courseDeletionInRequisite = await courseModel.updateMany(
            {
                $or: [
                    { 'coRequisiteCourses._course_id': convertToMongoObjectId(_course_id) },
                    { 'preRequisiteCourses._course_id': convertToMongoObjectId(_course_id) },
                ],
            },
            {
                $pull: {
                    coRequisiteCourses: {
                        _course_id: convertToMongoObjectId(_course_id),
                    },
                    preRequisiteCourses: {
                        _course_id: convertToMongoObjectId(_course_id),
                    },
                },
            },
        );

        if (courseCheck.courseRecurring.length > 0 || courseCheck.courseOccurring.length > 0) {
            const courseLevelIds = courseCheck.courseRecurring.concat(courseCheck.courseOccurring);
            if (courseLevelIds.length > 0) {
                const levelIds = courseLevelIds.map((element) => element._level_id);
                const selectiveCheck = await curriculumModel
                    .findOne(
                        {
                            _id: convertToMongoObjectId(courseCheck._curriculum_id),
                            'selectiveCourseConfiguration._level_id': { $in: levelIds },
                            'selectiveCourseConfiguration._program_id': convertToMongoObjectId(
                                courseCheck._program_id,
                            ),
                            isDeleted: false,
                        },
                        {
                            _id: 1,
                            selectiveCourseConfiguration: 1,
                        },
                    )
                    .lean();
                const levels = levelIds.map((element) => element.toString());
                if (selectiveCheck && selectiveCheck.selectiveCourseConfiguration) {
                    const selectiveLists = selectiveCheck.selectiveCourseConfiguration.filter(
                        (element) =>
                            levels.includes(element._level_id.toString()) &&
                            element._program_id.toString() === courseCheck._program_id.toString(),
                    );
                    const selectiveIds = selectiveLists.map((element) => element._id);
                    const selectiveDeletion = await curriculumModel.updateMany(
                        {
                            _id: convertToMongoObjectId(courseCheck._curriculum_id),
                        },
                        {
                            $pull: {
                                selectiveCourseConfiguration: {
                                    _id: { $in: selectiveIds },
                                },
                            },
                        },
                    );
                }
            }
        }

        await courseGroupModel.updateMany(
            { _course_ids: { $in: [_course_id] } },
            { $pull: { _course_ids: _course_id } },
        );

        await courseGroupModel.deleteMany({ $expr: { $eq: [{ $size: '$_course_ids' }, 0] } });

        if (courseDeletion)
            return {
                statusCode: 200,
                message: 'DS_DELETED',
            };
        return {
            statusCode: 500,
            message: 'DS_DELETE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//todo: get courses by using program Id, curriculum Id, year Id, level Id
const courseParamWiseGet = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _program_id, _curriculum_id, _year_id, _level_id } = params;
        const courseProject = {};
        const courseAssignCheck = await courseModel
            .find(
                {
                    _program_id: convertToMongoObjectId(_program_id),
                    _curriculum_id: convertToMongoObjectId(_curriculum_id),
                    'courseAssignedDetails._program_id': convertToMongoObjectId(_program_id),
                    'courseAssignedDetails._curriculum_id': convertToMongoObjectId(_curriculum_id),
                    'courseAssignedDetails._level_id': convertToMongoObjectId(_level_id),
                    'courseAssignedDetails._year_id': convertToMongoObjectId(_year_id),
                    isDeleted: false,
                },
                courseProject,
            )
            .lean();
        if (!courseAssignCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_COURSE',
            };
        if (courseAssignCheck) {
            const courseDatas = [];
            for (courseAssign of courseAssignCheck) {
                const courseAssignedData = courseAssign.courseAssignedDetails.find(
                    (element) =>
                        element._year_id.toString() === _year_id &&
                        element._level_id.toString() === _level_id &&
                        element._program_id.toString() === _program_id &&
                        element._curriculum_id.toString() === _curriculum_id,
                );

                if (courseAssignedData) {
                    const sharedWith = courseAssignedData.courseSharedWith.filter(
                        (element) =>
                            element._year_id.toString() === _year_id &&
                            element._level_id.toString() === _level_id &&
                            element._program_id.toString() === _program_id &&
                            element._curriculum_id.toString() === _curriculum_id,
                    );

                    courseDatas.push({
                        _id: courseAssign._id,
                        isDeleted: courseAssign.isDeleted,
                        isActive: courseAssign.isActive,
                        _curriculum_id: courseAssign._curriculum_id,
                        _program_id: courseAssign._program_id,
                        courseType: courseAssign.courseType,
                        courseCode: courseAssign.courseCode,
                        courseName: courseAssign.courseName,
                        courseAssignedDetails: {
                            _id: courseAssignedData._id,
                            _curriculum_id: courseAssignedData._curriculum_id,
                            _program_id: courseAssignedData._program_id,
                            _level_id: courseAssignedData._level_id,
                            programName: courseAssignedData.programName,
                            curriculumName: courseAssignedData.curriculumName,
                            year: courseAssignedData.year,
                            levelNo: courseAssignedData.levelNo,
                            courseSharedWith: sharedWith,
                        },
                    });
                }
            }

            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: courseDatas,
            };
        }
        return {
            statusCode: 500,
            message: 'FAILED_TO_GET_LIST',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseAssigning = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const { courseAssignedDetails } = body;
        const { _course_id } = params;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    isPhaseFlowWithOutLevel: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };

        if (courseCheck.isPhaseFlowWithOutLevel === true) {
            const assignCheck = await courseModel
                .findOne(
                    {
                        _id: convertToMongoObjectId(_course_id),
                        'courseAssignedDetails._year_id': convertToMongoObjectId(
                            courseAssignedDetails._year_id,
                        ),
                        'courseAssignedDetails._program_id': convertToMongoObjectId(
                            courseAssignedDetails._program_id,
                        ),
                        'courseAssignedDetails._curriculum_id': convertToMongoObjectId(
                            courseAssignedDetails._curriculum_id,
                        ),
                    },
                    {
                        _id: 1,
                        courseAssignedDetails: 1,
                    },
                )
                .lean();
            if (!assignCheck) {
                const courseUpdate = await courseModel.updateOne(
                    {
                        _id: convertToMongoObjectId(_course_id),
                    },
                    {
                        $push: {
                            courseAssignedDetails,
                        },
                    },
                );
                if (courseUpdate)
                    return {
                        statusCode: 201,
                        message: 'DS_UPDATED',
                    };
            }
            const assignData = assignCheck.courseAssignedDetails.find(
                (assignEntry) =>
                    assignEntry._curriculum_id.toString() ===
                        courseAssignedDetails._curriculum_id &&
                    assignEntry._program_id.toString() === courseAssignedDetails._program_id &&
                    assignEntry._year_id.toString() === courseAssignedDetails._year_id,
            );

            //for Selective Course Remove Option Start
            const existingLevelLists = assignData.courseSharedWith;
            const existingLevelIds = existingLevelLists.map((element) => {
                return {
                    _curriculum_id: element._curriculum_id,
                    _program_id: element._program_id,
                    _year_id: element._year_id,
                };
            });

            const requestedLevelLists = courseAssignedDetails.courseSharedWith;
            const requestedLevelIds = requestedLevelLists.map((element) => {
                return {
                    _curriculum_id: element._curriculum_id,
                    _program_id: element._program_id,
                    _level_id: element._level_id,
                };
            });

            const levelIdDifference = existingLevelIds.filter(
                (item1) =>
                    !requestedLevelIds.some(
                        (item2) =>
                            item2._curriculum_id.toString() === item1._curriculum_id.toString() &&
                            item2._program_id.toString() === item1._program_id.toString() &&
                            item2._year_id.toString() === item1._year_id.toString(),
                    ),
            );
            const curriculumIds = levelIdDifference.map((element) =>
                convertToMongoObjectId(element._curriculum_id),
            );
            const curriculumLists = await curriculumModel
                .find(
                    {
                        _id: { $in: curriculumIds },
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        selectiveCourseConfiguration: 1,
                    },
                )
                .lean();
            const bulkUpdate = [];
            for (const curriculumList of curriculumLists) {
                if (
                    curriculumList.selectiveCourseConfiguration &&
                    curriculumList.selectiveCourseConfiguration.length > 0
                ) {
                    for (selectiveList of curriculumList.selectiveCourseConfiguration) {
                        if (
                            levelIdDifference.find(
                                (element) =>
                                    element._curriculum_id.toString() ===
                                        curriculumList._id.toString() &&
                                    element._program_id.toString() ===
                                        selectiveList._program_id.toString() &&
                                    element._year_id.toString() ===
                                        selectiveList._year_id.toString(),
                            )
                        ) {
                            bulkUpdate.push({
                                updateMany: {
                                    filter: {
                                        isDeleted: false,
                                        _id: convertToMongoObjectId(curriculumList._id),
                                    },
                                    update: {
                                        $pull: {
                                            selectiveCourseConfiguration: {
                                                _id: convertToMongoObjectId(selectiveList._id),
                                            },
                                        },
                                    },
                                },
                            });
                        }
                    }
                }
            }
            if (bulkUpdate.length) {
                const update = await curriculumModel.bulkWrite(bulkUpdate);
            }
            //for Selective Course Remove Option End

            const shareUpdate = await courseModel.updateOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    'courseAssignedDetails._id': assignData._id,
                },
                {
                    'courseAssignedDetails.$[assignData].courseDuration':
                        courseAssignedDetails.courseDuration,
                    'courseAssignedDetails.$[assignData].programName':
                        courseAssignedDetails.programName,
                    'courseAssignedDetails.$[assignData].curriculumName':
                        courseAssignedDetails.curriculumName,
                    'courseAssignedDetails.$[assignData].year': courseAssignedDetails.year,
                    'courseAssignedDetails.$[assignData].isActive': courseAssignedDetails.isActive,
                    'courseAssignedDetails.$[assignData].courseSharedWith':
                        courseAssignedDetails.courseSharedWith,
                    'courseAssignedDetails.$[assignData].isSharedWithIndependentCourse':
                        courseAssignedDetails.isSharedWithIndependentCourse,
                },
                {
                    arrayFilters: [{ 'assignData._id': convertToMongoObjectId(assignData._id) }],
                },
            );
            if (shareUpdate)
                return {
                    statusCode: 201,
                    message: 'DS_UPDATED',
                };

            return {
                statusCode: 500,
                message: 'DS_UPDATE_FAILED',
            };
        }

        const assignCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    'courseAssignedDetails._year_id': convertToMongoObjectId(
                        courseAssignedDetails._year_id,
                    ),
                    'courseAssignedDetails._level_id': convertToMongoObjectId(
                        courseAssignedDetails._level_id,
                    ),
                    'courseAssignedDetails._program_id': convertToMongoObjectId(
                        courseAssignedDetails._program_id,
                    ),
                    'courseAssignedDetails._curriculum_id': convertToMongoObjectId(
                        courseAssignedDetails._curriculum_id,
                    ),
                },
                {
                    _id: 1,
                    courseAssignedDetails: 1,
                },
            )
            .lean();
        if (!assignCheck) {
            const courseUpdate = await courseModel.updateOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                },
                {
                    $push: {
                        courseAssignedDetails,
                    },
                },
            );
            if (courseUpdate)
                return {
                    statusCode: 201,
                    message: 'DS_UPDATED',
                };
        }
        const assignData = assignCheck.courseAssignedDetails.find(
            (assignEntry) =>
                assignEntry._curriculum_id.toString() === courseAssignedDetails._curriculum_id &&
                assignEntry._program_id.toString() === courseAssignedDetails._program_id &&
                assignEntry._year_id.toString() === courseAssignedDetails._year_id &&
                assignEntry._level_id.toString() === courseAssignedDetails._level_id,
        );

        //for Selective Course Remove Option Start
        const existingLevelLists = assignData.courseSharedWith;
        const existingLevelIds = existingLevelLists.map((element) => {
            return {
                _curriculum_id: element._curriculum_id,
                _program_id: element._program_id,
                _level_id: element._level_id,
                _year_id: element._year_id,
            };
        });

        const requestedLevelLists = courseAssignedDetails.courseSharedWith;
        const requestedLevelIds = requestedLevelLists.map((element) => {
            return {
                _curriculum_id: element._curriculum_id,
                _program_id: element._program_id,
                _level_id: element._level_id,
                _year_id: element._year_id,
            };
        });

        const levelIdDifference = existingLevelIds.filter(
            (item1) =>
                !requestedLevelIds.some(
                    (item2) =>
                        item2._curriculum_id.toString() === item1._curriculum_id.toString() &&
                        item2._program_id.toString() === item1._program_id.toString() &&
                        item2._level_id.toString() === item1._level_id.toString() &&
                        item2._year_id.toString() === item1._year_id.toString(),
                ),
        );
        const curriculumIds = levelIdDifference.map((element) =>
            convertToMongoObjectId(element._curriculum_id),
        );
        const curriculumLists = await curriculumModel
            .find(
                {
                    _id: { $in: curriculumIds },
                    isDeleted: false,
                },
                {
                    _id: 1,
                    selectiveCourseConfiguration: 1,
                },
            )
            .lean();
        const bulkUpdate = [];
        for (const curriculumList of curriculumLists) {
            if (
                curriculumList.selectiveCourseConfiguration &&
                curriculumList.selectiveCourseConfiguration.length > 0
            ) {
                for (selectiveList of curriculumList.selectiveCourseConfiguration) {
                    if (
                        levelIdDifference.find(
                            (element) =>
                                element._curriculum_id.toString() ===
                                    curriculumList._id.toString() &&
                                element._program_id.toString() ===
                                    selectiveList._program_id.toString() &&
                                element._year_id.toString() === selectiveList._year_id.toString() &&
                                element._level_id.toString() === selectiveList._level_id.toString(),
                        )
                    ) {
                        bulkUpdate.push({
                            updateMany: {
                                filter: {
                                    isDeleted: false,
                                    _id: convertToMongoObjectId(curriculumList._id),
                                },
                                update: {
                                    $pull: {
                                        selectiveCourseConfiguration: {
                                            _id: convertToMongoObjectId(selectiveList._id),
                                        },
                                    },
                                },
                            },
                        });
                    }
                }
            }
        }
        if (bulkUpdate.length) {
            const update = await curriculumModel.bulkWrite(bulkUpdate);
        }
        //for Selective Course Remove Option End

        const shareUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
                'courseAssignedDetails._id': assignData._id,
            },
            {
                'courseAssignedDetails.$[assignData].courseDuration':
                    courseAssignedDetails.courseDuration,
                'courseAssignedDetails.$[assignData].programName':
                    courseAssignedDetails.programName,
                'courseAssignedDetails.$[assignData].curriculumName':
                    courseAssignedDetails.curriculumName,
                'courseAssignedDetails.$[assignData].year': courseAssignedDetails.year,
                'courseAssignedDetails.$[assignData].isActive': courseAssignedDetails.isActive,
                'courseAssignedDetails.$[assignData].levelNo': courseAssignedDetails.levelNo,
                'courseAssignedDetails.$[assignData].courseSharedWith':
                    courseAssignedDetails.courseSharedWith,
                'courseAssignedDetails.$[assignData].isSharedWithIndependentCourse':
                    courseAssignedDetails.isSharedWithIndependentCourse,
            },
            {
                arrayFilters: [{ 'assignData._id': convertToMongoObjectId(assignData._id) }],
            },
        );
        if (shareUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };

        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const assignDelete = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, _assign_id } = params;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };

        const assignDeletion = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            {
                $pull: {
                    courseAssignedDetails: {
                        _id: convertToMongoObjectId(_assign_id),
                    },
                },
            },
        );
        if (assignDeletion)
            return {
                statusCode: 200,
                message: 'DS_DELETED',
            };
        return {
            statusCode: 500,
            message: 'DS_DELETE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const courseEdit = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const {
            _institution_id,
            _curriculum_id,
            _program_id,
            courseCode,
            courseName,
            courseType,
            administration,
            participating,
            courseEditor,
        } = body;
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id } = params;
        const institutionCheck = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!institutionCheck)
            return {
                statusCode: 410,
                message: 'INSTITUTION_NOT_FOUND',
            };
        const courseNameCheck = await courseModel
            .find(
                {
                    _id: { $ne: convertToMongoObjectId(_course_id) },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseName: 1,
                },
            )
            .lean();
        const courseCodeCheck = await courseModel
            .find(
                {
                    _id: { $ne: convertToMongoObjectId(_course_id) },
                    _curriculum_id: convertToMongoObjectId(_curriculum_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseCode: 1,
                },
            )
            .lean();

        let isDuplicateCourseName = false;
        let isDuplicateCourseCode = false;
        if (courseNameCheck.length) {
            courseNameCheck.forEach((course) => {
                if (course.courseName.toLowerCase() === courseName.toLowerCase()) {
                    isDuplicateCourseName = true;
                }
            });
        }
        if (courseCodeCheck.length) {
            courseCodeCheck.forEach((course) => {
                if (course.courseCode.toLowerCase() === courseCode.toLowerCase()) {
                    isDuplicateCourseCode = true;
                }
            });
        }
        if (isDuplicateCourseName) {
            return { statusCode: 404, message: 'DUPLICATE_COURSE_NAME' };
        }
        if (isDuplicateCourseCode) {
            return { statusCode: 404, message: 'DUPLICATE_COURSE_CODE' };
        }
        const courseUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            {
                _institution_id,
                _curriculum_id,
                _program_id,
                courseType,
                courseCode,
                courseName,
                administration,
                participating,
                courseEditor,
            },
        );
        if (courseUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addIndependentCourse = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const {
            _institution_id,
            courseCode,
            courseName,
            administration,
            participating,
            courseEditor,
        } = body;
        const institutionExists = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!institutionExists)
            return {
                statusCode: 400,
                message: 'INSTITUTION_NOT_FOUND',
            };
        const courseCodeExists = await courseModel
            .findOne(
                {
                    courseCode,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (courseCodeExists)
            return {
                statusCode: 410,
                message: 'COURSE_CODE_ALREADY_EXISTS',
            };
        const createQuery = {
            courseType: COURSE_TYPE.INDEPENDENT,
            courseCode,
            courseName,
            administration,
            courseEditor,
            participating,
            isActive: true,
            _institution_id,
        };
        const createCourse = await courseModel.create(createQuery);
        if (createCourse)
            return {
                statusCode: 201,
                message: 'DS_CREATED',
                data: { courseId: createCourse._id },
            };
        return {
            statusCode: 500,
            message: 'DS_CREATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getIndependentCourses = async ({ query = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _institution_id, tab, searchKey } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        let getQuery = {
            _institution_id,
            isDeleted: false,
            $or: [
                {
                    courseType: COURSE_TYPE.INDEPENDENT,
                },
                {
                    'courseAssignedDetails.isSharedWithIndependentCourse': true,
                    'courseAssignedDetails.isActive': true,
                    isActive: true,
                },
            ],
        };
        if (searchKey) {
            getQuery = {
                ...getQuery,
                $or: [
                    { courseName: { $regex: searchKey, $options: 'i' } },
                    { courseCode: { $regex: searchKey, $options: 'i' } },
                ],
            };
        }
        const courses = await courseModel
            .find(getQuery, {
                courseType: 1,
                courseCode: 1,
                courseName: 1,
                administration: 1,
                courseEditor: 1,
                participating: 1,
                isActive: 1,
                isConfigured: 1,
                courseAssignedDetails: 1,
            })
            .skip(skip)
            .limit(limit)
            .lean();
        courses.forEach((courseDoc, courseIndex) => {
            if (courseDoc.courseAssignedDetails && courseDoc.courseAssignedDetails.length) {
                courseDoc.courseAssignedDetails.forEach((courseAssignedElement, assignIndex) => {
                    if (courseAssignedElement.isSharedWithIndependentCourse) {
                        courses[courseIndex].courseShared = true;
                        courses[courseIndex].courseSharedFrom = {
                            programName: courseAssignedElement.programName,
                            curriculumName: courseAssignedElement.curriculumName,
                            year: courseAssignedElement.year,
                            levelNo: courseAssignedElement.levelNo,
                            _program_id: courseAssignedElement._program_id,
                            _curriculum_id: courseAssignedElement._curriculum_id,
                            _year_id: courseAssignedElement._year_id,
                            _level_id: courseAssignedElement._level_id,
                        };
                    }
                });
            }
        });
        const totalCourses = await courseModel.find(getQuery).countDocuments().lean();
        if (!courses) return { statusCode: 500, message: DS_GET_FAILED };
        return {
            statusCode: 200,
            data: {
                totalPages: Math.ceil(totalCourses / limit),
                currentPage: pageNo,
                courses,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const GetProgramCurriculumList = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const programInputModel = getModel(tenantURL, PROGRAM, programSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const { _institution_id } = params;
        const institutionCheck = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!institutionCheck)
            return {
                statusCode: 410,
                message: 'INSTITUTION_NOT_FOUND',
            };
        const Program = await programInputModel
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    portfolio: 0,
                    settings: 0,
                    goals: 0,
                    createdAt: 0,
                    updatedAt: 0,
                    isDeleted: 0,
                    isConfigured: 0,
                },
            )
            .lean();

        if (!Program)
            return {
                statusCode: 410,
                message: 'DS_NOT_FOUND',
            };
        const programIds = Program.map((element) => element._id);

        const CurriculumList = await curriculumModel
            .find(
                {
                    _program_id: { $in: programIds },
                    isDeleted: false,
                },
                { createdAt: 0, updatedAt: 0, isDeleted: 0 },
            )
            .lean();

        Program.map((element) => {
            const programObj = element;
            programObj.curriculumData = CurriculumList.filter(
                (elementEntry) => elementEntry._program_id.toString() === element._id.toString(),
            ).map((curriculum) => {
                const yearLevel = curriculum.yearLevel.filter((level) => !level._preRequisite_id);
                curriculum.yearLevel = yearLevel;
                return curriculum;
            });
            return programObj;
        });

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: Program,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getIndependentCourse = async ({ params = {}, query = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { id } = params;
        const { courseId } = query;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const independentCourse = await courseModel.findOne(
            { _institution_id: id, _id: courseId },
            { courseCode: 1, courseName: 1, participating: 1, administration: 1 },
        );
        if (!independentCourse) return { statusCode: 500, message: DS_GET_FAILED };
        return { statusCode: 200, data: { course: independentCourse } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getIndependentCourseOverview = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliverySchema,
        );
        const { _institution_id } = params;
        const courseCount = await courseModel.countDocuments({
            _institution_id,
            $or: [
                {
                    courseType: COURSE_TYPE.INDEPENDENT,
                },
                {
                    'courseAssignedDetails.isSharedWithIndependentCourse': true,
                },
            ],
            isActive: true,
            isDeleted: false,
        });
        const sessions = await sessionDeliveryModel.find(
            {
                _institution_id,
                _program_id: { $exists: false },
                isDeleted: false,
            },
            { deliveryTypes: 1 },
        );
        const sessionTypesCount = sessions.length;
        const deliveryTypesCount = sessions
            .filter((session) => session.deliveryTypes && session.deliveryTypes.length)
            .reduce((prev, curr) => {
                return prev + curr.deliveryTypes.filter((type) => type.isDeleted === false).length;
            }, 0);
        return { statusCode: 200, data: { courseCount, sessionTypesCount, deliveryTypesCount } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const independentCourseEdit = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const {
            _institution_id,
            courseCode,
            courseName,
            courseType,
            administration,
            participating,
            courseEditor,
        } = body;
        const { _course_id } = params;
        const institutionCheck = await institutionModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_institution_id),
                },
                {
                    _id: 1,
                },
            )
            .lean();
        if (!institutionCheck)
            return {
                statusCode: 410,
                message: 'INSTITUTION_NOT_FOUND',
            };
        const courseNameCheck = await courseModel
            .find(
                {
                    _id: { $ne: convertToMongoObjectId(_course_id) },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    courseType,
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseName: 1,
                },
            )
            .lean();
        const courseCodeCheck = await courseModel
            .find(
                {
                    _id: { $ne: convertToMongoObjectId(_course_id) },
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseCode: 1,
                },
            )
            .lean();

        let isDuplicateCourseName = false;
        let isDuplicateCourseCode = false;
        if (courseNameCheck.length) {
            courseNameCheck.forEach((course) => {
                if (course.courseName.toLowerCase() === courseName.toLowerCase()) {
                    isDuplicateCourseName = true;
                }
            });
        }
        if (courseCodeCheck.length) {
            courseCodeCheck.forEach((course) => {
                if (course.courseCode.toLowerCase() === courseCode.toLowerCase()) {
                    isDuplicateCourseCode = true;
                }
            });
        }
        if (isDuplicateCourseName) {
            return { statusCode: 404, message: 'DUPLICATE_COURSE_NAME' };
        }
        if (isDuplicateCourseCode) {
            return { statusCode: 404, message: 'DUPLICATE_COURSE_CODE' };
        }
        const courseUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            {
                _institution_id,
                courseType,
                courseCode,
                courseName,
                administration,
                participating,
                courseEditor,
            },
        );
        if (courseUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const independentCourseConfigAdd = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const {
            _course_id,
            courseCode,
            courseName,
            allowEditing,
            sessionDeliveryType,
            draft,
            preRequisiteCourses,
            coRequisiteCourses,
            courseAssignedDetails,
            achieveTarget,
            duration,
            courseType,
            participating,
            courseEditor,
            administration,
        } = body;
        const { _institution_id } = headers;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseOccurring: 1,
                    courseRecurring: 1,
                    _program_id: 1,
                    _curriculum_id: 1,
                    courseAssignedDetails: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const updateQuery = {
            courseCode,
            courseName,
            allowEditing,
            sessionDeliveryType,
            preRequisiteCourses,
            coRequisiteCourses,
            achieveTarget,
            duration,
            isActive: true,
            isConfigured: true,
            courseType,
            participating,
            courseEditor,
            administration,
            $set: { courseAssignedDetails },
        };
        if (courseCode || courseName) {
            const courseNameCheck = await courseModel
                .find(
                    {
                        _id: { $ne: convertToMongoObjectId(_course_id) },
                        _institution_id: convertToMongoObjectId(_institution_id),
                        courseType,
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        courseName: 1,
                    },
                )
                .lean();
            const courseCodeCheck = await courseModel
                .find(
                    {
                        _id: { $ne: convertToMongoObjectId(_course_id) },
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isDeleted: false,
                    },
                    {
                        _id: 1,
                        courseCode: 1,
                    },
                )
                .lean();

            let isDuplicateCourseName = false;
            let isDuplicateCourseCode = false;
            if (courseNameCheck.length) {
                courseNameCheck.forEach((course) => {
                    if (course.courseName.toLowerCase() === courseName.toLowerCase()) {
                        isDuplicateCourseName = true;
                    }
                });
            }
            if (courseCodeCheck.length) {
                courseCodeCheck.forEach((course) => {
                    if (course.courseCode.toLowerCase() === courseCode.toLowerCase()) {
                        isDuplicateCourseCode = true;
                    }
                });
            }
            if (courseName && isDuplicateCourseName) {
                return { statusCode: 404, message: 'DUPLICATE_COURSE_NAME' };
            }
            if (courseCode && isDuplicateCourseCode) {
                return { statusCode: 404, message: 'DUPLICATE_COURSE_CODE' };
            }
        }
        const courseAssigningDetails = courseCheck.courseAssignedDetails;
        if (draft) updateQuery.isActive = false;
        const courseUpdate = await courseModel.findByIdAndUpdate(_course_id, updateQuery, {
            new: true,
        });
        if (courseUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const independentCourseAssign = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { courseAssignedDetails } = body;
        const { _course_id } = params;
        const assignCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                },
                {
                    _id: 1,
                    courseAssignedDetails: 1,
                },
            )
            .lean();
        if (!assignCheck.courseAssignedDetails.length) {
            const courseUpdate = await courseModel.updateOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                },
                {
                    $push: {
                        courseAssignedDetails,
                    },
                },
            );
            if (courseUpdate)
                return {
                    statusCode: 201,
                    message: 'DS_UPDATED',
                };
        }
        const shareUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
                'courseAssignedDetails._id': assignData._id,
            },
            {
                'courseAssignedDetails.$[assignData].courseDuration':
                    courseAssignedDetails.courseDuration,
                'courseAssignedDetails.$[assignData].courseSharedWith':
                    courseAssignedDetails.courseSharedWith,
            },
            {
                arrayFilters: [{ 'assignData._id': convertToMongoObjectId(assignData._id) }],
            },
        );
        if (shareUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };

        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const independentCourseConfigGet = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const sessionOrderModel = getModel(tenantURL, SESSION_ORDER, sessionOrderSchema);
        const { _course_id } = params;
        const courseProject = {
            isDeleted: 0,
            createdAt: 0,
            updatedAt: 0,
            __v: 0,
        };
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                courseProject,
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'FAILED_TO_GET_COURSE',
            };
        const findQuery = {
            _institution_id: courseCheck._institution_id,
            _course_id,
            isDeleted: false,
        };
        const sessionDocs = await sessionOrderModel
            .find(findQuery, {
                deliveryTopic: 1,
                _deliveryType_id: 1,
                sNo: 1,
                deliveryNumber: 1,
                deliverySymbol: 1,
                theme: 1,
            })
            .sort({ sNo: 1 })
            .lean();
        if (courseCheck && courseCheck.portfolio && courseCheck.portfolio.length > 0) {
            const portfolioLists = courseCheck.portfolio.map(async (element) => {
                const portfolioObject = {
                    _id: element._id,
                    title: element.title,
                    url: element.url,
                    description: element.description,
                };
                if (element.url) {
                    portfolioObject.presignedMediaURL = await getsignedUrl(element.url);
                }
                return portfolioObject;
            });
            courseCheck.portfolio = await Promise.all(portfolioLists);
        }
        const themeList = [];
        if (courseCheck.themes && courseCheck.themes.length) {
            for (courseThemeList of courseCheck.themes) {
                themeList.push({
                    _id: courseThemeList._id,
                    color: courseThemeList.color,
                    name: courseThemeList.name,
                    symbol: courseThemeList.symbol,
                    subThemes: courseThemeList.subThemes,
                });
                if (courseThemeList.subThemes && courseThemeList.subThemes.length > 0) {
                    for (courseSubThemeList of courseThemeList.subThemes) {
                        themeList.push({
                            _id: courseSubThemeList._id,
                            color: courseSubThemeList.color,
                            name: courseSubThemeList.name,
                            symbol: courseSubThemeList.symbol,
                        });
                    }
                }
            }
        }
        courseCheck.linkSessions.map((linkSession) => {
            linkSession.sessionOrder.map((sessionOrder) => {
                const sessions = sessionOrder;
                const sessionDetails = sessionDocs.find(
                    (elementEntry) =>
                        elementEntry._id.toString() === sessions._session_id.toString(),
                );
                if (sessionDetails) {
                    if (sessionDetails.theme) {
                        sessions._theme_id = sessionDetails.theme;
                        const themeDetails = themeList.find(
                            (elementEntry) =>
                                elementEntry._id.toString() === sessions._theme_id.toString(),
                        );
                        sessions.themeDetails = themeDetails;
                    }
                    if (sessionDetails.deliveryTopic)
                        sessions.deliveryTopic = sessionDetails.deliveryTopic;
                    if (sessionDetails.sNo) sessions.sNO = sessionDetails.sNo;
                    if (sessionDetails.deliverySymbol)
                        sessions.deliverySymbol = sessionDetails.deliverySymbol;
                    if (sessionDetails.deliveryNumber)
                        sessions.deliveryNo = sessionDetails.deliveryNumber;
                }
                return sessions;
            });
        });
        if (courseCheck)
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: courseCheck,
            };
        return {
            statusCode: 500,
            message: 'FAILED_TO_UPDATE',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const courseConfigAddIndianSystem = async ({ headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, courseRecurring, sessionDeliveryType, duration, isDrafted } = body;
        const courseCheck = await courseModel
            .findOne(
                {
                    _id: convertToMongoObjectId(_course_id),
                    isDeleted: false,
                },
                {
                    _id: 1,
                    courseRecurring: 1,
                    _program_id: 1,
                    _institution_id: 1,
                },
            )
            .lean();
        if (!courseCheck)
            return {
                statusCode: 410,
                message: 'DS_GET_FAILED',
            };
        const updateQuery = {
            courseRecurring,
            sessionDeliveryType,
            duration,
            isActive: true,
            isConfigured: true,
            // eslint-disable-next-line no-unneeded-ternary
            isDrafted: isDrafted === 'true' ? true : false,
        };

        const courseUpdate = await courseModel.updateOne(
            {
                _id: convertToMongoObjectId(_course_id),
            },
            updateQuery,
        );
        if (courseUpdate)
            return {
                statusCode: 201,
                message: 'DS_UPDATED',
            };
        return {
            statusCode: 500,
            message: 'DS_UPDATE_FAILED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleIndianSystemAdvanceCreditConfiguration = async () => {
    try {
        const { tenantURL } = headers;
        const courseModel = getModel(tenantURL, COURSE, courseSchema);
        const { _course_id, tab, state } = body;
        let updateQuery = {};
        if (tab === TOGGLE_CREDIT_HOURS) {
            updateQuery = { ...updateQuery, disableCreditHoursIndianSystem: state };
        } else if (tab === TOGGLE_PERIOD_WEEK) {
            updateQuery = { ...updateQuery, disablePeriodWeekIndianSystem: state };
        } else {
            updateQuery = { ...updateQuery, disableDeliveryDurationIndianSystem: state };
        }
        await courseModel.findByIdAndUpdate(_course_id, updateQuery);
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    courseAdd,
    getIndependentCourseOverview,
    courseConfigAdd,
    portfolioAdd,
    portfolioDelete,
    portfolioUpdate,
    portfolioGet,
    portfolioIdGet,
    requisiteCoursesAdd,
    addCourseTheme,
    getCourseTheme,
    listCourseTheme,
    deleteCourseTheme,
    getSubCourseTheme,
    courseGet,
    courseCurriculamWiseGet,
    getUnlinkedSessions,
    linkSessions,
    editLinkSession,
    courseProgramWiseGet,
    courseDelete,
    courseParamWiseGet,
    courseAssigning,
    assignDelete,
    addIndependentCourse,
    getIndependentCourses,
    getIndependentCourse,
    getlinkedSessions,
    courseEdit,
    GetProgramCurriculumList,
    independentCourseEdit,
    independentCourseConfigAdd,
    independentCourseConfigGet,
    independentCourseAssign,
    courseConfigAddIndianSystem,
    toggleIndianSystemAdvanceCreditConfiguration,
};
