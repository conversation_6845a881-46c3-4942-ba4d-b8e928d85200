const mongoose = require('mongoose');
const {
    SESSION_MODE: { AUTO, MANUAL },
    LMS_ATTENDANCE_CONFIG,
} = require('../utility/constants');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;
const lmsAttendanceConfigSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        mode: { type: String },
        lateConfig: {
            lateType: { type: String, enum: [AUTO, MANUAL] },
            range: {
                startRange: { type: Number },
                endRange: { type: Number },
                lateLabel: { type: String },
                noOfLate: { type: Number },
                noOfAbsent: { type: Number },
                short_code: { type: String },
            },
        },
        attendanceMarkConfig: {
            basedGrandMarks: { type: Boolean },
            equalGrandMarks: { type: Boolean },
            range: {
                startRange: { type: Number },
                endRange: { type: Number },
                marks: { type: Number },
            },
        },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(LMS_ATTENDANCE_CONFIG, lmsAttendanceConfigSchema);
