const roleAssignSchema = require('../../models/role_assign');
const { convertToMongoObjectId } = require('../../utility/common');
const { DC_STAFF } = require('../../utility/constants');

const userRoleGet = async ({ userType = '', userId = '' }) => {
    const userRoleData =
        userType === DC_STAFF
            ? (
                  await roleAssignSchema
                      .findOne(
                          {
                              isDeleted: false,
                              isActive: true,
                              _user_id: convertToMongoObjectId(userId),
                          },
                          {
                              'roles._role_id': 1,
                              'roles.isAdmin': 1,
                          },
                      )
                      .lean()
              )?.roles
            : [];
    return userRoleData;
};

module.exports = { userRoleGet };
