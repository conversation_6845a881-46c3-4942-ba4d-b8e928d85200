const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_USER_DASHBOARD_SETTING,
    USER,
    ROLE,
    INSTITUTION_CALENDAR,
    INSTITUTION,
} = require('../../utility/constants');

const qapcUserDashboardSettings = new Schema(
    {
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        userId: { type: ObjectId, ref: USER },
        roleId: { type: ObjectId, ref: ROLE },
        institutionCalendarId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
        selectedItems: [
            {
                categoryId: ObjectId,
                categoryName: String,
                categoryLevel: String,
                forms: [
                    {
                        formId: ObjectId,
                        formName: String,
                        programs: [
                            {
                                programId: ObjectId,
                                programName: String,
                                curriculumId: ObjectId,
                                curriculumName: String,
                                courseId: ObjectId,
                                courseName: String,
                            },
                        ],
                        institutions: [
                            {
                                institutionId: ObjectId,
                                institutionName: String,
                            },
                        ],
                    },
                ],
                updatedAt: { type: Date, default: Date.now() },
            },
        ],
        colorCodes: [
            {
                name: String,
                color: String,
            },
        ],
        levels: [
            {
                levelName: String,
                lowerLimit: { type: Number, default: 0 },
                rangeLimit: { type: Number, default: 0 },
                upperLimit: { type: Number, default: 0 },
                levelData: [
                    {
                        categoryId: ObjectId,
                        categoryName: String,
                        formId: ObjectId,
                        formName: String,
                        programId: ObjectId,
                        programName: String,
                        courseId: ObjectId,
                        courseName: String,
                        institutionId: ObjectId,
                        institutionName: String,
                    },
                ],
            },
        ],
        isActive: { type: Boolean, default: true },
        isDeleted: { type: Boolean, default: false },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_USER_DASHBOARD_SETTING, qapcUserDashboardSettings);
