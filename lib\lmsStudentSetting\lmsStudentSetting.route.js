const express = require('express');
const router = express.Router();
const catchAsync = require('../utility/catch-async');
const {
    getStudentLMSSetting,
    updateGeneraConfig,
    createCatagories,
    deleteCatagories,
    editCategoryName,
    createCatagoriesTypes,
    deleteCatagoriesTypes,
    updateCategoryTypes,
    updateTermsAndCondition,
    addLeaveApprovalLevel,
    deleteLevel,
    addWarningDenialConfiguration,
    editLevels,
    updateCategoryStatus,
    listProgram,
    addProgramLevel,
    editProgramLevel,
    deleteProgramLevel,
    divideGenderSegregation,
    updateWarningConfig,
    addLeavePolicy,
    deleteLeavePolicy,
    editLeavePolicy,
    getLeavePoliceForStudent,
    deleteWarningConfig,
    upload_Document,
    generateUrl,
    getUserCalendarList,
    updateCourseOrComprehensive,
    deleteLmsSettingData,
} = require('./lmsStudentSetting.controller');
const { validate } = require('../../middleware/validation');
const {
    getStudentLMSSettingValidator,
    updateGeneraConfigValidator,
    createCatagoriesValidator,
    deleteCatagoriesValidator,
    createCatagoriesTypesValidator,
    deleteCatagoriesTypesValidator,
    updateTermsAndConditionValidator,
    addLeaveApprovalLevelValidate,
    deleteLevelValidator,
    editLevelsValidator,
    updateCategoryTypesValidator,
    updateCategoryStatusValidator,
    listProgramValidator,
    deleteLevelApproverValidator,
    addProgramLevelValidator,
    editProgramLevelValidator,
    divideGenderSegregationValidator,
    updateWarningConfigValidator,
    addLeavePolicyValidator,
    deleteLeavePolicyValidator,
    editLeavePolicyValidator,
    editCategoryNameValidator,
    addWarningDenialConfigurationValidator,
    getLeavePoliceForStudentValidator,
    deleteWarningConfigValidator,
    listScheduleValidator,
    courseOrComprehensiveValidator,
    deleteLmsSettingDataValidator,
} = require('./lmsStudentSetting.validator');
const { uploadDocument } = require('./lmsStudentSetting.service');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.get(
    '/getSetting',
    [
        userPolicyAuthentication([
            defaultPolicy.DC_STUDENT,
            defaultPolicy.DC_STAFF,
            'leave_management:leave_settings:student_lms:permission:view',
            'leave_management:leave_settings:student_lms:leave:view',
            'leave_management:leave_settings:student_lms:on_duty:view',
            'leave_management:leave_settings:student_lms:leave:warnings_&_notifications_view',
            'leave_management:leave_settings:student_lms:leave:leave_policy_documents_view',
        ]),
    ],
    validate(getStudentLMSSettingValidator),
    catchAsync(getStudentLMSSetting),
);
router.put(
    '/updateGeneral/:settingId',
    validate(updateGeneraConfigValidator),
    catchAsync(updateGeneraConfig),
);
router.post(
    '/createCategory',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:permission:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:on_duty:on_duty_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_view',
        ]),
    ],
    validate(createCatagoriesValidator),
    catchAsync(createCatagories),
);
router.delete(
    '/removeCategory',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:permission:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:general_edit',
        ]),
    ],
    validate(deleteCatagoriesValidator),
    catchAsync(deleteCatagories),
);
router.put(
    '/createTypes',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:permission:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:general_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_view',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
        ]),
    ],
    validate(createCatagoriesTypesValidator),
    catchAsync(createCatagoriesTypes),
);
router.delete(
    '/removeCategoryTypes',
    validate(deleteCatagoriesTypesValidator),
    catchAsync(deleteCatagoriesTypes),
);
router.put(
    '/updateTypes',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:permission:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:general_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
        ]),
    ],
    validate(updateCategoryTypesValidator),
    catchAsync(updateCategoryTypes),
);
router.put(
    '/termsAndCondition',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:permission:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:on_duty:on_duty_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_view',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_view',
        ]),
    ],
    validate(updateTermsAndConditionValidator),
    catchAsync(updateTermsAndCondition),
);
router.put(
    '/addLevels',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:permission:general_configuration_view',
            'leave_management:leave_settings:student_lms:leave:general_edit',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
        ]),
    ],
    validate(addLeaveApprovalLevelValidate),
    catchAsync(addLeaveApprovalLevel),
);
router.delete(
    '/removeLevel',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:general_edit',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
        ]),
    ],
    validate(deleteLevelValidator),
    catchAsync(deleteLevel),
);
router.put(
    '/editLevel',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:general_edit',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
        ]),
    ],
    validate(editLevelsValidator),
    catchAsync(editLevels),
);
router.put(
    '/add-warning-denial-configuration',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:warnings_&_notifications_edit',
        ]),
    ],
    validate(addWarningDenialConfigurationValidator),
    catchAsync(addWarningDenialConfiguration),
);
router.put(
    '/updateCategoryStatus',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:general_configuration_edit',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:general_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
            '',
        ]),
    ],
    validate(updateCategoryStatusValidator),
    catchAsync(updateCategoryStatus),
);
router.get('/listProgram', validate(listProgramValidator), catchAsync(listProgram));
router.put(
    '/addProgramLevel',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_edit',
            'leave_management:leave_settings:student_lms:on_duty:on_duty_approval_levels_and_roles_edit',
        ]),
    ],
    validate(addProgramLevelValidator),
    catchAsync(addProgramLevel),
);
router.put(
    '/editProgramLevel',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_view',
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_edit',
            'leave_management:leave_settings:student_lms:on_duty:on_duty_approval_levels_and_roles_edit',
        ]),
    ],
    validate(editProgramLevelValidator),
    catchAsync(editProgramLevel),
);
router.delete(
    '/deleteProgramLevel',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_view',
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_edit',
            'leave_management:leave_settings:student_lms:on_duty:on_duty_approval_levels_and_roles_edit',
        ]),
    ],
    validate(deleteLevelApproverValidator),
    catchAsync(deleteProgramLevel),
);
router.put(
    '/divideGenderSegregation',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_view',
            'leave_management:leave_settings:student_lms:permission:permission_approval_levels_and_roles_edit',
            'leave_management:leave_settings:student_lms:on_duty:on_duty_approval_levels_and_roles_edit',
        ]),
    ],
    validate(divideGenderSegregationValidator),
    catchAsync(divideGenderSegregation),
);
router.put(
    '/updateWarningConfig',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:warnings_&_notifications_edit',
        ]),
    ],
    validate(updateWarningConfigValidator),
    catchAsync(updateWarningConfig),
);
router.put(
    '/addLeavePolicy',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:leave_policy_documents_edit',
        ]),
    ],
    validate(addLeavePolicyValidator),
    catchAsync(addLeavePolicy),
);
router.delete(
    '/deleteLeavePolicy',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:leave_policy_documents_edit',
        ]),
    ],
    validate(deleteLeavePolicyValidator),
    catchAsync(deleteLeavePolicy),
);
router.put(
    '/editLeavePolicy',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:leave_policy_documents_edit',
        ]),
    ],
    validate(editLeavePolicyValidator),
    catchAsync(editLeavePolicy),
);
router.put(
    '/editCategoryName',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:permission:permission_configuration_edit',
            'leave_management:leave_settings:student_lms:on_duty:general_configuration_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
        ]),
    ],
    validate(editCategoryNameValidator),
    catchAsync(editCategoryName),
);
router.get(
    '/getLeavePoliceForStudent',
    validate(getLeavePoliceForStudentValidator),
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    catchAsync(getLeavePoliceForStudent),
);
router.delete('/deleteWarningConfig', catchAsync(deleteWarningConfig));
router.post(
    '/upload',
    uploadDocument,
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT, defaultPolicy.DC_STAFF])],
    catchAsync(upload_Document),
);
router.get(
    '/generateUrl',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT, defaultPolicy.DC_STAFF])],
    catchAsync(generateUrl),
);
router.put(
    '/updateCourseOrComprehensive',
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:leave_classifications_edit',
            'leave_management:leave_settings:student_lms:leave:leave_classifications_view',
        ]),
    ],
    courseOrComprehensiveValidator,
    catchAsync(updateCourseOrComprehensive),
);
// User Calendar List
router.get(
    '/getUserCalendarList/:userId/:userType/:isWarning',
    [
        userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT], {
            paramName: 'userType',
        }),
    ],
    catchAsync(getUserCalendarList),
);
router.delete(
    '/deleteLmsSetting',
    validate(deleteLmsSettingDataValidator),
    catchAsync(deleteLmsSettingData),
);
module.exports = router;
