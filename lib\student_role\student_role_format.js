module.exports.listToTree = (arr, where = null) => {
  var tree = [],
    mappedArr = {},
    mappedElem;
  arr.forEach((ele) => (mappedArr[ele._id] = ele));
  for (var _id in mappedArr) {
    if (mappedArr.hasOwnProperty(_id)) {
      mappedElem = mappedArr[_id];
      if (mappedElem.parent_id)
        mappedArr[mappedElem["parent_id"]]["children"].push(mappedElem);
      else tree.push(mappedElem);
    }
  }
  if (where) tree = tree.filter((ele) => ele._id == where);
  return tree;
};
