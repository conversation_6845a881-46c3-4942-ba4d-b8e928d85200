const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../../utility/constants');
const { getConnectionString } = require('../../config/db.config');

const institution = new Schema(
    {
        calendarName: String,
        calendarType: {
            type: String,
            enum: [constant.PRIMARY, constant.SECONDARY],
        },
        _primary_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION_CALENDAR,
        },
        batch: {
            type: String,
            required: true,
        },
        startDate: {
            type: Date,
            required: true,
        },
        endDate: {
            type: Date,
            required: true,
        },
        primaryCalendar: {
            type: String,
            enum: [constant.GREGORIAN, constant.HIJRI],
            required: true,
        },
        status: {
            type: String,
        },
        _creater_id: {
            type: Schema.Types.ObjectId,
            ref: constant.STAFF,
            required: true,
        },
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = getConnectionString(constant.INSTITUTION_CALENDAR, institution);
