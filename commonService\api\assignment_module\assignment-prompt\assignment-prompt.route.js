const express = require('express');
const route = express.Router();
const assignmentPrompt = require('./assignment-prompt.controller');
const catchAsync = require('../../../utility/catch-async');
const {
    createPromptValidator,
    getPromptValidator,
    promptListValidator,
    promptWithoutAnswerValidator,
} = require('./assignment-prompt.validator');
const { validate } = require('../../../../middleware/validation');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

route.post(
    '/',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: createPromptValidator, property: 'body' }]),
    catchAsync(assignmentPrompt.addAssignmentPrompt),
);
route.get(
    '/',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getPromptValidator, property: 'query' }]),
    catchAsync(assignmentPrompt.getAssignmentPrompt),
);
route.get(
    '/without-answer',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getPromptValidator, property: 'query' }]),
    catchAsync(assignmentPrompt.getAssignmentPromptWithoutAnswer),
);
route.delete(
    '/',
    validate([{ schema: getPromptValidator, property: 'query' }]),
    catchAsync(assignmentPrompt.deleteAssignmentPrompt),
);
route.post(
    '/lists',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: promptListValidator, property: 'body' }]),
    catchAsync(assignmentPrompt.listsAssignmentPrompt),
);
route.post(
    '/lists-without-answer',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: promptWithoutAnswerValidator, property: 'body' }]),
    catchAsync(assignmentPrompt.listsAssignmentPromptWithoutAnswer),
);
route.put(
    '/deleteRubrics',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getPromptValidator, property: 'body' }]),
    catchAsync(assignmentPrompt.deletePromptRubric),
);
module.exports = route;
