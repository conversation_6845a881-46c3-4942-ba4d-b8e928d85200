const assessmentLibrarySchema = require('./assessment-library.model');
const assessmentCourseProgramSchema = require('../assessment-course-program/assessment-course-program.model');
const { convertToMongoObjectId, clone } = require('../../../utility/common');
const {
    getCourseSLO,
    getCourseCLO,
    getProgramPLO,
    getCourseStudents,
    getProgramLevelStudents,
} = require('./assessment-library.service');
const { SLO, CLO, PLO, PENDING } = require('../../../utility/constants');

const getCourseCLOSLO_ProgramPLO = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { getValues, programId, courseId, institutionCalendarId } = query;
        let responseData = [];
        switch (getValues) {
            case SLO:
                responseData = await getCourseSLO({ _institution_id, programId, courseId });
                break;
            case CLO:
                responseData = await getCourseCLO({ _institution_id, courseId });
                break;
            case PLO:
                responseData = await getProgramPLO({
                    _institution_id,
                    programId,
                    institutionCalendarId,
                });
                break;
            default:
                break;
        }
        return { statusCode: 200, data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAssessment = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            programId,
            institutionCalendarId,
            term,
            level,
            courseId,
            type,
            rotationCount,
            assessmentId,
        } = query;
        let assessmentPlanning = await assessmentLibrarySchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                    level,
                    _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                    type,
                    rotationNo: rotationCount,
                    _assessment_id: convertToMongoObjectId(assessmentId),
                },
                {},
            )
            .lean()
            .populate({
                path: '_course_id',
                select: { course_name: 1 },
            })
            .populate({
                path: '_program_id',
                select: { name: 1 },
            });
        if (!assessmentPlanning) {
            const assessmentTypes = await assessmentCourseProgramSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        term,
                        level,
                        _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                        type,
                        'types.subTypes.assessmentTypes.planning._id':
                            convertToMongoObjectId(assessmentId),
                    },
                    {
                        'types.subTypes.isActive': 1,
                        'types.subTypes.assessmentTypes.isActive': 1,
                        'types.subTypes.assessmentTypes.planning': 1,
                    },
                )
                .lean();
            if (!assessmentTypes) return { statusCode: 404, message: 'Assessment Not Found' };
            let assessmentObject;
            for (typeElement of assessmentTypes.types) {
                for (subTypeElement of typeElement.subTypes) {
                    if (subTypeElement.isActive) {
                        for (const assessmentTypeElement of subTypeElement.assessmentTypes) {
                            if (assessmentTypeElement.isActive) {
                                for (planningElement of assessmentTypeElement.planning) {
                                    if (
                                        planningElement._id.toString() === assessmentId.toString()
                                    ) {
                                        assessmentObject = planningElement;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (!assessmentObject)
                return { statusCode: 200, message: 'Assessment Not Found', data: {} };
            await assessmentLibrarySchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                term,
                level,
                _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                type,
                rotationNo: rotationCount,
                _assessment_id: convertToMongoObjectId(assessmentId),
                assessmentName: assessmentObject.name,
                assessmentMark: assessmentObject.totalMark,
            });
            assessmentPlanning = await assessmentLibrarySchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        term,
                        level,
                        _course_id: courseId ? convertToMongoObjectId(courseId) : undefined,
                        type,
                        rotationNo: rotationCount,
                        _assessment_id: convertToMongoObjectId(assessmentId),
                    },
                    {},
                )
                .lean()
                .populate({
                    path: '_course_id',
                    select: { course_name: 1 },
                })
                .populate({
                    path: '_program_id',
                    select: { name: 1 },
                });
        }
        return { statusCode: 200, data: assessmentPlanning };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const assessmentFeeding = async ({ body = {} }) => {
    try {
        const {
            _id,
            noQuestions,
            questionOutcome,
            benchMark,
            questionMarks,
            studentDetails,
            curriculumName,
            levelNo,
            typeName,
            typeId,
            subTypeName,
            subTypeId,
            assessmentTypeId,
            assessmentTypeName,
        } = body;
        const dbUpdate = {
            $set: {
                noQuestions,
                questionOutcome,
                benchMark,
                questionMarks,
                studentDetails,
                curriculumName,
                levelNo,
                typeName,
                typeId,
                subTypeName,
                subTypeId,
                assessmentTypeId,
                assessmentTypeName,
                status: PENDING,
            },
        };
        const assessmentSetting = await assessmentLibrarySchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT ADDED' };
        return { statusCode: 200, message: 'ASSESSMENT ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseStudentList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, courseId, institutionCalendarId, term, level, rotationCount } = query;
        let responseData = [];
        responseData = await getCourseStudents({
            _institution_id,
            programId,
            courseId,
            institutionCalendarId,
            term,
            level,
            rotationCount,
        });
        return { statusCode: 200, data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getProgramLevelsStudentList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, institutionCalendarId, term, level } = query;
        let responseData = [];
        responseData = await getProgramLevelStudents({
            _institution_id,
            programId,
            institutionCalendarId,
            term,
            level,
        });
        return { statusCode: 200, data: responseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const publishedAssignment = async ({ query = {} }) => {
    try {
        const { id } = query;
        const assessmentData = await assessmentLibrarySchema.updateOne(
            {
                _id: convertToMongoObjectId(id),
            },
            {
                $set: {
                    publishedTime: new Date(),
                    status: 'published',
                },
            },
        );
        if (!assessmentData) return { statusCode: 400, message: 'Data_Not_update' };
        return { statusCode: 200, message: 'Data_Update' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    getCourseCLOSLO_ProgramPLO,
    getAssessment,
    assessmentFeeding,
    getCourseStudentList,
    getProgramLevelsStudentList,
    publishedAssignment,
};
