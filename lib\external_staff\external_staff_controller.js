const externalStaffSchema = require('../models/externalStaff');
const { convertToMongoObjectId } = require('../utility/common');
exports.addExternalStaff = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _program_id,
            _course_id,
            name,
            mobileNo,
            email,
            _institution_calendar_id,
            _infra_id,
        } = body;
        const externalStaffData = await externalStaffSchema.create({
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            _program_id: convertToMongoObjectId(_program_id),
            _course_id: convertToMongoObjectId(_course_id),
            _infra_id: convertToMongoObjectId(_infra_id),
            name,
            mobileNo,
            email,
        });
        if (!externalStaffData) {
            return { statusCode: 200, message: 'Not_Created_Data' };
        }
        return { statusCode: 200, message: 'Created Data', data: { _id: externalStaffData._id } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.editExternalStaff = async ({ body = {} }) => {
    try {
        const { name, mobileNo, email, _id } = body;
        const externalStaffData = await externalStaffSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(_id),
            },
            {
                $set: {
                    ...(name && { name }),
                    ...(mobileNo && { mobileNo }),
                    ...(email && { email }),
                },
            },
        );
        if (!externalStaffData) {
            return { status: 200, message: 'UNABLE_TO_UPDATE' };
        }
        return { status: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.getExternalStaff = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        // const { _institution_calendar_id } = query;
        const externalStaffData = await externalStaffSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    // _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    name: 1,
                    mobileNo: 1,
                    email: 1,
                    createdAt: 1,
                },
            )
            .sort({ createdAt: -1 })
            .lean();
        if (!externalStaffData.length) {
            return { status: 200, message: 'No_Data', data: [] };
        }
        return { status: 200, message: 'List_Data', data: externalStaffData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteExternalStaff = async ({ params = {} }) => {
    try {
        const { _id } = params;
        const externalStaffData = await externalStaffSchema.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(_id),
            },
            {
                $set: {
                    isDeleted: true,
                },
            },
        );
        if (!externalStaffData) return { statusCode: 410, message: 'Not deleted' };
        return { statusCode: 200, message: 'Deleted successfully' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
