const mongoose = require('mongoose');
const ObjectId = mongoose.Types.ObjectId;
const Schema = mongoose.Schema;
const {
    ANNOUNCEMENTUSERSETTING,
    USER,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_COURSE,
} = require('../utility/constants');
const announcementUserSettingSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        manageUser: { type: ObjectId },
        userId: { type: ObjectId, ref: USER, required: true },
        selectedCourses: [
            {
                _program_id: [{ type: ObjectId, ref: DIGI_PROGRAM }],
                _institution_calendar_id: { type: ObjectId, ref: INSTITUTION_CALENDAR },
                level: [
                    {
                        term: {
                            type: String,
                        },
                        year: { type: String },
                        level_no: { type: String },
                        curriculum: {
                            type: String,
                        },
                        rotation: {
                            type: String,
                            enum: ['yes', 'no'],
                        },
                        course: [
                            {
                                _course_id: {
                                    type: Schema.Types.ObjectId,
                                    ref: DIGI_COURSE,
                                },
                                courses_name: {
                                    type: String,
                                },
                                courses_number: {
                                    type: String,
                                },
                            },
                        ],
                        rotation_course: [
                            {
                                rotation_count: { type: Number },
                                course: [
                                    {
                                        _course_id: {
                                            type: Schema.Types.ObjectId,
                                            ref: DIGI_COURSE,
                                        },
                                        courses_name: {
                                            type: String,
                                        },
                                        courses_number: {
                                            type: String,
                                        },
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        ],
        selectedPrograms: [{ type: ObjectId, ref: DIGI_PROGRAM }],
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(ANNOUNCEMENTUSERSETTING, announcementUserSettingSchema);
