const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const student_group = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _institution_calendar_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION_CALENDAR,
            required: true,
        },
        master: {
            _program_id: {
                type: Schema.Types.ObjectId,
                ref: constant.PROGRAM,
                required: true,
            },
            program_no: {
                type: String,
                required: true,
            },
            program_name: {
                type: String,
                required: true,
            },
            year: {
                type: String,
                required: true,
            },
            yearCode: { type: String },
        },
        groups: [
            {
                group_name: {
                    type: String,
                    required: true,
                },
                group_mode: {
                    type: String,
                    enum: [
                        constant.STUDENT_GROUP_MODE.FYD,
                        constant.STUDENT_GROUP_MODE.COURSE,
                        constant.STUDENT_GROUP_MODE.ROTATION,
                    ],
                    required: true,
                },
                level: {
                    type: String,
                    required: true,
                },
                term: {
                    type: String,
                    // enum: [constant.BATCH.REGULAR, constant.BATCH.INTERIM],
                    required: true,
                },
                termCode: { type: String },
                levelCode: { type: String },
                curriculumCode: { type: String },
                curriculum: {
                    type: String,
                    required: true,
                },
                ungrouped: [
                    {
                        type: Schema.Types.ObjectId,
                        ref: constant.USER,
                    },
                ],
                students: [
                    {
                        _student_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.USER,
                        },
                        academic_no: String,
                        name: {
                            first: {
                                type: String,
                                trim: true,
                            },
                            middle: {
                                type: String,
                                trim: true,
                            },
                            last: {
                                type: String,
                                trim: true,
                            },
                            family: {
                                type: String,
                                trim: true,
                            },
                        },
                        gender: {
                            type: String,
                            enum: [constant.GENDER.MALE, constant.GENDER.FEMALE],
                        },
                        mark: String,
                        imported_on: {
                            type: Date,
                        },
                        imported_by: {
                            first: {
                                type: String,
                                trim: true,
                            },
                            middle: {
                                type: String,
                                trim: true,
                            },
                            last: {
                                type: String,
                                trim: true,
                            },
                            family: {
                                type: String,
                                trim: true,
                            },
                        },
                        _imported_by: {
                            type: Schema.Types.ObjectId,
                            ref: constant.USER,
                        },
                        master_group_status: {
                            type: String,
                            default: 'pending',
                        },
                        course_group_status: [
                            {
                                _course_id: Schema.Types.ObjectId,
                                status: {
                                    type: String,
                                    default: 'pending',
                                },
                            },
                        ],
                    },
                ],
                group_excess_count: Number,
                rotation: String,
                rotation_count: Number,
                rotation_group_setting: [
                    {
                        gender_type: String,
                        gender: {
                            type: String,
                            enum: [
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            ],
                        },
                        group_no: Number,
                        group_name: String,
                        no_of_student: {
                            type: Number,
                            min: 1,
                        },
                        _student_ids: [
                            {
                                type: Schema.Types.ObjectId,
                                ref: constant.USER,
                            },
                        ],
                        // excess_count: Number
                    },
                ],
                group_setting: [
                    {
                        gender_type: String,
                        gender: {
                            type: String,
                            enum: [
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            ],
                        },
                        no_of_group: {
                            type: Number,
                            min: 1,
                        },
                        no_of_student: {
                            type: Number,
                            min: 1,
                        },
                        // excess_count: Number,
                        groups: [
                            {
                                group_no: Number,
                                group_name: String,
                                _student_ids: [
                                    {
                                        type: Schema.Types.ObjectId,
                                        ref: constant.USER,
                                    },
                                ],
                            },
                        ],
                    },
                ],
                course_excess_count: Number,
                courses: [
                    {
                        _course_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DIGI_COURSE,
                        },
                        course_name: String,
                        course_no: String,
                        course_type: String,
                        versionNo: Number,
                        versioned: Boolean,
                        versionName: String,
                        versionedFrom: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DIGI_COURSE,
                        },
                        session_types: [
                            {
                                session_type: String,
                                symbol: String,
                            },
                        ],
                        _removed_student_ids: [
                            {
                                type: Schema.Types.ObjectId,
                                ref: constant.USER,
                            },
                        ],
                        setting: [
                            {
                                // _group_id: Schema.Types.ObjectId,
                                _group_name: String,
                                _group_no: Number,
                                ungrouped: [
                                    {
                                        type: Schema.Types.ObjectId,
                                        ref: constant.USER,
                                    },
                                ],
                                gender: {
                                    type: String,
                                    enum: [
                                        constant.GENDER.MALE,
                                        constant.GENDER.FEMALE,
                                        constant.GENDER.BOTH,
                                    ],
                                },
                                // excess_count: Number,
                                session_setting: [
                                    {
                                        group_name: String,
                                        delivery_type: String,
                                        session_type: String,
                                        no_of_group: {
                                            type: Number,
                                            min: 1,
                                        },
                                        no_of_student: {
                                            type: Number,
                                            min: 1,
                                        },
                                        groups: [
                                            {
                                                group_no: Number,
                                                group_name: String,
                                                _student_ids: [
                                                    {
                                                        type: Schema.Types.ObjectId,
                                                        ref: constant.USER,
                                                    },
                                                ],
                                            },
                                        ],
                                    },
                                ],
                            },
                        ],
                        student_absence_percentage: {
                            type: Number,
                            default: 0,
                        },
                        gender_mixed: { type: Boolean, default: false },
                    },
                ],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.STUDENT_GROUP, student_group);
