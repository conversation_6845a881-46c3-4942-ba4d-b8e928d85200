const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const FromTypeController = require('./form-type.controller');
const {
    createFormTypeSchema,
    updateFormTypeSchema,
    deleteFormTypeSchema,
} = require('./form-type.validation');

router.post('/', validate(createFormTypeSchema), catchAsync(FromTypeController.createFormType));

router.get('/', catchAsync(FromTypeController.getFormTypes));

router.put('/', validate(updateFormTypeSchema), catchAsync(FromTypeController.updateFormType));

router.delete('/', validate(deleteFormTypeSchema), catchAsync(FromTypeController.deleteFormType));

module.exports = router;
