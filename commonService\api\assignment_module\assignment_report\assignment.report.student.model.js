const mongoose = require('mongoose');
const {
    ASSIGNMENT_REPORT,
    PUBLISH,
    DRAFT,
    ASSIGNMENT_STUDENT_REPORT,
} = require('../../../utility/constants');
const { DIGI_PROGRAM, USER, INSTITUTION_CALENDAR } = require('../../../../lib/utility/constants');
const Schema = mongoose.Schema;
const objectId = mongoose.Types.ObjectId;

const nestedField = {
    mark: {
        type: Number,
    },
    reasonForMarkUpdate: {
        reason: {
            type: String,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
};

const assignmentReportStudentData = new Schema({
    _institution_calendar_id: {
        type: objectId,
        ref: INSTITUTION_CALENDAR,
    },
    studentName: {
        type: String,
    },
    reportId: {
        type: objectId,
        ref: ASSIGNMENT_REPORT,
    },
    studentId: {
        type: objectId,
        ref: USER,
    },
    status: {
        type: String,
        enum: [PUBLISH, DRAFT],
    },
    isDeleted: {
        type: Boolean,
        default: false,
    },
    assignmentDetails: {
        assignment: [
            {
                assignmentId: {
                    type: objectId,
                },
                assignmentName: {
                    type: String,
                },
                assignmentMark: {
                    type: Number,
                },
                adjustMark: {
                    type: Number,
                },
                studentMark: {
                    type: Number,
                },
            },
        ],
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    markAdjustment: {
        isChecked: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
        },
        ...nestedField,
    },
    obtained: {
        mark: {
            type: Number,
        },
        percentage: {
            type: Number,
            min: 0,
            max: 100,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    extraMark: {
        mark: {
            type: Number,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    deductMark: {
        isChecked: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
        },
        ...nestedField,
    },
    finalObtainedMark: {
        mark: {
            type: Number,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    markEquivalence: {
        equivalenceMark: {
            type: Number,
        },
        studentMark: {
            type: Number,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    finalObtainedPercentage: {
        percentage: {
            type: Number,
            min: 0,
            max: 100,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    grade: {
        studentGrade: {
            type: String,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
    criticalStatus: {
        status: {
            type: String,
        },
        isChecked: {
            type: Boolean,
            default: false,
        },
    },
});

module.exports = mongoose.model(ASSIGNMENT_STUDENT_REPORT, assignmentReportStudentData);
