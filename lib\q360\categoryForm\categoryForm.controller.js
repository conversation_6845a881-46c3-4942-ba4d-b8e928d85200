const programSchema = require('../../models/digi_programs');
const curriculamSchema = require('../../models/digi_curriculum');
const roleSchema = require('../../models/role');
const roleAssignSchema = require('../../models/role_assign');
const { convertToMongoObjectId } = require('../../utility/common');
const { getPaginationValues } = require('../../utility/pagination');
const {
    updateFormCourseSetting,
    getDraftFormSetting,
    checkDuplicateFormName,
    getCurriculumData,
    getCourseList,
    programCurriculumList,
    assignCoursesToPrograms,
    updateCourseGroupStatus,
    isCreatedFromInitiator,
} = require('./categoryForm.service');
const {
    ARCHIVE,
    PUBLISHED,
    DRAFT,
    LEVEL_HIERARCHY,
    UNPUBLISHED,
    PRIMARY,
} = require('../../utility/constants');
const { getS3SignedUrl } = require('../../../service/aws.service');
const formSettingSchema = require('./formSetting.model');
const formSettingCourseSchema = require('./formSettingCourses.model');
const formCourseGroupSchema = require('./formCourseGroups.module');
const institutionsSchema = require('../../models/institution');
const institutionCalenderSchema = require('../../models/institution_calendar');
const { BUCKET_QAPC_DOCUMENT } = require('../../utility/util_keys');
const { searchKeyFunction } = require('../../utility/common_functions');

exports.getProgramList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { searchKey } = query;
        const pagination = getPaginationValues(query);
        const programQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
            ...(searchKey && {
                $or: [
                    { name: searchKeyFunction({ searchKey }) },
                    { code: searchKeyFunction({ searchKey }) },
                ],
            }),
        };
        const totalCount = await programSchema.countDocuments(programQuery);
        const programData = await programSchema
            .find(programQuery, { _id: 1, name: 1, code: 1 })
            .sort({ _id: -1 })
            .skip(pagination.skip)
            .limit(pagination.limit)
            .lean();
        if (!programData.length) return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalCount,
                programData,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.programDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId } = query;
        const curriculamData = await getCurriculumData({ _institution_id, programId });
        const curriculumIds = curriculamData.map((curriculumElement) =>
            convertToMongoObjectId(curriculumElement._id),
        );
        const courseList = await getCourseList({ programId, curriculumIds });
        const programList = {};
        await programCurriculumList({ programId, programList, curriculamData });
        await assignCoursesToPrograms({ programList, courseList });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programList };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.roleUserList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const roleQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
        };
        const totalCount = await roleSchema.countDocuments(roleQuery);
        const roleData = await roleSchema
            .find(roleQuery, {
                name: 1,
            })
            .lean();
        if (!roleData.length)
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: { totalCount } };
        const roleUserList = await roleAssignSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    ...(roleData &&
                        roleData.length && {
                            'roles._role_id': {
                                $in: roleData.map((roleElement) =>
                                    convertToMongoObjectId(roleElement._id),
                                ),
                            },
                        }),
                },
                {
                    _user_id: 1,
                    'roles._role_id': 1,
                },
            )
            .populate({ path: '_user_id', select: { user_id: 1, name: 1 } })
            .populate({ path: 'roles._role_id', select: { name: 1 } })
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { roleUserList, roleData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createDuplicateForm = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            categoryFormType,
            categoryId,
            templateId,
            formName,
            describe,
            selectCourses,
            selectedProgram,
            selectedInstitution,
            formType,
            incorporateMandatory,
            categorySettings,
            actions,
        } = body;
        //check the Duplicate formName
        const isDuplicate = await checkDuplicateFormName({ formName, categoryId });
        if (isDuplicate.checkFormNameCount) {
            return { statusCode: 409, message: 'FORM_NAME_ALREADY_EXISTS' };
        }
        const formCourseBulkWrites = [];
        const createdFormQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryId: convertToMongoObjectId(categoryId),
        };
        const createFormData = await formSettingSchema.create({
            ...createdFormQuery,
            templateId: convertToMongoObjectId(templateId),
            ...(selectedProgram && { selectedProgram }),
            ...(selectedInstitution && { selectedInstitution }),
            formName,
            describe,
            formType,
            incorporateMandatory,
            categorySettings,
            categoryFormType,
            approvalLevel: [{ name: LEVEL_HIERARCHY }],
        });
        if (!createFormData) return { statusCode: 400, message: 'NOT_SAVED' };
        createdFormQuery.categoryFormId = convertToMongoObjectId(createFormData._id);
        //bulkWrite selected course
        if (selectCourses && selectCourses.length) {
            selectCourses.forEach((assignElement) => {
                formCourseBulkWrites.push({ ...createdFormQuery, ...assignElement, actions });
            });
        }
        if (formCourseBulkWrites.length) {
            await formSettingCourseSchema.insertMany(formCourseBulkWrites);
        }
        const formSetting = {
            _id: createFormData._id,
            formName: createFormData.formName,
            describe: createFormData.describe,
            status: createFormData.status,
            selectedProgram: createFormData.selectedProgram ? createFormData.selectedProgram : [],
            selectedInstitution: createFormData.selectedInstitution
                ? createFormData.selectedInstitution
                : [],
        };
        return {
            statusCode: 200,
            message: 'SAVED_SUCCESSFULLY',
            data: formSetting,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCategoryForm = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId, searchKey } = query;
        const pagination = getPaginationValues(query);
        const formQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryId: convertToMongoObjectId(categoryId),
            isDeleted: false,
        };
        const totalCount = await formSettingSchema.countDocuments({
            ...formQuery,
            ...(searchKey && {
                formName: searchKeyFunction({ searchKey }),
            }),
            archive: false,
        });
        const formSettingData = await formSettingSchema
            .find(
                {
                    ...formQuery,
                    ...(searchKey && {
                        formName: searchKeyFunction({ searchKey }),
                    }),
                    archive: false,
                },
                {
                    formName: 1,
                    describe: 1,
                    status: 1,
                    formType: 1,
                    step: 1,
                    updatedAt: 1,
                    selectedProgram: 1,
                    selectedInstitution: 1,
                    isActive: 1,
                    incorporateMandatory: 1,
                },
            )
            .sort({ updatedAt: -1 })
            .skip(pagination.skip)
            .limit(pagination.limit)
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalCount,
                formSettingData,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateCategoryForm = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            categoryFormId,
            categoryId,
            formName,
            describe,
            status,
            attachments,
            approvalLevel,
            isActive,
            step,
            isDeleted,
            archive,
            selectCourses,
            selectedProgram,
            selectedInstitution,
            sectionAttachments,
            formType,
            incorporateMandatory,
        } = body;
        let resetSection = false;
        let resetApproverLevel = [];
        let resetSteps;
        if (formType) {
            //reset the CK editor
            const formSettingData = await formSettingSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(categoryFormId),
                    },
                    {
                        formType: 1,
                        approvalLevel: 1,
                        step: 1,
                    },
                )
                .lean();
            resetSection = formType != formSettingData?.formType;
            if (resetSection) {
                resetApproverLevel = formSettingData?.approvalLevel.map((approverElement) => {
                    return {
                        ...approverElement,
                        specificSections: [],
                    };
                });
                resetSteps =
                    formSettingData?.step?.filter(
                        (stepElement) => ![2, 3, 4].includes(stepElement),
                    ) ?? [];
            }
        }
        const formCourseBulkWrite = [];
        const formCourseBulkUpdate = [];
        const courseSettingQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryFormId: convertToMongoObjectId(categoryFormId),
            ...(categoryId && { categoryId: convertToMongoObjectId(categoryId) }),
        };
        if (formName) {
            //check the Duplicate formName
            const isDuplicate = await checkDuplicateFormName({
                formName,
                categoryId,
                categoryFormId,
            });
            if (isDuplicate.checkFormNameCount) {
                return { statusCode: 409, message: 'FORM_NAME_ALREADY_EXISTS' };
            }
        }
        const categoryFormData = await formSettingSchema.findByIdAndUpdate(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(categoryFormId),
            },
            {
                $set: {
                    ...(formName && { formName }),
                    ...(describe && { describe }),
                    ...(status && { status }),
                    ...(approvalLevel && approvalLevel.length && { approvalLevel }),
                    ...(attachments && { attachments }),
                    ...(typeof isActive === 'boolean' && { isActive }),
                    ...(typeof archive === 'boolean' && { archive }),
                    ...(typeof isDeleted === 'boolean' && { isDeleted }),
                    ...(typeof incorporateMandatory === 'boolean' && { incorporateMandatory }),
                    ...(selectedProgram && { selectedProgram }),
                    ...(selectedInstitution && { selectedInstitution }),
                    ...(sectionAttachments && { sectionAttachments }),
                    ...(formType && { formType }),
                    ...(resetSection && {
                        sectionAttachments: [],
                        approvalLevel: resetApproverLevel,
                        step: resetSteps,
                    }),
                    updatedAt: new Date(),
                },
                ...(step && {
                    $addToSet: { step: { $each: [step] } },
                }),
            },
        );
        if (!categoryFormData.modifiedCount && selectCourses && !selectCourses.length)
            return { statusCode: 400, message: 'UPDATE_ERROR' };
        let filter;
        let update;
        if (selectCourses && selectCourses.length) {
            selectCourses.forEach((courseElement) => {
                if (courseElement._id) {
                    filter = {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(courseElement._id),
                    };
                    update = {
                        $set: {
                            ...(typeof courseElement.isDeleted === 'boolean' && {
                                isDeleted: courseElement.isDeleted,
                            }),
                            ...(typeof courseElement.isConfigure === 'boolean' && {
                                isConfigure: courseElement.isConfigure,
                            }),
                            ...(typeof courseElement.isEnable === 'boolean' && {
                                isEnable: courseElement.isEnable,
                            }),
                        },
                    };
                    formCourseBulkUpdate.push({
                        updateOne: {
                            filter,
                            update,
                            upsert: true,
                        },
                    });
                } else {
                    formCourseBulkWrite.push({ ...courseElement, ...courseSettingQuery });
                }
            });
        }
        if (formCourseBulkWrite.length) {
            await formSettingCourseSchema.insertMany(formCourseBulkWrite);
        }
        if (formCourseBulkUpdate.length) {
            await formSettingCourseSchema.bulkWrite(formCourseBulkUpdate);
        }
        //updated the status in form setting course
        if (status === PUBLISHED) {
            await updateFormCourseSetting({
                categoryFormId,
                _institution_id,
            });
            await updateCourseGroupStatus({
                categoryFormId,
                _institution_id,
            });
        }
        return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.categoryTypeForm = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { formType, searchKey } = query;
        const pagination = getPaginationValues(query);
        const formCommonQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            ...(searchKey && {
                formName: searchKeyFunction({ searchKey }),
            }),
        };
        //form setting query
        const formSettingQuery = {
            ...formCommonQuery,
            isDeleted: formType !== ARCHIVE,
            archive: formType === ARCHIVE,
        };
        const totalCount = await formSettingSchema.countDocuments(formSettingQuery);
        const categoryFormData = await formSettingSchema
            .find(formSettingQuery, {
                categoryId: 1,
                formName: 1,
                describe: 1,
                status: 1,
                step: 1,
                updatedAt: 1,
                selectedProgram: 1,
                selectedInstitution: 1,
            })
            .populate({ path: 'categoryId', select: { categoryName: 1, categoryFor: 1 } })
            .sort({ updatedAt: -1 })
            .skip(pagination.skip)
            .limit(pagination.limit)
            .lean();
        if (!categoryFormData.length) {
            return {
                statusCode: 404,
                message: 'NO_DATA_FOUND',
                data: { totalCount, categoryFormData },
            };
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalCount,
                categoryFormData,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getFormAttachment = async ({ query = {}, headers = {} }) => {
    try {
        const { categoryFormId } = query;
        const { _institution_id } = headers;
        const formSettingData = await formSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(categoryFormId),
                    isActive: true,
                },
                {
                    'attachments.url': 1,
                    'attachments.name': 1,
                    'sectionAttachments.description': 1,
                    'sectionAttachments.sectionName': 1,
                    'sectionAttachments._id': 1,
                    formType: 1,
                    categoryFormType: 1,
                    formName: 1,
                },
            )
            .lean();
        if (!formSettingData) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: formSettingData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getApprovalHierarchy = async ({ headers = {}, query = {} }) => {
    try {
        const { categoryFormId } = query;
        const { _institution_id } = headers;
        const formSettingData = await formSettingSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(categoryFormId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                },
                {
                    approvalLevel: 1,
                    categoryFormType: 1,
                    formType: 1,
                    'sectionAttachments.sectionName': 1,
                },
            )
            .lean();
        if (!formSettingData) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: {} };
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: formSettingData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.singleFormOccurrence = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormId } = query;
        const formSettingCourseData = await formSettingCourseSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                    sharedWithOthers: 1,
                    sharedFormOthers: 1,
                    courseType: 1,
                    institutionType: 1,
                    isConfigure: 1,
                    isEnable: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: {
                    'course_assigned_details.course_shared_with.program_name': 1,
                    'course_assigned_details.program_name': 1,
                },
            })
            .lean();
        if (!formSettingCourseData) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: formSettingCourseData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//get program wise curriculum
exports.getCurriculum = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId } = query;
        const curriculumData = await curriculamSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _program_id: convertToMongoObjectId(programId),
                },
                {
                    curriculum_name: 1,
                    program_name: 1,
                },
            )
            .lean();
        if (!curriculumData.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: curriculumData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.createGroups = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            categoryId,
            categoryFormId,
            categoryFormCourseIds,
            numberOfGroups,
            selectedType,
            tags,
            actions,
        } = body;
        const groupsQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryFormId: convertToMongoObjectId(categoryFormId),
            categoryId: convertToMongoObjectId(categoryId),
        };
        const categoryFormData = await formSettingCourseSchema.updateMany(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: {
                    $in: categoryFormCourseIds.map((formCourseElement) =>
                        convertToMongoObjectId(formCourseElement),
                    ),
                },
            },
            {
                $set: {
                    ...(numberOfGroups && { numberOfGroups }),
                    ...(tags && { tags }),
                    ...(actions && { actions }),
                    isConfigure: true,
                },
            },
        );
        if (!categoryFormData.modifiedCount && !categoryFormCourseIds.length) {
            return { statusCode: 400, message: 'NOT_SAVED' };
        }
        const groupsBulkWrite = categoryFormCourseIds.flatMap((formCourseElement) => {
            const formCourseQuery = {
                ...groupsQuery,
                categoryFormCourseId: convertToMongoObjectId(formCourseElement),
            };
            return selectedType.map((typeElement) => ({ ...formCourseQuery, ...typeElement }));
        });
        if (groupsBulkWrite.length) {
            await formCourseGroupSchema.insertMany(groupsBulkWrite);
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.formConfigureList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormCourseId } = query;
        const formQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isActive: true,
            isDeleted: false,
        };
        const formCourseSettingData = await formSettingCourseSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(categoryFormCourseId),
                },
                {
                    numberOfGroups: 1,
                    tags: 1,
                    courseType: 1,
                    actions: 1,
                    institutionType: 1,
                },
            )
            .lean();
        const formGroupData = await formCourseGroupSchema
            .find(
                {
                    ...formQuery,
                    categoryFormCourseId: convertToMongoObjectId(categoryFormCourseId),
                },
                {
                    attemptTypeName: 1,
                    term: 1,
                    executionsPer: 1,
                    academicYear: 1,
                    group: 1,
                    groupName: 1,
                    minimum: 1,
                    startMonth: 1,
                    endMonth: 1,
                },
            )
            .lean();
        if (!formGroupData.length) {
            return {
                statusCode: 404,
                message: 'NO_DATA_FOUND',
                data: { formGroupData: [], formCourseSettingData: {} },
            };
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { formGroupData, formCourseSettingData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.editConfigure = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            categoryFormCourseId,
            numberOfGroups,
            tags,
            selectedType,
            categoryFormId,
            categoryId,
            status,
            isConfigure,
            isEnable,
        } = body;
        await formSettingCourseSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(categoryFormCourseId),
            },
            {
                $set: {
                    ...(numberOfGroups && { numberOfGroups }),
                    ...(tags && { tags }),
                    ...(typeof isConfigure === 'boolean' && { isConfigure }),
                    ...(typeof isEnable === 'boolean' && { isEnable }),
                    ...(status && { status }),
                },
            },
        );
        const groupsQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            categoryFormId: convertToMongoObjectId(categoryFormId),
            categoryId: convertToMongoObjectId(categoryId),
            categoryFormCourseId: convertToMongoObjectId(categoryFormCourseId),
        };
        const groupsBulkWrite = [];
        const groupBulkUpdate = [];
        if (selectedType && selectedType.length) {
            selectedType.forEach((typeElement) => {
                if (typeElement._id) {
                    const filter = {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: convertToMongoObjectId(typeElement._id),
                    };
                    const update = {
                        $set: {
                            ...(typeElement.attemptTypeName && {
                                attemptTypeName: typeElement.attemptTypeName,
                            }),
                            ...(typeof typeElement.executionsPer === 'boolean' && {
                                executionsPer: typeElement.executionsPer,
                            }),
                            ...(typeElement.academicYear && {
                                academicYear: typeElement.academicYear,
                            }),
                            ...(typeElement.group && {
                                group: typeElement.group,
                            }),
                            ...(typeElement.groupName && {
                                groupName: typeElement.groupName,
                            }),
                            ...(typeElement.minimum && {
                                minimum: typeElement.minimum,
                            }),
                            ...(typeElement.startMonth && {
                                startMonth: typeElement.startMonth,
                            }),
                            ...(typeElement.endMonth && {
                                endMonth: typeElement.endMonth,
                            }),
                            ...(typeof typeElement.isDeleted === 'boolean' && {
                                isDeleted: typeElement.isDeleted,
                            }),
                        },
                    };
                    groupBulkUpdate.push({
                        updateOne: {
                            filter,
                            update,
                            upsert: true,
                        },
                    });
                } else {
                    groupsBulkWrite.push({ ...groupsQuery, ...typeElement });
                }
            });
        }
        if (groupBulkUpdate.length) {
            await formCourseGroupSchema.bulkWrite(groupBulkUpdate);
        }
        if (groupsBulkWrite.length) {
            await formCourseGroupSchema.insertMany(groupsBulkWrite);
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.concludingPhase = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryFormId } = query;
        const formCourseSettingData = await formSettingCourseSchema
            .find(
                {
                    categoryFormId: convertToMongoObjectId(categoryFormId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                    isConfigure: true,
                    isEnable: true,
                },
                {
                    programId: 1,
                    programName: 1,
                    curriculumId: 1,
                    curriculumName: 1,
                    year: 1,
                    courseId: 1,
                    courseName: 1,
                    courseCode: 1,
                    courseType: 1,
                    sharedWithOthers: 1,
                    sharedFormOthers: 1,
                    assignedInstitutionId: 1,
                    institutionName: 1,
                },
            )
            .lean();
        //get approval history
        const formSettingData = await formSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(categoryFormId),
                    isActive: true,
                },
                {
                    'approvalLevel.turnAroundTime': 1,
                    'approvalLevel.name': 1,
                    'approvalLevel.category': 1,
                    attachments: 1,
                    templateId: 1,
                    'approvalLevel.specificSections': 1,
                },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { formCourseSettingData, formSettingData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getInstitution = async () => {
    try {
        const institutionsData = await institutionsSchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                },
                {
                    name: 1,
                    code: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        if (!institutionsData.length) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: institutionsData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.termList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId } = query;
        const programTermData = await programSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: convertToMongoObjectId(programId),
                },
                { 'term.term_name': 1 },
            )
            .lean();
        if (!programTermData) {
            return { statusCode: 404, message: 'NO_DATA_FOUND', data: [] };
        }
        const termName = programTermData.term.map((termElement) => {
            return termElement.term_name;
        });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: termName };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.resetFormCourseSetting = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { categoryId } = query;
        const formSettingData = await getDraftFormSetting({ _institution_id, categoryId });
        const formIds = formSettingData.formSettingIds.map((formSettingElement) =>
            convertToMongoObjectId(formSettingElement._id),
        );
        if (formIds.length) {
            await formSettingCourseSchema.updateMany(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: convertToMongoObjectId(categoryId),
                    categoryFormId: { $in: formIds },
                    status: DRAFT,
                    isActive: true,
                    isDeleted: false,
                },
                {
                    $set: {
                        isConfigure: false,
                    },
                },
            );
            //deleted category group data
            await formCourseGroupSchema.updateMany(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    categoryId: convertToMongoObjectId(categoryId),
                    categoryFormId: { $in: formIds },
                    isActive: true,
                    isDeleted: false,
                },
                {
                    $set: {
                        isDeleted: true,
                    },
                },
            );
        }
        return { statusCode: 200, message: 'SAVED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

//file upload
exports.qapcGuideResources = async ({ body = {} }) => {
    try {
        const { urls } = body;
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: urls };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.qapcSignedUrl = async ({ query = {} }) => {
    try {
        const { url } = query;
        const fileName = url.split('/').pop();
        const signedUrl = await getS3SignedUrl({
            bucket: BUCKET_QAPC_DOCUMENT,
            key: fileName,
        });
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: signedUrl };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

exports.unPublishedForm = async ({ query = {} }) => {
    try {
        const { categoryFormId } = query;
        const isUnpublished = await isCreatedFromInitiator({ categoryFormId });
        if (!isUnpublished) {
            await formSettingSchema.updateOne(
                {
                    _id: convertToMongoObjectId(categoryFormId),
                },
                {
                    $set: { status: UNPUBLISHED },
                },
            );
            return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
        }
        return {
            statusCode: 200,
            message:
                'FORM_INITIATOR_HAS_STARTED_PROCESSING_EDITING_THE_PUBLISHED_SETTING_IS_NO_LONGER_ALLOWED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};

exports.getAllInstitutionCalender = async () => {
    try {
        const institutionCalenderData = await institutionCalenderSchema
            .find(
                {
                    calendar_type: PRIMARY,
                    isDeleted: false,
                    $or: [
                        {
                            status: PUBLISHED,
                        },
                        {
                            end_date: { $gt: new Date() },
                            status: { $exists: false },
                        },
                    ],
                },
                {
                    _id: 1,
                    calendar_name: 1,
                    start_date: 1,
                    end_date: 1,
                    isActive: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        if (!institutionCalenderData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: institutionCalenderData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error();
        }
    }
};
