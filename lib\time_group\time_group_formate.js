module.exports = {

    time_group: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                type: element.type,
                start_time: element.start_time, 
                end_time: element.end_time, 
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    time_group_ID: (doc) => {
        let obj = {
            _id: doc._id,
            type: doc.type,
            start_time: doc.start_time, 
            end_time: doc.end_time, 
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    }
}