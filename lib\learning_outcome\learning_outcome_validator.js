const Joi = require('joi');

function individualReportSchema() {
    const schema = {
        query: Joi.object().keys({
            programId: Joi.string().length(24),
            institutionCalendarId: Joi.string().length(24),
            courseId: Joi.string().length(24),
            term: Joi.string(),
            year: Joi.string(),
            rotation: Joi.string(),
            rotationCount: Joi.string(),
        }),
    };
    return schema;
}

function selfEvaluationSurveyReportSchema() {
    const schema = {
        query: Joi.object().keys({
            programId: Joi.string().length(24),
            institutionCalendarId: Joi.string().length(24),
            courseId: Joi.string().length(24),
            term: Joi.string(),
            year: Joi.string(),
            levelNo: Joi.string(),
            rotation: Joi.string(),
            rotationCount: Joi.string(),
            tab: Joi.string().valid(...['session', 'slo', 'clo']),
        }),
    };
    return schema;
}

module.exports = {
    individualReportSchema: individualReportSchema(),
    selfEvaluationSurveyReportSchema: selfEvaluationSurveyReportSchema(),
};
