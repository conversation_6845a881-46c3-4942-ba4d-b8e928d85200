const flatCache = require('flat-cache');
const path = require('path');
const { convertToMongoObjectId } = require('../lib/utility/common');
const {
    INSTITUTION_CALENDAR,
    PUBLISHED,
    PROGRAM_CALENDAR,
    DIGI_SESSION_ORDER,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    STUDENT_GROUP,
    DIGI_SESSION_DELIVERY_TYPES,
    DIGI_DEPARTMENT_SUBJECT,
    DIGI_COURSE,
    ROLE_ASSIGN,
    COURSE_SCHEDULE,
    ACTIVITIES,
    LMS,
    COMPLETED,
} = require('../lib/utility/constants');
const { logger } = require('../lib/utility/util_keys');
const institutionCalendar = require('mongoose').model(INSTITUTION_CALENDAR);
const programCalendar = require('mongoose').model(PROGRAM_CALENDAR);
const sessionOrder = require('mongoose').model(DIGI_SESSION_ORDER);
const program = require('mongoose').model(DIGI_PROGRAM);
const curriculum = require('mongoose').model(DIGI_CURRICULUM);
const studentGroup = require('mongoose').model(STUDENT_GROUP);
const sessionDeliveryType = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const departmentSubject = require('mongoose').model(DIGI_DEPARTMENT_SUBJECT);
const course = require('mongoose').model(DIGI_COURSE);
const roleAssign = require('mongoose').model(ROLE_ASSIGN);
const courseSchedule = require('mongoose').model(COURSE_SCHEDULE);
const lmsSchema = require('mongoose').model(LMS);
const userSetting = require('mongoose').model('dashboard_setting');
const ProgramReportSettings = require('../lib/models/program_report_setting');

//const activity = require('mongoose').model('activities');
const Activity = require('../lib/models/activities');
const questions = require('../lib/models/question');

const cacheFolderPath = './public/.cache';

const cacheDate = ({ cacheId, key, data }) => {
    const cache = flatCache.load(key, path.resolve(cacheFolderPath));
    cache.setKey(cacheId.toString(), JSON.stringify(data));
    cache.save(); // very important, if you don't save no changes will be persisted.
};

const getProgramCalendar = async (institutionCalendarId) => {
    let cache = flatCache.load('programCalendar', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('programCalendar');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> getProgramCalendar --> Get All Program Calendar based institution calendar from DB',
        );
        const pcData = await programCalendar
            .find({
                //_institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                isDeleted: false,
                status: PUBLISHED,
            })
            .lean();
        const pcCourses = [];
        for (programCalendarElement of pcData) {
            for (levelElement of programCalendarElement.level) {
                if (levelElement.rotation === 'no')
                    for (courseElement of levelElement.course) {
                        pcCourses.push({
                            _program_id: programCalendarElement._program_id.toString(),
                            term: levelElement.term,
                            year: levelElement.year,
                            level: levelElement.level_no,
                            rotation: levelElement.rotation,
                            _id: courseElement._course_id,
                            _course_id: courseElement._course_id,
                            course_name: courseElement.courses_name,
                            course_number: courseElement.courses_number,
                            course_type: courseElement.model,
                            start_date: courseElement.start_date,
                            end_date: courseElement.end_date,
                            _institution_calendar_id:
                                programCalendarElement._institution_calendar_id,
                        });
                    }
                else
                    for (rotationCourseElement of levelElement.rotation_course) {
                        for (courseElement of rotationCourseElement.course) {
                            pcCourses.push({
                                _program_id: programCalendarElement._program_id.toString(),
                                term: levelElement.term,
                                year: levelElement.year,
                                level: levelElement.level_no,
                                rotation: levelElement.rotation,
                                rotation_count: rotationCourseElement.rotation_count,
                                _id: courseElement._course_id,
                                _course_id: courseElement._course_id,
                                course_name: courseElement.courses_name,
                                course_number: courseElement.courses_number,
                                course_type: courseElement.model,
                                start_date: courseElement.start_date,
                                end_date: courseElement.end_date,
                                _institution_calendar_id:
                                    programCalendarElement._institution_calendar_id,
                            });
                        }
                    }
            }
        }
        cacheDate({
            cacheId: 'programCalendar',
            key: 'programCalendar',
            data: pcCourses,
        });
        cache = flatCache.load('programCalendar', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('programCalendar');
    } else {
        logger.info(
            'service --> cache.service --> getProgramCalendar --> Get cached Program Calendar based institution calendar',
        );
    }
    return cachedData ? JSON.parse(cachedData) : undefined;
};

const getInstitutionCalendar = async () => {
    let cache = flatCache.load('institutionCalendar', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('institutionCalendar');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> getInstitutionCalendar --> Get Institution Calendar from DB',
        );
        const icData = await institutionCalendar
            .findOne(
                { isDeleted: false, status: PUBLISHED },
                {
                    _id: 1,
                    calendar_name: 1,
                    start_date: 1,
                    end_date: 1,
                },
            )
            .sort({ _id: -1 })
            .limit(1)
            .lean();
        cacheDate({
            cacheId: 'institutionCalendar',
            key: 'institutionCalendar',
            data: icData,
        });
        cache = flatCache.load('institutionCalendar', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('institutionCalendar');
    } else {
        logger.info(
            'service --> cache.service --> getInstitutionCalendar --> Get cached Institution Calendar',
        );
    }
    return cachedData ? JSON.parse(cachedData) : undefined;
};

const getCourseSessionOrder = async (courseId) => {
    let cache = flatCache.load(courseId.toString(), path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('sessionOrder');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> getCourseSessionOrder --> Get Course Session Order from DB',
        );
        const soData = await sessionOrder
            .findOne({ isDeleted: false, _course_id: convertToMongoObjectId(courseId) }, {})
            .lean();
        cacheDate({
            cacheId: 'sessionOrder',
            key: courseId.toString(),
            data: soData,
        });
        cache = flatCache.load(courseId.toString(), path.resolve(cacheFolderPath));
        cachedData = cache.getKey('sessionOrder');
    } else {
        logger.info(
            'service --> cache.service --> getCourseSessionOrder --> Get cached Course Session Order',
        );
    }
    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allProgramDatas = async () => {
    let cache = flatCache.load('allProgram', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allProgram');
    if (!cachedData) {
        logger.info('service --> cache.service --> allProgramDatas --> Get All Programs from DB');
        const programData = await program
            .find(
                {
                    isDeleted: false /* , _institution_id: convertToMongoObjectId(_institution_id) */,
                },
                {
                    name: 1,
                    code: 1,
                    program_type: 1,
                    type: 1,
                    no_term: 1,
                    term: 1,
                },
            )
            .lean();
        cacheDate({
            cacheId: 'allProgram',
            key: 'allProgram',
            data: programData,
        });
        cache = flatCache.load('allProgram', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allProgram');
    } else
        logger.info(
            'service --> cache.service --> allProgramDatas --> Get All Programs from Local',
        );
    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allCurriculumDatas = async () => {
    let cache = flatCache.load('allCurriculum', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allCurriculum');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allCurriculumDatas --> Get All Curriculums from DB',
        );
        const curriculumData = await curriculum.find({ isDeleted: false }, {}).lean();
        cacheDate({
            cacheId: 'allCurriculum',
            key: 'allCurriculum',
            data: curriculumData,
        });
        cache = flatCache.load('allCurriculum', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allCurriculum');
    } else
        logger.info(
            'service --> cache.service --> allCurriculumDatas --> Get All Curriculums from Local',
        );

    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allProgramCalendarDatas = async () => {
    let cache = flatCache.load('allProgramCalendar', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allProgramCalendar');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allProgramCalendarDatas --> Get All Program Calendar from DB',
        );
        const programData = await programCalendar
            .find(
                {
                    // _program_id: { $in: programIds },
                    // _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {},
            )
            .lean();
        cacheDate({
            cacheId: 'allProgramCalendar',
            key: 'allProgramCalendar',
            data: programData,
        });
        cache = flatCache.load('allProgramCalendar', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allProgramCalendar');
    } else
        logger.info(
            'service --> cache.service --> allProgramCalendarDatas --> Get All Programs from Local',
        );

    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allSessionDeliveryTypesDatas = async () => {
    let cache = flatCache.load('allSessionDeliveryTypes', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allSessionDeliveryTypes');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allSessionDeliveryTypesDatas --> Get All Session Delivery Types from DB',
        );
        const dbData = await sessionDeliveryType
            .find({
                // _program_id: { $in: programIds },
                // _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            })
            .lean();
        cacheDate({
            cacheId: 'allSessionDeliveryTypes',
            key: 'allSessionDeliveryTypes',
            data: dbData,
        });
        cache = flatCache.load('allSessionDeliveryTypes', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allSessionDeliveryTypes');
    } else
        logger.info(
            'service --> cache.service --> allSessionDeliveryTypesDatas --> Get All Session Delivery Types from Local',
        );

    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allSessionOrderDatas = async () => {
    let cache = flatCache.load('allSessionOrders', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allSessionOrders');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allSessionOrderDatas --> Get All Session Order Datas from DB',
        );
        const dbData = await sessionOrder
            .find({
                // _program_id: { $in: programIds },
                // _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            })
            .lean();
        cacheDate({
            cacheId: 'allSessionOrders',
            key: 'allSessionOrders',
            data: dbData,
        });
        cache = flatCache.load('allSessionOrders', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allSessionOrders');
    } else
        logger.info(
            'service --> cache.service --> allSessionOrderDatas --> Get All Session Order Datas from Local',
        );

    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allRoleAssignDatas = async (userId) => {
    const keyData = `${userId.toString()}-userRoleAssign`;
    let cache = flatCache.load(keyData.toString(), path.resolve(cacheFolderPath));
    let cachedData = cache.getKey(keyData);
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allRoleAssignDatas --> Get User role assign from DB',
        );
        const curriculumData = await roleAssign
            .findOne({ isDeleted: false, _user_id: convertToMongoObjectId(userId) }, {})
            .lean();
        cacheDate({
            cacheId: keyData,
            key: keyData,
            data: curriculumData,
        });
        cache = flatCache.load(keyData.toString(), path.resolve(cacheFolderPath));
        cachedData = cache.getKey(keyData.toString());
    } else
        logger.info(
            'service --> cache.service --> allRoleAssignDatas --> Get User role assign from Local',
        );

    return cachedData ? JSON.parse(cachedData) : undefined;
};

const allCourseScheduleTillYesterday = async (institutionCalendarId, todayDate) => {
    let cache = flatCache.load('allCourseSchedule', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey(todayDate);
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allCourseScheduleDatas --> Get All Course Schedule Datas from DB',
        );
        const scheduleQuery = {
            type: 'regular',
            // isActive: true,
            isDeleted: false,
        };
        /*TODO- add object institution calender id based store changes
         if (institutionCalendarId && institutionCalendarId.length === 24)
             scheduleQuery._institution_calendar_id = convertToMongoObjectId(institutionCalendarId); */
        const dbData = await courseSchedule.find(scheduleQuery, {}).lean();
        cacheDate({
            cacheId: todayDate,
            key: 'allCourseSchedule',
            data: dbData,
        });
        cache = flatCache.load('allCourseSchedule', path.resolve(cacheFolderPath));
        cachedData = cache.getKey(todayDate);
    } else
        logger.info(
            'service --> cache.service --> allCourseScheduleDatas --> Get All Course Schedule Datas from Local',
        );

    return cachedData ? JSON.parse(cachedData) : [];
};

const allStudentGroupYesterday = async (institutionCalendarId, todayDate) => {
    let cache = flatCache.load('allStudentGroup', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey(todayDate);
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allStudentGroupYesterday --> Get All Student Group Datas from DB',
        );
        const sgQuery = {
            isDeleted: false,
        };
        /*TODO- add object institution calender id based store changes
        if (institutionCalendarId && institutionCalendarId.length === 24)
        sgQuery._institution_calendar_id = convertToMongoObjectId(institutionCalendarId);*/
        const dbData = await studentGroup.find(sgQuery).lean();
        cacheDate({
            cacheId: todayDate,
            key: 'allStudentGroup',
            data: dbData,
        });
        cache = flatCache.load('allStudentGroup', path.resolve(cacheFolderPath));
        cachedData = cache.getKey(todayDate);
    } else
        logger.info(
            'service --> cache.service --> allStudentGroupYesterday --> Get All Student Group Datas from Local',
        );

    return cachedData ? JSON.parse(cachedData) : [];
};

const allCourseList = async () => {
    let cache = flatCache.load('allCourses', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allCourses');
    if (!cachedData) {
        logger.info('service --> cache.service --> allCourseList --> Get All Course Datas from DB');
        const dbData = await course.find({ isDeleted: false }).lean();
        cacheDate({
            cacheId: 'allCourses',
            key: 'allCourses',
            data: dbData,
        });
        cache = flatCache.load('allCourses', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allCourses');
    } else
        logger.info(
            'service --> cache.service --> allCourseList --> Get All Course Datas from Local',
        );

    return cachedData ? JSON.parse(cachedData) : [];
};

const allDepartmentSubjectList = async () => {
    let cache = flatCache.load('allDepartmentSubjects', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allDepartmentSubjects');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allDepartmentSubjectList --> Get All Department Subject Datas from DB',
        );
        const dbData = await departmentSubject.find({ isDeleted: false }).lean();
        cacheDate({
            cacheId: 'allDepartmentSubjects',
            key: 'allDepartmentSubjects',
            data: dbData,
        });
        cache = flatCache.load('allDepartmentSubjects', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allDepartmentSubjects');
    } else
        logger.info(
            'service --> cache.service --> allDepartmentSubjectList --> Get All Department Subject Datas from Local',
        );

    return cachedData ? JSON.parse(cachedData) : [];
};

const allActivities = async (activityQuery) => {
    let cache = flatCache.load('allActivities', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allActivities');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allActivities --> Get All allActivities Datas from DB',
        );
        const dbData = await Activity.find(activityQuery).lean();
        cacheDate({
            cacheId: 'allActivities',
            key: 'allActivities',
            data: dbData,
        });
        cache = flatCache.load('allActivities', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allActivities');
    } else
        logger.info(
            'service --> cache.service --> allActivitiesList --> Get All Activities Datas from Local',
        );

    return cachedData ? JSON.parse(cachedData) : [];
};

const allQuestions = async (query) => {
    let cache = flatCache.load('allQuestions', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('allQuestions');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> allQuestions --> Get All Questions Datas from DB',
        );
        const dbData = await questions.find(query).lean();
        cacheDate({
            cacheId: 'allQuestions',
            key: 'allQuestions',
            data: dbData,
        });
        cache = flatCache.load('allQuestions', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('allQuestions');
    } else
        logger.info(
            'service --> cache.service --> allQuestionsList --> Get All Questions Datas from Local',
        );
    return cachedData ? JSON.parse(cachedData) : [];
};

const lmsData = async () => {
    let cache = flatCache.load('lmsData', path.resolve(cacheFolderPath));
    let cachedData = cache.getKey('lmsData');
    if (!cachedData) {
        logger.info('service --> cache.service --> lmsData --> Get All LMS Datas from DB');
        const dbData = await lmsSchema.find({ isDeleted: false }).lean();
        cacheDate({
            cacheId: 'lmsData',
            key: 'lmsData',
            data: dbData,
        });
        cache = flatCache.load('lmsData', path.resolve(cacheFolderPath));
        cachedData = cache.getKey('lmsData');
    } else logger.info('service --> cache.service --> lmsData --> Get All LMS Datas from Local');

    return cachedData ? JSON.parse(cachedData) : [];
};

const userDashboardSettingDatas = async (userId) => {
    const keyData = `${userId.toString()}-userSetting`;
    let cache = flatCache.load(keyData.toString(), path.resolve(cacheFolderPath));
    let cachedData = cache.getKey(keyData);
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> userDashboardSettingDatas --> Get User Dashboard Setting from DB',
        );
        const dbData = await userSetting
            .find({ _user_id: convertToMongoObjectId(userId), 'settings.isConfigured': true }, {})
            .lean();
        cacheDate({
            cacheId: keyData,
            key: keyData,
            data: dbData,
        });
        cache = flatCache.load(keyData.toString(), path.resolve(cacheFolderPath));
        cachedData = cache.getKey(keyData.toString());
    } else
        logger.info(
            'service --> cache.service --> userDashboardSettingDatas --> Get User Dashboard Setting from Local',
        );

    return cachedData ? JSON.parse(cachedData) : undefined;
};

const clearItem = (idToClear) => {
    logger.info('service --> cache.service --> clearItem --> Removing ' + idToClear);
    console.log(flatCache.clearCacheById(idToClear.toString(), path.resolve(cacheFolderPath))); // removes the cacheId document if one exists.
};

// if cache event id value exist check
const getCacheValueWithEventId = (cacheEventId, cacheEventKey) => {
    const cache = flatCache.load(cacheEventId, path.resolve(cacheFolderPath));
    const cacheEventKeyEntry = cacheEventKey || cacheEventId;
    const cachedData = cache.getKey(cacheEventKeyEntry);
    return cachedData;
};

// set cache value in cache event id
const setCacheValue = (data, cacheEventId, cacheEventKey) => {
    const cacheEventKeyEntry = cacheEventKey || cacheEventId;
    cacheDate({
        cacheId: cacheEventId,
        key: cacheEventKeyEntry,
        data,
    });
    const cache = flatCache.load(cacheEventId, path.resolve(cacheFolderPath));
    const cachedData = cache.getKey(cacheEventKeyEntry);
    return cachedData;
};
const clearAllItem = () => {
    logger.info('service --> cache.service --> clearAllItem --> Removing All');
    console.log(flatCache.clearAll(path.resolve(cacheFolderPath))); // remove the all documents
};

const allProgramReportSetting = async () => {
    let cachedData = getCacheValueWithEventId('programReportSettings');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> getProgramReportSettings --> Get All Program report settings from DB',
        );
        const programReportSettings = await ProgramReportSettings.find(
            {},
            { createdAt: -1, updatedAt: -1 },
        ).lean();
        cachedData = setCacheValue(programReportSettings, 'programReportSettings', '');
    } else {
        logger.info(
            'service --> cache.service --> getProgramReportSettings --> Get cached Program Report settings',
        );
    }
    return cachedData ? JSON.parse(cachedData) : undefined;
};

// completed activities
const allCompletedActivities = async (institutionCalendarId) => {
    let cachedDataCheck = getCacheValueWithEventId('getAllCompletedActivities');
    if (!cachedDataCheck || !cachedDataCheck.length) {
        logger.info(
            'service --> cache.service --> getAllCompletedActivities --> Get All Completed Activities from DB',
        );
        const Activities = await Activity.find({
            isDeleted: false,
            isActive: true,
            status: COMPLETED,
            /*TODO- add object institution calender id based store changes
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId), */
        }).lean();
        cachedDataCheck = setCacheValue(Activities, 'getAllCompletedActivities', '');
    } else {
        logger.info(
            'service --> cache.service --> getAllCompletedActivities --> Get cached Activities',
        );
    }
    return cachedDataCheck ? JSON.parse(cachedDataCheck) : [];
};

const getProgramReportSetting = async (programId, institutionId) => {
    let cachedData = getCacheValueWithEventId('programReportSettings');
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> getProgramReportSettings --> Get All Program report settings from DB',
        );
        const programReportSettings = await ProgramReportSettings.find(
            {},
            { createdAt: -1, updatedAt: -1 },
        ).lean();
        cachedData = setCacheValue(programReportSettings, 'programReportSettings', '');
    } else {
        cachedData = cachedData.find(
            (cacheData) =>
                cacheData._program_id.toString() === programId.toString() &&
                cacheData._institution_id.toString() === institutionId.toString(),
        );
    }
    return cachedData ? JSON.parse(cachedData) : undefined;
};
module.exports = {
    cacheDate,
    getProgramCalendar,
    getInstitutionCalendar,
    getCourseSessionOrder,
    lmsData,
    userDashboardSettingDatas,
    clearItem,
    clearAllItem,
    getProgramReportSetting,
    // Report Datas
    allProgramDatas,
    allCurriculumDatas,
    allCourseList,
    allProgramCalendarDatas,
    allSessionDeliveryTypesDatas,
    allSessionOrderDatas,
    allRoleAssignDatas,
    allCourseScheduleTillYesterday,
    allStudentGroupYesterday,
    allDepartmentSubjectList,
    allProgramReportSetting,
    allActivities,
    allQuestions,
    allCompletedActivities,
};
