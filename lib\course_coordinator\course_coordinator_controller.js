const {
    responseFunctionWithRequest,
    convertToMongoObjectId,
    axiosCall,
} = require('../utility/common');
const {
    get_list,
    get,
    update_condition,
    update_push_pull_many,
    insert,
    update,
} = require('../base/base_controller');
const {
    INSTITUTION,
    PROGRAM_CALENDAR,
    DIGI_COURSE,
    USER,
    ROLE,
    ROLE_ASSIGN,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
} = require('../utility/constants');
const {
    nameFormatter,
    send_email,
    emailGreetingContent,
    emailRegardsContent,
} = require('../utility/common_functions');

const institution = require('mongoose').model(INSTITUTION);
const program_calendar = require('mongoose').model(PROGRAM_CALENDAR);
const course = require('mongoose').model(DIGI_COURSE);
const user = require('mongoose').model(USER);
const role = require('mongoose').model(ROLE);
const role_assign = require('mongoose').model(ROLE_ASSIGN);
const digi_curriculum = require('mongoose').model(DIGI_CURRICULUM);
const program = require('mongoose').model(DIGI_PROGRAM);
const { course_coordinator } = require('../utility/common_datas');
const { clearItem, allCourseList } = require('../../service/cache.service');
const { INSTITUTION_NAME } = require('../utility/util_keys');

// Updating Course Flat Caching Data
const updateCourseFlatCacheData = async () => {
    clearItem('allCourses');
    await allCourseList();
};

// Get Program -> Level wise Course Course Coordinators
exports.getCourseCourseCoordinators = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { program, institution_calendar },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { status: pc_status, data: pc_data } = await get(
            program_calendar,
            {
                _program_id: convertToMongoObjectId(program),
                _institution_calendar_id: convertToMongoObjectId(institution_calendar),
            },
            {},
        );
        //return res.send(pc_data);
        if (!pc_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );
        const { status: u_status, data: u_data } = await get_list(
            user,
            {
                user_type: 'staff',
                'academic_allocation.allocation_type': 'primary',
            },
            {
                email: 1,
                name: 1,
                'academic_allocation.$': 1,
                academicCourses: 1,
            },
        );
        if (!u_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STAFFS_NOT_FOUND'),
                        req.t('STAFFS_NOT_FOUND'),
                    ),
                );

        const year_level = [];
        let course_ids = [];
        let term = pc_data.level.map((item) => item.term);
        for (year_element of pc_data.level) {
            let courses = [];
            if (year_element.rotation === 'no') {
                courses = year_element.course.map((i) => {
                    return {
                        _course_id: i._course_id,
                        courses_name: i.courses_name,
                        courses_number: i.courses_number,
                    };
                });
                course_ids = course_ids.concat(
                    year_element.course.map((i) => convertToMongoObjectId(i._course_id)),
                );
            } else {
                for (rotationElement of year_element.rotation_course) {
                    courses = [
                        ...courses,
                        ...rotationElement.course.map((i) => {
                            return {
                                _course_id: i._course_id,
                                courses_name: i.courses_name,
                                courses_number: i.courses_number,
                            };
                        }),
                    ];
                    course_ids = course_ids.concat(
                        rotationElement.course.map((i) => convertToMongoObjectId(i._course_id)),
                    );
                }
                courses = courses.filter(
                    (item, index) =>
                        courses.findIndex(
                            (ele) => ele._course_id.toString() === item._course_id.toString(),
                        ) === index,
                );
            }
            if (
                year_level.indexOf(
                    (i) => i.level_no.toString() === year_element.level_no.toString(),
                ) === -1
            )
                year_level.push({
                    year: year_element.year,
                    level_no: year_element.level_no,
                    term: year_element.term,
                    curriculum: year_element.curriculum,
                    courses,
                });
        }
        course_ids = [...new Set(course_ids)];
        term = [...new Set(term)];
        const { status: c_status, data: c_data } = await get_list(
            course,
            {
                _id: { $in: course_ids },
                isActive: true,
                isDeleted: false,
            },
            {
                course_name: 1,
                course_code: 1,
                participating: 1,
                administration: 1,
                course_assigned_details: 1,
                coordinators: 1,
                versionNo: 1,
                versioned: 1,
                versionName: 1,
                versionedFrom: 1,
                versionedCourseIds: 1,
            },
        );
        if (!c_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSES_NOT_FOUND'),
                        req.t('COURSES_NOT_FOUND'),
                    ),
                );
        const level_course_data = [];
        // return res.send(year_level);
        year_level.forEach((i) => {
            i.courses.forEach((course_element) => {
                const course_ind = c_data.findIndex(
                    (ind) => ind._id.toString() === course_element._course_id.toString(),
                );
                const userCourse = c_data.find(
                    (courseData) =>
                        courseData._id.toString() === course_element._course_id.toString(),
                );
                let coordinator_data = {};
                if (userCourse) {
                    coordinator_data = userCourse.coordinators.find(
                        (coordinator) =>
                            coordinator._institution_calendar_id.toString() ===
                                institution_calendar.toString() &&
                            coordinator.term === i.term &&
                            i.level_no.toString() === coordinator.level_no.toString(),
                    );
                }
                level_course_data.push({
                    year: i.year,
                    level_no: i.level_no,
                    term: i.term,
                    curriculum: i.curriculum,
                    _course_id: course_element._course_id,
                    courses_name: course_element.courses_name,
                    courses_number: course_element.courses_number,
                    versionNo: c_data[course_ind].versionNo || 1,
                    versioned: c_data[course_ind].versioned || false,
                    versionName: c_data[course_ind].versionName || '',
                    versionedFrom: c_data[course_ind].versionedFrom || null,
                    versionedCourseIds: c_data[course_ind].versionedCourseIds || [],
                    administration: c_data[course_ind].administration,
                    coordinators: coordinator_data,
                });
            });
        });
        level_course_data.forEach((course_element, index) => {
            const course_users = [];
            u_data.forEach((user_element) => {
                if (user_element?.academicCourses?.length) {
                    user_element.academicCourses.forEach((courseElement) => {
                        courseElement.courseIds.forEach((courseIdElement) => {
                            if (
                                courseIdElement.toString() === course_element._course_id.toString()
                            ) {
                                course_users.push({
                                    _id: user_element._id,
                                    name: user_element.name,
                                    email: user_element.email,
                                    course_id: courseIdElement,
                                });
                            }
                        });
                    });
                }
                user_element.academic_allocation.forEach((ele1) => {
                    if (
                        course_users.findIndex(
                            (ele2) => ele2._id.toString() === user_element._id.toString(),
                        ) === -1 &&
                        /* ele1._program_id.toString() ===
                            course_element.administration._program_id.toString() && */
                        course_element &&
                        course_element.administration &&
                        course_element.administration._subject_id &&
                        ele1._department_subject_id.findIndex(
                            (i) =>
                                i.toString() ===
                                course_element.administration._subject_id.toString(),
                        ) !== -1
                    )
                        course_users.push(user_element);
                });
                // if (
                //     user_element.academic_allocation.findIndex(
                //         (i) =>
                //             i.allocation_type === 'primary' &&
                //             i._program_id.toString() ===
                //                 course_element.administration._program_id.toString(),
                //     ) !== -1
                // )
                //     course_users.push(user_element);
            });
            Object.assign(level_course_data[index], { staffs: course_users });
        });
        ///////////////////////////////////////////////////////
        const courseDetails = await get_list(
            course,
            {
                isActive: true,
                isDeleted: false,
                _program_id: { $ne: convertToMongoObjectId(program) },
            },
            {
                _id: 1,
                course_assigned_details: 1,
            },
        );
        if (!courseDetails.status) courseDetails.data = [];
        // return res
        //     .status(404)
        //     .send(
        //         response_function(
        //             res,
        //             404,
        //             false,
        //             req.t('COURSES_NOT_FOUND'),
        //             req.t('COURSES_NOT_FOUND'),
        //         ),
        //     );
        //return res.send(courseDetails);
        for (let j = 0; j < level_course_data.length; j++) {
            const cInd = courseDetails.data.findIndex(
                (eleCD) =>
                    eleCD._id &&
                    eleCD._id.toString() === level_course_data[j]._course_id.toString(),
            );
            if (cInd == -1) {
                level_course_data[j].isShared = false;
                continue;
            }
            let csInd = -1;
            for (let k = 0; k < courseDetails.data[cInd].course_assigned_details.length; k++) {
                csInd = courseDetails.data[cInd].course_assigned_details[
                    k
                ].course_shared_with.findIndex(
                    (eleCS) =>
                        eleCS.year == level_course_data[j].year &&
                        eleCS.level_no == level_course_data[j].level_no &&
                        eleCS._program_id.toString() == program.toString(),
                );
                if (csInd != -1) break;
            }
            if (csInd == -1) level_course_data[j].isShared = false;
            else level_course_data[j].isShared = true;
        }
        //////////////////////////////////////////////////////
        const response = {
            term,
            courses: level_course_data,
        };
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_COORDINATORS'), response),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, req.t('ERROR'), error.toString()));
    }
};

//Remove Course Coordinator Role
async function removeRole(_institution_id, _user_id, _course_id, term) {
    const role_assign_list = await get(
        role_assign,
        {
            _user_id: convertToMongoObjectId(_user_id),
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
        },
        {},
    );
    const response = { courseAdmin: true };
    if (role_assign_list.status) {
        const courseData = await get_list(
            course,
            {
                'coordinators._user_id': convertToMongoObjectId(_user_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            { coordinators: 1 },
        );
        let userCourseStatus = true;
        if (courseData.status)
            for (courseElement of courseData.data) {
                for (coordinatorElement of courseElement.coordinators) {
                    if (
                        _course_id &&
                        term &&
                        ((_course_id.toString() !== courseElement._id.toString() &&
                            _user_id.toString() === coordinatorElement._user_id.toString()) ||
                            (_course_id.toString() === courseElement._id.toString() &&
                                term !== coordinatorElement.term &&
                                _user_id.toString() === coordinatorElement._user_id.toString()))
                    )
                        userCourseStatus = false;
                }
            }
        if (userCourseStatus) {
            const loc = role_assign_list.data.roles.findIndex(
                (ele) => ele.role_name.toString() === 'Course Coordinator',
            );
            if (loc !== -1) {
                role_assign_list.data.roles.splice(loc, 1);
                const roleRemove = await update_condition(
                    role_assign,
                    {
                        _user_id: convertToMongoObjectId(_user_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isDeleted: false,
                    },
                    { $set: { roles: role_assign_list.data.roles } },
                );
                if (roleRemove.status && courseData.status && courseData.data.length == 1)
                    response.courseAdmin = false;
            }
        }
    }
    return response;
}

// Assign & Change & Remove Course Coordinator
exports.assignCourseCoordinator = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: { _institution_calendar_id, _course_id, term, year, level_no, _user_id },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { status: c_status, data: c_data } = await get(
            course,
            {
                _id: convertToMongoObjectId(_course_id),
                isActive: true,
                isDeleted: false,
            },
            {
                coordinators: 1,
                course_type: 1,
                course_recurring: 1,
                'course_assigned_details.year': 1,
                'course_assigned_details.level_no': 1,
                'course_assigned_details._level_id': 1,
            },
        );
        if (!c_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSES_NOT_FOUND'),
                        req.t('COURSES_NOT_FOUND'),
                    ),
                );
        let response_message = '';
        let rem_user_id = '';
        let courseAdmin = true;
        if (_user_id && _user_id.length === 24) {
            const { status: u_status, data: u_data } = await get(
                user,
                {
                    _id: convertToMongoObjectId(_user_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    name: 1,
                },
            );
            if (!u_status)
                return res
                    .status(404)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('USER_NOT_FOUND'),
                            req.t('USER_NOT_FOUND'),
                        ),
                    );
            if (c_data.coordinators.length !== 0) {
                const term_loc = c_data.coordinators.findIndex(
                    (i) =>
                        i.term === term &&
                        i._institution_calendar_id &&
                        i._institution_calendar_id.toString() ===
                            _institution_calendar_id.toString(),
                );
                if (term_loc !== -1) {
                    await removeRole(
                        _institution_id,
                        c_data.coordinators[term_loc]._user_id,
                        _course_id,
                        term,
                    );
                    if (c_data.course_type === 'selective') {
                        for (courseRecurring of c_data.course_recurring) {
                            const coordinatorTermIndex = c_data.coordinators.findIndex(
                                (i) =>
                                    i.term === term &&
                                    i._institution_calendar_id &&
                                    i._institution_calendar_id.toString() ===
                                        _institution_calendar_id.toString() &&
                                    i.level_no.toString() === courseRecurring.level_no.toString(),
                            );
                            if (coordinatorTermIndex === -1) {
                                const courseCoordinatorYear = c_data.course_assigned_details.find(
                                    (courseAssignElement) =>
                                        courseAssignElement._level_id.toString() ===
                                        courseRecurring._level_id.toString(),
                                );
                                c_data.coordinators.push({
                                    _institution_calendar_id,
                                    term,
                                    year: courseCoordinatorYear ? courseCoordinatorYear.year : '',
                                    level_no: courseRecurring.level_no,
                                    _user_id: u_data._id,
                                    user_name: u_data.name,
                                });
                            } else {
                                const courseCoordinatorYear = c_data.course_assigned_details.find(
                                    (courseAssignElement) =>
                                        courseAssignElement._level_id.toString() ===
                                        courseRecurring._level_id.toString(),
                                );
                                c_data.coordinators[coordinatorTermIndex] = {
                                    _institution_calendar_id,
                                    term,
                                    year: courseCoordinatorYear ? courseCoordinatorYear.year : '',
                                    level_no: courseRecurring.level_no,
                                    _user_id: u_data._id,
                                    user_name: u_data.name,
                                };
                            }
                        }
                    } else {
                        c_data.coordinators[term_loc] = {
                            _institution_calendar_id,
                            term,
                            year,
                            level_no,
                            _user_id: u_data._id,
                            user_name: u_data.name,
                        };
                    }
                } else if (c_data.course_type === 'selective') {
                    for (courseRecurring of c_data.course_recurring) {
                        const coordinatorTermIndex = c_data.coordinators.findIndex(
                            (i) =>
                                i.term === term &&
                                i._institution_calendar_id &&
                                i._institution_calendar_id.toString() ===
                                    _institution_calendar_id.toString() &&
                                i.level_no === courseRecurring.level_no,
                        );
                        if (coordinatorTermIndex === -1) {
                            const courseCoordinatorYear = c_data.course_assigned_details.find(
                                (courseAssignElement) =>
                                    courseAssignElement._level_id.toString() ===
                                    courseRecurring._level_id.toString(),
                            );
                            c_data.coordinators.push({
                                _institution_calendar_id,
                                term,
                                year: courseCoordinatorYear ? courseCoordinatorYear.year : '',
                                level_no: courseRecurring.level_no,
                                _user_id: u_data._id,
                                user_name: u_data.name,
                            });
                        } else {
                            const courseCoordinatorYear = c_data.course_assigned_details.find(
                                (courseAssignElement) =>
                                    courseAssignElement._level_id.toString() ===
                                    courseRecurring._level_id.toString(),
                            );
                            c_data.coordinators[coordinatorTermIndex] = {
                                _institution_calendar_id,
                                term,
                                year: courseCoordinatorYear ? courseCoordinatorYear.year : '',
                                level_no: courseRecurring.level_no,
                                _user_id: u_data._id,
                                user_name: u_data.name,
                            };
                        }
                    }
                } else
                    c_data.coordinators.push({
                        _institution_calendar_id,
                        term,
                        year,
                        level_no,
                        _user_id: u_data._id,
                        user_name: u_data.name,
                    });
                response_message = req.t('COURSE_COORDINATOR_ASSIGNED');
            } else {
                if (c_data.course_type === 'selective') {
                    for (courseRecurring of c_data.course_recurring) {
                        const courseCoordinatorYear = c_data.course_assigned_details.find(
                            (courseAssignElement) =>
                                courseAssignElement._level_id.toString() ===
                                courseRecurring._level_id.toString(),
                        );
                        c_data.coordinators.push({
                            _institution_calendar_id,
                            term,
                            year: courseCoordinatorYear ? courseCoordinatorYear.year : '',
                            level_no: courseRecurring.level_no,
                            _user_id: u_data._id,
                            user_name: u_data.name,
                        });
                    }
                } else
                    c_data.coordinators.push({
                        _institution_calendar_id,
                        term,
                        year,
                        level_no,
                        _user_id: u_data._id,
                        user_name: u_data.name,
                    });
                response_message = req.t('COURSE_COORDINATOR_ASSIGNED');
            }

            //Add Leave Approver Role to User
            //Creating Role and assigning to User
            const regex = new RegExp(['^Course Coordinator$'].join(''), 'i');
            const role_data = await get(
                role,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    name: regex,
                    isDeleted: false,
                    isActive: true,
                },
                {},
            );
            let roleAssignData = {};
            const role_assign_list = await get(
                role_assign,
                {
                    _user_id: convertToMongoObjectId(_user_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                },
                {},
            );
            if (!role_data.status) {
                //Create a new Role and add Course Coordinator is Only module
                const roleData = {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    name: 'Course Coordinator',
                    modules: course_coordinator(),
                };
                const { status: roleCreateStatus, responses: roleCreateData } = await insert(
                    role,
                    roleData,
                );
                if (!roleCreateStatus)
                    return res
                        .status(410)
                        .send(
                            responseFunctionWithRequest(
                                req,
                                410,
                                false,
                                req.t('ERROR_UNABLE_TO_CREATE_NEW_ROLE'),
                                req.t('ERROR_UNABLE_TO_CREATE_NEW_ROLE'),
                            ),
                        );
                roleAssignData = roleCreateData;
            } else {
                roleAssignData = {
                    _id: role_data.data._id,
                    name: role_data.data.name,
                };
            }
            const objs = {
                _institution_id: convertToMongoObjectId(_institution_id),
                _user_id: convertToMongoObjectId(_user_id),
                user_name: u_data.name,
                roles: [
                    {
                        _role_id: convertToMongoObjectId(roleAssignData._id),
                        role_name: roleAssignData.name,
                        program: [],
                        department: [],
                        isDefault: true,
                        isActive: true,
                    },
                ],
            };
            let doc;
            if (!role_assign_list.status) {
                doc = await insert(role_assign, objs);
                await update(user, convertToMongoObjectId(_user_id), {
                    _role_id: convertToMongoObjectId(doc.responses._id),
                });
            } else {
                const roleLoc = role_assign_list.data.roles.findIndex(
                    (ele) =>
                        ele._role_id === convertToMongoObjectId(roleAssignData._id) ||
                        ele.role_name === roleAssignData.name,
                );
                if (roleLoc === -1) {
                    role_assign_list.data.roles.push({
                        _role_id: convertToMongoObjectId(roleAssignData._id),
                        role_name: roleAssignData.name,
                        program: [],
                        department: [],
                    });
                    doc = await update_condition(
                        role_assign,
                        { _user_id: convertToMongoObjectId(_user_id) },
                        { $set: role_assign_list.data },
                    );
                } else doc = { status: true };
            }
            if (!doc.status)
                return res
                    .status(410)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('UNABLE_TO_ASSIGN_ROLE_TO_USER'),
                            doc.data,
                        ),
                    );
        } else {
            if (c_data.coordinators.length !== 0) {
                const term_loc = c_data.coordinators.findIndex((i) => i.term === term);
                if (term_loc !== -1) {
                    const response = await removeRole(
                        _institution_id,
                        c_data.coordinators[term_loc]._user_id,
                        _course_id,
                        term,
                    );
                    rem_user_id = c_data.coordinators[term_loc]._user_id;
                    courseAdmin = response.courseAdmin;
                    //console.log(response);
                    if (c_data.course_type === 'selective') {
                        for (courseRecurring of c_data.course_recurring) {
                            const coordinatorTermIndex = c_data.coordinators.findIndex(
                                (i) =>
                                    i.term === term &&
                                    i._institution_calendar_id &&
                                    i._institution_calendar_id.toString() ===
                                        _institution_calendar_id.toString() &&
                                    i.level_no === courseRecurring.level_no,
                            );
                            if (coordinatorTermIndex !== -1) {
                                c_data.coordinators.splice(coordinatorTermIndex, 1);
                            }
                        }
                    } else {
                        if (term_loc !== -1) {
                            c_data.coordinators.splice(term_loc, 1);
                        }
                    }

                    response_message = req.t('COURSE_COORDINATOR_REMOVED');
                }
            }
        }
        const { status: assign_status } = await update_condition(
            course,
            { _id: convertToMongoObjectId(_course_id) },
            {
                $set: { coordinators: c_data.coordinators },
            },
        );
        updateCourseFlatCacheData();
        if (!assign_status) {
            return res
                .status(410)
                .send(
                    responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('UNABLE_TO_ASSIGN_COURSE_COORDINATOR'),
                        req.t('UNABLE_TO_ASSIGN_COURSE_COORDINATOR'),
                    ),
                );
        }
        const sendSocketData = [];
        let eventId = '';
        if (_user_id && _user_id.length === 24) eventId = _user_id;
        else eventId = rem_user_id;
        data = JSON.stringify({ courseAdmin });
        //console.log(data, eventId);
        sendSocketData.push({ eventId, data });
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, response_message, response_message));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, req.t('ERROR'), error.toString()));
    }
};

// Assign & Change & Remove Course Coordinator
exports.publishCourseCoordinator = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: { _institution_calendar_id, _program_id },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { status: c_status, data: c_data } = await get_list(
            course,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(_program_id),
                // 'coordinators.status': false,
                'coordinators._institution_calendar_id':
                    convertToMongoObjectId(_institution_calendar_id),
                isActive: true,
                isDeleted: false,
            },
            {
                course_name: 1,
                course_code: 1,
                // 'coordinators.$': 1,
                coordinators: 1,
            },
        );
        if (!c_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('COURSES_NOT_FOUND'),
                        req.t('COURSES_NOT_FOUND'),
                    ),
                );
        let courseStaffIds = [];
        for (coordinatorsElement of c_data) {
            courseStaffIds = courseStaffIds.concat(
                // coordinatorsElement.coordinators.filter((ele) => ele.status === false),
                coordinatorsElement.coordinators.map((ele) => {
                    if (
                        ele.status === false &&
                        ele._institution_calendar_id.toString() === _institution_calendar_id
                    ) {
                        return {
                            user_name: ele.user_name,
                            status: ele.status,
                            term: ele.term,
                            _user_id: ele._user_id,
                            email: '',
                            courses: [],
                        };
                    }
                }),
            );
        }
        courseStaffIds = [...new Set(courseStaffIds)];
        courseStaffIds = courseStaffIds.filter(
            (v, i, a) =>
                a.findIndex((t) => t && v && t._user_id.toString() === v._user_id.toString()) === i,
        );
        const staffIds = courseStaffIds.map((ele) => convertToMongoObjectId(ele._user_id));
        const { status: u_status, data: u_data } = await get_list(
            user,
            {
                _id: staffIds,
                isActive: true,
                isDeleted: false,
            },
            {
                email: 1,
            },
        );
        const courseIds = [];
        courseStaffIds.forEach((ele, index) => {
            for (coordinatorsElement of c_data) {
                if (
                    coordinatorsElement.coordinators.findIndex(
                        (ele1) =>
                            ele1._user_id.toString() === ele._user_id.toString() &&
                            ele1.status === false,
                    ) !== -1
                ) {
                    courseIds.push(convertToMongoObjectId(coordinatorsElement._id));
                    courseStaffIds[index].courses.push({
                        _id: coordinatorsElement._id,
                        course_name: coordinatorsElement.course_name,
                        course_code: coordinatorsElement.course_code,
                    });
                    courseStaffIds[index].email = u_status
                        ? u_data.findIndex(
                              (ele2) => ele2._id.toString() === ele._user_id.toString(),
                          ) !== -1
                            ? u_data[
                                  u_data.findIndex(
                                      (ele2) => ele2._id.toString() === ele._user_id.toString(),
                                  )
                              ].email
                            : ''
                        : '';
                }
            }
        });
        for (staffEle of courseStaffIds) {
            let courseContent = '';
            staffEle.courses.forEach((ele) => {
                courseContent += ele.course_name + ' (' + ele.course_code + '), ';
            });
            const mailContent =
                '<p>' +
                req.t('DEAR') +
                ' ' +
                nameFormatter(staffEle.user_name) +
                ',' +
                emailGreetingContent() +
                req.t('YOU_HAVE_BEEN_ASSIGNED_AS_COURSE_COORDINATOR_FOR_') +
                courseContent.substring(0, courseContent.length - 2) +
                req.t('COURSES') +
                ' <br>' +
                emailRegardsContent() +
                '</p>';
            await send_email(staffEle.email, req.t('DIGISCHEDULER_ALERT'), mailContent);
        }
        // const { status: c_update_status } = await update_push_pull_many(
        //     course,
        //     {
        //         _id: { $in: courseIds },
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         _program_id: convertToMongoObjectId(_program_id),
        //         isActive: true,
        //         isDeleted: false,
        //     },
        //     {
        //         $set: {
        //             'coordinators.$[].status': true,
        //         },
        //     },
        // );
        const c_update_status = await course.updateMany(
            {
                _id: { $in: courseIds },
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(_program_id),
                isActive: true,
                isDeleted: false,
            },
            {
                $set: {
                    'coordinators.$[i].status': true,
                },
            },
            {
                arrayFilters: [
                    {
                        'i._institution_calendar_id':
                            convertToMongoObjectId(_institution_calendar_id),
                    },
                ],
            },
        );
        updateCourseFlatCacheData();
        if (!c_update_status)
            return res
                .status(410)
                .send(
                    responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('ALL_COURSE_COORDINATORS_ALREADY_NOTIFIED'),
                        req.t('ALL_COURSE_COORDINATORS_ALREADY_NOTIFIED'),
                    ),
                );
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('EMAIL_PUSHED_TO_COURSE_COORDINATORS'),
                    req.t('EMAIL_PUSHED_TO_COURSE_COORDINATORS'),
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, req.t('ERROR'), error.toString()));
    }
};

exports.getUserProgram = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { userId, roleId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const program_list = await get_list(
            program,
            { _institution_id: convertToMongoObjectId(_institution_id), isDeleted: false },
            {},
        );
        if (!program_list.status)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []));
        const responses = [];
        const curriculum_data = await get_list(
            digi_curriculum,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
                isActive: true,
            },
            {
                curriculum_name: 1,
                _program_id: 1,
            },
        );
        const role_assign_list = await get(
            role_assign,
            {
                _user_id: convertToMongoObjectId(userId),
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
            },
            {},
        );
        if (!role_assign_list.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('USERS_ROLE_NOT_FOUND'),
                        req.t('USERS_ROLE_NOT_FOUND'),
                    ),
                );
        let programIds = program_list.data.map((ele) => ele._id.toString());
        const roleData = role_assign_list.data.roles.find(
            (ele) => ele._role_id.toString() === roleId.toString(),
        );
        // if (roleData.isAdmin) {
        if (roleData /* && roleData.isAdmin  && roleData.name === 'Course Coordinator' */) {
            programIds = roleData.program.map((ele) => ele._program_id.toString());
        }
        if (roleData && roleData.role_name.toString() === 'Course Coordinator') {
            const { status: c_status, data: c_data } = await get_list(
                course,
                {
                    'coordinators._user_id': convertToMongoObjectId(userId),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    _program_id: 1,
                    coordinators: 1,
                    'course_assigned_details._program_id': 1,
                    'course_assigned_details.course_shared_with._program_id': 1,
                },
            );
            if (!c_status)
                return res
                    .status(404)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('COURSES_NOT_FOUND'),
                            req.t('COURSES_NOT_FOUND'),
                        ),
                    );
            programIds = c_data.map((ele) => ele._program_id.toString());
            for (courseData of c_data) {
                for (courseAssigned of courseData.course_assigned_details) {
                    if (courseAssigned.course_shared_with.length !== 0) {
                        for (courseShared of courseAssigned.course_shared_with) {
                            console.log(courseShared);
                            programIds.push(courseShared._program_id);
                        }
                    }
                }
            }
        }
        programIds = [...new Set(programIds)];
        program_list.data.forEach((element) => {
            if (programIds.findIndex((ele) => ele.toString() === element._id.toString()) !== -1) {
                let curriculum_list = [];
                if (curriculum_data.status) {
                    curriculum_list = curriculum_data.data.filter(
                        (i) => i._program_id.toString() == element._id.toString(),
                    );
                }
                responses.push({
                    ...element.toObject(),
                    curriculum: curriculum_list,
                });
            }
        });
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), responses));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, req.t('ERROR'), error.toString()));
    }
};
