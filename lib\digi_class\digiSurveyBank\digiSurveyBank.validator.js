const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const optionalObjectId = Joi.string().alphanum().length(24);

exports.getSurveyBankListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            publishLevel: Joi.string()
                .required()
                .error(() => 'PUBLISH LEVEL IS REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getSurveyTemplateValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getSingleProgramDetailValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.updateSurveyTemplateValidator = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            updateId: objectId.error(() => 'UPDATE ID REQUIRED'),
            updateType: Joi.string()
                .required()
                .error(() => 'UPDATE TYPE IS REQUIRED'),
            status: Joi.string().error(() => 'STATUS IS REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getSingleSurveyTemplateValidator = (req, res, next) => {
    const schema = Joi.object({
        query: Joi.object({
            surveyBankId: objectId.error(() => 'SURVEY BANK ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserPermissionProgramListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserPermissionCurriculumListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserPermissionLevelListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
            term: Joi.string()
                .required()
                .error(() => 'TERM IS REQUIRED'),
            yearNo: Joi.string()
                .required()
                .error(() => 'YEAR IS REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserPermissionYearListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
            term: Joi.string()
                .required()
                .error(() => 'TERM IS REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserPermissionCourseListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
            term: Joi.string()
                .required()
                .error(() => 'TERM IS REQUIRED'),
            yearNo: Joi.string()
                .required()
                .error(() => 'YEAR IS REQUIRED'),
            levelNo: Joi.string()
                .required()
                .error(() => 'LEVEL IS REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getPloDetailValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getCloDetailValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            courseId: objectId.error(() => 'COURSE ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        query: req.query,
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserSelectedProgramDetailValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            userId: objectId.error(() => 'USER ID REQUIRED'),
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getAllProgramListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getProgramUserDetailValidator = (req, res, next) => {
    const schema = Joi.object({
        body: Joi.object({
            selectedProgram: Joi.array()
                .items(
                    Joi.object({
                        programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
                    }).unknown(true),
                )
                .error((error) => error),
        }).unknown(true),
    });
    const { error } = schema.validate({
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getUserSurveyTypeListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.createNewSurveyValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            isTemplate: Joi.boolean().error(() => 'IS TEMPLATE REQUIRED'),
            surveyName: Joi.string().error(() => 'SURVEY NAME REQUIRED'),
            createdBy: optionalObjectId.error(() => 'CREATED BY ID REQUIRED'),
            surveyLevel: Joi.string().error(() => 'SURVEY LEVEL REQUIRED'),
            surveyType: Joi.string().error(() => 'SURVEY TYPE REQUIRED'),
            learningOutcome: Joi.boolean().error(() => 'LEARNING OUTCOME REQUIRED'),
            manageQuestionTags: Joi.boolean().error(() => 'MANAGE QUESTION TAGS REQUIRED'),
            mapLearningOutcomes: Joi.boolean().error(() => 'MAPPING LEARNING OUTCOMES REQUIRED'),
            status: Joi.string().error(() => 'STATUS REQUIRED'),
            cloneId: optionalObjectId.error(() => 'INVALID CLONE ID'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.createUserSurveyTypeListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            userSurveyTypeList: Joi.array().items(Joi.string()),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getTotalUserListValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
            programId: objectId.error(() => 'PROGRAM ID REQUIRED'),
            selectedUserGroups: Joi.array().required(),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.publishSurveyValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        body: Joi.object({
            institutionCalendarId: objectId.error(() => 'INSTITUTION CALENDAR ID REQUIRED'),
            isTemplate: Joi.boolean().error(() => 'IS TEMPLATE REQUIRED'),
            surveyName: Joi.string().error(() => 'SURVEY NAME REQUIRED'),
            createdBy: optionalObjectId.error(() => 'CREATED BY ID REQUIRED'),
            surveyLevel: Joi.string().error(() => 'SURVEY LEVEL REQUIRED'),
            surveyType: Joi.string().error(() => 'SURVEY TYPE REQUIRED'),
            programId: optionalObjectId.error(() => 'INVALID PROGRAM ID'),
            surveyBankId: objectId.error(() => 'SURVEY BANK ID REQUIRED'),
            surveyUsers: Joi.array().items(Joi.string()),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};

exports.getDraftSurveyValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId.error(() => 'INSTITUTION ID REQUIRED'),
        }).unknown(true),
        query: Joi.object({
            isTemplate: Joi.boolean()
                .required()
                .error(() => 'IS TEMPLATE REQUIRED'),
            surveyLevel: Joi.string()
                .required()
                .error(() => 'SURVEY LEVEL REQUIRED'),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((d) => d.message).join(',');
        return res.status(400).json({ errors });
    }
    next();
};
