const fireBase = require('firebase-admin');
const {
    FIREBASE_PROJECT_ID,
    FIREBASE_PRIVATE_KEY,
    FIREBASE_CLIENT_EMAIL,
} = require('../lib/utility/util_keys');
const { objectStringConvertor } = require('../lib/utility/common');
const { ANDROID, HIGH } = require('../lib/utility/constants');

fireBase.initializeApp({
    credential: fireBase.credential.cert({
        projectId: FIREBASE_PROJECT_ID,
        privateKey: FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        clientEmail: FIREBASE_CLIENT_EMAIL,
    }),
});

const sendNotificationPush = async (token, data, deviceType) => {
    try {
        const notification = {
            title: data.title || data.Title || data.data?.Title || 'Default Title',
            body: data.body || data.Body || data.data?.Body || 'Default Body',
        };
        const tokens = Array.isArray(token) ? token : [token];
        for (const tokenElement of tokens) {
            if (tokenElement && tokenElement.length) {
                let message = {};
                const flatData = objectStringConvertor(data);
                if (deviceType === ANDROID) {
                    message = {
                        token: tokenElement,
                        data: flatData,
                        android: {
                            priority: HIGH,
                        },
                    };
                } else {
                    message = {
                        token: tokenElement,
                        notification,
                        data: flatData,
                    };
                }
                try {
                    const pushResponse = await fireBase.messaging().send(message);
                    if (pushResponse.failureCount > 0)
                        console.error(
                            `${response.failureCount} push messages failed`,
                            pushResponse.responses,
                        );
                } catch (error) {
                    console.error('FCM Error sending to token:', tokenElement, 'Error:', error);
                }
            }
        }
    } catch (error) {
        console.error('FCM General Error:', error);
    }
};

module.exports = {
    sendNotificationPush,
};
