const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');


exports.infra_delivery_medium = (req,res,next) =>{
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            name: Joi.string().min(1).max(50).required().error(error =>{
                return error;
            }),
            remote: Joi.boolean().required().error(error =>{
                return error;
            }),
            onsite: Joi.boolean().required().error(error =>{
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err, value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    })
}

exports.delivery_medium = (req,res,next) =>{
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            remote: Joi.boolean().error(error =>{
                return error;
            }),
            onsite: Joi.boolean().error(error =>{
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err, value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    })
}

exports.infra_delivery_medium_id = (req,res,next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req,schema,function(err,value){
        if(err) {
            return common_files.com_response(res, 422, false, 'validation error' , err.details[0].message);
        } else {
            return next();
        }
    })
}