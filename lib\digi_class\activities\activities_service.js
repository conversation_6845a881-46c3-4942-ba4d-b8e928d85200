const CourseSchedule = require('../../models/course_schedule');
const Activities = require('../../models/activities');
const Question = require('../../models/question');
const User = require('../../models/user');
const Taxonomy = require('../../models/taxonomy');
const Notification = require('../../models/notification');
const Course = require('../../models/digi_course');
const Program = require('../../models/digi_programs');
const DigiLabel = require('../../models/digi_labels');
const { dsGetAllWithSortAsJSON, dsCustomUpdate, insert } = require('../../base/base_controller');
const { convertToMongoObjectId, cs, join, clone } = require('../../utility/common');
const { getClosBySloIds, getClos } = require('../learning_outcomes/learning_outcomes.service');
const { getCoursesForActivity } = require('../document_manager/document_manager_service');
const InstitutionCalendars = require('../../utility/institution_calendar');

// constants
const {
    DC_STAFF,
    DC_STUDENT,
    DRAFT,
    NOT_DRAFT,
    SCHEDULE_TYPES: { REGULAR },
    DS_CLO_KEY,
    DS_SLO_KEY,
    SCHEDULE,
    STARTED,
    NOT_STARTED,
    ANDROID,
    WEB,
    COMPLETED,
    COLLEGE_PROGRAM_TYPE,
} = require('../../utility/constants');
const { getCourseIds, getCourses } = require('../course_session/course_session_service');
const { timestampNow } = require('../../utility/common_functions');
const { DigiDate } = require('./digi_date');
const { axiosCall } = require('../../utility/common');
const { getUnsignedUrl } = require('../../utility/question_file_upload');
const { TIME, ONE_BY_ONE } = require('../../utility/enums');
const { sendDashboardNotification } = require('../notification/notification_service');
const { sendNotificationPush } = require('../../../service/pushNotification.service');

// get staff one by one result (quiz)
exports.getStaffOneByOneResult = async ({ activityId, totalStudents }) => {
    const { students, questions: activityQuestions } = await Activities.findOne(
        { _id: convertToMongoObjectId(activityId) },
        { students: 1 },
    );
    let questionIds;
    questionIds = activityQuestions.sort((a, b) => {
        return a.order - b.order;
    });
    questionIds = activityQuestions.map((question) => question._id);
    const questions = await Question.find({
        _id: { $in: questionIds },
    });

    const studentQuestion = questions.map((question) => {
        const {
            _id,
            text,
            options,
            questionType,
            feedback,
            attachments,
            questionMoved,
            acceptQuestionResponse,
        } = question;
        let studentWithQuestion;
        let studentAttendQuestionCount = 0;
        if (students && students.length > 0) {
            studentWithQuestion = students.filter((student) => {
                const { questions: studentQuestions } = student;
                if (
                    studentQuestions.find(
                        (studentEntry) =>
                            studentEntry._questionId &&
                            studentEntry._optionId &&
                            studentEntry._questionId.toString() === _id.toString(),
                    )
                ) {
                    studentAttendQuestionCount++;
                    return true;
                }
                return false;
            });
        }

        const studentOptions = options.map((option) => {
            const {
                _id: optionId,
                text: optionText,
                answer,
                attachments: optionAttachments,
            } = option;
            let studentWithOption;
            if (studentWithQuestion && studentWithQuestion.length > 0) {
                studentWithOption = studentWithQuestion.filter((student) => {
                    const { questions: studentQuestions } = student;
                    if (
                        studentQuestions.find(
                            (studentEntry) =>
                                studentEntry._optionId &&
                                studentEntry._optionId.toString() === optionId.toString(),
                        )
                    ) {
                        return true;
                    }
                    return false;
                });
            }
            const studentCount =
                studentWithOption && studentWithOption.length > 0 ? studentWithOption.length : 0;
            const percentage =
                ((studentCount / studentAttendQuestionCount) * 100).toFixed() !== 'NaN'
                    ? ((studentCount / studentAttendQuestionCount) * 100).toFixed()
                    : 0;
            return {
                _id: optionId,
                text: optionText,
                answer,
                attachments: optionAttachments,
                studentAnsweredCount: studentCount,
                totalStudentAnswered: studentAttendQuestionCount,
                percentage,
            };
        });
        let movedStatus = false;
        if (questionMoved) {
            movedStatus = true;
        }
        return {
            _id,
            text,
            options: studentOptions,
            questionType,
            feedback,
            attachments,
            studentAnsweredCount: studentAttendQuestionCount,
            questionMoved: movedStatus,
            acceptQuestionResponse,
        };
    });
    // format question
    const formatQuestion = this.formatQuestionsWithAttachmentLink(studentQuestion);
    return formatQuestion;
};

exports.sendActivityEvent = async (studentSocketId, activityId) => {
    const sendSocketData = [];
    for (const socketId of studentSocketId) {
        const { socketEventId, _id: userId } = socketId;
        if (socketEventId.activityEventId) {
            const eventId = socketEventId.activityEventId;
            const responseData = JSON.stringify(await getActivity(activityId, userId));
            sendSocketData.push({ eventId, data: responseData });
        }
    }
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
};

exports.quizEndByTimeOut = async ({ activityId, currentTimeEntry, quizStartedBy }) => {
    this.sendNotificationByActivityId(activityId);
    const updateQuery = {
        _id: convertToMongoObjectId(activityId),
    };
    const updateData = {
        quizStopBy: convertToMongoObjectId(quizStartedBy),
        endTime: currentTimeEntry,
        status: COMPLETED,
    };
    let data = { $set: updateData };
    const { success } = await dsCustomUpdate(Activities, updateQuery, data);
    if (success) {
        const {
            socketEventStudentId: studentEventId,
            socketEventStaffId: staffEventId,
            sessionId,
            scheduleIds,
        } = await Activities.findOne(
            {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
            },
            {
                endTime: 1,
                socketEventStudentId: 1,
                socketEventStaffId: 1,
                sessionId: 1,
                scheduleIds: 1,
            },
        );
        const studentStatus = { examOnLive: false };
        let eventId;
        let totalStudents;
        const scheduleConvertIds = scheduleIds.map((scheduleId) =>
            convertToMongoObjectId(scheduleId),
        );
        const courseSchedules = await CourseSchedule.find({
            _id: { $in: scheduleConvertIds },
            isDeleted: false,
            isActive: true,
        });
        const mergedStudents = [];
        const mergedStaffs = [];
        for (const courseSchedule of courseSchedules) {
            const { students, staffs, merge_status, merge_with } = courseSchedule;
            let scheduleStudents;
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                }
            }
            if (scheduleStudents && scheduleStudents.length) {
                scheduleStudents = scheduleStudents.concat(students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            } else {
                scheduleStudents = students;
            }
            if (scheduleStudents && scheduleStudents.length) {
                mergedStudents.push(scheduleStudents);
            } else {
                mergedStudents.push(students);
            }
            mergedStaffs.push(staffs);
        }
        // eslint-disable-next-line prefer-spread
        totalStudents = [].concat.apply([], mergedStudents);
        // eslint-disable-next-line prefer-spread
        const totalStaffs = [].concat.apply([], mergedStaffs);
        // different calendar and year and level based courses
        totalStudents = totalStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        studentIds = totalStudents.filter((student) => student._id);
        staffIds = totalStaffs.filter((staff) => staff._staff_id);
        const userIds = studentIds.concat(staffIds);
        const userDetails = await User.find(
            { _id: { $in: userIds } },
            { device_type: 1, fcm_token: 1, socketEventId: 1, user_type: 1 },
        );
        const studentSocketId = userDetails.map((student) => {
            return {
                socketEventId: student.socketEventId,
                user_type: student.user_type,
                _id: student._id,
            };
        });

        if (studentSocketId && studentSocketId.length > 0) {
            await this.sendActivityEvent(studentSocketId, activityId);
        }
        const sendSocketData = [];
        // student response
        eventId = studentEventId;
        data = JSON.stringify(studentStatus);
        sendSocketData.push({ eventId, data });

        // staff data
        const staffResult = {
            examOnLive: false,
            data: await this.getStaffTimeBasedResult({ activityId, totalStudents: [] }),
        };
        eventId = staffEventId;
        data = JSON.stringify(staffResult);
        sendSocketData.push({ eventId, data });
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    }
};

// create socket event
exports.createSocketEvent = async ({
    activityId,
    scheduleIds,
    staffStartWithExam,
    time = '',
    userId,
}) => {
    const scheduleConvertIds = scheduleIds.map((scheduleId) => convertToMongoObjectId(scheduleId));
    let totalStudents;
    const courseSchedules = await CourseSchedule.find({
        _id: { $in: scheduleConvertIds },
        isDeleted: false,
        isActive: true,
    });
    const mergedStudents = [];
    for (const courseSchedule of courseSchedules) {
        const { students, merge_status, merge_with } = courseSchedule;
        let scheduleStudents;
        if (merge_status) {
            const scheduleIds = merge_with.map((mergeWith) =>
                convertToMongoObjectId(mergeWith.schedule_id),
            );
            if (scheduleIds.length) {
                const schedules = await CourseSchedule.find({
                    _id: { $in: scheduleIds },
                    isDeleted: false,
                    isActive: true,
                });
                scheduleStudents = schedules.map((schedule) => schedule.students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            }
        }
        if (scheduleStudents && scheduleStudents.length) {
            scheduleStudents = scheduleStudents.concat(students);
            // eslint-disable-next-line prefer-spread
            scheduleStudents = [].concat.apply([], scheduleStudents);
        } else {
            scheduleStudents = students;
        }
        if (scheduleStudents && scheduleStudents.length) {
            mergedStudents.push(scheduleStudents);
        } else {
            mergedStudents.push(students);
        }
    }
    // eslint-disable-next-line prefer-spread
    totalStudents = [].concat.apply([], mergedStudents);
    // different calendar and year and level based courses
    totalStudents = totalStudents.reduce((acc, current) => {
        const x = acc.find((item) => item._id.toString() === current._id.toString());
        if (!x) {
            return acc.concat([current]);
        }
        return acc;
    }, []);
    const currentTime = timestampNow();
    let intervalTime;
    let staffResult;
    // if staff started exam with time based
    if (staffStartWithExam === TIME && time) {
        intervalTime = currentTime + time;
        // staff data
        staffResult = {
            examOnLive: true,
            data: await this.getStaffTimeBasedResult({ activityId, totalStudents }),
        };
    } else if (staffStartWithExam === ONE_BY_ONE) {
        staffResult = {
            examOnLive: true,
            data: await this.getStaffOneByOneResult({ activityId, totalStudents }),
        };
    }
    const staffEventId = 'staff_' + activityId + '_' + currentTime;
    const studentEventId = 'student_' + activityId + '_' + currentTime;
    let eventId;
    let data;

    // student quiz status
    const studentStatus = { examOnLive: true };
    const sendSocketData = [];
    // student response
    eventId = studentEventId;
    data = JSON.stringify(studentStatus);
    sendSocketData.push({ eventId, data });

    eventId = staffEventId;
    data = JSON.stringify(staffResult);
    sendSocketData.push({ eventId, data });
    if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    if (intervalTime) {
        const intervalData = setInterval(() => {
            const currentTimeEntry = timestampNow();
            if (currentTimeEntry > intervalTime) {
                quizEndByTimeOut({ activityId, currentTimeEntry, quizStartedBy: userId });
                clearInterval(intervalData);
            }
        }, 1000);
    }
    return {
        staffEventId,
        studentEventId,
    };
};

const getCommaSeparatedStudentGroup = (studentGroups) => {
    try {
        return studentGroups.map((studentGroup) => studentGroup.name).toString();
    } catch (error) {
        throw new Error(error);
    }
};

// get question
const formatQuestions = async (activityQuestions, answeredQuestions) => {
    const questions = [];
    let answeredCount = 0;
    let studentCorrectAnswered = 0;
    for (const activityQuestion of activityQuestions) {
        const { _id, options } = activityQuestion;
        if (options && options.length > 0) {
            for (const option of options) {
                const { _id: optionId, answer: optionAnswer } = option;
                if (
                    answeredQuestions &&
                    answeredQuestions.find(
                        (studentEntry) =>
                            studentEntry._optionId &&
                            studentEntry._optionId.toString() === optionId.toString(),
                    )
                ) {
                    if (optionAnswer) {
                        studentCorrectAnswered++;
                    }
                    studentAnswered = optionAnswer;
                    studentAnsweredOptionId = optionId;
                }
            }
        }
        let questionAnswered = false;
        if (
            answeredQuestions &&
            answeredQuestions.find(
                (answeredQuestion) => answeredQuestion._questionId.toString() === _id.toString(),
            )
        ) {
            questionAnswered = true;
            answeredCount++;
        }
    }
    return {
        questions,
        answeredCount,
        totalQuestionCount: activityQuestions.length,
        studentCorrectAnswered,
    };
};

const getStudentGroup = async (sessionId) => {
    try {
        const query = {
            $or: [
                { 'session._session_id': convertToMongoObjectId(sessionId) },
                { _id: convertToMongoObjectId(sessionId) },
            ],
            isDeleted: false,
            isActive: true,
        };
        const project = {
            _id: 1,
            session: 1,
            student_groups: 1,
        };
        const courseSchedules = (await dsGetAllWithSortAsJSON(CourseSchedule, query, project)).data;
        if (!courseSchedules.length) return [];
        const studentGroups = [];
        courseSchedules.forEach((courseSchedule) => {
            const groupName = courseSchedule.student_groups
                .map((studentGroup) => studentGroup.group_name)
                .toString();
            const names = [];
            groupName.split(',').forEach((grp) => {
                const groupSplitted = grp.split('-');
                names.push(
                    groupSplitted[groupSplitted.length - 3] +
                        '-' +
                        groupSplitted[groupSplitted.length - 1],
                );
            });
            studentGroups.push({
                scheduleId: courseSchedule._id,
                studentGroupId: courseSchedule._student_group_id,
                groupName,
                name: names.toString(),
            });
        });
        return studentGroups;
    } catch (error) {
        throw new Error(error);
    }
};

const studentGroupByGroupName = (studentGroups) => {
    const grouped = {};
    studentGroups.forEach((groupElement) => {
        const { group_name } = groupElement;
        if (grouped.hasOwnProperty(group_name)) {
            grouped[group_name].session_group.push(...groupElement.session_group);
        } else {
            grouped[group_name] = {
                group_name,
                session_group: [...groupElement.session_group],
            };
        }
    });
    const formattedOutput = Object.values(grouped);
    return formattedOutput;
};

const getAllActivity = async (
    type,
    userId,
    limit,
    page,
    _institution_calendar_id,
    courseAdmin,
    courseId,
    sessionId,
    scheduleId,
    mergeStatus,
    search,
    mode,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    headerInstitutionCalendarId,
    rotation_count,
) => {
    try {
        const project = {
            createdAt: 0,
            updatedAt: 0,
        };
        let activityQuery;
        let notInSchedule = true;
        const activityOrQuery = [];
        const activityAndQuery = [];
        if (sessionId) {
            activityQuery = {
                $or: [
                    {
                        'sessionFlowIds._id': convertToMongoObjectId(sessionId),
                        createdBy: { $ne: convertToMongoObjectId(userId) },
                        status: { $ne: DRAFT },
                    },
                    {
                        'sessionFlowIds._id': convertToMongoObjectId(sessionId),
                        createdBy: convertToMongoObjectId(userId),
                    },
                    {
                        sessionId: convertToMongoObjectId(sessionId),
                        createdBy: { $ne: convertToMongoObjectId(userId) },
                        status: { $ne: DRAFT },
                    },
                    {
                        sessionId: convertToMongoObjectId(sessionId),
                        createdBy: convertToMongoObjectId(userId),
                    },
                ],
                isDeleted: false,
            };
        } else {
            activityQuery = { isDeleted: false };
        }
        // const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        if (courseId) {
            activityQuery._program_id = convertToMongoObjectId(_program_id);
            activityQuery.year_no = year_no;
            activityQuery.level_no = level_no;
            activityQuery.term = term;
            activityQuery.rotation = rotation;
            // activityQuery._institution_calendar_id = { $in: institutionCalendarIds };
            activityQuery._institution_calendar_id = headerInstitutionCalendarId;
            if (rotation_count) {
                activityQuery.rotation_count = parseInt(rotation_count);
            }
        }

        if (search) {
            activityQuery.name = { $regex: search, $options: 'i' };
        }

        if (mode && mode !== NOT_DRAFT) {
            activityQuery.status = mode;
        }
        if (scheduleId) activityQuery.scheduleIds = convertToMongoObjectId(scheduleId);
        if (courseId) activityQuery.courseId = convertToMongoObjectId(courseId);
        if (type === DC_STUDENT) {
            if (mode && mode === NOT_DRAFT) {
                activityQuery.status = { $ne: DRAFT };
            } else {
                if (mode && mode !== NOT_DRAFT) {
                    activityQuery.$and = [{ status: { $ne: DRAFT } }, { status: mode }];
                } else {
                    activityQuery.status = { $ne: DRAFT };
                }
            }
            if (!scheduleId) {
                const studentQuery = {
                    'students._id': convertToMongoObjectId(userId),
                    isDeleted: false,
                    isActive: true,
                };
                const courseSchedules = await CourseSchedule.find(studentQuery, {
                    _id: 1,
                });
                if (courseSchedules.length) {
                    const scheduleIds = courseSchedules.map((courseSchedule) =>
                        convertToMongoObjectId(courseSchedule._id),
                    );
                    activityQuery.scheduleIds = { $in: scheduleIds };
                } else {
                    notInSchedule = false;
                }
            }
            if (mergeStatus) {
                const scheduleQuery = {
                    'session._session_id': convertToMongoObjectId(sessionId),
                    merge_status: mergeStatus,
                    isDeleted: false,
                    isActive: true,
                };
                const courseSchedule = await CourseSchedule.findOne(scheduleQuery, {
                    _id: 1,
                    session: 1,
                    student_groups: 1,
                    merge_with: 1,
                });
                if (courseSchedule) {
                    const { merge_with } = courseSchedule;
                    const courseSchedules = merge_with.map(
                        (mergeWithEntry) => mergeWithEntry.schedule_id,
                    );

                    if (courseSchedules.length) {
                        const scheduleIds = courseSchedules.map((courseSchedule) =>
                            convertToMongoObjectId(courseSchedule),
                        );
                        activityQuery.scheduleId = { $in: scheduleIds };
                    }
                }
            }
        }
        let courseIds = [];
        if (!courseId)
            courseIds = await getCoursesForActivity(
                userId,
                courseAdmin,
                headerInstitutionCalendarId,
            );
        const activityList = [];
        if (!courseId && courseIds.length) {
            // courseIds = [...new Set(courseIds.map((courseId) => courseId.toString()))].map(
            //     (courseId) => convertToMongoObjectId(courseId),
            // );

            courseIds = courseIds.map((courseId) => {
                const mappedCourseInfo = {
                    _institution_calendar_id: courseId._institution_calendar_id,
                    _program_id: courseId._program_id,
                    term: courseId.term,
                    year_no: courseId.year_no,
                    level_no: courseId.level_no,
                    courseId: courseId._id,
                };
                if (courseId.rotation_count)
                    mappedCourseInfo.rotation_count = courseId.rotation_count;
                activityOrQuery.push(mappedCourseInfo);
                return mappedCourseInfo;
            });
        } else if (!courseId) {
            return { totalDoc: 0, totalPages: 0, currentPage: 0, activities: activityList };
        }
        if (type === DC_STAFF) {
            const staffQuery = {
                isDeleted: false,
            };
            if (courseAdmin !== 'true') {
                staffQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
            if (courseId) {
                staffQuery._course_id = convertToMongoObjectId(courseId);
                staffQuery._program_id = convertToMongoObjectId(_program_id);
                staffQuery.year_no = year_no;
                staffQuery.level_no = level_no;
                staffQuery.term = term;
                staffQuery.rotation = rotation;
                // staffQuery._institution_calendar_id = { $in: institutionCalendarIds };
                staffQuery._institution_calendar_id = convertToMongoObjectId(
                    headerInstitutionCalendarId,
                );
                if (rotation_count) {
                    staffQuery.rotation_count = parseInt(rotation_count);
                }
            }

            const courseSchedules = await CourseSchedule.find(staffQuery, {
                _id: 1,
                session: 1,
            });
            // if (sessionId) {
            //     if (courseAdmin !== 'true') {
            //         activityQuery.createdBy = convertToMongoObjectId(userId);
            //     }
            // } else {
            // if (userId && !courseId) {
            //     // eslint-disable-next-line no-use-before-define
            //     const getCourseInfo = await getCoursesForActivity(userId, courseAdmin);
            //     if (getCourseInfo.length > 0) {
            //         const _institution_calendar_id = [
            //             ...new Set(
            //                 getCourseInfo.map(
            //                     (getCourse) => getCourse._institution_calendar_id,
            //                 ),
            //             ),
            //         ];
            //         const _program_id = [
            //             ...new Set(getCourseInfo.map((getCourse) => getCourse._program_id)),
            //         ];

            //         const term = [...new Set(getCourseInfo.map((getCourse) => getCourse.term))];

            //         const year_no = [
            //             ...new Set(getCourseInfo.map((getCourse) => getCourse.year_no)),
            //         ];

            //         const level_no = [
            //             ...new Set(getCourseInfo.map((getCourse) => getCourse.level_no)),
            //         ];

            //         const _course_id = [
            //             ...new Set(getCourseInfo.map((getCourse) => getCourse._id)),
            //         ];

            //         const rotation = [
            //             ...new Set(getCourseInfo.map((getCourse) => getCourse.rotation)),
            //         ];

            //         const rotation_count = [
            //             ...new Set(getCourseInfo.map((getCourse) => getCourse.rotation_count)),
            //         ];
            //         activityQuery.$and = [
            //             { _institution_calendar_id: { $in: _institution_calendar_id } },
            //             { _program_id: { $in: _program_id } },
            //             { term: { $in: term } },
            //             { year_no: { $in: year_no } },
            //             { level_no: { $in: level_no } },
            //             { courseId: { $in: _course_id } },
            //             { rotation: { $in: rotation } },
            //             { rotation_count: { $in: rotation_count } },
            //         ];
            //     }
            // }

            if (mode && mode === NOT_DRAFT) {
                activityQuery.status = { $ne: DRAFT };
            } else {
                const courseScheduleIds = [
                    ...new Set(
                        courseSchedules.map((scheduleId) => convertToMongoObjectId(scheduleId._id)),
                    ),
                ];
                if (
                    headerInstitutionCalendarId &&
                    headerInstitutionCalendarId.toString() !== _institution_calendar_id.toString()
                    // !institutionCalendarIds.find(
                    //     (institutionCalendarIdElement) =>
                    //         institutionCalendarIdElement.toString() ===
                    //         headerInstitutionCalendarId.toString(),
                    // )
                ) {
                    activityAndQuery.push(
                        {
                            createdBy: { $ne: convertToMongoObjectId(userId) },
                            status: COMPLETED,
                        },
                        { createdBy: convertToMongoObjectId(userId), status: COMPLETED },
                    );
                } else {
                    activityAndQuery.push(
                        {
                            createdBy: { $ne: convertToMongoObjectId(userId) },
                            status: { $ne: DRAFT },
                        },
                        { createdBy: convertToMongoObjectId(userId) },
                    );
                }
                // const sessionIds = [
                //     ...new Set(
                //         courseSchedules
                //             .filter(
                //                 (courseSchedule) =>
                //                     courseSchedule.session && courseSchedule.session._session_id,
                //             )
                //             .map((cSchedule) => cSchedule.session._session_id.toString()),
                //     ),
                // ].map((sessionId) => convertToMongoObjectId(sessionId));

                // for (cSchedule of courseSchedules) {
                //     if (cSchedule.session && cSchedule.session._session_id) {
                //         sessionIds.push(
                //             {
                //                 'sessionFlowIds._id': cSchedule.session._session_id,
                //             },
                //             {
                //                 'sessionFlowIds._id': cSchedule.session._session_id,
                //                 createdBy: convertToMongoObjectId(userId),
                //             },
                //         );
                //     }
                // }
                if (courseScheduleIds.length > 0) {
                    if (courseAdmin === 'true') {
                        //activityQuery.scheduleIds = { $in: courseScheduleIds };
                        // sessionIds.push(
                        //     { createdBy: convertToMongoObjectId(userId) },
                        //     {
                        //         createdBy: { $ne: convertToMongoObjectId(userId) },
                        //         status: { $ne: DRAFT },
                        //         scheduleIds: { $in: courseScheduleIds },
                        //     },
                        // );
                        //activityQuery.$or = sessionIds;
                    }
                    //  else {
                    //     if (sessionIds.length > 0) {
                    //         //activityQuery['sessionFlowIds._id'] = { $in: sessionIds };
                    //     }
                    // }
                } else {
                    activityQuery.createdBy = convertToMongoObjectId(userId);
                }
            }
            //}
        }
        if (activityAndQuery.length || activityOrQuery.length) {
            const activityAndQueryArray = [];
            if (activityOrQuery.length) activityAndQueryArray.push({ $or: activityOrQuery });
            if (activityAndQuery.length) activityAndQueryArray.push({ $or: activityAndQuery });
            activityQuery.$and = activityAndQueryArray;
        }
        // get query activities data
        let activities;
        let totalDoc;
        let totalPages;
        let currentPage;
        if (limit && page) {
            // make perPage and pageNo
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            if (notInSchedule) {
                activities = await Activities.find(activityQuery, project)
                    .populate({ path: 'createdBy', select: { _id: 1, name: 1 } })
                    .populate({
                        path: 'courseId',
                        select: {
                            course_code: 1,
                            course_name: 1,
                            versionNo: 1,
                            versioned: 1,
                            versionName: 1,
                            versionedFrom: 1,
                            versionedCourseIds: 1,
                        },
                    })
                    .sort({ status: -1, createdAt: -1 })
                    .skip(perPage * (pageNo - 1))
                    .limit(perPage)
                    .exec();

                //get all document
                totalDoc = await Activities.find(activityQuery).countDocuments().exec();
            } else {
                activities = [];
                totalDoc = 0;
            }

            // filter pages
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        }

        // Gathering Datas from certain collections
        // Questions gathering
        let activityQuestionIds = [];
        let activitySessionIds = [];
        let activityCourseScheduleIds = [];
        for (const activityElement of activities) {
            activityQuestionIds = [
                ...activityQuestionIds,
                ...activityElement.questions.map((question) =>
                    convertToMongoObjectId(question._id),
                ),
            ];
            if (activityElement.sessionFlowIds) {
                activitySessionIds = [
                    ...activitySessionIds,
                    ...activityElement.sessionFlowIds.map((sessionFlowId) =>
                        convertToMongoObjectId(sessionFlowId._id),
                    ),
                ];
            }
            if (activityElement.scheduleIds) {
                activityCourseScheduleIds = [
                    ...activityCourseScheduleIds,
                    ...activityElement.scheduleIds.map((scheduleId) =>
                        convertToMongoObjectId(scheduleId),
                    ),
                ];
            }
        }
        const activityQuestionDatas = await Question.find({
            _id: { $in: activityQuestionIds },
            isDeleted: false,
        });
        // Course Schedule Data gathering
        const courseScheduleList = await CourseSchedule.find(
            {
                $or: [
                    {
                        'session._session_id': { $in: activitySessionIds },
                    },
                    {
                        _id: { $in: activityCourseScheduleIds },
                    },
                ],
            },
            {
                merge_status: 1,
                merge_with: 1,
                students: 1,
                session: 1,
                staffs: 1,
                student_groups: 1,
            },
        );
        // Merged Session gathering
        let mergedScheduleIds = [];
        for (const scheduleElement of courseScheduleList) {
            if (scheduleElement.merge_status) {
                mergedScheduleIds = [
                    ...mergedScheduleIds,
                    ...scheduleElement.merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    ),
                ];
            }
        }
        let mergedCourseScheduleData = [];
        if (mergedScheduleIds.length)
            mergedCourseScheduleData = await CourseSchedule.find({
                _id: { $in: mergedScheduleIds },
            });
        for (const activity of activities) {
            const {
                status,
                _id,
                name,
                type: activityType,
                quizType,
                createdBy,
                setQuizTime,
                socketEventStaffId,
                socketEventStudentId,
                staffStartWithExam,
                startTime,
                endTime,
                students,
                studentCompletedQuiz,
                questions,
                student_groups,
                courseId,
                scheduleIds,
                schedule,
                sessionFlowIds,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
                courseAdmin,
            } = activity;
            let studentGroupsName = '';
            let totalStudents;
            const questionDetails = [];
            if (questions.length) {
                let questionIds;
                questionIds = questions.sort((a, b) => {
                    return a.order - b.order;
                });
                questionIds = questions.map((question) => question._id.toString());
                for (questionIdElement of questionIds) {
                    const questionDataElement = activityQuestionDatas.find(
                        (ele) => ele._id.toString() === questionIdElement.toString(),
                    );
                    if (questionDataElement) questionDetails.push(questionDataElement);
                }
            }
            let sessionDetails = [];
            if (sessionFlowIds) {
                const sessionIds = sessionFlowIds.map((sessionFlowId) =>
                    sessionFlowId._id.toString(),
                );

                const sessions = courseScheduleList
                    .filter(
                        (ele) =>
                            ele.session &&
                            ele.session._session_id &&
                            sessionIds.find(
                                (ele2) => ele2.toString() === ele.session._session_id.toString(),
                            ),
                    )
                    .map((ele3) => {
                        return {
                            _id: ele3._id,
                            title: ele3.title,
                            type: ele3.type,
                            session: ele3.session,
                        };
                    });
                sessionDetails = sessionFlowIds.map((sessionId) => {
                    const sessionDetail = sessions.find(
                        (sessionEntry) =>
                            (sessionEntry.session &&
                                sessionEntry.session._session_id.toString() ===
                                    sessionId._id.toString()) ||
                            (sessionEntry.type === sessionId.type &&
                                sessionEntry._id.toString() === sessionId._id.toString()),
                    );
                    if (sessionDetail) {
                        const { session, _id, title, type } = sessionDetail;
                        if (session) {
                            const { _session_id } = session;
                            if (
                                session &&
                                _session_id &&
                                sessionIds.includes(_session_id.toString())
                            ) {
                                return {
                                    _id: session._session_id,
                                    s_no: session.s_no,
                                    delivery_symbol: session.delivery_symbol,
                                    delivery_no: session.delivery_no,
                                    session_type: session.session_type,
                                    session_topic: session.session_topic,
                                    type: REGULAR,
                                };
                            }
                        }
                        if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                            return { _id, title, type };
                        }
                    }
                });
            }
            // if student end the quiz time based
            const userCompletedQuizStatus = studentCompletedQuiz.find(
                (studentCompleted) => cs(studentCompleted) === cs(userId),
            );
            const completedQuizStatus = !!userCompletedQuizStatus;
            let answeredQuestions;
            if (type === DC_STUDENT) {
                answeredStudent = students.find((student) => cs(student._studentId) === cs(userId));
                if (answeredStudent) answeredQuestions = answeredStudent.questions;
            }
            let activityQuestions;
            if (questionDetails && questionDetails.length) {
                activityQuestions = questionDetails.map((selectedQuestion) => {
                    const { _id } = selectedQuestion;
                    const questionType = questions.find((q) => q._id.toString() === _id.toString());
                    if (questionType) {
                        selectedQuestion.type = questionType.type;
                        selectedQuestion.order = questionType.order;
                    }
                    return selectedQuestion;
                });
                activityQuestions = await formatQuestions(activityQuestions, answeredQuestions);
            }

            let courseSchedule;
            if (scheduleIds && scheduleIds.length) {
                const courseSchedules = courseScheduleList.filter((ele) =>
                    scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                );
                const mergedStudents = [];
                let studentGroups = [];
                for (const scheduleIdEntry of courseSchedules) {
                    const { merge_status, merge_with, students } = scheduleIdEntry;
                    let scheduleStudents;
                    if (merge_status) {
                        const scheduleIds = merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        );
                        if (scheduleIds.length) {
                            const schedules = mergedCourseScheduleData.filter((ele) =>
                                scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                            );
                            scheduleStudents = schedules.map((schedule) => schedule.students);
                            // eslint-disable-next-line prefer-spread
                            scheduleStudents = [].concat.apply([], scheduleStudents);
                            schedules.push(scheduleIdEntry);
                            schedules.forEach((courseSchedule) => {
                                const { student_groups, _id } = courseSchedule;
                                studentGroups.push(student_groups);
                            });
                        }
                    } else {
                        const { student_groups } = scheduleIdEntry;
                        studentGroups.push(student_groups);
                    }

                    if (scheduleStudents && scheduleStudents.length) {
                        scheduleStudents = scheduleStudents.concat(students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    } else {
                        scheduleStudents = students;
                    }
                    if (scheduleStudents && scheduleStudents.length) {
                        mergedStudents.push(scheduleStudents);
                    }
                }

                // eslint-disable-next-line no-sequences
                studentGroups = studentGroups.reduce((r, e) => (r.push(...e), r), []);
                // eslint-disable-next-line no-sequences
                studentGroups = studentGroups.reduce((studentGroup, currentStudentGroup) => {
                    const x = studentGroup.find(
                        (item) =>
                            item.group_id.toString() === currentStudentGroup.group_id.toString() &&
                            item.group_no.toString() === currentStudentGroup.group_no.toString() &&
                            item.session_group.length &&
                            currentStudentGroup.session_group.length &&
                            item.session_group.find((sessionGroup) => {
                                const currentSessionStudentGroup =
                                    currentStudentGroup.session_group.map((sessionGroup) =>
                                        sessionGroup.session_group_id.toString(),
                                    );
                                if (
                                    currentSessionStudentGroup.includes(
                                        sessionGroup.session_group_id.toString(),
                                    )
                                )
                                    return true;
                            }),
                    );
                    if (!x) {
                        return studentGroup.concat([currentStudentGroup]);
                    }
                    return studentGroup;
                }, []);
                studentGroups = studentGroupByGroupName(studentGroups);
                if (studentGroups && studentGroups.length) {
                    studentGroups = studentGroups.map((student_group) => {
                        const { group_name, session_group } = student_group;
                        let groupName = group_name.split('-').slice(-2);
                        groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                        if (session_group && session_group.length) {
                            let sessionGroup = session_group.map((groupNameEntry) => {
                                let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                groupNames = groupNames[1]
                                    ? groupNames[0] + '-' + groupNames[1]
                                    : groupNames[0];
                                return groupNames;
                            });
                            sessionGroup = sessionGroup.toString();
                            groupName += '(' + sessionGroup + ')';
                        }
                        return groupName;
                    });
                    studentGroups = studentGroups.toString();
                    studentGroupsName += studentGroups;
                }
                // eslint-disable-next-line prefer-spread
                totalStudents = [].concat.apply([], mergedStudents);
                // different calendar and year and level based courses
                totalStudents = totalStudents.reduce((acc, current) => {
                    const x = acc.find((item) => item._id.toString() === current._id.toString());
                    if (!x) {
                        return acc.concat([current]);
                    }
                    return acc;
                }, []);

                courseSchedule = courseSchedules.find(
                    (scheduleId) =>
                        scheduleId.staffs.find(
                            (staff) => staff._staff_id.toString() === userId.toString(),
                        ) ||
                        scheduleId.students.find(
                            (student) => student._id.toString() === userId.toString(),
                        ),
                );
            }
            activityList.push({
                status,
                student_groups,
                studentGroupName: studentGroupsName,
                _id,
                name: name !== null ? name : '',
                type: activityType,
                quizType,
                courseId,
                createdBy,
                setQuizTime,
                socketEventStaffId,
                socketEventStudentId,
                staffStartWithExam,
                studentCompletedQuiz: completedQuizStatus,
                startTime,
                schedule,
                endTime,
                scheduleIds,
                courseAdmin,
                sessionFlowIds: sessionDetails || [],
                questions: activityQuestions ? activityQuestions.questions : [],
                answeredCount: activityQuestions ? activityQuestions.answeredCount : 0,
                totalQuestionCount: activityQuestions ? activityQuestions.totalQuestionCount : 0,
                studentCorrectAnsweredCount: activityQuestions
                    ? activityQuestions.studentCorrectAnswered
                    : 0,
                totalStudentAnsweredCount: students.length,
                totalStudentCount: totalStudents ? totalStudents.length : 0,
                sessionType:
                    courseSchedule && courseSchedule.type ? courseSchedule.type : undefined,
                _course_id:
                    courseSchedule && courseSchedule._course_id
                        ? courseSchedule._course_id
                        : undefined,
                course_name:
                    courseSchedule && courseSchedule.course_name
                        ? courseSchedule.course_name
                        : undefined,
                merge_status: courseSchedule ? courseSchedule.merge_status : undefined,
                scheduleId: courseSchedule && courseSchedule._id ? courseSchedule._id : undefined,
                sessionId:
                    courseSchedule && courseSchedule.session
                        ? courseSchedule.session._session_id
                        : undefined,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
            });
        }

        if (limit && page) {
            return { totalDoc, totalPages, currentPage, activities: activityList };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, activities: activityList };
    } catch (error) {
        throw new Error(error);
    }
};
const getAllAdminActivities = async (userId, limit, page, courseId) => {
    try {
        const project = {
            createdAt: 0,
            updatedAt: 0,
        };

        const activityQuery = { isDeleted: false };

        const courses = await Course.find({
            'coordinators._user_id': convertToMongoObjectId(userId),
        });
        if (courses.length) {
            const currentCourseIds = courses.map((course) => course._id);
            if (currentCourseIds) {
                activityQuery._course_id = { $in: currentCourseIds };
            }
        } else {
            return { totalDoc: 0, totalPages: 0, currentPage: 0, activities: [] };
        }

        // get query activities data
        let activities;
        let totalDoc;
        let totalPages;
        let currentPage;
        if (limit && page) {
            // make perPage and pageNo
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            activities = await Activities.find(activityQuery, project)
                .populate({ path: 'createdBy', select: { _id: 1, name: 1 } })
                .populate({ path: 'courseId', select: { course_code: 1, course_name: 1 } })
                .sort({ createdAt: -1 })
                .skip(perPage * (pageNo - 1))
                .limit(perPage)
                .exec();
            //get all document
            totalDoc = await Activities.find(activityQuery).countDocuments().exec();
            // filter pages
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        }

        // Gathering Datas from certain collections
        // Questions gathering
        let activityQuestionIds = [];
        let activitySessionIds = [];
        let activityCourseScheduleIds = [];
        for (const activityElement of activities) {
            activityQuestionIds = [
                ...activityQuestionIds,
                ...activityElement.questions.map((question) =>
                    convertToMongoObjectId(question._id),
                ),
            ];
            activitySessionIds = [
                ...activitySessionIds,
                ...activityElement.sessionFlowIds.map((sessionFlowId) =>
                    convertToMongoObjectId(sessionFlowId._id),
                ),
            ];
            activityCourseScheduleIds = [
                ...activityCourseScheduleIds,
                ...activityElement.scheduleIds.map((scheduleId) =>
                    convertToMongoObjectId(scheduleId),
                ),
            ];
        }
        const activityQuestionDatas = await Question.find({
            _id: { $in: activityQuestionIds },
            isDeleted: false,
        });
        // Course Schedule Data gathering
        const courseScheduleList = await CourseSchedule.find(
            {
                $or: [
                    {
                        'session._session_id': { $in: activitySessionIds },
                    },
                    {
                        _id: { $in: activityCourseScheduleIds },
                    },
                ],
            },
            {},
        );
        // Merged Session gathering
        let mergedScheduleIds = [];
        for (scheduleElement of courseScheduleList) {
            if (scheduleElement.merge_status) {
                mergedScheduleIds = [
                    ...mergedScheduleIds,
                    ...scheduleElement.merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    ),
                ];
            }
        }
        let mergedCourseScheduleData = [];
        if (mergedScheduleIds.length)
            mergedCourseScheduleData = await CourseSchedule.find({
                _id: { $in: mergedScheduleIds },
            });
        const activityList = [];
        for (const activity of activities) {
            const {
                status,
                _id,
                name,
                type: activityType,
                quizType,
                createdBy,
                setQuizTime,
                socketEventStaffId,
                socketEventStudentId,
                staffStartWithExam,
                startTime,
                endTime,
                students,
                studentCompletedQuiz,
                questions,
                student_groups,
                courseId,
                scheduleIds,
                schedule,
                sessionFlowIds,
                courseAdmin,
            } = activity;
            let studentGroupsName = '';
            let totalStudents;
            const questionDetails = [];
            if (questions.length) {
                let questionIds;
                questionIds = questions.sort((a, b) => {
                    return a.order - b.order;
                });
                questionIds = questions.map((question) => question._id.toString());
                for (questionIdElement of questionIds) {
                    const questionDataElement = activityQuestionDatas.find(
                        (ele) => ele._id.toString() === questionIdElement.toString(),
                    );
                    if (questionDataElement) questionDetails.push(questionDataElement);
                }
            }

            const sessionIds = sessionFlowIds.map((sessionFlowId) => sessionFlowId._id.toString());

            const sessions = courseScheduleList
                .filter(
                    (ele) =>
                        ele.session &&
                        ele.session._session_id &&
                        sessionIds.find(
                            (ele2) => ele2.toString() === ele.session._session_id.toString(),
                        ),
                )
                .map((ele3) => {
                    return {
                        _id: ele3._id,
                        title: ele3.title,
                        type: ele3.type,
                        session: ele3.session,
                    };
                });
            const sessionDetails = sessionFlowIds.map((sessionId) => {
                const sessionDetail = sessions.find(
                    (sessionEntry) =>
                        (sessionEntry.session &&
                            sessionEntry.session._session_id.toString() ===
                                sessionId._id.toString()) ||
                        (sessionEntry.type === sessionId.type &&
                            sessionEntry._id.toString() === sessionId._id.toString()),
                );
                if (sessionDetail) {
                    const { session, _id, title, type } = sessionDetail;
                    if (session) {
                        const { _session_id } = session;
                        if (session && _session_id && sessionIds.includes(_session_id.toString())) {
                            return {
                                _id: session._session_id,
                                s_no: session.s_no,
                                delivery_symbol: session.delivery_symbol,
                                delivery_no: session.delivery_no,
                                session_type: session.session_type,
                                session_topic: session.session_topic,
                                type: REGULAR,
                            };
                        }
                    }
                    if (title && type !== REGULAR && sessionIds.includes(_id.toString())) {
                        return { _id, title, type };
                    }
                }
            });
            // if student end the quiz time based
            const userCompletedQuizStatus = studentCompletedQuiz.find(
                (studentCompleted) => cs(studentCompleted) === cs(userId),
            );
            const completedQuizStatus = userCompletedQuizStatus;
            let answeredQuestions;
            if (type === DC_STUDENT) {
                answeredStudent = students.find((student) => cs(student._studentId) === cs(userId));
                if (answeredStudent) answeredQuestions = answeredStudent.questions;
            }
            let activityQuestions;
            if (questionDetails && questionDetails.length) {
                activityQuestions = questionDetails.map((selectedQuestion) => {
                    const { _id } = selectedQuestion;
                    const questionType = questions.find((q) => q._id.toString() === _id.toString());
                    if (questionType) {
                        selectedQuestion.type = questionType.type;
                        selectedQuestion.order = questionType.order;
                    }
                    return selectedQuestion;
                });
                activityQuestions = await formatQuestions(activityQuestions, answeredQuestions);
            }

            let courseSchedule;
            if (scheduleIds && scheduleIds.length) {
                const courseSchedules = courseScheduleList.filter((ele) =>
                    scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                );
                const mergedStudents = [];
                for (const scheduleIdEntry of courseSchedules) {
                    const { /* _id: scheduleId, */ merge_status, merge_with, students } =
                        scheduleIdEntry;
                    let scheduleStudents;
                    if (merge_status) {
                        const scheduleIds = merge_with.map((mergeWith) =>
                            convertToMongoObjectId(mergeWith.schedule_id),
                        );
                        if (scheduleIds.length) {
                            const schedules = mergedCourseScheduleData.filter((ele) =>
                                scheduleIds.find((ele2) => ele2.toString() === ele._id.toString()),
                            );
                            scheduleStudents = schedules.map((schedule) => schedule.students);
                            // eslint-disable-next-line prefer-spread
                            scheduleStudents = [].concat.apply([], scheduleStudents);
                            schedules.push(scheduleIdEntry);
                            schedules.forEach((courseSchedule) => {
                                const { student_groups, _id } = courseSchedule;
                                studentGroups.push(student_groups);
                            });
                        }
                    } else {
                        const { student_groups } = scheduleIdEntry;
                        studentGroups.push(student_groups);
                    }

                    if (scheduleStudents && scheduleStudents.length) {
                        scheduleStudents = scheduleStudents.concat(students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    } else {
                        scheduleStudents = students;
                    }
                    if (scheduleStudents && scheduleStudents.length) {
                        mergedStudents.push(scheduleStudents);
                    }
                }

                // eslint-disable-next-line no-sequences
                studentGroups = studentGroups.reduce((r, e) => (r.push(...e), r), []);
                // eslint-disable-next-line no-sequences
                studentGroups = studentGroups.reduce((studentGroup, currentStudentGroup) => {
                    const x = studentGroup.find(
                        (item) =>
                            item.group_id.toString() === currentStudentGroup.group_id.toString() &&
                            item.session_group.length &&
                            currentStudentGroup.session_group.length &&
                            item.session_group.find((sessionGroup) => {
                                const currentSessionStudentGroup =
                                    currentStudentGroup.session_group.map((sessionGroup) =>
                                        sessionGroup.session_group_id.toString(),
                                    );
                                if (
                                    currentSessionStudentGroup.includes(
                                        sessionGroup.session_group_id.toString(),
                                    )
                                )
                                    return true;
                            }),
                    );
                    if (!x) {
                        return studentGroup.concat([currentStudentGroup]);
                    }
                    return studentGroup;
                }, []);
                if (studentGroups && studentGroups.length) {
                    studentGroups = studentGroups.map((student_group) => {
                        const { group_name, session_group } = student_group;
                        let groupName = group_name.split('-').slice(-2);
                        groupName = groupName[1] ? groupName[0] + '-' + groupName[1] : groupName[0];
                        if (session_group && session_group.length) {
                            let sessionGroup = session_group.map((groupNameEntry) => {
                                let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                groupNames = groupNames[1]
                                    ? groupNames[0] + '-' + groupNames[1]
                                    : groupNames[0];
                                return groupNames;
                            });
                            sessionGroup = sessionGroup.toString();
                            groupName += '(' + sessionGroup + ')';
                        }
                        return groupName;
                    });
                    studentGroups = studentGroups.toString();
                    studentGroupsName += studentGroups;
                }
                // eslint-disable-next-line prefer-spread
                totalStudents = [].concat.apply([], mergedStudents);
                // different calendar and year and level based courses
                totalStudents = totalStudents.reduce((acc, current) => {
                    const x = acc.find((item) => item._id.toString() === current._id.toString());
                    if (!x) {
                        return acc.concat([current]);
                    }
                    return acc;
                }, []);

                courseSchedule = courseSchedules.find(
                    (scheduleId) =>
                        scheduleId.staffs.find(
                            (staff) => staff._staff_id.toString() === userId.toString(),
                        ) ||
                        scheduleId.students.find(
                            (student) => student._id.toString() === userId.toString(),
                        ),
                );
            }
            activityList.push({
                status,
                student_groups,
                studentGroupName: studentGroupsName,
                _id,
                name: name !== null ? name : '',
                type: activityType,
                quizType,
                courseId,
                createdBy,
                setQuizTime,
                socketEventStaffId,
                socketEventStudentId,
                staffStartWithExam,
                studentCompletedQuiz: completedQuizStatus,
                startTime,
                schedule,
                endTime,
                scheduleIds,
                courseAdmin,
                sessionFlowIds: sessionDetails || [],
                questions: activityQuestions ? activityQuestions.questions : [],
                answeredCount: activityQuestions ? activityQuestions.answeredCount : 0,
                totalQuestionCount: activityQuestions ? activityQuestions.totalQuestionCount : 0,
                studentCorrectAnsweredCount: activityQuestions
                    ? activityQuestions.studentCorrectAnswered
                    : 0,
                totalStudentAnsweredCount: students.length,
                totalStudentCount: totalStudents ? totalStudents.length : 0,
                sessionType:
                    courseSchedule && courseSchedule.type ? courseSchedule.type : undefined,
                _course_id:
                    courseSchedule && courseSchedule._course_id
                        ? courseSchedule._course_id
                        : undefined,
                course_name:
                    courseSchedule && courseSchedule.course_name
                        ? courseSchedule.course_name
                        : undefined,
                merge_status: courseSchedule ? courseSchedule.merge_status : undefined,
                scheduleId: courseSchedule && courseSchedule._id ? courseSchedule._id : undefined,
                sessionId:
                    courseSchedule && courseSchedule.session
                        ? courseSchedule.session._session_id
                        : undefined,
                _program_id,
                year_no,
                level_no,
                term,
                rotation,
                rotation_count,
                _institution_calendar_id,
            });
        }

        if (limit && page) {
            return { totalDoc, totalPages, currentPage, activities: activityList };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, activities: activityList };
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseSchedules = async (courseScheduleIds) => {
    try {
        const query = {
            _id: { $in: courseScheduleIds },
            isDeleted: false,
            isActive: true,
        };
        const project = {
            _id: 1,
            staffs: 1,
            students: 1,
            student_groups: 1,
            session: 1,
            _institution_calendar_id: 1,
            _program_id: 1,
            program_name: 1,
            _course_id: 1,
            course_name: 1,
            year_no: 1,
            level_no: 1,
            term: 1,
            merge_status: 1,
            rotation: 1,
            rotation_count: 1,
            type: 1,
        };
        return (await dsGetAllWithSortAsJSON(CourseSchedule, query, project)).data;
    } catch (error) {
        throw new Error(error);
    }
};

const getUserIdWithSchedules = async (courseSchedules) => {
    try {
        const userIdWithSchedules = [];
        for (const courseSchedule of courseSchedules) {
            const { _id, staffs, students, student_groups, session } = courseSchedule;
            const { _course_id, course_name, _program_id, program_name } = courseSchedule;
            const { _institution_calendar_id, year_no, level_no, term, rotation } = courseSchedule;
            const { rotation_count, merge_status, type } = courseSchedule;
            studentGroups = student_groups.map((studentGroup) => studentGroup.group_name);
            const groupName = student_groups
                .map((studentGroup) => studentGroup.group_name)
                .toString();
            const names = [];
            groupName.split(',').forEach((grp) => {
                const groupSplitted = grp.split('-');
                const getIVal = (index) => {
                    return groupSplitted[groupSplitted.length - index]
                        ? groupSplitted[groupSplitted.length - index] + '-'
                        : null;
                };
                let name = '';
                name += getIVal(3) || '';
                name += getIVal(2) || '';
                name += getIVal(1) || '';
                names.push(name);
            });
            const schedule = {
                ScheduleId: _id.toString(),
                SessionId: session ? session._session_id.toString() : _id.toString(),
                Session: session ? session._session_id.toString() : _id.toString(),
                CourseId: _course_id.toString(),
                courseName: course_name,
                programId: _program_id.toString(),
                programName: program_name,
                institutionCalendarId: _institution_calendar_id.toString(),
                student_groups: names.toString(),
                yearNo: year_no,
                levelNo: level_no,
                term,
                mergeStatus: merge_status,
                rotation,
                rotation_count,
                mergeType: type,
            };
            staffs.forEach((staff) => {
                userIdWithSchedules.push({
                    userId: staff._staff_id.toString(),
                    userType: 'staff',
                    ...schedule,
                });
            });
            students.forEach((student) => {
                userIdWithSchedules.push({
                    userId: student._id.toString(),
                    userType: 'student',
                    ...schedule,
                });
            });
        }
        const userIds = userIdWithSchedules.map((userIdWithSchedule) => {
            return userIdWithSchedule.userId;
        });
        const userQuery = { _id: { $in: userIds }, isDeleted: false, isActive: true };
        const userProject = {
            _id: 1,
            fcm_token: 1,
            web_fcm_token: 1,
            device_type: 1,
            socketEventId: 1,
        };
        const users = (await dsGetAllWithSortAsJSON(User, userQuery, userProject)).data;
        userIdWithSchedules.forEach((userIdWithSchedule) => {
            const user = users.find(
                (user) => user._id.toString() === userIdWithSchedule.userId.toString(),
            );
            if (user) {
                userIdWithSchedule._id = user._id;
                userIdWithSchedule.deviceType = user.device_type;
                userIdWithSchedule.fcm_token = user.fcm_token;
                userIdWithSchedule.web_fcm_token = user.web_fcm_token;
                userIdWithSchedule.socketEventId = user.socketEventId;
            }
        });
        return userIdWithSchedules;
    } catch (error) {
        throw new Error(error);
    }
};

const getStartAndEndTimeFormate = (schedule) => {
    try {
        if (!schedule || !schedule.endDateAndTime || !schedule.startDateAndTime) return '';
        const { endDateAndTime, startDateAndTime } = schedule;
        const startHour = new Date(startDateAndTime).getHours();
        const startTimeZone = startDateAndTime > 12 ? 'PM' : 'AM';
        const startMinute = new Date(startDateAndTime).getMinutes();
        const endHour = new Date(endDateAndTime).getHours();
        const endTimeZone = endDateAndTime > 12 ? 'PM' : 'AM';
        const endMinute = new Date(endDateAndTime).getMinutes();
        const a = [{ day: 'numeric' }, { month: 'short' }];
        const startDate = join(new Date(startDateAndTime), a, ' ');
        const endDate = join(new Date(endDateAndTime), a, ' ');
        scheduleDate =
            '\n' +
            startDate +
            ' ' +
            (startHour > 12 ? startHour - 12 : startHour) +
            ':' +
            startMinute +
            ' ' +
            startTimeZone +
            ' - ' +
            endDate +
            ' ' +
            (endHour > 12 ? endHour - 12 : endHour) +
            ':' +
            endMinute +
            ' ' +
            endTimeZone;
        return scheduleDate;
    } catch (error) {
        throw new Error(error);
    }
};

const getCurrentActivities = async (mode) => {
    try {
        // get current cron sendable activities
        const currentLocalTime = DigiDate.getLocalISO();
        const query = {
            status: mode === 'end' ? STARTED : NOT_STARTED,
            type: SCHEDULE,
            isActive: true,
            isDeleted: false,
        };
        query[`schedule.${mode}DateAndTime`] = currentLocalTime;
        const project = {};
        const activities = (await dsGetAllWithSortAsJSON(Activities, query, project)).data;
        // get course schedules of activities
        const scheduleIds = activities.map((activity) => activity.scheduleIds).flat();
        const courseSchedules = await getCourseSchedules(scheduleIds);
        const users = await getUserIdWithSchedules(courseSchedules);
        // send dashboard notifications
        sendDashboardNotification(users);
        // map activities with users
        const notifications = [];
        for (const activity of activities) {
            schedules = courseSchedules.filter((courseSchedule) => {
                const scheduleIds = activity.scheduleIds.map((scheduleId) => scheduleId.toString());
                return scheduleIds.includes(courseSchedule._id.toString());
            });
            studentGroups = schedules.map((schedule) => schedule.student_groups);
            groupNames = [];
            studentGroups.forEach((groups) => {
                const groupName = groups.map((studentGroup) => studentGroup.group_name).toString();
                const names = [];
                groupName.split(',').forEach((grp) => {
                    const groupSplitted = grp.split('-');
                    const getIVal = (index) => {
                        return groupSplitted[groupSplitted.length - index]
                            ? groupSplitted[groupSplitted.length - index] + '-'
                            : null;
                    };
                    let name = '';
                    name += getIVal(3) || '';
                    name += getIVal(2) || '';
                    name += getIVal(1) || '';
                    names.push(name);
                });
                groupNames.push(names.toString() + ' ');
            });
            activity.users = [];
            schedules.forEach((schedule) => {
                const { _id, staffs, students } = schedule;
                staffs.forEach((staff) => {
                    const user = users.find(
                        (user) =>
                            user.userId.toString() === staff._staff_id.toString() &&
                            user.ScheduleId.toString() === _id.toString(),
                    );
                    if (user) {
                        user.student_groups = groupNames.toString();
                        activity.users.push(user);
                        user.Title = activity.quizType;
                        user.Body =
                            activity.name +
                            getStartAndEndTimeFormate(activity.schedule) +
                            '\n' +
                            groupNames.toString() +
                            '\n' +
                            user.programName +
                            ' • ' +
                            user.levelNo +
                            ' • ' +
                            user.courseName;
                        user.activityStatus = mode;
                        user.scheduleType = activity.type;
                        user.ActivityId = activity._id.toString();
                        notifications.push(user);
                    }
                });
                students.forEach((student) => {
                    const user = users.find(
                        (user) =>
                            user.userId.toString() === student._id.toString() &&
                            user.ScheduleId.toString() === _id.toString(),
                    );
                    if (user) {
                        activity.users.push(user);
                        user.Title = activity.quizType;
                        user.Body =
                            activity.name +
                            getStartAndEndTimeFormate(activity.schedule) +
                            '\n' +
                            user.programName +
                            ' • ' +
                            user.levelNo +
                            ' • ' +
                            user.courseName;
                        user.activityStatus = mode;
                        user.scheduleType = activity.type;
                        user.ActivityId = activity._id.toString();
                        notifications.push(user);
                    }
                });
            });
        }
        return { activities, notifications };
    } catch (error) {
        throw new Error(error);
    }
};

const activityStart = async (
    activityId,
    userId,
    sessionId,
    scheduleIds,
    staffStartWithExam = 'TIME',
) => {
    try {
        const updateQuery = { _id: convertToMongoObjectId(activityId) };
        const { staffEventId, studentEventId } = await this.createSocketEvent({
            activityId,
            scheduleIds,
            staffStartWithExam,
            time: 0,
            userId,
        });
        updateData = {
            status: STARTED,
            socketEventStaffId: staffEventId,
            socketEventStudentId: studentEventId,
            quizStartedBy: convertToMongoObjectId(userId),
            staffStartWithExam,
            scheduleIds,
            sessionId,
            startTime: timestampNow(),
        };
        let data = { $set: updateData };
        dsCustomUpdate(Activities, updateQuery, data);

        const studentStatus = { examOnLive: true };
        let eventId;
        const scheduleConvertIds = scheduleIds.map((scheduleId) =>
            convertToMongoObjectId(scheduleId),
        );
        let totalStudents;
        const courseSchedules = await CourseSchedule.find({
            _id: { $in: scheduleConvertIds },
            isDeleted: false,
            isActive: true,
        });
        const mergedStudents = [];
        const mergedStaffs = [];
        for (const courseSchedule of courseSchedules) {
            const { students, staffs, merge_status, merge_with } = courseSchedule;
            let scheduleStudents;
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                }
            }
            if (scheduleStudents && scheduleStudents.length) {
                scheduleStudents = scheduleStudents.concat(students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            }
            if (scheduleStudents && scheduleStudents.length) {
                mergedStudents.push(scheduleStudents);
            } else {
                mergedStudents.push(students);
            }
            mergedStaffs.push(staffs);
        }
        // eslint-disable-next-line prefer-spread
        totalStudents = [].concat.apply([], mergedStudents);
        // eslint-disable-next-line prefer-spread
        const totalStaffs = [].concat.apply([], mergedStaffs);
        // different calendar and year and level based courses
        totalStudents = totalStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        const studentIds = totalStudents.map((student) => convertToMongoObjectId(student._id));
        const staffIds = totalStaffs.map((staff) => convertToMongoObjectId(staff._staff_id));
        const userIds = studentIds.concat(staffIds);
        const userDetails = await User.find(
            { _id: { $in: userIds } },
            { device_type: 1, fcm_token: 1, socketEventId: 1, user_type: 1 },
        );
        const studentSocketId = userDetails.map((student) => {
            return {
                socketEventId: student.socketEventId,
                user_type: student.user_type,
                _id: student._id,
            };
        });

        if (studentSocketId && studentSocketId.length > 0) {
            await this.sendActivityEvent(studentSocketId, activityId);
        }
        const sendSocketData = [];
        // student response
        eventId = studentEventId;
        data = JSON.stringify(studentStatus);
        sendSocketData.push({ eventId, data });
        // staff data
        const staffResult = {
            examOnLive: false,
            data: await this.getStaffTimeBasedResult({ activityId, totalStudents }),
        };
        eventId = staffEventId;
        data = JSON.stringify(staffResult);
        sendSocketData.push({ eventId, data });
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    } catch (error) {
        throw new Error(error);
    }
};

// format question with attachment link
exports.formatQuestionsWithAttachmentLink = async (questions) => {
    for (const question of questions) {
        const { attachments, options, _id, sloIds, taxonomyIds } = question;
        const questionAttachments = [];
        // get attachments unsigned url
        if (attachments && attachments.length > 0) {
            for (const attachment of attachments) {
                const { size, _id: attachmentId, link } = attachment;
                let unsignedUrl;
                if (link) {
                    unsignedUrl = await getUnsignedUrl(link, _id);
                }
                questionAttachments.push({ size, _id: attachmentId, link: unsignedUrl });
            }
            question.attachments = questionAttachments;
        }

        // get option attachments unsigned url
        const questionOptionWithAttachments = [];
        if (options && options.length > 0) {
            for (const option of options) {
                const { attachments: optionAttachments } = option;
                const questionOptionAttachments = [];
                for (const optionAttachment of optionAttachments) {
                    const {
                        size: optionAttachmentSize,
                        _id: optionAttachmentId,
                        link,
                    } = optionAttachment;
                    let unsignedUrl;
                    if (link) {
                        unsignedUrl = await getUnsignedUrl(link, _id);
                    }
                    questionOptionAttachments.push({
                        size: optionAttachmentSize,
                        _id: optionAttachmentId,
                        link: unsignedUrl,
                    });
                }

                option.attachments = questionOptionAttachments;
                questionOptionWithAttachments.push(option);
            }
            question.options = questionOptionWithAttachments;
        }
        if (sloIds && sloIds.length) {
            // slo name format
            const sloIdEntry = sloIds.map((sloId) => convertToMongoObjectId(sloId));
            const sloDetails = await getClosBySloIds(sloIdEntry);
            const cloDetails = await getClos(sloIdEntry);
            sloDetailList = sloIds
                .map((sloId) => {
                    const sloList = sloDetails.find((sloDetail) =>
                        sloDetail.slos.find((slo) => slo.slo_id === sloId.toString()),
                    );
                    let cloList;
                    if (cloDetails.length) {
                        cloList = cloDetails.find(
                            (cloDetail) => cloDetail._id.toString() === sloId.toString(),
                        );
                    }
                    let sloIdDetail;
                    if (sloList) {
                        sloIdDetail = sloList.slos.find((slo) => slo.slo_id === sloId.toString());
                        sloIdDetail._id = sloIdDetail.slo_id;
                        sloIdDetail.type = DS_SLO_KEY;
                    }
                    if (!sloIdDetail && cloList) {
                        sloIdDetail = { _id: cloList._id, name: cloList.name, type: DS_CLO_KEY };
                    }
                    return sloIdDetail;
                })
                .filter((item) => item); //removing undefined and null values from array using filter
            question.sloIds = sloDetailList;
        }
        if (taxonomyIds && taxonomyIds.length) {
            // taxonomy
            const taxonomyIdEntry = taxonomyIds.map((taxonomyId) => taxonomyId);
            const taxonomy = await Taxonomy.find(
                { _id: { $in: taxonomyIdEntry } },
                { name: 1, _id: 1 },
            );
            question.taxonomyIds = taxonomy;
        }
    }
    return questions;
};

// get staff time result (quiz)
exports.getStaffTimeBasedResult = async ({ activityId, totalStudents }) => {
    const { students, questions } = await Activities.findOne(
        { _id: convertToMongoObjectId(activityId) },
        { students: 1, questions: 1 },
    ).exec();
    let questionIds;
    questionIds = questions.sort((a, b) => {
        return a.order - b.order;
    });
    questionIds = questions.map((question) => question._id);
    const questionEntry = await Question.find({
        _id: { $in: questionIds },
    });

    const studentQuestion = questionEntry.map((question) => {
        const {
            _id,
            text,
            options,
            questionType,
            attachments,
            feedback,
            questionMoved,
            sloIds,
            taxonomyIds,
            acceptQuestionResponse,
        } = question;
        let studentWithQuestion;
        let studentAttendQuestionCount = 0;
        if (students && students.length > 0) {
            studentWithQuestion = students.filter((student) => {
                const { questions: studentQuestions } = student;
                if (
                    studentQuestions.find(
                        (studentEntry) =>
                            studentEntry._questionId &&
                            studentEntry._optionId &&
                            studentEntry._questionId.toString() === _id.toString(),
                    )
                ) {
                    studentAttendQuestionCount++;
                    return true;
                }
                return false;
            });
        }

        const studentOptions = options.map((option) => {
            const {
                _id: optionId,
                text: optionText,
                answer,
                attachments: optionAttachments,
            } = option;
            let studentWithOption;
            if (studentWithQuestion && studentWithQuestion.length > 0) {
                studentWithOption = studentWithQuestion.filter((student) => {
                    const { questions: studentQuestions } = student;
                    if (
                        studentQuestions.find(
                            (studentEntry) =>
                                studentEntry._optionId &&
                                studentEntry._optionId.toString() === optionId.toString(),
                        )
                    ) {
                        return true;
                    }
                    return false;
                });
            }
            const studentCount =
                studentWithOption && studentWithOption.length > 0 ? studentWithOption.length : 0;
            const percentage =
                ((studentCount / studentAttendQuestionCount) * 100).toFixed() !== 'NaN'
                    ? ((studentCount / studentAttendQuestionCount) * 100).toFixed()
                    : 0;
            return {
                _id: optionId,
                text: optionText,
                answer,
                attachments: optionAttachments,
                studentAnsweredCount: studentCount,
                totalStudentAnswered: studentAttendQuestionCount,
                percentage,
            };
        });
        let movedStatus = false;
        if (questionMoved) {
            movedStatus = true;
        }
        return {
            _id,
            text,
            options: studentOptions,
            questionType,
            sloIds,
            taxonomyIds,
            feedback,
            attachments,
            studentAnsweredCount: studentAttendQuestionCount,
            questionMoved: movedStatus,
            acceptQuestionResponse,
        };
    });

    // format question
    const formatQuestion = this.formatQuestionsWithAttachmentLink(studentQuestion);
    return formatQuestion;
};

const activityEnd = async (activityId, userId) => {
    try {
        const {
            endTime,
            socketEventStudentId: studentEventId,
            socketEventStaffId: staffEventId,
            sessionId,
            scheduleIds,
        } = await Activities.findOne(
            {
                _id: convertToMongoObjectId(activityId),
                isDeleted: false,
            },
            {
                endTime: 1,
                socketEventStudentId: 1,
                socketEventStaffId: 1,
                sessionId: 1,
                scheduleIds: 1,
            },
        );
        const updateQuery = { _id: convertToMongoObjectId(activityId) };
        const updateData = {
            quizStopBy: convertToMongoObjectId(userId),
            endTime: timestampNow(),
            status: COMPLETED,
        };
        let data = { $set: updateData };
        await dsCustomUpdate(Activities, updateQuery, data);
        const studentStatus = { examOnLive: false };
        let eventId;
        const scheduleConvertIds = scheduleIds.map((scheduleId) =>
            convertToMongoObjectId(scheduleId),
        );
        let totalStudents;
        const courseSchedules = await CourseSchedule.find({
            _id: { $in: scheduleConvertIds },
            isDeleted: false,
            isActive: true,
        });
        const mergedStudents = [];
        const mergedStaffs = [];
        for (const courseSchedule of courseSchedules) {
            const { students, staffs, merge_status, merge_with } = courseSchedule;
            let scheduleStudents;
            if (merge_status) {
                const scheduleIds = merge_with.map((mergeWith) =>
                    convertToMongoObjectId(mergeWith.schedule_id),
                );
                if (scheduleIds.length) {
                    const schedules = await CourseSchedule.find({
                        _id: { $in: scheduleIds },
                        isDeleted: false,
                        isActive: true,
                    });
                    scheduleStudents = schedules.map((schedule) => schedule.students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                }
            }
            if (scheduleStudents && scheduleStudents.length) {
                scheduleStudents = scheduleStudents.concat(students);
                // eslint-disable-next-line prefer-spread
                scheduleStudents = [].concat.apply([], scheduleStudents);
            }
            if (scheduleStudents && scheduleStudents.length) {
                mergedStudents.push(scheduleStudents);
            } else {
                mergedStudents.push(students);
            }
            mergedStaffs.push(staffs);
        }
        // eslint-disable-next-line prefer-spread
        totalStudents = [].concat.apply([], mergedStudents);
        // eslint-disable-next-line prefer-spread
        const totalStaffs = [].concat.apply([], mergedStaffs);
        // different calendar and year and level based courses
        totalStudents = totalStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        const studentIds = totalStudents.map((student) => convertToMongoObjectId(student._id));
        const staffIds = totalStaffs.map((staff) => convertToMongoObjectId(staff._staff_id));
        const userIds = studentIds.concat(staffIds);
        const userDetails = await User.find(
            { _id: { $in: userIds } },
            { device_type: 1, fcm_token: 1, socketEventId: 1, user_type: 1 },
        );
        const studentSocketId = userDetails.map((student) => {
            return {
                socketEventId: student.socketEventId,
                user_type: student.user_type,
                _id: student._id,
            };
        });

        if (studentSocketId && studentSocketId.length > 0) {
            await this.sendActivityEvent(studentSocketId, activityId, sessionId);
        }
        const sendSocketData = [];
        // student response
        eventId = studentEventId;
        data = JSON.stringify(studentStatus);
        sendSocketData.push({ eventId, data });
        // staff data
        const staffResult = {
            examOnLive: false,
            data: await this.getStaffTimeBasedResult({ activityId, totalStudents }),
        };
        eventId = staffEventId;
        data = JSON.stringify(staffResult);
        sendSocketData.push({ eventId, data });
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
    } catch (error) {
        throw new Error(error);
    }
};

const sendFCM = async (message, device) => {
    try {
        await sendNotificationPush(message.to, message.data, device);
    } catch (error) {
        throw new Error(error);
    }
};

const capitalizeFirstLetter = (string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
};

const storeInAppNotification = async (data, notification, titleFlag = 'Started') => {
    try {
        const insetData = {
            ...data,
            rotation: notification.rotation,
            rotation_count: notification.rotation_count,
            title: `${capitalizeFirstLetter(notification.Title.toLowerCase())} ${titleFlag}`,
            description: notification.Body,
            courseId: notification.CourseId,
            activityId: notification.ActivityId,
            programId: notification.programId,
            institutionCalendarId: notification.institutionCalendarId,
            yearNo: notification.yearNo,
            levelNo: notification.levelNo,
            term: notification.term,
            mergeStatus: notification.mergeStatus,
            mergeType: notification.mergeType,
            sessionId: notification.Session,
            scheduleId: notification.ScheduleId,
            type: 'activity',
        };
        await insert(Notification, insetData);
    } catch (error) {
        throw new Error(error);
    }
};

const sendScheduleNotification = async (notifications) => {
    try {
        if (!notifications.length) return;
        const users = [];
        for (const datum of notifications) {
            const data = clone(datum);
            users.push({
                _id: data.userId,
                isViewed: false,
                push_status: 'failed',
                redirect: data.ScheduleId,
            });
            data.Title = `${capitalizeFirstLetter(data.Title.toLowerCase())} Scheduled`;
            const { Title, Body, deviceType, fcm_token, web_fcm_token } = data;
            data.ClickAction = 'activity_schedule';
            data.notificationType = 'activity_schedule';
            const notification = { title: Title, body: Body };
            const message = { data, priority: 'high' };
            if (fcm_token) {
                message.to = fcm_token;
                message.notification = deviceType === ANDROID ? undefined : notification;
                sendFCM(message, deviceType);
            }
            if (web_fcm_token) {
                message.to = web_fcm_token;
                message.notification = notification;
                sendFCM(message, WEB);
            }
        }
        const data = {
            users,
            staffStartWithExam: 'TIME',
            buttonAction: 'activity_schedule',
            notificationType: 'activity_schedule',
        };
        storeInAppNotification(data, notifications[0], 'Scheduled');
    } catch (error) {
        throw new Error(error);
    }
};

const sendStartNotification = async (notifications) => {
    try {
        if (!notifications.length) return;
        const users = [];
        for (const datum of notifications) {
            const data = clone(datum);
            users.push({
                _id: data.userId,
                isViewed: false,
                push_status: 'failed',
                redirect: data.ScheduleId,
            });
            data.Title = `${capitalizeFirstLetter(data.Title.toLowerCase())} Started`;
            const { Title, Body, deviceType, fcm_token, web_fcm_token } = data;
            data.ClickAction = 'activity_start';
            data.notificationType = 'activity_start';
            const notification = { title: Title, body: Body };
            const message = { data, priority: 'high' };
            if (fcm_token) {
                message.to = fcm_token;
                message.notification = deviceType === ANDROID ? undefined : notification;
                sendFCM(message, deviceType);
            }
            if (web_fcm_token) {
                message.to = web_fcm_token;
                message.notification = notification;
                sendFCM(message, WEB);
            }
        }
        const data = {
            users,
            staffStartWithExam: 'TIME',
            buttonAction: 'activity_start',
            notificationType: 'activity_start',
        };
        storeInAppNotification(data, notifications[0], 'Started');
    } catch (error) {
        throw new Error(error);
    }
};

const sendEndNotification = async (notifications) => {
    try {
        if (!notifications.length) return;
        const users = [];
        for (const datum of notifications) {
            const data = clone(datum);
            users.push({
                _id: data.userId,
                isViewed: false,
                push_status: 'failed',
                redirect: data.ScheduleId,
            });
            data.Title = `${capitalizeFirstLetter(data.Title.toLowerCase())} Ended`;
            const { Title, Body, deviceType, fcm_token, web_fcm_token } = data;
            data.ClickAction = 'activity_end';
            data.notificationType = 'activity_end';
            const notification = { title: Title, body: Body };
            const message = { data, priority: 'high' };
            if (fcm_token) {
                message.to = fcm_token;
                message.notification = deviceType === ANDROID ? undefined : notification;
                sendFCM(message, deviceType);
            }
            if (web_fcm_token) {
                message.to = web_fcm_token;
                message.notification = notification;
                sendFCM(message, WEB);
            }
        }
        const data = {
            users,
            staffStartWithExam: 'TIME',
            buttonAction: 'activity_end',
            notificationType: 'activity_end',
        };
        storeInAppNotification(data, notifications[0], 'Ended');
    } catch (error) {
        throw new Error(error);
    }
};

const activityCron = async () => {
    const autoStartResult = await getCurrentActivities('start');
    const autoEndResult = await getCurrentActivities('end');
    const startActivities = autoStartResult.activities;
    const startNotifications = autoStartResult.notifications;
    const endActivities = autoEndResult.activities;
    const endNotifications = autoEndResult.notifications;
    sendStartNotification(startNotifications);
    sendEndNotification(endNotifications);
    for (const startActivity of startActivities) {
        const { _id, createdBy, sessionId, scheduleIds } = startActivity;
        activityStart(_id, createdBy, sessionId, scheduleIds);
    }
    for (const endActivity of endActivities) {
        const { _id, createdBy, sessionId, scheduleIds } = endActivity;
        activityEnd(_id, createdBy);
    }
};

const sendNotificationByActivityId = async (activityId, mode = 'end') => {
    try {
        const activityQuery = { _id: activityId, isDeleted: false, isActive: true };
        const activity = await Activities.findOne(activityQuery)
            .populate({
                path: 'courseId',
                select: { course_code: 1, course_name: 1 },
            })
            .lean();
        const program = await Program.findById(activity._program_id, {
            college_program_type: 1,
        }).lean();
        const label = await DigiLabel.findOne({
            college_program_type: program.college_program_type,
        });
        if (!activity || !activity.scheduleIds || !activity.scheduleIds.length) return;
        const scheduleIds = activity.scheduleIds;
        const courseSchedules = await getCourseSchedules(scheduleIds);
        const users = await getUserIdWithSchedules(courseSchedules);
        const user = await User.findOne(
            { _id: convertToMongoObjectId(activity.createdBy) },
            { _id: 1, name: 1 },
        ).lean();
        if (user) {
            activity.createdBy = user;
        }
        activity.totalStudentAnsweredCount = activity.students.length;
        activity.totalStudentCount = users ? users.length : 0;
        activity.totalQuestionCount = activity.questions.length;
        sendDashboardNotification(users, activity);
        const activities = [activity];
        const notifications = [];
        for (const activity of activities) {
            const { staffStartWithExam, quizType } = activity;
            schedules = courseSchedules.filter((courseSchedule) => {
                const scheduleIds = activity.scheduleIds.map((scheduleId) => scheduleId.toString());
                return scheduleIds.includes(courseSchedule._id.toString());
            });
            studentGroups = schedules.map((schedule) => schedule.student_groups);
            groupNames = [];
            studentGroups.forEach((groups) => {
                const groupName = groups.map((studentGroup) => studentGroup.group_name).toString();
                const names = [];
                groupName.split(',').forEach((grp) => {
                    const groupSplitted = grp.split('-');
                    const getIVal = (index) => {
                        return groupSplitted[groupSplitted.length - index]
                            ? groupSplitted[groupSplitted.length - index] + '-'
                            : null;
                    };
                    let name = '';
                    name += getIVal(3) || '';
                    name += getIVal(2) || '';
                    name += getIVal(1) || '';
                    names.push(name);
                });
                groupNames.push(names.toString() + '  ');
            });
            activity.users = [];
            schedules.forEach((schedule) => {
                const { _id, staffs, students, type } = schedule;
                let betweenMessage = '';
                if (type !== 'schedule' && staffStartWithExam) {
                    betweenMessage = staffStartWithExam === 'TIME' ? 'Standard ' : 'One by one ';
                    betweenMessage += capitalizeFirstLetter(quizType.toLowerCase()) + '\n';
                }
                staffs.forEach((staff) => {
                    const user = users.find(
                        (user) =>
                            user.userId.toString() === staff._staff_id.toString() &&
                            user.ScheduleId.toString() === _id.toString(),
                    );
                    user.type = 'activity';
                    if (user) {
                        user.student_groups = groupNames.toString();
                        activity.users.push(user);
                        user.Title = activity.quizType;
                        user.Body =
                            activity.name +
                                //   getStartAndEndTimeFormate(activity.schedule) +
                                '\n' +
                                groupNames.toString() +
                                '\n' +
                                `${betweenMessage}` +
                                //  ' • ' +
                                user.programName +
                                ' • ' +
                                program.college_program_type ===
                            COLLEGE_PROGRAM_TYPE.MEDICAL
                                ? user.levelNo
                                : label.level +
                                  ' ' +
                                  user.levelNo.slice(6, user.levelNo.length) +
                                  ' • ' +
                                  user.courseName;
                        user.activityStatus = mode;
                        user.scheduleType = activity.type;
                        user.ActivityId = activity._id.toString();
                        notifications.push(user);
                    }
                });
                students.forEach((student) => {
                    const user = users.find(
                        (user) =>
                            user.userId.toString() === student._id.toString() &&
                            user.ScheduleId.toString() === _id.toString(),
                    );
                    user.type = 'activity';
                    if (user) {
                        activity.users.push(user);
                        user.Title = activity.quizType;
                        user.Body =
                            activity.name +
                                //   getStartAndEndTimeFormate(activity.schedule) +
                                '\n' +
                                `${betweenMessage}` +
                                // ' • ' +
                                user.programName +
                                ' • ' +
                                program.college_program_type ===
                            COLLEGE_PROGRAM_TYPE.MEDICAL
                                ? user.levelNo
                                : label.level +
                                  ' ' +
                                  user.levelNo.slice(6, user.levelNo.length) +
                                  ' • ' +
                                  user.courseName;
                        user.activityStatus = mode;
                        user.scheduleType = activity.type;
                        user.ActivityId = activity._id.toString();
                        notifications.push(user);
                    }
                });
            });
        }
        if (mode === 'schedule') sendScheduleNotification(notifications);
        else if (mode === 'end') sendEndNotification(notifications);
    } catch (error) {
        throw new Error(error);
    }
};

// send quiz scheduled notification
const sendQuizScheduleNotification = async (
    insert,
    sendNotificationPush,
    activity,
    sessionId = '',
    schedule = '',
    scheduleIds = [],
) => {
    try {
        const {
            _id: activityId,
            sessionId: activitySessionId,
            schedule: activitySchedule,
            scheduleIds: activityScheduleIds,
            name,
            staffStartWithExam,
            quizType,
        } = activity;
        let totalStudents;
        const session = sessionId || activitySessionId;
        let courseScheduleIds =
            scheduleIds && scheduleIds.length ? scheduleIds : activityScheduleIds;

        courseScheduleIds = await CourseSchedule.find({
            _id: { $in: courseScheduleIds },
            isDeleted: false,
            isActive: true,
        }).lean();

        if (courseScheduleIds && courseScheduleIds.length) {
            const mergedStudents = [];
            for (const scheduleIdEntry of courseScheduleIds) {
                const { _id: scheduleId, merge_status, merge_with, students } = scheduleIdEntry;
                let scheduleStudents;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const schedules = await CourseSchedule.find({
                            _id: { $in: scheduleId },
                            isDeleted: false,
                            isActive: true,
                        });
                        scheduleStudents = schedules.map((schedule) => schedule.students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);
                    }
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.concat(students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                } else {
                    scheduleStudents = students;
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.map((scheduleStudent) => {
                        const { name, status, _id } = scheduleStudent;
                        return { name, status, _id, scheduleId };
                    });
                    mergedStudents.push(scheduleStudents);
                }
            }
            // eslint-disable-next-line prefer-spread
            totalStudents = [].concat.apply([], mergedStudents);
            // different calendar and year and level based courses
            totalStudents = totalStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
            const totalStudentIds = totalStudents.map((totalStudent) =>
                convertToMongoObjectId(totalStudent._id),
            );
            let studentDetails = await User.find(
                {
                    _id: { $in: totalStudentIds },
                    $or: [{ fcm_token: { $exists: true } }, { web_fcm_token: { $exists: true } }],
                    device_type: { $exists: true },
                },
                {
                    name: 1,
                    mobile: 1,
                    biometric_data: 1,
                    device_type: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    socketEventId: 1,
                    user_type: 1,
                },
            );
            const studentSocketId = studentDetails.map((student) => {
                return {
                    socketEventId: student.socketEventId,
                    user_type: student.user_type,
                    _id: student._id,
                };
            });

            studentDetails = studentDetails.map((studentDetail) => {
                const { device_type, fcm_token, web_fcm_token, _id } = studentDetail;
                return { device_type, token: fcm_token, web_fcm_token, _user_id: _id };
            });

            if (studentSocketId && studentSocketId.length)
                await this.sendActivityEvent(studentSocketId, activityId);
            let scheduleDate;
            if (schedule || activitySchedule) {
                const scheduleEntry = schedule || activitySchedule;
                const { endDateAndTime, startDateAndTime } = scheduleEntry;
                const startHour = new Date(startDateAndTime).getHours();
                const startTimeZone = startDateAndTime > 12 ? 'PM' : 'AM';
                const startMinute = new Date(startDateAndTime).getMinutes();
                const endHour = new Date(endDateAndTime).getHours();
                const endTimeZone = endDateAndTime > 12 ? 'PM' : 'AM';
                const endMinute = new Date(endDateAndTime).getMinutes();
                const a = [{ day: 'numeric' }, { month: 'short' }];
                const startDate = join(new Date(startDateAndTime), a, ' ');
                const endDate = join(new Date(endDateAndTime), a, ' ');
                scheduleDate =
                    startDate +
                    ' ' +
                    startHour +
                    ':' +
                    startMinute +
                    ' ' +
                    startTimeZone +
                    '-' +
                    endDate +
                    ' ' +
                    endHour +
                    ':' +
                    endMinute +
                    ' ' +
                    endTimeZone;
            }
            if (studentDetails.length) {
                const title = capitalizeFirstLetter(quizType.toLowerCase()) + ' Scheduled';
                for (const studentDetail of studentDetails) {
                    let message = name; /*+ '\n' + scheduleDate*/
                    const {
                        token: fcm_token,
                        web_fcm_token,
                        _user_id: studentId,
                        device_type,
                    } = studentDetail;
                    const schedule = courseScheduleIds.find((scheduleId) =>
                        scheduleId.students.find(
                            (student) => student._id.toString() === studentId.toString(),
                        ),
                    );
                    if (schedule) {
                        const {
                            _id: scheduleId,
                            _course_id,
                            year_no,
                            level_no,
                            _program_id,
                            term,
                            _institution_calendar_id,
                            merge_status,
                            type,
                            course_name,
                            program_name,
                            rotation,
                            rotation_count,
                            session,
                        } = schedule;
                        let program = await Program.findById(_program_id, {
                            college_program_type: 1,
                        }).lean();
                        const label = await DigiLabel.findOne(
                            {
                                college_program_type: program.college_program_type,
                            },
                            { term: 1, year: 1 },
                        ).lean();
                        program = program.college_program_type;
                        message +=
                            '\n' +
                            `${
                                staffStartWithExam === 'TIME' ? 'Standard' : 'One by one'
                            } ${capitalizeFirstLetter(quizType.toLowerCase())}`;
                        message += '\n' + program_name + ' • ' + level_no + ' • ' + course_name;
                        const pushData = {
                            Title: title,
                            Body: message,
                            ClickAction: 'activity_schedule',
                            ScheduleId: scheduleId,
                            Session: schedule.session ? session._session_id : undefined,
                            programId: _program_id,
                            institutionCalendarId: _institution_calendar_id,
                            yearNo: year_no,
                            levelNo:
                                program.collegeProgramType === COLLEGE_PROGRAM_TYPE.MEDICAL
                                    ? level_no
                                    : level_no.slice(6, level_no.length),
                            term:
                                program.collegeProgramType === COLLEGE_PROGRAM_TYPE.MEDICAL
                                    ? term
                                    : label.level + ' ' + label.term,
                            mergeStatus: merge_status,
                            mergeType: type,
                            CourseId: _course_id,
                            activityId,
                            StaffStartWithExam: staffStartWithExam,
                            _id: scheduleId,
                            courseId: _course_id,
                            notificationType: 'activity_schedule',
                            rotation,
                            rotation_count,
                            type: 'activity',
                        };
                        if (fcm_token) {
                            sendNotificationPush(fcm_token, pushData, device_type);
                        }
                        if (web_fcm_token) {
                            sendNotificationPush(web_fcm_token, pushData, device_type);
                        }
                    }
                }
            }
            if (courseScheduleIds && courseScheduleIds.length) {
                for (const scheduleId of courseScheduleIds) {
                    const users = totalStudents.filter((totalStudent) => {
                        if (totalStudent.scheduleId.toString() === scheduleId._id.toString()) {
                            return {
                                _id: totalStudent._id,
                            };
                        }
                    });
                    const data = {
                        users,
                        staffStartWithExam,
                        rotation: scheduleId.rotation,
                        rotation_count: scheduleId.rotation_count,
                        title: capitalizeFirstLetter(quizType.toLowerCase()) + ' Scheduled',
                        description:
                            name +
                            '\n' +
                            /*  scheduleDate +
                            '\n' +*/
                            quizType +
                            '\n' +
                            scheduleId.program_name +
                            ' • ' +
                            scheduleId.level_no +
                            ' • ' +
                            scheduleId.course_name,
                        buttonAction: 'activity_schedule',
                        courseId: scheduleId._course_id,
                        activityId,
                        notificationType: 'activity_schedule',
                        programId: scheduleId._program_id,
                        institutionCalendarId: scheduleId._institution_calendar_id,
                        yearNo: scheduleId.year_no,
                        levelNo: scheduleId.level_no,
                        mergeStatus: scheduleId.merge_status,
                        mergeType: scheduleId.type,
                        sessionId: scheduleId.session ? scheduleId.session._session_id : undefined,
                        scheduleId: scheduleId ? scheduleId._id : undefined,
                    };
                    await insert(Notification, data);
                }
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};

getActivity = async (id, userId) => {
    try {
        const activityQuery = { _id: id, isDeleted: false, isActive: true };
        let activities = await Activities.findOne(activityQuery, project)
            .populate({ path: 'createdBy', select: { name: 1, _id: 1 } })
            .exec();
        if (!activities) return sendResponse(res, 200, false, DS_NO_DATA_FOUND);
        const {
            _id: activityId,
            sessionId,
            scheduleIds,
            status,
            name,
            type,
            quizType,
            createdBy,
            setQuizTime,
            socketEventStaffId,
            socketEventStudentId,
            staffStartWithExam,
            startTime,
            endTime,
            students,
            questions,
        } = activities;
        let totalStudents;
        let studentGroupsName = '';
        if (scheduleIds && scheduleIds.length) {
            const courseSchedules = await CourseSchedule.find({
                _id: { $in: scheduleIds },
            });
            const mergedStudents = [];
            for (const scheduleIdEntry of courseSchedules) {
                const { _id: scheduleId, merge_status, merge_with, students } = scheduleIdEntry;
                let scheduleStudents;
                if (merge_status) {
                    const scheduleIds = merge_with.map((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    if (scheduleIds.length) {
                        const schedules = await CourseSchedule.find({
                            _id: { $in: scheduleIds },
                            isDeleted: false,
                            isActive: true,
                        });
                        scheduleStudents = schedules.map((schedule) => schedule.students);
                        // eslint-disable-next-line prefer-spread
                        scheduleStudents = [].concat.apply([], scheduleStudents);

                        schedules.push(scheduleIdEntry);
                        schedules.forEach((courseSchedule) => {
                            const { student_groups, _id } = courseSchedule;
                            if (student_groups && student_groups.length) {
                                let studentGroups = student_groups.map((student_group) => {
                                    const { group_name, session_group } = student_group;
                                    let groupName = group_name.split('-').slice(-2);
                                    groupName = groupName[1]
                                        ? groupName[0] + '-' + groupName[1]
                                        : groupName[0];
                                    if (session_group && session_group.length) {
                                        let sessionGroup = session_group.map((groupNameEntry) => {
                                            let groupNames = groupNameEntry.group_name
                                                .split('-')
                                                .slice(-2);
                                            groupNames = groupNames[1]
                                                ? groupNames[0] + '-' + groupNames[1]
                                                : groupNames[0];
                                            return groupNames;
                                        });
                                        sessionGroup = sessionGroup.toString();
                                        groupName += '(' + sessionGroup + ')';
                                    }
                                    return groupName;
                                });
                                studentGroups = studentGroups.toString();
                                studentGroupsName += studentGroups;
                            }
                        });
                    }
                } else {
                    const { student_groups } = scheduleIdEntry;
                    if (student_groups && student_groups.length) {
                        let studentGroups = student_groups.map((student_group) => {
                            const { group_name, session_group } = student_group;
                            let groupName = group_name.split('-').slice(-2);
                            groupName = groupName[1]
                                ? groupName[0] + '-' + groupName[1]
                                : groupName[0];
                            if (session_group && session_group.length) {
                                let sessionGroup = session_group.map((groupNameEntry) => {
                                    let groupNames = groupNameEntry.group_name.split('-').slice(-2);
                                    groupNames = groupNames[1]
                                        ? groupNames[0] + '-' + groupNames[1]
                                        : groupNames[0];
                                    return groupNames;
                                });
                                sessionGroup = sessionGroup.toString();
                                groupName += '(' + sessionGroup + ')';
                            }
                            return groupName;
                        });
                        studentGroups = studentGroups.toString();
                        studentGroupsName += studentGroups;
                    }
                }
                if (scheduleStudents && scheduleStudents.length) {
                    scheduleStudents = scheduleStudents.concat(students);
                    // eslint-disable-next-line prefer-spread
                    scheduleStudents = [].concat.apply([], scheduleStudents);
                } else {
                    scheduleStudents = students;
                }
                if (scheduleStudents && scheduleStudents.length) {
                    mergedStudents.push(scheduleStudents);
                }
            }
            // eslint-disable-next-line prefer-spread
            totalStudents = [].concat.apply([], mergedStudents);
            // different calendar and year and level based courses
            totalStudents = totalStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
        }
        let questionIds;
        questionIds = questions.sort((a, b) => {
            return a.order - b.order;
        });
        questionIds = questions.map((question) => convertToMongoObjectId(question._id));
        let selectedQuestion = [];
        if (questionIds.length)
            selectedQuestion = await Question.find({ _id: { $in: questionIds }, isDeleted: false });
        let answeredQuestions;
        if (userId) {
            answeredStudent = students.find((student) => cs(student._studentId) === cs(userId));
            if (answeredStudent) answeredQuestions = answeredStudent.questions;
        }
        const activityQuestions = await formatQuestions(selectedQuestion, answeredQuestions);
        let sessionDetails;
        if (sessionId) {
            sessionDetails = {
                _id: sessionId._id,
                session_date: sessionId.session_date,
                start_time: sessionId.start_time,
                end_time: sessionId.end_time,
            };
        }
        activities = {
            status,
            _id: activityId,
            name,
            type,
            quizType,
            createdBy,
            studentGroupsName,
            setQuizTime,
            socketEventStaffId,
            socketEventStudentId,
            staffStartWithExam,
            startTime,
            endTime,
            questions,
            answeredCount: activityQuestions.answeredCount,
            totalQuestionCount: activityQuestions.totalQuestionCount,
            studentCorrectAnsweredCount: activityQuestions.studentCorrectAnswered,
            totalStudentAnsweredCount: students.length,
            totalStudentCount: totalStudents.length,
        };
        return activities;
    } catch (error) {
        throw new Error(error);
    }
};

const getAllSessionList = async (
    courseId,
    userId,
    _program_id,
    year_no,
    level_no,
    term,
    rotation,
    rotation_count,
    _institution_calendar_id,
    courseAdmin,
) => {
    try {
        const cond = {
            isDeleted: false,
        };
        if (courseAdmin !== 'true') {
            cond['staffs._staff_id'] = convertToMongoObjectId(userId);
        }

        if (courseId) {
            cond._course_id = convertToMongoObjectId(courseId);
        }

        if (_program_id) {
            cond._program_id = convertToMongoObjectId(_program_id);
            cond.year_no = year_no;
            cond.level_no = level_no;
            cond.term = term;
            cond.rotation = rotation;
            cond._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
            if (rotation_count) {
                cond.rotation_count = rotation_count;
            }
        }
        const proj = {
            _course_id: 1,
            session: 1,
            _id: 1,
            title: 1,
            type: 1,
            merge_status: 1,
            merge_with: 1,
            _program_id: 1,
            year_no: 1,
            level_no: 1,
            term: 1,
            rotation: 1,
            rotation_count: 1,
            _institution_calendar_id: 1,
        };
        let courseScheduleData = await CourseSchedule.find(cond, proj);
        const sessionIds = [];
        const scheduleIds = [];
        let regularSessionList = [];
        const list = [];
        courseScheduleData = courseScheduleData.sort((a, b) =>
            a.session &&
            b.session &&
            a.session.s_no &&
            b.session.s_no &&
            a.type === REGULAR &&
            b.type === REGULAR &&
            a.session.s_no > b.session.s_no
                ? 1
                : -1,
        );
        courseScheduleData.forEach((courseScheduleDatum) => {
            const { session, _id, title, type, merge_status, merge_with } = courseScheduleDatum;
            const { _session_id, s_no } = session;
            if (
                session &&
                _session_id &&
                !sessionIds.find(
                    (sessionIdEntry) =>
                        sessionIdEntry.sessionId.toString() === _session_id.toString() &&
                        sessionIdEntry.merge_status.toString() === merge_status.toString(),
                )
            ) {
                sessionIds.push({ sessionId: _session_id.toString(), merge_status });
                if (merge_status) {
                    const scheduleIds = merge_with.filter((mergeWith) =>
                        convertToMongoObjectId(mergeWith.schedule_id),
                    );
                    const scheduleIdsToString = merge_with.map((mergeWith) =>
                        mergeWith.session_id.toString(),
                    );
                    if (
                        !regularSessionList.find((regularSessionEntry) =>
                            scheduleIdsToString.includes(regularSessionEntry._id.toString()),
                        )
                    ) {
                        let sessionTitle = session.delivery_symbol + session.delivery_no;
                        scheduleIds.forEach((scheduleId) => {
                            sessionIds.push({
                                sessionId: scheduleId.session_id.toString(),
                                merge_status,
                            });
                            const schedule = courseScheduleData.find(
                                (courseSchedule) =>
                                    courseSchedule._id.toString() ===
                                    scheduleId.schedule_id.toString(),
                            );
                            sessionTitle +=
                                ',' +
                                schedule.session.delivery_symbol +
                                schedule.session.delivery_no;
                        });
                        regularSessionList.push({
                            _id,
                            title: sessionTitle,
                            type: REGULAR,
                            s_no,
                        });
                    }
                } else {
                    sessionIds.push({
                        sessionId: _session_id.toString(),
                        merge_status,
                    });
                    regularSessionList.push({
                        _id: _session_id,
                        title: session.delivery_symbol + session.delivery_no,
                        type: REGULAR,
                        s_no,
                    });
                }
            }
            regularSessionList = regularSessionList.sort((a, b) =>
                a.s_no && b.s_no && a.s_no > b.s_no ? 1 : -1,
            );
            if (title && !scheduleIds.includes(_id.toString())) {
                scheduleIds.push(_id.toString());
                list.push({
                    _id,
                    title,
                    type,
                });
            }
        });
        return regularSessionList.concat(list);
    } catch (error) {
        throw new Error(error);
    }
};
module.exports = {
    getCommaSeparatedStudentGroup,
    getStudentGroup,
    getAllActivity,
    sendQuizScheduleNotification,
    activityCron,
    capitalizeFirstLetter,
    sendNotificationByActivityId,
    getAllSessionList,
    getAllAdminActivities,
    studentGroupByGroupName,
};
