const { convertToMongoObjectId, response_function, clone } = require('../utility/common');
const {
    STUDENT_CRITERIA_MANIPULATION,
    STUDENT_GROUP,
    COURSE_SCHEDULE,
    SCHEDULE_TYPES: { REGULAR },
    COMPLETED,
    ABSENT,
    LEAVE,
    PRESENT,
    LMS,
} = require('../utility/constants');
const { logger } = require('../utility/util_keys');

exports.lmsStudentAbsenceList = ({ lmsStudentSetting }) => {
    lmsStudentSetting.data.student_warning_absence_calculation = clone(
        lmsStudentSetting.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        ),
    );
    let warningAbsenceData = lmsStudentSetting.data.student_warning_absence_calculation.filter(
        (ele) => ele.isDeleted === false,
    );
    warningAbsenceData = warningAbsenceData.sort((a, b) => {
        let comparison = 0;
        if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
            comparison = -1;
        } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
            comparison = 1;
        }
        return comparison;
    });
    return warningAbsenceData;
};
