const levelBasedAccessSchema = require('./levelBasedAccess.module');
const { convertToMongoObjectId } = require('../utility/common');
const programSchema = require('../models/digi_programs');
const curriculamSchema = require('../models/digi_curriculum');
const roleSchema = require('../models/role');
const userRoleSchema = require('../models/role_assign');
const userSchema = require('../models/user');
const { LEVEL_AUTHOR } = require('../utility/constants');

exports.updateLevelUserAccess = async ({ body = {}, headers = {} }) => {
    try {
        const { level, userIds, programId, curriculumId, studentGroup, schedule } = body;
        const { _institution_id } = headers;
        const roleId = await roleSchema
            .findOne(
                {
                    name: LEVEL_AUTHOR,
                    isDeleted: false,
                    isActive: true,
                },
                { _id: 1 },
            )
            .lean();
        const userRoles = await userRoleSchema
            .find({ _user_id: { $in: userIds } }, { _user_id: 1, 'roles._role_id': 1 })
            .lean();
        const bulkUpdate = [];
        const userBulkUpdate = [];
        const courseAuthorRole = { _role_id: roleId._id, role_name: LEVEL_AUTHOR };
        userIds.map((staffIdElement) => {
            const userRole = userRoles.find(
                (userRoleElement) => String(userRoleElement._user_id) === String(staffIdElement),
            );
            if (userRole) {
                if (
                    !userRole.roles.find(
                        (roleElement) => String(roleElement._role_id) === String(roleId._id),
                    )
                ) {
                    bulkUpdate.push({
                        updateOne: {
                            filter: {
                                _user_id: convertToMongoObjectId(staffIdElement),
                            },
                            update: {
                                $push: {
                                    roles: courseAuthorRole,
                                },
                            },
                        },
                    });
                }
            } else {
                const userRoleId = convertToMongoObjectId();
                userBulkUpdate.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(staffIdElement),
                        },
                        update: { $set: { _role_id: userRoleId } },
                    },
                });
                bulkUpdate.push({
                    insertOne: {
                        document: {
                            _id: userRoleId,
                            _institution_id: convertToMongoObjectId(_institution_id),
                            _user_id: convertToMongoObjectId(staffIdElement),
                            roles: [courseAuthorRole],
                        },
                    },
                });
            }
        });
        if (bulkUpdate.length) await userRoleSchema.bulkWrite(bulkUpdate);
        if (userBulkUpdate.length) await userSchema.bulkWrite(userBulkUpdate);
        const levelBasedAccessData = await levelBasedAccessSchema.updateOne(
            {
                level,
                programId: convertToMongoObjectId(programId),
                curriculumId: convertToMongoObjectId(curriculumId),
                _institution_id: convertToMongoObjectId(_institution_id),
            },
            {
                $set: {
                    level,
                    programId: convertToMongoObjectId(programId),
                    curriculumId: convertToMongoObjectId(curriculumId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    ...(userIds && { userIds }),
                    ...(typeof studentGroup === 'boolean' && { studentGroup }),
                    ...(typeof schedule === 'boolean' && { schedule }),
                },
            },
            { upsert: true },
        );
        if (levelBasedAccessData?.upsertedCount || levelBasedAccessData?.modifiedCount)
            return { statusCode: 200, message: 'UPDATED_SUCCESSFULLY' };
        return { statusCode: 400, message: 'UPDATE_ERROR' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getLevelUserAccess = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const levelBasedAccessData = await levelBasedAccessSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    curriculumId: 1,
                    level: 1,
                    programId: 1,
                    schedule: 1,
                    studentGroup: 1,
                    userIds: 1,
                },
            )
            .lean();
        if (!levelBasedAccessData?.length) return { statusCode: 200, message: 'NO_DATA_FOUND' };
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: levelBasedAccessData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.listOfProgram = async ({ headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const programData = await programSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                {
                    name: 1,
                    code: 1,
                },
            )
            .lean();
        const curriculamData = await curriculamSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    curriculum_name: 1,
                    _program_id: 1,
                },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { programData, curriculamData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getCurriculumByProgramId = async ({ params = {}, headers = {} }) => {
    try {
        const { programId } = params;
        const { _institution_id } = headers;
        const curriculumData = await curriculamSchema
            .find(
                {
                    _program_id: convertToMongoObjectId(programId),
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isDeleted: false,
                    isActive: true,
                },
                {
                    'year_level.levels.level_name': 1,
                },
            )
            .lean();
        const levelBasedAccessData = await levelBasedAccessSchema
            .find(
                {
                    programId: convertToMongoObjectId(programId),
                },
                {
                    curriculumId: 1,
                    level: 1,
                    schedule: 1,
                    studentGroup: 1,
                    userIds: 1,
                },
            )
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { curriculumData, levelBasedAccessData },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.userRoleBasedCurriculum = async ({ headers = {} }) => {
    try {
        const { _user_id } = headers;
        const levelBasedAccessData = await levelBasedAccessSchema
            .find(
                {
                    userIds: { $in: convertToMongoObjectId(_user_id) },
                },
                {
                    curriculumId: 1,
                    level: 1,
                    programId: 1,
                    schedule: 1,
                    studentGroup: 1,
                },
            )
            .lean();
        if (!levelBasedAccessData?.length) {
            return { statusCode: 200, message: 'NO_DATA_FOUND', data: [] };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: levelBasedAccessData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
