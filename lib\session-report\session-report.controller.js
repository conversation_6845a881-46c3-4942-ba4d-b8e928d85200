const {
    getUserRoleProgramList,
    getActiveInstitutionCalendars,
} = require('../utility/utility.service');
const { logger } = require('../utility/util_keys');
const {
    getDateWiseAttendanceReport,
    getProgramCourse,
    getScheduleReportDetails,
    getUserProgram,
    getDateWiseAttendanceReportExport,
} = require('./session-report.service');
const { checkHandoutUploadDocument } = require('../courseHandout/courseHandout.service');

exports.getAttendanceReport = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { sessionDate } = params;
        const {
            // programIds,
            // institutionCalendarId,
            start,
            end,
            term,
            year,
            levelNo,
            courseId,
            rotationCount,
            sessionStatus,
        } = query;
        let { programIds, institutionCalendarId } = query;

        // Get Active InstitutionCalendars
        if (!institutionCalendarId) {
            const { activeCalendarIds } = await getActiveInstitutionCalendars({ _institution_id });
            // Todo Deactivating for Current Release But have to Activate for Multi Calendar Flow
            // institutionCalendarId = activeCalendarIds;
            institutionCalendarId = [activeCalendarIds[0]];
        } else if (!Array.isArray(institutionCalendarId))
            institutionCalendarId = [institutionCalendarId];

        let courseCoordinatorFilter = [];
        // Checking User Role Program if No Program Filter enable
        // if (!programIds) {
        const { userProgramIds, userCourseIds, isCourseAdmin, isProgramAdmin } =
            await getUserRoleProgramList({
                _institution_id,
                user_id,
                role_id,
                institutionCalendarId,
            });
        if (!isCourseAdmin && !isProgramAdmin)
            return { statusCode: 200, message: 'DATA_RETRIEVED', data: {} };
        if ((!programIds || (programIds && !programIds.length)) && userProgramIds.length)
            programIds = userProgramIds;
        courseCoordinatorFilter = userCourseIds.length ? userCourseIds : [];
        if (isCourseAdmin && !courseCoordinatorFilter.length)
            return { statusCode: 200, message: 'DATA_RETRIEVED', data: {} };
        // }
        const queryParsing = {
            _institution_id,
            sessionDate,
            programIds,
            institutionCalendarId,
            start,
            end,
            term,
            year,
            levelNo,
            courseId,
            rotationCount,
            sessionStatus,
            courseCoordinatorFilter,
        };
        logger.info(
            JSON.stringify(queryParsing),
            'InstitutionSessionReport -> getAttendanceReport -> Start',
        );
        const courseScheduleReport = await getDateWiseAttendanceReport(queryParsing);
        logger.info(
            JSON.stringify(queryParsing),
            'InstitutionSessionReport -> getAttendanceReport -> End',
        );
        if (courseScheduleReport) {
            const courseHandData = await checkHandoutUploadDocument({
                scheduleList: courseScheduleReport?.scheduleList,
                _institution_id,
            });
            if (courseHandData?.scheduleList) {
                courseScheduleReport.scheduleList = courseHandData?.scheduleList;
                courseScheduleReport.courseHandout = courseHandData?.courseHandout;
            }
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseScheduleReport };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'InstitutionSessionReport -> getAttendanceReport -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getProgramCourseList = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { sessionDate } = params;
        const { programIds } = query;
        let { institutionCalendarId } = query;

        // Get Active InstitutionCalendars
        if (!institutionCalendarId) {
            const { activeCalendarIds } = await getActiveInstitutionCalendars({ _institution_id });
            // Todo Deactivating for Current Release But have to Activate for Multi Calendar Flow
            // institutionCalendarId = activeCalendarIds;
            institutionCalendarId = [activeCalendarIds[0]];
        } else if (!Array.isArray(institutionCalendarId))
            institutionCalendarId = [institutionCalendarId];

        let courseCoordinatorFilter = [];
        // Checking User Role Program
        const { userCourseIds } = await getUserRoleProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
        });
        courseCoordinatorFilter = userCourseIds.length
            ? userCourseIds.filter(
                  (courseElement) => courseElement._program_id.toString() === programIds,
              )
            : [];

        const queryParsing = {
            sessionDate,
            programIds,
            institutionCalendarId,
            courseCoordinatorFilter,
        };
        logger.info(queryParsing, 'InstitutionSessionReport -> getProgramCourseList -> Start');
        const programCourseDatas = await getProgramCourse(queryParsing);
        logger.info(queryParsing, 'InstitutionSessionReport -> getProgramCourseList -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: programCourseDatas };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'InstitutionSessionReport -> getProgramCourseList -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getScheduleDetails = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { scheduleId } = query;
        const queryParsing = { _institution_id, scheduleId };
        logger.info(queryParsing, 'InstitutionSessionReport -> getScheduleDetails -> Start');
        const scheduleDetails = await getScheduleReportDetails(queryParsing);
        logger.info(queryParsing, 'InstitutionSessionReport -> getScheduleDetails -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: scheduleDetails };
    } catch (error) {
        logger.error({ error }, 'InstitutionSessionReport -> getScheduleDetails -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getUserPrograms = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        let { institutionCalendarId } = query;

        if (!institutionCalendarId) {
            const { activeCalendarIds } = await getActiveInstitutionCalendars({ _institution_id });
            // Todo Deactivating for Current Release But have to Activate for Multi Calendar Flow
            institutionCalendarId = [activeCalendarIds[0]];
        } else if (!Array.isArray(institutionCalendarId))
            institutionCalendarId = [institutionCalendarId];

        const { userProgramIds } = await getUserRoleProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
        });
        const queryParsing = {
            _institution_id,
            user_id,
            role_id,
            userProgramIds,
        };
        if (!userProgramIds.length) return { statusCode: 200, message: 'DATA_RETRIEVED', data: [] };
        logger.info(queryParsing, 'InstitutionSessionReport -> getUserPrograms -> Start');
        const userProgramList = await getUserProgram(queryParsing);
        logger.info(queryParsing, 'InstitutionSessionReport -> getUserPrograms -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: userProgramList };
    } catch (error) {
        logger.error({ error }, 'InstitutionSessionReport -> getUserPrograms -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getAttendanceReportExport = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { sessionDate } = params;
        const { start, end, term, year, levelNo, courseId, rotationCount, sessionStatus } = query;
        let { programIds, institutionCalendarId } = query;

        // Get Active InstitutionCalendars
        if (!institutionCalendarId) {
            const { activeCalendarIds } = await getActiveInstitutionCalendars({ _institution_id });
            // Todo Deactivating for Current Release But have to Activate for Multi Calendar Flow
            // institutionCalendarId = activeCalendarIds;
            institutionCalendarId = [activeCalendarIds[0]];
        } else if (!Array.isArray(institutionCalendarId))
            institutionCalendarId = [institutionCalendarId];

        let courseCoordinatorFilter = [];
        // Checking User Role Program if No Program Filter enable
        const { userProgramIds, userCourseIds, isCourseAdmin, isProgramAdmin } =
            await getUserRoleProgramList({
                _institution_id,
                user_id,
                role_id,
                institutionCalendarId,
            });
        if (!isCourseAdmin && !isProgramAdmin)
            return { statusCode: 200, message: 'DATA_RETRIEVED', data: {} };
        if ((!programIds || (programIds && !programIds.length)) && userProgramIds.length)
            programIds = userProgramIds;
        courseCoordinatorFilter = userCourseIds.length ? userCourseIds : [];
        const queryParsing = {
            _institution_id,
            sessionDate,
            programIds,
            institutionCalendarId,
            start,
            end,
            term,
            year,
            levelNo,
            courseId,
            rotationCount,
            sessionStatus,
            courseCoordinatorFilter,
        };
        logger.info(queryParsing, 'InstitutionSessionReport -> getAttendanceReportExport -> Start');
        const courseScheduleReport = await getDateWiseAttendanceReportExport(queryParsing);
        if (courseScheduleReport) {
            const courseHandData = await checkHandoutUploadDocument({
                scheduleList: courseScheduleReport?.scheduleList,
                _institution_id,
            });
            if (courseHandData?.scheduleList) {
                courseScheduleReport.scheduleList = courseHandData?.scheduleList;
                courseScheduleReport.courseHandout = courseHandData?.courseHandout;
            }
        }
        logger.info(queryParsing, 'InstitutionSessionReport -> getAttendanceReportExport -> End');
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: courseScheduleReport };
    } catch (error) {
        console.log(error);
        logger.error({ error }, 'InstitutionSessionReport -> getAttendanceReportExport -> Error');
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
