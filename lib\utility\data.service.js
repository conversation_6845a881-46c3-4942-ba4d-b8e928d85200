const { INSTITUTION_CALENDAR, PUBLISHED, COURSE_SCHEDULE_SETTING } = require('./constants');
const { logger } = require('./util_keys');
const institutionCalendar = require('mongoose').model(INSTITUTION_CALENDAR);
const course_schedule_settings = require('mongoose').model(COURSE_SCHEDULE_SETTING);

/**
 * Push Institution Calendar last published data
 */
const institutionCalendarData = {
    status: false,
    data: [],
};
const loadInstitutionCalendar = async () => {
    logger.info('Loading Data DB');
    institutionCalendarData.data = await institutionCalendar
        .findOne(
            { isDeleted: false, status: PUBLISHED },
            {
                _id: 1,
                calendar_name: 1,
                start_date: 1,
                end_date: 1,
            },
        )
        .sort({ _id: -1 })
        .limit(1)
        .lean();
    institutionCalendarData.status = true;
};

/**
 * Institution Calendar get last published
 */
const getInstitutionCalendar = async () => {
    if (institutionCalendarData.status) {
        logger.info('From Local Data');
        return institutionCalendarData.data;
    }
    logger.info('From DB');
    institutionCalendarData.data = await institutionCalendar
        .findOne(
            { isDeleted: false, status: PUBLISHED },
            {
                _id: 1,
                calendar_name: 1,
                start_date: 1,
                end_date: 1,
            },
        )
        .sort({ _id: -1 })
        .limit(1)
        .lean();
    institutionCalendarData.status = true;
    return institutionCalendarData.data;
};

const remoteInfraData = {
    status: false,
    data: [],
};
/**
 * Remote Infra List
 */
const getRemoteInfra = async () => {
    if (remoteInfraData.status) {
        logger.info('From Local Data');
        return remoteInfraData.data;
    }
    logger.info('From DB');
    remoteInfraData.data = (
        await course_schedule_settings
            .findOne(
                { isDeleted: false },
                {
                    'programs._program_id': 1,
                    'programs.remoteScheduling._id': 1,
                    'programs.remoteScheduling.meetingTitle': 1,
                    'programs.remoteScheduling.gender': 1,
                    'programs.remoteScheduling.meetingUrl': 1,
                    'programs.remoteScheduling.meetingId': 1,
                    'programs.remoteScheduling.meetingUsername': 1,
                    'programs.remoteScheduling.passCode': 1,
                    'programs.remoteScheduling.associatedEmail': 1,
                    'programs.remoteScheduling.password': 1,
                    'programs.remoteScheduling.term': 1,
                    'programs.remoteScheduling.yearName': 1,
                    'programs.remoteScheduling.levelName': 1,
                },
            )
            .lean()
    ).programs
        .flat()
        .map((a) => a.remoteScheduling)
        .flat();
    remoteInfraData.status = true;
    return remoteInfraData.data;
};

module.exports = {
    getInstitutionCalendar,
    loadInstitutionCalendar,
    getRemoteInfra,
};
