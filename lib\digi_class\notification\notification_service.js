const {
    axiosCall,
    convertToMongoObjectId,
    clone,
    convertToUtcFormat,
} = require('../../utility/common');
const {
    GENDER: { MALE },
    DC_STAFF,
    COMPLETED,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    ABSENT,
    LEAVE,
    PRESENT,
} = require('../../utility/constants');
const Notification = require('../../models/notification');
const InstitutionCalendar = require('../../models/institution_calendar');
const CourseSchedule = require('../../models/course_schedule');
const WarningMail = require('../../models/warning_mail');
const StudentCriteriaManipulations = require('../../models/studentCriteriaManipulation');
const Course = require('../../models/digi_course');
const User = require('../../models/user');
const studentGroupCollection = require('../../models/student_group');
const programCalendarCollection = require('../../models/program_calendar');
const lmsCollection = require('../../models/lms');
const { get_list, get } = require('../../base/base_controller');
const {
    allCourseScheduleTillYesterday,
    lmsData,
    allStudentGroupYesterday,
} = require('../../../service/cache.service');
const { scheduleDateFormateChange } = require('../../utility/common_functions');

exports.getNotifications = async (userIds) => {
    try {
        userIds = userIds.map((userId) => convertToMongoObjectId(userId));
        const notifications = await Notification.find(
            {
                'users._id': { $in: userIds },
                isDeleted: false,
            },
            {
                _id: 1,
                'users._id': 1,
                'users.isViewed': 1,
            },
        ).lean();
        const userNotifications = [];
        userIds.forEach((userId) => {
            const counts = notifications.filter((notification) => {
                if (
                    notification.users.find(
                        (user) => user._id.toString() === userId.toString() && !user.isViewed,
                    )
                ) {
                    return true;
                }
                return false;
            }).length;
            userNotifications.push({ userId, counts });
        });
        return userNotifications;
    } catch (error) {
        throw new Error(error);
    }
};

exports.sendDashboardNotification = async (users, activity) => {
    try {
        if (!users.length) return;
        if (users.length) {
            const sendSocketData = [];
            const { studentCompletedQuiz } = activity;
            for (const user of users) {
                const eventId = user._id;
                activity.studentCompletedQuiz = false;
                if (studentCompletedQuiz.length) {
                    const studentIds = studentCompletedQuiz.map((studentEntry) =>
                        studentEntry.toString(),
                    );
                    if (studentIds.includes(eventId.toString()))
                        activity.studentCompletedQuiz = true;
                }
                sendSocketData.push({
                    eventId,
                    data: JSON.stringify({ notificationCount: 1, activity }),
                });
            }
            if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
        }
    } catch (error) {
        throw new Error(error);
    }
};

// student mail content
const makeMailContent = (userDetail, contentDetail, studentWarningAbsence, denialPercentage) => {
    const { name, _id } = userDetail;
    const {
        warning,
        warning_message,
        courseName,
        courseCode,
        absence_percentage,
        term,
        rotation,
        levelNo,
    } = contentDetail;
    let nameFormat = name.first ? name.first : ' ';
    nameFormat += name.last ? ' ' + name.last : ' ';
    nameFormat += name.middle ? ' ' + name.middle : ' ';
    let mailContent = '<p>Dear ' + nameFormat + ',<br>';
    const courseDetails =
        /* rotation && rotation !== 0
            ? courseCode +
              '-' +
              courseName +
              '-R' +
              rotation +
              '(' +
              term.charAt(0).toUpperCase() +
              term.slice(1) +
              ')'
            : */ courseCode +
        '-' +
        courseName +
        '(' +
        levelNo +
        ',' +
        term.charAt(0).toUpperCase() +
        term.slice(1) +
        ')';
    // let absencePercentageSetting = studentWarningAbsence.map(
    //     (studentWarning) => studentWarning.absence_percentage,
    // );
    // absencePercentageSetting = absencePercentageSetting.sort(function (a, b) {
    //     return a - b;
    // });
    // mailContent +=
    //     'This is to Notify you that you have reached the ' +
    //     studentWarningAbsence.warning +
    //     ' Stage of "' +
    //     studentWarningAbsence.absence_percentage +
    //     '%" Absence in the Sessions of ' +
    //     courseCode +
    //     '-' +
    //     courseName;
    // mailContent +=
    //     ' and you will be denied sitting for Final Examination. Please contact the Course Coordinator for further action.';
    if (studentWarningAbsence.warning === 'Denial') {
        mailContent +=
            'This is to notify you that you have reached the Denial Stage of "' +
            studentWarningAbsence.absence_percentage +
            '%" Absence in the Sessions of "' +
            courseDetails +
            '"';
        mailContent +=
            ' and you will be denied sitting for Final Examination. Please contact the Course Coordinator for further action.';
    } else if (studentWarningAbsence.warning === 'Final Warning') {
        mailContent +=
            'This is the "Final Warning" Level for Absence of "' +
            studentWarningAbsence.absence_percentage +
            '%" regarding your Poor Attendance in the Sessions of "' +
            courseDetails +
            '".';
        mailContent +=
            'We want to remind you that if your Absence Level reaches Denial Stage of ' +
            denialPercentage +
            '% Absence, you will be denied sitting for Final Examination.';
    } else {
        mailContent +=
            'You have reached the "' +
            studentWarningAbsence.warning +
            '" Level for Absence of "' +
            studentWarningAbsence.absence_percentage +
            '%" from the Session of "' +
            courseDetails +
            '".';
        mailContent += 'You are advised to attend the Session promptly going forward.';
    }
    mailContent += '<br>Best Regards</p>';
    return mailContent;
};

// staff mail content
const makeStaffMailContent = async (
    name,
    staffSendMails,
    studentWarningAbsence,
    studentDetails,
    students,
) => {
    // const staffs = [];
    const { institutionId, warningId, programName, courseName, courseCode, levelNo, term } =
        staffSendMails;
    // let studentWarningAbsence = await lmsCollection.findOne({
    //     _institution_id: convertToMongoObjectId(institutionId),
    // });
    // studentWarningAbsence = studentWarningAbsence.student_warning_absence_calculation.filter(
    //     (studentWarning) => !studentWarning.isDeleted,
    // );
    const warning = studentWarningAbsence.find(
        (studentWarning) => studentWarning._id.toString() === warningId.toString(),
    );
    let absencePercentageSetting = studentWarningAbsence.map(
        (studentWarning) => studentWarning.absence_percentage,
    );
    absencePercentageSetting = absencePercentageSetting.sort(function (a, b) {
        return a - b;
    });
    let nameFormat = name.first ? name.first : ' ';
    nameFormat += name.middle ? ' ' + name.middle : ' ';
    nameFormat += name.last ? ' ' + name.last : ' ';
    nameFormat = nameFormat.trim();
    let mailContent = '<p>Dear ' + nameFormat + ',<br>';
    if (warning && warning.warning === 'Denial') {
        mailContent +=
            'The below list of Students have reached the "Denial Stage" Level for Absence of "' +
            warning.absence_percentage +
            '%" from the Sessions of "' +
            programName +
            '-' +
            levelNo +
            '-' +
            term.charAt(0).toUpperCase() +
            term.slice(1) +
            '-' +
            courseCode +
            '-' +
            courseName +
            '" and these students will be denied sitting for Final Examination';
        // mailContent +=
        //     ' and you will be denied sitting for Final Examination. Please contact the Course Coordinator for further action.';
    } else if (
        warning &&
        absencePercentageSetting.indexOf(warning.absence_percentage) ===
            studentWarningAbsence.length - 2
    ) {
        mailContent +=
            'The below list of Students have reached the "Final Warning" Level for Absence of "' +
            warning.absence_percentage +
            '%" from the Sessions of "' +
            programName +
            '-' +
            levelNo +
            '-' +
            term.charAt(0).toUpperCase() +
            term.slice(1) +
            '-' +
            courseCode +
            '-' +
            courseName +
            '" and these students will be denied sitting for Final Examination if they reach the Denial Stage of "' +
            absencePercentageSetting[studentWarningAbsence.length - 1] +
            '%"';
    } else {
        mailContent +=
            'The below list of Students have reached the "' +
            warning.warning_message +
            '" Level for Absence of "' +
            warning.absence_percentage +
            '%" from the Sessions of "' +
            programName +
            '-' +
            levelNo +
            '-' +
            term.charAt(0).toUpperCase() +
            term.slice(1) +
            '-' +
            courseCode +
            '-' +
            courseName +
            '"';
    }
    mailContent += '<br><br>';
    // let students = staffSendMails.map((staffSendMail) => staffSendMail.students);
    // // eslint-disable-next-line prefer-spread
    // students = [].concat.apply([], students);
    mailContent += `<table style='border-collapse: collapse;width: 100%;'><tbody>`;
    mailContent += `<thead><tr style='border: 1px solid #171616d4;text-align: left;'>`;
    mailContent += `<th style='border: 1px solid #999;padding:0.5rem;'>Student Name</th>`;
    mailContent += `<th style='border: 1px solid #999;padding:0.5rem;'>Academic ID</th></tr></thead>`;
    for (const student of students) {
        const studentName = studentDetails.find(
            (studentDetail) => studentDetail._id.toString() === student.toString(),
        );
        let name = studentName.name.first ? studentName.name.first : ' ';
        name += studentName.name.middle ? ' ' + studentName.name.middle : ' ';
        name += studentName.name.last ? ' ' + studentName.name.last : ' ';
        name = name.trim();
        // mailContent += name + ' - ' + studentName.user_id + '<br>';
        mailContent += `<tr><td style='border: 1px solid #999;padding:0.5rem;'>${name}</td>`;
        mailContent += `<td style='border: 1px solid #999;padding:0.5rem;'>${studentName.user_id}</td></tr>`;
    }
    mailContent += `</tbody></table>`;
    mailContent += '<br>Best Regards</p>';
    return mailContent;
};

const studentGroupList = async (institutionCalendarId) => {
    try {
        // let studentGroupData = (
        //     await allStudentGroupYesterday(
        //         institutionCalendarId,
        //         scheduleDateFormateChange(new Date()),
        //     )
        // ).filter(
        //     (sgElement) =>
        //         sgElement.isDeleted === false &&
        //         sgElement._institution_calendar_id.toString() === institutionCalendarId.toString(),
        // );
        // if (!studentGroupData) throw new Error('Student group not found');
        // studentGroupData = studentGroupData.filter(
        //     (sgElement) => sgElement.master._program_id.toString() === '60ed537fc824f4ec522c6208',
        // );
        console.time('studentGroupData');
        const studentGroupData = await get_list(
            studentGroupCollection,
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                // 'master._program_id': {
                //     $in: [convertToMongoObjectId('61fd106e81f497546fd03b64')],
                // },
                // isDeleted: false,
            },
            {},
        );
        console.timeEnd('studentGroupData');
        console.time('programCalendarData');
        const programCalendarData = await get_list(
            programCalendarCollection,
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                // _program_id: {
                //     $in: [convertToMongoObjectId('61fd106e81f497546fd03b64')],
                // },
                // isDeleted: false,
            },
            {
                _program_id: 1,
                'level.term': 1,
                'level.year': 1,
                'level.level_no': 1,
                'level.rotation': 1,
                'level.course._course_id': 1,
                'level.course.start_date': 1,
                'level.course.end_date': 1,
                'level.rotation_course.rotation_count': 1,
                'level.rotation_course.course._course_id': 1,
                'level.rotation_course.course.start_date': 1,
                'level.rotation_course.course.end_date': 1,
            },
        );
        console.timeEnd('programCalendarData');
        if (!programCalendarData.status) throw new Error('Student group not found');
        // console.log(new Date());
        const programCalendarCourses = [];
        for (calendarElement of programCalendarData.data) {
            for (levelElement of calendarElement.level) {
                if (levelElement.rotation === 'yes') {
                    // Disable this for Stop Rotation course mail push based on client request
                    for (rotationElement of levelElement.rotation_course) {
                        for (courseElement of rotationElement.course) {
                            // console.log(
                            //     new Date(courseElement.start_date) <= new Date() &&
                            //         new Date(courseElement.end_date) >= new Date(),
                            //     new Date(courseElement.start_date),
                            //     new Date(courseElement.end_date),
                            // );
                            if (
                                new Date(courseElement.start_date) <= new Date() &&
                                new Date(courseElement.end_date) >= new Date()
                            )
                                programCalendarCourses.push({
                                    _program_id: calendarElement._program_id.toString(),
                                    term: levelElement.term,
                                    year: levelElement.year,
                                    level_no: levelElement.level_no,
                                    _course_id: courseElement._course_id,
                                    start_date: courseElement.start_date,
                                    end_date: courseElement.end_date,
                                    rotation: 'yes',
                                    rotation_count: rotationElement.rotation_count,
                                });
                        }
                    }
                } else {
                    for (courseElement of levelElement.course) {
                        // console.log(
                        //     new Date(courseElement.start_date) <= new Date() &&
                        //         new Date(courseElement.end_date) >= new Date(),
                        //     new Date(courseElement.start_date),
                        //     new Date(courseElement.end_date),
                        // );
                        if (
                            new Date(courseElement.start_date) <= new Date() &&
                            new Date(courseElement.end_date) >= new Date()
                        )
                            programCalendarCourses.push({
                                _program_id: calendarElement._program_id.toString(),
                                term: levelElement.term,
                                year: levelElement.year,
                                level_no: levelElement.level_no,
                                _course_id: courseElement._course_id,
                                start_date: courseElement.start_date,
                                end_date: courseElement.end_date,
                                rotation: 'no',
                            });
                    }
                }
            }
        }
        const studentWithGroupList = [];
        for (const sgCourseSettingData of studentGroupData.data) {
            const {
                master: { _program_id: programId, year },
                _institution_calendar_id,
                _institution_id,
            } = sgCourseSettingData;
            for (const group of sgCourseSettingData.groups) {
                const { level, term, rotation, rotation_count: rotationCount } = group;
                for (const course of group.courses) {
                    const { _course_id: courseId, student_absence_percentage } = course;
                    const programCalendarCourseFind = programCalendarCourses.filter(
                        (courseElement) =>
                            courseElement._course_id.toString() === courseId.toString() &&
                            courseElement.term.toString() === term.toString() &&
                            courseElement.level_no.toString() === level.toString() &&
                            courseElement.rotation.toString() === rotation.toString(),
                    );
                    if (programCalendarCourseFind.length)
                        for (courseSetting of course.setting) {
                            if (
                                rotation.toString() === 'yes'
                                    ? programCalendarCourseFind.find(
                                          (rotationElement) =>
                                              rotationElement.rotation_count.toString() ===
                                              courseSetting._group_no.toString(),
                                      )
                                    : true
                            ) {
                                const group_name =
                                    courseSetting._group_no !== undefined
                                        ? courseSetting.gender === MALE
                                            ? 'MG-' + courseSetting._group_no
                                            : 'FG-' + courseSetting._group_no
                                        : courseSetting.gender === MALE
                                        ? 'MG-1'
                                        : 'FG-1';
                                const deliveryGroup =
                                    courseSetting &&
                                    courseSetting.session_setting &&
                                    courseSetting.session_setting.length &&
                                    courseSetting.session_setting[0].groups
                                        ? courseSetting.session_setting[0].groups
                                        : [];
                                const studentIds = deliveryGroup
                                    .map((ele) => ele._student_ids)
                                    .flat();
                                const groupStudentData = group.students
                                    .filter((ele) =>
                                        studentIds.find(
                                            (ele2) =>
                                                ele2.toString() === ele._student_id.toString(),
                                        ),
                                    )
                                    .map((ele3) => {
                                        return {
                                            _student_id: ele3._student_id,
                                            name: ele3.name,
                                            academic_no: ele3.academic_no,
                                            gender: ele3.gender,
                                        };
                                    });
                                if (groupStudentData.length)
                                    studentWithGroupList.push({
                                        _id: courseSetting._id,
                                        gender: courseSetting.gender,
                                        group_name,
                                        courseId,
                                        programId,
                                        institutionCalendarId: _institution_calendar_id,
                                        institutionId: _institution_id,
                                        level,
                                        year,
                                        term,
                                        rotation,
                                        rotationCount: courseSetting._group_no,
                                        students: groupStudentData,
                                        student_absence_percentage,
                                    });
                            }
                        }
                }
            }
        }
        return studentWithGroupList;
    } catch (error) {
        console.log(error);
        throw new Error(error);
    }
};

const getCourseAdmins = async (institutionCalendarId) => {
    try {
        const courseFindQuery = {
            'coordinators._institution_calendar_id': convertToMongoObjectId(institutionCalendarId),
        };
        const courses = await Course.find(courseFindQuery, {
            coordinators: 1,
            _program_id: 1,
            course_name: 1,
            course_code: 1,
        })
            .populate({
                path: '_program_id',
                select: { name: 1 },
            })
            .lean();
        const courseParams = [];
        if (courses.length) {
            for (course of courses) {
                for (courseParam of course.coordinators) {
                    courseParams.push({
                        staffId: courseParam._user_id,
                        term: courseParam.term,
                        level_no: courseParam.level_no,
                        year_no: courseParam.year,
                        _institution_calendar_id: courseParam._institution_calendar_id,
                        _program_id: course._program_id._id,
                        program_name: course._program_id.name,
                        _course_id: course._id,
                        course_name: course.course_name,
                        course_code: course.course_code,
                    });
                }
            }
        }
        return courseParams;
    } catch (error) {
        throw new Error(error);
    }
};

// course details to check student attendance warning mail
exports.warningMailForStudent = async () => {
    //get current institutionCalendarId
    const institutionCalendarId = (
        await InstitutionCalendar.findOne({
            isDeleted: false,
            status: 'published',
        })
            .sort({ _id: -1 }) // TODO work with sort desc
            .lean()
    )._id;
    // get student group
    const studentGroups = await studentGroupList(institutionCalendarId);
    const students = [
        ...new Set(
            // eslint-disable-next-line prefer-spread
            [].concat
                .apply(
                    [],
                    studentGroups.map((studentGroup) => studentGroup.students),
                )
                .map((student) => student._student_id.toString()),
        ),
    ];
    const studentWithCourses = [];
    for (const student of students) {
        const studentCourses = studentGroups
            .filter((studentGroup) =>
                studentGroup.students.find(
                    (groupStudent) => groupStudent._student_id.toString() === student.toString(),
                ),
            )
            .map((studentGroupCourse) => {
                return {
                    gender: studentGroupCourse.gender,
                    group_name: studentGroupCourse.group_name,
                    courseId: studentGroupCourse.courseId,
                    programId: studentGroupCourse.programId,
                    institutionCalendarId: studentGroupCourse.institutionCalendarId,
                    institutionId: studentGroupCourse.institutionId,
                    level: studentGroupCourse.level,
                    year: studentGroupCourse.year,
                    term: studentGroupCourse.term,
                    rotation: studentGroupCourse.rotation,
                    rotationCount:
                        studentGroupCourse.rotation && studentGroupCourse.rotation !== 'no'
                            ? studentGroupCourse.rotationCount
                            : undefined,
                    student_absence_percentage: studentGroupCourse.student_absence_percentage,
                };
            });
        studentWithCourses.push({
            _id: student,
            courses: studentCourses,
            programIds: [
                ...new Set(
                    studentCourses.map((courseElement) => courseElement.programId.toString()),
                ),
            ],
        });
    }
    // return { sendMail: studentWithCourses };
    const sendMail = [];
    let warningMails;
    if (studentWithCourses.length) {
        const studentIds = studentWithCourses.map((student) => convertToMongoObjectId(student._id));
        const studentDetails = await User.find(
            { _id: { $in: studentIds } },
            { email: 1, name: 1 },
        ).lean();
        // warningMail
        // eslint-disable-next-line prefer-spread
        let studentCourses = [].concat.apply(
            [],
            studentWithCourses.map((studentWithCourse) => studentWithCourse.courses),
        );
        studentCourses = studentCourses.reduce((acc, current) => {
            const x = acc.find((item) =>
                item.rotation === 'no'
                    ? item.courseId.toString() === current.courseId.toString() &&
                      item.programId.toString() === current.programId.toString() &&
                      item.institutionCalendarId.toString() ===
                          current.institutionCalendarId.toString() &&
                      item.term.toString() === current.term.toString() &&
                      item.level.toString() === current.level.toString() &&
                      item.year.toString() === current.year.toString()
                    : item.courseId.toString() === current.courseId.toString() &&
                      item.programId.toString() === current.programId.toString() &&
                      item.institutionCalendarId.toString() ===
                          current.institutionCalendarId.toString() &&
                      item.term.toString() === current.term.toString() &&
                      item.year.toString() === current.year.toString() &&
                      item.rotationCount.toString() === current.rotationCount.toString() &&
                      item.level.toString() === current.level.toString(),
            );
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        const scheduleProgramIds = [
            ...new Set(
                studentCourses.map((scheduleElement) => scheduleElement.programId.toString()),
            ),
        ];
        // const lms = (await lmsData()).filter((lmsEntry) => !lmsEntry.isDeleted);
        console.time('lms');
        const lms = await get(
            lmsCollection,
            { isDeleted: false },
            { _institution_id: 1, student_warning_absence_calculation: 1 },
        );
        console.timeEnd('lms');
        if (!lms.status) lms.data = [{ student_warning_absence_calculation: [] }];

        let studentWarningAbsenceSetting = lms.data.student_warning_absence_calculation.filter(
            (studentWarning) => !studentWarning.isDeleted,
        );
        studentWarningAbsenceSetting = studentWarningAbsenceSetting.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        if (studentWarningAbsenceSetting[1] && studentWarningAbsenceSetting[1].warning)
            studentWarningAbsenceSetting[1].warning = 'Final Warning';

        studentCourses = studentCourses.map((studentCourse) => {
            const rotationCount = studentCourse.rotationCount
                ? studentCourse.rotationCount
                : undefined;
            const studentWarningQuery = {
                courseId: convertToMongoObjectId(studentCourse.courseId),
                levelNo: studentCourse.level,
                term: studentCourse.term,
                yearNo: studentCourse.year,
                programId: convertToMongoObjectId(studentCourse.programId),
                institutionCalendarId: convertToMongoObjectId(studentCourse.institutionCalendarId),
            };
            if (rotationCount) studentWarningQuery.rotationCount = rotationCount;
            return studentWarningQuery;
        });
        const warningQuery = {
            institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
            isDeleted: false,
        };
        warningQuery.$or = studentCourses;
        console.time('WarningMail');
        warningMails = await WarningMail.find(warningQuery).sort({ sendDate: -1 }).lean();
        console.timeEnd('WarningMail');
        //student_criteria_manipulations
        console.time('studentCriteriaManipulations');
        const studentCriteriaManipulations = await StudentCriteriaManipulations.find(warningQuery, {
            courseId: 1,
            levelNo: 1,
            term: 1,
            programId: 1,
            yearNo: 1,
            institutionCalendarId: 1,
            rotationCount: 1,
            studentId: 1,
            absencePercentage: 1,
        }).lean();
        console.timeEnd('studentCriteriaManipulations');
        // const allCourseSchedules = await allCourseScheduleTillYesterday(
        //     institutionCalendarId,
        //     scheduleDateFormateChange(new Date()),
        // );

        for (programIdElement of scheduleProgramIds) {
            console.time(`Program Wise ${programIdElement}`);
            const scheduleCourseIds = [
                ...new Set(
                    studentCourses
                        .filter(
                            (scheduleElement) =>
                                scheduleElement.programId.toString() ===
                                programIdElement.toString(),
                        )
                        .map((scheduleElement) => scheduleElement.courseId.toString()),
                ),
            ];
            console.time('allCourseSchedules');
            let allCourseSchedules = (
                await get_list(
                    CourseSchedule,
                    {
                        isDeleted: false,
                        type: 'regular',
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _course_id: { $in: scheduleCourseIds },
                        _program_id: convertToMongoObjectId(programIdElement),
                    },
                    {
                        type: 1,
                        _course_id: 1,
                        course_name: 1,
                        course_code: 1,
                        year_no: 1,
                        level_no: 1,
                        term: 1,
                        _program_id: 1,
                        _institution_calendar_id: 1,
                        rotation_count: 1,
                        status: 1,
                        'students._id': 1,
                        'students.status': 1,
                    },
                )
            ).data;
            console.timeEnd('allCourseSchedules');
            if (allCourseSchedules.toString() === 'Not found') allCourseSchedules = [];
            const programStudents = studentWithCourses.filter((studentCourseElement) =>
                studentCourseElement.programIds.find(
                    (programIdsElement) =>
                        programIdsElement.toString() === programIdElement.toString(),
                ),
            );
            for (const studentWithCourse of programStudents) {
                const { _id: studentId, courses, programIds } = studentWithCourse;
                const studentProgramCourses = courses.filter(
                    (courseElement) =>
                        courseElement.programId.toString() === programIdElement.toString(),
                );
                for (const course of studentProgramCourses) {
                    const {
                        courseId,
                        level: levelNo,
                        term,
                        programId,
                        year: yearNo,
                        institutionCalendarId: institutionCalendarIdEntry,
                        institutionId: institutionIdEntry,
                        rotationCount,
                        student_absence_percentage,
                    } = course;
                    const studentPrivateWarningAbsence = studentCriteriaManipulations.length
                        ? studentCriteriaManipulations.find((studentCriteriaManipulation) =>
                              studentCriteriaManipulation.rotationCount && rotationCount
                                  ? studentCriteriaManipulation.courseId.toString() ===
                                        courseId.toString() &&
                                    studentCriteriaManipulation.levelNo.toString() ===
                                        levelNo.toString() &&
                                    studentCriteriaManipulation.term.toString() ===
                                        term.toString() &&
                                    studentCriteriaManipulation.programId.toString() ===
                                        programId.toString() &&
                                    studentCriteriaManipulation.yearNo.toString() ===
                                        yearNo.toString() &&
                                    studentCriteriaManipulation.institutionCalendarId.toString() ===
                                        institutionCalendarIdEntry.toString() &&
                                    studentCriteriaManipulation.rotationCount.toString() ===
                                        rotationCount.toString() &&
                                    studentCriteriaManipulation.studentId.toString() ===
                                        studentId.toString()
                                  : studentCriteriaManipulation.courseId.toString() ===
                                        courseId.toString() &&
                                    studentCriteriaManipulation.levelNo.toString() ===
                                        levelNo.toString() &&
                                    studentCriteriaManipulation.term.toString() ===
                                        term.toString() &&
                                    studentCriteriaManipulation.programId.toString() ===
                                        programId.toString() &&
                                    studentCriteriaManipulation.yearNo.toString() ===
                                        yearNo.toString() &&
                                    studentCriteriaManipulation.institutionCalendarId.toString() ===
                                        institutionCalendarIdEntry.toString() &&
                                    studentCriteriaManipulation.studentId.toString() ===
                                        studentId.toString(),
                          )
                        : undefined;
                    const courseSchedules = allCourseSchedules.filter((courseSchedule) =>
                        rotationCount && courseSchedule.rotation_count
                            ? courseSchedule.type === 'regular' &&
                              courseSchedule._course_id.toString() === courseId.toString() &&
                              courseSchedule.year_no === yearNo &&
                              courseSchedule.level_no === levelNo &&
                              courseSchedule.term === term &&
                              courseSchedule._program_id.toString() === programId.toString() &&
                              courseSchedule._institution_calendar_id.toString() ===
                                  institutionCalendarIdEntry.toString() &&
                              courseSchedule.rotation_count.toString() === rotationCount.toString()
                            : courseSchedule.type === 'regular' &&
                              courseSchedule._course_id.toString() === courseId.toString() &&
                              courseSchedule.year_no === yearNo &&
                              courseSchedule.level_no === levelNo &&
                              courseSchedule.term === term &&
                              courseSchedule._program_id.toString() === programId.toString() &&
                              courseSchedule._institution_calendar_id.toString() ===
                                  institutionCalendarIdEntry.toString(),
                    );
                    const totalNoSchedules = courseSchedules.length;
                    const studentCourseSchedules = courseSchedules.filter((courseSchedule) =>
                        courseSchedule.students.find(
                            (studentEntry) => studentEntry._id.toString() === studentId.toString(),
                        ),
                    );
                    /// lms review student ONDUTY
                    const studentCourseScheduleNotAttended = studentCourseSchedules.filter(
                        (studentCourse) =>
                            studentCourse.status === COMPLETED &&
                            studentCourse.students.find(
                                (studentEntry) =>
                                    // studentEntry.status !== 'present' || studentEntry.status !== ONDUTY,
                                    studentEntry._id.toString() === studentId.toString() &&
                                    (studentEntry.status === ABSENT ||
                                        studentEntry.status === LEAVE ||
                                        studentEntry.status === PERMISSION),
                            ),
                    ).length;
                    const warningCalculation =
                        studentCourseScheduleNotAttended !== 0 &&
                        studentCourseSchedules.length !== 0
                            ? 100 *
                              (studentCourseScheduleNotAttended / studentCourseSchedules.length)
                            : 0;
                    const warningMailFilters = warningMails.filter((warningMail) =>
                        rotationCount && warningMail.rotationCount
                            ? warningMail.courseId.toString() === courseId.toString() &&
                              warningMail.levelNo === levelNo &&
                              warningMail.term === term &&
                              warningMail.programId.toString() === programId.toString() &&
                              warningMail.yearNo === yearNo &&
                              warningMail.institutionCalendarId.toString() ===
                                  institutionCalendarIdEntry.toString() &&
                              warningMail.rotationCount.toString() === rotationCount.toString() &&
                              warningMail.userIds.find(
                                  (userElement) => userElement.toString() === studentId.toString(),
                              )
                            : warningMail.courseId.toString() === courseId.toString() &&
                              warningMail.levelNo === levelNo &&
                              warningMail.term === term &&
                              warningMail.programId.toString() === programId.toString() &&
                              warningMail.yearNo === yearNo &&
                              warningMail.institutionCalendarId.toString() ===
                                  institutionCalendarIdEntry.toString() &&
                              warningMail.userIds.find(
                                  (userElement) => userElement.toString() === studentId.toString(),
                              ),
                    );
                    const studentWarningAbsence = clone(studentWarningAbsenceSetting);
                    // Check Course Level Criteria
                    if (
                        student_absence_percentage &&
                        studentWarningAbsence[0].absence_percentage < student_absence_percentage
                    )
                        studentWarningAbsence[0].absence_percentage = student_absence_percentage;
                    if (
                        studentPrivateWarningAbsence &&
                        studentPrivateWarningAbsence.absencePercentage &&
                        studentWarningAbsence[0].absence_percentage <
                            studentPrivateWarningAbsence.absencePercentage
                    )
                        studentWarningAbsence[0].absence_percentage =
                            studentPrivateWarningAbsence.absencePercentage;

                    //check warning mail status
                    let studentPush = studentWarningAbsence.find((studentWarning) => {
                        if (studentWarning.absence_percentage <= warningCalculation) {
                            return studentWarning;
                        }
                    });
                    if (studentPush) {
                        if (
                            warningMailFilters[0] &&
                            warningMailFilters[0].warningId &&
                            warningMailFilters[0].warningId.toString() ===
                                studentPush._id.toString()
                        )
                            studentPush = undefined;
                    }
                    if (studentPush) {
                        const userDetail = studentDetails.find(
                            (studentEntry) => studentEntry._id.toString() === studentId.toString(),
                        );
                        studentPush.courseName = courseSchedules[0].course_name;
                        studentPush.courseCode = courseSchedules[0].course_code;
                        studentPush.term = courseSchedules[0].term;
                        studentPush.levelNo = courseSchedules[0].level_no;
                        studentPush.rotation =
                            courseSchedules[0] && courseSchedules[0].rotation_count
                                ? courseSchedules[0].rotation_count
                                : undefined;
                        // mail content
                        const content = makeMailContent(
                            userDetail,
                            studentPush,
                            studentPush,
                            studentWarningAbsence[0].absence_percentage || '',
                        );
                        sendMail.push({
                            userId: userDetail._id,
                            email: userDetail.email,
                            warningId: studentPush._id,
                            warning: studentPush.warning,
                            warning_message: studentPush.warning_message,
                            content,
                            courseDetail: {
                                courseId,
                                yearNo,
                                levelNo,
                                term,
                                programId,
                                institutionCalendarId: institutionCalendarIdEntry,
                                rotationCount,
                            },
                        });
                    }
                }
            }
            console.timeEnd(`Program Wise ${programIdElement}`);
        }
    }
    return { sendMail, warningMails };
};

const getCourseWithSchedules = async (staffs, institutionCalendarId) => {
    // courseSchedule from cache
    const allCourseSchedules = await allCourseScheduleTillYesterday(
        institutionCalendarId,
        scheduleDateFormateChange(new Date()),
    );
    const courseFilterDetails = [];
    const staffCourses = [];
    for (const staff of staffs) {
        const {
            staffId,
            email,
            name,
            term,
            _institution_calendar_id,
            _program_id,
            _course_id,
            level_no,
            year_no,
        } = staff;
        //check course schedules and course list
        const staffCourseDetails = allCourseSchedules.filter(
            (allCourseSchedule) =>
                _course_id.toString() === allCourseSchedule._course_id.toString() &&
                _institution_calendar_id.toString() ===
                    allCourseSchedule._institution_calendar_id.toString() &&
                _program_id.toString() === allCourseSchedule._program_id.toString() &&
                term === allCourseSchedule.term &&
                level_no === allCourseSchedule.level_no &&
                year_no === allCourseSchedule.year_no &&
                allCourseSchedule.staffs.find(
                    (staff) => staff._staff_id.toString() === staffId.toString(),
                ),
        );
        const courseDetails = staffCourseDetails.map((staffCourseDetail) => {
            return {
                _institution_calendar_id: staffCourseDetail._institution_calendar_id,
                _program_id: staffCourseDetail._program_id,
                programName: staffCourseDetail.program_name,
                courseName: staffCourseDetail.course_name,
                courseCode: staffCourseDetail.course_code,
                term: staffCourseDetail.term,
                year_no: staffCourseDetail.year_no,
                level_no: staffCourseDetail.level_no,
                _course_id: staffCourseDetail._course_id,
                rotation_count: staffCourseDetail.rotation_count,
                _institution_id: staffCourseDetail._institution_id,
            };
        });
        const courseReduceDetails = courseDetails.reduce((acc, current) => {
            const x = acc.find((item) =>
                item.rotation_count && current.rotation_count
                    ? item._course_id.toString() === current._course_id.toString() &&
                      item._program_id.toString() === current._program_id.toString() &&
                      item._institution_calendar_id.toString() ===
                          current._institution_calendar_id.toString() &&
                      item.term.toString() === current.term.toString() &&
                      item.level_no.toString() === current.level_no.toString() &&
                      item.rotation_count.toString() === current.rotation_count.toString() &&
                      item.year_no.toString() === current.year_no.toString()
                    : item._course_id.toString() === current._course_id.toString() &&
                      item._program_id.toString() === current._program_id.toString() &&
                      item._institution_calendar_id.toString() ===
                          current._institution_calendar_id.toString() &&
                      item.term.toString() === current.term.toString() &&
                      item.level_no.toString() === current.level_no.toString() &&
                      item.year_no.toString() === current.year_no.toString(),
            );
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        if (courseReduceDetails.length) {
            courseFilterDetails.push(courseReduceDetails);
            staffCourses.push({
                staffId,
                email,
                name,
                courses: courseReduceDetails,
            });
        }
    }
    // course filter
    const uniqueStaffs = [
        ...new Set(staffCourses.map((staffCourse) => staffCourse.staffId.toString())),
    ];
    const staffFilteredCourses = [];
    for (const uniqueStaff of uniqueStaffs) {
        let staffFilterCourses = staffCourses
            .filter((staffCourse) => staffCourse.staffId.toString() === uniqueStaff.toString())
            .map((staffCourseEntry) => staffCourseEntry.courses);
        // eslint-disable-next-line prefer-spread
        staffFilterCourses = [].concat.apply([], staffFilterCourses);
        const staff = staffCourses.find(
            (staffCourse) => staffCourse.staffId.toString() === uniqueStaff,
        );
        staffFilterCourses = staffFilterCourses.reduce((acc, current) => {
            const x = acc.find(
                (item) =>
                    item._course_id.toString() === current._course_id.toString() &&
                    item._program_id.toString() === current._program_id.toString() &&
                    item._institution_calendar_id.toString() ===
                        current._institution_calendar_id.toString() &&
                    item.term.toString() === current.term.toString() &&
                    item.level_no.toString() === current.level_no.toString() &&
                    item.year_no.toString() === current.year_no.toString(),
            );
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        staffFilteredCourses.push({
            staffId: staff.staffId,
            email: staff.email,
            name: staff.name,
            courses: staffFilterCourses,
        });
    }
    // eslint-disable-next-line prefer-spread
    const courseDetails = [].concat.apply([], courseFilterDetails);

    const warningQuery = {
        isDeleted: false,
    };

    warningQuery.$or = courseDetails.map((courseDetail) => {
        const query = {
            institutionCalendarId: courseDetail._institution_calendar_id,
            programId: courseDetail._program_id,
            term: courseDetail.term,
            yearNo: courseDetail.year_no,
            levelNo: courseDetail.level_no,
            courseId: courseDetail._course_id,
        };
        if (courseDetail.rotation_count) query.rotationCount = courseDetail.rotation_count;
        return query;
    });

    const warningMails = await WarningMail.find(warningQuery, {}).sort({ sendDate: -1 }).lean();
    const staffSendMails = [];
    for (const staffCourse of staffFilteredCourses) {
        const { courses, staffId, email, name } = staffCourse;
        //check if warning already send
        for (const course of courses) {
            const staffWarningMails = warningMails.filter((warningMail) =>
                warningMail.rotationCount && course.rotation_count
                    ? warningMail.courseId.toString() === course._course_id.toString() &&
                      warningMail.institutionCalendarId.toString() ===
                          course._institution_calendar_id.toString() &&
                      warningMail.programId.toString() === course._program_id.toString() &&
                      warningMail.term === course.term &&
                      warningMail.yearNo === course.year_no &&
                      warningMail.levelNo === course.levelNo &&
                      warningMail.rotationCount.toString() &&
                      course.rotation_count.toString()
                    : warningMail.courseId.toString() === course._course_id.toString() &&
                      warningMail.institutionCalendarId.toString() ===
                          course._institution_calendar_id.toString() &&
                      warningMail.programId.toString() === course._program_id &&
                      warningMail.term === course.term &&
                      warningMail.yearNo === course.year_no &&
                      warningMail.levelNo === course.level_no,
            );
            if (staffWarningMails.length) {
                for (const warningMail of staffWarningMails) {
                    const { userIds, staffs } = warningMail;
                    const staffSendWarningStudents =
                        staffs && staffs.length
                            ? staffs.find((staff) => staff._id.toString() === staffId.toString())
                            : undefined;
                    if (staffSendWarningStudents) {
                        const notSendStudents = userIds.filter(
                            (userId) =>
                                !staffSendWarningStudents.sendStudents.find(
                                    (sendStudent) => userId.toString() === sendStudent.toString(),
                                ),
                        );
                        if (notSendStudents.length) {
                            staffSendMails.push({
                                staffId,
                                email,
                                name,
                                courseId: course._course_id,
                                rotationCount: course.rotation_count,
                                institutionCalendarId: course._institution_calendar_id,
                                programId: course._program_id,
                                term: course.term,
                                yearNo: course.year_no,
                                levelNo: course.level_no,
                                students: notSendStudents,
                                institutionId: course._institution_id,
                                warningId: warningMail.warningId,
                                programName: course.programName,
                                courseName: course.courseName,
                                courseCode: course.courseCode,
                            });
                        }
                    } else {
                        staffSendMails.push({
                            staffId,
                            email,
                            name,
                            courseId: course._course_id,
                            rotationCount: course.rotation_count,
                            institutionCalendarId: course._institution_calendar_id,
                            programId: course._program_id,
                            term: course.term,
                            yearNo: course.year_no,
                            levelNo: course.level_no,
                            students: userIds,
                            warningId: warningMail.warningId,
                            institutionId: course._institution_id,
                            programName: course.programName,
                            courseName: course.courseName,
                            courseCode: course.courseCode,
                        });
                    }
                }
            }
        }
    }
    return { staffSendMails, warningMails };
};

exports.warningMailForStaff = async () => {
    //get current institutionCalendarId
    const institutionCalendarId = (
        await InstitutionCalendar.findOne({
            isDeleted: false,
            status: 'published',
        })
            .sort({ _id: 1 }) // TODO work with sort desc
            .lean()
    )._id;
    // get staff details course admin only
    let staffs = await getCourseAdmins(institutionCalendarId);
    let staffIds = [
        ...new Set(
            staffs
                .filter((staff) => staff.staffId)
                .map((staffMapped) => staffMapped.staffId.toString()),
        ),
    ];
    staffIds = staffIds.map((staffId) => convertToMongoObjectId(staffId));
    const staffDetails = await User.find({ _id: { $in: staffIds } }, { email: 1, name: 1 }).lean();
    staffs = staffs
        .filter((staffEntry) =>
            staffDetails.find(
                (staffDetailEntry) =>
                    staffDetailEntry._id.toString() === staffEntry.staffId.toString(),
            ),
        )
        .map((staff) => {
            const staffName = staffDetails.find(
                (staffDetail) => staffDetail._id.toString() === staff.staffId.toString(),
            );
            if (staffName) {
                staff.name = staffName.name;
                staff.email = staffName.email;
            }
            return staff;
        });
    const { staffSendMails: staffSendMailWithCourses, warningMails } = await getCourseWithSchedules(
        staffs,
        institutionCalendarId,
    );
    const staffSendMails = [];
    const lms = (await lmsData()).filter((lmsEntry) => !lmsEntry.isDeleted);
    let students = staffSendMailWithCourses.map(
        (staffSendMailWithCourse) => staffSendMailWithCourse.students,
    );
    // eslint-disable-next-line prefer-spread
    students = [].concat.apply([], students);
    const studentIds = [...new Set(students.map((student) => student.toString()))].map(
        (studentEntry) => convertToMongoObjectId(studentEntry),
    );
    const studentDetails = await User.find(
        { _id: { $in: studentIds } },
        { email: 1, name: 1 },
    ).lean();
    if (staffs.length) {
        staffs = staffs.reduce((acc, current) => {
            const x = acc.find((item) => item.staffId.toString() === current.staffId.toString());
            if (!x) {
                return acc.concat([current]);
            }
            return acc;
        }, []);
        for (staff of staffs) {
            const staffSendMailMakeContents = staffSendMailWithCourses.filter(
                (staffSendMail) => staffSendMail.staffId.toString() === staff.staffId.toString(),
            );
            // send mail content making
            if (staffSendMailMakeContents.length) {
                let uniqueContents = staffSendMailMakeContents.map((staffSendMailMakeContent) => {
                    return {
                        courseId: staffSendMailMakeContent.courseId,
                        institutionCalendarId: staffSendMailMakeContent.institutionCalendarId,
                        programId: staffSendMailMakeContent.programId,
                        term: staffSendMailMakeContent.term,
                        yearNo: staffSendMailMakeContent.yearNo,
                        levelNo: staffSendMailMakeContent.levelNo,
                        warningId: staffSendMailMakeContent.warningId,
                    };
                });
                uniqueContents = uniqueContents.reduce((acc, current) => {
                    const x = acc.find(
                        (item) =>
                            item.courseId.toString() === current.courseId.toString() &&
                            item.programId.toString() === current.programId.toString() &&
                            item.institutionCalendarId.toString() ===
                                current.institutionCalendarId.toString() &&
                            item.term.toString() === current.term.toString() &&
                            item.levelNo.toString() === current.levelNo.toString() &&
                            item.warningId.toString() === current.warningId.toString() &&
                            item.yearNo.toString() === current.yearNo.toString(),
                    );
                    if (!x) {
                        return acc.concat([current]);
                    }
                    return acc;
                }, []);

                for (const uniqueContent of uniqueContents) {
                    const uniqueStaffContents = staffSendMailMakeContents.filter(
                        (staffSendMailMakeContent) =>
                            staffSendMailMakeContent.courseId.toString() ===
                                uniqueContent.courseId.toString() &&
                            staffSendMailMakeContent.programId.toString() ===
                                uniqueContent.programId.toString() &&
                            staffSendMailMakeContent.institutionCalendarId.toString() ===
                                uniqueContent.institutionCalendarId.toString() &&
                            staffSendMailMakeContent.term.toString() ===
                                uniqueContent.term.toString() &&
                            staffSendMailMakeContent.levelNo.toString() ===
                                uniqueContent.levelNo.toString() &&
                            staffSendMailMakeContent.warningId.toString() ===
                                uniqueContent.warningId.toString() &&
                            staffSendMailMakeContent.yearNo.toString() ===
                                uniqueContent.yearNo.toString(),
                    );
                    const sendMailContent = await makeStaffMailContent(
                        staff.name,
                        uniqueStaffContents,
                        lms,
                        studentDetails,
                    );
                    staffSendMails.push({
                        staffId: staff.staffId,
                        name: staff.name,
                        email: staff.email,
                        content: sendMailContent,
                    });
                }
            }
        }
    }
    return { staffSendMails, staffCourses: staffSendMailWithCourses, warningMails };
};

exports.warningMailForStaffWithData = async () => {
    //get current institutionCalendarId
    const institutionCalendarId = (
        await InstitutionCalendar.findOne({
            isDeleted: false,
            status: 'published',
        })
            .sort({ _id: 1 }) // TODO work with sort desc
            .lean()
    )._id;
    const warningQuery = {
        isDeleted: false,
        sendDate: convertToUtcFormat(new Date()),
        institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
    };
    console.time('warningMails');
    const warningMails = await WarningMail.find(warningQuery, {}).lean();
    console.timeEnd('warningMails');
    if (warningMails && warningMails.length === 0) return { status: false };
    const courseId = [
        ...new Set(warningMails.map((warningElement) => warningElement.courseId.toString())),
    ];
    console.time('courseData');
    const courseData = await Course.find(
        { isDeleted: false, _id: { $in: courseId } },
        {
            coordinators: 1,
            _program_id: 1,
            course_name: 1,
            course_code: 1,
        },
    )
        .populate({
            path: '_program_id',
            select: { name: 1 },
        })
        .lean();
    console.timeEnd('courseData');
    const courseCoordinatorData = [];
    const courseStaffIds = [];
    for (courseElement of courseData) {
        // programIds.push(courseElement._program_id.toString());
        // const userCourseData = courseElement.coordinators.filter(
        //     (coordinatorElement) => coordinatorElement._user_id.toString() === userId.toString(),
        // );
        for (userElement of courseElement.coordinators) {
            const courseFind = warningMails.find(
                (warningElement) =>
                    warningElement.institutionCalendarId.toString() ===
                        userElement._institution_calendar_id.toString() &&
                    warningElement.courseId.toString() === courseElement._id.toString() &&
                    warningElement.programId.toString() ===
                        courseElement._program_id._id.toString() &&
                    warningElement.term.toString() === userElement.term.toString() &&
                    warningElement.levelNo.toString() === userElement.level_no.toString(),
            );
            if (courseFind) {
                courseStaffIds.push(userElement._user_id.toString());
                courseCoordinatorData.push({
                    courseId: courseElement._id,
                    courseName: courseElement.course_name,
                    courseCode: courseElement.course_code,
                    programId: courseElement._program_id,
                    term: userElement.term,
                    year: userElement.year,
                    level_no: userElement.level_no,
                    userId: userElement._user_id,
                });
            }
        }
    }
    const userIds = [
        ...warningMails
            .map((warningElement) =>
                warningElement.userIds.map((userElement) => userElement.toString()),
            )
            .flat(),
        ...courseStaffIds,
    ];
    console.time('userDetails');
    const userDetails = await User.find(
        { _id: { $in: userIds } },
        { email: 1, name: 1, user_id: 1 },
    ).lean();
    console.timeEnd('userDetails');
    console.time('lms');
    const lms = await get(
        lmsCollection,
        { isDeleted: false },
        { _institution_id: 1, student_warning_absence_calculation: 1 },
    );
    if (!lms.status) lms.data = [{ student_warning_absence_calculation: [] }];
    console.timeEnd('lms');
    let studentWarningAbsenceSetting = lms.data.student_warning_absence_calculation.filter(
        (studentWarning) => !studentWarning.isDeleted,
    );
    studentWarningAbsenceSetting = studentWarningAbsenceSetting.sort((a, b) => {
        let comparison = 0;
        if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
            comparison = -1;
        } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
            comparison = 1;
        }
        return comparison;
    });
    const staffSendMails = [];
    const staffCourses = [];
    for (warningElement of warningMails) {
        const warningCourseStaff = courseCoordinatorData.find(
            (courseElement) =>
                warningElement.courseId.toString() === courseElement.courseId.toString() &&
                warningElement.programId.toString() === courseElement.programId._id.toString() &&
                warningElement.term.toString() === courseElement.term.toString() &&
                warningElement.levelNo.toString() === courseElement.level_no.toString(),
        );
        if (warningCourseStaff) {
            const courseStaff = userDetails.find(
                (userElement) =>
                    userElement._id.toString() === warningCourseStaff.userId.toString(),
            );
            const uniqueStaffContents = {
                institutionId: warningElement.institutionCalendarId,
                warningId: warningElement.warningId,
                programName: warningCourseStaff.programId.name,
                courseName: warningCourseStaff.courseName,
                courseCode: warningCourseStaff.courseCode,
                levelNo: warningElement.levelNo,
                term: warningElement.term,
            };
            const sendMailContent = await makeStaffMailContent(
                courseStaff.name,
                uniqueStaffContents,
                studentWarningAbsenceSetting,
                userDetails,
                warningElement.userIds,
            );
            staffSendMails.push({
                staffId: courseStaff._id,
                name: courseStaff.name,
                email: courseStaff.email,
                content: sendMailContent,
            });
            staffCourses.push({
                courseId: warningElement.courseId,
                institutionCalendarId: warningElement.institutionCalendarId,
                programId: warningElement.programId,
                term: warningElement.term,
                yearNo: warningElement.yearNo,
                levelNo: warningElement.levelNo,
                warningId: warningElement.warningId,
                students: warningElement.userIds,
                staffId: warningCourseStaff.userId,
                rotationCount: warningElement.rotationCount,
            });
        }
    }
    return { staffSendMails, staffCourses, warningMails };
};
