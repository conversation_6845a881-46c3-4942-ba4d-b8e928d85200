const express = require('express');
const route = express.Router();
const credit_hours_master = require('./credit_hours_master_controller');
const validater = require('./credit_hours_master_validator');
route.post('/list', credit_hours_master.list_values);
route.get('/:id', validater.credit_hours_master_id, credit_hours_master.list_id);
route.get('/program/:id', validater.credit_hours_master_id, credit_hours_master.list_program);
route.get('/', credit_hours_master.list);
route.post('/', validater.credit_hours_master, credit_hours_master.insert);
route.put('/:id', validater.credit_hours_master_id, validater.credit_hours_master_update, credit_hours_master.update);
route.delete('/:id', validater.credit_hours_master_id, credit_hours_master.delete);
route.get('/is_credit_available/:id', validater.credit_hours_master_id, credit_hours_master.is_credit_available);

module.exports = route;