const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    qualityAssuranceApproverList,
    formApplicationList,
    approverUserList,
    updateApproverBy,
    levelRoleUserList,
    approverCategoryList,
    formInitiatorUserList,
    sendEmailToFormInitiator,
    addComment,
} = require('./formApprover.controller');

router.get('/qualityAssuranceApproverList', catchAsync(qualityAssuranceApproverList));
router.get('/formApplicationList', catchAsync(formApplicationList));
router.get('/approverUserList', catchAsync(approverUserList));
router.put('/updateApproverBy', catchAsync(updateApproverBy));
router.get('/levelRoleUserList', catchAsync(levelRoleUserList));
router.get('/approverCategoryList', catchAsync(approverCategoryList));
//form initiator user list
router.get('/formInitiatorUserList', catchAsync(formInitiatorUserList));
router.post('/sendEmailToFormInitiator', catchAsync(sendEmailToFormInitiator));
//add commend
router.post('/addComment', catchAsync(addComment));
module.exports = router;
