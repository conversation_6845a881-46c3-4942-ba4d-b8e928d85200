const constant = require('../utility/constants');
var roles_permissions = require('mongoose').model(constant.ROLES_PERMISSION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');


exports.create = async(req,res) => {
    let objs = {
        role_name: req.body.role_name,
        _role_id: req.body._role_id,
        _assigned_by_id: req.body._assigned_by_id,
        assigned_by: req.body.assigned_by,
        _office_id: req.body._office_id,
        office_name: req.body.office_name,
        program_mode: req.body.program_mode,
        _program_id: req.body._program_id,
        program_name: req.body.program_name,
        _department_id:req.body._department_id,
        department_name: req.body.department_name,
        _permissions: req.body._permissions
    }
    let doc = await base_control.insert(roles_permissions ,objs);
    if(doc.status){
        common_files.com_response(res ,200 ,true, "roles added" ,doc.responses)
    } else {
        common_files.com_response(res ,404 ,false ,"Error in adding roles", "Error in adding roles")
    }
}

exports.update = async(req,res) => {
    let checks = {status:true};
    checks = await base_control.check_id(roles_permissions,{_id:{$in:req.params.id}, 'isDeleted':false});
    if(checks.status){
    let objs = {};
    if(req.body != undefined){
        Object.assign(objs,{'role_name' :req.body.role_name},{'_role_id' :req.body._role_id},{'_assigned_by_id' :req.body._assigned_by_id},
        {'assigned_by' :req.body.assigned_by},{'_office_id' :req.body._office_id},{'office_name' :req.body.office_name},
        {'program_mode' :req.body.program_mode},{'_program_id' :req.body._program_id},{'program_name' :req.body.program_name},
        {'_department_id' :req.body._department_id},{'department_name' :req.body.department_name},{'_permissions' :req.body._permissions})
    } else {
        if(req.body.role_name == undefined && req.body._role_id == undefined){
          Object.assign(objs,{'_assigned_by_id' :req.body._assigned_by_id},{'assigned_by' :req.body.assigned_by},{'_office_id' :req.body._office_id},
          {'office_name' :req.body.office_name},{'program_mode' :req.body.program_mode},{'_program_id' :req.body._program_id},{'program_name' :req.body.program_name},
          {'_department_id' :req.body._department_id},{'department_name' :req.body.department_name},{'_permissions' :req.body._permissions})
        }
        if(req.body._assigned_by_id == undefined && req.body.assigned_by == undefined){
            Object.assign(objs,{'role_name' :req.body.role_name},{'_role_id' :req.body._role_id},{'_office_id' :req.body._office_id},
            {'office_name' :req.body.office_name},{'program_mode' :req.body.program_mode},{'_program_id' :req.body._program_id},{'program_name' :req.body.program_name},
            {'_department_id' :req.body._department_id},{'department_name' :req.body.department_name},{'_permissions' :req.body._permissions})
        }
        if(req.body._office_id == undefined && req.body.office_name == undefined){
            Object.assign(objs,{'role_name' :req.body.role_name},{'_role_id' :req.body._role_id},{'_assigned_by_id' :req.body._assigned_by_id},
            {'assigned_by' :req.body.assigned_by},{'program_mode' :req.body.program_mode},{'_program_id' :req.body._program_id},{'program_name' :req.body.program_name},
            {'_department_id' :req.body._department_id},{'department_name' :req.body.department_name},{'_permissions' :req.body._permissions})
        }
        if(req.body.program_mode == undefined && req.body._program_id == undefined && req.body.program_name == undefined){
            Object.assign(objs,{'role_name' :req.body.role_name},{'_role_id' :req.body._role_id},{'_assigned_by_id' :req.body._assigned_by_id},
            {'assigned_by' :req.body.assigned_by},{'_office_id' :req.body._office_id},{'office_name' :req.body.office_name},
            {'_department_id' :req.body._department_id},{'department_name' :req.body.department_name},{'_permissions' :req.body._permissions})
        }
        if(req.body._department_id == undefined && req.body.department_name == undefined){
            Object.assign(objs,{'role_name' :req.body.role_name},{'_role_id' :req.body._role_id},{'_assigned_by_id' :req.body._assigned_by_id},
            {'assigned_by' :req.body.assigned_by},{'_office_id' :req.body._office_id},{'office_name' :req.body.office_name},
            {'program_mode' :req.body.program_mode},{'_program_id' :req.body._program_id},{'program_name' :req.body.program_name},{'_permissions' :req.body._permissions})
        }
        if(req.body._permissions == undefined){
            Object.assign(objs,{'role_name' :req.body.role_name},{'_role_id' :req.body._role_id},{'_assigned_by_id' :req.body._assigned_by_id},
            {'assigned_by' :req.body.assigned_by},{'_office_id' :req.body._office_id},{'office_name' :req.body.office_name},
            {'program_mode' :req.body.program_mode},{'_program_id' :req.body._program_id},{'program_name' :req.body.program_name},
            {'_department_id' :req.body._department_id},{'department_name' :req.body.department_name})
        }
    }
    let doc = await base_control.update(roles_permissions,{_id:req.params.id} ,objs);
    if(doc.status){
        common_files.com_response(res ,200 ,true ,"Updated roles" ,doc.data)
    } else {
        common_files.com_response(res ,404 ,false ,"Error in updating roles" ,"Error in updating roles")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.get = async(req,res) => {
    let doc_data = [];
    let doc = await base_control.get_list(roles_permissions,{'isDeleted':false});
    if(doc.status){
        doc.data.forEach(element => {
            doc_data.push({_id:element._id, role_name:element.role_name, _role_id:element._role_id,
                _assigned_by_id: element._assigned_by_id, assigned_by:element.assigned_by, _office_id:element._office_id,
                office_name:element.office_name, program_mode:element.program_mode, _program_id:element._program_id, 
                program_name:element.program_name, _department_id:element._department_id, department_name:element.department_name,
                _permissions:element._permissions});
        });
        common_files.com_response(res ,200 ,true ,"Got all the roles added" ,doc_data)
    } else {
        common_files.com_response(res ,404 ,false ,"Error in getting added roles" ,"Error in getting added roles")
    }
}

exports.get_id = async(req,res) => {
    let checks = {status:true};
    checks = await base_control.check_id(roles_permissions,{_id: {$in:req.params.id}, 'isDeleted':false});
    if(checks.status){
    let doc_data = [];
    let doc = await base_control.get_list(roles_permissions ,{_id:req.params.id});
    if(doc.status){
        doc.data.forEach(element => {
            doc_data.push({_id:element._id, role_name:element.role_name, _role_id:element._role_id,
                _assigned_by_id: element._assigned_by_id, assigned_by:element.assigned_by, _office_id:element._office_id,
                office_name:element.office_name, program_mode:element.program_mode, _program_id:element._program_id, 
                program_name:element.program_name, _department_id:element._department_id, department_name:element.department_name,
                _permissions:element._permissions});
        })
        common_files.com_response(res ,200 ,true ,"Got added roles by id" ,doc_data)
    }else {
        common_files.com_response(res ,404 ,false ,"Error in getting added roles by id" ,"Error in getting added roles by id")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.delete = async(req,res) =>{
    let checks = {status: true};
    checks = await base_control.check_id(roles_permissions, {_id:{$in:req.params.id}, 'isDeleted':false});
    if(checks.status){
    let doc = await base_control.delete(roles_permissions,{_id:req.params.id});
    if(doc.status){
        common_files.com_response(res ,200 ,true ,"Deleted added role")
    } else {
        common_files.com_response(res ,404 ,false ,"Error in deleting added role" ,"Error in deleting addded role")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}