const { convertToMongoObjectId } = require('../utility/common');
const { ABSENT, EXCLUDE, COMPLETED } = require('../utility/constants');
const sessionStatusManagementSchema = require('./session_status_management_model');
const courseScheduleSchema = require('../models/course_schedule');
exports.getCourseSessionStatusDetail = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { institutionCalendarId, programId, curriculumId, term, year, level, courseId } =
            query;
        const courseSessionStatusDetail = await sessionStatusManagementSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                    year,
                    level,
                    _course_id: convertToMongoObjectId(courseId),
                },
                { activeToInactive: 1, inactiveToActive: 1, isEnabled: 1 },
            )
            .lean();
        let sessionStatusDetailForCurrentCourse = courseSessionStatusDetail;
        if (!courseSessionStatusDetail) {
            const manuallyCreatedSessionStatusDetail = {
                _institution_id,
                _institution_calendar_id: institutionCalendarId,
                _program_id: programId,
                _curriculum_id: curriculumId,
                year,
                level,
                term,
                _course_id: courseId,
                activeToInactive: ABSENT,
                inactiveToActive: EXCLUDE,
            };
            const createdSessionStatusDetail = await sessionStatusManagementSchema.create(
                manuallyCreatedSessionStatusDetail,
            );
            if (createdSessionStatusDetail) {
                sessionStatusDetailForCurrentCourse = {
                    activeToInactive: createdSessionStatusDetail.activeToInactive,
                    inactiveToActive: createdSessionStatusDetail.inactiveToActive,
                    isEnabled: createdSessionStatusDetail.isEnabled,
                    _id: createdSessionStatusDetail._id,
                };
            }
        }
        return {
            statusCode: 200,
            message: 'session status details for current course',
            data: sessionStatusDetailForCurrentCourse,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.updateCourseSessionStatusDetail = async ({ params = {}, body = {} }) => {
    try {
        const { sessionStatusId } = params;
        const { isEnabled, activeToInactive, inactiveToActive } = body;
        const updatedSessionStatusDetail = await sessionStatusManagementSchema.updateOne(
            {
                _id: convertToMongoObjectId(sessionStatusId),
            },
            {
                $set: {
                    isEnabled,
                    activeToInactive,
                    inactiveToActive,
                },
            },
        );
        const updatedCount = updatedSessionStatusDetail.modifiedCount || 0;
        const successMessage = 'course session status details updated successfully';
        const failureMessage = 'Unable to update course session status details';
        return {
            statusCode: updatedCount ? 200 : 400,
            message: updatedCount ? successMessage : failureMessage,
        };
    } catch (error) {
        if (error instanceof Error) throw error;
        throw new Error(error);
    }
};
exports.getTotalScheduleCount = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            institutionCalendarId,
            term,
            year,
            level,
            programId,
            courseId,
            deliveryTypes,
            selectedGroups,
            gender,
        } = query;
        const scheduleSGQuery = [];
        selectedGroups.forEach((deliveryTypeElement, index) => {
            if (deliveryTypes[index])
                scheduleSGQuery.push({
                    $and: [
                        {
                            'session.delivery_symbol': deliveryTypes[index],
                        },
                        {
                            'student_groups.session_group.group_no': deliveryTypeElement,
                        },
                    ],
                });
        });
        const totalCompletedScheduleCount = await courseScheduleSchema.countDocuments({
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            term,
            year_no: year,
            level_no: level,
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            // 'session.delivery_symbol': { $in: deliveryTypes },
            // 'student_groups.group_no': { $in: selectedGroups },
            'student_groups.gender': gender,
            $or: scheduleSGQuery,
            type: 'regular',
            status: COMPLETED,
            isDeleted: false,
            isActive: true,
        });
        return {
            statusCode: 200,
            message: 'Total completed schedule count',
            data: totalCompletedScheduleCount,
        };
    } catch (error) {
        console.log(`Error in getTotalScheduleCount - ${error.message}`);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
