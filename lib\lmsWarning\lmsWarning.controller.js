const constant = require('../utility/constants');
const lmsStudentSettingSchema = require('mongoose').model(constant.LMS_STUDENT_SETTING);
const lmsSettingSchema = require('../lmsStudentSetting/lmsStudentSetting.model');
const warningMailSchema = require('mongoose').model(constant.WARNING_MAIL);
const userSchema = require('mongoose').model(constant.USER);
const lmsWarningConfigSchema = require('./lmsWarningConfig.model');
const { convertToMongoObjectId } = require('../utility/common');
const {
    getUserCourseListsForMultiCalendar,
    getUserCourseLists,
} = require('../digi_class/course_session/course_session_service');
const courseScheduleSchema = require('../models/course_schedule');
const courseSchema = require('../models/digi_course');
const {
    LEAVE_TYPE: { LEAVE },
    STUDENTS,
    STAFF,
    OVERVIEW,
    HISTORY,
    AUTOMATIC,
    MANUAL,
    COURSE_COORDINATOR,
    WARNING_CONFIG_TYPE,
    DS_INSTITUTION_KEY,
} = require('../utility/constants');
const moment = require('moment');
const { send_email } = require('../utility/common_functions');
const { createEmailContent } = require('../utility/warningContent');
const { nameFormatter } = require('../utility/common_functions');
const roleAssignSchema = require('../models/role_assign');
const roleSchema = require('../models/role');
const institutionCalendarsSchema = require('../models/institution_calendar');
const { lmsNewSetting, updateWarningsInRecord } = require('../utility/utility.service');
const { comprehensiveWarning } = require('./lmsWarning.service');

const getCourseWarning = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { studentId } = query;
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() - 1);
        const currentTime = moment();
        const calendar = await institutionCalendarsSchema
            .find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    status: constant.PUBLISHED,
                    isActive: true,
                    isDeleted: false,
                    start_date: { $lte: currentDate },
                    end_date: { $gte: currentDate },
                },
                {
                    _id: 1,
                    isActive: 1,
                    end_date: 1,
                },
            )
            .lean();
        const institutionCalendarId = calendar.map(({ _id }) => _id);
        console.time('studentWarningData');
        let studentWarningData = await warningMailSchema
            .find(
                {
                    institutionCalendarId: {
                        $in: institutionCalendarId,
                    },
                    userIds: convertToMongoObjectId(studentId),
                },
                {
                    institutionCalendarId: 1,
                    courseId: 1,
                    warningId: 1,
                    user: 1,
                    sendDate: 1,

                    // Comprehensive Warning Config
                    warningMode: 1,
                    programId: 1,
                    term: 1,
                    yearNo: 1,
                    levelNo: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: { course_name: 1 },
            })
            .sort({ sendDate: -1, createdAt: -1 })
            .lean();
        console.timeEnd('studentWarningData');

        console.time('studentSettingData');
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType: LEAVE,
                    isActive: true,
                },
                {
                    'warningConfig._id': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.message': 1,
                    'warningConfig.acknowledgeToStudent': 1,
                    'warningConfig.markItMandatory': 1,
                    'warningConfig.notificationToStudent.setType': 1,
                    'warningConfig.notificationToStudent.isActive': 1,
                    'warningConfig.restrictCourseAccess': 1,
                    'warningConfig.adminRestrictedAttendance': 1,

                    // Warning mode Configuration
                    warningMode: 1,
                    warningConfigBased: 1,

                    // Comprehensive Warning Config
                    'comprehensiveWarningConfig._id': 1,
                    'comprehensiveWarningConfig.isActive': 1,
                    'comprehensiveWarningConfig.labelName': 1,
                    'comprehensiveWarningConfig.colorCode': 1,
                    'comprehensiveWarningConfig.warningValue': 1,
                    'comprehensiveWarningConfig.message': 1,
                    'comprehensiveWarningConfig.acknowledgeToStudent': 1,
                    'comprehensiveWarningConfig.markItMandatory': 1,
                    'comprehensiveWarningConfig.notificationToStudent.setType': 1,
                    'comprehensiveWarningConfig.notificationToStudent.isActive': 1,
                    'comprehensiveWarningConfig.restrictCourseAccess': 1,
                    'comprehensiveWarningConfig.adminRestrictedAttendance': 1,
                    leaveCalculation: 1,
                },
            )
            .lean();
        console.timeEnd('studentSettingData');
        const userId = studentId;
        const type = STUDENTS;
        console.time('studentCourseList');
        const studentCourseList = await getUserCourseListsForMultiCalendar({
            userId,
            type,
            institutionCalendarId,
        });
        console.timeEnd('studentCourseList');
        if (!studentSettingData)
            return {
                status: 404,
                message: 'NOT_FOUND',
            };
        if (
            studentSettingData.warningMode &&
            studentSettingData.warningMode === WARNING_CONFIG_TYPE.COMPREHENSIVE
        ) {
            const studentComprehensiveWarningCards = comprehensiveWarning({
                studentId,
                studentWarningData,
                warningConfigBased: studentSettingData.warningConfigBased,
                warningConfig: studentSettingData.comprehensiveWarningConfig
                    .filter((warningConfig) => warningConfig.isActive)
                    .sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.warningValue) > parseInt(b.warningValue)) {
                            comparison = -1;
                        } else if (parseInt(a.warningValue) < parseInt(b.warningValue)) {
                            comparison = 1;
                        }
                        return comparison;
                    }),
                studentCourseList,
                institutionCalendar: calendar,
            });
            return {
                statusCode: 200,
                message: 'LIST_DATA',
                data: studentComprehensiveWarningCards,
            };
        }
        studentWarningData = studentWarningData.filter(
            (studentWarningElement) =>
                !(
                    studentWarningElement.warningMode &&
                    studentWarningElement.warningMode === WARNING_CONFIG_TYPE.COMPREHENSIVE
                ),
        );
        const courseData = new Map();
        for (courseDetails of studentCourseList) {
            const key =
                courseDetails._id.toString() + courseDetails._institution_calendar_id.toString();
            if (!courseData.has(key)) {
                courseData.set(key, {
                    startDate: courseDetails.start_date,
                    endDate: courseDetails.end_date,
                    term: courseDetails.term,
                    year: courseDetails.year,
                    level: courseDetails.level,
                });
            }
        }
        const warningMap = new Map();
        for (const warningConfig of studentSettingData.warningConfig) {
            const key = warningConfig._id.toString();
            if (!warningMap.has(key)) {
                warningMap.set(key, {
                    _id: key,
                    isActive: warningConfig.isActive,
                    notificationToStudent: warningConfig.notificationToStudent.isActive,
                    setType: warningConfig.notificationToStudent.setType,
                    labelName: warningConfig.labelName,
                    colorCode: warningConfig.colorCode,
                    percentage: warningConfig.percentage,
                    message: warningConfig.message,
                    acknowledgeToStudent: warningConfig.acknowledgeToStudent,
                    markItMandatory: warningConfig.markItMandatory,
                    restrictCourseAccess: warningConfig.restrictCourseAccess,
                    adminRestrictedAttendance: warningConfig.adminRestrictedAttendance,
                });
            }
        }
        console.time('Warning iteration');
        for (const warningData of studentWarningData) {
            warningData.isShownFor = false;
            const users =
                warningData.user &&
                warningData.user.length &&
                warningData.user.find((data) => data.userId.toString() === studentId.toString());
            if (users) {
                warningData.acknowledge = users.acknowledge;
                warningData.acknowledgedData = users.acknowledgedData;
                warningData.percentage = users.percentage ? users.percentage : 0;
            } else {
                warningData.acknowledge = false;
            }
            warningData.isActiveCourse = false;
            if (studentSettingData.warningConfig && warningMap.size) {
                const warningConfig = warningMap.get(warningData.warningId.toString());
                if (
                    warningConfig &&
                    warningData.warningId.toString() === warningConfig._id.toString()
                ) {
                    if (
                        warningConfig.isActive &&
                        users &&
                        users.notificationInfo &&
                        users.notificationInfo.isSent
                    ) {
                        warningData.isShownFor = true;
                    }
                    warningData.warningName = warningConfig.labelName;
                    warningData.colorCode = warningConfig.colorCode;
                    warningData.percentage = warningConfig.percentage;
                    warningData.message = warningConfig.message;
                    warningData.acknowledgeToStudent = warningConfig.acknowledgeToStudent;
                    warningData.markItMandatory = warningConfig.markItMandatory;
                    warningData.restrictCourseAccess = warningConfig.restrictCourseAccess;
                    warningData.adminRestrictedAttendance = warningConfig.adminRestrictedAttendance;
                }
            }
            delete warningData.user;
            const studentCourseDetail = courseData.get(
                warningData.courseId._id.toString() + warningData.institutionCalendarId.toString(),
            );
            if (studentCourseDetail) {
                warningData.startDate = studentCourseDetail.start_date;
                warningData.end_date = studentCourseDetail.end_date;
                warningData.level = studentCourseDetail.level;
                warningData.year = studentCourseDetail.year;
                warningData.term = studentCourseDetail.term;
                const courseStartDateTime = moment(
                    studentCourseDetail.startDate,
                    'YYYY-MM-DDTHH:mm:ss',
                );
                const courseEndDateTime = moment(
                    studentCourseDetail.endDate,
                    'YYYY-MM-DDTHH:mm:ss',
                );
                if (
                    currentTime.isSameOrAfter(courseStartDateTime) &&
                    currentTime.isSameOrBefore(courseEndDateTime)
                ) {
                    warningData.isActiveCourse = true;
                }
            }
            const warningCalendar = calendar.find(
                (calendarElement) =>
                    calendarElement._id.toString() === warningData.institutionCalendarId.toString(),
            );
            warningData.calendarEndDate = warningCalendar.end_date;
            warningData.calendarActive = warningCalendar.isActive;
            warningData.dashboardShown = false;
            if (
                warningData.calendarActive &&
                warningData.calendarEndDate.getTime() >= currentDate.getTime()
            ) {
                warningData.dashboardShown = true;
            }
        }
        console.timeEnd('Warning iteration');
        studentWarningData = studentWarningData.filter(
            (studentWarningElement) => studentWarningElement.isShownFor,
        );
        let allAcknowledgement = true;
        const acknowledge = studentWarningData
            .filter(
                (data) =>
                    data.isActiveCourse === true &&
                    data.calendarEndDate.getTime() >= currentDate.getTime(),
            )
            .find((element) => element.acknowledge === false && element.markItMandatory === true);
        if (acknowledge) {
            allAcknowledgement = false;
        }
        studentWarningData = studentWarningData.sort((a, b) => {
            if (a.calendarActive === b.calendarActive) {
                return b.calendarEndDate.getTime() - a.calendarEndDate.getTime();
            }
            return a.calendarActive ? -1 : 1;
        });
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: { studentWarningData, allAcknowledgement },
        };
    } catch (error) {
        console.error(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseWarningForCalendar = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { studentId, institutionCalendarId } = query;
        const currentDate = new Date();
        currentDate.setDate(currentDate.getDate() - 1);
        const currentTime = moment();
        console.time('studentWarningData');
        let studentWarningData = await warningMailSchema
            .find(
                {
                    institutionCalendarId,
                    userIds: convertToMongoObjectId(studentId),
                },
                {
                    institutionCalendarId: 1,
                    courseId: 1,
                    warningId: 1,
                    user: 1,
                    sendDate: 1,

                    // Comprehensive Warning Config
                    warningMode: 1,
                    programId: 1,
                    term: 1,
                    yearNo: 1,
                    levelNo: 1,
                },
            )
            .populate({
                path: 'courseId',
                select: { course_name: 1 },
            })
            .sort({ createdAt: -1 })
            .lean();
        console.timeEnd('studentWarningData');
        const warningCalendar = await institutionCalendarsSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(institutionCalendarId),
                },
                {
                    _id: 1,
                    isActive: 1,
                    end_date: 1,
                },
            )
            .lean();
        console.time('studentSettingData');
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType: LEAVE,
                    isActive: true,
                },
                {
                    'warningConfig._id': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.message': 1,
                    'warningConfig.acknowledgeToStudent': 1,
                    'warningConfig.markItMandatory': 1,
                    'warningConfig.notificationToStudent.setType': 1,
                    'warningConfig.notificationToStudent.isActive': 1,

                    // Warning mode Configuration
                    warningMode: 1,
                    warningConfigBased: 1,

                    // Comprehensive Warning Config
                    'comprehensiveWarningConfig._id': 1,
                    'comprehensiveWarningConfig.isActive': 1,
                    'comprehensiveWarningConfig.labelName': 1,
                    'comprehensiveWarningConfig.colorCode': 1,
                    'comprehensiveWarningConfig.warningValue': 1,
                    'comprehensiveWarningConfig.message': 1,
                    'comprehensiveWarningConfig.acknowledgeToStudent': 1,
                    'comprehensiveWarningConfig.markItMandatory': 1,
                    'comprehensiveWarningConfig.notificationToStudent.setType': 1,
                    'comprehensiveWarningConfig.notificationToStudent.isActive': 1,
                    'comprehensiveWarningConfig.restrictCourseAccess': 1,
                    leaveCalculation: 1,
                },
            )
            .lean();
        console.timeEnd('studentSettingData');
        const userId = studentId;
        const type = STUDENTS;
        console.time('studentCourseList');
        const studentCourseList = await getUserCourseLists({
            userId,
            type,
            institutionCalendarId,
        });
        console.timeEnd('studentCourseList');
        if (
            studentSettingData.warningMode &&
            studentSettingData.warningMode === WARNING_CONFIG_TYPE.COMPREHENSIVE
        ) {
            const studentComprehensiveWarningCards = comprehensiveWarning({
                studentId,
                studentWarningData,
                warningConfigBased: studentSettingData.warningConfigBased,
                warningConfig: studentSettingData.comprehensiveWarningConfig
                    .filter((warningConfig) => warningConfig.isActive)
                    .sort((a, b) => {
                        let comparison = 0;
                        if (parseInt(a.warningValue) > parseInt(b.warningValue)) {
                            comparison = -1;
                        } else if (parseInt(a.warningValue) < parseInt(b.warningValue)) {
                            comparison = 1;
                        }
                        return comparison;
                    }),
                studentCourseList,
                institutionCalendar: [warningCalendar],
            });
            return {
                statusCode: 200,
                message: 'LIST_DATA',
                data: studentComprehensiveWarningCards,
            };
        }
        studentWarningData = studentWarningData.filter(
            (studentWarningElement) =>
                !(
                    studentWarningElement.warningMode &&
                    studentWarningElement.warningMode === WARNING_CONFIG_TYPE.COMPREHENSIVE
                ),
        );
        const courseData = new Map();
        for (courseDetails of studentCourseList) {
            const key =
                courseDetails._id.toString() + courseDetails._institution_calendar_id.toString();
            if (!courseData.has(key)) {
                courseData.set(key, {
                    startDate: courseDetails.start_date,
                    endDate: courseDetails.end_date,
                    term: courseDetails.term,
                    year: courseDetails.year,
                    level: courseDetails.level,
                });
            }
        }
        const warningMap = new Map();
        for (const warningConfig of studentSettingData.warningConfig) {
            const key = warningConfig._id.toString();
            if (!warningMap.has(key)) {
                warningMap.set(key, {
                    _id: key,
                    isActive: warningConfig.isActive,
                    notificationToStudent: warningConfig.notificationToStudent.isActive,
                    setType: warningConfig.notificationToStudent.setType,
                    labelName: warningConfig.labelName,
                    colorCode: warningConfig.colorCode,
                    percentage: warningConfig.percentage,
                    message: warningConfig.message,
                    acknowledgeToStudent: warningConfig.acknowledgeToStudent,
                    markItMandatory: warningConfig.markItMandatory,
                });
            }
        }
        console.time('Warning iteration');
        for (const warningData of studentWarningData) {
            warningData.isShownFor = false;
            const users =
                warningData.user &&
                warningData.user.length &&
                warningData.user.find((data) => data.userId.toString() === studentId.toString());
            if (users) {
                warningData.acknowledge = users.acknowledge;
                warningData.acknowledgedData = users.acknowledgedData;
                warningData.percentage = users.percentage ? users.percentage : 0;
            } else {
                warningData.acknowledge = false;
            }
            warningData.isActiveCourse = false;
            if (studentSettingData.warningConfig && warningMap.size) {
                const warningConfig = warningMap.get(warningData.warningId.toString());
                if (
                    warningConfig &&
                    warningData.warningId.toString() === warningConfig._id.toString()
                ) {
                    if (
                        warningConfig.isActive &&
                        users &&
                        users.notificationInfo &&
                        users.notificationInfo.isSent
                    ) {
                        warningData.isShownFor = true;
                    }
                    warningData.warningName = warningConfig.labelName;
                    warningData.colorCode = warningConfig.colorCode;
                    warningData.percentage = warningConfig.percentage;
                    warningData.message = warningConfig.message;
                    warningData.acknowledgeToStudent = warningConfig.acknowledgeToStudent;
                    warningData.markItMandatory = warningConfig.markItMandatory;
                }
            }
            delete warningData.user;
            const studentCourseDetail = courseData.get(
                warningData.courseId._id.toString() + warningData.institutionCalendarId.toString(),
            );
            if (studentCourseDetail) {
                warningData.startDate = studentCourseDetail.start_date;
                warningData.end_date = studentCourseDetail.end_date;
                warningData.level = studentCourseDetail.level;
                warningData.year = studentCourseDetail.year;
                warningData.term = studentCourseDetail.term;
                const courseStartDateTime = moment(
                    studentCourseDetail.startDate,
                    'YYYY-MM-DDTHH:mm:ss',
                );
                const courseEndDateTime = moment(
                    studentCourseDetail.endDate,
                    'YYYY-MM-DDTHH:mm:ss',
                );
                if (
                    currentTime.isSameOrAfter(courseStartDateTime) &&
                    currentTime.isSameOrBefore(courseEndDateTime)
                ) {
                    warningData.isActiveCourse = true;
                }
            }
            warningData.calendarEndDate = warningCalendar.end_date;
            warningData.calendarActive = warningCalendar.isActive;
            warningData.dashboardShown = false;
            if (
                warningData.calendarActive &&
                warningData.calendarEndDate.getTime() >= currentDate.getTime()
            ) {
                warningData.dashboardShown = true;
            }
        }
        console.timeEnd('Warning iteration');
        studentWarningData = studentWarningData.filter(
            (studentWarningElement) => studentWarningElement.isShownFor,
        );
        let allAcknowledgement = true;
        const acknowledge = studentWarningData
            .filter(
                (studentWarningElement) =>
                    studentWarningElement.isActiveCourse === true &&
                    studentWarningElement.calendarEndDate.getTime() >= currentDate.getTime(),
            )
            .find(
                (filteredStudentWarningElement) =>
                    filteredStudentWarningElement.acknowledge === false &&
                    filteredStudentWarningElement.markItMandatory === true,
            );
        if (acknowledge) {
            allAcknowledgement = false;
        }
        studentWarningData = studentWarningData.sort((a, b) => {
            if (a.calendarActive === b.calendarActive) {
                return b.calendarEndDate.getTime() - a.calendarEndDate.getTime();
            }
            return a.calendarActive ? -1 : 1;
        });
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: { studentWarningData, allAcknowledgement },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const studentAcknowledge = async ({ query = {}, body = {} }) => {
    try {
        const { id, user_Id, currentDate } = query;
        let studentWarningData = await warningMailSchema
            .findOne({
                _id: convertToMongoObjectId(id),
                'user.userId': convertToMongoObjectId(user_Id),
            })
            .lean();
        if (!studentWarningData) {
            studentWarningData = await warningMailSchema.updateOne(
                {
                    _id: convertToMongoObjectId(id),
                },
                {
                    $push: {
                        user: {
                            userId: convertToMongoObjectId(user_Id),
                            acknowledge: true,
                            acknowledgedData: new Date(currentDate),
                        },
                    },
                },
            );
        }
        if (studentWarningData) {
            studentWarningData = await warningMailSchema.updateOne(
                { _id: convertToMongoObjectId(id) },
                {
                    $set: {
                        'user.$[userArray].acknowledge': true,
                        'user.$[userArray].acknowledgedData': new Date(currentDate),
                    },
                },
                { arrayFilters: [{ 'userArray.userId': convertToMongoObjectId(user_Id) }] },
            );
        }
        if (!studentWarningData) return { statusCode: 410, message: 'UNABLE_TO_DATA_UPDATED' };
        return { statusCode: 200, message: 'DATA_UPDATE' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const staffAutoWarning = async ({ headers = {}, body = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id, _user_id } = headers;
        const { viewMode, roleId } = query;
        let roleAssignDocs;
        let studentWarningData = [];
        let roleIds = [];
        let roleAssignIds = [];
        let roleProgram = [];
        if (roleId) {
            roleAssignDocs = await roleAssignSchema
                .findOne({
                    _user_id: convertToMongoObjectId(_user_id),
                    isDeleted: false,
                    isActive: true,
                    'roles._role_id': convertToMongoObjectId(roleId),
                })
                .populate({ path: '_user_id', select: { name: 1 } })
                .lean();
            if (roleAssignDocs && roleAssignDocs.roles && roleAssignDocs.roles.length) {
                roleIds = roleAssignDocs.roles.map((roleElement) =>
                    roleElement._role_id.toString(),
                );
                roleAssignIds = roleAssignDocs.roles.map((roleElement) => {
                    if (roleElement.isAdmin) return roleElement._role_id.toString();
                });
                roleProgram =
                    roleAssignDocs.roles
                        .flatMap((ele) => ele.program.map((ele1) => ele1._program_id))
                        .filter(Boolean) || [];
            }
            console.time('warningMailSchema');
            studentWarningData = await warningMailSchema
                .find(
                    {
                        institutionCalendarId: convertToMongoObjectId(_institution_calendar_id),
                        programId: { $in: roleProgram },
                        user: { $exists: true, $ne: [] },
                    },
                    {
                        sendDate: 1,
                        courseId: 1,
                        yearNo: 1,
                        levelNo: 1,
                        term: 1,
                        programId: 1,
                        institutionCalendarId: 1,
                        warningId: 1,
                        user: 1,
                        rotationCount: 1,
                        course_name: 1,
                        course_code: 1,
                        program_name: 1,
                    },
                )
                .sort({ sendDate: -1, createdAt: -1 })
                .lean();
            console.timeEnd('warningMailSchema');
        }
        const courseCoordinators = await roleSchema
            .findOne({ name: COURSE_COORDINATOR }, { _id: 1 })
            .lean();
        const courseDocs = await courseSchema
            .find(
                {
                    isActive: true,
                    isDeleted: false,
                    'coordinators._user_id': convertToMongoObjectId(_user_id),
                    'coordinators._institution_calendar_id':
                        convertToMongoObjectId(_institution_calendar_id),
                },
                { _id: 1, _program_id: 1, 'coordinators.$': 1 },
            )
            .lean();
        let courseId = [];
        const courseDetails = {};
        for (const courseDocElement of courseDocs) {
            const key = courseDocElement._id.toString();
            if (!courseDetails.hasOwnProperty(key)) {
                courseDetails[key] = true;
                courseId.push(courseDocElement._id);
            }
        }
        const courseObjectIds = await courseScheduleSchema.distinct('_course_id', {
            _institution_id: convertToMongoObjectId(_institution_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            'staffs._staff_id': convertToMongoObjectId(_user_id),
            isDeleted: false,
            isActive: true,
        });
        const courseIds = courseObjectIds.map((course) => course.toString());
        if (!studentWarningData.length) {
            courseId = [...courseId, ...courseObjectIds];
            console.time('warningMailSchema');
            studentWarningData = await warningMailSchema
                .find(
                    {
                        institutionCalendarId: convertToMongoObjectId(_institution_calendar_id),
                        courseId: { $in: courseId },
                        user: { $exists: true, $ne: [] },
                    },
                    {
                        sendDate: 1,
                        courseId: 1,
                        yearNo: 1,
                        levelNo: 1,
                        term: 1,
                        programId: 1,
                        institutionCalendarId: 1,
                        warningId: 1,
                        user: 1,
                        rotationCount: 1,
                        course_name: 1,
                        course_code: 1,
                        program_name: 1,
                    },
                )
                .sort({ sendDate: -1, createdAt: -1 })
                .lean();
            console.timeEnd('warningMailSchema');
        }
        if (!(studentWarningData && studentWarningData.length))
            return { statusCode: 200, message: 'NO_DATA' };
        console.time('studentSettingData');
        const studentSettingData = await lmsStudentSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    classificationType: LEAVE,
                    isActive: true,
                },
                {
                    'warningConfig._id': 1,
                    'warningConfig.labelName': 1,
                    'warningConfig.percentage': 1,
                    'warningConfig.isActive': 1,
                    'warningConfig.message': 1,
                    'warningConfig.colorCode': 1,
                    'warningConfig.notificationToStaff': 1,
                    'warningConfig.isAdditionStaffNotify': 1,
                    'warningConfig.notificationToStudent': 1,
                    'warningConfig.notificationRoleIds': 1,
                    'warningConfig.acknowledgeToStudent': 1,
                    'warningConfig.markItMandatory': 1,

                    // Warning mode Configuration
                    warningMode: 1,
                    warningConfigBased: 1,

                    // Comprehensive Warning Config
                    'comprehensiveWarningConfig._id': 1,
                    'comprehensiveWarningConfig.labelName': 1,
                    'comprehensiveWarningConfig.percentage': 1,
                    'comprehensiveWarningConfig.isActive': 1,
                    'comprehensiveWarningConfig.message': 1,
                    'comprehensiveWarningConfig.colorCode': 1,
                    'comprehensiveWarningConfig.notificationToStaff': 1,
                    'comprehensiveWarningConfig.isAdditionStaffNotify': 1,
                    'comprehensiveWarningConfig.notificationToStudent': 1,
                    'comprehensiveWarningConfig.notificationRoleIds': 1,
                    'comprehensiveWarningConfig.acknowledgeToStudent': 1,
                    'comprehensiveWarningConfig.markItMandatory': 1,
                    'comprehensiveWarningConfig.adminRestrictedAttendance': 1,
                    leaveCalculation: 1,
                },
            )
            .lean();
        console.timeEnd('studentSettingData');
        if (!(studentSettingData.warningConfig && studentSettingData.warningConfig.length)) {
            return { statusCode: 200, message: 'NO_DATA' };
        }
        let warningCount = 0;
        studentWarningData = studentWarningData.flatMap(({ user, ...rest }) =>
            user.map((userItem) => ({ ...rest, user: userItem })),
        );
        const warningMap = new Map();
        for (const warningConfig of studentSettingData.warningMode &&
        studentSettingData.warningMode === WARNING_CONFIG_TYPE.COMPREHENSIVE
            ? studentSettingData.comprehensiveWarningConfig
            : studentSettingData.warningConfig) {
            const key = warningConfig._id.toString();
            if (!warningMap.has(key)) {
                const notificationIdToStudent = warningConfig.notificationToStudent
                    .sendNotificationAuthority
                    ? warningConfig.notificationToStudent.sendNotificationAuthority.length
                        ? warningConfig.notificationToStudent.sendNotificationAuthority.map(
                              (sendNotificationElement) => sendNotificationElement.toString(),
                          )
                        : []
                    : [];
                const notificationRoleId = warningConfig.notificationRoleIds
                    ? warningConfig.notificationRoleIds.length
                        ? warningConfig.notificationRoleIds.map((notifyElement) =>
                              notifyElement.toString(),
                          )
                        : []
                    : [];
                const checkCourseCoOrdinator = notificationIdToStudent.includes(
                    courseCoordinators._id.toString(),
                );
                warningMap.set(key, {
                    _id: key,
                    isActive: warningConfig.isActive,
                    notificationToStaff: warningConfig.notificationToStaff,
                    isAdditionStaffNotify: warningConfig.isAdditionStaffNotify,
                    notificationToStudent: warningConfig.notificationToStudent,
                    notificationRoleIds: warningConfig.notificationRoleIds,
                    labelName: warningConfig.labelName,
                    colorCode: warningConfig.colorCode,
                    acknowledgeToStudent: warningConfig.acknowledgeToStudent,
                    markItMandatory: warningConfig.markItMandatory,
                    adminRestrictedAttendance: warningConfig.adminRestrictedAttendance,
                    notificationIdToStudent,
                    notificationRoleId,
                    checkCourseCoOrdinator,
                });
            }
        }
        const seenCombinations = new Set();
        const filteredStudentWarningData = [];
        let sendAuthority = false;
        console.time('studentWarningData iteration');
        for (const studentWarningDataElement of studentWarningData) {
            studentWarningDataElement.isShownFor = false;
            const warning = warningMap.get(studentWarningDataElement.warningId.toString());
            if (warning && warning.isActive) {
                studentWarningDataElement.programId = {
                    _id: studentWarningDataElement.programId,
                    name: studentWarningDataElement.program_name,
                };
                if (studentWarningDataElement.courseId)
                    studentWarningDataElement.courseId = {
                        _id: studentWarningDataElement.courseId,
                        course_name: studentWarningDataElement.course_name,
                        course_code: studentWarningDataElement.course_code,
                    };
                studentWarningDataElement.user.userId = {
                    _id: studentWarningDataElement.user.userId,
                    name: studentWarningDataElement.user.name,
                    user_id: studentWarningDataElement.user.user_id,
                };
                const notificationToStaff =
                    studentWarningDataElement.user.notificationInfo?.notifyToCourseStaff ||
                    (warning.notificationToStaff
                        ? !!studentWarningDataElement.user.notificationInfo?.isSent
                        : false);
                const notificationIdToStudent = warning.notificationIdToStudent;
                const additionalAuthorityRoles =
                    warning &&
                    warning.isAdditionStaffNotify &&
                    studentWarningDataElement.user &&
                    studentWarningDataElement.user.notificationInfo &&
                    studentWarningDataElement.user.notificationInfo.isSent
                        ? warning.notificationRoleId
                        : [];
                const combinedRoles = [...additionalAuthorityRoles, ...notificationIdToStudent];
                let programIds = [];
                if (roleAssignDocs && roleAssignDocs.roles && roleAssignDocs.roles.length) {
                    programIds = roleAssignDocs.roles.flatMap((roleElement) => {
                        if (combinedRoles.includes(roleElement._role_id.toString())) {
                            return roleElement.program.map((programElement) =>
                                programElement._program_id.toString(),
                            );
                        }
                        return [];
                    });
                }
                const roleExists =
                    combinedRoles.some((roleIdElement) =>
                        roleIds.includes(roleIdElement.toString()),
                    ) && programIds.includes(studentWarningDataElement.programId._id.toString());
                const courseDocsExists = studentWarningDataElement.courseId
                    ? courseDetails.hasOwnProperty(
                          studentWarningDataElement.courseId._id.toString(),
                      )
                    : undefined;
                if (roleExists && warning.isActive) {
                    studentWarningDataElement.isShownFor = true;
                }
                if (combinedRoles.includes(roleId.toString())) {
                    if (warning.isActive && courseDocsExists) {
                        studentWarningDataElement.isShownFor = true;
                    }
                }
                if (notificationToStaff) {
                    if (courseObjectIds.length) {
                        const courseExists = studentWarningDataElement.courseId
                            ? courseIds.includes(studentWarningDataElement.courseId._id.toString())
                            : undefined;
                        if (courseExists) {
                            if (
                                (studentWarningDataElement.user &&
                                    studentWarningDataElement.user.notificationInfo &&
                                    studentWarningDataElement.user.notificationInfo.isSent) ||
                                warning.notificationToStudent.setType === AUTOMATIC
                            ) {
                                studentWarningDataElement.isShownFor = true;
                            }
                        }
                    }
                }
                studentWarningDataElement.warningName =
                    warning.isActive && warning ? warning.labelName : '';
                studentWarningDataElement.colorCode =
                    warning.isActive && warning ? warning.colorCode : '';
                if (warning) {
                    studentWarningDataElement.isNotificationEnabled =
                        warning.notificationToStudent.isActive;
                    studentWarningDataElement.acknowledgement = studentWarningDataElement.user
                        .acknowledge
                        ? 'Acknowledged'
                        : warning.acknowledgeToStudent
                        ? warning.markItMandatory
                            ? 'Not Yet Acknowledged'
                            : 'Pending For Acknowledgement'
                        : 'Not Yet Acknowledged';
                    studentWarningDataElement.sendType = warning.notificationToStudent.setType;
                    if (
                        warning.notificationToStudent.isActive &&
                        warning.notificationToStudent.setType === AUTOMATIC
                    ) {
                        studentWarningDataElement.user.notificationInfo = {
                            isSent: true,
                            sentDate: studentWarningDataElement.sendDate,
                        };
                    }
                    if (
                        warning.notificationToStudent.setType === 'manual' &&
                        warning.notificationToStudent.sendNotificationAuthority
                    ) {
                        if (!studentWarningDataElement.user.notificationInfo) {
                            studentWarningDataElement.user.notificationInfo = {
                                isSent: false,
                            };
                        } else if (
                            studentWarningDataElement?.user?.notificationInfo?.isSent &&
                            !studentWarningDataElement?.user?.notificationInfo?.sentBy
                        ) {
                            studentWarningDataElement.user.notificationInfo = {
                                isSent: true,
                                sentBy: 'automatic',
                            };
                        }
                        const AuthorityRoleIds = notificationIdToStudent;
                        const isRoleAdminExists = AuthorityRoleIds.some((roleIdElement) =>
                            roleAssignIds.includes(roleIdElement.toString()),
                        );

                        if (
                            (warning.isActive && isRoleAdminExists) ||
                            (warning.isActive && warning.checkCourseCoOrdinator && courseDocsExists)
                        ) {
                            studentWarningDataElement.sendAuthority = true;
                        } else {
                            studentWarningDataElement.sendAuthority = false;
                        }
                    } else {
                        studentWarningDataElement.sendAuthority = false;
                    }
                } else {
                    studentWarningDataElement.acknowledgement = studentWarningDataElement.user
                        .acknowledge
                        ? 'Acknowledged'
                        : 'Not Yet Acknowledged';
                    studentWarningDataElement.sendAuthority = false;
                }
                const combination =
                    studentWarningDataElement._id.toString() +
                    studentWarningDataElement.user.userId._id.toString();
                if (!seenCombinations.has(combination) && studentWarningDataElement.isShownFor) {
                    if (!sendAuthority && studentWarningDataElement.sendAuthority === true) {
                        sendAuthority = true;
                    }
                    seenCombinations.add(combination);
                    filteredStudentWarningData.push(studentWarningDataElement);
                    if (
                        viewMode === OVERVIEW &&
                        ((studentWarningDataElement.sendAuthority &&
                            (!studentWarningDataElement.user.notificationInfo ||
                                !studentWarningDataElement.user.notificationInfo.isSent)) ||
                            studentWarningDataElement.sendType === AUTOMATIC)
                    ) {
                        warningCount++;
                    } else if (
                        viewMode === HISTORY &&
                        studentWarningDataElement.user.notificationInfo &&
                        studentWarningDataElement.user.notificationInfo.isSent
                    ) {
                        warningCount++;
                    }
                }
            }
        }
        console.timeEnd('studentWarningData iteration');
        studentWarningData = filteredStudentWarningData;
        console.time('staffAutoWarningOVERVIEW');
        if (viewMode === OVERVIEW) {
            if (studentWarningData.length && sendAuthority) {
                studentWarningData = studentWarningData.filter((studentHistoryElement) => {
                    if (
                        studentHistoryElement &&
                        studentHistoryElement.sendAuthority &&
                        studentHistoryElement.sendType === MANUAL
                    ) {
                        return !studentHistoryElement?.user?.notificationInfo?.isSent;
                    }
                    return true;
                });
            } else if (!sendAuthority) {
                warningCount = studentWarningData.length;
            }
        } else if (viewMode === HISTORY) {
            studentWarningData = studentWarningData.filter(
                (studentHistoryElement) =>
                    studentHistoryElement.user.notificationInfo &&
                    studentHistoryElement.user.notificationInfo.isSent &&
                    studentHistoryElement?.user?.notificationInfo?.sentBy != 'automatic',
            );
            studentWarningData.sort(
                (a, b) =>
                    new Date(b.user.notificationInfo.sentDate) -
                    new Date(a.user.notificationInfo.sentDate),
            );
        }
        console.timeEnd('staffAutoWarningOVERVIEW');
        return {
            statusCode: 200,
            message: 'LIST_DATA',
            data: {
                studentWarningData,
                warningCount,
                isSendAuthority:
                    viewMode === 'overview'
                        ? studentWarningData.length
                            ? sendAuthority
                            : false
                        : true,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sendManualMail = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id, _user_id } = headers;
        const { sendNotification } = body;

        const lmsSettingData = await lmsSettingSchema
            .findOne({
                _institution_id: convertToMongoObjectId(_institution_id),
                classificationType: LEAVE,
            })
            .lean();
        const lmsWarningConfig = lmsSettingData.warningConfig.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.percentage) > parseInt(b.percentage)) {
                comparison = -1;
            } else if (parseInt(a.percentage) < parseInt(b.percentage)) {
                comparison = 1;
            }
            return comparison;
        });
        const warningConfig = await lmsNewSetting({ _institution_id });
        const userData = await userSchema.find(
            {
                _id: {
                    $in: sendNotification.map((notificationElement) =>
                        convertToMongoObjectId(notificationElement.userId),
                    ),
                },
            },
            { email: 1 },
        );
        const staffData = await userSchema.findOne(
            { _id: convertToMongoObjectId(_user_id) },
            { name: 1 },
        );
        const bulkWrites = [];
        let notificationDetails;
        const updateWarningsInRecordArr = [];
        for (const sendNotificationElement of sendNotification) {
            const nameFormat = nameFormatter(sendNotificationElement.userName);
            const courseDetails =
                sendNotificationElement.courseCode +
                '-' +
                sendNotificationElement.courseName +
                '(' +
                sendNotificationElement.levelNo +
                ',' +
                sendNotificationElement.term.charAt(0).toUpperCase() +
                sendNotificationElement.term.slice(1) +
                ')';
            const studentWarningConfigIndex = warningConfig.warningAbsenceData.findIndex(
                (warningElement) =>
                    warningElement._id.toString() === sendNotificationElement.warningId.toString(),
            );
            const createMailContent = createEmailContent();
            let mailContent;
            if (
                warningConfig.warningAbsenceData[
                    studentWarningConfigIndex
                ].labelName.toLocaleLowerCase() === warningConfig.denialLabel.toLocaleLowerCase()
            ) {
                mailContent = createMailContent.denialWarningContent
                    .replace('USERNAME', nameFormat)
                    .replace(
                        'ABSENTPERCENTAGE',
                        warningConfig.warningAbsenceData[studentWarningConfigIndex].percentage,
                    )
                    .replace('COURSEDETAILS', courseDetails);
            } else {
                mailContent = createMailContent.otherWarningContent
                    .replace('USERNAME', nameFormat)
                    .replace(
                        'WARNINGNAME',
                        warningConfig.warningAbsenceData[studentWarningConfigIndex]
                            ? warningConfig.warningAbsenceData[studentWarningConfigIndex].labelName
                            : '',
                    )
                    .replace(
                        'ABSENTPERCENTAGE',
                        warningConfig.warningAbsenceData[studentWarningConfigIndex].percentage,
                    )
                    .replace('COURSEDETAILS', courseDetails);
            }
            const userEmail = userData.find(
                (userDataElement) =>
                    userDataElement._id.toString() === sendNotificationElement.userId.toString(),
            ).email;
            await send_email(userEmail, 'Digi Scheduler Student Warning Notification', mailContent);
            const staffNameFormat = nameFormatter(staffData.name);
            notificationDetails = {
                isSent: true,
                sentDate: new Date(),
                sentBy: staffNameFormat,
                notifyToCourseStaff:
                    warningConfig.warningAbsenceData[studentWarningConfigIndex].notificationToStaff,
            };
            bulkWrites.push({
                updateOne: {
                    filter: {
                        _id: convertToMongoObjectId(sendNotificationElement._id),
                    },
                    update: {
                        $set: {
                            'user.$[userArray].notificationInfo': notificationDetails,
                        },
                    },
                    arrayFilters: [
                        {
                            'userArray.userId': convertToMongoObjectId(
                                sendNotificationElement.userId,
                            ),
                        },
                    ],
                },
            });
            const {
                institutionCalendarId,
                programId,
                courseId,
                yearNo,
                levelNo,
                term,
                rotationCount,
                warningId,
                userId,
            } = sendNotificationElement;
            updateWarningsInRecordArr.push({
                institutionCalendarId,
                programId,
                courseId,
                yearNo,
                levelNo,
                term,
                rotationCount,
                warningId,
                userId,
            });
        }
        await warningMailSchema.bulkWrite(bulkWrites);
        await updateWarningsInRecord(updateWarningsInRecordArr);
        return {
            statusCode: 200,
            message: 'Email Sent Successfully',
            data: notificationDetails.sentDate,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const changeCourseName = async ({ courseId, course_name, course_code }) => {
    try {
        const updateQuery = {};
        if (course_name) {
            updateQuery.course_name = course_name;
        }
        if (course_code) {
            updateQuery.course_code = course_code;
        }
        if (Object.keys(updateQuery).length) {
            await warningMailSchema.updateMany({ courseId }, updateQuery);
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const changeProgramName = async ({ programId, name }) => {
    try {
        const updateQuery = {};
        if (name) {
            updateQuery.program_name = name;
        }
        if (Object.keys(updateQuery).length) {
            await warningMailSchema.updateMany({ programId }, updateQuery);
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const changeUserName = async ({ userId, name }) => {
    try {
        const updateQuery = {};
        if (Object.keys(updateQuery).length) {
            await warningMailSchema.updateMany(
                { userIds: { $in: userId } },
                { 'user.$[i].name': name },
                { arrayFilters: [{ 'i.userId': userId }] },
            );
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLmsWarningConfig = async ({ query = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, courseId, warningConfigBasedOn } = query;

        const filterCriteria = {
            institutionId: convertToMongoObjectId(_institution_id),
            warningConfigBasedOn,
            ...(programId && { programId: convertToMongoObjectId(programId) }),
            ...(courseId && { courseId: convertToMongoObjectId(courseId) }),
        };
        const projectionFields = {
            'warningConfigLevels.typeName': 1,
            'warningConfigLevels.unappliedLeaveConsideredAs': 1,
            'warningConfigLevels.labelName': 1,
            'warningConfigLevels.percentage': 1,
            'warningConfigLevels.colorCode': 1,
            'warningConfigLevels.categoryWisePercentage': 1,
            'warningConfigLevels.notificationToParent': 1,
            'warningConfigLevels.isAdditionStaffNotify': 1,
            'warningConfigLevels.notificationRoleIds': 1,
            'warningConfigLevels.acknowledgeToStudent': 1,
            'warningConfigLevels.typeIndex': 1,
            'warningConfigLevels.notificationToStaff': 1,
            'warningConfigLevels.notificationToStudent': 1,
            'warningConfigLevels.markItMandatory': 1,
            warningConfigBasedOn: 1,
        };
        let warningConfigData = await lmsWarningConfigSchema
            .findOne(filterCriteria, projectionFields)
            .lean();
        if (!warningConfigData) {
            const institutionFilter = {
                institutionId: convertToMongoObjectId(_institution_id),
                warningConfigBasedOn: DS_INSTITUTION_KEY,
            };
            warningConfigData = await lmsWarningConfigSchema
                .findOne(institutionFilter, projectionFields)
                .lean();
            if (!warningConfigData) {
                await lmsWarningConfigSchema.create(institutionFilter);
                return { statusCode: 200, message: 'DATA_RETRIEVED', data: {} };
            }
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: warningConfigData };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

const createLmsWarningConfig = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { warningConfigBasedOn, programId, courseId, warningConfigLevels } = body;
        const newWarningConfig = await lmsWarningConfigSchema.findOneAndUpdate(
            {
                institutionId: convertToMongoObjectId(_institution_id),
                warningConfigBasedOn,
                ...(programId && { programId: convertToMongoObjectId(programId) }),
                ...(courseId && { courseId: convertToMongoObjectId(courseId) }),
            },
            {
                $set: {
                    warningConfigBasedOn,
                    warningConfigLevels,
                    ...(programId && { programId }),
                    ...(courseId && { courseId }),
                },
            },
            { upsert: true, new: true },
        );
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: newWarningConfig };
    } catch (error) {
        throw error instanceof Error ? error : new Error(error);
    }
};

module.exports = {
    getCourseWarning,
    studentAcknowledge,
    staffAutoWarning,
    sendManualMail,
    changeCourseName,
    changeProgramName,
    changeUserName,
    getCourseWarningForCalendar,
    getLmsWarningConfig,
    createLmsWarningConfig,
};
