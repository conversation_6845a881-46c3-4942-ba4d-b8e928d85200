const express = require("express");
const route = express.Router();
const course_management_setting = require('./course_management_setting_controller');
const validator = require('./course_management_setting_validator');

route.get('/:_program_id', validator.program_id, course_management_setting.course_management_setting_session_list);
route.post('/', validator.course_management_setting, course_management_setting.insert);
route.put('/:_id', validator.course_management_setting_id, validator.course_management_setting,  course_management_setting.update);
route.delete('/:_id', validator.course_management_setting_id, course_management_setting.delete);

module.exports = route;