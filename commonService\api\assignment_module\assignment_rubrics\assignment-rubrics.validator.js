const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    stringRQSchema,
    numberSchema,
    stringSchema,
    booleanSchema,
} = require('../../../utility/validationSchemas');

exports.createRubricsValidator = Joi.object({
    courseId: objectIdSchema,
    programId: objectIdSchema,
    rubricScope: stringSchema,
    code: stringRQSchema,
    name: stringRQSchema,
    abbreviation: stringSchema,
    description: Joi.object({
        value: stringSchema,
        attachments: Joi.array().items(
            Joi.object({
                url: stringSchema,
                signedUrl: stringSchema,
                name: stringSchema,
                sizeInKb: numberSchema,
            }).unknown(true),
        ),
    }).unknown(true),
    evaluateInSequence: booleanSchema,
    isPublic: booleanSchema,
    scoreType: stringSchema,
    rubrics: Joi.array().items(
        Joi.object({
            rubricsType: stringSchema,
            applyWeightAgeTo: stringSchema,
            criteria: Joi.object({
                name: stringSchema,
                totalPercent: numberSchema,
                outcome: Joi.array().items(
                    Joi.object({ id: objectIdSchema, name: stringSchema }).unknown(true),
                ),
            }).unknown(true),
            dimensional: Joi.array().items(
                Joi.object({
                    name: stringSchema,
                    description: stringSchema,
                    feedback: stringSchema,
                    min: numberSchema,
                    max: numberSchema,
                }).unknown(true),
            ),
        }).unknown(true),
    ),
    duplicateFromTheRubrics: objectIdSchema,
}).unknown(true);

exports.paramIDValidator = Joi.object({
    id: objectIdRQSchema,
});
