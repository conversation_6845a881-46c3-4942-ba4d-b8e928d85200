const Notification = require('../../models/notification');
const WarningMail = require('../../models/warning_mail');
const CourseSchedule = require('../../models/course_schedule');
const DigiLabel = require('../../models/digi_labels');
const Program = require('../../models/digi_programs');
const lmsCollection = require('../../models/lms');
const { get } = require('../../base/base_controller');
const {
    insert,
    dsCustomUpdate,
    dsDeleteOne,
    dsGetCount,
    dsGetAllWithSortAsJSON,
} = require('../../base/base_controller');
const getJSON = dsGetAllWithSortAsJSON;
const {
    convertToMongoObjectId,
    sendResponse,
    sendErrorResponse,
    cs,
    convertToUtcFormat,
    sendResponseWithRequest,
} = require('../../utility/common');
const convertToString = cs;
const { timeAgo, send_email } = require('../../utility/common_functions');
// constants
const {
    DS_ADDED,
    DS_ADD_FAILED,
    DS_DATA_RETRIEVED,
    DS_UPDATED,
    DS_DELETED,
    DS_UPDATE_FAILED,
    INVALID_ID,
    COLLEGE_PROGRAM_TYPE,
} = require('../../utility/constants');
const {
    warningMailForStudent,
    warningMailForStaff,
    warningMailForStaffWithData,
} = require('./notification_service');
const { getSelectedProgramUserDetails } = require('../digiSurveyBank/digiSurveyBank.service');
// get all Notification
exports.getAllNotification = async (req, res) => {
    try {
        const { userId } = req.params;
        const { limit, page } = req.query;
        const startCurrentTime = new Date();
        startCurrentTime.setHours(0);
        startCurrentTime.setMinutes(0);
        startCurrentTime.setSeconds(0);
        startCurrentTime.setMilliseconds(0);
        const endCurrentTime = new Date();
        endCurrentTime.setHours(23);
        endCurrentTime.setMinutes(59);
        endCurrentTime.setSeconds(0);
        endCurrentTime.setMilliseconds(0);
        const search = {
            users: { $elemMatch: { _id: convertToMongoObjectId(userId) } },
            isDeleted: false,
            $and: [
                { createdAt: { $gt: startCurrentTime } },
                { createdAt: { $lt: endCurrentTime } },
            ],
        };
        const docCount = await dsGetCount(Notification, search);
        if (!docCount) return sendResponseWithRequest(req, res, 200, false, INVALID_ID, []);
        // const perPage = parseInt(limit > 0 ? limit : 10);
        // const pageNo = parseInt(page > 0 ? page : 1);
        let notifications = await Notification.find(search).sort({ _id: -1 }).exec();
        const scheduleIds = notifications
            .filter((notification) => notification.scheduleId !== undefined)
            .map((notification) => convertToMongoObjectId(notification.scheduleId));

        scheduleQuery = { _id: { $in: scheduleIds }, isActive: true, isDeleted: false };
        scheduleProject = { _id: 1, start: 1, end: 1 };
        const schedules = (await getJSON(CourseSchedule, scheduleQuery, scheduleProject)).data;
        const data = [];
        const programIds = notifications.map((notification) => notification.programId);
        const programs = await Program.find(
            { _id: { $in: programIds } },
            { college_program_type: 1 },
        ).lean();
        const hasDiscussion = notifications.some(
            (notification) => notification.notificationType === 'discussion',
        );
        const hasAssignment = notifications.some(
            (notification) => notification.notificationType === 'assignment',
        );
        const hasActivity = notifications.some((notification) => notification.type === 'activity');
        const labels = await DigiLabel.find().lean();
        notifications = notifications.forEach((notification) => {
            const session = schedules.find(
                (schedule) =>
                    schedule._id &&
                    notification.scheduleId &&
                    convertToString(schedule._id) === convertToString(notification.scheduleId),
            );

            if (!session && !hasDiscussion && !hasActivity && !hasAssignment) return;
            const program = programs.filter(
                (programEntry) => programEntry._id.toString() === notification.programId.toString(),
            )[0];
            const programType = program.college_program_type
                ? program.college_program_type
                : COLLEGE_PROGRAM_TYPE.MEDICAL;
            const label = labels.filter(
                (labelEntry) => labelEntry.college_program_type === programType,
            )[0];
            data.push({
                _id: notification._id,
                title: notification.title,
                description:
                    programType === COLLEGE_PROGRAM_TYPE.MEDICAL
                        ? notification.description
                        : notification.description.replace('Level', label.level),
                buttonName: notification.buttonName,
                buttonAction: notification.buttonAction,
                timeAgo: timeAgo(notification.createdAt),
                read: !!notification.users.find((user) => user.isViewed),
                courseId: notification.courseId,
                sessionId: notification.sessionId,
                scheduleId: notification.scheduleId,
                notificationType: notification.notificationType,
                notificationPeriod: notification.notificationPeriod,
                institutionCalendarId: notification.institutionCalendarId,
                programId: notification.programId,
                yearNo: notification.yearNo,
                levelNo: notification.levelNo,
                term: notification.term,
                mergeStatus: notification.mergeStatus,
                sessionType: notification.mergeType,
                type: notification.type ? notification.type : notification.mergeType,
                session,
                rotation: notification.rotation,
                rotation_count: notification.rotation_count,
                staffStartWithExam: notification.staffStartWithExam,
                activityId: notification.activityId,
                discussionId: notification.discussionId,
                channelId: notification.channelId,
                adminCourse: notification.adminCourse,
                groupName: notification.groupName,
                courseName: notification.courseName,
                courseCode: notification.courseCode,
                programName: notification.programName,
                ClickAction: notification.ClickAction,
                assignmentId: notification.assignmentId,
                assignmentData: notification.assignmentData,
                studentId: userId,
                isNewActivity: notification?.isNewActivity,
                correctionType: notification?.correctionType,
            });
        });
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_DATA_RETRIEVED), data);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('SERVER_ERROR'),
            error.toString(),
        );
    }
};

// create Notification
exports.createNotification = async (req, res) => {
    try {
        const {
            title,
            description,
            buttonName,
            buttonAction,
            scheduleId,
            sessionId,
            courseId,
            notificationType,
        } = req.body;

        const csQuery = {
            _id: convertToMongoObjectId(scheduleId),
            isDeleted: false,
            isActive: true,
        };
        const courseSchedules = (await getJSON(CourseSchedule, csQuery, {})).data;
        if (!courseSchedules.length) sendResponse(res, 200, false, req.t('SCHEDULE_NOT_FOUND'));
        const {
            year_no,
            level_no,
            type,
            _program_id,
            _institution_calendar_id,
            merge_status,
            students,
        } = courseSchedules[0];
        const program = await Program.findById(_program_id, { college_program_type: 1 }).lean();
        const programType = program.college_program_type
            ? program.college_program_type
            : COLLEGE_PROGRAM_TYPE.MEDICAL;
        const label = await DigiLabel.findOne(
            {
                college_program_type: programType,
            },
            { term: 1, level: 1 },
        );
        const document = {
            students: students.map((student) => {
                return { _id: student._id };
            }),
            title,
            description,
            buttonName,
            buttonAction,
            scheduleId,
            sessionId,
            courseId,
            mergeStatus: merge_status,
            mergeType: type,
            programId: _program_id,
            institutionCalendarId: _institution_calendar_id,
            yearNo: year_no,
            levelNo: level_no,
        };
        if (notificationType) document.notificationType = notificationType;
        const { status } = await insert(Notification, document);
        if (!status) sendResponse(res, 200, false, req.t(DS_ADD_FAILED));
        return sendResponse(res, 200, true, req.t(DS_ADDED));
    } catch (error) {
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

// update Notification
exports.updateNotification = async (req, res) => {
    try {
        const { id: userId } = req.params;
        const { read } = req.body;
        const updateQuery = {
            isDeleted: false,
            users: { $elemMatch: { _id: convertToMongoObjectId(userId), isViewed: false } },
        };
        const data = { $set: { 'users.$.isViewed': read } };
        const success = await Notification.updateMany(updateQuery, data);
        if (!success) return sendResponseWithRequest(req, res, 200, false, req.t(DS_UPDATE_FAILED));
        return sendResponseWithRequest(req, res, 200, true, req.t(DS_UPDATED));
    } catch (error) {
        return sendErrorResponse(res, 500, false, 'Server Error', error.toString());
    }
};

exports.deleteNotification = async (req, res) => {
    try {
        const { id } = req.params;
        const docCount = await dsGetCount(Notification, { _id: id });
        if (docCount === 0) return sendResponse(res, 200, false, INVALID_ID);
        const { success, message } = await dsDeleteOne(Notification, id);
        if (!success) return sendResponse(res, 200, false, message);
        return sendResponse(res, 200, true, req.t(DS_DELETED));
    } catch (error) {
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

const sendMailWithUser = async (sendMails, warningMails) => {
    const warningWithStudents = [];
    const warningInsertStudents = [];
    let studentCourseDetails;
    if (sendMails.length) {
        for (const mail of sendMails) {
            const { courseDetail } = mail;
            warningWithStudents.push({
                warningId: mail.warningId,
                userId: mail.userId,
                courseDetail,
            });
            const mailStatus = await send_email(mail.email, 'Warning Alert', mail.content);
        }
        if (warningWithStudents.length) {
            studentCourseDetails = warningWithStudents.map((warningWithStudent) => {
                return {
                    ...warningWithStudent.courseDetail,
                    warningId: warningWithStudent.warningId,
                };
            });
            studentCourseDetails = studentCourseDetails.reduce((acc, current) => {
                const x = acc.find((item) =>
                    !item.rotationCount && !current.rotationCount
                        ? item.courseId.toString() === current.courseId.toString() &&
                          item.programId.toString() === current.programId.toString() &&
                          item.institutionCalendarId.toString() ===
                              current.institutionCalendarId.toString() &&
                          item.term.toString() === current.term.toString() &&
                          item.levelNo === current.levelNo &&
                          item.yearNo === current.yearNo &&
                          item.warningId.toString() === current.warningId.toString()
                        : item.courseId.toString() === current.courseId.toString() &&
                          item.programId.toString() === current.programId.toString() &&
                          item.institutionCalendarId.toString() ===
                              current.institutionCalendarId.toString() &&
                          item.term.toString() === current.term.toString() &&
                          item.yearNo === current.yearNo &&
                          item.rotationCount.toString() === current.rotationCount.toString() &&
                          item.levelNo === current.levelNo &&
                          item.warningId.toString() === current.warningId.toString(),
                );
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
            studentCourseDetails.forEach((studentCourseDetail) => {
                const userIds = warningWithStudents.filter((warningWithStudent) =>
                    studentCourseDetail.rotationCount
                        ? warningWithStudent.warningId.toString() ===
                              studentCourseDetail.warningId.toString() &&
                          warningWithStudent.courseDetail.courseId.toString() ===
                              studentCourseDetail.courseId.toString() &&
                          warningWithStudent.courseDetail.yearNo === studentCourseDetail.yearNo &&
                          warningWithStudent.courseDetail.levelNo === studentCourseDetail.levelNo &&
                          warningWithStudent.courseDetail.term === studentCourseDetail.term &&
                          warningWithStudent.courseDetail.programId.toString() ===
                              studentCourseDetail.programId.toString() &&
                          warningWithStudent.courseDetail.rotationCount.toString() ===
                              studentCourseDetail.rotationCount.toString() &&
                          warningWithStudent.courseDetail.institutionCalendarId.toString() ===
                              studentCourseDetail.institutionCalendarId.toString()
                        : warningWithStudent.warningId.toString() ===
                              studentCourseDetail.warningId.toString() &&
                          warningWithStudent.courseDetail.courseId.toString() ===
                              studentCourseDetail.courseId.toString() &&
                          warningWithStudent.courseDetail.yearNo === studentCourseDetail.yearNo &&
                          warningWithStudent.courseDetail.levelNo === studentCourseDetail.levelNo &&
                          warningWithStudent.courseDetail.term === studentCourseDetail.term &&
                          warningWithStudent.courseDetail.programId.toString() ===
                              studentCourseDetail.programId.toString() &&
                          warningWithStudent.courseDetail.institutionCalendarId.toString() ===
                              studentCourseDetail.institutionCalendarId.toString(),
                );
                if (userIds.length) {
                    const rotationCount = studentCourseDetail.rotationCount
                        ? studentCourseDetail.rotationCount
                        : undefined;

                    const courseDetail = {
                        courseId: studentCourseDetail.courseId,
                        yearNo: studentCourseDetail.yearNo,
                        levelNo: studentCourseDetail.levelNo,
                        term: studentCourseDetail.term,
                        programId: studentCourseDetail.programId,
                        institutionCalendarId: studentCourseDetail.institutionCalendarId,
                    };
                    if (rotationCount) courseDetail.rotationCount = rotationCount;
                    warningInsertStudents.push({
                        warningId: studentCourseDetail.warningId,
                        userIds: userIds.map((userId) => userId.userId),
                        courseDetail,
                    });
                }
            });
        }
    }
    if (warningInsertStudents.length) {
        const bulkWrites = [];
        // if (warningMails.length) {
        warningInsertStudents.forEach((warningInsertStudent) => {
            const { warningId, courseDetail } = warningInsertStudent;
            const warningIdExist = warningMails.find((warningMail) =>
                courseDetail.rotationCount
                    ? convertToUtcFormat(new Date()).toString() ===
                          convertToUtcFormat(warningMail.sendDate).toString() &&
                      warningMail.warningId.toString() === warningId.toString() &&
                      warningMail.courseId.toString() === courseDetail.courseId.toString() &&
                      warningMail.yearNo === courseDetail.yearNo &&
                      warningMail.levelNo === courseDetail.levelNo &&
                      warningMail.term === courseDetail.term &&
                      warningMail.programId.toString() === courseDetail.programId.toString() &&
                      warningMail.institutionCalendarId.toString() ===
                          courseDetail.institutionCalendarId.toString() &&
                      warningMail.rotationCount.toString() === courseDetail.rotationCount.toString()
                    : convertToUtcFormat(new Date()).toString() ===
                          convertToUtcFormat(warningMail.sendDate).toString() &&
                      warningMail.warningId.toString() === warningId.toString() &&
                      warningMail.courseId.toString() === courseDetail.courseId.toString() &&
                      warningMail.yearNo === courseDetail.yearNo &&
                      warningMail.levelNo === courseDetail.levelNo &&
                      warningMail.term === courseDetail.term &&
                      warningMail.programId.toString() === courseDetail.programId.toString() &&
                      warningMail.institutionCalendarId.toString() ===
                          courseDetail.institutionCalendarId.toString(),
            );
            if (warningIdExist) {
                const { userIds } = warningIdExist;
                let mergedUserIds = userIds.concat(warningInsertStudent.userIds);
                mergedUserIds = mergedUserIds.map((mergedUserId) => mergedUserId.toString());
                mergedUserIds = [...new Set(mergedUserIds)];
                bulkWrites.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(warningIdExist._id),
                        },
                        update: { $set: { userIds: mergedUserIds } },
                    },
                });
            } else {
                const data = {
                    sendDate: convertToUtcFormat(new Date()),
                    courseId: courseDetail.courseId,
                    yearNo: courseDetail.yearNo,
                    levelNo: courseDetail.levelNo,
                    term: courseDetail.term,
                    programId: courseDetail.programId,
                    institutionCalendarId: courseDetail.institutionCalendarId,
                    warningId: warningInsertStudent.warningId,
                    userIds: warningInsertStudent.userIds,
                };
                if (courseDetail.rotationCount) {
                    data.rotationCount = courseDetail.rotationCount;
                }
                bulkWrites.push({
                    insertOne: {
                        document: data,
                    },
                });
            }
        });
        if (bulkWrites.length) {
            await WarningMail.bulkWrite(bulkWrites);
        }
    }
};

const sendMailWithStaffUser = async (staffSendMails, staffCourses, warningMails) => {
    if (staffSendMails.length) {
        for (const mail of staffSendMails) {
            const mailStatus = await send_email(mail.email, 'Warning Alert', mail.content);
        }
    }
    const bulkWrites = [];
    if (staffCourses.length) {
        for (const staffCourse of staffCourses) {
            const {
                courseId,
                institutionCalendarId,
                programId,
                term,
                yearNo,
                levelNo,
                warningId,
                students,
                staffId,
                rotationCount,
            } = staffCourse;
            const warningMail = warningMails.find((warningMail) =>
                rotationCount
                    ? convertToUtcFormat(new Date()).toString() ===
                          convertToUtcFormat(warningMail.sendDate).toString() &&
                      warningMail.warningId.toString() === warningId.toString() &&
                      warningMail.courseId.toString() === courseId.toString() &&
                      warningMail.institutionCalendarId.toString() ===
                          institutionCalendarId.toString() &&
                      warningMail.programId.toString() === programId.toString() &&
                      warningMail.term === term &&
                      warningMail.yearNo === yearNo &&
                      warningMail.levelNo === levelNo &&
                      warningMail.rotationCount.toString() === rotationCount.toString()
                    : convertToUtcFormat(new Date()).toString() ===
                          convertToUtcFormat(warningMail.sendDate).toString() &&
                      warningMail.warningId.toString() === warningId.toString() &&
                      warningMail.courseId.toString() === courseId.toString() &&
                      warningMail.institutionCalendarId.toString() ===
                          institutionCalendarId.toString() &&
                      warningMail.programId.toString() === programId.toString() &&
                      warningMail.term === term &&
                      warningMail.yearNo === yearNo &&
                      warningMail.levelNo === levelNo,
            );
            if (warningMail) {
                if (
                    warningMail.staffs.length &&
                    warningMail.staffs.find((staff) => staff._id.toString() === staffId.toString())
                ) {
                    bulkWrites.push({
                        updateOne: {
                            filter: { _id: convertToMongoObjectId(warningMail._id) },
                            update: { $push: { 'staffs.$[i].sendStudents': { $each: students } } },
                            arrayFilters: [
                                {
                                    'i._id': convertToMongoObjectId(staffId),
                                },
                            ],
                        },
                    });
                } else {
                    bulkWrites.push({
                        updateOne: {
                            filter: { _id: convertToMongoObjectId(warningMail._id) },
                            update: {
                                $push: {
                                    staffs: {
                                        _id: staffId,
                                        sendStudents: students,
                                        sendDate: convertToUtcFormat(new Date()),
                                    },
                                },
                            },
                        },
                    });
                }
            }
        }
    }
    if (bulkWrites.length) {
        const update = await WarningMail.bulkWrite(bulkWrites);
        return update;
    }
    return false;
};

exports.warningMailForStudent = async (req, res) => {
    try {
        // LMS Check
        const lms = await get(
            lmsCollection,
            { isDeleted: false, 'student_warning_absence_calculation.isDeleted': false },
            { _institution_id: 1, student_warning_absence_calculation: 1 },
        );
        if (!lms.status) return sendResponse(res, 200, true, DS_UPDATED, []);
        const { sendMail: sendMails, warningMails } = await warningMailForStudent();
        // return res.send({ sendMails, warningMails });
        await sendMailWithUser(sendMails, warningMails);
        return sendResponse(res, 200, true, req.t(DS_UPDATED), sendMails);
    } catch (error) {
        console.log(error);
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.warningMailForStaff = async (req, res) => {
    try {
        // LMS Check
        const lms = await get(
            lmsCollection,
            { isDeleted: false, 'student_warning_absence_calculation.isDeleted': false },
            { _institution_id: 1, student_warning_absence_calculation: 1 },
        );
        if (!lms.status) return sendResponse(res, 200, true, DS_UPDATED, []);
        const { status, staffSendMails, staffCourses, warningMails } =
            await warningMailForStaffWithData();
        // return res.send({ staffSendMails, staffCourses, warningMails });
        if (status !== undefined && status === false)
            return sendResponse(res, 200, true, req.t(DS_UPDATED), []);
        await sendMailWithStaffUser(staffSendMails, staffCourses, warningMails);
        return sendResponse(res, 200, true, req.t(DS_UPDATED), staffSendMails);
    } catch (error) {
        console.log('error', error);
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
exports.getProgramUserDetails = async ({ body = {} }) => {
    try {
        const { selectedProgram } = body;
        const selectedProgramUserDetails = await getSelectedProgramUserDetails({
            programDetails: selectedProgram,
        });
        return {
            statusCode: 200,
            message: 'SELECTED_PROGRAM_USERS',
            data: selectedProgramUserDetails,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
