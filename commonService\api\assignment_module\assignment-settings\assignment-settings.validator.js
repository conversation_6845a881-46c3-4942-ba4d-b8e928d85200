const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    numberSchema,
    stringSchema,
    booleanSchema,
} = require('../../../utility/validationSchemas');

exports.createProgramSettingValidator = Joi.object({
    programId: objectIdRQSchema,
    gradingSystem: Joi.object({
        points: Joi.object({
            isActive: booleanSchema,
            scalePoints: numberSchema,
        }).unknown(true),
        letterGrade: Joi.object({
            isActive: booleanSchema,
            gradingMethod: Joi.array().items(
                Joi.object({
                    grade: stringSchema,
                    description: stringSchema,
                    percentage: Joi.object({
                        from: numberSchema,
                        to: numberSchema,
                    }).unknown(true),
                }).unknown(true),
            ),
        }).unknown(true),
    }).unknown(true),
    gamification: Joi.object({
        isActive: booleanSchema,
        levels: Joi.array().items(
            Joi.object({
                index: numberSchema,
                title: stringSchema,
                creditsRequired: numberSchema,
            }).unknown(true),
        ),
        creditPointsFor: Joi.object({
            submissionAssignmentCount: numberSchema,
            submissionOnTime: Joi.object({
                isActive: booleanSchema,
                count: numberSchema,
            }).unknown(true),
            targetBenchmark: Joi.object({
                isActive: booleanSchema,
                count: numberSchema,
            }).unknown(true),
            scoring: Joi.object({
                isActive: booleanSchema,
                scores: Joi.array().items(
                    Joi.object({
                        startPercentage: numberSchema,
                        endPercentage: numberSchema,
                        creditPoints: numberSchema,
                    }).unknown(true),
                ),
            }).unknown(true),
            leaderBoard: booleanSchema,
            badges: Joi.object({
                isActive: booleanSchema,
                badge: Joi.array().items(
                    Joi.object({
                        title: stringSchema,
                        description: stringSchema,
                        time: numberSchema,
                        badgeIcon: stringSchema,
                    }).unknown(true),
                ),
            }).unknown(true),
        }).unknown(true),
    }).unknown(true),
}).unknown(true);

exports.getProgramSettingValidator = Joi.object({ programId: objectIdRQSchema }).unknown(true);

const courseGeneralAssignmentTypeSchema = Joi.object({
    isActive: booleanSchema,
    assignmentLimit: numberSchema,
    totalAssignmentMarks: Joi.object({
        isActive: booleanSchema,
        value: numberSchema,
    }).unknown(true),
    copyAssignmentFromPrevious: Joi.object({
        isCopy: booleanSchema,
        year: stringSchema,
        level: stringSchema,
        term: stringSchema,
        _institution_calendar_id: objectIdSchema,
        _program_id: objectIdSchema,
        _course_id: objectIdSchema,
    }).unknown(true),
    export: Joi.object({
        allowSubmittedAssignmentsExport: booleanSchema,
        allowDiscussionExport: booleanSchema,
        allowCompletedReportsAndAnalyticsExport: booleanSchema,
        allowStudentCompletedAssignmentExport: booleanSchema,
    }).unknown(true),
}).unknown(true);

exports.createCourseSettingValidator = Joi.object({
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    general: Joi.object({
        groupLeaderCount: numberSchema,
        allowMultiSubjectAssignment: booleanSchema,
        assignmentGuideLines: Joi.object({
            isActive: booleanSchema,
            info: Joi.object({
                title: stringSchema,
                description: stringSchema,
                isDraft: booleanSchema,
                urls: Joi.array().items(
                    Joi.object({
                        url: stringSchema,
                        name: stringSchema,
                        sizeInKb: numberSchema,
                    }).unknown(true),
                ),
            }).unknown(true),
        }).unknown(true),
        allowCustomAssignmentFormats: Joi.object({
            isActive: booleanSchema,
            isAdminOnly: booleanSchema,
        }).unknown(true),
        summative: courseGeneralAssignmentTypeSchema,
        formative: courseGeneralAssignmentTypeSchema,
    }).unknown(true),
}).unknown(true);

exports.getCourseSettingValidator = Joi.object({
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
}).unknown(true);

const subjectGeneralAssignmentTypeSchema = Joi.object({
    isActive: booleanSchema,
    assignmentLimit: numberSchema,
    totalAssignmentMarks: Joi.object({
        isActive: booleanSchema,
        value: numberSchema,
    }).unknown(true),
    export: Joi.object({
        allowSubmittedAssignmentsExport: booleanSchema,
        allowDiscussionExport: booleanSchema,
        allowCompletedReportsAndAnalyticsExport: booleanSchema,
        allowStudentCompletedAssignmentExport: booleanSchema,
    }).unknown(true),
}).unknown(true);

exports.createSubjectSettingValidator = Joi.object({
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    subjectId: objectIdRQSchema,
    departmentId: objectIdRQSchema,
    general: Joi.object({
        assignmentGuideLines: Joi.object({
            isActive: booleanSchema,
            info: Joi.object({
                title: stringSchema,
                description: stringSchema,
                isDraft: booleanSchema,
                urls: Joi.array().items(
                    Joi.object({
                        url: stringSchema,
                        name: stringSchema,
                        sizeInKb: numberSchema,
                    }).unknown(true),
                ),
            }).unknown(true),
        }).unknown(true),
        summative: subjectGeneralAssignmentTypeSchema,
        formative: subjectGeneralAssignmentTypeSchema,
    }).unknown(true),
}).unknown(true);

exports.getSubjectSettingValidator = Joi.object({
    programId: objectIdRQSchema,
    courseId: objectIdRQSchema,
    departmentId: objectIdRQSchema,
    subjectId: objectIdRQSchema,
}).unknown(true);
