const Joi = require('joi');
const { MulterError } = require('multer');
const { qapcMultipleFileUpload } = require('../../utility/file_upload');
const objectId = Joi.string().alphanum().length(24).required();
const {
    COURSE_TYPE: { STANDARD, SELECTIVE },
    FORM,
    TEMPLATE,
    COMPLETE,
    SECTION,
    TAG_LEVEL,
    PUBLISHED,
    INACTIVE,
    DRAFT,
    SPECIFIC,
    ENTIRE,
    ALL,
    EVERY,
    INDIVIDUAL,
} = require('../../utility/constants');

exports.qapcGuideResourcesValidator = (req, res, next) => {
    qapcMultipleFileUpload(req, res, (err) => {
        if (err instanceof MulterError) {
            return res.status(500).send(`Multer Error in uploading,${err.toString()}`);
        }
        if (err) {
            return res.status(500).send('Please change file format and upload');
        }
        next();
    });
};
exports.programDetailsValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                programId: Joi.array()
                    .items(Joi.string().alphanum().length(24))
                    .optional()
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getCurriculumValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                programId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.createDuplicateFormValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
                categoryFormType: Joi.string()
                    .valid(FORM, TEMPLATE)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_TYPE_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:FORM, TEMPLATE';
                    }),
                templateId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'TEMPLATED_ID_REQUIRED';
                    }),
                formName: Joi.string()
                    .optional()
                    .error(() => {
                        return 'FORM_NAME_MUST_BE_STRING';
                    }),
                describe: Joi.string()
                    .optional()
                    .allow('')
                    .error(() => {
                        return 'DESCRIBE_MUST_BE_STRING';
                    }),
                selectCourses: Joi.array()
                    .items({
                        assignedInstitutionId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'ASSIGN_INSTITUTION_IDMUST_BE_OBJECTID';
                            }),
                        institutionName: Joi.string()
                            .optional()
                            .error(() => {
                                return 'INSTITUTION_NAME_MUST_BE_STRING';
                            }),
                        programId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'PROGRAM_ID_REQUIRED';
                            }),
                        programName: Joi.string()
                            .optional()
                            .error(() => {
                                return 'PROGRAM_NAME_MUST_BE_STRING';
                            }),
                        curriculumId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'CURRICULUM_ID_REQUIRED';
                            }),
                        curriculumName: Joi.string()
                            .optional()
                            .error(() => {
                                return 'CURRICULUM_NAME_MUST_BE_STRING';
                            }),
                        year: Joi.string()
                            .optional()
                            .error(() => {
                                return 'YEAR_NAME_MUST_BE_STRING';
                            }),
                        courseId: Joi.string()
                            .alphanum()
                            .length(24)
                            .optional()
                            .error(() => {
                                return 'COURSE_ID_REQUIRED';
                            }),
                        courseName: Joi.string()
                            .optional()
                            .error(() => {
                                return 'COURSE_NAME_MUST_BE_STRING';
                            }),
                        courseCode: Joi.string()
                            .optional()
                            .error(() => {
                                return 'COURSE_CODE_MUST_BE_STRING';
                            }),
                        sharedWithOthers: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        sharedFormOthers: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        courseType: Joi.string()
                            .valid(STANDARD, SELECTIVE)
                            .optional()
                            .error(() => {
                                return 'CATEGORY_FORM_TYPE_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:STANDARD, SELECTIVE';
                            }),
                    })
                    .optional(),
                selectedProgram: Joi.array()
                    .items(
                        Joi.object().keys({
                            programId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'PEOGRAM_ID_REQUIRED';
                                }),
                            programName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'PROGRAM_NAME_MUST_BE_STRING';
                                }),
                            all: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                        }),
                    )
                    .optional(),
                selectedInstitution: Joi.array()
                    .items(
                        Joi.object().keys({
                            assignedInstitutionId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ASSIGN_INSTITUTION_IDMUST_BE_OBJECTID';
                                }),
                            institutionName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'INSTITUTION_NAME_MUST_BE_STRING';
                                }),
                        }),
                    )
                    .optional(),
                formType: Joi.string()
                    .valid(COMPLETE, SECTION)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_TYPE_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:COMPLETE, SECTION';
                    }),
                incorporateMandatory: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                categorySettings: Joi.object()
                    .keys({
                        level: Joi.string()
                            .valid(TAG_LEVEL.PROGRAM, TAG_LEVEL.COURSE, TAG_LEVEL.INSTITUTION)
                            .optional()
                            .error(() => {
                                return 'LEVEL_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:PROGRAM,COURSE,INSTITUTION';
                            }),
                    })
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getCategoryFormValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
                searchKey: Joi.string().optional().allow(''),
                pageNo: Joi.number().optional(),
                limit: Joi.number().optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.categoryTypeFormValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                formType: Joi.string()
                    .required()
                    .error(() => {
                        return 'FORM_TYPE_MUST_BE_STRING';
                    }),
                searchKey: Joi.string().optional().allow(''),
                pageNo: Joi.number().optional(),
                limit: Joi.number().optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getFormAttachmentValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.updateCategoryFormValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
                categoryFormType: Joi.string()
                    .valid(FORM, TEMPLATE)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_TYPE_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:FORM, TEMPLATE';
                    }),
                templateId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'TEMPLATR_ID_REQUIRED';
                    }),
                formName: Joi.string()
                    .optional()
                    .error(() => {
                        return 'FORM_NAME_MUST_BE_STRING';
                    }),
                describe: Joi.string()
                    .optional()
                    .allow('')
                    .error(() => {
                        return 'DESCRIBE_MUST_BE_STRING';
                    }),
                status: Joi.string()
                    .valid(PUBLISHED, INACTIVE, DRAFT)
                    .optional()
                    .error(() => {
                        return 'STATUS_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:PUBLISHED, INACTIVE, DRAFT';
                    }),
                attachments: Joi.array()
                    .items(
                        Joi.object()
                            .keys({
                                url: Joi.string()
                                    .optional()
                                    .error(() => {
                                        return 'URL_MUST_BE_STRING';
                                    }),
                                name: Joi.string()
                                    .optional()
                                    .error(() => {
                                        return 'NAME_MUST_BE_STRING';
                                    }),
                            })
                            .optional(),
                    )
                    .optional(),
                approvalLevel: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            name: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'NAME_MUST_BE_STRING';
                                }),
                            approvalStatus: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'APPROVAL_STATUS_MUST_BE_STRING';
                                }),
                            requireAll: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            requireMinium: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            levelNumber: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'LEVEL_NUMBER_MUST_BE_NUMBER';
                                }),
                            minimum_user: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'MINIUM_USER_MUST_BE_NUMBER';
                                }),
                            turnAroundTime: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'TURN_AROUND_TIME_MUST_BE_NUMBER';
                                }),
                            escalateRequest: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            allowToSkipping: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            allowToOverwrite: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            category: Joi.string()
                                .valid(ENTIRE, SPECIFIC)
                                .optional()
                                .error(() => {
                                    return 'CATEGORY_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ENTIRE, SPECIFIC';
                                }),
                            specificSections: Joi.array()
                                .items(Joi.string().optional())
                                .optional()
                                .error(() => {
                                    return 'SPECIFIC_SECTION_MUST_BE_STRING';
                                }),
                        }),
                    )
                    .optional(),
                isActive: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                isDeleted: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                archive: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                step: Joi.number()
                    .optional()
                    .error(() => {
                        return 'STEP_MUST_BE_NUMBER';
                    }),
                selectCourses: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            isDeleted: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            isConfigure: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            isEnable: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            assignedInstitutionId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ASSIGN_INSTITUTION_IDMUST_BE_OBJECTID';
                                }),
                            institutionName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'INSTITUTION_NAME_MUST_BE_STRING';
                                }),
                            programId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'PROGRAM_ID_REQUIRED';
                                }),
                            programName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'PROGRAM_NAME_MUST_BE_STRING';
                                }),
                            curriculumId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'CURRICULUM_ID_REQUIRED';
                                }),
                            curriculumName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'CURRICULUM_NAME_MUST_BE_STRING';
                                }),
                            year: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'YEAR_NAME_MUST_BE_STRING';
                                }),
                            courseId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'COURSE_ID_REQUIRED';
                                }),
                            courseName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'COURSE_NAME_MUST_BE_STRING';
                                }),
                            courseCode: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'COURSE_CODE_MUST_BE_STRING';
                                }),
                            sharedWithOthers: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            sharedFormOthers: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            courseType: Joi.string()
                                .valid(STANDARD, SELECTIVE)
                                .optional()
                                .error(() => {
                                    return 'CATEGORY_FORM_TYPE_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:STANDARD, SELECTIVE';
                                }),
                        }),
                    )
                    .optional(),
                selectedProgram: Joi.array()
                    .items(
                        Joi.object().keys({
                            programId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'PEOGRAM_ID_REQUIRED';
                                }),
                            programName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'PROGRAM_NAME_MUST_BE_STRING';
                                }),
                            all: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                        }),
                    )
                    .optional(),
                selectedInstitution: Joi.array()
                    .items(
                        Joi.object().keys({
                            assignedInstitutionId: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ASSIGN_INSTITUTION_IDMUST_BE_OBJECTID';
                                }),
                            institutionName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'INSTITUTION_NAME_MUST_BE_STRING';
                                }),
                        }),
                    )
                    .optional(),
                sectionAttachments: Joi.array()
                    .items(
                        Joi.object().keys({
                            sectionName: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'SECTION_NAME_MUST_BE_STRING';
                                }),
                            description: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'DESCRIPTION_MUST_BE_STRING';
                                }),
                        }),
                    )
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.createGroupsValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
                categoryFormCourseIds: Joi.array()
                    .items(Joi.string().alphanum().length(24))
                    .optional()
                    .error(() => {
                        return 'CATEGORY_FORM_COURSE_IDS_MUST_BE_REQURED';
                    }),
                actions: Joi.object()
                    .keys({
                        studentGroups: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        everyAcademic: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        occurrenceConfiguration: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        academicTerms: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        attemptType: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        incorporateMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        displayMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                    })
                    .optional(),
                numberOfGroups: Joi.array()
                    .items(Joi.string())
                    .optional()
                    .error(() => {
                        return 'NUMBER_OF_GROUPS_MUST_BE_STRING';
                    }),
                selectedType: Joi.array()
                    .items(
                        Joi.object().keys({
                            term: Joi.string()
                                .optional()
                                .allow('')
                                .error(() => {
                                    return 'TERM_MUST_BE_STRING';
                                }),
                            attemptTypeName: Joi.string()
                                .optional()
                                .allow('')
                                .error(() => {
                                    return 'ATTEMPT_TYPE_NAME_MUST_BE_STRING';
                                }),
                            executionsPer: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            academicYear: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'ACADEMIC_YEAR_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ALL, EVERY';
                                }),
                            group: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'GROUP_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ALL, INDIVIDUAL';
                                }),
                            groupName: Joi.string()
                                .optional()
                                .allow('')
                                .error(() => {
                                    return 'GROUP_NAME_MUST_BE_STRING';
                                }),
                            minimum: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'MINIMUM_MUST_BE_NUMBER';
                                }),
                            startMonth: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'START_MONTH_MUST_BE_NUMBER';
                                }),
                            endMonth: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'END_MONTH_MUST_BE_NUMBER';
                                }),
                        }),
                    )
                    .optional(),
                tags: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            name: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'NAME_MUST_BE_STRING';
                                }),
                            subTag: Joi.array()
                                .items(Joi.string())
                                .optional()
                                .error(() => {
                                    return 'TAGS_MUST_BE_STRING';
                                }),
                        }),
                    )
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.formConfigureListValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryFormCourseId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_COURSE_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.editConfigureValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        body: Joi.object()
            .keys({
                categoryFormCourseId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_COURSE_ID_REQUIRED';
                    }),
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
                categoryFormId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_FORM_ID_REQUIRED';
                    }),
                status: Joi.string()
                    .valid(PUBLISHED, DRAFT)
                    .optional()
                    .error(() => {
                        return 'STATUS_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:PUBLISHED, DRAFT';
                    }),
                actions: Joi.object()
                    .keys({
                        studentGroups: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        everyAcademic: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        occurrenceConfiguration: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        academicTerms: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        attemptType: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        incorporateMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                        displayMandatory: Joi.boolean()
                            .optional()
                            .error(() => {
                                return 'EITHER_TRUE_OR_FALSE';
                            }),
                    })
                    .optional(),
                numberOfGroups: Joi.array()
                    .items(Joi.string())
                    .optional()
                    .error(() => {
                        return 'NUMBER_OF_GROUPS_MUST_BE_STRING';
                    }),
                isConfigure: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                isEnable: Joi.boolean()
                    .optional()
                    .error(() => {
                        return 'EITHER_TRUE_OR_FALSE';
                    }),
                selectedType: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            isDeleted: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            term: Joi.string()
                                .optional()
                                .allow('')
                                .error(() => {
                                    return 'TERM_MUST_BE_STRING';
                                }),
                            attemptTypeName: Joi.string()
                                .optional()
                                .allow('')
                                .error(() => {
                                    return 'ATTEMPT_TYPE_NAME_MUST_BE_STRING';
                                }),
                            executionsPer: Joi.boolean()
                                .optional()
                                .error(() => {
                                    return 'EITHER_TRUE_OR_FALSE';
                                }),
                            academicYear: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'ACADEMIC_YEAR_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ALL, EVERY';
                                }),
                            group: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'GROUP_MUST_BE_ONE_OF_THE_FOLLOWING_VALUE:ALL, INDIVIDUAL';
                                }),
                            groupName: Joi.string()
                                .optional()
                                .allow('')
                                .error(() => {
                                    return 'GROUP_NAME_MUST_BE_STRING';
                                }),
                            minimum: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'MINIMUM_MUST_BE_NUMBER';
                                }),
                            startMonth: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'START_MONTH_MUST_BE_NUMBER';
                                }),
                            endMonth: Joi.number()
                                .optional()
                                .error(() => {
                                    return 'END_MONTH_MUST_BE_NUMBER';
                                }),
                        }),
                    )
                    .optional(),
                tags: Joi.array()
                    .items(
                        Joi.object().keys({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .optional()
                                .error(() => {
                                    return 'ID_REQUIRED';
                                }),
                            name: Joi.string()
                                .optional()
                                .error(() => {
                                    return 'NAME_MUST_BE_STRING';
                                }),
                            subTag: Joi.array()
                                .items(Joi.string())
                                .optional()
                                .error(() => {
                                    return 'TAGS_MUST_BE_STRING';
                                }),
                        }),
                    )
                    .optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.qapcSignedUrlValidator = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                url: Joi.string()
                    .required()
                    .error(() => {
                        return 'URL_MUST_BE_STRING';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getProgramValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                searchKey: Joi.string().optional().allow(''),
                pageNo: Joi.number().optional(),
                limit: Joi.number().optional(),
            })
            .unknown(true),
    })
    .unknown(true);

exports.getCurriculumValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                programId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'PROGRAM_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

exports.resetFormCourseSettingValidator = Joi.object()
    .keys({
        Headers: Joi.object().keys({
            _institution_id: objectId.error(() => {
                return 'INSTITUTION_ID_REQUIRED';
            }),
        }),
        query: Joi.object()
            .keys({
                categoryId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'CATEGORY_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
