const assignmentPromptAnswerSchema = require('./assignment_prompt_answer.model');
const { convertToMongoObjectId } = require('../../../utility/common');

const createAssignmentPromptAnswer = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            studentId,
            assignmentId,
            assignmentPromptId,
            choiceType,
            singleAnswer,
            matchTheFollowing,
            fillInTheBlanks,
        } = body;
        const userUpdateObject = {
            _institution_id: convertToMongoObjectId(_institution_id),
            studentId: convertToMongoObjectId(studentId),
            assignmentId: convertToMongoObjectId(assignmentId),
            assignmentPromptId: convertToMongoObjectId(assignmentPromptId),
            ...(choiceType && { choiceType }),
            ...(fillInTheBlanks && { fillInTheBlanks }),
            ...(singleAnswer && { singleAnswer }),
            ...(matchTheFollowing && { matchTheFollowing }),
        };
        const assignmentPromptAnswerExists = await assignmentPromptAnswerSchema.findOne({
            _institution_id: convertToMongoObjectId(_institution_id),
            studentId: convertToMongoObjectId(studentId),
            assignmentId: convertToMongoObjectId(assignmentId),
            assignmentPromptId: convertToMongoObjectId(assignmentPromptId),
        });
        let assignmentPromptAnswer;
        if (!assignmentPromptAnswerExists) {
            assignmentPromptAnswer = await assignmentPromptAnswerSchema.create(userUpdateObject);
        } else {
            assignmentPromptAnswer = await assignmentPromptAnswerSchema.findByIdAndUpdate(
                { _id: convertToMongoObjectId(assignmentPromptAnswerExists._id) },
                userUpdateObject,
            );
        }
        if (!assignmentPromptAnswer)
            return { statusCode: 410, message: 'ERROR IN CREATING ASSIGNMENT PROMPT ANSWER' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT PROMPT ANSWER CREATED',
            data: { assignmentPromptAnswerId: assignmentPromptAnswer._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const assignmentPromptAnswerBulkUpdate = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { studentId, assignmentId, promptAnswers } = body;

        const promptAnswerBulkUpdateData = [];

        const answerQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            studentId: convertToMongoObjectId(studentId),
            assignmentId: convertToMongoObjectId(assignmentId),
        };

        for (const answerElement of promptAnswers) {
            const {
                assignmentPromptId,
                choiceType,
                fillInTheBlanks,
                singleAnswer,
                matchTheFollowing,
                attachments,
            } = answerElement;
            const promptId = convertToMongoObjectId(assignmentPromptId);
            const updateData = {
                ...answerQuery,
                ...(attachments && { attachments }),
                assignmentPromptId: promptId,
                choiceType: choiceType || [],
                fillInTheBlanks: fillInTheBlanks || [],
                singleAnswer: singleAnswer || [],
                matchTheFollowing: matchTheFollowing || [],
                staffEvaluation: [],
            };
            promptAnswerBulkUpdateData.push({
                updateOne: {
                    filter: {
                        ...answerQuery,
                        assignmentPromptId: promptId,
                    },
                    update: updateData,
                    upsert: true,
                },
            });
        }

        const updateAssignmentPromptAnswerData = await assignmentPromptAnswerSchema.bulkWrite(
            promptAnswerBulkUpdateData,
        );

        if (!updateAssignmentPromptAnswerData)
            return {
                statusCode: 410,
                message: 'ERROR IN CREATING ASSIGNMENT PROMPT ANSWER',
            };

        return {
            statusCode: 200,
            message: 'ASSIGNMENT PROMPT ANSWER BULK CREATED',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const listAssignmentPromptAnswer = async () => {
    try {
        const assignmentPromptAnswer = await assignmentPromptAnswerSchema.find({}).lean();
        if (!assignmentPromptAnswer)
            return { statusCode: 410, message: 'ERROR IN LISTING ASSIGNMENT PROMPT ANSWER' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT PROMPT ANSWER',
            data: assignmentPromptAnswer,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getAssignmentPromptAnswer = async ({ params = {} }) => {
    try {
        const { id } = params;
        const assignmentPromptAnswer = await assignmentPromptAnswerSchema
            .findById({ _id: convertToMongoObjectId(id) })
            .lean();
        if (!assignmentPromptAnswer)
            return { statusCode: 410, message: 'ERROR IN GETTING ASSIGNMENT PROMPT ANSWER ' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT PROMPT ANSWER',
            data: assignmentPromptAnswer,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteAssignmentPromptAnswer = async ({ params = {} }) => {
    try {
        const { id } = params;
        const assignmentPromptAnswer = await assignmentPromptAnswerSchema
            .findByIdAndDelete({ _id: convertToMongoObjectId(id) })
            .lean();
        if (!assignmentPromptAnswer)
            return { statusCode: 410, message: 'ERROR IN DELETING ASSIGNMENT PROMPT ANSWER' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT PROMPT ANSWER DELETED',
            data: {},
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateAssignmentPromptAnswer = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { _institution_id } = headers;
        const { id } = params;
        const {
            subjectId,
            assignmentId,
            assignmentPromptId,
            fillInTheBlanks,
            matchTheFollowing,
            singleAnswer,
            choiceType,
            choiceTypeId,
            singleAnswerId,
            matchTheFollowingId,
            fillInTheBlanksId,
            isResubmitted,
        } = body;
        let assignmentPromptAnswer;
        if (isResubmitted) {
            const userUpdateObject = {
                _institution_id,
                subjectId,
                assignmentId,
                assignmentPromptId,
            };
            userUpdateObject.$addToSet = {
                ...(choiceType && { choiceType }),
                ...(fillInTheBlanks && { fillInTheBlanks }),
                ...(singleAnswer && { singleAnswer }),
                ...(matchTheFollowing && { matchTheFollowing }),
            };

            assignmentPromptAnswer = await assignmentPromptAnswerSchema.findByIdAndUpdate(
                { _id: convertToMongoObjectId(id) },
                userUpdateObject,
            );
        } else {
            assignmentPromptAnswer = await assignmentPromptAnswerSchema.findByIdAndUpdate(
                { _id: convertToMongoObjectId(id) },
                {
                    _institution_id,
                    subjectId,
                    assignmentId,
                    assignmentPromptId,
                    ...(choiceType && { 'choiceType.$[choiceTypeId]': choiceType }),
                    ...(singleAnswer && { 'singleAnswer.$[singleAnswerId]': singleAnswer }),
                    ...(matchTheFollowing && {
                        'matchTheFollowing.$[matchTheFollowingId]': matchTheFollowing,
                    }),
                    ...(fillInTheBlanks && {
                        'fillInTheBlanks.$[fillInTheBlanksId]': fillInTheBlanks,
                    }),
                },
                {
                    arrayFilters: [
                        { 'choiceTypeId._id': convertToMongoObjectId(choiceTypeId) },
                        { 'singleAnswerId._id': convertToMongoObjectId(singleAnswerId) },
                        { 'matchTheFollowingId._id': convertToMongoObjectId(matchTheFollowingId) },
                        { 'fillInTheBlanksId._id': convertToMongoObjectId(fillInTheBlanksId) },
                    ],
                },
            );
        }
        if (!assignmentPromptAnswer)
            return { statusCode: 410, message: 'ERROR IN UPDATING ASSIGNMENT PROMPT ANSWER' };
        return {
            statusCode: 200,
            message: 'ASSIGNMENT PROMPT ANSWER UPDATED',
            data: { assignmentPromptAnswerId: assignmentPromptAnswer._id },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    createAssignmentPromptAnswer,
    listAssignmentPromptAnswer,
    getAssignmentPromptAnswer,
    deleteAssignmentPromptAnswer,
    updateAssignmentPromptAnswer,
    assignmentPromptAnswerBulkUpdate,
};
