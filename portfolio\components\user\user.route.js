const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const { getProgramList, getProgramCourseList } = require('./user.controller');
const {
    getInstitutionCalendars,
} = require('../../../lib/dcExternalSync/dcExternalSync.controller');
const { getProgramListSchema, getProgramCourseListSchema } = require('./user.validation');

router.get('/institution-calendar', catchAsync(getInstitutionCalendars));

router.get('/program', validate(getProgramListSchema), catchAsync(getProgramList));

router.get('/course', validate(getProgramCourseListSchema), catchAsync(getProgramCourseList));

module.exports = router;
