const announcementSchema = require('../../announcement/announcement_model');
const {
    convertToMongoObjectId,
    sendResponse,
    list_all_response_function,
    response_function,
    sendResponseWithRequest,
    listAllResponseFunctionWithRequest,
} = require('../../utility/common');
const {
    SERVICES: { ANNOUNCEMENT_V2, PARENT_APP },
} = require('../../utility/util_keys');
const { PUBLISHED } = require('../../utility/constants');
const { getSignedUrl } = require('../../announcement/announcement_service');
const parentUserSchema = require('../../models/parent.user');
exports.userViewNotification = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { userId, page, limit, announcementType, priorityType },
        } = req;
        const perPage = parseInt(limit > 0 ? limit : 10);
        const pageNo = parseInt(page > 0 ? page : 1);
        const countQuery = {
            _institution_id: convertToMongoObjectId(_institution_id),
            'userViewList.userViewId': convertToMongoObjectId(userId),
            isActive: true,
            status: PUBLISHED,
            ...(announcementType &&
                announcementType.length && {
                    'announcementType.name': { $in: announcementType },
                }),
            ...(priorityType &&
                priorityType.length && {
                    'priorityType.name': { $in: priorityType },
                }),
        };
        const totalDoc = await announcementSchema.countDocuments(countQuery);
        let announcementData = await announcementSchema
            .find(countQuery, {
                announcementTitle: 1,
                message: 1,
                announcementType: 1,
                priorityType: 1,
                publishDate: 1,
                status: 1,
                attachments: 1,
                createdBy: 1,
                'userViewList.$': 1,
                selectUserGroup: 1,
                childIds: 1,
            })
            .sort({ publishDate: -1 })
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .skip(perPage * (pageNo - 1))
            .limit(perPage)
            .lean();
        let totalPages = 0;
        let currentPage = 0;
        if (limit && page) {
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        }
        if (ANNOUNCEMENT_V2 === 'true' && PARENT_APP === 'true') {
            const parentData = await parentUserSchema
                .findOne(
                    {
                        _id: convertToMongoObjectId(userId),
                    },
                    {
                        childIds: 1,
                    },
                )
                .populate({ path: 'childIds', select: { name: 1, user_id: 1, _id: 1 } })
                .lean();
            for (const announcementElement of announcementData) {
                if (
                    [
                        'all',
                        'female student',
                        'male student',
                        'digiclass connect',
                        'individual student',
                    ].includes(announcementElement.selectUserGroup[0])
                ) {
                    if (parentData && parentData.childIds) {
                        announcementElement.childData = parentData.childIds.filter(
                            (childElement) => {
                                return announcementElement.childIds
                                    .toString()
                                    .includes(childElement._id.toString());
                            },
                        );
                    }
                }
            }
        }
        if (!announcementData.length) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('No announcements found'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        [],
                    ),
                );
        }
        announcementData = announcementData.map((announcementElement) => {
            const { selectUserGroup, childIds, ...rest } = announcementElement;
            return rest;
        });
        if ((limit && page) || announcementData.length) {
            return res
                .status(200)
                .send(
                    listAllResponseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('LIST_DATA'),
                        totalDoc,
                        totalPages,
                        currentPage,
                        announcementData,
                    ),
                );
        }
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.typeList = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            query: { user_Id },
        } = req;
        const announcementData = await announcementSchema.aggregate([
            {
                $match: {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    'userViewList.userViewId': convertToMongoObjectId(user_Id),
                    status: PUBLISHED,
                    isActive: true,
                    // 'announcementType.name': { $ne: '' },
                    // 'priorityType.name': { $ne: '' },
                },
            },
            {
                $group: {
                    _id: null,
                    announcementType: { $addToSet: '$announcementType.name' },
                    priorityType: { $addToSet: '$priorityType.name' },
                },
            },
            {
                $project: {
                    _id: 0,
                    announcementType: 1,
                    priorityType: 1,
                },
            },
        ]);
        if (!announcementData.length) {
            return sendResponseWithRequest(req, res, 200, false, 'NO_Data');
        }
        for (announcementDataElement of announcementData) {
            announcementDataElement.announcementType =
                announcementDataElement.announcementType.filter(
                    (announcementTypeElement) => announcementTypeElement !== '',
                );
            announcementDataElement.priorityType = announcementDataElement.priorityType.filter(
                (priorityTypeElement) => priorityTypeElement !== '',
            );
        }
        return sendResponseWithRequest(req, res, 200, true, 'List_Data', announcementData);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.updateViewedNotification = async (req, res) => {
    try {
        const {
            query: { announcementId, userId },
        } = req;
        const announcementData = await announcementSchema.updateMany(
            {
                _id: {
                    $in: announcementId.map((announcementIdElement) =>
                        convertToMongoObjectId(announcementIdElement),
                    ),
                },
                'userViewList.userViewId': convertToMongoObjectId(userId),
            },
            {
                $set: { 'userViewList.$.view': true },
            },
            {
                new: true,
            },
        );
        if (!announcementData) {
            return sendResponseWithRequest(req, res, 200, false, 'UNABLE_TO_DATA_UPDATED');
        }
        return sendResponseWithRequest(req, res, 200, true, 'DATA_UPDATED');
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.singleListAnnouncement = async (req, res) => {
    try {
        const {
            query: { announcementId, userId },
        } = req;
        let announcementData = await announcementSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(announcementId),
                },
                {
                    announcementTitle: 1,
                    message: 1,
                    announcementType: 1,
                    priorityType: 1,
                    attachments: 1,
                    publishDate: 1,
                    expiryDate: 1,
                    status: 1,
                    createdBy: 1,
                    childIds: 1,
                    selectUserGroup: 1,
                },
            )
            .populate({ path: 'createdBy', select: { name: 1, user_id: 1 } })
            .lean();
        const updatedAttachments = [];
        if (announcementData.attachments && announcementData.attachments.length) {
            for (const urlElement of announcementData.attachments) {
                const url = urlElement.url;
                const signedUrl = await getSignedUrl(url);
                const updatedUrlElement = {
                    _id: urlElement._id,
                    url: urlElement.url,
                    signedUrl,
                    name: urlElement.name,
                };
                updatedAttachments.push(updatedUrlElement);
            }
        }
        let childData = [];
        if (process.env.ANNOUNCEMENT_V2 && process.env.ANNOUNCEMENT_V2 === 'true') {
            if (
                [
                    'all',
                    'female student',
                    'male student',
                    'digiclass connect',
                    'individual student',
                ].includes(announcementData.selectUserGroup[0])
            ) {
                const parentData = await parentUserSchema
                    .findOne(
                        {
                            _id: convertToMongoObjectId(userId),
                        },
                        {
                            childIds: 1,
                        },
                    )
                    .populate({ path: 'childIds', select: { name: 1, user_id: 1, _id: 1 } })
                    .lean();
                if (parentData) {
                    childData = parentData.childIds.filter((childElement) => {
                        return announcementData.childIds
                            .toString()
                            .includes(childElement._id.toString());
                    });
                }
            }
        }
        announcementData = {
            _id: announcementData._id,
            attachments: updatedAttachments,
            announcementTitle: announcementData.announcementTitle,
            message: announcementData.message,
            announcementType: announcementData.announcementType,
            priorityType: announcementData.priorityType,
            publishDate: announcementData.publishDate,
            expiryDate: announcementData.expiryDate,
            status: announcementData.status,
            createdBy: announcementData.createdBy,
            childData,
        };
        if (!announcementData) sendResponseWithRequest(req, res, 200, false, 'No_Data');
        return sendResponseWithRequest(req, res, 200, true, 'List_Data', announcementData);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.receiveNotificationCount = async (req, res) => {
    try {
        const {
            query: { userId },
        } = req;
        const announcementDataCount = await announcementSchema.countDocuments({
            status: PUBLISHED,
            userViewList: {
                $elemMatch: {
                    userViewId: convertToMongoObjectId(userId),
                    view: false,
                },
            },
        });

        return sendResponseWithRequest(req, res, 200, true, 'Data_Count', announcementDataCount);
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
