const Joi = require('joi');

function getCourseCourseCoordinatorsValidate() {
    const schema = {
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).required(),
            term: Joi.string().min(3).required(),
            year: Joi.string().min(3).required(),
            level_no: Joi.string().min(3).required(),
            _user_id: Joi.string().allow('').required(),
            _institution_calendar_id: Joi.string().alphanum().length(24),
        }),
    };
    return schema;
}

module.exports = {
    getCourseCourseCoordinatorsValidate: getCourseCourseCoordinatorsValidate(),
};
