const { WebClient } = require('@slack/web-api');
const { SLACK_AUTH_TOKEN, logger, SLACK_CHANNEL } = require('../utility/util_keys');

async function sendMessage(text) {
    if (!text) throw new Error('Please provide text to post in slack');
    const web = new WebClient(SLACK_AUTH_TOKEN);
    return web.chat
        .postMessage({
            channel: SLACK_CHANNEL,
            text,
        })
        .then((resp) => resp)
        .catch((err) => {
            logger.error({ err }, 'slackService -> sendMessage -> err:');
            throw err;
        });
}

module.exports = {
    sendMessage,
};
