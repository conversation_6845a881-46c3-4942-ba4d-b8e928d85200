const constant = require('../utility/constants');
var infra_event_exam = require('mongoose').model(constant.INFRASTRUCTURE_EVENT_EXAM);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');


exports.create = async(req,res) =>{
    let objs = {
        type: req.body.type,
        event: req.body.event,
        exam: req.body.exam
    }
    let doc = await base_control.insert(infra_event_exam, objs);
    if(doc.status) {
        common_files.com_response(res, 200, true, "selected Event and Exam" ,doc.data)
    } else {
        common_files.com_response(res, 404, false, "Error in selecting event and exam", "Error in selecting event and exam")
    }
}

exports.get = async(req,res) => {
    let doc_data = [];
    let doc = await base_control.get_list(infra_event_exam,{'isDeleted': false})
    if(doc.status) {
        doc.data.forEach(element => {
            doc_data.push({_id:element._id,type:element.type,event:element.event,exam:element.exam});
        });
        common_files.com_response(res, 200, true, "Got the selected events and exam", doc_data)
    } else {
        common_files.com_response(res, 404, false, "Error in getting infra events and exam" ,"Error in getting infra events and exam")
    }
}

exports.get_id = async(req,res) => {
    let checks = { status: true}
    checks = await base_control.check_id(infra_event_exam,{_id: {$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
    let doc_data = [];
    let doc = await base_control.get_list(infra_event_exam,{_id: req.params.id})
    if(doc.status) {
        doc.data.forEach(element => {
            doc_data.push({_id:element._id,type:element.type,event:element.event,exam:element.exam})
        })
        common_files.com_response(res, 200, true, "Got the selected events and exam by id", doc_data)
    } else {
        common_files.com_response(res, 404, false, "Error in getting infra events and exam", "Error in getting infra events and exam")
    }
} else{
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.update = async(req,res) => {
    let checks = {status: true}
    checks = await base_control.check_id(infra_event_exam,{_id: {$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
    let objs = {
            type: req.body.type,
            event: req.body.event,
            exam: req.body.exam
    }
    let doc = await base_control.update(infra_event_exam,{_id:req.params.id},objs)
    if(doc.status) {
            common_files.com_response(res, 200, true, "updated selected event and exam", doc.data)
    } else {
            common_files.com_response(res, 404, false, "Error in updating event and exam", "Error in updating event and exam")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.update_event_exam = async(req,res) => {
    let checks = {status: true}
    checks = await base_control.check_id(infra_event_exam,{_id: {$in: req.params.id}, 'isDeleted': false});
    if(checks.status){
    let objs = {};
    if(req.body.event != undefined && req.body.exam != undefined){
        Object.assign(objs,{'event' :req.body.event} ,{'exam' :req.body.exam})
    } else{
        if(req.body.event != undefined && req.body.exam == undefined){
            Object.assign(objs,{'event' :req.body.event})
        }
        if(req.body.event == undefined && req.body.exam != undefined){
            Object.assign(objs,{'exam' :req.body.exam})
        }
    }
    let doc = await base_control.update(infra_event_exam,{_id:req.params.id},objs)
    if(doc.status) {
            common_files.com_response(res, 200, true, "updated only event and exam", doc.data)
    } else {
            common_files.com_response(res, 404, false, "Error in updating event and exam", "Error in updating event and exam")
    }
} else {
    common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
}
}

exports.delete = async(req,res) => {
    let checks = {status: true}
    checks = await base_control.check_id(infra_event_exam,{_id: {$in: req.params.id}, 'isDeleted': false});
    if(checks.status) {
        let doc = await base_control.delete(infra_event_exam,{_id: req.params.id})
        if(doc.status) {
            common_files.com_response(res, 200, true, "Deleted selected event and exam" ,doc.data)
        } else {
            common_files.com_response(res, 404, false, "Error in deleting event and exam", "Error in deleting event and exam")
        }
    } else {
        common_files.com_response(res, 404, false, "Error id not match", "Check parsing reference ID")
    }
}