let department_formate = require('../department/department_formate');
let department_division_formate = require('../department_division/department_division_formate');
let department_subject_formate = require('../department_subject/department_subject_formate');

module.exports = {
    allotment: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                allotments_name: element.allotments_name,
                study_year: element.study_year,
                study_level: element.study_level,
                order: element.order,
                allotments_number: element.allotments_number,
                duration: element.duration,
                model: element.model,
                elective: element.elective,
                administration_department: department_formate.department_ID_Onlys(element.administration_department),
                // administration_division: department_division_formate.department_division_ID_Onlys(element.administration_division),
                administration_subject: department_subject_formate.department_subject_ID_Only(element.administration_subject),
                participating_department: department_formate.department_ID_Array_Only(element.participating_department),
                participating_division: department_division_formate.department_division_ID_Array_Only(element.participating_division),
                participating_subject: department_subject_formate.department_subject_ID_Array_Only(element.participating_subject),
                theory_credit: element.theory_credit,
                practical_credit: element.practical_credit,
                clinical_credit: element.clinical_credit,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            if (element.administration_division != undefined) {
                obj.administration_division = department_division_formate.department_division_ID_Onlys(element.administration_division);
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    allotment_ID: (doc) => {
        let obj = {
            _id: doc._id,
            allotments_name: doc.allotments_name,
            study_year: doc.study_year,
            study_level: doc.study_level,
            order: doc.order,
            allotments_number: doc.allotments_number,
            duration: doc.duration,
            model: doc.model,
            elective: doc.elective,
            administration_department: department_formate.department_ID_Onlys(doc.administration_department),
            // administration_division: department_division_formate.department_division_ID_Onlys(doc.administration_division) || '',
            administration_subject: department_subject_formate.department_subject_ID_Only(doc.administration_subject),
            participating_department: department_formate.department_ID_Array_Only(doc.participating_department),
            participating_division: department_division_formate.department_division_ID_Array_Only(doc.participating_division),
            participating_subject: department_subject_formate.department_subject_ID_Array_Only(doc.participating_subject),
            theory_credit: doc.theory_credit,
            practical_credit: doc.practical_credit,
            clinical_credit: doc.clinical_credit,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        if (doc.administration_division != undefined) {
            obj.administration_division = department_division_formate.department_division_ID_Onlys(doc.administration_division);
        } else {
            obj.administration_division = [];
        }
        return obj;
    },

    allotment_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            allotments_name: doc.allotments_name,
            study_year: doc.study_year,
            study_level: doc.study_level,
            order: doc.order,
            allotments_number: doc.allotments_number,
            duration: doc.duration,
            model: doc.model,
            elective: doc.elective,
            administration_department: doc._administration_department_id,
            administration_division: doc._administration_division_id,
            administration_subject: doc._administration_subject_id,
            participating_department: doc._participating_department_id,
            participating_division: doc._participating_division_id,
            participating_subject: doc._participating_subject_id,
            theory_credit: doc.theory_credit,
            practical_credit: doc.practical_credit,
            clinical_credit: doc.clinical_credit,
            isDeleted: doc.isDeleted,
            isActive: doc.isActive
        }
        if (doc.administration_division != undefined) {
            obj.administration_division = department_division_formate.department_division_ID_Onlys(doc.administration_division);
        } else {
            obj.administration_division = [];
        }
        return obj;
    },

    allotment_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                allotments_name: element.allotments_name,
                study_year: element.study_year,
                study_level: element.study_level,
                order: element.order,
                allotments_number: element.allotments_number,
                duration: element.duration,
                model: element.model,
                elective: element.elective,
                administration_department: element._administration_department_id,
                administration_division: element._administration_division_id,
                administration_subject: element._administration_subject_id,
                participating_department: element._participating_department_id,
                participating_division: element._participating_division_id,
                participating_subject: element._participating_subject_id,
                theory_credit: element.theory_credit,
                practical_credit: element.practical_credit,
                clinical_credit: element.clinical_credit,
                isDeleted: element.isDeleted,
                isActive: element.isActive
            }
            // if (element.administration_division != undefined) {
            //     obj.administration_division = department_division_formate.department_division_ID_Onlys(element.administration_division);
            // }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    // allotment_ID_Array_Only: (doc) => {
    //     let formate_obj = [];
    //     doc.forEach(element => {
    //         let obj = {
    //             _id: element._id,
    //             allotments_name: element.allotments_name,
    //             study_year: element.study_year,
    //             study_level: element.study_level,
    //             order: element.order,
    //             allotments_number: element.allotments_number,
    //             duration: element.duration,
    //             model: element.model,
    //             elective: element.elective,
    //             administration_department: element._administration_department_id,
    //             administration_division: element._administration_division_id,
    //             administration_subject: element._administration_subject_id,
    //             participating_department: element._participating_department_id,
    //             participating_division: element._participating_division_id,
    //             participating_subject: element._participating_subject_id,
    //             theory_credit: element.theory_credit,
    //             practical_credit: element.practical_credit,
    //             clinical_credit: element.clinical_credit,
    //             isDeleted: element.isDeleted,
    //             isActive: element.isActive
    //         }
    //         if (element.administration_division != undefined) {
    //             obj.administration_division = department_division_formate.department_division_ID_Onlys(element.administration_division);
    //         }
    //         formate_obj.push(obj);
    //     });
    //     return formate_obj;
    // }
}