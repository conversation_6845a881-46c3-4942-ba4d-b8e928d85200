const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const { DIGI_SURVEY_OUTCOME_REPORT_RESPONSE_SETTINGS, USER } = require('../../utility/constants');

const digiSurveyOutcomeReportResponseSettingsSchema = new Schema({
    _institution_id: { type: ObjectId },
    userId: { type: ObjectId, ref: USER },
    maxResponseSettings: [
        {
            responseLevel: { type: String },
            from: { type: Number },
            to: { type: Number },
            colorCode: { type: String },
        },
    ],
});

module.exports = model(
    DIGI_SURVEY_OUTCOME_REPORT_RESPONSE_SETTINGS,
    digiSurveyOutcomeReportResponseSettingsSchema,
);
