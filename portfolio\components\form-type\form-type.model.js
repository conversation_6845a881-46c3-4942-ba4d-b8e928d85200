const { Schema, model } = require('mongoose');

const { FORM_TYPE } = require('../../common/utils/constants');

const schema = new Schema(
    {
        name: { type: String, required: true },
        code: { type: String, required: true },
        isDeleted: { type: Boolean, default: false },
        isLogBook: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(FORM_TYPE, schema);
