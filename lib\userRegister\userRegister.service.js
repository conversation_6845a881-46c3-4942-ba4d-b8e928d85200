// userRegister.service.js

const OTPGreetings = 'Dear User,Your DigiClass User Registration OTP is ';
const userRegisterSubject = ' DigiClass User Registration';
const userApplicationRejectorMessage =
    'Hi I am sorry to inform you that your DigiClass Registration application has been rejected due to : \n\n ';
const userApplicationApprovedMessage =
    'Hi  your DigiClass Registration application has been Approved successfully ';

function generateExpiryDate() {
    const expiryDate = new Date(Date.now() + 3 * 60 * 1000);
    return expiryDate;
}

module.exports = {
    OTPGreetings,
    userRegisterSubject,
    userApplicationRejectorMessage,
    userApplicationApprovedMessage,
    generateExpiryDate,
};
