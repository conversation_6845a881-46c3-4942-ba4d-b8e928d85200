const Joi = require('joi');
const {
    SERVICES: { DIGI_CHAT },
} = require('../../utility/util_keys');

const createCourseChannelValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    userId: Joi.string().length(24).required(),
                    type: Joi.string().required(),
                    programId: Joi.string().allow('').optional(),
                })
                .unknown(true),
        })
        .unknown(true);

    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const getChannelsValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    institutionCalendarId: Joi.string().length(24).optional(),
                    type: Joi.string().required(),
                    userId: Joi.string().length(24).required(),
                    courseId: Joi.string().length(24).optional(),
                    rotationCount: Joi.number().optional(),
                    term: Joi.string().optional(),
                    level: Joi.string().optional(),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const checkDigiChatV2Enabled = (req, res, next) => {
    if (DIGI_CHAT !== 'true') {
        return res.status(403).send('service unavailable');
    }
    next();
};

const getChannelMsgValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                channelId: Joi.string().length(24).required(),
                page: Joi.number().required().optional(),
                limit: Joi.number().required().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};

const groupInfoValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                channelId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const getUserChatCountValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                institutionCalendarId: Joi.string().length(24).required(),
                userId: Joi.string().length(24).required(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const getChannelListValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                institutionCalendarId: Joi.string().length(24).required(),
                userId: Joi.string().length(24).required(),
                tab: Joi.string().required(),
                mode: Joi.string().optional(),
                page: Joi.number(),
                limit: Joi.number(),
                userName: Joi.string().required(),
                searchKey: Joi.string().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const getUserListValidator = (req, res, next) => {
    // Normalize headers to ensure case insensitivity
    const normalizedHeaders = {};
    Object.keys(req.headers).forEach((key) => {
        if (req.headers.hasOwnProperty(key)) {
            normalizedHeaders[key.toLowerCase()] = req.headers[key];
        }
    });
    const schema = Joi.object()
        .keys({
            headers: Joi.object({
                _user_id: Joi.string().length(24).required(),
            }).unknown(true),
            query: Joi.object().keys({
                type: Joi.string().optional(),
                searchKey: Joi.string(),
                institutionCalendarId: Joi.string(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate({
        headers: normalizedHeaders,
        params: req.params,
    });
    if (error) {
        return res.status(400).send(req.t(error.details[0].message));
    }
    next();
};
const createPrivateChannelValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _institution_calendar_id: Joi.string().length(24),
                from: Joi.object().keys({
                    userId: Joi.string().length(24).required(),
                    type: Joi.string().required(),
                    name: Joi.string().required(),
                    academicNo: Joi.string().required(),
                }),
                to: Joi.object().keys({
                    userId: Joi.string().length(24).required(),
                    type: Joi.string().required(),
                    name: Joi.string().required(),
                    academicNo: Joi.string().required(),
                }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const getChatProfileValidator = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            query: Joi.object().keys({
                channelId: Joi.string().length(24).required(),
                tab: Joi.string().required(),
                page: Joi.number(),
                limit: Joi.number(),
                type: Joi.string().optional(),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return res.status(422).json({ error: errorMessage });
    }
    next();
};
const deleteChatValidator = (req, res, next) => {
    // Normalize headers to ensure case insensitivity
    const normalizedHeaders = {};
    Object.keys(req.headers).forEach((key) => {
        if (req.headers.hasOwnProperty(key)) {
            normalizedHeaders[key.toLowerCase()] = req.headers[key];
        }
    });
    const schema = Joi.object({
        headers: Joi.object({
            _user_id: Joi.string().length(24).required(),
        }).unknown(true),
        params: Joi.object({
            channelId: Joi.string().length(24).required(),
        }).required(),
    });
    const { error } = schema.validate({
        headers: normalizedHeaders,
        params: req.params,
    });
    if (error) {
        return res.status(400).send(req.t(error.details[0].message));
    }
    next();
};
module.exports = {
    createCourseChannelValidator,
    checkDigiChatV2Enabled,
    getChannelsValidator,
    getChannelMsgValidator,
    groupInfoValidator,
    getUserChatCountValidator,
    getChannelListValidator,
    getUserListValidator,
    createPrivateChannelValidator,
    getChatProfileValidator,
    deleteChatValidator,
};
