const express = require('express');
const route = express.Router();
const catchAsync = require('../../utility/catch-async');
const {
    elasticGetAll,
    elasticSearch,
    elasticDataAdd,
    elasticDataUpdate,
    elasticDataRemove,
} = require('./elasticsSearch.controller');

route.get('/elasticGetAll', catchAsync(elasticGetAll));
route.post('/elasticSearch', catchAsync(elasticSearch));
route.post('/elasticDataAdd', catchAsync(elasticDataAdd));
route.put('/elasticDataUpdate', catchAsync(elasticDataUpdate));
route.delete('/elasticDataRemove', catchAsync(elasticDataRemove));

module.exports = route;
