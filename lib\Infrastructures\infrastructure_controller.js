const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const institution = require('mongoose').model(constant.INSTITUTION);
const infrastructure = require('mongoose').model(constant.INFRASTRUCTURE);
const infrastructure_management = require('mongoose').model(constant.INFRASTRUCTURE_MANAGEMENT);

exports.insert = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const obj = {
            _institution_id: req.headers._institution_id,
            name: req.body.name,
            location: req.body.location,
            type: req.body.type,
            floors: req.body.floors,
            zones: req.body.zones,
            outsideCampus: req.body.outsideCampus,
            radius: req.body.radius,
            latitude: req.body.latitude,
            longitude: req.body.longitude,
        };
        const building_data = await base_control.get_list_sort(
            infrastructure,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            { _id: 1, name: 1, location: 1, type: 1, floors: 1, zones: 1 },
            { updatedAt: -1 },
        );
        if (building_data.status) {
            const building_ind = building_data.data.findIndex(
                (ele) =>
                    ele.name.toLowerCase() == obj.name.toLowerCase() &&
                    ele.location.toLowerCase() == obj.location.toLowerCase(),
            );
            if (building_ind != -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('DUPLICATE_BUILDING_AND_LOCATION_FOUND'),
                            req.t('DUPLICATE_BUILDING_AND_LOCATION_FOUND'),
                        ),
                    );
        }

        const doc = await base_control.insert(infrastructure, obj);
        if (doc.status) {
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('INFRASTRUCTURE_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const obj = {
            name: req.body.name,
            location: req.body.location,
            type: req.body.type,
            floors: req.body.floors,
            zones: req.body.zones,
            radius: req.body.radius,
            latitude: req.body.latitude,
            longitude: req.body.longitude,
        };
        const building_data = await base_control.get_list_sort(
            infrastructure,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
                _id: { $ne: ObjectId(req.params._id) },
            },
            { _id: 1, name: 1, location: 1, type: 1, floors: 1, zones: 1 },
            { updatedAt: -1 },
        );
        if (building_data.status) {
            const building_ind = building_data.data.findIndex(
                (ele) =>
                    ele.name.toLowerCase() == obj.name.toLowerCase() &&
                    ele.location.toLowerCase() == obj.location.toLowerCase(),
            );
            if (building_ind != -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('DUPLICATE_BUILDING_AND_LOCATION_FOUND'),
                            req.t('DUPLICATE_BUILDING_AND_LOCATION_FOUND'),
                        ),
                    );
        }
        const query = { _id: ObjectId(req.params._id), isDeleted: false };
        const doc = await base_control.update(infrastructure, query, req.body);
        if (doc.status) {
            if (req.body.radius && req.body.latitude && req.body.longitude)
                await infrastructure_management.updateMany(
                    {
                        _building_id: common_files.convertToMongoObjectId(req.params._id),
                    },
                    {
                        $set: {
                            radius: req.body.radius,
                            latitude: req.body.latitude,
                            longitude: req.body.longitude,
                        },
                    },
                );
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('INFRASTRUCTURE_UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.delete = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //Check building is already assigned in infrastructure management
        const query = { isDeleted: false, _institution_id: ObjectId(req.headers._institution_id) };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query,
        );
        res.send(infrastructure_management_data);
        if (infrastructure_management_data.status) {
            const building_ind = infrastructure_management_data.data.findIndex((ele) =>
                ele._building_id.equals(req.params._id),
            );
            if (building_ind != -1)
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t(
                                'BUILDING_CAN_NOT_BE_DELETED_IT_IS_ALREADY_ASSIGNED_IN_INFRASTRUCTURE_MANAGEMENT',
                            ),
                            req.t(
                                'BUILDING_CAN_NOT_BE_DELETED_IT_IS_ALREADY_ASSIGNED_IN_INFRASTRUCTURE_MANAGEMENT',
                            ),
                        ),
                    );
        }
        const query_infra = {
            _id: ObjectId(req.params._id),
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const doc = await base_control.delete(infrastructure, query_infra);
        if (doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('INFRASTRUCTURE_DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.get = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const building_data = await base_control.get_list_sort(
            infrastructure,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            {
                _id: 1,
                name: 1,
                location: 1,
                type: 1,
                floors: 1,
                zones: 1,
                outsideCampus: 1,
                radius: 1,
                latitude: 1,
                longitude: 1,
            },
            { updatedAt: -1 },
        );
        if (!building_data.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('UNABLE_TO_FIND_BUILDING_HOSPITALS'),
                        [],
                    ),
                );

        const query_infra = {
            isDeleted: false,
            _institution_id: ObjectId(req.headers._institution_id),
        };
        const infrastructure_management_data = await base_control.get_list(
            infrastructure_management,
            query_infra,
        );

        if (!infrastructure_management_data.status) {
            const building_arr = [];
            for (let b = 0; b < building_data.data.length; b++) {
                const floor = [];
                const zone = [];
                for (let f = 0; f < building_data.data[b].floors.length; f++) {
                    floor.push({
                        _id: building_data.data[b].floors[f]._id,
                        floor_name: building_data.data[b].floors[f].floor_name,
                        category: building_data.data[b].floors[f].category,
                        isAssigned: false,
                        building_id: building_data.data[b]._id,
                    });
                }
                for (let z = 0; z < building_data.data[b].zones.length; z++) {
                    zone.push({
                        building_id: building_data.data[b]._id,
                        zone: building_data.data[b].zones[z],
                        isAssigned: false,
                    });
                }

                building_arr.push({
                    _id: building_data.data[b]._id,
                    name: building_data.data[b].name,
                    location: building_data.data[b].location,
                    type: building_data.data[b].type,
                    floors: floor,
                    no_of_floors: floor.length,
                    zones: zone,
                    no_of_zones: zone.length,
                    isAssigned: false,
                    latitude: building_data.data[b].latitude,
                    longitude: building_data.data[b].longitude,
                    radius: building_data.data[b].radius,
                    outsideCampus: building_data.data[b].outsideCampus,
                });
            }
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('INFRASTRUCTURES_LIST1'),
                        building_arr,
                    ),
                );
        }
        //Zone check
        const zone_arr = [];
        for (let b = 0; b < building_data.data.length; b++) {
            for (let z = 0; z < building_data.data[b].zones.length; z++) {
                let flag = 0;
                for (let im = 0; im < infrastructure_management_data.data.length; im++) {
                    const zone_ind = infrastructure_management_data.data[im].zone.findIndex(
                        (ele) => ele == building_data.data[b].zones[z],
                    );
                    if (
                        infrastructure_management_data.data[im]._building_id.equals(
                            building_data.data[b]._id,
                        ) &&
                        zone_ind != -1
                    ) {
                        flag = 1;
                        break;
                    }
                }
                zone_arr.push({
                    building_id: building_data.data[b]._id,
                    zone: building_data.data[b].zones[z],
                    isAssigned: flag == 1,
                });
            }
        }

        //Floor Check
        const floor_arr = [];
        for (let b = 0; b < building_data.data.length; b++) {
            let floor_obj = {};
            for (let f = 0; f < building_data.data[b].floors.length; f++) {
                let flag = 0;
                const floor_ind = infrastructure_management_data.data.findIndex(
                    (ele) =>
                        ele._building_id.equals(building_data.data[b]._id) &&
                        ele.floor_no == building_data.data[b].floors[f].floor_name,
                );
                if (floor_ind != -1) flag = 1;

                floor_obj = {
                    _id: building_data.data[b].floors[f]._id,
                    floor_name: building_data.data[b].floors[f].floor_name,
                    category: building_data.data[b].floors[f].category,
                    isAssigned: flag == 1,
                    building_id: building_data.data[b]._id,
                };
                floor_arr.push(floor_obj);
            }
        }

        //Building check overall group check
        const new_building_arr = [];
        for (let i = 0; i < building_data.data.length; i++) {
            let flag = 0;
            for (let j = 0; j < infrastructure_management_data.data.length; j++) {
                const building_ind = infrastructure_management_data.data.findIndex((ele) =>
                    ele._building_id.equals(building_data.data[i]._id),
                );
                if (building_ind != -1) {
                    flag = 1;
                    break;
                }
            }
            const obj = {
                _id: building_data.data[i]._id,
                name: building_data.data[i].name,
                location: building_data.data[i].location,
                type: building_data.data[i].type,
                floors: building_data.data[i].floors,
                no_of_floors: building_data.data[i].floors.length,
                zones: building_data.data[i].zones,
                no_of_zones: building_data.data[i].zones.length,
                radius: building_data.data[i].radius,
                latitude: building_data.data[i].latitude,
                longitude: building_data.data[i].longitude,
            };
            let target_temp = {};
            if (flag == 1) target_temp = Object.assign(obj, { isAssigned: true });
            else target_temp = Object.assign(obj, { isAssigned: false });
            new_building_arr.push(target_temp);
        }
        data = [
            {
                Overall: new_building_arr,
                floor: floor_arr,
                zone: zone_arr,
            },
        ];

        const new_building_data = [];
        for (let b = 0; b < new_building_arr.length; b++) {
            const floor_data = floor_arr.filter(
                (ele) => ele.building_id == new_building_arr[b]._id,
            );
            const zone_data = zone_arr.filter((ele) => ele.building_id == new_building_arr[b]._id);
            const building_obj = {
                _id: new_building_arr[b]._id,
                name: new_building_arr[b].name,
                location: new_building_arr[b].location,
                type: new_building_arr[b].type,
                floors: floor_data,
                no_of_floors: new_building_arr[b].no_of_floors,
                zones: zone_data,
                no_of_zones: new_building_arr[b].no_of_zones,
                isAssigned: new_building_arr[b].isAssigned,
                radius: new_building_arr[b].radius,
                latitude: new_building_arr[b].latitude,
                longitude: new_building_arr[b].longitude,
            };
            new_building_data.push(building_obj);
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('INFRASTRUCTURES_LIST2'),
                    new_building_data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.get_id = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await base_control.get(infrastructure, {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            isDeleted: false,
        });
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_ID_NOT_FOUND_OR_DATA_MISMATCH'),
                        req.t('CHECK_PARSING_ID'),
                    ),
                );
        const doc_data = {
            _id: doc.data._id,
            name: doc.data.name,
            location: doc.data.location,
            type: doc.data.type,
            floors: doc.data.floors,
            no_of_floors: doc.data.floors.length,
            zones: doc.data.zones,
            no_of_zones: doc.data.zones.length,
        };
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('GOT_INFRASTRUCTURE_BY_ID'),
                    doc_data,
                ),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(res, 500, false, req.t('ERROR'), error.toString()),
            );
    }
};

exports.get_location = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const aggregate = [
            { $match: { isDeleted: false } },
            {
                $group: {
                    _id: '$location',
                    radius: { $first: '$radius' },
                    longitude: { $first: '$longitude' },
                    latitude: { $first: '$latitude' },
                },
            },
            { $project: { _id: 0, location: '$_id', radius: 1, longitude: 1, latitude: 1 } },
        ];
        const doc = await base_control.get_aggregate(infrastructure, aggregate);
        if (doc.status) {
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('GOT_INFRASTRUCTURE'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(
                common_files.responseFunctionWithRequest(req, 404, false, req.t('ERROR'), doc.data),
            );
    } catch (error) {
        //console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR'),
                    error.toString(),
                ),
            );
    }
};
