const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.roles = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object()
                .keys({
                    role_name: Joi.string()
                        .min(3)
                        .max(50)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    _role_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    _assigned_by_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    assigned_by: Joi.string()
                        .min(3)
                        .max(50)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    _office_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    office_name: Joi.string()
                        .min(3)
                        .max(30)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    program_mode: Joi.string()
                        .min(2)
                        .max(10)
                        .error((error) => {
                            return error;
                        }),
                    _program_id: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .error((error) => {
                            return error;
                        }),
                    program_name: Joi.string()
                        .min(3)
                        .max(50)
                        .error((error) => {
                            return error;
                        }),
                    _department_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                    department_name: Joi.string()
                        .min(3)
                        .max(50)
                        .error((error) => {
                            return error;
                        }),
                    _permissions: Joi.array()
                        .items(Joi.string().alphanum().length(24))
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.roles_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
