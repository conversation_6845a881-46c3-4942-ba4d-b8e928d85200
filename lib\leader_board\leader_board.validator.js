const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const { STAFF_ATTENDANCE, ENGAGER, QUIZ, SUPPORT_SESSION } = require('../utility/constants');
exports.createParameterValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        body: Joi.object({
            parameters: Joi.array().items(
                Joi.object({
                    name: Joi.string().error(() => {
                        return req.t('NAME_MUST_BE_STRING');
                    }),
                    weight: Joi.number().error(() => {
                        return req.t('WEIGHT_MUST_BE_NUMBER');
                    }),
                    criteria: Joi.array()
                        .items(
                            Joi.object({
                                criteriaName: Joi.string()
                                    .valid(STAFF_ATTENDANCE, ENGAGER, QUIZ, SUPPORT_SESSION)
                                    .optional(),

                                isActive: Joi.boolean().optional(),
                                noOfSession: Joi.boolean().optional(),
                                sessionStarted: Joi.object({
                                    noOfStudent: Joi.number().required(),
                                    minsOfStart: Joi.number().required(),
                                }).optional(),
                                noOfEngager: Joi.boolean().optional(),
                                studentParticipated: Joi.number().optional(),
                                score: Joi.object({
                                    isActive: Joi.boolean().optional(),
                                    noOfScore: Joi.number().optional(),
                                }).optional(),
                                consideration: Joi.object({
                                    isActive: Joi.boolean().optional(),
                                    noOfConsideration: Joi.number().optional(),
                                }).optional(),
                                bufferTime: Joi.object({
                                    isActive: Joi.boolean().optional(),
                                    noOfBuffer: Joi.number().optional(),
                                }).optional(),
                                allowance: Joi.object({
                                    isActive: Joi.boolean().optional(),
                                    noOfAllowance: Joi.number().optional(),
                                }).optional(),
                                supportSession: Joi.object({
                                    regular: Joi.boolean().optional(),
                                    activity: Joi.boolean().optional(),
                                }).optional(),
                            }),
                        )
                        .optional(),
                }),
            ),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message);
        return res.status(400).json(errors);
    }
    next();
};
exports.getParametersValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            boardType: Joi.string().valid('leader', 'anomaly').required(),
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message);
        return res.status(400).json(errors);
    }
    next();
};
exports.editParametersValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        body: Joi.object({
            parameterId: objectId,
            name: Joi.string().error(() => {
                return req.t('NAME_MUST_BE_STRING');
            }),
            weight: Joi.number().error(() => {
                return req.t('WEIGHT_MUST_BE_NUMBER');
            }),
            criteria: Joi.array().items(
                Joi.object({
                    criteriaName: Joi.string()
                        .valid(STAFF_ATTENDANCE, ENGAGER, QUIZ, SUPPORT_SESSION)
                        .optional(),

                    isActive: Joi.boolean().optional(),
                    noOfSession: Joi.boolean().optional(),
                    sessionStarted: Joi.object({
                        noOfStudent: Joi.number().required(),
                        minsOfStart: Joi.number().required(),
                    }).optional(),
                    noOfEngager: Joi.boolean().optional(),
                    studentParticipated: Joi.number().optional(),
                    score: Joi.object({
                        isActive: Joi.boolean().optional(),
                        noOfScore: Joi.number().optional(),
                    }).optional(),
                    consideration: Joi.object({
                        isActive: Joi.boolean().optional(),
                        noOfConsideration: Joi.number().optional(),
                    }).optional(),
                    bufferTime: Joi.object({
                        isActive: Joi.boolean().optional(),
                        noOfBuffer: Joi.number().optional(),
                    }).optional(),
                    allowance: Joi.object({
                        isActive: Joi.boolean().optional(),
                        noOfAllowance: Joi.number().optional(),
                    }).optional(),
                    supportSession: Joi.object({
                        regular: Joi.boolean().optional(),
                        activity: Joi.boolean().optional(),
                    }).optional(),
                }),
            ),
        }),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message);
        return res.status(400).json(errors);
    }
    next();
};
exports.deleteParametersValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        query: Joi.object({
            parameterId: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        query: req.query,
    });
    if (error) {
        const errors = error.details.map((e) => e.message);
        return res.status(400).json(errors);
    }
    next();
};
