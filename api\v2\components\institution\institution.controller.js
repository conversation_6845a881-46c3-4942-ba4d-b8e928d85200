const institutionSchema = require('./institution.model');
const programSchema = require('../program-input/program-input.model');
const institutionFormat = require('./institution.format');
const { getPaginationValues } = require('../../utility/pagination');
const { getPresignedUrlsForInstitutes } = require('./institution.util');
const { timezones } = require('../../utility/timezones');
const {
    DS_DATA_RETRIEVED,
    DS_UPDATED,
    DS_ADDED,
    DS_DELETED,
    DS_UPDATE_FAILED,
    DS_ADD_FAILED,
    DS_GET_FAILED,
    DS_CREATED,
    DS_CREATE_FAILED,
    DS_DELETE_FAILED,
    DS_ARCHIVED_FAILED,
    DS_ARCHIVED,
    DS_RESTORE_FAILED,
    DS_RESTORE,
    ACTIVE,
    ARCHIVED,
    INSTITUTION,
    PROGRAM,
} = require('../../utility/constants');
const { getModel } = require('../../utility/common');
const ObjectId = require('mongodb').ObjectID;
const {
    bulkWrite,
    removeRecords,
    updateRecords,
    getRecords,
} = require('../../services/elasticSearch.service');

const listInstitutes = async ({ query = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { search, sort, status } = query;
        const { limit, skip, pageNo } = getPaginationValues(query);
        const { id } = params;
        let dbQuery = { isDeleted: false, parentInstitute: id, isUniversity: false };
        if (search && search.length) {
            dbQuery = {
                ...dbQuery,
                $or: [
                    { name: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                ],
            };
        }
        if (status === ACTIVE) {
            dbQuery = { ...dbQuery, isActive: true };
        } else if (status === ARCHIVED) {
            dbQuery = { ...dbQuery, isActive: false };
        }
        let institutes = [];
        if (sort === 'true') {
            institutes = await institutionModel
                .find(dbQuery)
                .collation({ locale: 'en' })
                .sort({ name: 1 })
                .skip(skip)
                .limit(limit)
                .lean();
        } else if (sort === 'false') {
            institutes = await institutionModel
                .find(dbQuery)
                .collation({ locale: 'en' })
                .sort({ name: -1 })
                .skip(skip)
                .limit(limit)
                .lean();
        } else {
            institutes = await institutionModel.find(dbQuery).skip(skip).limit(limit).lean();
        }
        const totalInstitutes = await institutionModel.find(dbQuery).countDocuments().lean();
        const childInstitutesCount = await institutionModel
            .find({
                parentInstitute: id,
                isDeleted: false,
            })
            .countDocuments();
        if (!institutes || Number.isNaN(totalInstitutes)) {
            return { statusCode: 500, message: DS_GET_FAILED };
        }
        const institutionIds = institutes.map((institutionElement) => institutionElement._id);
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const programWithInstitutions = await programModel
            .find({ _institution_id: { $in: institutionIds } }, { _institution_id: 1 })
            .lean();
        const totalPages = Math.ceil(totalInstitutes / limit);
        return {
            statusCode: 200,
            data: {
                totalPages,
                currentPage: pageNo,
                institutes: await institutionFormat.institution(
                    institutes,
                    programWithInstitutions,
                ),
                childInstitutesCount,
            },
            message: DS_DATA_RETRIEVED,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInstitutionById = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const query = { _id: ObjectId(id), isDeleted: false };
        const doc = await institutionModel.findOne(query).lean();
        if (!doc) {
            return { statusCode: 500, message: DS_GET_FAILED, data: doc };
        }
        const data = await getPresignedUrlsForInstitutes(doc);
        if (!data.parentInstitute) {
            const childInstitutionCount = await institutionModel.countDocuments({
                parentInstitute: data._id,
                isDeleted: false,
            });
            data.childInstitutionCount = childInstitutionCount;
        }
        return { statusCode: 200, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createInstitute = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const {
            logo,
            name,
            type,
            code,
            accreditation,
            address,
            country,
            countryId,
            state,
            stateId,
            city,
            district,
            zipCode,
            parentInstitute,
            noOfColleges,
            isUniversity,
        } = body;
        let createQuery = {
            accreditation,
            logo,
            name,
            type,
            code,
            isUniversity,
            parentInstitute,
            address: {
                address,
                country: {
                    name: country,
                    countryId,
                },
                state: {
                    name: state,
                    stateId,
                },
                city: {
                    name: city,
                },
                district,
                zipCode,
            },
        };
        const institute = await institutionModel.findOne({
            code,
            isDeleted: false,
        });
        if (institute) {
            return {
                statusCode: 400,
                message: 'INSTITUTE_WITH_SAME_CODE_ALREADY_EXISTS',
            };
        }

        if (isUniversity === 'true') {
            createQuery = { ...createQuery, noOfColleges };
        } else if (isUniversity === 'false' && parentInstitute) {
            const parentInstitution = await institutionModel.findOne({
                _id: parentInstitute,
                isUniversity: true,
            });
            const collegesCreated = await institutionModel.countDocuments({
                parentInstitute,
                isUniversity: false,
                isDeleted: false,
            });
            if (collegesCreated + 1 > parentInstitution.noOfColleges) {
                return { statusCode: 400, message: 'YOUR_ARE_EXCEEDING_THE_NUMBER_OF_INSTITUTION' };
            }
            delete createQuery.accreditation;
        }
        if (typeof createQuery.accreditation === 'string') {
            createQuery.accreditation = JSON.parse(createQuery.accreditation);
        }

        const newInstitute = await institutionModel.create(createQuery);
        if (!newInstitute) {
            return { statusCode: 500, message: DS_CREATE_FAILED };
        }
        const userRecords = bulkWrite({ indexName: INSTITUTION, dataset: [createQuery] });
        const data = await getPresignedUrlsForInstitutes(newInstitute);
        return { statusCode: 200, message: DS_CREATED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateInstitute = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const {
            logo,
            name,
            type,
            code,
            accreditation,
            address,
            country,
            countryId,
            state,
            stateId,
            city,
            district,
            zipCode,
            parentInstitute,
            noOfColleges,
            isUniversity,
        } = body;

        let updateQuery = {
            logo,
            name,
            type,
            code,
            accreditation,
            isUniversity,
            address: {
                address,
                country: {
                    name: country,
                    countryId,
                },
                state: {
                    name: state,
                    stateId,
                },
                city: {
                    name: city,
                },
                district,
                zipCode,
            },
            parentInstitute,
        };

        const instituteExists = await institutionModel.findById(id).lean();
        if (!instituteExists) return { statusCode: 400, message: 'INSTITUTION_NOT_FOUND' };

        const institute = await institutionModel
            .findOne({
                code,
                isDeleted: false,
                _id: { $ne: id },
            })
            .lean();
        if (institute) {
            return {
                statusCode: 400,
                message: 'INSTITUTE_WITH_SAME_CODE_ALREADY_EXISTS',
            };
        }

        if (isUniversity) {
            updateQuery = { ...updateQuery, noOfColleges };
        } else if (!isUniversity && parentInstitute) {
            delete updateQuery.accreditation;
        }

        if (!logo) {
            updateQuery = { ...updateQuery, logo: null };
        }

        Object.keys(updateQuery).map((key) => {
            if (!updateQuery[[key]]) delete updateQuery[[key]];
        });
        if (typeof updateQuery.accreditation === 'string') {
            updateQuery.accreditation = JSON.parse(updateQuery.accreditation);
        }

        const updatedInstitute = await institutionModel
            .findByIdAndUpdate(id, updateQuery, {
                new: true,
            })
            .lean();
        const script = `ctx._source['logo'] = '${updateQuery.logo}';ctx._source['name'] = '${updateQuery.name}';ctx._source['type'] = '${updateQuery.type}';ctx._source['code'] = '${updateQuery.code}';ctx._source['parentInstitute'] = '${updateQuery.parentInstitute}';ctx._source['address'] = [ "address": "${updateQuery.address.address}","district": "${updateQuery.address.district}","zipCode": "${updateQuery.address.zipCode}","country":["name":"${updateQuery.address.country.name}","countryId":"${updateQuery.address.country.name}"],"state":["name":"${updateQuery.address.state.name}","stateId":"${updateQuery.address.state.name}"],"city":["name":"${updateQuery.address.city.name}"]]`;
        await updateRecords({
            indexName: INSTITUTION,
            query: {
                match: { code: instituteExists.code },
            },
            script,
        });
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_UPDATE_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_UPDATED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addOrEditAccreditation = async ({ headers = {}, params = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { accreditation } = body;
        const { id } = params;
        const updatedInstitute = await institutionModel.findByIdAndUpdate(
            id,
            { accreditation },
            { new: true },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_ADD_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const addOrEditInstituteDescription = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { description, media } = body;
        const { id } = params;
        let instituteDescription = { 'instituteDescription.description': description };
        if (media) {
            instituteDescription = {
                ...instituteDescription,
                'instituteDescription.mediaURL': media,
            };
        } else {
            instituteDescription = {
                ...instituteDescription,
                'instituteDescription.mediaURL': null,
            };
        }
        const updatedInstitute = await institutionModel.findByIdAndUpdate(
            id,
            {
                $set: instituteDescription,
            },
            { new: true },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_ADD_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_ADDED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addInstitutePortfolio = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { title, description, media } = body;
        const { id } = params;
        let newPortfolio = {
            title,
            description,
        };
        if (media) {
            newPortfolio = { ...newPortfolio, mediaURL: media };
        } else {
            newPortfolio = { ...newPortfolio, mediaURL: null };
        }
        const updatedInstitute = await institutionModel.findByIdAndUpdate(
            id,
            {
                $push: { portfolio: newPortfolio },
            },
            { new: true },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, data: DS_ADD_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_ADDED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editInstitutePortfolio = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { title, description, portfolioId, media } = body;
        const { id } = params;
        let institutePortfolio = {
            'portfolio.$.title': title,
            'portfolio.$.description': description,
        };
        if (media) {
            institutePortfolio = { ...institutePortfolio, 'portfolio.$.mediaURL': media };
        } else {
            institutePortfolio = { ...institutePortfolio, 'portfolio.$.mediaURL': null };
        }
        const updatedInstitute = await institutionModel.findOneAndUpdate(
            { _id: id, 'portfolio._id': portfolioId },
            {
                $set: institutePortfolio,
            },
            {
                returnDocument: 'after',
            },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_UPDATE_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_UPDATED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteInstitutePortfolioMedia = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { portfolioId } = body;
        const { id } = params;
        const updatedInstitute = await institutionModel.findOneAndUpdate(
            { _id: id, 'portfolio._id': portfolioId },
            {
                $unset: { 'portfolio.$.mediaURL': '' },
            },
            {
                new: true,
            },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_DELETE_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_DELETED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeInstituteDescription = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const updatedInstitute = await institutionModel.findByIdAndUpdate(
            id,
            { $unset: { instituteDescription: '' } },
            { new: true },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_DELETE_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_DELETED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteInstitutePortfolio = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { portfolioId } = body;
        const { id } = params;
        const updatedInstitute = await institutionModel.findOneAndUpdate(
            { _id: id },
            {
                $pull: { portfolio: { _id: portfolioId } },
            },
            {
                new: true,
            },
        );
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_DELETE_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_DELETED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteInstitute = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const updateQuery = {
            isDeleted: true,
            isActive: false,
        };
        const institute = await institutionModel.findById(id);
        if (institute && institute.isUniversity) {
            const childInstitutes = await institutionModel.countDocuments({
                parentInstitute: institute._id,
                isDeleted: false,
            });
            if (childInstitutes) {
                return { statusCode: 400, message: 'INSTITUTE_CANNOT_BE_DELETED' };
            }
        }
        const deletedInstitute = await institutionModel.findByIdAndUpdate(id, updateQuery, {
            new: true,
        });
        if (!deletedInstitute) {
            return { statusCode: 500, message: DS_DELETE_FAILED };
        }
        removeRecords({
            indexName: INSTITUTION,
            query: {
                dis_max: {
                    queries: [{ match: { code: institute.code } }],
                },
            },
        });
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const archieveInstitute = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const updateQuery = {
            isActive: false,
        };
        const institute = await institutionModel.findByIdAndUpdate(id, updateQuery);
        if (!institute) {
            return { statusCode: 500, message: DS_ARCHIVED_FAILED };
        }
        return { statusCode: 200, message: DS_ARCHIVED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const unArchieveInstitute = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const updateQuery = {
            isActive: true,
        };
        const institute = await institutionModel.findByIdAndUpdate(id, updateQuery);
        if (!institute) {
            return { statusCode: 500, message: DS_RESTORE_FAILED };
        }
        return { statusCode: 200, message: DS_RESTORE };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateInstituteLogo = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { logo } = body;
        const { id } = params;
        const obj = {
            logo,
        };
        const updatedInstitute = await institutionModel.findByIdAndUpdate(id, obj, { new: true });
        if (!updatedInstitute) {
            return { statusCode: 500, message: DS_UPDATE_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(updatedInstitute);
        return { statusCode: 200, message: DS_UPDATED, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getInstituteLogo = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const institutionModel = getModel(tenantURL, INSTITUTION, institutionSchema);
        const { id } = params;
        const institute = await institutionModel.findById(id, { logo: 1 }).lean();
        if (!institute) {
            return { statusCode: 500, message: DS_GET_FAILED };
        }
        const data = await getPresignedUrlsForInstitutes(institute);
        return { statusCode: 200, data };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getTimezones = () => {
    return { statusCode: 200, data: timezones };
};

module.exports = {
    listInstitutes,
    getInstitutionById,
    createInstitute,
    updateInstitute,
    updateInstituteLogo,
    getInstituteLogo,
    addOrEditInstituteDescription,
    deleteInstitute,
    archieveInstitute,
    unArchieveInstitute,
    addInstitutePortfolio,
    editInstitutePortfolio,
    deleteInstitutePortfolioMedia,
    deleteInstitutePortfolio,
    removeInstituteDescription,
    getTimezones,
    addOrEditAccreditation,
};
