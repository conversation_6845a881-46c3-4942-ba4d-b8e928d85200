stages:
    - build
    - test
    - deploy

workflow:
    rules:
        - if: $CI_COMMIT_TAG || $CI_PIPELINE_SOURCE == "web"

variables:
    ECR: $AWS_CONTAINER_REGISTRY/$APP_NAME
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ''
    DEPLOY_TAG:
        value: $CI_COMMIT_TAG
        description: 'Enter the current tag to deploy to production'
    DEPLOY_ENVIRONMENT:
        value: "upm"
        options:
          - "upm"
          - "sla"
        description: "The deployment target. Change this variable to 'upm' or 'sla' if needed."

test ymlfile:
  stage: test
  script:
    - echo "Testing Main YML File"

include:
  - local: .gitlab/ci/oracle-upm.gitlab-ci.yml
    rules:
      - if: $DEPLOY_ENVIRONMENT == "upm"
  - local: .gitlab/ci/oracle-sla.gitlab-ci.yml
    rules:
      - if: $DEPLOY_ENVIRONMENT == "sla"