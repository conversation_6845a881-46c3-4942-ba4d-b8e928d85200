const express = require('express');
const route = express.Router();
const validator = require('./sessions_validator');
const controller = require('./sessions_controller');
const { uploadDocument } = require('./session_service');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../middleware/policy.middleware');
route.get(
    '/session_status/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.session_status,
);
route.get(
    '/student_session_status/:id/:student',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    controller.student_session_status,
);
route.get(
    '/session_report/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_report,
);
route.get(
    '/session_report_with_face/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.session_report_with_face,
);
route.put(
    '/session_reset/:id',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_reset,
);
route.post(
    '/session_change_attendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_change_attendance,
);
route.post(
    '/session_attendance_change',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_attendance_change,
);
route.post(
    '/session_start',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_start,
);
route.post(
    '/session_retake',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_retake,
);
route.post(
    '/session_retake_with_attendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_retake_with_attendance,
);
route.post(
    '/student_attendance',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    controller.student_attendance,
);
route.post(
    '/staffStudentAttendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.staffStudentAttendance,
);
route.post(
    '/session_stop',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_stop,
);
route.post(
    '/session_close',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_close,
);
route.post(
    '/session_close_with_attendance',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_close_with_attendance,
);
route.post(
    '/session_comment',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.session_comment,
);
route.put(
    '/session_missedToComplete',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.updateMissedToCompleteSession,
);

route.post(
    '/scheduleStart',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    controller.scheduleStart,
);
route.post(
    '/scheduleDateChange',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.scheduleDateChange,
);
//campus otp verification
route.post(
    '/campusOtpVerification',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.campusOtpVerification,
);
route.get(
    '/campusStudentList/:_id',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    controller.campusStudentList,
);
route.post(
    '/campusImageUrl',
    [userPolicyAuthentication([defaultPolicy.DC_STUDENT])],
    uploadDocument,
    controller.uploadCampusImageUrl,
);
route.get(
    '/generateUrl',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.generateUrl,
);
route.post(
    '/updateStudentStatus',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    controller.updateStudentStatus,
);
module.exports = route;
