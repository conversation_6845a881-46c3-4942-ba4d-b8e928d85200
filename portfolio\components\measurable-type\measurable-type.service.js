const MeasurableTypeModel = require('./measurable-type.model');

const {
    UpdateFailedError,
    NotFoundError,
    BadRequestError,
} = require('../../common/utils/api_error_util');

const createMeasurableType = async ({ name, code }) => {
    const measurable = await MeasurableTypeModel.create({ name, code });
    return measurable;
};

const getMeasurableTypes = async () => {
    const measurable = await MeasurableTypeModel.find(
        { isDeleted: false },
        { name: 1, code: 1 },
    ).lean();
    if (!measurable?.length) {
        throw new NotFoundError('MEASURABLE_NOT_FOUND');
    }

    return measurable;
};

const updateMeasurableType = async ({ measurableTypeId, name, code }) => {
    const measurable = await MeasurableTypeModel.updateOne(
        { _id: measurableTypeId },
        { name, code },
    );
    if (!measurable.modifiedCount) {
        throw new UpdateFailedError('UPDATE_FAILED');
    }

    return measurable;
};

const deleteMeasurableType = async ({ measurableTypeId }) => {
    const measurable = await MeasurableTypeModel.deleteOne({ _id: measurableTypeId });
    if (!measurable.deletedCount) {
        throw new BadRequestError('DELETE_FAILED');
    }

    return measurable;
};

module.exports = {
    createMeasurableType,
    getMeasurableTypes,
    updateMeasurableType,
    deleteMeasurableType,
};
