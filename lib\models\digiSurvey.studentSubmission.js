const mongoose = require('mongoose');
const {
    DIGI_SURVEY,
    USER,
    DIGI_SURVEY_QUESTION,
    DIGI_SURVEY_STUDENT_SUBMISSION,
    DIGI_SURVEY_SECTION,
} = require('../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const digiSurveyStudentSubmissionSchemas = new Schema(
    {
        surveyId: {
            type: ObjectId,
            ref: DIGI_SURVEY,
        },
        studentId: {
            type: ObjectId,
            ref: USER,
        },
        // studentName: { type: String },
        // studentUserId: { type: String },
        // studentGender: { type: String },
        studentResponse: [
            {
                // sectionId: {
                //     type: ObjectId,
                //     ref: DIGI_SURVEY_SECTION,
                // },
                questionId: {
                    type: ObjectId,
                    ref: DIGI_SURVEY_QUESTION,
                },
                questionType: {
                    type: String,
                },
                studentResponseRating: {
                    type: Number,
                },
                studentResponse: {
                    type: String,
                },
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(DIGI_SURVEY_STUDENT_SUBMISSION, digiSurveyStudentSubmissionSchemas);
