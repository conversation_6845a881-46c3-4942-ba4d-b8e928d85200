const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../utility/constants');

const frameworkSchema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        name: {
            type: String,
            required: true,
            trim: true,
        },
        code: {
            type: String,
            required: true,
            trim: true,
        },
        mapped_count: {
            type: Number,
            default: 0,
        },
        domains: [
            {
                no: String,
                name: String,
            },
        ],
        order: {
            type: Number,
        },
        standard_range_settings: [
            {
                year_program: String,
                domain_id: Schema.Types.ObjectId,
                domain_name: String,
                year: Number,
                higher_limit: Number,
                lower_limit: Number,
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(constant.FRAMEWORK, frameworkSchema);
