const multer = require('multer');
const { uploadDocumentFile } = require('../utility/file_upload');
const keys = require('../utility/util_keys');
const { getS3SignedUrl, getSizeOfS3Url } = require('../../service/aws.service');
const { response_function } = require('../utility/common');
const announcementSchema = require('./announcement_model');
const { PUBLISHED } = require('../utility/constants');
const { convertToMongoObjectId } = require('../utility/common');
const userSchema = require('../models/user');
const fileUpload = uploadDocumentFile.fields([{ name: 'file', maxCount: 1 }]);
const announcementUserSettingSchema = require('./announcement_user_setting_model');
const studentGroupSchema = require('../models/student_group');
const courseScheduledSchema = require('../models/course_schedule');
const {
    ACADEMIC,
    BOTH,
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
    EVENT_WHOM: { STUDENT, STAFF },
    GENDER: { MALE },
} = require('../utility/constants');
const uploadDocument = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getSignedUrl = async (url) => {
    const fileName = url.split('/').pop();
    if (url.includes('digiclass/')) {
        const key = url.split(keys.BUCKET_NAME_DOC + '/').pop();
        return await getS3SignedUrl({
            bucket: keys.BUCKET_NAME_DOC,
            key,
        });
    }
    return await getS3SignedUrl({
        bucket: keys.BUCKET_NAME_DOC,
        key: `digiclass/` + fileName,
    });
};

const getSizeOfUrl = async (url) => {
    const fileName = url.split('/').pop();
    if (url.includes('digiclass/')) {
        const key = url.split(keys.BUCKET_NAME_DOC + '/').pop();
        return await getSizeOfS3Url({
            bucket: keys.BUCKET_NAME_DOC,
            key,
        });
    }
    return await getSizeOfS3Url({
        bucket: keys.BUCKET_NAME_DOC,
        key: `digiclass/` + fileName,
    });
};
const filterUniqueId = ({ userDetails }) => {
    return [
        ...new Map(
            userDetails.map((userElement) => [userElement._id.toString(), userElement]),
        ).values(),
    ];
};
const sendAnnouncement = async (userId, type) => {
    try {
        if (
            type.toLowerCase() === 'father' ||
            type.toLowerCase() === 'mother' ||
            type.toLowerCase() === 'spouse' ||
            type.toLowerCase() === 'guardian'
        ) {
            const announcementData = await announcementSchema.updateMany(
                {
                    isActive: true,
                    status: PUBLISHED,
                    selectUserGroup: { $in: [`digiConnect-${type}`] },
                    'userViewList.$.userViewId': { $nin: [convertToMongoObjectId(userId)] },
                },
                {
                    $push: {
                        userViewList: {
                            userViewId: convertToMongoObjectId(userId),
                            view: false,
                        },
                    },
                },
            );
        }
        if (type === 'newUser') {
            const userData = await userSchema.findOne(
                {
                    _id: convertToMongoObjectId(userId),
                },
                {
                    user_type: 1,
                    'employment.user_employment_type': 1,
                    staff_employment_type: 1,
                    gender: 1,
                },
            );
            if (userData.user_type.toLowerCase() === 'staff') {
                const announcementData = await announcementSchema.updateMany(
                    {
                        isActive: true,
                        status: PUBLISHED,
                        $or: [
                            {
                                selectUserGroup: {
                                    $in: [
                                        `${userData.user_type}-${userData.employment.user_employment_type}-${userData.staff_employment_type}-${userData.gender}`,
                                    ],
                                },
                            },
                            {
                                $or: [
                                    {
                                        selectUserGroup: {
                                            $all: [
                                                'staff-part_time-administration-male',
                                                'staff-part_time-academic-male',
                                            ],
                                        },
                                    },
                                    {
                                        selectUserGroup: {
                                            $all: [
                                                'staff-full_time-administration-female',
                                                'staff-full_time-academic-female',
                                            ],
                                        },
                                    },
                                    {
                                        selectUserGroup: {
                                            $all: [
                                                'staff-full_time-administration-male',
                                                'staff-full_time-academic-male',
                                            ],
                                        },
                                    },
                                    {
                                        selectUserGroup: {
                                            $all: [
                                                'staff-part_time-administration-female',
                                                'staff-part_time-academic-female',
                                            ],
                                        },
                                    },
                                ],
                            },
                        ],
                        'userViewList.$.userViewId': { $nin: [convertToMongoObjectId(userId)] },
                    },
                    {
                        $push: {
                            userViewList: {
                                userViewId: convertToMongoObjectId(userId),
                                view: false,
                            },
                        },
                    },
                );
            } else if (userData.user_type.toLowerCase() === 'student') {
                const announcementData = await announcementSchema.updateMany(
                    {
                        isActive: true,
                        status: PUBLISHED,
                        selectUserGroup: {
                            $in: [`${userData.user_type}-${userData.gender}`],
                        },
                        'userViewList.$.userViewId': { $nin: [convertToMongoObjectId(userId)] },
                    },
                    {
                        $push: {
                            userViewList: {
                                userViewId: convertToMongoObjectId(userId),
                                view: false,
                            },
                        },
                    },
                );
            }
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getUserList = async (type, _institution_id, userId) => {
    try {
        let uniqueStudentIds = new Set();
        const studentIds = [];
        const staffIds = [];
        const studentGroupQuery = [];
        const schedulesQuery = [];
        const userDetails = [];
        let studentGroup;
        let staffData;
        const announcementUserList = await announcementUserSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    userId: convertToMongoObjectId(userId),
                },
                {
                    selectedPrograms: 1,
                    selectedCourses: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        announcementUserList.selectedCourses?.forEach((selectedCoursesElement) => {
            selectedCoursesElement.level?.forEach((levelElement) => {
                levelElement?.course?.forEach((courseElement) => {
                    studentGroupQuery.push({
                        _institution_calendar_id: selectedCoursesElement._institution_calendar_id,
                        'master._program_id': selectedCoursesElement._program_id[0],
                        'master.year': levelElement.year,
                        'groups.term': levelElement.term,
                        'groups.level': levelElement.level_no,
                        'groups.curriculum': levelElement.curriculum,
                        'groups.rotation': 'no',
                        'groups.courses._course_id': courseElement._course_id,
                    });
                    schedulesQuery.push({
                        _institution_calendar_id: selectedCoursesElement._institution_calendar_id,
                        _program_id: selectedCoursesElement._program_id[0],
                        term: levelElement.term,
                        year_no: levelElement.year,
                        level_no: levelElement.level_no,
                        _course_id: courseElement._course_id,
                    });
                });
                levelElement?.rotation_course?.forEach((rotationCourseElement) => {
                    rotationCourseElement?.course?.forEach((courseElement) => {
                        studentGroupQuery.push({
                            _institution_calendar_id:
                                selectedCoursesElement._institution_calendar_id,
                            'master._program_id': selectedCoursesElement._program_id[0],
                            'master.year': levelElement.year,
                            'groups.term': levelElement.term,
                            'groups.level': levelElement.level_no,
                            'groups.curriculum': levelElement.curriculum,
                            'groups.rotation': 'yes',
                            'groups.courses.setting._group_no':
                                rotationCourseElement.rotation_count,
                            'groups.courses._course_id': courseElement._course_id,
                        });
                        schedulesQuery.push({
                            _institution_calendar_id:
                                selectedCoursesElement._institution_calendar_id,
                            _program_id: selectedCoursesElement._program_id[0],
                            term: levelElement.term,
                            year_no: levelElement.year,
                            level_no: levelElement.level_no,
                            rotation: 'yes',
                            rotation_count: rotationCourseElement.rotation_count,
                            _course_id: courseElement._course_id,
                        });
                    });
                });
            });
        });
        const studentGroupData = [];
        const studentGroupInProgram = [];
        //get studentIds
        if (
            (type === 'all' || type === 'female student' || type === 'male student') &&
            announcementUserList?.selectedPrograms?.length
        ) {
            studentGroup = await studentGroupSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        'master._program_id': {
                            $in: announcementUserList.selectedPrograms.map((programElement) =>
                                convertToMongoObjectId(programElement),
                            ),
                        },
                    },
                    {
                        _id: 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                    },
                )
                .lean();
            studentGroupInProgram.push(...studentGroup);
        }
        if (
            (type === 'all' || type === 'female student' || type === 'male student') &&
            studentGroupQuery.length
        ) {
            studentGroup = await studentGroupSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: studentGroupQuery,
                    },
                    {
                        _id: 1,
                        _institution_calendar_id: 1,
                        'master._program_id': 1,
                        'master.year': 1,
                        'groups.level': 1,
                        'groups.term': 1,
                        'groups.curriculum': 1,
                        'groups.rotation_count': 1,
                        'groups.courses._course_id': 1,
                        'groups.courses.setting._group_no': 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                    },
                )
                .lean();
            studentGroupData.push(...studentGroup);
        }
        studentGroupData.forEach((studentGroupElement) => {
            studentGroupElement?.groups.forEach((groupElement) => {
                groupElement?.courses.forEach((courseElement) => {
                    studentGroupQuery.forEach((userGroupElement) => {
                        if (
                            userGroupElement['groups.rotation'] === 'no' &&
                            userGroupElement._institution_calendar_id.toString() ===
                                studentGroupElement._institution_calendar_id.toString() &&
                            userGroupElement['master._program_id'].toString() ===
                                studentGroupElement.master._program_id.toString() &&
                            userGroupElement['groups.level'] === groupElement.level &&
                            userGroupElement['groups.term'] === groupElement.term &&
                            userGroupElement['groups.curriculum'] === groupElement.curriculum &&
                            userGroupElement['groups.courses._course_id'].toString() ===
                                courseElement._course_id.toString()
                        ) {
                            courseElement?.setting?.forEach((settingElement) => {
                                settingElement?.session_setting.forEach((sessionElement) => {
                                    sessionElement?.groups.forEach((sessionGroupElement) => {
                                        sessionGroupElement?._student_ids.forEach(
                                            (studentElement) => {
                                                studentIds.push(studentElement.toString());
                                            },
                                        );
                                    });
                                });
                            });
                        }
                        if (
                            userGroupElement['groups.rotation'] === 'yes' &&
                            userGroupElement._institution_calendar_id.toString() ===
                                studentGroupElement._institution_calendar_id.toString() &&
                            userGroupElement['master._program_id'].toString() ===
                                studentGroupElement.master._program_id.toString() &&
                            userGroupElement['groups.level'] === groupElement.level &&
                            userGroupElement['groups.term'] === groupElement.term &&
                            userGroupElement['groups.curriculum'] === groupElement.curriculum &&
                            userGroupElement['groups.courses._course_id'].toString() ===
                                courseElement._course_id.toString()
                        ) {
                            courseElement?.setting?.forEach((settingElement) => {
                                if (
                                    settingElement._group_no ===
                                    userGroupElement['groups.courses.setting._group_no']
                                ) {
                                    settingElement.session_setting.forEach((sessionElement) => {
                                        sessionElement.groups.forEach((sessionGroupElement) => {
                                            sessionGroupElement._student_ids.forEach(
                                                (studentElement) => {
                                                    studentIds.push(studentElement.toString());
                                                },
                                            );
                                        });
                                    });
                                }
                            });
                        }
                    });
                });
            });
        });
        studentGroupInProgram.forEach((studentGroupElement) => {
            studentGroupElement?.groups.forEach((groupElement) => {
                groupElement?.courses.forEach((courseElement) => {
                    courseElement?.setting.forEach((settingElement) => {
                        settingElement?.session_setting.forEach((sessionElement) => {
                            sessionElement?.groups.forEach((sessionGroupElement) => {
                                sessionGroupElement._student_ids.forEach((studentElement) => {
                                    studentIds.push(studentElement.toString());
                                });
                            });
                        });
                    });
                });
            });
        });
        //get staffIds
        const staffDetails = [];
        if (
            type === 'all' &&
            announcementUserList &&
            announcementUserList.selectedPrograms.length
        ) {
            staffData = await courseScheduledSchema
                .find(
                    {
                        _program_id: {
                            $in: announcementUserList.selectedPrograms.map((programElement) =>
                                convertToMongoObjectId(programElement),
                            ),
                        },
                    },
                    { _id: 0, 'staffs._staff_id': 1 },
                )
                .lean();
            staffDetails.push(...staffData);
        }
        if (type === 'all' && schedulesQuery.length) {
            staffData = await courseScheduledSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: schedulesQuery,
                    },
                    { _id: 0, 'staffs._staff_id': 1 },
                )
                .lean();
            staffDetails.push(...staffData);
        }
        staffDetails?.forEach((staffElement) => {
            staffElement?.staffs.forEach((staffIdElement) => {
                staffIds.push(staffIdElement._staff_id.toString());
            });
        });
        let studentGender;
        if (type === 'male student' || type === 'female student') {
            const splitGender = type.split(' ');
            studentGender = splitGender[0];
        }
        studentIds.push(...staffIds);
        uniqueStudentIds = [...new Set(studentIds)];
        let userData;
        if (uniqueStudentIds.length) {
            userData = await userSchema.find(
                {
                    _id: { $in: uniqueStudentIds.map((userElement) => userElement) },
                    ...(studentGender && { gender: studentGender, user_type: 'student' }),
                },
                {
                    _id: 1,
                },
            );
        }
        if (userData?.length) userDetails.push(...userData);
        return userDetails;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getSelectedUserProgram = async (_institution_id, userId, type) => {
    try {
        let uniqueStudentIds = new Set();
        const studentIds = [];
        const studentGroupQuery = [];
        const schedulesQuery = [];
        let studentGroup;
        let staffData;
        const announcementUserList = await announcementUserSettingSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    userId: convertToMongoObjectId(userId),
                },
                {
                    selectedPrograms: 1,
                    selectedCourses: 1,
                },
            )
            .sort({ _id: -1 })
            .lean();
        announcementUserList?.selectedCourses?.forEach((selectedCoursesElement) => {
            selectedCoursesElement.level.forEach((levelElement) => {
                levelElement?.course?.forEach((courseElement) => {
                    studentGroupQuery.push({
                        _institution_calendar_id: selectedCoursesElement._institution_calendar_id,
                        'master._program_id': selectedCoursesElement._program_id[0],
                        'master.year': levelElement.year,
                        'groups.term': levelElement.term,
                        'groups.level': levelElement.level_no,
                        'groups.curriculum': levelElement.curriculum,
                        'groups.rotation': 'no',
                        'groups.courses._course_id': courseElement._course_id,
                    });
                    schedulesQuery.push({
                        _institution_calendar_id: selectedCoursesElement._institution_calendar_id,
                        _program_id: selectedCoursesElement._program_id[0],
                        term: levelElement.term,
                        year_no: levelElement.year,
                        level_no: levelElement.level_no,
                        _course_id: courseElement._course_id,
                    });
                });
                levelElement?.rotation_course?.forEach((rotationCourseElement) => {
                    rotationCourseElement?.course?.forEach((courseElement) => {
                        studentGroupQuery.push({
                            _institution_calendar_id:
                                selectedCoursesElement._institution_calendar_id,
                            'master._program_id': selectedCoursesElement._program_id[0],
                            'master.year': levelElement.year,
                            'groups.term': levelElement.term,
                            'groups.level': levelElement.level_no,
                            'groups.curriculum': levelElement.curriculum,
                            'groups.rotation': 'yes',
                            'groups.courses.setting._group_no':
                                rotationCourseElement.rotation_count,
                            'groups.courses._course_id': courseElement._course_id,
                        });
                        schedulesQuery.push({
                            _institution_calendar_id:
                                selectedCoursesElement._institution_calendar_id,
                            _program_id: selectedCoursesElement._program_id[0],
                            term: levelElement.term,
                            year_no: levelElement.year,
                            level_no: levelElement.level_no,
                            rotation: 'yes',
                            rotation_count: rotationCourseElement.rotation_count,
                            _course_id: courseElement._course_id,
                        });
                    });
                });
            });
        });
        const studentGroupData = [];
        const studentGroupInProgram = [];
        if (
            (type === 'student' || type === 'digiConnect' || type === 'digiclass connect') &&
            announcementUserList?.selectedPrograms?.length
        ) {
            studentGroup = await studentGroupSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        'master._program_id': {
                            $in: announcementUserList.selectedPrograms.map((programElement) =>
                                convertToMongoObjectId(programElement),
                            ),
                        },
                    },
                    {
                        _id: 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                    },
                )
                .lean();
            studentGroupInProgram.push(...studentGroup);
        }
        if (
            (type === 'student' || type === 'digiConnect' || type === 'digiclass connect') &&
            studentGroupQuery.length
        ) {
            studentGroup = await studentGroupSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: studentGroupQuery,
                    },
                    {
                        _id: 1,
                        _institution_calendar_id: 1,
                        'master._program_id': 1,
                        'master.year': 1,
                        'groups.level': 1,
                        'groups.term': 1,
                        'groups.curriculum': 1,
                        'groups.rotation_count': 1,
                        'groups.courses._course_id': 1,
                        'groups.courses.setting._group_no': 1,
                        'groups.courses.setting.session_setting.groups._student_ids': 1,
                    },
                )
                .lean();
            studentGroupData.push(...studentGroup);
        }
        studentGroupData?.forEach((studentGroupElement) => {
            studentGroupElement?.groups.forEach((groupElement) => {
                groupElement?.courses.forEach((courseElement) => {
                    studentGroupQuery.forEach((userGroupElement) => {
                        if (
                            userGroupElement['groups.rotation'] === 'no' &&
                            userGroupElement._institution_calendar_id.toString() ===
                                studentGroupElement._institution_calendar_id.toString() &&
                            userGroupElement['master._program_id'].toString() ===
                                studentGroupElement.master._program_id.toString() &&
                            userGroupElement['groups.level'] === groupElement.level &&
                            userGroupElement['groups.term'] === groupElement.term &&
                            userGroupElement['groups.curriculum'] === groupElement.curriculum &&
                            userGroupElement['groups.courses._course_id'].toString() ===
                                courseElement._course_id.toString()
                        ) {
                            courseElement?.setting?.forEach((settingElement) => {
                                settingElement?.session_setting?.forEach((sessionElement) => {
                                    sessionElement.groups.forEach((sessionGroupElement) => {
                                        sessionGroupElement._student_ids.forEach(
                                            (studentElement) => {
                                                studentIds.push(studentElement.toString());
                                            },
                                        );
                                    });
                                });
                            });
                        }
                        if (
                            userGroupElement['groups.rotation'] === 'yes' &&
                            userGroupElement._institution_calendar_id.toString() ===
                                studentGroupElement._institution_calendar_id.toString() &&
                            userGroupElement['master._program_id'].toString() ===
                                studentGroupElement.master._program_id.toString() &&
                            userGroupElement['groups.level'] === groupElement.level &&
                            userGroupElement['groups.term'] === groupElement.term &&
                            userGroupElement['groups.curriculum'] === groupElement.curriculum &&
                            userGroupElement['groups.courses._course_id'].toString() ===
                                courseElement._course_id.toString()
                        ) {
                            courseElement?.setting?.forEach((settingElement) => {
                                if (
                                    settingElement._group_no ===
                                    userGroupElement['groups.courses.setting._group_no']
                                ) {
                                    settingElement.session_setting.forEach((sessionElement) => {
                                        sessionElement.groups.forEach((sessionGroupElement) => {
                                            sessionGroupElement._student_ids.forEach(
                                                (studentElement) => {
                                                    studentIds.push(studentElement.toString());
                                                },
                                            );
                                        });
                                    });
                                }
                            });
                        }
                    });
                });
            });
        });
        studentGroupInProgram?.forEach((studentGroupElement) => {
            studentGroupElement?.groups.forEach((groupElement) => {
                groupElement?.courses.forEach((courseElement) => {
                    courseElement?.setting.forEach((settingElement) => {
                        settingElement?.session_setting.forEach((sessionElement) => {
                            sessionElement?.groups.forEach((sessionGroupElement) => {
                                sessionGroupElement._student_ids.forEach((studentElement) => {
                                    studentIds.push(studentElement.toString());
                                });
                            });
                        });
                    });
                });
            });
        });
        const staffDetails = [];
        if (type === 'staff' && announcementUserList?.selectedPrograms?.length) {
            staffData = await courseScheduledSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        _program_id: {
                            $in: announcementUserList.selectedPrograms.map((programElement) =>
                                convertToMongoObjectId(programElement),
                            ),
                        },
                    },
                    { _id: 0, 'staffs._staff_id': 1 },
                )
                .lean();
            staffDetails.push(...staffData);
        }
        if (type === 'staff' && schedulesQuery?.length) {
            staffData = await courseScheduledSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: schedulesQuery,
                    },
                    { _id: 0, 'staffs._staff_id': 1 },
                )
                .lean();
            staffDetails.push(...staffData);
        }
        staffDetails.forEach((staffElement) => {
            staffElement.staffs.forEach((staffIdElement) => {
                studentIds.push(staffIdElement._staff_id.toString());
            });
        });
        uniqueStudentIds = [...new Set(studentIds)];
        return uniqueStudentIds;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getAcademicStaffDetails = async ({ selectedProgram }) => {
    try {
        const schedulesQuery = [];
        let staffIds = [];
        selectedProgram.forEach((selectedElement) => {
            selectedElement.programList.forEach((programElement) => {
                programElement.level.forEach((levelElement) => {
                    if (levelElement?.rotation === 'no') {
                        levelElement.course.forEach((courseElement) => {
                            schedulesQuery.push({
                                _institution_calendar_id: convertToMongoObjectId(
                                    selectedElement._institution_calendar_id,
                                ),
                                _program_id: convertToMongoObjectId(programElement._program_id),
                                term: levelElement.term,
                                year_no: levelElement.year,
                                level_no: levelElement.level_no,
                                _course_id: convertToMongoObjectId(courseElement._course_id),
                            });
                        });
                    } else {
                        levelElement.rotation_course.forEach((rotationCourseElement) => {
                            rotationCourseElement.course.forEach((courseElement) => {
                                schedulesQuery.push({
                                    _institution_calendar_id: convertToMongoObjectId(
                                        selectedElement._institution_calendar_id,
                                    ),
                                    _program_id: convertToMongoObjectId(programElement._program_id),
                                    term: levelElement.term,
                                    year_no: levelElement.year,
                                    level_no: levelElement.level_no,
                                    rotation: levelElement.rotation,
                                    _course_id: convertToMongoObjectId(courseElement._course_id),
                                    rotation_count: rotationCourseElement.rotation_count,
                                });
                            });
                        });
                    }
                });
            });
        });
        if (schedulesQuery.length) {
            const staffDetails = await courseScheduledSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: schedulesQuery,
                    },
                    {
                        _id: 1,
                        'staffs._staff_id': 1,
                        _institution_calendar_id: 1,
                        _program_id: 1,
                        term: 1,
                        year_no: 1,
                        level_no: 1,
                        _course_id: 1,
                        rotation_count: 1,
                        rotation: 1,
                    },
                )
                .populate({
                    path: 'staffs._staff_id',
                    select: {
                        gender: 1,
                        'employment.user_employment_type': 1,
                        staff_employment_type: 1,
                        isDeleted: 1,
                        status: 1,
                    },
                })
                .lean();
            selectedProgram.forEach((selectedElement) => {
                selectedElement?.programList.forEach((programElement) => {
                    programElement?.level.forEach((levelElement) => {
                        staffDetails?.forEach((staffElement) => {
                            const matchingIds =
                                selectedElement._institution_calendar_id.toString() ===
                                    staffElement._institution_calendar_id.toString() &&
                                programElement._program_id.toString() ===
                                    staffElement._program_id.toString() &&
                                levelElement.term.toString() === staffElement.term.toString() &&
                                levelElement.year.toString() === staffElement.year_no.toString() &&
                                levelElement.level_no.toString() ===
                                    staffElement.level_no.toString();
                            if (matchingIds) {
                                if (
                                    levelElement.rotation === 'no' &&
                                    staffElement.rotation === 'no'
                                ) {
                                    levelElement.course.forEach((courseElement) => {
                                        if (
                                            courseElement._course_id.toString() ===
                                            staffElement._course_id.toString()
                                        ) {
                                            staffElement.staffs.forEach((staffIdElement) => {
                                                const employmentType =
                                                    staffIdElement._staff_id.staff_employment_type;
                                                if (
                                                    (courseElement.selectedAll &&
                                                        (employmentType === ACADEMIC ||
                                                            employmentType === BOTH) &&
                                                        !staffIdElement._staff_id.isDeleted &&
                                                        staffIdElement._staff_id.status ===
                                                            'completed') ||
                                                    (courseElement.selectedCourse.length &&
                                                        courseElement.selectedCourse.some(
                                                            (selectedCourseElement) => {
                                                                const [
                                                                    userEmploymentType,
                                                                    userGender,
                                                                ] =
                                                                    selectedCourseElement.split(
                                                                        '-',
                                                                    );
                                                                return (
                                                                    !staffIdElement._staff_id
                                                                        .isDeleted &&
                                                                    staffIdElement._staff_id
                                                                        .status === 'completed' &&
                                                                    staffIdElement._staff_id.gender.toLowerCase() ===
                                                                        userGender.toLowerCase() &&
                                                                    staffIdElement._staff_id.employment.user_employment_type.toLowerCase() ===
                                                                        userEmploymentType.toLowerCase() &&
                                                                    (employmentType === ACADEMIC ||
                                                                        employmentType === BOTH)
                                                                );
                                                            },
                                                        ))
                                                ) {
                                                    staffIds.push({
                                                        _id: staffIdElement._staff_id._id.toString(),
                                                    });
                                                }
                                            });
                                        }
                                    });
                                } else if (
                                    levelElement.rotation === 'yes' &&
                                    staffElement.rotation === 'yes'
                                ) {
                                    levelElement.rotation_course.forEach(
                                        (rotationCourseElement) => {
                                            rotationCourseElement.course.forEach(
                                                (courseElement) => {
                                                    if (
                                                        courseElement._course_id.toString() ===
                                                            staffElement._course_id.toString() &&
                                                        rotationCourseElement.rotation_count ===
                                                            staffElement.rotation_count
                                                    ) {
                                                        staffElement.staffs.forEach(
                                                            (staffIdElement) => {
                                                                const employmentType =
                                                                    staffIdElement._staff_id
                                                                        .staff_employment_type;
                                                                if (
                                                                    (courseElement.selectedAll &&
                                                                        !staffIdElement._staff_id
                                                                            .isDeleted &&
                                                                        staffIdElement._staff_id
                                                                            .status ===
                                                                            'completed' &&
                                                                        (employmentType ===
                                                                            ACADEMIC ||
                                                                            employmentType ===
                                                                                BOTH)) ||
                                                                    (courseElement.selectedCourse
                                                                        .length &&
                                                                        courseElement.selectedCourse.some(
                                                                            (
                                                                                selectedCourseElement,
                                                                            ) => {
                                                                                const [
                                                                                    userEmploymentType,
                                                                                    userGender,
                                                                                ] =
                                                                                    selectedCourseElement.split(
                                                                                        '-',
                                                                                    );
                                                                                return (
                                                                                    !staffIdElement
                                                                                        ._staff_id
                                                                                        .isDeleted &&
                                                                                    staffIdElement
                                                                                        ._staff_id
                                                                                        .status ===
                                                                                        'completed' &&
                                                                                    staffIdElement._staff_id.gender.toLowerCase() ===
                                                                                        userGender.toLowerCase() &&
                                                                                    staffIdElement._staff_id.employment.user_employment_type.toLowerCase() ===
                                                                                        userEmploymentType.toLowerCase() &&
                                                                                    (employmentType ===
                                                                                        ACADEMIC ||
                                                                                        employmentType ===
                                                                                            BOTH)
                                                                                );
                                                                            },
                                                                        ))
                                                                ) {
                                                                    staffIds.push({
                                                                        _id: staffIdElement._staff_id._id.toString(),
                                                                    });
                                                                }
                                                            },
                                                        );
                                                    }
                                                },
                                            );
                                        },
                                    );
                                }
                            }
                        });
                    });
                });
            });
        }
        if (staffIds?.length) {
            staffIds = filterUniqueId({ userDetails: staffIds });
        }
        return staffIds;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getProgramUserDetails = async ({ selectedProgram }) => {
    try {
        const schedulesQuery = [];
        const studentGroupQuery = [];
        const studentIDs = [];
        const staffIds = [];
        let userIds = [];
        selectedProgram.forEach((selectedElement) => {
            selectedElement?.programList.forEach((programElement) => {
                programElement?.level.forEach((levelElement) => {
                    if (levelElement.rotation === 'no') {
                        levelElement.course.forEach((courseElement) => {
                            schedulesQuery.push({
                                _institution_calendar_id: convertToMongoObjectId(
                                    selectedElement._institution_calendar_id,
                                ),
                                _program_id: convertToMongoObjectId(programElement._program_id),
                                term: levelElement.term,
                                year_no: levelElement.year,
                                level_no: levelElement.level_no,
                                _course_id: convertToMongoObjectId(courseElement._course_id),
                            });
                            studentGroupQuery.push({
                                _institution_calendar_id: convertToMongoObjectId(
                                    selectedElement._institution_calendar_id,
                                ),
                                'master._program_id': convertToMongoObjectId(
                                    programElement._program_id,
                                ),
                                'master.year': levelElement.year,
                                'groups.term': levelElement.term,
                                'groups.level': levelElement.level_no,
                                'groups.curriculum': levelElement.curriculum,
                                'groups.rotation': 'no',
                                'groups.courses._course_id': convertToMongoObjectId(
                                    courseElement._course_id,
                                ),
                            });
                        });
                    } else {
                        levelElement.rotation_course.forEach((rotationCourseElement) => {
                            rotationCourseElement.course.forEach((courseElement) => {
                                schedulesQuery.push({
                                    _institution_calendar_id: convertToMongoObjectId(
                                        selectedElement._institution_calendar_id,
                                    ),
                                    _program_id: convertToMongoObjectId(programElement._program_id),
                                    term: levelElement.term,
                                    year_no: levelElement.year,
                                    level_no: levelElement.level_no,
                                    rotation: levelElement.rotation,
                                    _course_id: convertToMongoObjectId(courseElement._course_id),
                                    rotation_count: rotationCourseElement.rotation_count,
                                });
                                studentGroupQuery.push({
                                    _institution_calendar_id: convertToMongoObjectId(
                                        selectedElement._institution_calendar_id,
                                    ),
                                    'master._program_id': convertToMongoObjectId(
                                        programElement._program_id,
                                    ),
                                    'master.year': levelElement.year,
                                    'groups.term': levelElement.term,
                                    'groups.level': levelElement.level_no,
                                    'groups.curriculum': levelElement.curriculum,
                                    'groups.rotation': levelElement.rotation,
                                    'groups.courses.setting._group_no':
                                        rotationCourseElement.rotation_count,
                                    'groups.courses._course_id': convertToMongoObjectId(
                                        courseElement._course_id,
                                    ),
                                });
                            });
                        });
                    }
                });
            });
        });
        if (studentGroupQuery.length) {
            const studentGroupData = await studentGroupSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: studentGroupQuery,
                    },
                    {
                        _institution_calendar_id: 1,
                        'master._program_id': 1,
                        'master.year': 1,
                        'groups.group_mode': 1,
                        'groups.level': 1,
                        'groups.term': 1,
                        'groups.curriculum': 1,
                        'groups.courses._course_id': 1,
                        'groups.courses.setting._group_no': 1,
                        'groups.courses.setting.gender': 1,
                        'groups.courses.setting.session_setting.group_name': 1,
                        'groups.courses.setting.session_setting.groups': 1,
                        'groups.group_setting.gender': 1,
                        'groups.group_setting.groups': 1,
                        'groups.rotation_group_setting': 1,
                    },
                )
                .lean();
            selectedProgram?.forEach((selectedElement) => {
                selectedElement?.programList.forEach((programElement) => {
                    programElement?.level.forEach((levelElement) => {
                        studentGroupData?.forEach((studentGroupElement) => {
                            studentGroupElement.groups.forEach((groupElement) => {
                                const matchingIds =
                                    studentGroupElement._institution_calendar_id.toString() ===
                                        selectedElement._institution_calendar_id.toString() &&
                                    studentGroupElement.master._program_id.toString() ===
                                        programElement._program_id.toString() &&
                                    groupElement.term === levelElement.term &&
                                    studentGroupElement.master.year === levelElement.year &&
                                    groupElement.level === levelElement.level_no &&
                                    groupElement.curriculum === levelElement.curriculum;
                                if (matchingIds) {
                                    if (
                                        groupElement.group_mode === COURSE ||
                                        groupElement.group_mode === FYD
                                    ) {
                                        levelElement.course.forEach((courseElement) => {
                                            groupElement.courses.forEach((groupCourseElement) => {
                                                if (
                                                    groupCourseElement._course_id.toString() ===
                                                    courseElement._course_id.toString()
                                                ) {
                                                    if (courseElement.selectedAll) {
                                                        groupCourseElement.setting.forEach(
                                                            (settingElement) => {
                                                                settingElement.session_setting.forEach(
                                                                    (sessionElement) => {
                                                                        sessionElement.groups.forEach(
                                                                            (
                                                                                sessionGroupElement,
                                                                            ) => {
                                                                                sessionGroupElement._student_ids.forEach(
                                                                                    (
                                                                                        studentElement,
                                                                                    ) => {
                                                                                        studentIDs.push(
                                                                                            {
                                                                                                _id: studentElement.toString(),
                                                                                            },
                                                                                        );
                                                                                    },
                                                                                );
                                                                            },
                                                                        );
                                                                    },
                                                                );
                                                            },
                                                        );
                                                    }
                                                    if (courseElement.selectedCourse) {
                                                        courseElement.selectedCourse.forEach(
                                                            (selectedCourseElement) => {
                                                                groupCourseElement.setting.forEach(
                                                                    (settingElement) => {
                                                                        settingElement.session_setting.forEach(
                                                                            (sessionElement) => {
                                                                                const [
                                                                                    splitGender,
                                                                                    userType,
                                                                                ] =
                                                                                    selectedCourseElement.split(
                                                                                        ' ',
                                                                                    );
                                                                                let regularGroupName =
                                                                                    settingElement.gender ===
                                                                                    BOTH
                                                                                        ? 'MFG-1'
                                                                                        : settingElement.gender ===
                                                                                          MALE
                                                                                        ? 'MG-1'
                                                                                        : 'FG-1';

                                                                                if (
                                                                                    groupElement.group_mode ===
                                                                                    FYD
                                                                                ) {
                                                                                    const rotationGroupName =
                                                                                        sessionElement.group_name
                                                                                            .split(
                                                                                                '-',
                                                                                            )
                                                                                            .slice(
                                                                                                -2,
                                                                                                -1,
                                                                                            )[0];
                                                                                    splitGenderName =
                                                                                        settingElement.gender
                                                                                            .slice(
                                                                                                0,
                                                                                                1,
                                                                                            )
                                                                                            .toUpperCase();
                                                                                    regularGroupName =
                                                                                        splitGenderName +
                                                                                        rotationGroupName;

                                                                                    selectedCourseElement =
                                                                                        selectedCourseElement.replace(
                                                                                            '-',
                                                                                            '',
                                                                                        );
                                                                                }

                                                                                if (
                                                                                    (splitGender &&
                                                                                        settingElement.gender &&
                                                                                        settingElement.gender.toLowerCase() ===
                                                                                            splitGender.toLowerCase() &&
                                                                                        userType &&
                                                                                        userType.toLowerCase() ===
                                                                                            STUDENT) ||
                                                                                    selectedCourseElement ===
                                                                                        regularGroupName
                                                                                ) {
                                                                                    sessionElement.groups.forEach(
                                                                                        (
                                                                                            sessionGroupElement,
                                                                                        ) => {
                                                                                            sessionGroupElement._student_ids.forEach(
                                                                                                (
                                                                                                    studentElement,
                                                                                                ) => {
                                                                                                    studentIDs.push(
                                                                                                        {
                                                                                                            _id: studentElement.toString(),
                                                                                                        },
                                                                                                    );
                                                                                                },
                                                                                            );
                                                                                        },
                                                                                    );
                                                                                }
                                                                            },
                                                                        );
                                                                    },
                                                                );
                                                            },
                                                        );
                                                    }
                                                }
                                            });
                                        });
                                    }
                                    if (groupElement.group_mode === ROTATION) {
                                        levelElement.rotation_course.forEach(
                                            (rotationCourseElement) => {
                                                rotationCourseElement.course.forEach(
                                                    (courseElement) => {
                                                        groupElement.courses.forEach(
                                                            (groupCourseElement) => {
                                                                groupCourseElement.setting.forEach(
                                                                    (settingElement) => {
                                                                        if (
                                                                            courseElement.selectedAll &&
                                                                            rotationCourseElement.rotation_count ===
                                                                                settingElement._group_no
                                                                        ) {
                                                                            settingElement.session_setting.forEach(
                                                                                (
                                                                                    sessionElement,
                                                                                ) => {
                                                                                    sessionElement.groups.forEach(
                                                                                        (
                                                                                            sessionGroupElement,
                                                                                        ) => {
                                                                                            sessionGroupElement._student_ids.forEach(
                                                                                                (
                                                                                                    studentElement,
                                                                                                ) => {
                                                                                                    studentIDs.push(
                                                                                                        {
                                                                                                            _id: studentElement.toString(),
                                                                                                        },
                                                                                                    );
                                                                                                },
                                                                                            );
                                                                                        },
                                                                                    );
                                                                                },
                                                                            );
                                                                        }
                                                                        if (
                                                                            courseElement.selectedCourse
                                                                        ) {
                                                                            courseElement.selectedCourse.forEach(
                                                                                (
                                                                                    selectedCourseElement,
                                                                                ) => {
                                                                                    settingElement.session_setting.forEach(
                                                                                        (
                                                                                            sessionElement,
                                                                                        ) => {
                                                                                            const rotationGroupName =
                                                                                                sessionElement.group_name
                                                                                                    .split(
                                                                                                        '-',
                                                                                                    )
                                                                                                    .slice(
                                                                                                        -2,
                                                                                                        -1,
                                                                                                    )[0];
                                                                                            const splitGenderName =
                                                                                                settingElement.gender
                                                                                                    .slice(
                                                                                                        0,
                                                                                                        1,
                                                                                                    )
                                                                                                    .toUpperCase();
                                                                                            const joinGroupName =
                                                                                                splitGenderName +
                                                                                                '-R' +
                                                                                                rotationGroupName;

                                                                                            const [
                                                                                                splitGender,
                                                                                                userType,
                                                                                            ] =
                                                                                                selectedCourseElement.split(
                                                                                                    ' ',
                                                                                                );

                                                                                            if (
                                                                                                selectedCourseElement ===
                                                                                                    joinGroupName ||
                                                                                                (splitGender &&
                                                                                                    splitGender.toLowerCase() ===
                                                                                                        (settingElement.gender &&
                                                                                                            settingElement.gender.toLowerCase()) &&
                                                                                                    userType &&
                                                                                                    userType.toLowerCase() ===
                                                                                                        STUDENT &&
                                                                                                    rotationCourseElement.rotation_count ===
                                                                                                        settingElement._group_no)
                                                                                            ) {
                                                                                                sessionElement.groups.forEach(
                                                                                                    (
                                                                                                        sessionGroupElement,
                                                                                                    ) => {
                                                                                                        sessionGroupElement._student_ids.forEach(
                                                                                                            (
                                                                                                                studentElement,
                                                                                                            ) => {
                                                                                                                studentIDs.push(
                                                                                                                    {
                                                                                                                        _id: studentElement.toString(),
                                                                                                                    },
                                                                                                                );
                                                                                                            },
                                                                                                        );
                                                                                                    },
                                                                                                );
                                                                                            }
                                                                                        },
                                                                                    );
                                                                                },
                                                                            );
                                                                        }
                                                                    },
                                                                );
                                                            },
                                                        );
                                                    },
                                                );
                                            },
                                        );
                                    }
                                }
                            });
                        });
                    });
                });
            });
        }
        //get staffId
        if (schedulesQuery.length) {
            const staffDetails = await courseScheduledSchema
                .find(
                    {
                        isActive: true,
                        isDeleted: false,
                        $or: schedulesQuery,
                    },
                    {
                        _id: 1,
                        'staffs._staff_id': 1,
                        _institution_calendar_id: 1,
                        _program_id: 1,
                        term: 1,
                        year_no: 1,
                        level_no: 1,
                        _course_id: 1,
                        rotation_count: 1,
                        rotation: 1,
                    },
                )
                .populate({
                    path: 'staffs._staff_id',
                    select: {
                        gender: 1,
                    },
                })
                .lean();
            selectedProgram.forEach((selectedElement) => {
                selectedElement.programList.forEach((programElement) => {
                    programElement.level.forEach((levelElement) => {
                        staffDetails.forEach((staffElement) => {
                            const matchingIds =
                                selectedElement._institution_calendar_id.toString() ===
                                    staffElement._institution_calendar_id.toString() &&
                                programElement._program_id.toString() ===
                                    staffElement._program_id.toString() &&
                                levelElement.term.toString() === staffElement.term.toString() &&
                                levelElement.year.toString() === staffElement.year_no.toString() &&
                                levelElement.level_no.toString() ===
                                    staffElement.level_no.toString();

                            if (matchingIds) {
                                if (
                                    levelElement.rotation === 'no' &&
                                    staffElement.rotation === 'no'
                                ) {
                                    levelElement.course.forEach((courseElement) => {
                                        if (
                                            courseElement._course_id.toString() ===
                                            staffElement._course_id.toString()
                                        ) {
                                            staffElement.staffs.forEach((staffIdElement) => {
                                                const isStaffSelected =
                                                    courseElement.selectedAll ||
                                                    courseElement.selectedCourse.some(
                                                        (selectedCourseElement) => {
                                                            const [splitGender, userType] =
                                                                selectedCourseElement.split(' ');
                                                            return (
                                                                typeof userType !== 'undefined' &&
                                                                userType.toLowerCase() === STAFF &&
                                                                staffIdElement._staff_id &&
                                                                typeof staffIdElement._staff_id
                                                                    .gender !== 'undefined' &&
                                                                staffIdElement._staff_id.gender.toLowerCase() ===
                                                                    (typeof splitGender !==
                                                                        'undefined' &&
                                                                        splitGender.toLowerCase())
                                                            );
                                                        },
                                                    );
                                                if (isStaffSelected) {
                                                    staffIds.push({
                                                        _id: staffIdElement._staff_id._id.toString(),
                                                    });
                                                }
                                            });
                                        }
                                    });
                                } else if (
                                    levelElement.rotation === 'yes' &&
                                    staffElement.rotation === 'yes'
                                ) {
                                    levelElement.rotation_course.forEach(
                                        (rotationCourseElement) => {
                                            rotationCourseElement.course.forEach(
                                                (courseElement) => {
                                                    if (
                                                        courseElement._course_id.toString() ===
                                                            staffElement._course_id.toString() &&
                                                        rotationCourseElement.rotation_count ===
                                                            staffElement.rotation_count
                                                    ) {
                                                        staffElement.staffs.forEach(
                                                            (staffIdElement) => {
                                                                const isStaffSelected =
                                                                    courseElement.selectedAll ||
                                                                    courseElement.selectedCourse.some(
                                                                        (selectedCourseElement) => {
                                                                            const [
                                                                                splitGender,
                                                                                userType,
                                                                            ] =
                                                                                selectedCourseElement.split(
                                                                                    ' ',
                                                                                );
                                                                            return (
                                                                                typeof userType !==
                                                                                    'undefined' &&
                                                                                userType.toLowerCase() ===
                                                                                    STAFF &&
                                                                                staffIdElement._staff_id &&
                                                                                typeof staffIdElement
                                                                                    ._staff_id
                                                                                    .gender !==
                                                                                    'undefined' &&
                                                                                staffIdElement._staff_id.gender.toLowerCase() ===
                                                                                    (typeof splitGender !==
                                                                                        'undefined' &&
                                                                                        splitGender.toLowerCase())
                                                                            );
                                                                        },
                                                                    );
                                                                if (isStaffSelected) {
                                                                    staffIds.push({
                                                                        _id: staffIdElement._staff_id._id.toString(),
                                                                    });
                                                                }
                                                            },
                                                        );
                                                    }
                                                },
                                            );
                                        },
                                    );
                                }
                            }
                        });
                    });
                });
            });
        }
        userIds.push(...staffIds, ...studentIDs);
        if (userIds?.length) {
            userIds = filterUniqueId({ userDetails: userIds });
        }
        return userIds;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    uploadDocument,
    getSignedUrl,
    getSizeOfUrl,
    sendAnnouncement,
    getUserList,
    getSelectedUserProgram,
    getAcademicStaffDetails,
    getProgramUserDetails,
    filterUniqueId,
};
