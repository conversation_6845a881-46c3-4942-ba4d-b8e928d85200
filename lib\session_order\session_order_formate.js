const program_formate = require('../program/program_formate');
const course_formate = require('../course/course_formate');
const subject_formate = require('../department_subject/department_subject_formate');
exports.session_order_ID_Only = (doc) => {
    //console.log(doc);
    let obj = {
        _id: doc._id,
        s_no: doc.s_no,
        delivery_symbol: doc.delivery_symbol,
        delivery_no: doc.delivery_no,
        session_name: doc.session_name,
        subject: doc._subject_id,
        contact_hours: doc.contact_hours,
        week: doc.week,
        course: doc._course_id,
        program: doc._program_id,
        isActive: doc.isActive,
        isDeleted: doc.isDeleted
    }
    return obj;
}

module.exports = {
    session_order: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                s_no: element.s_no,
                delivery_symbol: element.delivery_symbol,
                delivery_no: element.delivery_no,
                session_name: element.session_name,
                subject: subject_formate.department_subject_ID_Array_Only(element.subject),
                contact_hours: element.contact_hours,
                week: element.week,
                course: course_formate.course_ID_Only(element.course),
                program: program_formate.program_ID_Only(element.program),
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    session_order_ID: (doc) => {
        let obj = {
            _id: doc._id,
            s_no: doc.s_no,
            delivery_symbol: doc.delivery_symbol,
            delivery_no: doc.delivery_no,
            session_name: doc.session_name,
            subject: subject_formate.department_subject_ID_Array_Only(doc.subject),
            contact_hours: doc.contact_hours,
            week: doc.week,
            course: course_formate.course_ID_Only(doc.course),
            program: program_formate.program_ID_Only(doc.program),
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    session_order_ID_Onlys: function (doc) {
        let obj = {
            _id: doc._id,
            s_no: doc.s_no,
            delivery_symbol: doc.delivery_symbol,
            delivery_no: doc.delivery_no,
            session_name: doc.session_name,
            subject: doc._subject_id,
            contact_hours: doc.contact_hours,
            week: doc.week,
            course: doc._course_id,
            program: doc._program_id,
            isActive: doc.isActive,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    session_order_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                s_no: element.s_no,
                delivery_symbol: element.delivery_symbol,
                delivery_no: element.delivery_no,
                session_name: element.session_name,
                subject: element._subject_id,
                contact_hours: element.contact_hours,
                week: element.week,
                course: element._course_id,
                program: element._program_id,
                isActive: element.isActive,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}