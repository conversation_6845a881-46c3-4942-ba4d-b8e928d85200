#!/usr/bin/env node

/**
 * Module dependencies.
 */

require('dotenv').config();
const elasticApm = require('../config/elastic-apm.config');
elasticApm.initApm();

var http = require('http');
var app = (module.exports = require('../index'));

/**
 * Get port from environment and store in Express.
 */

var port = normalizePort(process.env.PORT || '3000');
app.set('port', port);

/**
 * Create HTTP server.
 */

var server = http.createServer(app);

/**
 * Listen on provided port, on all network interfaces.
 */

server
    .listen(app.get('port'), function () {
        console.log('Application started on port %d', app.get('port'));
    })
    .on('error', onError)
    .on('listening', onListening)
    .on('connection', (socket) => {
        console.log('new connection initiated');
        server.getConnections((err, count) => {
            if (err) {
                console.error('err on getConnections:', err);
            }
            console.log('connections=', count);
        });
    })
    .on('clientError', (err, socket) => {
        if (err) {
            console.error('clientError:', err);
        }

        // socket.end('HTTP/1.1 400 Bad Request')
    });

// keepAliveTimeout should be greater than AWS ALB's idle timeout
// to avoid 502 - Bad Gateway & 504 - Gateway Timeout errors
server.keepAliveTimeout = (+process.env.NODE_KEEPALIVE_TIMEOUT || 61) * 1000; // default 5000 (5s)
// headersTimeout should be greater than keepAliveTimeout https://github.com/nodejs/node/issues/27363
server.headersTimeout = (+process.env.NODE_HEADERS_TIMEOUT || 65) * 1000; // default 60000 (60s)

/**
 * Normalize a port into a number, string, or false.
 */

function normalizePort(val) {
    var port = parseInt(val, 10);

    if (isNaN(port)) {
        // named pipe
        return val;
    }

    if (port >= 0) {
        // port number
        return port;
    }

    return false;
}

/**
 * Event listener for HTTP server "error" event.
 */

function onError(error) {
    if (error.syscall !== 'listen') {
        throw error;
    }

    var bind = typeof port === 'string' ? 'Pipe ' + port : 'Port ' + port;

    // handle specific listen errors with friendly messages
    switch (error.code) {
        case 'EACCES':
            console.error(bind + ' requires elevated privileges');
            process.exit(1);
            break;
        case 'EADDRINUSE':
            console.error(bind + ' is already in use');
            process.exit(1);
            break;
        default:
            throw error;
    }
}

/**
 * Event listener for HTTP server "listening" event.
 */

function onListening() {
    var addr = server.address();
    var bind = typeof addr === 'string' ? 'pipe ' + addr : 'port ' + addr.port;
    console.log('Listening on %s', bind);
}
