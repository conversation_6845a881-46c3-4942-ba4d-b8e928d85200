const { convertToMongoObjectId, query: commonQuery } = require('../../../utility/common');
const {
    LOCAL,
    DIGI_SESSION_ORDER,
    USER,
    STUDENT_GROUP,
    DIGI_SESSION_DELIVERY_TYPES,
    COURSE_SCHEDULE,
    ROLE_ASSIGNS,
    PROGRAM_CALENDAR,
    RAT,
    IRAT,
    TRAT,
} = require('../../../utility/constants');
const studentGroupSchema = require('mongoose').model(STUDENT_GROUP);
const sessionDeliveryTypeSchema = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
// const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const sessionSchema = require('mongoose').model(DIGI_SESSION_ORDER);
const userSchema = require('mongoose').model(USER);
const courseScheduleSchema = require('mongoose').model(COURSE_SCHEDULE);
const roleAssignSchema = require('mongoose').model(ROLE_ASSIGNS);
const ProgramCalendar = require('mongoose').model(PROGRAM_CALENDAR);
const {
    listSessionFormatting,
    getStudentGroupListsFormatting,
    getStudentCourseGroupListsFormatting,
    getInstructorFormatting,
} = require('../../serviceAdapter/adapter.formatter');
const assignmentSchema = require('./assignment.model');
const assignmentCommentsSchema = require('../assignment_comments/assignment_comments.model');
const assignmentAnswerSchema = require('../assignment_answer/assignment_answer.model');
const { groupBy } = require('lodash');
const { getSignedUrl } = require('../assignment-settings/assignment-settings.util');

const getSessionLists = async ({
    _institution_id,
    _course_id,
    _user_id,
    isCoordinator,
    _institution_calendar_id,
}) => {
    try {
        // let sessionList;
        // if (BASIC_DATA_FROM === LOCAL) {
        sessionList = await sessionSchema
            .findOne(
                {
                    ...commonQuery,
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _course_id: convertToMongoObjectId(_course_id),
                },
                {
                    _id: 0,
                    'session_flow_data._id': 1,
                    'session_flow_data._session_id': 1,
                    'session_flow_data.delivery_no': 1,
                    'session_flow_data.delivery_symbol': 1,
                    'session_flow_data.delivery_topic': 1,
                },
            )
            .lean();
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        if (!isCoordinator) {
            const courseSchedules = await courseScheduleSchema
                .find(
                    {
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _course_id: convertToMongoObjectId(_course_id),
                        'staffs._staff_id': convertToMongoObjectId(_user_id),
                        type: 'regular',
                        isActive: true,
                        isDeleted: false,
                    },
                    { _id: 0, 'session._session_id': 1 },
                )
                .lean();
            const sessionListArray = sessionList.session_flow_data.filter((sessionElement) =>
                courseSchedules.find(
                    (courseElement) =>
                        courseElement.session._session_id.toString() ===
                        sessionElement._id.toString(),
                ),
            );
            if (sessionListArray.length)
                return listSessionFormatting({
                    sessionLists: sessionListArray,
                    // dataFrom: BASIC_DATA_FROM,
                });
            return null;
        }
        if (sessionList)
            return listSessionFormatting({
                sessionLists: sessionList.session_flow_data,
                // dataFrom: BASIC_DATA_FROM,
            });
        return null;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentGroupLists = async ({
    _institution_id,
    _institution_calendar_id,
    _program_id,
    _course_id,
    term,
}) => {
    // let studentGroupData;
    // let sessionDeliveryType;
    // if (BASIC_DATA_FROM === LOCAL) {
    sessionDeliveryType = await sessionDeliveryTypeSchema.find(
        {
            isDeleted: false,
            _program_id: convertToMongoObjectId(_program_id),
        },
        { delivery_types: 1 },
    );
    studentGroupData = await studentGroupSchema
        .findOne({
            isDeleted: false,
            'master._program_id': convertToMongoObjectId(_program_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            'groups.courses._course_id': convertToMongoObjectId(_course_id),
        })
        .lean();
    // }
    return getStudentGroupListsFormatting({
        studentGroupData,
        sessionDeliveryType,
        _course_id,
        term,
        // dataFrom: BASIC_DATA_FROM,
    });
};

const getStudentCourseGroupLists = async ({
    _institution_id,
    _institution_calendar_id,
    _program_id,
    _course_id,
    term,
    deliverySymbol,
}) => {
    // let studentGroupData;
    // let sessionDeliveryType;
    // if (BASIC_DATA_FROM === LOCAL) {
    sessionDeliveryType = await sessionDeliveryTypeSchema.find(
        {
            isDeleted: false,
            _program_id: convertToMongoObjectId(_program_id),
        },
        { delivery_types: 1 },
    );
    studentGroupData = await studentGroupSchema
        .findOne({
            isDeleted: false,
            'master._program_id': convertToMongoObjectId(_program_id),
            _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
            'groups.courses._course_id': convertToMongoObjectId(_course_id),
        })
        .lean();
    // }
    return getStudentCourseGroupListsFormatting({
        studentGroupData,
        sessionDeliveryType,
        _course_id,
        term,
        deliverySymbol,
        // dataFrom: BASIC_DATA_FROM,
    });
};

const getInstructorLists = async ({ _program_id, subjectIds, searchKey }) => {
    try {
        // let userLists;
        subjectIds = subjectIds.map((i) => convertToMongoObjectId(i));
        // if (BASIC_DATA_FROM === LOCAL) {
        userLists = await userSchema
            .find(
                {
                    'academic_allocation._program_id': convertToMongoObjectId(_program_id),
                    'academic_allocation._department_subject_id': { $in: subjectIds },
                    ...(searchKey && {
                        $or: [
                            { 'name.first': { $regex: searchKey, $options: 'i' } },
                            { 'name.middle': { $regex: searchKey, $options: 'i' } },
                            { 'name.last': { $regex: searchKey, $options: 'i' } },
                        ],
                    }),
                },
                { _id: 1, name: 1, user_id: 1 },
            )
            .lean();
        // } else {
        // AXIOS Call has to be here for Get Data from Other Environments
        // }
        return getInstructorFormatting({
            userLists,
            // dataFrom: BASIC_DATA_FROM,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCommentList = async ({ _institution_id, studentId, allStudentData }) => {
    const comments = await assignmentCommentsSchema
        .find(
            {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                ...(studentId && { studentId: convertToMongoObjectId(studentId) }),
            },
            { assignmentId: 1, comments: 1, studentId: 1 },
        )
        .lean();

    const groupedAssignmentData = groupBy(allStudentData, '_id');

    const commentsList = {};
    for (const commentElement of comments) {
        const { assignmentId, studentId: commentStudentId, comments: commentList } = commentElement;

        let needToGet = studentId;
        if (!studentId) {
            const assignmentStudentList = groupedAssignmentData[assignmentId];
            if (assignmentStudentList) {
                const groupedStudentData = groupBy(
                    assignmentStudentList[0].assignments,
                    'studentId',
                );
                const individualStudentData = groupedStudentData[commentStudentId];
                const status = individualStudentData[0] ? individualStudentData[0].status : '';
                needToGet = individualStudentData && status === 'submitted';
            }
        }
        if (needToGet) {
            const messageCount = commentList.length;
            const attachmentCount = commentList.reduce(
                (total, individualComment) => total + individualComment.attachments.length,
                0,
            );

            if (commentsList.hasOwnProperty(assignmentId)) {
                commentsList[assignmentId].messageCount += messageCount;
                commentsList[assignmentId].attachmentCount += attachmentCount;
            } else {
                commentsList[assignmentId] = { messageCount, attachmentCount };
            }
        }
    }
    return commentsList;
};

const includesCourse = ['After Course Starts', 'Before Course Ends'];
const includesSession = ['After Session Begins', 'After Session Ends'];

const getAssignmentDate = ({ individualAssignment, assignmentIndex }) => {
    const { submission, publishedAt, _course_id, _program_id, term } = individualAssignment;
    const { start, end, due } = submission;
    let allSessions = {};
    let allCourses = {};
    const getDate = ({ data, key, dueDate }) => {
        const { type, dateAndTime, courseOrSession, duration } = data;
        if (type === '') return '';
        if (type === 'Date') return dateAndTime;
        if (type === 'Duration') {
            const { type, value } = duration;
            if (type === 'After Assignment is Published') {
                const isoTime = new Date(publishedAt);
                isoTime.setDate(isoTime.getDate() + Number(value));
                return isoTime;
            }
            if (type === 'After Due') {
                return dueDate.setDate(dueDate.getDate() + value);
            }
            if (includesCourse.includes(type)) {
                allCourses = {
                    ...allCourses,
                    [`${assignmentIndex}+${type}+${key}+${value}`]: `${_program_id}+${_course_id}+${term}`,
                };
                return;
            }
            if (includesSession.includes(type)) {
                allSessions = {
                    ...allSessions,
                    [`${assignmentIndex}+${type}+${key}+${courseOrSession.after}`]: `${courseOrSession.type}+${_program_id}+${_course_id}+${term}`,
                };
                return;
            }
        }
        if (includesSession.includes(type))
            allSessions = {
                ...allSessions,
                [`${assignmentIndex}+${type}+${key}+${courseOrSession.after}`]: `${courseOrSession.type}+${_program_id}+${_course_id}+${term}`,
            };
    };
    const startDate = getDate({ data: start, key: 'startDate' });
    const dueDate = getDate({ data: due, key: 'dueDate' });
    const endDate = getDate({ data: end, key: 'endDate', dueDate });
    return { startDate, dueDate, endDate, allSessions, allCourses };
};
const submittedStatus = ['submitted', 'released'];
const getSubmissionData = async ({
    individualAssignment,
    allStudentData,
    assignmentIndex,
    getAllSubmissionData,
}) => {
    const { _id, submission } = individualAssignment;
    const matchedStudentIndex = allStudentData.findIndex(
        (assignmentElement) => assignmentElement._id.toString() === _id.toString(),
    );
    const studentData =
        matchedStudentIndex === -1 ? [] : allStudentData[matchedStudentIndex].assignments;
    const { startDate, dueDate, endDate, allSessions, allCourses } = getAssignmentDate({
        individualAssignment,
        assignmentIndex,
    });
    return {
        data: {
            submission: {
                ...(startDate !== '' && { startDate }),
                ...(dueDate !== '' && { dueDate }),
                ...(endDate !== '' && { endDate }),
                ...(getAllSubmissionData && submission),
                totalStudents: studentData.length,
                submitted: studentData.filter((studentElement) =>
                    submittedStatus.includes(studentElement.status.toLowerCase()),
                ).length,
            },
        },
        allSessions,
        allCourses,
    };
};
const updateAssignmentDate = (scheduleDate, typeSelect) => {
    if (!scheduleDate) return 'Assignment Not Started';
    let date = new Date(scheduleDate);
    const time = typeSelect.split(' ');
    switch (time[1]) {
        case 'Mins':
            date = new Date(date.setMinutes(date.getMinutes() + Number(time[0])));
            break;
        case 'Hour':
            date = new Date(date.setHours(date.getHours() + Number(time[0])));
            break;
        case 'Day':
            date = new Date(date.setDate(date.getDate() + Number(time[0])));
            break;
        default:
            break;
    }
    return date;
};

const emptyComment = { messageCount: 0, attachmentCount: 0 };
const updateDateBasedOnSession = async ({
    sessionIds,
    assignmentList,
    _institution_id,
    _institution_calendar_id,
}) => {
    const courseSchedules = await courseScheduleSchema
        .find(
            {
                'session._session_id': {
                    $in: Object.values(sessionIds).map((sessionId) => {
                        const [_session_id] = sessionId.split('+');

                        return convertToMongoObjectId(_session_id);
                    }),
                },
                _institution_id,
                _institution_calendar_id,
            },
            {
                session: 1,
                scheduleStartDateAndTime: '$sessionDetail.start_time',
                scheduleEndDateAndTime: '$sessionDetail.stop_time',
                _program_id: 1,
                _course_id: 1,
                term: 1,
            },
        )
        .lean();
    Object.keys(sessionIds).forEach((sessionKey) => {
        let date = 'invalid date';
        //`${_program_id}+${_course_id}+${term}+${courseOrSession.type}` ,
        const [_session_id, _program_id, _course_id, term] = sessionIds[sessionKey].split('+');
        const scheduleTime = courseSchedules.find((scheduleElement) => {
            return (
                String(scheduleElement._program_id) === String(_program_id) &&
                String(scheduleElement._course_id) === String(_course_id) &&
                String(scheduleElement.term) === String(term) &&
                String(scheduleElement.session._session_id) === String(_session_id)
            );
        });
        if (!scheduleTime) return;
        const [assignmentIndex, type, key, after] = sessionKey.split('+');
        if (type === 'After Session Begins')
            date = updateAssignmentDate(scheduleTime.scheduleStartDateAndTime, after);
        if (type === 'After Session Ends')
            date = updateAssignmentDate(scheduleTime.scheduleEndDateAndTime, after);
        //`${index}+${type}+${key}+${courseOrSession.after}`
        assignmentList[Number(assignmentIndex)].submission[key] = date;
    });
};

const updateCommentsAndDate = async ({
    assignmentList,
    _institution_id,
    allStudentData,
    allStudentAnswers,
    studentId,
    getAllSubmissionData = false,
}) => {
    const commentList = await getCommentList({ _institution_id, studentId, allStudentData });
    let assignmentIndex = 0;
    let sessionIds = {};
    let courseIds = {};
    for (const individualAssignment of assignmentList) {
        const { data, allSessions, allCourses } = await getSubmissionData({
            individualAssignment,
            allStudentData,
            assignmentIndex,
            getAllSubmissionData,
        });
        const matchedComments = commentList[individualAssignment._id]
            ? commentList[individualAssignment._id]
            : {};
        Object.assign(
            individualAssignment,
            data,
            commentList[individualAssignment._id] ? matchedComments : emptyComment,
            allStudentAnswers ? { studentData: allStudentAnswers[individualAssignment._id] } : {},
        );
        assignmentIndex++;
        sessionIds = { ...sessionIds, ...allSessions };
        courseIds = { ...courseIds, ...allCourses };
    }
    return { sessionIds, courseIds };
};
const assignmentQuery = {
    basic: 1,
    isDuplicate: 1,
    title: '$basic.title',
    isGroupProject: '$basic.isGroupProject',
    totalScore: '$basic.totalScore',
    gradeAs: '$basic.gradeAs',
    assignmentType: '$basic.type',
    scoringType: '$basic.scoringType',
    category: '$basic.category.name',
    evaluators: '$evaluation.evaluators',
    multiStaffMultiStudent: '$evaluation.multiStaffMultiStudent',
    isStudentsAnonymous: '$evaluation.isStudentsAnonymous',
    gradingScore: '$evaluation.gradingScore',
    submission: 1,
    isDraft: 1,
    pause: 1,
    cancel: 1,
    hide: 1,
    isDeleted: 1,
    isActive: 1,
    _course_id: 1,
    courseName: 1,
    programName: 1,
    subject: 1,
    _program_id: 1,
    term: 1,
    year_no: 1,
    level_no: 1,
    rotation_count: 1,
    publishedAt: 1,
    contributor: 1,
    createdBy: 1,
    course_code: 1,
    reportCreated: 1,
    ratType: 1,
    ratId: 1,
};

const promptsProject = {
    _id: 1,
    assignmentId: 1,
    parentPromptId: 1,
    childPromptIds: 1,
    createdAt: 1,
    updatedAt: 1,
    isDeleted: 1,
    promptText: 1,
    isReset: 1,
    isDuplicate: 1,
    _institution_id: 1,
    subjectId: 1,
    learningOutcome: 1,
    category: 1,
    score: 1,
    rubricsId: 1,
};
const updateDateBasedOnCourse = async ({ courseIds, assignmentList, _institution_calendar_id }) => {
    //`${index}+${type}+${key}+${value}`
    //`${_program_id}+${_course_id}+${term}`
    const _courseId_ = [];
    const _program_id = [];
    Object.values(courseIds).forEach((courseElement) => {
        const [programId, courseId, term] = courseElement.split('+');
        _courseId_.push(convertToMongoObjectId(courseId));
        _program_id.push(convertToMongoObjectId(programId));
    });
    const aggregate = [
        {
            $unwind: '$level',
        },
        {
            $unwind: '$level.course',
        },
        {
            $match: {
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                _program_id: { $in: _program_id },
                isActive: true,
                isDeleted: false,
                'level.course._course_id': { $in: _courseId_ },
            },
        },
    ];
    const programCalendar = await ProgramCalendar.aggregate(aggregate);
    Object.keys(courseIds).forEach((courseElement) => {
        const [assignmentIndex, type, key, after] = courseElement.split('+');
        const [programId, courseId, term] = courseIds[courseElement].split('+');
        const courseTime = programCalendar.find((programElement) => {
            return (
                String(programElement.level.course._course_id) === String(courseId) &&
                programElement.level.term === term
            );
        });
        if (!courseTime) return;
        let date = 'no date';
        if (type === 'After Course Starts')
            date = updateAssignmentDate(courseTime.level.course.start_date, after);
        if (type === 'Before Course Ends')
            date = updateAssignmentDate(courseTime.level.course.end_date, after);
        assignmentList[assignmentIndex].submission[key] = date;
    });
};
const constructAssignment = async ({
    _institution_id,
    staffId,
    _institution_calendar_id,
    assignmentList,
}) => {
    try {
        const assignmentIds = [];
        for (const individualAssignment of assignmentList) {
            assignmentIds.push(convertToMongoObjectId(individualAssignment._id));
        }
        const allStudentData = await assignmentAnswerSchema.aggregate([
            {
                $match: {
                    isDeleted: false,
                    assignmentId: { $in: assignmentIds },
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
            },
            {
                $group: {
                    _id: '$assignmentId',
                    assignments: { $push: '$$ROOT' },
                },
            },
        ]);
        const { sessionIds, courseIds } = await updateCommentsAndDate({
            assignmentList,
            _institution_id,
            allStudentData,
            getAllSubmissionData: false,
        });
        if (Object.keys(sessionIds).length) {
            await updateDateBasedOnSession({
                sessionIds,
                assignmentList,
                _institution_id,
                _institution_calendar_id,
            });
        }
        if (Object.keys(courseIds).length) {
            await updateDateBasedOnCourse({ courseIds, assignmentList, _institution_calendar_id });
        }

        return { assignmentList, todaysDate: new Date() };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getAssignmentList = async ({
    _institution_id,
    staffId,
    _institution_calendar_id,
    programAndCourseIds,
    assignmentData,
}) => {
    try {
        const { userProgramIds, userCourseIds, subjectIds, isCourseAdmin, isProgramAdmin } =
            programAndCourseIds;
        let queryBasedOnAdmins = [];
        if (isProgramAdmin && userProgramIds.length)
            queryBasedOnAdmins = [{ _program_id: { $in: userProgramIds } }];
        if (isCourseAdmin && userCourseIds.length)
            queryBasedOnAdmins = [{ _course_id: { $in: userCourseIds } }];
        if (subjectIds && subjectIds.length)
            queryBasedOnAdmins = [{ 'subject.subjectId': { $in: subjectIds } }];
        let assignmentList;
        if (!assignmentData) {
            assignmentList = await assignmentSchema
                .find(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                        isDeleted: false,
                        $or: [
                            ...queryBasedOnAdmins,
                            {
                                'evaluation.evaluators.evaluatorId':
                                    convertToMongoObjectId(staffId),
                            },
                        ],
                    },
                    assignmentQuery,
                )
                .sort({ createdAt: -1 })
                .lean();
        }

        return constructAssignment({
            _institution_id,
            staffId,
            _institution_calendar_id,
            assignmentList: assignmentData || assignmentList,
        });
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createNewAssignmentPrompts = ({
    assignmentPromptBulkCreate,
    assignmentPrompt,
    newAssignmentId,
    isDuplicateKey = false,
    isReset = false,
}) => {
    const assignmentParentPromptId = [];
    const parentPromptList = {};
    const childPromptList = {};

    for (const promptElement of assignmentPrompt) {
        const existingPromptId = promptElement._id;
        const promptObjectId = convertToMongoObjectId();
        delete promptElement.createdAt;
        delete promptElement.updatedAt;
        promptElement.isDeleted = false;
        promptElement._id = promptObjectId;
        promptElement.isReset = isReset;
        promptElement.isDuplicate = isDuplicateKey;
        promptElement.assignmentId = newAssignmentId;
        assignmentPromptBulkCreate.push({
            insertOne: {
                document: promptElement,
            },
        });
        if (!promptElement.parentPromptId) {
            assignmentParentPromptId.push(promptObjectId);
            parentPromptList[existingPromptId] = promptObjectId;
        } else {
            childPromptList[existingPromptId] = promptObjectId;
        }
    }

    for (const promptElement of assignmentPromptBulkCreate) {
        const { parentPromptId, childPromptIds } = promptElement.insertOne.document;
        if (parentPromptId) {
            promptElement.insertOne.document.parentPromptId = parentPromptList[parentPromptId];
        }
        if (childPromptIds.length) {
            promptElement.insertOne.document.childPromptIds = childPromptIds.map(
                (childPromptElement) => {
                    return childPromptList[childPromptElement];
                },
            );
        }
    }

    return { assignmentParentPromptId };
};

const getBothRatAssignmentsData = async ({ headers = {}, params = {}, query = {} }) => {
    try {
        const { _institution_id, _institution_calendar_id } = headers;
        const { id } = params;
        const { studentId } = query;
        const currentAssignment = await assignmentSchema
            .findById({ _id: convertToMongoObjectId(id) }, assignmentQuery)
            .populate({ path: 'basic.evaluation.rubricsId', select: { name: 1 } })
            .lean();
        if (!currentAssignment) {
            return { statusCode: 410, message: 'ASSIGNMENT NOT FOUND' };
        }
        if (currentAssignment.basic.type !== RAT || !currentAssignment.ratId) {
            return {
                statusCode: 200,
                message: 'ASSIGNMENT DATA',
                data: currentAssignment,
            };
        }
        const ratAssignments = await assignmentSchema
            .find(
                {
                    ratId: currentAssignment.ratId,
                    isDeleted: false,
                },
                assignmentQuery,
            )
            .populate({ path: 'basic.evaluation.rubricsId', select: { name: 1 } })
            .lean();
        if (ratAssignments.length === 0) {
            return { statusCode: 410, message: 'RAT ASSIGNMENTS NOT FOUND' };
        }
        for (const assignmentElement of ratAssignments) {
            if (assignmentElement.basic.instructionAttachment) {
                for (const instructionAttachment of assignmentElement.basic.instructionAttachment) {
                    instructionAttachment.signedUrl = await getSignedUrl(instructionAttachment.url);
                }
            }
        }
        const { sessionIds, courseIds } = await updateCommentsAndDate({
            assignmentList: ratAssignments,
            _institution_id,
            allStudentData: [],
            studentId,
            getAllSubmissionData: true,
        });
        if (Object.keys(sessionIds).length) {
            await updateDateBasedOnSession({
                sessionIds,
                assignmentList: ratAssignments,
                _institution_id,
                _institution_calendar_id,
            });
        }
        if (Object.keys(courseIds).length) {
            await updateDateBasedOnCourse({
                courseIds,
                assignmentList: ratAssignments,
                _institution_calendar_id,
            });
        }
        const iRatAssignment = ratAssignments.find(
            (assignmentElement) => assignmentElement.ratType === IRAT,
        );
        const tRatAssignment = ratAssignments.find(
            (assignmentElement) => assignmentElement.ratType === TRAT,
        );
        const combinedData = {
            ...currentAssignment,
            ratAssignments: {
                iRat: iRatAssignment,
                tRat: tRatAssignment,
            },
            submission: currentAssignment.submission,
            ratSubmissionData: {
                iRat: iRatAssignment?.submission,
                tRat: tRatAssignment?.submission,
            },
        };
        combinedData.todaysDate = new Date();
        return {
            statusCode: 200,
            message: 'RAT ASSIGNMENTS DATA',
            data: combinedData,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getSessionLists,
    getInstructorLists,
    getStudentGroupLists,
    getStudentCourseGroupLists,
    getAssignmentList,
    updateCommentsAndDate,
    updateDateBasedOnSession,
    updateDateBasedOnCourse,
    assignmentQuery,
    promptsProject,
    getSubmissionData,
    getCommentList,
    emptyComment,
    constructAssignment,
    createNewAssignmentPrompts,
    getBothRatAssignmentsData,
};
