const Joi = require('joi');
const common_files = require('../utility/common');

exports.elective_topic_allocation = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).allow('').required().error(error => {
                return error;
            }),
            data: Joi.array().items(
                Joi.object().keys({
                    id: Joi.string().alphanum().length(24).allow('').required().error(error => {
                        return error;
                    }),
                    topics: Joi.string().min(3).allow('').required().error(error => {
                        return error;
                    }),
                    _subject_id: Joi.string().alphanum().length(24).allow('').required().error(error => {
                        return error;
                    }),
                    /* // _staff_id: Joi.string().alphanum().length(24).allow('').required().error(error => {
                    _staff_id: Joi.string().alphanum().allow('').required().error(error => {
                        return error;
                    }), */
                    // _elective_student_group_id: Joi.string().alphanum().length(24).allow('').required().error(error => {
                    _elective_student_group_id: Joi.string().alphanum().allow('').required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.elective_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.elective_id_level = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            level: Joi.number().min(1).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.elective_session_order = (req, res, next) => {
    const schema = Joi.object().keys({
        body: Joi.object().keys({
            _course_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            _program_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            data: Joi.array().items(
                Joi.object().keys({
                    s_no: Joi.number().min(1).max(50).required().error(error => {
                        return error;
                    }),
                    delivery_symbol: Joi.string().min(1).max(3).trim().required().error(error => {
                        return error;
                    }),
                    delivery_no: Joi.number().min(1).max(50).required().error(error => {
                        return error;
                    }),
                    session_name: Joi.string().min(3).max(100).trim().required().error(error => {
                        return error;
                    }),
                    contact_hours: Joi.number().min(1).max(3).required().error(error => {
                        return error;
                    })
                }).unknown(true)
            )
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}

exports.elective_id_topic_id = (req, res, next) => {
    const schema = Joi.object().keys({
        params: Joi.object().keys({
            course_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            }),
            topic_id: Joi.string().alphanum().length(24).required().error(error => {
                return error;
            })
        }).unknown(true)
    }).unknown(true);
    Joi.validate(req, schema, function (err, value) {
        if (err) {
            return common_files.com_response(res, 422, false, 'validation error', err.details[0].message);
        } else {
            return next();
        }
    });
}