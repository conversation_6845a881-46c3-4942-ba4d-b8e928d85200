const { calendarProgramList, programCourseList } = require('./user.service');

const getProgramList = async ({ query: { institutionCalendarId } }) => {
    const programs = await calendarProgramList({ institutionCalendarId });

    return { statusCode: 200, data: programs };
};

const getProgramCourseList = async ({ query: { institutionCalendarId, programId } }) => {
    const courses = await programCourseList({ institutionCalendarId, programId });

    return { statusCode: 200, data: courses };
};

module.exports = {
    getProgramList,
    getProgramCourseList,
};
