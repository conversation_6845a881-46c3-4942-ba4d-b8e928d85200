const StudentResponseModel = require('./student-response.model');
const ProgramModel = require('../../../lib/models/digi_programs');
const CourseModel = require('../../../lib/models/digi_course');
const StudentPortfolioModel = require('../portfolio/student-portfolio.model');
const AssignEvaluatorModel = require('../evaluation/assign-evaluator.model');
const PortFolioRoleModel = require('../role/role.model');

const { ON_GOING, COMPLETED } = require('../../common/utils/enums');
const {
    NotFoundError,
    BadRequestError,
    UpdateFailedError,
} = require('../../common/utils/api_error_util');
const {
    convertToMongoObjectId,
    getFileCategory,
    isIDEquals,
    removeDuplicatesFromArrayOfObjects,
} = require('../../common/utils/common.util');
const { checkDocumentsExists } = require('../../base/base.helper');
const { getS3SignedUrl, deleteS3Object } = require('../../../service/aws.service');

const startForm = async ({ portfolioId, componentId, childrenId, userId, scheduleId }) => {
    const query = {
        'student._id': convertToMongoObjectId(userId),
        portfolioId,
        componentId,
        childrenId,
        ...(scheduleId && { scheduleId }),
    };
    const formExists = await checkDocumentsExists({
        Model: StudentResponseModel,
        query: { ...query, status: ON_GOING },
        canThrowError: false,
    });
    if (formExists) {
        throw new BadRequestError('ALREADY_FORM_STARTED');
    }

    const updateStudentResponse = await StudentResponseModel.findOneAndUpdate(
        query,
        {
            $set: {
                status: ON_GOING,
                'formTimestamps.firstStartedAt': new Date(),
            },
        },
        {
            projection: { _id: 1, student: 1 },
        },
    ).lean();

    return updateStudentResponse;
};

const updateStudentResponse = async ({ studentResponseId, pageId, sectionId, section, userId }) => {
    const studentResponse = await StudentResponseModel.updateOne(
        { _id: studentResponseId, 'student._id': userId },
        {
            $set: {
                'pages.$[pageId].elements.$[sectionId].elements': section.elements,
                'formTimestamps.lastUpdated': new Date(),
            },
        },
        {
            arrayFilters: [
                { 'pageId._id': convertToMongoObjectId(pageId) },
                { 'sectionId._id': convertToMongoObjectId(sectionId) },
            ],
        },
    );
    if (!studentResponse.modifiedCount) {
        throw new UpdateFailedError('FAILED_TO_UPDATE_STUDENT_RESPONSE');
    }
};

const submitForm = async ({ studentResponseId, pages, userId }) => {
    await StudentResponseModel.updateOne(
        { _id: studentResponseId, 'student._id': userId },
        {
            $set: { status: COMPLETED, pages, 'formTimestamps.submittedAt': new Date() },
            $push: {
                statusHistory: {
                    status: COMPLETED,
                    createdAt: new Date(),
                    userId: convertToMongoObjectId(userId),
                },
            },
        },
    );
};

const getStudentResponse = async ({
    formId,
    portfolioId,
    componentId,
    childrenId,
    scheduleId,
    userId,
}) => {
    const portfolio = await StudentPortfolioModel.findOne(
        { _id: portfolioId, isDeleted: false },
        { programId: 1, courseId: 1, institutionCalendarId: 1 },
    ).lean();
    if (!portfolio) {
        throw new NotFoundError('PORTFOLIO_NOT_FOUND');
    }

    const program = await ProgramModel.findOne(
        { _id: portfolio?.programId, isDeleted: false },
        { name: 1, code: 1 },
    ).lean();

    const course = await CourseModel.findOne(
        { _id: portfolio?.courseId, isDeleted: false },
        { name: '$course_name', code: '$course_code' },
    );

    const studentResponse = await StudentResponseModel.findOne(
        {
            formId,
            'student._id': userId,
            portfolioId,
            componentId,
            childrenId,
            ...(scheduleId && { scheduleId }),
        },
        { student: 1, pages: 1, status: 1, totalMarks: 1, title: 1, type: 1, prepareAndPublish: 1 },
    ).lean();
    if (!studentResponse) {
        throw new NotFoundError('STUDENT_RESPONSE_NOT_FOUND');
    }

    for (const page of studentResponse.pages) {
        for (const section of page.elements) {
            for (const question of section.elements) {
                if (question?.attachment?.key) {
                    question.attachment.link = await getS3SignedUrl({
                        bucket: question.attachment.bucket,
                        key: question.attachment.key,
                    });
                }
            }
        }
    }

    return { ...studentResponse, program, course };
};

const uploadStudentAttachment = async ({
    studentResponseId,
    attachment,
    pageId,
    sectionId,
    questionId,
    email,
}) => {
    const key = `${attachment.folder}/${attachment.id}/${attachment.fileName}.${attachment.fileType}`;
    const attachmentData = {
        key,
        bucket: attachment.bucket,
        type: getFileCategory({ type: attachment.fileType }),
    };

    if (email) {
        // Case: Evaluator image logic
        const exists = await StudentResponseModel.exists({
            _id: studentResponseId,
            'evaluatorImages.email': email,
        });

        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            exists
                ? { $set: { 'evaluatorImages.$[elem].attachment': attachmentData } }
                : {
                      $push: {
                          evaluatorImages: { email, attachment: attachmentData },
                      },
                  },
            ...(exists && { arrayFilters: [{ 'elem.email': email }] }),
        );
    } else if (pageId && sectionId && questionId) {
        // Case: Attachment inside student response page structure
        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            {
                $set: {
                    'pages.$[pageId].elements.$[sectionId].elements.$[questionId].attachment':
                        attachmentData,
                },
            },
            {
                arrayFilters: [
                    { 'pageId._id': convertToMongoObjectId(pageId) },
                    { 'sectionId._id': convertToMongoObjectId(sectionId) },
                    { 'questionId._id': convertToMongoObjectId(questionId) },
                ],
            },
        );
    }

    const signedUrl = await getS3SignedUrl({
        bucket: attachment.bucket,
        key,
    });

    return signedUrl;
};

const deleteStudentAttachment = async ({
    studentResponseId,
    pageId,
    sectionId,
    questionId,
    email,
}) => {
    let attachmentData = null;

    if (email) {
        // Case: Evaluator image logic
        const studentResponse = await StudentResponseModel.findOne(
            { _id: studentResponseId, 'evaluatorImages.email': email },
            { 'evaluatorImages.$': 1 },
        ).lean();

        if (!studentResponse || !studentResponse.evaluatorImages?.[0]?.attachment) {
            throw new NotFoundError('EVALUATOR_ATTACHMENT_NOT_FOUND');
        }

        attachmentData = studentResponse.evaluatorImages[0].attachment;

        // Remove attachment from database
        await StudentResponseModel.updateOne(
            { _id: studentResponseId, 'evaluatorImages.email': email },
            { $unset: { 'evaluatorImages.$.attachment': 1 } },
        );
    } else if (pageId && sectionId && questionId) {
        const studentResponse = await StudentResponseModel.findOne(
            { _id: studentResponseId },
            { pages: 1 },
        ).lean();

        if (!studentResponse) {
            throw new NotFoundError('STUDENT_RESPONSE_NOT_FOUND');
        }

        const page = studentResponse.pages?.find((p) => p._id.toString() === pageId);
        const section = page?.elements?.find((s) => s._id.toString() === sectionId);
        const question = section?.elements?.find((q) => q._id.toString() === questionId);

        if (!question?.attachment) {
            throw new NotFoundError('ATTACHMENT_NOT_FOUND');
        }

        attachmentData = question.attachment;

        await StudentResponseModel.updateOne(
            { _id: studentResponseId },
            {
                $unset: {
                    'pages.$[pageId].elements.$[sectionId].elements.$[questionId].attachment': 1,
                },
            },
            {
                arrayFilters: [
                    { 'pageId._id': convertToMongoObjectId(pageId) },
                    { 'sectionId._id': convertToMongoObjectId(sectionId) },
                    { 'questionId._id': convertToMongoObjectId(questionId) },
                ],
            },
        );
    } else {
        throw new BadRequestError('INVALID_PARAMETERS_FOR_ATTACHMENT_DELETION');
    }

    // Delete from AWS S3
    if (attachmentData?.key && attachmentData?.bucket) {
        await deleteS3Object({
            bucket: attachmentData.bucket,
            key: attachmentData.key,
        });
    }
};

const getEvaluatorAndApproverForStudent = async ({
    componentId,
    childrenId,
    deliveryTypeId,
    studentId,
}) => {
    const assignEvaluator = await AssignEvaluatorModel.findOne(
        {
            componentId,
            childrenId,
            'students.studentId': studentId,
            ...(deliveryTypeId && { deliveryTypeId }),
        },
        {
            students: 1,
            groups: 1,
            infrastructures: 1,
            typeOfEvaluation: 1,
        },
    ).lean();

    if (!assignEvaluator) return [];

    const roleMap = new Map();

    assignEvaluator.students?.forEach(({ studentId: sId, roles }) => {
        if (!isIDEquals(sId, studentId)) return;

        roles.forEach(({ roleId, users }) => {
            const key = roleId.toString();
            if (!roleMap.has(key)) {
                roleMap.set(key, { roleId, users: [...users] });
            } else {
                roleMap.get(key).users.push(...users);
            }
        });
    });

    const roleIds = [...roleMap.keys()].map(convertToMongoObjectId);
    const rolesInfo = await PortFolioRoleModel.find(
        { _id: { $in: roleIds } },
        { name: 1, type: 1 },
    ).lean();

    const roleInfoMap = new Map(rolesInfo.map((r) => [r._id.toString(), r]));

    const result = [];
    for (const [roleId, { users }] of roleMap.entries()) {
        const roleMeta = roleInfoMap.get(roleId);
        if (roleMeta) {
            result.push({
                roleId,
                name: roleMeta.name,
                type: roleMeta.type,
                users: removeDuplicatesFromArrayOfObjects(users, 'email'),
            });
        }
    }

    return result;
};

module.exports = {
    startForm,
    updateStudentResponse,
    submitForm,
    getStudentResponse,
    uploadStudentAttachment,
    deleteStudentAttachment,
    getEvaluatorAndApproverForStudent,
};
