const constant = require('../utility/constants');
const lms_review = require('mongoose').model(constant.LMS_REVIEW);
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const lms = require('mongoose').model(constant.LMS);
const institution = require('mongoose').model(constant.INSTITUTION);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const user = require('mongoose').model(constant.USER);
const role_assign = require('mongoose').model(constant.ROLE_ASSIGN);
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_functions = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const globalSettingSchema = require('../global_session_settings/global_session_settings_model');
// process.env.TZ = 'Asia/Calcutta'
const { response_function, convertToMongoObjectId, clone } = require('../utility/common');
const {
    get_list,
    get,
    get_list_populate,
    bulk_write,
    update_condition,
    dsGetAllWithSortAsJSON,
    get_list_sort,
} = require('../base/base_controller');
const {
    COURSE_SCHEDULE,
    DIGI_COURSE,
    DIGI_PROGRAM,
    EVENT_WHOM: { STAFF, STUDENT },
    NOTIFY_VIA: { MOBILE, EMAIL },
    WEEKDAYS,
    FULL_TIME,
    PART_TIME,
    COMPLETED,
    ABSENT,
    LEAVE,
    PRESENT,
    LEAVE_FLOW_TYPE: { APPROVE },
    LEAVE_FLOW_TYPE_STATUS: { REJECTED },
    ROLE,
    PRIMARY,
    PENDING,
    SCHEDULE_TYPES: { REGULAR, SUPPORT_SESSION, EVENT },
    LEAVE_TYPE: { PERMISSION, ONDUTY },
    ACADEMIC,
    DONE,
} = require('../utility/constants');

const {
    nameFormatter,
    dateTimeLocalFormatter,
    scheduleTimeFormateChange,
    scheduleDateFormateChange,
    send_email,
    send_sms,
    scheduleDateTimeFormatter,
    dateTimeLocalFormatInString,
    capitalize,
    emailRegardsContent,
} = require('../utility/common_functions');
// const { INSTITUTION_NAME } = require('../utility/util_keys');
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const course = require('mongoose').model(DIGI_COURSE);
const role = require('mongoose').model(ROLE);
const program = require('mongoose').model(DIGI_PROGRAM);

const scheduleToTable = async (/* tableHeaders, */ scheduleData) => {
    let tableData = `<table style='border-collapse: collapse;width: 100%;'><thead><tr style='border: 1px solid #171616d4;text-align: left;'>`;
    for (objectElement of Object.keys(scheduleData[0])) {
        tableData = tableData.concat(
            `<th style='border: 1px solid #999;padding:0.5rem;'>${objectElement.toString()}</th>`,
        );
    }
    tableData = tableData.concat(`</tr></thead><tbody>`);
    for (scheduleElement of scheduleData) {
        tableData = tableData.concat(`<tr>`);
        for (objectElement of Object.keys(scheduleElement)) {
            tableData = tableData.concat(
                `<td style='border: 1px solid #999;padding:0.5rem;'>${scheduleElement[objectElement]}</td>`,
            );
        }
        tableData = tableData.concat(`</tr>`);
    }
    tableData = tableData.concat(`</tbody></table>`);
    return tableData;
};

const contentScheduleMailPush = async (
    receiverData,
    notifyVia,
    scheduleData,
    mailSubject,
    mailBody,
    smsContent,
    approverData,
) => {
    let paragraph = `<p><b> Dear ${nameFormatter(receiverData.name)}</b>,<br>`;
    paragraph = paragraph.concat(mailBody);
    if (scheduleData && scheduleData.length)
        paragraph = paragraph.concat(await scheduleToTable(scheduleData));
    paragraph = paragraph.concat(emailRegardsContent());
    if (notifyVia.includes(EMAIL)) send_email(receiverData.email, mailSubject, paragraph);
    if (notifyVia.includes(MOBILE) && receiverData.mobile && receiverData.mobile.length !== 0)
        send_sms(receiverData.mobile, smsContent);
};

const studentContentPush = async (receiverData, notifyVia, mailSubject, mailBody, smsContent) => {
    let paragraph = `<p><b> Dear ${nameFormatter(receiverData.name)}</b>,<br>`;
    paragraph = paragraph.concat(mailBody);
    paragraph = paragraph.concat(emailRegardsContent());
    if (notifyVia.includes(EMAIL)) send_email(receiverData.email, mailSubject, paragraph);
    if (notifyVia.includes(MOBILE) && receiverData.mobile && receiverData.mobile.length !== 0)
        send_sms(receiverData.mobile, smsContent);
};

const contentMailPush = async (
    lmsType,
    applicantData,
    approverData,
    mailBody,
    approverComment,
    startDate,
    endDate,
) => {
    let paragraph = `<p><b> Dear ${nameFormatter(applicantData.name)}</b>,<br>`;
    paragraph = paragraph.concat(
        `Your ${lmsType} application (${dateTimeLocalFormatInString(startDate)} `,
    );
    paragraph = paragraph.concat(
        endDate ? `and ${dateTimeLocalFormatInString(endDate)}) has been` : `has been `,
    );
    paragraph = paragraph.concat(
        mailBody === REJECTED
            ? ` ${mailBody}<b><br><br>Approver Comment :</b><br>${approverComment}<br>`
            : ` ${mailBody}<br>`,
    );
    paragraph = paragraph.concat(emailRegardsContent());
    send_email(applicantData.email, `${lmsType} application Status`, paragraph);
};

const dayCalculation = async (startDate, endDate) => {
    const weekdays = [
        { day: 'friday', dateDayNo: 5 },
        { day: 'saturday', dateDayNo: 6 },
    ];
    const daysOfYear = [];
    for (
        let elementDate = new Date(startDate);
        elementDate <= endDate;
        elementDate.setDate(elementDate.getDate() + 1)
    ) {
        const dateDayNo = elementDate.getDay();
        daysOfYear.push({ date: elementDate, dateDayNo });
    }
    const weekdays_1 = daysOfYear.filter((ele) => ele.dateDayNo == weekdays[0].dateDayNo);
    const weekdays_2 = daysOfYear.filter((ele) => ele.dateDayNo == weekdays[1].dateDayNo);
    const total_days = daysOfYear.length;
    calc_days = total_days - (weekdays_1.length + weekdays_2.length);
    return {
        withHolidays: total_days,
        withOutHolidays: calc_days,
    };
};

const getTotalDays = async ({
    startDate,
    endDate,
    isWeekendConsider,
    leaveType,
    _institution_id,
}) => {
    try {
        const getGlobalSetting = await globalSettingSchema
            .findOne(
                { isDeleted: false, isActive: true, _institution_id },
                { 'basicDetails.workingDays': 1 },
            )
            .lean();
        if (
            getGlobalSetting &&
            getGlobalSetting.basicDetails &&
            getGlobalSetting.basicDetails.workingDays &&
            getGlobalSetting.basicDetails.workingDays.length
        ) {
            const weekEnds = getGlobalSetting.basicDetails.workingDays
                .filter((workingElement) => !workingElement.isActive)
                .map((daysElement) => daysElement.day);
            const daysOfWeek = [
                'sunday',
                'monday',
                'tuesday',
                'wednesday',
                'thursday',
                'friday',
                'saturday',
            ];
            let totalDays = 0;
            while (new Date(startDate) <= new Date(endDate)) {
                if (leaveType === 'leave' && isWeekendConsider) {
                    totalDays++;
                } else {
                    if (!weekEnds.includes(daysOfWeek[startDate.getDay()])) {
                        totalDays++;
                    }
                }
                startDate.setDate(startDate.getDate() + 1);
            }
            return totalDays;
        }
        return 0;
    } catch (error) {
        console.log('Error in counting total days -> getTotalDays', error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

const noOfDayCalculation = async (dt1, dt2, leaveData, weekendConsideration) => {
    let noOfDays = 0;
    for (element of leaveData) {
        const leaveStartDate = dateTimeLocalFormatter(element.from);
        const leaveEndDate = dateTimeLocalFormatter(element.to);
        if (
            dt1.getUTCMonth() === leaveStartDate.getUTCMonth() ||
            (dt2.getUTCMonth() === leaveStartDate.getUTCMonth() &&
                dt1.getUTCMonth() === leaveEndDate.getUTCMonth()) ||
            dt2.getUTCMonth() === leaveEndDate.getUTCMonth()
        ) {
            if (leaveStartDate.getUTCMonth() === leaveEndDate.getUTCMonth())
                noOfDays += element.days;
            else {
                const leaveFirstDate = new Date(
                    leaveStartDate.getFullYear(),
                    leaveStartDate.getUTCMonth() + 1,
                    0,
                );
                const leaveLastDate = new Date(
                    leaveEndDate.getFullYear(),
                    leaveEndDate.getUTCMonth(),
                    1,
                );
                const leaveFromDateDays = await dayCalculation(leaveStartDate, leaveFirstDate);
                const leaveToDateDays = await dayCalculation(leaveLastDate, leaveEndDate);
                if (
                    dt1.getUTCMonth() === leaveFirstDate.getUTCMonth() ||
                    dt2.getUTCMonth() === leaveFirstDate.getUTCMonth()
                )
                    noOfDays +=
                        weekendConsideration !== undefined && weekendConsideration === true
                            ? leaveFromDateDays.withHolidays
                            : leaveFromDateDays.withOutHolidays;
                if (
                    dt1.getUTCMonth() === leaveLastDate.getUTCMonth() ||
                    dt2.getUTCMonth() === leaveLastDate.getUTCMonth()
                )
                    noOfDays +=
                        weekendConsideration !== undefined && weekendConsideration === true
                            ? leaveToDateDays.withHolidays
                            : leaveToDateDays.withOutHolidays;
            }
        }
    }
    return noOfDays;
};

const diffMonthDateCalculation = async (dt1, dt2, leaveData, weekendConsideration) => {
    const startMonthDate = new Date(dt1.getFullYear(), dt1.getUTCMonth() + 1, 0);
    const endMonthDate = new Date(dt2.getFullYear(), dt2.getUTCMonth(), 1);
    const leaveFromDateDays = await dayCalculation(dt1, startMonthDate);
    const leaveToDateDays = await dayCalculation(endMonthDate, dt2);
    const startDateCheck = await noOfDayCalculation(
        dt1,
        startMonthDate,
        leaveData,
        weekendConsideration,
    );
    const endDateCheck = await noOfDayCalculation(
        endMonthDate,
        dt2,
        leaveData,
        weekendConsideration,
    );
    let fromMonthDates = weekendConsideration
        ? leaveFromDateDays.withHolidays
        : leaveFromDateDays.withOutHolidays;
    let toMonthDates = weekendConsideration
        ? leaveToDateDays.withHolidays
        : leaveToDateDays.withOutHolidays;
    fromMonthDates += startDateCheck;
    toMonthDates += endDateCheck;
    return { fromMonthDates, toMonthDates };
};

exports.list_leaves = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const doc = await base_control.get_list(lms_review, {});
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('APPLIED_LEAVE_LIST'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
    }
};
exports.list_leaves_report_absence = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    //let query = { type: constant.LMS_ROLE_TYPE.REPORT_ABSENCE, user_type: req.params.user_type, _institution_calendar_id: ObjectId(req.params._institution_calendar_id) };
    //let doc = await base_control.get_list(lms_review, query);
    const aggre = [
        {
            $match: {
                _institution_calendar_id: ObjectId(req.params._institution_calendar_id),
                type: constant.LMS_ROLE_TYPE.REPORT_ABSENCE,
                user_type: req.params.user_type,
                isDeleted: false,
            },
        },
        {
            $lookup: {
                from: 'users',
                localField: '_user_id',
                foreignField: '_id',
                as: 'user_data',
            },
        },
        { $unwind: { path: '$user_data', preserveNullAndEmptyArrays: true } },
    ];
    if (req.params.user_type == 'staff') {
        //For Academic Staff Validation
        aggre.push(
            // { $match: { 'user_data.staff_employment_type': 'academic' } },
            // {
            //     $unwind: {
            //         path: '$user_data.academic_allocation',
            //         preserveNullAndEmptyArrays: true,
            //     },
            // },
            // { $match: { 'user_data.academic_allocation.allocation_type': 'primary' } },
            // {
            //     $lookup: {
            //         from: constant.DIGI_PROGRAM,
            //         localField: 'user_data.academic_allocation._program_id',
            //         foreignField: '_id',
            //         as: 'user_data.academic_allocation.program',
            //     },
            // },
            // {
            //     $unwind: {
            //         path: '$user_data.academic_allocation.program',
            //         preserveNullAndEmptyArrays: true,
            //     },
            // },
            // { $addFields: { 'user_data.programs': '$user_data.academic_allocation.program.name' } },
            //Need to remove while update in new program inputs
            // {
            //     $addFields: {
            //         'user_data.programs': {
            //             $substr: [
            //                 '$user_data.program',
            //                 0,
            //                 { $subtract: [{ $strLenCP: '$user_data.program' }, 3] },
            //             ],
            //         },
            //     },
            // },
            {
                $project: {
                    status_time: 1,
                    isActive: 1,
                    isDeleted: 1,
                    _institution_id: 1,
                    _institution_calendar_id: 1,
                    _user_id: 1,
                    user_type: 1,
                    type: 1,
                    from: 1,
                    to: 1,
                    days: 1,
                    permission_hour: 1,
                    is_noticed: 1,
                    reason: 1,
                    payment_status: 1,
                    leave_category: 1,
                    leave_type: 1,
                    _leave_reason_doc: 1,
                    created_by: 1,
                    application_date: 1,
                    approved_by: 1,
                    comments: 1,
                    session: 1,
                    level_approvers: 1,
                    'user_data.name': 1,
                    'user_data.user_id': 1,
                    'user_data.email': 1,
                    'user_data.programs': 1,
                    'user_data.staff_employment_type': 1,
                    'user_data.academic_allocation': 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        );
    } else {
        aggre.push(
            {
                $lookup: {
                    from: constant.DIGI_PROGRAM,
                    localField: 'user_data.program._program_id',
                    foreignField: '_id',
                    as: 'user_data.programs',
                },
            },
            { $unwind: { path: '$user_data.programs', preserveNullAndEmptyArrays: true } },
            { $addFields: { 'user_data.programs': '$user_data.programs.name' } },
            //Need to remove while update in new program inputs
            // {
            //     $addFields: {
            //         'user_data.programs': {
            //             $substr: [
            //                 '$user_data.programs',
            //                 0,
            //                 { $subtract: [{ $strLenCP: '$user_data.programs' }, 3] },
            //             ],
            //         },
            //     },
            // },
            {
                $project: {
                    status_time: 1,
                    isActive: 1,
                    isDeleted: 1,
                    _institution_id: 1,
                    _institution_calendar_id: 1,
                    _user_id: 1,
                    user_type: 1,
                    type: 1,
                    from: 1,
                    to: 1,
                    days: 1,
                    reason: 1,
                    leave_category: 1,
                    leave_type: 1,
                    _leave_reason_doc: 1,
                    application_date: 1,
                    approved_by: 1,
                    comments: 1,
                    session: 1,
                    level_approvers: 1,
                    'user_data.name': 1,
                    'user_data.user_id': 1,
                    'user_data.email': 1,
                    'user_data.programs': 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        );
    }
    aggre.push({ $sort: { _id: -1 } });
    const doc = await base_control.get_aggregate(lms_review, aggre);
    if (doc.status) {
        const { data: programData } = await get_list(program, { isDeleted: false }, { name: 1 });
        if (req.params.user_type === 'staff') {
            doc.data = clone(doc.data);
            for (staffElement of doc.data) {
                if (
                    staffElement._leave_reason_doc &&
                    staffElement._leave_reason_doc !== undefined &&
                    staffElement._leave_reason_doc !== 'undefined' &&
                    staffElement._leave_reason_doc.length &&
                    staffElement._leave_reason_doc !== null &&
                    staffElement._leave_reason_doc !== 'null'
                ) {
                    console.log(staffElement._leave_reason_doc);
                    staffElement._leave_reason_doc_url = await common_functions.getSignedURL(
                        staffElement._leave_reason_doc,
                    );
                }
                if (
                    staffElement.user_data.staff_employment_type === ACADEMIC ||
                    staffElement.user_data.staff_employment_type === 'both'
                ) {
                    const staffProgramData = staffElement.user_data.academic_allocation.find(
                        (ele) => ele && ele.allocation_type === PRIMARY,
                    );
                    if (staffProgramData) {
                        staffElement.user_data.programs = programData.find(
                            (ele) => ele._id.toString() === staffProgramData._program_id,
                        )
                            ? programData.find(
                                  (ele) => ele._id.toString() === staffProgramData._program_id,
                              ).name
                            : '';
                        delete staffElement.user_data.academic_allocation;
                    }
                }
            }
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('REPORT_ABSENCE_LIST'),
                    doc.data,
                ),
            );
    }
    return res
        .status(404)
        .send(common_files.response_function(res, 404, false, req.t('NO_DATA_FOUND'), doc.data));
};
exports.list_lms = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        isDeleted: false,
        type: req.params.type,
        _user_id: ObjectId(req.params.user),
        _institution_calendar_id: ObjectId(req.params.institution),
    };
    const project = {};
    const doc = await base_control.get_list(lms_review, query, project);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('APPLIED_LEAVE_LIST'), doc.data);
    } else {
        common_files.com_response(res, 200, true, req.t('APPLIED_LEAVE_LIST'), []);
    }
};
exports.report_absence_list = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const query = {
        isDeleted: false,
        type: constant.LEAVE_TYPE.REPORT_ABSENCE,
        user_type: req.params.user_type,
        _institution_calendar_id: ObjectId(req.params.inst_cal_id),
    };
    const project = {};
    const doc = await base_control.get_list(lms_review, query, project);
    if (doc.status) {
        common_files.com_response(res, 200, true, req.t('APPLIED_LEAVE_LIST'), doc.data);
    } else {
        common_files.com_response(res, 200, true, req.t('APPLIED_LEAVE_LIST'), []);
    }
};

const dateFormatter = (year, month, date, hours, minute, format) => {
    const dateFormat = new Date(
        year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ' ' + format,
    );
    return dateFormat;
};

const mergeSchedule = (schedules) => {
    const mergedSchedules = clone(schedules.filter((ele) => ele.merge_status === true));
    for (schedulesElement of mergedSchedules) {
        schedulesElement.merge_sessions = [];
        for (mergeScheduleElement of schedulesElement.merge_with) {
            const mergedLoc = mergedSchedules.findIndex(
                (ele) => ele._id.toString() === mergeScheduleElement.schedule_id.toString(),
            );
            if (mergedLoc !== -1) {
                schedulesElement.merge_sessions.push({
                    ...mergedSchedules[mergedLoc].session,
                    ...{ _schedule_id: mergedSchedules[mergedLoc]._id },
                });
                mergedSchedules.splice(mergedLoc, 1);
            }
        }
    }
    const normalSchedules = clone([
        ...clone(schedules.filter((ele) => ele.merge_status === false)),
        ...mergedSchedules,
    ]);
    return normalSchedules;
};

exports.list_schedule_based_date = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, userId, startDate, endDate },
        } = req;
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const scheduleStart = dateTimeLocalFormatter(startDate);
        const scheduleEnd = dateTimeLocalFormatter(endDate);
        const startDateOnly = new Date(
            scheduleStart.getFullYear() +
                '-' +
                (scheduleStart.getMonth() + 1).toString().padStart(2, '0') +
                '-' +
                scheduleStart.getDate().toString().padStart(2, '0') +
                'T00:00:00.000Z',
        );
        const endDateOnly = new Date(
            scheduleEnd.getFullYear() +
                '-' +
                (scheduleEnd.getMonth() + 1).toString().padStart(2, '0') +
                '-' +
                scheduleEnd.getDate().toString().padStart(2, '0') +
                'T00:00:00.000Z',
        );
        console.log(scheduleStart, scheduleEnd);
        // Course Schedule Datas
        const scheduleData = await get_list_sort(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                // _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'staffs._staff_id': convertToMongoObjectId(userId),
                schedule_date: { $gte: startDateOnly, $lte: endDateOnly },
                status: PENDING,
                // type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _session_id: '$session._session_id',
                session: 1,
                type: 1,
                title: 1,
                sub_type: 1,
                merge_status: 1,
                merge_with: 1,
                schedule_date: 1,
                start: 1,
                end: 1,
                status: 1,
                _program_id: 1,
                program_name: 1,
                course_name: 1,
                course_code: 1,
                topic: 1,
                _course_id: 1,
                level_no: 1,
                term: 1,
                rotation: 1,
                rotation_count: 1,
                subjects: 1,
                staffs: 1,
                substitute_staffs: 1,
                merge_sessions: [],
            },
            { schedule_date: 1 },
        );
        scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
        // If it is Permission Filter Schedule Based on timings
        const filteredSchedule = [];
        for (scheduleElement of scheduleData.data) {
            const scheduleStartTime = scheduleDateTimeFormatter(
                scheduleStart,
                scheduleElement.start,
            );
            const scheduleEndTime = scheduleDateTimeFormatter(scheduleEnd, scheduleElement.end);
            scheduleStartTime.setMinutes(scheduleStartTime.getMinutes() + 1);
            scheduleEndTime.setMinutes(scheduleEndTime.getMinutes() - 1);
            if (
                (scheduleStart <= scheduleStartTime && scheduleEnd >= scheduleStartTime) ||
                (scheduleStart <= scheduleEndTime && scheduleEnd >= scheduleEndTime)
            ) {
                filteredSchedule.push(scheduleElement);
            }
        }
        // Merge Schedule for merged schedules
        scheduleData.data = mergeSchedule(filteredSchedule);
        const subjectIds = [
            ...new Set(
                scheduleData.data
                    .map((ele) => ele.subjects.map((ele2) => ele2._subject_id.toString()).flat())
                    .flat(),
            ),
        ];
        const scheduleDates = [
            ...new Set(scheduleData.data.map((ele) => new Date(ele.schedule_date))),
        ];

        let { status: u_status, data: u_data } = await get_list(
            user,
            {
                _id: { $ne: convertToMongoObjectId(userId) },
                user_type: STAFF,
                status: COMPLETED,
                'academic_allocation._department_subject_id': { $in: subjectIds },
            },
            { email: 1, name: 1, academic_allocation: 1, employment: 1 },
        );
        u_status = clone(u_status);
        u_data = u_status ? clone(u_data) : [];
        const staffIds = u_data.map((ele) => ele._id.toString());

        // Course Schedule Datas
        const otherScheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                // _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'staffs._staff_id': staffIds,
                schedule_date: scheduleDates,
                isActive: true,
                isDeleted: false,
            },
            {
                _session_id: '$session._session_id',
                session: 1,
                type: 1,
                title: 1,
                sub_type: 1,
                schedule_date: 1,
                start: 1,
                end: 1,
                status: 1,
                _program_id: 1,
                program_name: 1,
                course_name: 1,
                course_code: 1,
                topic: 1,
                _course_id: 1,
                level_no: 1,
                term: 1,
                rotation: 1,
                rotation_count: 1,
                subjects: 1,
                staffs: 1,
                substitute_staffs: 1,
            },
        );
        otherScheduleData.data = otherScheduleData.status ? clone(otherScheduleData.data) : [];
        for (userElement of u_data) {
            userElement.subjectIds = userElement.academic_allocation
                .map((ele) => ele._department_subject_id)
                .flat();
            delete userElement.academic_allocation;
        }
        // Leave/Permission/On_Duty
        const lmsData = await get_list(
            lms_review,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                // _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _user_id: staffIds,
                $or: [
                    {
                        from: {
                            $gte: scheduleStart,
                            $lte: scheduleEnd,
                        },
                    },
                    {
                        to: {
                            $gte: scheduleStart,
                            $lte: scheduleEnd,
                        },
                    },
                ],
                status: { $not: { $eq: 'rejected' } },
                isActive: true,
                isDeleted: false,
            },
            {},
        );
        lmsData.data = lmsData.status ? lmsData.data : [];
        // return res.send({ u_data, lmsData });
        for (scheduleElement of scheduleData.data) {
            const scheduling_date = new Date(scheduleElement.schedule_date);
            const otherSchedule = otherScheduleData.data.filter(
                (ele) => ele.schedule_date === scheduleElement.schedule_date,
            );
            const scheduleSubjectId = scheduleElement.subjects[0]._subject_id.toString();
            const start = dateFormatter(
                scheduling_date.getFullYear(),
                scheduling_date.getMonth() + 1,
                scheduling_date.getDate(),
                scheduleElement.start.hour,
                scheduleElement.start.minute,
                scheduleElement.start.format,
            );
            const end = dateFormatter(
                scheduling_date.getFullYear(),
                scheduling_date.getMonth() + 1,
                scheduling_date.getDate(),
                scheduleElement.end.hour,
                scheduleElement.end.minute,
                scheduleElement.end.format,
            );
            start.setMinutes(start.getMinutes() + 1);
            end.setMinutes(end.getMinutes() - 1);

            let subStaffs = scheduleElement.staffs.map((ele) => ele._staff_id.toString());
            for (otherScheduleElement of otherSchedule) {
                const otherSchedulingDate = new Date(otherScheduleElement.schedule_date);
                const otherStart = dateFormatter(
                    otherSchedulingDate.getFullYear(),
                    otherSchedulingDate.getMonth() + 1,
                    otherSchedulingDate.getDate(),
                    otherScheduleElement.start.hour,
                    otherScheduleElement.start.minute,
                    otherScheduleElement.start.format,
                );
                const otherEnd = dateFormatter(
                    otherSchedulingDate.getFullYear(),
                    otherSchedulingDate.getMonth() + 1,
                    otherSchedulingDate.getDate(),
                    otherScheduleElement.end.hour,
                    otherScheduleElement.end.minute,
                    otherScheduleElement.end.format,
                );
                otherStart.setMinutes(otherStart.getMinutes() + 1);
                otherEnd.setMinutes(otherEnd.getMinutes() - 1);
                // console.log(otherStart, otherEnd);
                // console.log(start, end);
                // console.log(otherStart <= start && otherEnd >= end);
                // console.log(otherStart <= end && otherEnd >= end);
                if (
                    (otherStart <= start && otherEnd >= end) ||
                    (otherStart <= end && otherEnd >= end)
                )
                    subStaffs = [
                        ...subStaffs,
                        ...otherScheduleElement.staffs.map(
                            (ele) => ele.status === PENDING && ele._staff_id,
                        ),
                    ];
                // subStaffs = [...subStaffs, ...otherScheduleElement.staffs];
            }
            const lmsStaffs = [];
            for (lmsElement of lmsData.data) {
                const leaveStart = dateTimeLocalFormatter(lmsElement.from);
                const leaveEnd = dateTimeLocalFormatter(lmsElement.to);
                if (
                    (leaveStart <= start && leaveEnd >= end) ||
                    (leaveStart <= end && leaveEnd >= end)
                ) {
                    lmsStaffs.push(lmsElement._user_id.toString());
                }
            }
            const availableStaff = clone(
                u_data.filter(
                    (ele) =>
                        !subStaffs.find((ele2) => ele2.toString() === ele._id.toString()) &&
                        !lmsStaffs.find((ele3) => ele3.toString() === ele._id.toString()),
                ),
            );
            const course_users = [];
            for (user_element of availableStaff) {
                if (
                    user_element.subjectIds.find((ele) => ele.toString() === scheduleSubjectId) &&
                    user_element.employment.user_employment_type
                ) {
                    switch (user_element.employment.user_employment_type) {
                        case FULL_TIME:
                            if (
                                user_element.employment.schedule_times.full_time &&
                                user_element.employment.schedule_times.full_time.length !== 0
                            ) {
                                for (empType of user_element.employment.schedule_times.full_time) {
                                    if (
                                        course_users.findIndex(
                                            (userEle) =>
                                                userEle._staff_id.toString() ===
                                                user_element._id.toString(),
                                        ) === -1 &&
                                        empType.days.indexOf(WEEKDAYS[scheduling_date.getDay()]) !==
                                            -1
                                    ) {
                                        const t1 = empType.start_time.split(' ');
                                        const t2 = t1[0].split(':');
                                        const t3 = empType.end_time.split(' ');
                                        const t4 = t3[0].split(':');
                                        const userStartTime = dateFormatter(
                                            scheduling_date.getFullYear(),
                                            scheduling_date.getMonth() + 1,
                                            scheduling_date.getDate(),
                                            t2[0],
                                            t2[1],
                                            t1[1],
                                        );
                                        const userEndTime = dateFormatter(
                                            scheduling_date.getFullYear(),
                                            scheduling_date.getMonth() + 1,
                                            scheduling_date.getDate(),
                                            t4[0],
                                            t4[1],
                                            t3[1],
                                        );
                                        if (
                                            (userStartTime <= start && userEndTime >= end) ||
                                            (userStartTime <= end && userEndTime >= end)
                                        )
                                            course_users.push({
                                                name: user_element.name,
                                                _staff_id: user_element._id,
                                            });
                                    }
                                }
                            }
                            break;
                        case PART_TIME:
                            if (
                                user_element.employment.schedule_times.by_date &&
                                user_element.employment.schedule_times.by_date.length !== 0
                            ) {
                                for (partTime of user_element.employment.schedule_times.by_date) {
                                    for (empType of partTime.schedule) {
                                        if (
                                            course_users.findIndex(
                                                (userEle) =>
                                                    userEle._staff_id.toString() ===
                                                    user_element._id.toString(),
                                            ) === -1 &&
                                            empType.days.indexOf(
                                                WEEKDAYS[scheduling_date.getDay()],
                                            ) !== -1
                                        ) {
                                            const partTimeStart = new Date(partTime.start_date);
                                            const partTimeEnd = new Date(partTime.end_date);

                                            const t1 = empType.start_time.split(' ');
                                            const t2 = t1[0].split(':');
                                            const t3 = empType.end_time.split(' ');
                                            const t4 = t3[0].split(':');
                                            const userStartTime = dateFormatter(
                                                partTimeStart.getFullYear(),
                                                partTimeStart.getMonth() + 1,
                                                partTimeStart.getDate(),
                                                t2[0],
                                                t2[1],
                                                t1[1],
                                            );
                                            const userEndTime = dateFormatter(
                                                partTimeEnd.getFullYear(),
                                                partTimeEnd.getMonth() + 1,
                                                partTimeEnd.getDate(),
                                                t4[0],
                                                t4[1],
                                                t3[1],
                                            );
                                            if (
                                                (userStartTime <= start && userEndTime >= end) ||
                                                (userStartTime <= end && userEndTime >= end)
                                            )
                                                course_users.push({
                                                    name: user_element.name,
                                                    _staff_id: user_element._id,
                                                });
                                        }
                                    }
                                }
                            } else if (
                                user_element.employment.schedule_times.by_day &&
                                user_element.employment.schedule_times.by_day.length !== 0
                            ) {
                                for (empType of user_element.employment.schedule_times.by_day) {
                                    if (
                                        course_users.findIndex(
                                            (userEle) =>
                                                userEle._staff_id.toString() ===
                                                user_element._id.toString(),
                                        ) === -1 &&
                                        empType.days.toString() ===
                                            WEEKDAYS[scheduling_date.getDay()]
                                    ) {
                                        const t1 = empType.start_time.split(' ');
                                        const t2 = t1[0].split(':');
                                        const t3 = empType.end_time.split(' ');
                                        const t4 = t3[0].split(':');
                                        const userStartTime = dateFormatter(
                                            scheduling_date.getFullYear(),
                                            scheduling_date.getMonth() + 1,
                                            scheduling_date.getDate(),
                                            t2[0],
                                            t2[1],
                                            t1[1],
                                        );
                                        const userEndTime = dateFormatter(
                                            scheduling_date.getFullYear(),
                                            scheduling_date.getMonth() + 1,
                                            scheduling_date.getDate(),
                                            t4[0],
                                            t4[1],
                                            t3[1],
                                        );
                                        if (
                                            (userStartTime <= start && userEndTime >= end) ||
                                            (userStartTime <= end && userEndTime >= end)
                                        )
                                            course_users.push({
                                                name: user_element.name,
                                                _staff_id: user_element._id,
                                            });
                                    }
                                }
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            scheduleElement.substitute_staff = course_users;
            scheduleElement.assigned_substitute_staff =
                scheduleElement.substitute_staffs.find(
                    (ele) => ele._assigned_to.toString() === userId.toString(),
                ) || {};
            delete scheduleElement.substitute_staffs;
        }
        return res
            .status(200)
            .send(
                response_function(
                    res,
                    200,
                    true,
                    req.t('SCHEDULE_LIST_ALONG_WITH_SUBSTITUTE'),
                    scheduleData.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

exports.list_leave_id = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.get(lms_review, query);
        if (doc.status) {
            common_files.com_response(res, 200, true, req.t('LEAVE_DETAILS'), doc.data);
        } else {
            common_files.com_response(res, 500, false, req.t('SERVER_ERROR'), doc.data);
        }
    } catch (error) {
        console.log(error.toString());
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.list_leave_user_id = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _user_id: ObjectId(req.params.user_id) };
        const doc = await base_control.get_list(lms_review, query, {});
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('USER_LEAVE_LIST'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.my_leaves = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _user_id: ObjectId(req.params.user_id),
            _institution_calendar_id: ObjectId(req.params.inst_cal_id),
            type: req.params.leave_type,
        };

        const doc = await base_control.get_list(lms_review, query, {});
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('USER_LEAVE_LIST'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

const diff_hours = function (dt2, dt1) {
    let diff = (dt2.getTime() - dt1.getTime()) / 1000;
    diff /= 60 * 60;
    //return Math.abs(Math.round(diff));
    return Math.abs(diff);
};

const diff_days = function (dt1, dt2) {
    const diff_sec = Math.abs(dt1 - dt2) / 1000;
    const no_days = Math.floor(diff_sec / 86400);
    //return Math.abs(Math.round(diff));
    return no_days;
};

exports.insert_report_staff_absence = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _institution_calendar_id,
                staff_id,
                user_type,
                type,
                from,
                to,
                days,
                is_noticed,
                reason,
                _leave_reason_doc,
                created_by,
                notify_via,
                schedules,
            },
            params: { lmsReviewId },
        } = req;

        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query = { _id: ObjectId(req.body.staff_id), user_type: constant.EVENT_WHOM.STAFF };
        const user_data = await base_control.get(user, query, {});
        if (
            (user_data.data.staff_employment_type === ACADEMIC ||
                user_data.data.staff_employment_type === 'both') &&
            (!user_data.data.academic_allocation || user_data.data.academic_allocation.length == 0)
        )
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PRIMARY_PROGRAM_NOT_FOUND'),
                        req.t('PRIMARY_PROGRAM_NOT_FOUND'),
                    ),
                );
        if (
            !user_data.data.staff_employment_type ||
            user_data.data.staff_employment_type.length == 0
        )
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STAFF_EMPLOYMENT_TYPE_NOT_FOUND'),
                        req.t('STAFF_EMPLOYMENT_TYPE_NOT_FOUND'),
                    ),
                );
        // let primary_loc = {};
        // let program_department;
        // if (
        //     user_data.data.staff_employment_type === ACADEMIC ||
        //     user_data.data.staff_employment_type === 'both'
        // ) {
        //     primary_loc = user_data.data.academic_allocation.findIndex(
        //         (i) => i.allocation_type == 'primary',
        //     );
        //     program_department = user_data.data.academic_allocation[primary_loc];
        // }
        let applicantProgram;
        let applicantProgramId;
        let applicantDepartmentId;
        if (
            user_data.data.staff_employment_type === ACADEMIC ||
            user_data.data.staff_employment_type === 'both'
        ) {
            applicantProgram = user_data.data.academic_allocation.find(
                (ele2) => ele2.allocation_type === PRIMARY,
            );
            applicantProgramId = applicantProgram ? applicantProgram._program_id.toString() : '';
            applicantDepartmentId = applicantProgram
                ? applicantProgram._department_id.toString()
                : '';
        }
        const role_ids = [];
        const lms_setting = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id) },
            { leave_approval: 1 },
        );
        if (!lms_setting.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_LEAVE_SETTING_FOUND'),
                        req.t('NO_LEAVE_SETTING_FOUND'),
                    ),
                );
        // lms_setting.data.leave_approval.forEach((element) => {
        //     if (
        //         user_data.data.staff_employment_type.toString() != 'both' &&
        //         element.staff_type.toString() == user_data.data.staff_employment_type.toString()
        //     ) {
        //         if (element.role && element.role._roles_id)
        //             role_ids.push(ObjectId(element.role._roles_id));
        //     } else {
        //         if (element.role && element.role._roles_id)
        //             role_ids.push(ObjectId(element.role._roles_id));
        //     }
        // });
        //Created By Data
        const query_a = { _id: ObjectId(req.body.created_by) };
        const created_by_data = await base_control.get(user, query_a, {});
        const leave_check_query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
            user_type: req.body.user_type,
            _user_id: ObjectId(req.body.staff_id),
            status: { $ne: REJECTED },
            isActive: true,
            isDeleted: false,
        };
        const leave_check = await base_control.get_list(lms_review, leave_check_query, {});
        if (leave_check.status) {
            // console.log(req.body.type);
            for (element of leave_check.data) {
                const from = dateTimeLocalFormatter(element.from);
                const to = dateTimeLocalFormatter(element.to);
                const f = dateTimeLocalFormatter(parseInt(req.body.from));
                const t = dateTimeLocalFormatter(parseInt(req.body.to));
                if ((f <= from && t > from) || (f < to && t >= to) || (t > from && f < to))
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                                req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                            ),
                        );
            }
        }
        // return res.send(leave_check);
        const duration = 0;
        const payment_status = constant.PAYMENT_STATUS.UNPAID;
        const dt1 = dateTimeLocalFormatter(parseInt(from));
        const dt2 = dateTimeLocalFormatter(parseInt(to));
        const calc_days = await (await dayCalculation(dt1, dt2)).withOutHolidays;
        const objs = {
            _institution_id: ObjectId(req.headers._institution_id),
            _institution_calendar_id: req.body._institution_calendar_id,
            user_type: req.body.user_type,
            _user_id: req.body.staff_id,
            type: req.body.type,
            from: req.body.from,
            to: req.body.to,
            days: calc_days,
            permission_hour: duration,
            is_noticed: req.body.is_noticed,
            // notifyVia: req.body.notify_via,
            reason: req.body.reason,
            _leave_reason_doc: req.body._leave_reason_doc,
            status: 'approved',
            status_time: common_functions.timestampNow(),
            // review: constant.LEAVE_FLOW_TYPE_STATUS.APPROVED,
            // forwarded: constant.LEAVE_FLOW_TYPE_STATUS.APPROVED,
            // approval: constant.LEAVE_FLOW_TYPE_STATUS.APPROVED,
            payment_status,
            created_by: {
                _person_id: req.body.created_by,
            },
        };
        // const doc = { status: true };
        const doc = await base_control.insert(lms_review, objs);
        if (doc.status) {
            const fromDate = dateTimeLocalFormatter(parseInt(from));
            const toDate = dateTimeLocalFormatter(parseInt(to));
            // Applicant & Approver User Data
            const userQuery = {
                // _id: {
                //     $in: [
                //         convertToMongoObjectId(lmsReviewData._user_id),
                //         convertToMongoObjectId(_person_id),
                //     ],
                // },
                user_type: STAFF,
                status: COMPLETED,
                isDeleted: false,
                isActive: true,
            };
            const { data: userData } = await get_list(user, userQuery, {
                user_id: 1,
                name: 1,
                email: 1,
                mobile: 1,
                academic_allocation: 1,
                staff_employment_type: 1,
            });
            const applicantData = user_data.data;
            const approverData = created_by_data.data;
            // Notification Push
            const scheduleAlteredData = [];
            const mailedStaff = [];
            let roleData = {};
            let roleIds = {};
            let roleAssign = { data: [] };
            let staffsScheduleData = [];
            let mailBody;
            mailBody = `<b>Report application summary-Approved</b><br><br>`;
            mailBody = mailBody.concat(`Staff name : ${nameFormatter(applicantData.name)}<br>`);
            mailBody = mailBody.concat(`Employee id : ${applicantData.user_id}<br>`);
            mailBody = mailBody.concat(`Reason : ${reason}<br>`);
            mailBody = mailBody.concat(
                `Date/Time : ${dateTimeLocalFormatInString(parseInt(from))} `,
            );
            mailBody = mailBody.concat(
                toDate ? ` and ${dateTimeLocalFormatInString(parseInt(to))}<br>` : `<br><br>`,
            );
            if (
                user_data.data.staff_employment_type === ACADEMIC ||
                user_data.data.staff_employment_type === 'both'
            ) {
                // To Dean, Vice Dean, Department Chairman
                roleData = await base_control.get_list(
                    role,
                    {
                        name: { $nin: ['Super Admin', 'ADMIN'] },
                        $or: [
                            {
                                'modules.name': 'Calendar',
                                $and: [
                                    { 'modules.pages.name': 'Institution Calendar' },
                                    { 'modules.pages.actions.name': 'Create' },
                                    { 'modules.pages.actions.name': 'Add Event' },
                                    { 'modules.pages.actions.name': 'Publish' },
                                ],
                            },
                            {
                                'modules.name': 'Program Calendar',
                                $and: [
                                    { 'modules.pages.name': 'Dashboard' },
                                    { 'modules.pages.actions.name': 'Calendar Settings' },
                                    { 'modules.pages.actions.name': 'Add Course' },
                                    { 'modules.pages.actions.name': 'Review Calendar' },
                                ],
                            },
                        ],
                    },
                    { _id: 1 },
                );
                roleIds = roleData.status ? roleData.data.map((ele) => ele._id.toString()) : [];
                roleAssign = await base_control.get_list(
                    role_assign,
                    {
                        'roles.isAdmin': true,
                        'roles.program._program_id': convertToMongoObjectId(applicantProgramId),
                        $or: [
                            { 'roles._role_id': roleIds },
                            {
                                'roles.department._department_id':
                                    convertToMongoObjectId(applicantDepartmentId),
                            },
                        ],
                    },
                    { _user_id: 1 },
                );
            }

            if (schedules && schedules.length) {
                const scheduleIds = schedules.map((ele) => ele.schedule_id.toString());
                // Course Schedule Datas
                const scheduleData = await get_list(
                    course_schedule,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: scheduleIds,
                        // type: 'regular',
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        // 'session.session_type': 1,
                        subjects: 1,
                        program_name: 1,
                        _program_id: 1,
                        _course_id: 1,
                        course_code: 1,
                        session: 1,
                        type: 1,
                        title: 1,
                        sub_type: 1,
                        merge_status: 1,
                        merge_with: 1,
                        merge_sessions: [],
                        term: 1,
                        level_no: 1,
                        schedule_date: 1,
                        start: 1,
                        end: 1,
                        topic: 1,
                        substitute_staffs: 1,
                        staffs: 1,
                    },
                );
                scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];

                const bulkUpdateObj = [];
                for (scheduleElement of schedules) {
                    const scheduledElement = scheduleData.data.find(
                        (ele) => ele._id.toString() === scheduleElement.schedule_id.toString(),
                    );
                    if (scheduledElement) {
                        const scheduleStaff = scheduledElement.staffs;
                        if (scheduledElement.staffs && scheduledElement.staffs.length > 1) {
                            const availableStaffs = scheduleStaff.filter(
                                (ele) => ele.status === PENDING,
                            );
                            if (
                                scheduleElement.substitute_staff === undefined &&
                                availableStaffs.length < 2
                            )
                                return res
                                    .status(410)
                                    .send(
                                        common_files.response_function(
                                            res,
                                            410,
                                            true,
                                            `${scheduledElement.session.session_topic} is need Substitute Staff`,
                                            scheduledElement,
                                        ),
                                    );
                        }

                        const substituteElement = scheduledElement.substitute_staffs.find(
                            (ele) => ele._assigned_to.toString() === staff_id.toString(),
                        );
                        const pushObj = {
                            updateOne: {
                                filter: { _id: convertToMongoObjectId(scheduledElement._id) },
                            },
                        };
                        if (!substituteElement) {
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            )
                                pushObj.updateOne.update = {
                                    $push: {
                                        substitute_staffs: {
                                            _assigned_to: staff_id,
                                            _substitute_staff_id:
                                                scheduleElement.substitute_staff._staff_id,
                                            substitute_staff_name:
                                                scheduleElement.substitute_staff.name,
                                        },
                                    },
                                };

                            const staffLoc = scheduleStaff.findIndex(
                                (ele) => ele._staff_id.toString() === staff_id.toString(),
                            );
                            scheduleStaff[staffLoc].status = LEAVE;
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            ) {
                                scheduleStaff.push({
                                    staff_name: scheduleElement.substitute_staff.name,
                                    _staff_id: scheduleElement.substitute_staff._staff_id,
                                    status: PENDING,
                                });
                                pushObj.updateOne.update = {
                                    $set: {
                                        staffs: scheduleStaff,
                                    },
                                    $push: {
                                        substitute_staffs: {
                                            _assigned_to: staff_id,
                                            _substitute_staff_id:
                                                scheduleElement.substitute_staff._staff_id,
                                            substitute_staff_name:
                                                scheduleElement.substitute_staff.name,
                                        },
                                    },
                                };
                            } else {
                                pushObj.updateOne.update = {
                                    $set: {
                                        staffs: scheduleStaff,
                                    },
                                };
                            }
                        } /*  else {
                            if (
                                leave_flow_type_status === 'approve' &&
                                req.body.notify_via &&
                                lmsStatus === 'approve'
                            ) {
                                const staffLoc = scheduleStaff.findIndex(
                                    (ele) =>
                                        ele._staff_id.toString() ===
                                        lmsReviewData._user_id.toString(),
                                );
                                scheduleStaff[staffLoc].status = LEAVE;
                                if (
                                    scheduleElement.substitute_staff &&
                                    scheduleElement.substitute_staff.name &&
                                    scheduleElement.substitute_staff._staff_id
                                )
                                    scheduleStaff.push({
                                        staff_name: scheduleElement.substitute_staff.name,
                                        _staff_id: scheduleElement.substitute_staff._staff_id,
                                        status: PENDING,
                                    });
                            }
                            let staffSet = {
                                staffs: scheduleStaff,
                            };
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            )
                                staffSet = {
                                    'substitute_staffs.$[i]._substitute_staff_id':
                                        scheduleElement.substitute_staff._staff_id,
                                    'substitute_staffs.$[i].substitute_staff_name':
                                        scheduleElement.substitute_staff.name,
                                    staffs: scheduleStaff,
                                };
                            pushObj.updateOne.update = {
                                $set: staffSet,
                            };
                            pushObj.updateOne.arrayFilters = [
                                {
                                    'i._assigned_to': convertToMongoObjectId(
                                        lmsReviewData._user_id,
                                    ),
                                },
                            ];
                        } */
                        if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
                    }
                }
                if (bulkUpdateObj.length)
                    console.log(await bulk_write(course_schedule, bulkUpdateObj));

                scheduleData.data = mergeSchedule(scheduleData.data);
                for (scheduleElement of scheduleData.data) {
                    let title = '';
                    let subTypes = [];
                    if (scheduleElement.merge_status === false) {
                        switch (scheduleElement.type) {
                            case REGULAR:
                                title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no} - ${scheduleElement.session.session_topic}`;
                                subTypes.push(scheduleElement.session.session_type);
                                break;
                            case SUPPORT_SESSION:
                                title = scheduleElement.title;
                                subTypes.push(scheduleElement.sub_type);
                                break;
                            case EVENT:
                                title = scheduleElement.title;
                                subTypes.push(scheduleElement.sub_type);
                                break;
                            default:
                                break;
                        }
                    } else {
                        title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no}`;
                        subTypes.push(scheduleElement.session.session_type);
                        for (mergeSessionElement of scheduleElement.merge_sessions) {
                            title = title.concat(
                                `, ${mergeSessionElement.delivery_symbol}${mergeSessionElement.delivery_no}`,
                            );
                            subTypes.push(mergeSessionElement.session_type);
                        }
                    }
                    subTypes = [...new Set(subTypes)];
                    scheduleElement.mailTitle = title;
                    scheduleElement.subTypes = subTypes;
                }
                // Notification Push
                const scheduleCourseId = scheduleData.data.map((ele) => ele && ele._course_id);
                // Course Data Get
                const { data: courseData } = await get_list(
                    course,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: scheduleCourseId,
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        coordinators: 1,
                    },
                );

                // Checking scheduleData In table Mail Push
                let substituteStaffIds = [];
                for (const [index, scheduleElement] of scheduleData.data.entries()) {
                    const scheduleChangesElement = schedules.find(
                        (ele) =>
                            ele.schedule_id.toString() === scheduleElement._id.toString() &&
                            ele.substitute_staff &&
                            ele.substitute_staff.name,
                    );
                    const scheduleAlteredObj = {
                        'S NO': index + 1,
                        'Session Type':
                            scheduleElement.type.charAt(0).toUpperCase() +
                            scheduleElement.type.slice(1),
                        Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                        From: scheduleTimeFormateChange(scheduleElement.start),
                        To: scheduleTimeFormateChange(scheduleElement.end),
                        Program: scheduleElement.program_name,
                        Course: scheduleElement.course_code,
                        course_id: scheduleElement._course_id,
                        term: scheduleElement.term,
                        Subject: scheduleElement.subjects.map((ele) => ele.subject_name).toString(),
                        Title: scheduleElement.mailTitle,
                        'Substitute Staff': '',
                    };
                    if (scheduleChangesElement) {
                        scheduleAlteredObj['Substitute Staff'] = nameFormatter(
                            scheduleChangesElement.substitute_staff.name,
                        );
                        scheduleAlteredObj.staff_id =
                            scheduleChangesElement.substitute_staff._staff_id;
                        substituteStaffIds.push(
                            scheduleChangesElement.substitute_staff._staff_id.toString(),
                        );
                    }
                    scheduleAlteredData.push(scheduleAlteredObj);
                    // if (scheduleChangesElement)
                    //     scheduleAlteredData.push({
                    //         'S NO': index + 1,
                    //         'Session Type':
                    //             scheduleElement.type.charAt(0).toUpperCase() +
                    //             scheduleElement.type.slice(1),
                    //         Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                    //         From: scheduleTimeFormateChange(scheduleElement.start),
                    //         To: scheduleTimeFormateChange(scheduleElement.end),
                    //         Program: scheduleElement.program_name,
                    //         Course: scheduleElement.course_code,
                    //         course_id: scheduleElement._course_id,
                    //         term: scheduleElement.term,
                    //         Subject: scheduleElement.subjects
                    //             .map((ele) => ele.subject_name)
                    //             .toString(),
                    //         Title: scheduleElement.mailTitle,
                    //         // Topic: scheduleElement.topic ? scheduleElement.topic : '',
                    //         'Substitute Staff': nameFormatter(
                    //             scheduleChangesElement.substitute_staff.name,
                    //         ),
                    //         staff_id: scheduleChangesElement.substitute_staff._staff_id,
                    //     });
                }

                // const substituteStaffIds = [
                //     ...new Set(
                //         scheduleAlteredData.map(
                //             (ele) => ele && ele.staff_id && ele.staff_id.toString(),
                //         ),
                //     ),
                // ];
                substituteStaffIds = [...new Set(substituteStaffIds)];
                // Substitute Staff Notification Push
                for (substituteElement of substituteStaffIds) {
                    const substituteStaffsSchedule = clone(
                        scheduleAlteredData.filter(
                            (ele) =>
                                ele &&
                                ele.staff_id &&
                                ele.staff_id.toString() === substituteElement.toString(),
                        ),
                    );
                    substituteStaffsSchedule.forEach((ele) => {
                        delete ele.course_id;
                        delete ele.term;
                        delete ele['Substitute Staff'];
                        delete ele.staff_id;
                    });
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === substituteElement.toString(),
                    );
                    const mailSubject = `Assigned as a Substitute staff`;
                    const mailBody = `You have been assigned as a Substitute staff for the following sessions<br><br><b>Substitute for ${nameFormatter(
                        applicantData.name,
                    )}</b><br>`;
                    const smsContent = `You have been assigned as a Substitute staff; kindly check your mail to know more
                information about the sessions.`;
                    await contentScheduleMailPush(
                        substituteStaffDetails,
                        notify_via,
                        substituteStaffsSchedule,
                        mailSubject,
                        mailBody,
                        smsContent,
                        approverData,
                    );
                }
                staffsScheduleData = clone(scheduleAlteredData);
                // courseData
                for (courseElement of courseData) {
                    if (courseElement.coordinators && courseElement.coordinators.length) {
                        for (coordinatorsElement of courseElement.coordinators) {
                            const courseScheduleBasedTerm = clone(
                                scheduleAlteredData.filter(
                                    (ele) =>
                                        ele.course_id.toString() === courseElement._id.toString() &&
                                        ele.term === coordinatorsElement.term,
                                ),
                            );
                            if (courseScheduleBasedTerm.length !== 0) {
                                // const courseSchedule = clone(staffsScheduleData);
                                // courseSchedule.forEach((ele) => {
                                //     delete ele.course_id;
                                //     delete ele.staff_id;
                                // });
                                courseScheduleBasedTerm.forEach((ele) => {
                                    delete ele.course_id;
                                    delete ele.term;
                                    delete ele.staff_id;
                                });
                                let subMailBody = mailBody.toString();
                                subMailBody = subMailBody.concat(
                                    `<br>List of Sessions along with Substitute staff<br>`,
                                );
                                const substituteStaffDetails = userData.find(
                                    (ele) =>
                                        ele._id.toString() ===
                                        coordinatorsElement._user_id.toString(),
                                );
                                if (substituteStaffDetails) {
                                    mailedStaff.push(coordinatorsElement._user_id.toString());
                                    const mailSubject = `Absences Report application summary of : ${nameFormatter(
                                        applicantData.name,
                                    )}`;
                                    const smsContent = `Your Absences has been Reported added check your mail`;
                                    await contentScheduleMailPush(
                                        substituteStaffDetails,
                                        notify_via,
                                        courseScheduleBasedTerm,
                                        mailSubject,
                                        mailBody,
                                        smsContent,
                                        approverData,
                                    );
                                }
                            }
                        }
                    }
                }
            }
            // Vice Dean & Department Chairman Mail Push & Also to Approvers
            if (
                applicantData.staff_employment_type === ACADEMIC ||
                applicantData.staff_employment_type === 'both'
            ) {
                if (staffsScheduleData.length !== 0)
                    mailBody = mailBody.concat(
                        `<br>List of Sessions along with Substitute staff<br>`,
                    );
                console.log(roleAssign.data);
                let userIdsToPush =
                    roleAssign &&
                    roleAssign.data &&
                    Array.isArray(roleAssign.data) &&
                    roleAssign.data.map((ele2) => ele2._user_id.toString())
                        ? roleAssign.data.map((ele2) => ele2._user_id.toString())
                        : [];
                userIdsToPush = [...new Set(userIdsToPush)];
                for (staffElement of userIdsToPush) {
                    staffsScheduleData.forEach((ele) => {
                        delete ele.course_id;
                        delete ele.term;
                        delete ele.staff_id;
                    });
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === staffElement.toString(),
                    );
                    if (
                        substituteStaffDetails &&
                        !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                    ) {
                        mailedStaff.push(staffElement.toString());
                        const mailSubject = `Absences Report application summary of : ${nameFormatter(
                            applicantData.name,
                        )}`;
                        const smsContent = `${nameFormatter(
                            applicantData.name,
                        )} Absences Report has been added check your mail`;
                        await contentScheduleMailPush(
                            substituteStaffDetails,
                            notify_via,
                            staffsScheduleData,
                            mailSubject,
                            mailBody,
                            smsContent,
                            approverData,
                        );
                    }
                }
            } else {
                const userIdsToPush = [staff_id.toString()];
                for (staffElement of userIdsToPush) {
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === staffElement.toString(),
                    );
                    if (
                        substituteStaffDetails &&
                        !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                    ) {
                        mailedStaff.push(staffElement.toString());
                        const mailSubject = `Absences Report application summary of : ${nameFormatter(
                            applicantData.name,
                        )}`;
                        const smsContent = `${nameFormatter(
                            applicantData.name,
                        )}  Absences Report has been added check your mail`;
                        await contentScheduleMailPush(
                            substituteStaffDetails,
                            notify_via,
                            staffsScheduleData,
                            mailSubject,
                            mailBody,
                            smsContent,
                            approverData,
                        );
                    }
                }
            }
            // Mail Push for Applicant
            const applicantMailSubject = `Absences Report Status`;
            let applicantMailBody = `Your Absences Reported for dates ${dateTimeLocalFormatInString(
                parseInt(from),
            )}`;
            applicantMailBody = applicantMailBody.concat(
                toDate ? ` and ${dateTimeLocalFormatInString(parseInt(to))}<br>` : `<br><br>`,
            );
            if (
                (applicantData.staff_employment_type === ACADEMIC ||
                    applicantData.staff_employment_type === 'both') &&
                staffsScheduleData.length
            )
                applicantMailBody = applicantMailBody.concat(
                    `<br>List of Sessions along with Substitute staff<br>`,
                );
            let applicantSmsContent = `Your Absences Reported for dates ${dateTimeLocalFormatInString(
                parseInt(from),
            )}`;
            applicantSmsContent = applicantSmsContent.concat(
                toDate ? `and ${dateTimeLocalFormatInString(parseInt(to))}<br>` : '',
            );
            applicantSmsContent = applicantSmsContent.concat(` has been added.`);
            await contentScheduleMailPush(
                applicantData,
                notify_via,
                staffsScheduleData,
                applicantMailSubject,
                applicantMailBody,
                applicantSmsContent,
                approverData,
            );
            // Mail Push to HR
            const { data: lmsSettingData } = await get(
                lms,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                { hr_contact: 1 },
            );
            // Mail Push to HR
            if (lmsSettingData.hr_contact) {
                const hrDetails = {
                    name: { first: 'HR', last: ' ' },
                    email: lmsSettingData.hr_contact,
                };
                const mailSubject = `Absences Report application summary of : ${nameFormatter(
                    applicantData.name,
                )}`;
                const smsContent = `Absences Report of ${nameFormatter(
                    applicantData.name,
                )} has been added check your mail`;
                await contentScheduleMailPush(
                    hrDetails,
                    notify_via,
                    staffsScheduleData,
                    mailSubject,
                    mailBody,
                    smsContent,
                    approverData,
                );
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('LEAVE_APPLIED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update_report_staff_absence = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _institution_calendar_id,
                staff_id,
                user_type,
                type,
                from,
                to,
                days,
                is_noticed,
                reason,
                _leave_reason_doc,
                created_by,
                notify_via,
                schedules,
            },
            params: { lmsReviewId },
        } = req;
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.body.staff_id), user_type: constant.EVENT_WHOM.STAFF };
        const user_data = await base_control.get(user, query, {});
        let applicantProgram;
        let applicantProgramId;
        let applicantDepartmentId;
        if (
            user_data.data.staff_employment_type === ACADEMIC ||
            user_data.data.staff_employment_type === 'both'
        ) {
            applicantProgram = user_data.data.academic_allocation.find(
                (ele2) => ele2.allocation_type === PRIMARY,
            );
            applicantProgramId = applicantProgram ? applicantProgram._program_id.toString() : '';
            applicantDepartmentId = applicantProgram
                ? applicantProgram._department_id.toString()
                : '';
        }
        // const primary_loc = user_data.data.academic_allocation.findIndex(
        //     (i) => i.allocation_type == 'primary',
        // );
        // const program_department = user_data.data.academic_allocation[primary_loc];
        const role_ids = [];
        const lms_setting = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id) },
            { leave_approval: 1 },
        );
        if (!lms_setting.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_LEAVE_SETTING_FOUND'),
                        req.t('NO_LEAVE_SETTING_FOUND'),
                    ),
                );
        lms_setting.data.leave_approval.forEach((element) => {
            if (
                user_data.data.staff_employment_type.toString() != 'both' &&
                element.staff_type.toString() == user_data.data.staff_employment_type.toString()
            ) {
                if (element.role && element.role._roles_id)
                    role_ids.push(ObjectId(element.role._roles_id));
            } else {
                if (element.role && element.role._roles_id)
                    role_ids.push(ObjectId(element.role._roles_id));
            }
        });

        const leave_data_query = {
            _id: ObjectId(req.params.id),
        };
        const leave_data = await base_control.get(lms_review, leave_data_query, {});

        const leave_check_query = {
            _id: { $not: { $eq: ObjectId(req.params.id) } },
            _institution_id: ObjectId(req.headers._institution_id),
            _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
            user_type: req.body.user_type,
            _user_id: ObjectId(req.body.staff_id),
            isActive: true,
            isDeleted: false,
        };
        const leave_check = await base_control.get_list(lms_review, leave_check_query, {});
        if (leave_check.status) {
            // console.log(req.body.type);
            for (element of leave_check.data) {
                const from = dateTimeLocalFormatter(element.from);
                const to = dateTimeLocalFormatter(element.to);
                const f = dateTimeLocalFormatter(parseInt(req.body.from));
                const t = dateTimeLocalFormatter(parseInt(req.body.to));
                // console.log(from, ' ', to)
                // console.log(f, ' ', t)
                // console.log((from <= f && to >= f), ' ', (from <= t && to >= t));
                // console.log((f <= from && t > from), ' ', (f < to && t >= to));
                if ((f <= from && t > from) || (f < to && t >= to) || (t > from && f < to))
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                                req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                            ),
                        );
            }
        }
        const dt1 = dateTimeLocalFormatter(parseInt(from));
        const dt2 = dateTimeLocalFormatter(parseInt(to));
        const calc_days = await (await dayCalculation(dt1, dt2)).withOutHolidays;
        //Created By Data
        const query_a = { _id: ObjectId(req.body.created_by) };
        const created_by_data = await base_control.get(user, query_a, {});
        //return res.send(created_by_data);
        const duration = 0;
        const objs = {
            user_type: req.body.user_type,
            _user_id: req.body.staff_id,
            type: req.body.type,
            from: req.body.from,
            to: req.body.to,
            days: calc_days,
            permission_hour: duration,
            is_noticed: req.body.is_noticed,
            notifyVia: req.body.notify_via,
            reason: req.body.reason,
            _leave_reason_doc: req.body._leave_reason_doc,
            status: 'approved',
            status_time: common_functions.timestampNow(),
            // review: constant.LEAVE_FLOW_TYPE_STATUS.APPROVED,
            // forwarded: constant.LEAVE_FLOW_TYPE_STATUS.APPROVED,
            // approval: constant.LEAVE_FLOW_TYPE_STATUS.APPROVED,
            payment_status: constant.PAYMENT_STATUS.PAID,
            created_by: {
                _person_id: req.body.created_by,
            },
        };
        const query_l = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(lms_review, query_l, objs);
        if (doc.status) {
            //
            //Previous data revoke schedule
            const csDataP = await base_control.get_list(
                course_schedule,
                {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                    schedule_date: {
                        $gte: leave_data.data.from,
                        $lte: leave_data.data.to,
                    },
                    isDeleted: false,
                    'staffs._staff_id': ObjectId(req.body.staff_id),
                },
                { _id: 1, schedule_date: 1, staffs: 1 },
            );

            if (csDataP.status) {
                const bulk_data = [];
                csDataP.data.forEach((eleCS) => {
                    bulk_data.push({
                        updateOne: {
                            filter: {
                                _id: ObjectId(eleCS._id),
                            },
                            update: {
                                $set: {
                                    'staffs.$[i].status': PENDING,
                                },
                            },
                            arrayFilters: [{ 'i._staff_id': ObjectId(req.body.staff_id) }],
                        },
                    });
                });
                if (bulk_data.length > 0) await base_control.bulk_write(course_schedule, bulk_data);
            }
            //return res.send(csDataP);
            // //update in course schedule for this staff
            // const csData = await base_control.get_list(
            //     course_schedule,
            //     {
            //         _institution_id: ObjectId(req.headers._institution_id),
            //         _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
            //         schedule_date: {
            //             $gte: dateTimeLocalFormatter(parseInt(req.body.from)),
            //             $lte: dateTimeLocalFormatter(parseInt(req.body.to)),
            //         },
            //         isDeleted: false,
            //         'staffs._staff_id': ObjectId(req.body.staff_id),
            //     },
            //     { _id: 1, schedule_date: 1, staffs: 1 },
            // );

            // if (csData.status) {
            //     const bulk_data = [];
            //     csData.data.forEach((eleCS) => {
            //         bulk_data.push({
            //             updateOne: {
            //                 filter: {
            //                     _id: ObjectId(eleCS._id),
            //                 },
            //                 update: {
            //                     $set: {
            //                         'staffs.$[i].status': 'pending',
            //                     },
            //                 },
            //                 arrayFilters: [{ 'i._staff_id': ObjectId(req.body.staff_id) }],
            //             },
            //         });
            //     });
            //     const changes = await base_control.bulk_write(course_schedule, bulk_data);
            // }
            const fromDate = dateTimeLocalFormatter(parseInt(from));
            const toDate = dateTimeLocalFormatter(parseInt(to));
            // Applicant & Approver User Data
            const userQuery = {
                // _id: {
                //     $in: [
                //         convertToMongoObjectId(lmsReviewData._user_id),
                //         convertToMongoObjectId(_person_id),
                //     ],
                // },
                user_type: STAFF,
                status: COMPLETED,
                isDeleted: false,
                isActive: true,
            };
            const { data: userData } = await get_list(user, userQuery, {
                user_id: 1,
                name: 1,
                email: 1,
                mobile: 1,
                academic_allocation: 1,
                staff_employment_type: 1,
            });
            const applicantData = user_data.data;
            const approverData = created_by_data.data;
            // Notification Push
            const scheduleAlteredData = [];
            const mailedStaff = [];
            let roleData = {};
            let roleIds = {};
            let roleAssign = { data: [] };
            let staffsScheduleData = [];
            let mailBody;
            mailBody = `<b>Report application summary-Approved</b><br><br>`;
            mailBody = mailBody.concat(`Staff name : ${nameFormatter(applicantData.name)}<br>`);
            mailBody = mailBody.concat(`Employee id : ${applicantData.user_id}<br>`);
            mailBody = mailBody.concat(`Reason : ${reason}<br>`);
            mailBody = mailBody.concat(`Date/Time : ${dateTimeLocalFormatInString(fromDate)} `);
            mailBody = mailBody.concat(
                toDate ? `and ${dateTimeLocalFormatInString(toDate)}<br>` : `<br><br>`,
            );
            if (
                user_data.data.staff_employment_type === ACADEMIC ||
                user_data.data.staff_employment_type === 'both'
            ) {
                // To Dean, Vice Dean, Department Chairman
                roleData = await base_control.get_list(
                    role,
                    {
                        name: { $nin: ['Super Admin', 'ADMIN'] },
                        $or: [
                            {
                                'modules.name': 'Calendar',
                                $and: [
                                    { 'modules.pages.name': 'Institution Calendar' },
                                    { 'modules.pages.actions.name': 'Create' },
                                    { 'modules.pages.actions.name': 'Add Event' },
                                    { 'modules.pages.actions.name': 'Publish' },
                                ],
                            },
                            {
                                'modules.name': 'Program Calendar',
                                $and: [
                                    { 'modules.pages.name': 'Dashboard' },
                                    { 'modules.pages.actions.name': 'Calendar Settings' },
                                    { 'modules.pages.actions.name': 'Add Course' },
                                    { 'modules.pages.actions.name': 'Review Calendar' },
                                ],
                            },
                        ],
                    },
                    { _id: 1 },
                );
                roleIds = roleData.status ? roleData.data.map((ele) => ele._id.toString()) : [];
                roleAssign = await base_control.get_list(
                    role_assign,
                    {
                        'roles.isAdmin': true,
                        'roles.program._program_id': convertToMongoObjectId(applicantProgramId),
                        $or: [
                            { 'roles._role_id': roleIds },
                            {
                                'roles.department._department_id':
                                    convertToMongoObjectId(applicantDepartmentId),
                            },
                        ],
                    },
                    { _user_id: 1 },
                );
            }

            if (schedules && schedules.length) {
                const scheduleIds = schedules.map((ele) => ele.schedule_id.toString());
                // Course Schedule Datas
                const scheduleData = await get_list(
                    course_schedule,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: scheduleIds,
                        // type: 'regular',
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        // 'session.session_type': 1,
                        subjects: 1,
                        program_name: 1,
                        _program_id: 1,
                        _course_id: 1,
                        course_code: 1,
                        session: 1,
                        type: 1,
                        title: 1,
                        sub_type: 1,
                        merge_status: 1,
                        merge_with: 1,
                        merge_sessions: [],
                        term: 1,
                        level_no: 1,
                        schedule_date: 1,
                        start: 1,
                        end: 1,
                        topic: 1,
                        substitute_staffs: 1,
                        staffs: 1,
                    },
                );
                scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];

                const bulkUpdateObj = [];
                for (scheduleElement of schedules) {
                    const scheduledElement = scheduleData.data.find(
                        (ele) => ele._id.toString() === scheduleElement.schedule_id.toString(),
                    );
                    if (scheduledElement) {
                        const scheduleStaff = scheduledElement.staffs;
                        if (scheduledElement.staffs && scheduledElement.staffs.length > 1) {
                            const availableStaffs = scheduleStaff.filter(
                                (ele) => ele.status === PENDING,
                            );
                            if (
                                scheduleElement.substitute_staff === undefined &&
                                availableStaffs.length < 2
                            )
                                return res
                                    .status(410)
                                    .send(
                                        common_files.response_function(
                                            res,
                                            410,
                                            true,
                                            `${scheduledElement.session.session_topic} is need Substitute Staff`,
                                            scheduledElement,
                                        ),
                                    );
                        }

                        const substituteElement = scheduledElement.substitute_staffs.find(
                            (ele) => ele._assigned_to.toString() === staff_id.toString(),
                        );
                        const pushObj = {
                            updateOne: {
                                filter: { _id: convertToMongoObjectId(scheduledElement._id) },
                            },
                        };
                        if (!substituteElement) {
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            )
                                pushObj.updateOne.update = {
                                    $push: {
                                        substitute_staffs: {
                                            _assigned_to: staff_id,
                                            _substitute_staff_id:
                                                scheduleElement.substitute_staff._staff_id,
                                            substitute_staff_name:
                                                scheduleElement.substitute_staff.name,
                                        },
                                    },
                                };

                            const staffLoc = scheduleStaff.findIndex(
                                (ele) => ele._staff_id.toString() === staff_id.toString(),
                            );
                            scheduleStaff[staffLoc].status = LEAVE;
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            ) {
                                scheduleStaff.push({
                                    staff_name: scheduleElement.substitute_staff.name,
                                    _staff_id: scheduleElement.substitute_staff._staff_id,
                                    status: PENDING,
                                });
                                pushObj.updateOne.update = {
                                    $set: {
                                        staffs: scheduleStaff,
                                    },
                                    $push: {
                                        substitute_staffs: {
                                            _assigned_to: staff_id,
                                            _substitute_staff_id:
                                                scheduleElement.substitute_staff._staff_id,
                                            substitute_staff_name:
                                                scheduleElement.substitute_staff.name,
                                        },
                                    },
                                };
                            } else {
                                pushObj.updateOne.update = {
                                    $set: {
                                        staffs: scheduleStaff,
                                    },
                                };
                            }
                        }
                        if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
                    }
                }
                if (bulkUpdateObj.length)
                    console.log(await bulk_write(course_schedule, bulkUpdateObj));

                scheduleData.data = mergeSchedule(scheduleData.data);
                for (scheduleElement of scheduleData.data) {
                    let title = '';
                    let subTypes = [];
                    if (scheduleElement.merge_status === false) {
                        switch (scheduleElement.type) {
                            case REGULAR:
                                title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no} - ${scheduleElement.session.session_topic}`;
                                subTypes.push(scheduleElement.session.session_type);
                                break;
                            case SUPPORT_SESSION:
                                title = scheduleElement.title;
                                subTypes.push(scheduleElement.sub_type);
                                break;
                            case EVENT:
                                title = scheduleElement.title;
                                subTypes.push(scheduleElement.sub_type);
                                break;
                            default:
                                break;
                        }
                    } else {
                        title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no}`;
                        subTypes.push(scheduleElement.session.session_type);
                        for (mergeSessionElement of scheduleElement.merge_sessions) {
                            title = title.concat(
                                `, ${mergeSessionElement.delivery_symbol}${mergeSessionElement.delivery_no}`,
                            );
                            subTypes.push(mergeSessionElement.session_type);
                        }
                    }
                    subTypes = [...new Set(subTypes)];
                    scheduleElement.mailTitle = title;
                    scheduleElement.subTypes = subTypes;
                }

                // Notification Push
                const scheduleCourseId = scheduleData.data.map((ele) => ele && ele._course_id);
                // Course Data Get
                const { data: courseData } = await get_list(
                    course,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: scheduleCourseId,
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        coordinators: 1,
                    },
                );

                // Checking scheduleData In table Mail Push
                let substituteStaffIds = [];
                for (const [index, scheduleElement] of scheduleData.data.entries()) {
                    const scheduleChangesElement = schedules.find(
                        (ele) =>
                            ele.schedule_id.toString() === scheduleElement._id.toString() &&
                            ele.substitute_staff &&
                            ele.substitute_staff.name,
                    );
                    const scheduleAlteredObj = {
                        'S NO': index + 1,
                        'Session Type':
                            scheduleElement.type.charAt(0).toUpperCase() +
                            scheduleElement.type.slice(1),
                        Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                        From: scheduleTimeFormateChange(scheduleElement.start),
                        To: scheduleTimeFormateChange(scheduleElement.end),
                        Program: scheduleElement.program_name,
                        Course: scheduleElement.course_code,
                        course_id: scheduleElement._course_id,
                        term: scheduleElement.term,
                        Subject: scheduleElement.subjects.map((ele) => ele.subject_name).toString(),
                        Title: scheduleElement.mailTitle,
                        'Substitute Staff': '',
                    };
                    if (scheduleChangesElement) {
                        scheduleAlteredObj['Substitute Staff'] = nameFormatter(
                            scheduleChangesElement.substitute_staff.name,
                        );
                        scheduleAlteredObj.staff_id =
                            scheduleChangesElement.substitute_staff._staff_id;
                        substituteStaffIds.push(
                            scheduleChangesElement.substitute_staff._staff_id.toString(),
                        );
                    }
                    scheduleAlteredData.push(scheduleAlteredObj);
                    // if (scheduleChangesElement)
                    //     scheduleAlteredData.push({
                    //         'S NO': index + 1,
                    //         'Session Type':
                    //             scheduleElement.type.charAt(0).toUpperCase() +
                    //             scheduleElement.type.slice(1),
                    //         Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                    //         From: scheduleTimeFormateChange(scheduleElement.start),
                    //         To: scheduleTimeFormateChange(scheduleElement.end),
                    //         Program: scheduleElement.program_name,
                    //         Course: scheduleElement.course_code,
                    //         course_id: scheduleElement._course_id,
                    //         term: scheduleElement.term,
                    //         Subject: scheduleElement.subjects
                    //             .map((ele) => ele.subject_name)
                    //             .toString(),
                    //         Title: scheduleElement.mailTitle,
                    //         // Topic: scheduleElement.topic ? scheduleElement.topic : '',
                    //         'Substitute Staff': nameFormatter(
                    //             scheduleChangesElement.substitute_staff.name,
                    //         ),
                    //         staff_id: scheduleChangesElement.substitute_staff._staff_id,
                    //     });
                }

                // const substituteStaffIds = [
                //     ...new Set(
                //         scheduleAlteredData.map(
                //             (ele) => ele && ele.staff_id && ele.staff_id.toString(),
                //         ),
                //     ),
                // ];
                substituteStaffIds = [...new Set(substituteStaffIds)];
                // Substitute Staff Notification Push
                for (substituteElement of substituteStaffIds) {
                    const substituteStaffsSchedule = clone(
                        scheduleAlteredData.filter(
                            (ele) =>
                                ele &&
                                ele.staff_id &&
                                ele.staff_id.toString() === substituteElement.toString(),
                        ),
                    );
                    substituteStaffsSchedule.forEach((ele) => {
                        delete ele.course_id;
                        delete ele.term;
                        delete ele['Substitute Staff'];
                        delete ele.staff_id;
                    });
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === substituteElement.toString(),
                    );
                    const mailSubject = `Assigned as a Substitute staff`;
                    const mailBody = `You have been assigned as a Substitute staff for the following sessions<br><br><b>Substitute for ${nameFormatter(
                        applicantData.name,
                    )}</b><br>`;
                    const smsContent = `You have been assigned as a Substitute staff; kindly check your mail to know more
                information about the sessions.`;
                    await contentScheduleMailPush(
                        substituteStaffDetails,
                        notify_via,
                        substituteStaffsSchedule,
                        mailSubject,
                        mailBody,
                        smsContent,
                        approverData,
                    );
                }
                staffsScheduleData = clone(scheduleAlteredData);
                // courseData
                for (courseElement of courseData) {
                    if (courseElement.coordinators && courseElement.coordinators.length) {
                        for (coordinatorsElement of courseElement.coordinators) {
                            const courseScheduleBasedTerm = clone(
                                scheduleAlteredData.filter(
                                    (ele) =>
                                        ele.course_id.toString() === courseElement._id.toString() &&
                                        ele.term === coordinatorsElement.term,
                                ),
                            );
                            if (courseScheduleBasedTerm.length !== 0) {
                                // const courseSchedule = clone(staffsScheduleData);
                                // courseSchedule.forEach((ele) => {
                                //     delete ele.course_id;
                                //     delete ele.staff_id;
                                // });
                                let subMailBody = mailBody.toString();
                                subMailBody = subMailBody.concat(
                                    `<br>List of Sessions along with Substitute staff<br>`,
                                );
                                courseScheduleBasedTerm.forEach((ele) => {
                                    delete ele.course_id;
                                    delete ele.term;
                                    delete ele.staff_id;
                                });
                                const substituteStaffDetails = userData.find(
                                    (ele) =>
                                        ele._id.toString() ===
                                        coordinatorsElement._user_id.toString(),
                                );
                                if (substituteStaffDetails) {
                                    mailedStaff.push(coordinatorsElement._user_id.toString());
                                    const mailSubject = `Report Absences application summary of : ${nameFormatter(
                                        applicantData.name,
                                    )}`;
                                    const smsContent = `Report Absences has been added check your mail`;
                                    await contentScheduleMailPush(
                                        substituteStaffDetails,
                                        notify_via,
                                        courseScheduleBasedTerm,
                                        mailSubject,
                                        mailBody,
                                        smsContent,
                                        approverData,
                                    );
                                }
                            }
                        }
                    }
                }
            }
            // Vice Dean & Department Chairman Mail Push & Also to Approvers
            if (
                applicantData.staff_employment_type === ACADEMIC ||
                applicantData.staff_employment_type === 'both'
            ) {
                if (staffsScheduleData.length !== 0)
                    mailBody = mailBody.concat(
                        `<br>List of Sessions along with Substitute staff<br>`,
                    );
                let userIdsToPush = roleAssign.data.map((ele2) => ele2._user_id.toString())
                    ? roleAssign.data.map((ele2) => ele2._user_id.toString())
                    : [];
                userIdsToPush = [...new Set(userIdsToPush)];
                for (staffElement of userIdsToPush) {
                    staffsScheduleData.forEach((ele) => {
                        delete ele.course_id;
                        delete ele.term;
                        delete ele.staff_id;
                    });
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === staffElement.toString(),
                    );
                    if (
                        substituteStaffDetails &&
                        !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                    ) {
                        mailedStaff.push(staffElement.toString());
                        const mailSubject = `Report Absences application summary of : ${nameFormatter(
                            applicantData.name,
                        )}`;
                        const smsContent = `${nameFormatter(
                            applicantData.name,
                        )} Report Absences has been added check your mail`;
                        await contentScheduleMailPush(
                            substituteStaffDetails,
                            notify_via,
                            staffsScheduleData,
                            mailSubject,
                            mailBody,
                            smsContent,
                            approverData,
                        );
                    }
                }
            } else {
                const userIdsToPush = [staff_id.toString()];
                for (staffElement of userIdsToPush) {
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === staffElement.toString(),
                    );
                    if (
                        substituteStaffDetails &&
                        !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                    ) {
                        mailedStaff.push(staffElement.toString());
                        const mailSubject = `Report Absences  application summary of : ${nameFormatter(
                            applicantData.name,
                        )}`;
                        const smsContent = `${nameFormatter(
                            applicantData.name,
                        )} Report Absences has been added check your mail`;
                        await contentScheduleMailPush(
                            substituteStaffDetails,
                            notify_via,
                            staffsScheduleData,
                            mailSubject,
                            mailBody,
                            smsContent,
                            approverData,
                        );
                    }
                }
            }
            // Mail Push for Applicant
            const applicantMailSubject = `Report Absences Status`;
            let applicantMailBody = `Your Report Absences ${dateTimeLocalFormatInString(fromDate)}`;
            applicantMailBody = applicantMailBody.concat(
                toDate ? `and ${dateTimeLocalFormatInString(toDate)}<br>` : `<br><br>`,
            );
            if (
                applicantData.staff_employment_type === ACADEMIC ||
                applicantData.staff_employment_type === 'both'
            )
                applicantMailBody = applicantMailBody.concat(
                    `<br>List of Sessions along with Substitute staff<br>`,
                );
            let applicantSmsContent = `Your Report Absences ${dateTimeLocalFormatInString(
                fromDate,
            )}`;
            applicantSmsContent = applicantSmsContent.concat(
                toDate ? `and ${dateTimeLocalFormatInString(toDate)}<br>` : '',
            );
            applicantSmsContent = applicantSmsContent.concat(` has been added.`);
            await contentScheduleMailPush(
                applicantData,
                notify_via,
                staffsScheduleData,
                applicantMailSubject,
                applicantMailBody,
                applicantSmsContent,
                approverData,
            );
            // Mail Push to HR
            const { data: lmsSettingData } = await get(
                lms,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    isActive: true,
                    isDeleted: false,
                },
                { hr_contact: 1 },
            );
            // Mail Push to HR
            if (lmsSettingData.hr_contact) {
                const hrDetails = {
                    name: { first: 'HR', last: ' ' },
                    email: lmsSettingData.hr_contact,
                };
                const mailSubject = `Report Absences application summary of : ${nameFormatter(
                    applicantData.name,
                )}`;
                const smsContent = `Report Absences has been added check your mail`;
                await contentScheduleMailPush(
                    hrDetails,
                    notify_via,
                    staffsScheduleData,
                    mailSubject,
                    mailBody,
                    smsContent,
                    approverData,
                );
            }
            // if (req.body.notify_via.includes(constant.NOTIFY_VIA.MOBILE)) {
            //     const msg =
            //         '<p>Your leave has been reported by ' +
            //         created_by_data.data.name.first +
            //         ' ' +
            //         created_by_data.data.name.last +
            //         '</p>';
            //     common_functions.send_sms(user_data.data.mobile, msg);
            // }
            // if (req.body.notify_via.includes(constant.NOTIFY_VIA.EMAIL)) {
            //     const message =
            //         '<p>Your leave has been reported by ' +
            //         created_by_data.data.name.first +
            //         ' ' +
            //         created_by_data.data.name.last +
            //         '</p>';
            //     const to_email = user_data.data.email;
            //     const subject = 'Staff Report Absence';
            //     common_functions.send_email(to_email, subject, message);
            // }
            // //Send Mail to Approvers
            // let role_user_query;
            // if (
            //     user_data.data.staff_employment_type.toString() == 'both' ||
            //     user_data.data.staff_employment_type.toString() == 'academic'
            // ) {
            //     role_user_query = {
            //         roles: { $elemMatch: { _role_id: { $in: role_ids } } },
            //         'roles.program': {
            //             $elemMatch: { _program_id: ObjectId(program_department._program_id) },
            //         },
            //         _institution_id: ObjectId(req.headers._institution_id),
            //         isActive: true,
            //         isDeleted: false,
            //     };
            // } else {
            //     role_user_query = {
            //         roles: { $elemMatch: { _role_id: { $in: role_ids } } },
            //         _institution_id: ObjectId(req.headers._institution_id),
            //         isActive: true,
            //         isDeleted: false,
            //     };
            // }
            // const role_details = await base_control.get_list(role_assign, role_user_query, {});
            // // if (!role_details.status)
            // //     return res
            // //         .status(404)
            // //         .send(
            // //             common_files.response_function(
            // //                 res,
            // //                 404,
            // //                 false,
            // //                 'No approvers found',
            // //                 'No approvers found',
            // //             ),
            // //         );
            // role_details.data = role_details.status ? role_details.data : [];
            // // console.log(role_details.data.length);
            // const staff_ids = [];
            // role_details.data.forEach((element) => {
            //     element.roles.forEach((sub_element) => {
            //         if (
            //             sub_element.program.findIndex(
            //                 (i) =>
            //                     i._program_id.toString() ==
            //                     program_department._program_id.toString(),
            //             ) != -1
            //         ) {
            //             if (
            //                 sub_element.department.length == 0 &&
            //                 staff_ids.indexOf(element._user_id) == -1
            //             ) {
            //                 staff_ids.push(ObjectId(element._user_id));
            //             } else if (
            //                 sub_element.department.findIndex(
            //                     (i) =>
            //                         i._department_id.toString() ==
            //                         program_department._department_id.toString(),
            //                 ) != -1 &&
            //                 staff_ids.indexOf(element._user_id) == -1
            //             ) {
            //                 staff_ids.push(ObjectId(element._user_id));
            //             }
            //         }
            //     });
            // });
            // // console.log(staff_ids);
            // const query_c = { _id: { $in: staff_ids } };
            // const approvers_datas = await base_control.get_list(user, query_c, {
            //     name: 1,
            //     email: 1,
            //     mobile: 1,
            // });
            // // return res.send(approvers_datas);
            // const from = dateTimeLocalFormatter(parseInt(req.body.from));
            // const to = dateTimeLocalFormatter(parseInt(req.body.to));
            // approvers_datas.data.forEach((element) => {
            //     const name = element.name.middle
            //         ? element.name.first + ' ' + element.name.middle + ' ' + element.name.last
            //         : element.name.first + ' ' + element.name.last;
            //     const applicant_name = user_data.data.name.middle
            //         ? user_data.data.name.first +
            //           ' ' +
            //           user_data.data.name.middle +
            //           ' ' +
            //           user_data.data.name.last
            //         : user_data.data.name.first + ' ' + user_data.data.name.last;
            //     // let approver_name = approver_data.data.name.middle ? approver_data.data.name.first + ' ' + approver_data.data.name.middle + ' ' + approver_data.data.name.last : approver_data.data.name.first + ' ' + approver_data.data.name.last
            //     if (
            //         req.body.notify_via &&
            //         req.body.notify_via.includes(constant.NOTIFY_VIA.MOBILE)
            //     ) {
            //         const msg =
            //             'Dear ' +
            //             name +
            //             ', ' +
            //             applicant_name +
            //             ' is absent from ' +
            //             from +
            //             ' to ' +
            //             to +
            //             ' dates.';
            //         if (element.mobile) common_functions.send_sms(element.mobile, msg);
            //     }
            //     if (
            //         req.body.notify_via &&
            //         req.body.notify_via.includes(constant.NOTIFY_VIA.EMAIL)
            //     ) {
            //         const message =
            //             'Dear ' +
            //             name +
            //             '<br>' +
            //             applicant_name +
            //             ' is absent from ' +
            //             from +
            //             ' to ' +
            //             to +
            //             ' dates.<br><br> Best regards Ibn Sina National College for Medical Studies';
            //         const to_email = element.email;
            //         const subject = 'Staff Report absent';
            //         common_functions.send_email(to_email, subject, message);
            //     }
            // });
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('LEAVE_APPLIED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update_leave_permission_on_duty = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _institution_calendar_id,
                _user_id,
                user_type,
                type,
                from,
                to,
                reason,
                _leave_reason_doc,
                application_date,
                _leave_category_id,
                category_name,
                _person_id,
                approved_date,
                approved_comments,
                _leave_type_id,
                leave_type_name,
                notifyVia,
                schedules,
            },
        } = req;
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const lmsSetting = await get(lms, { _institution_id: ObjectId(_institution_id) }, {});
        if (!lmsSetting.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                    ),
                );
        const dt1 = dateTimeLocalFormatter(parseInt(from));
        const dt2 = dateTimeLocalFormatter(parseInt(to));
        console.log('From To Dates ', dt1, ' ', dt2);
        const leaveQuery = {
            _id: { $ne: ObjectId(req.params.id) },
            _institution_id: ObjectId(_institution_id),
            _institution_calendar_id: ObjectId(_institution_calendar_id),
            user_type,
            _user_id: ObjectId(_user_id),
            status: { $not: { $eq: 'rejected' } },
            isActive: true,
            isDeleted: false,
        };
        const leave_check = await get_list(lms_review, leaveQuery, {});
        leave_check.data = leave_check.status ? leave_check.data : [];
        // const duplicateCheckStatus = leave_check.data.filter(
        //     (ele) =>
        //         ele && ((ele.from <= dt1 && ele.from >= dt2) || (ele.to >= dt1 && ele.to <= dt2)),
        // );
        // if (duplicateCheckStatus.length !== 0) {
        //     return res
        //         .status(410)
        //         .send(
        //             response_function(
        //                 res,
        //                 410,
        //                 false,
        //                 'Duplicate on Permission/Leave/On_Duty',
        //                 'Duplicate on Permission/Leave/On_Duty',
        //             ),
        //         );
        // }
        for (element of leave_check.data) {
            const from = dateTimeLocalFormatter(element.from);
            const to = dateTimeLocalFormatter(element.to);
            if ((dt1 <= from && dt2 > from) || (dt1 < to && dt2 >= to) || (dt2 > from && dt1 < to))
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            false,
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                        ),
                    );
        }
        let type_data;
        let frequency = 0;
        let duration = 0;
        let calc_days = 0;
        let no_days = 0;
        const diff = dt2 - dt1;
        const diffInDays = diff / (1000 * 60 * 60 * 24);
        no_days = Math.round(diffInDays);
        console.log('No Of days', no_days);
        if (PERMISSION === type) {
            duration = diff_hours(dt1, dt2);
            type_data = lmsSetting.data.category.find(
                (i) => i.category_type == PERMISSION && i.category_to == user_type,
            );
            if (!type_data)
                return res
                    .status(409)
                    .send(
                        response_function(
                            res,
                            409,
                            false,
                            req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
                            req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
                        ),
                    );
            const leaveData = leave_check.data.filter((ele) => ele.type === type);
            frequency = type_data.permission.permission_frequency;
            if (
                type_data.permission.permission_frequency_by === 'year' ||
                type_data.permission.permission_frequency_by === 'null'
            ) {
                if (leaveData.length >= frequency)
                    return res
                        .status(410)
                        .send(
                            response_function(
                                res,
                                410,
                                false,
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                            ),
                        );
            } else if (type_data.permission.permission_frequency_by === 'month') {
                let i = 0;
                for (element of leaveData) {
                    const from = dateTimeLocalFormatter(element.from);
                    if (from.getUTCMonth() === dt1.getUTCMonth()) i += element.days;
                }
                if (frequency <= i)
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                            ),
                        );
            }
        }
        //Check Date are in Academic Calendar
        const institution_calendar_data = await base_control.get(
            institution_calendar,
            { _id: ObjectId(req.body._institution_calendar_id) },
            {},
        );
        if (!institution_calendar_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER'),
                        req.t('ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER'),
                    ),
                );
        const ins_from = dateTimeLocalFormatter(institution_calendar_data.data.start_date);
        const ins_to = dateTimeLocalFormatter(institution_calendar_data.data.end_date);
        console.log('Ins calendar dates ', ins_from, ' ', ins_to);
        const from_dates = dateTimeLocalFormatter(dt1);
        from_dates.setUTCDate(from_dates.getUTCDate() + 1);
        if (!(ins_from < from_dates && ins_to > from_dates && ins_from < dt2 && ins_to > dt2))
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER'),
                        req.t('DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER'),
                    ),
                );
        let payment_status = constant.PAYMENT_STATUS.PAID;
        if (type === LEAVE || type === ONDUTY) {
            let ondutyCheck = false;
            const c_data = lmsSetting.data.category.find(
                (ele) => ele._id.toString() === _leave_category_id.toString(),
            );
            type_data = c_data.leave_type.find(
                (ele) => ele._id.toString() === _leave_type_id.toString(),
            );
            payment_status = type_data.payment;
            const weekendConsideration =
                type_data.weekend_consideration === undefined
                    ? false
                    : type_data.weekend_consideration;
            const dayData = await dayCalculation(dt1, dt2);
            calc_days = weekendConsideration ? dayData.withHolidays : dayData.withOutHolidays;
            frequency = type_data.no_of_days;
            console.log({ dt1, dt2, type_data, frequency, calc_days });
            const leaveData = leave_check.data.filter(
                (ele) =>
                    ele.type === type &&
                    ele.leave_type._leave_type_id &&
                    ele.leave_type._leave_type_id.toString() == _leave_type_id.toString(),
            );
            if (
                (dt1.getUTCMonth() === dt2.getUTCMonth() && frequency < calc_days) ||
                (dt1.getUTCMonth() !== dt2.getUTCMonth() && frequency * 2 < calc_days)
            )
                ondutyCheck = true;
            else if (type_data.entitlement === 'month') {
                let i = calc_days;
                if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
                    i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
                    ondutyCheck = frequency < i;
                    console.log({ frequency, i });
                } else {
                    const diffMonthDates = await diffMonthDateCalculation(
                        dt1,
                        dt2,
                        leaveData,
                        weekendConsideration,
                    );
                    ondutyCheck =
                        frequency < diffMonthDates.fromMonthDates ||
                        frequency < diffMonthDates.toMonthDates;
                    console.log({ frequency, diffMonthDates });
                }
            } else if (type_data.entitlement === 'year') {
                let totalDays = calc_days;
                for (element of leaveData) {
                    totalDays += element.days;
                }
                console.log('Check ', totalDays, frequency < totalDays, frequency > totalDays);
                if (type_data.per_month === 0 || frequency < totalDays) {
                    ondutyCheck = frequency < totalDays;
                } else {
                    if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
                        let i = calc_days;
                        i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
                        ondutyCheck = type_data.per_month < i || frequency < i;
                        console.log({ perMonth: type_data.per_month, frequency, i });
                    } else {
                        const diffMonthDates = await diffMonthDateCalculation(
                            dt1,
                            dt2,
                            leaveData,
                            weekendConsideration,
                        );
                        ondutyCheck =
                            frequency < diffMonthDates.fromMonthDates ||
                            frequency < diffMonthDates.toMonthDates ||
                            (type_data.per_month < diffMonthDates.fromMonthDates &&
                                type_data.per_month < diffMonthDates.toMonthDates);
                        console.log({ perMonth: type_data.per_month, frequency, diffMonthDates });
                    }
                }
            }
            if (ondutyCheck)
                return res
                    .status(410)
                    .send(
                        response_function(
                            res,
                            410,
                            false,
                            'You have crossed your ' + type + ' Month Frequency/Year Frequency',
                            'You have crossed your ' + type + ' Month Frequency/Year Frequency',
                        ),
                    );
        }
        // const cat_data = await base_control.get(
        //     lms,
        //     { _institution_id: ObjectId(req.headers._institution_id) },
        //     {},
        // );
        // if (!cat_data.status)
        //     return res
        //         .status(404)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 404,
        //                 false,
        //                  req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
        //                 {},
        //             ),
        //         );
        // let type_data;
        // let frequency = 0;
        // let duration = 0;
        // const dt1 = dateTimeLocalFormatter(parseInt(req.body.from));
        // const dt2 = dateTimeLocalFormatter(parseInt(req.body.to));
        // let no_days = diff_days(dt1, dt2);
        // console.log(dt1, ' ', dt2);
        // const diff = dt2 - dt1;
        // const diffInDays = diff / (1000 * 60 * 60 * 24);
        // console.log(Math.round(diffInDays));
        // no_days = Math.round(diffInDays);

        // //Check Date are in Academic Calendar
        // const institution_calendar_data = await base_control.get(
        //     institution_calendar,
        //     { _id: ObjectId(req.body._institution_calendar_id) },
        //     {},
        // );
        // if (!institution_calendar_data.status)
        //     return res
        //         .status(404)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 404,
        //                 false,
        //                  req.t('ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER'),
        //                  req.t('ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER'),
        //             ),
        //         );
        // const ins_from = dateTimeLocalFormatter(institution_calendar_data.data.start_date);
        // const ins_to = dateTimeLocalFormatter(institution_calendar_data.data.end_date);
        // console.log('Ins calendar dates ', ins_from, ' ', ins_to);
        // // console.log(ins_from < dt1, ' ', ins_to > dt1);
        // // console.log(ins_from < dt2, ' ', ins_to > dt2);
        // if (!(ins_from < dt1 && ins_to > dt1 && ins_from < dt2 && ins_to > dt2))
        //     return res
        //         .status(409)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 409,
        //                 false,
        //                  req.t('DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER'),
        //                  req.t('DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER'),
        //             ),
        //         );

        // if (constant.LEAVE_TYPE.PERMISSION == req.body.type) {
        //     duration = diff_hours(dt1, dt2);
        //     const type_loc = cat_data.data.category.findIndex(
        //         (i) =>
        //             i.category_type == constant.LEAVE_TYPE.PERMISSION &&
        //             i.category_to == req.body.user_type,
        //     );
        //     type_data = cat_data.data.category[type_loc];
        //     frequency = type_data.permission.permission_frequency;
        // }
        // // let payment_status = constant.PAYMENT_STATUS.PAID;
        // if (
        //     req.body.type == constant.LEAVE_TYPE.LEAVE ||
        //     req.body.type == constant.LEAVE_TYPE.ONDUTY
        // ) {
        //     const c_data = cat_data.data.category.filter((ele) =>
        //         ele._id.equals(req.body._leave_category_id),
        //     );
        //     const l_data = c_data[0].leave_type.filter((ele) =>
        //         ele._id.equals(req.body._leave_type_id),
        //     );
        //     type_data = l_data[0];
        //     // payment_status = l_data[0].payment;
        //     frequency = type_data.no_of_days;
        // }
        // let calc_days = no_days;
        // console.log(dt1, ' ', dt2);
        // if (
        //     constant.LEAVE_TYPE.LEAVE == req.body.type ||
        //     constant.LEAVE_TYPE.ONDUTY == req.body.type
        // ) {
        //     if (
        //         type_data.weekend_consideration != undefined &&
        //         type_data.weekend_consideration == false
        //     ) {
        //         // const days = [
        //         //     { day: 'sunday', no: 0 },
        //         //     { day: 'monday', no: 1 },
        //         //     { day: 'tuesday', no: 2 },
        //         //     { day: 'wednesday', no: 3 },
        //         //     { day: 'thursday', no: 4 },
        //         //     { day: 'friday', no: 5 },
        //         //     { day: 'saturday', no: 6 },
        //         // ];
        //         const weekdays = [
        //             { day: 'friday', no: 5 },
        //             { day: 'saturday', no: 6 },
        //         ];
        //         const daysOfYear = [];
        //         for (let d = dateTimeLocalFormatter(dt1); d <= dt2; d.setDate(d.getDate() + 1)) {
        //             const dt = d;
        //             const no = dt.getDay();
        //             daysOfYear.push({ date: dt, no });
        //         }
        //         const weekdays_1 = daysOfYear.filter((ele) => ele.no == weekdays[0].no);
        //         const weekdays_2 = daysOfYear.filter((ele) => ele.no == weekdays[1].no);
        //         const total_days = daysOfYear.length;
        //         calc_days = total_days - (weekdays_1.length + weekdays_2.length);
        //     }
        // }
        // console.log(dt1, ' ', dt2, ' ', calc_days);
        // console.log(req.body.type, '\n', type_data);
        // no_days = calc_days;
        // const leave_check_query = {
        //     _id: { $ne: ObjectId(req.params.id) },
        //     _institution_id: ObjectId(req.headers._institution_id),
        //     _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
        //     user_type: req.body.user_type,
        //     _user_id: ObjectId(req.body._user_id),
        //     status: { $not: { $eq: 'rejected' } },
        //     isActive: true,
        //     isDeleted: false,
        // };
        // const leave_check = await base_control.get_list(lms_review, leave_check_query, {});
        // if (leave_check.status) {
        //     for (element of leave_check.data) {
        //         const from = dateTimeLocalFormatter(element.from);
        //         const to = dateTimeLocalFormatter(element.to);
        //         // const f = dt1;
        //         // const t = dt2;
        //         // console.log(from, ' ', to)
        //         // console.log(dt1, ' ', dt2)
        //         // console.log(dt1 <= from, ' ', dt2 > from, ' ', dt1 < to, ' ', dt2 >= to);
        //         // console.log((dt1 <= from && dt2 > from), ' ', (dt1 < to && dt2 >= to), ' ', (dt2 > from && dt1 < to))
        //         if (
        //             (dt1 <= from && dt2 > from) ||
        //             (dt1 < to && dt2 >= to) ||
        //             (dt2 > from && dt1 < to)
        //         )
        //             return res
        //                 .status(409)
        //                 .send(
        //                     common_files.response_function(
        //                         res,
        //                         409,
        //                         false,
        //                         'Duplicate on Permission/Leave/On_Duty',
        //                         'Duplicate on Permission/Leave/On_Duty',
        //                     ),
        //                 );
        //     }
        //     //Check his Frequency
        //     if (constant.LEAVE_TYPE.PERMISSION == req.body.type) {
        //         let counts = 0;
        //         if (type_data.permission.permission_frequency_by == 'month') {
        //             let i = 0;
        //             for (element of leave_check.data) {
        //                 if (element.type == req.body.type) {
        //                     counts++;
        //                     const from = dateTimeLocalFormatter(element.from);
        //                     if (from.getUTCMonth() == dt1.getUTCMonth()) i++;
        //                 }
        //             }
        //             if (frequency <= i)
        //                 return res
        //                     .status(409)
        //                     .send(
        //                         common_files.response_function(
        //                             res,
        //                             409,
        //                             false,
        //                              req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                              req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                         ),
        //                     );
        //         } else if (type_data.permission.permission_frequency_by == 'year') {
        //             if (frequency <= counts)
        //                 return res
        //                     .status(409)
        //                     .send(
        //                         common_files.response_function(
        //                             res,
        //                             409,
        //                             false,
        //                              req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                              req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                         ),
        //                     );
        //         }
        //     } else if (
        //         constant.LEAVE_TYPE.LEAVE == req.body.type ||
        //         constant.LEAVE_TYPE.ONDUTY == req.body.type
        //     ) {
        //         if (type_data.entitlement == 'month') {
        //             let i = 0;
        //             for (element of leave_check.data) {
        //                 if (element.type == req.body.type) {
        //                     if (
        //                         element.leave_type &&
        //                         element.leave_type._leave_type_id &&
        //                         element.leave_type._leave_type_id.toString() ==
        //                             req.body._leave_type_id.toString()
        //                     ) {
        //                         const from = dateTimeLocalFormatter(element.from);
        //                         if (from.getUTCMonth() == dt1.getUTCMonth()) i++;
        //                     }
        //                 }
        //             }
        //             if (frequency <= i)
        //                 return res
        //                     .status(409)
        //                     .send(
        //                         common_files.response_function(
        //                             res,
        //                             409,
        //                             false,
        //                             'You have crossed your ' +
        //                                 req.body.type +
        //                                 ' Month Frequency/Year Frequency',
        //                             'You have crossed your ' +
        //                                 req.body.type +
        //                                 ' Month Frequency/Year Frequency',
        //                         ),
        //                     );
        //         } else if (type_data.entitlement == 'year') {
        //             if (type_data.per_month == 0) {
        //                 let count = 0;
        //                 for (element of leave_check.data) {
        //                     if (element.type == req.body.type) {
        //                         if (
        //                             element.leave_type &&
        //                             element.leave_type._leave_type_id &&
        //                             element.leave_type._leave_type_id.toString() ==
        //                                 req.body._leave_type_id.toString()
        //                         ) {
        //                             count += element.days;
        //                         }
        //                     }
        //                 }
        //                 if (frequency <= count)
        //                     return res
        //                         .status(409)
        //                         .send(
        //                             common_files.response_function(
        //                                 res,
        //                                 409,
        //                                 false,
        //                                 'You have crossed your ' + req.body.type + ' Frequency',
        //                                 'You have crossed your ' + req.body.type + ' Frequency',
        //                             ),
        //                         );
        //             } else {
        //                 // let month = dt1;
        //                 let i = 0;
        //                 let j = 0;
        //                 const start_date = dateTimeLocalFormatter(dt1);
        //                 start_date.setUTCDate(start_date.getUTCDate() + 1);
        //                 for (element of leave_check.data) {
        //                     if (element.type == req.body.type) {
        //                         if (
        //                             element.leave_type &&
        //                             element.leave_type._leave_type_id &&
        //                             element.leave_type._leave_type_id.toString() ==
        //                                 req.body._leave_type_id.toString()
        //                         ) {
        //                             const from = dateTimeLocalFormatter(element.from);
        //                             const to = dateTimeLocalFormatter(element.to);
        //                             console.log('log ', from, ' ', to);
        //                             const d = dateTimeLocalFormatter(element.from);
        //                             d.setDate(d.getDate() + 1);
        //                             for (let k = 0; k < element.days; k++) {
        //                                 // console.log(d.getUTCMonth(), ' ', dt1.getUTCMonth(), ' ', dt2.getUTCMonth());
        //                                 if (
        //                                     (d.getFullYear() == start_date.getFullYear() ||
        //                                         d.getFullYear() == dt2.getFullYear()) &&
        //                                     (d.getUTCMonth() == start_date.getUTCMonth() ||
        //                                         d.getUTCMonth() == dt2.getUTCMonth())
        //                                 ) {
        //                                     i++;
        //                                 }
        //                                 d.setDate(d.getDate() + 1);
        //                             }
        //                             // for (let d = new Date(element.from); d <= new Date(element.to); d.setDate(d.getDate() + 1)) {
        //                             //     console.log(d);
        //                             //     console.log(d.getUTCMonth(), ' ', dt1.getUTCMonth(), ' ', dt2.getUTCMonth());
        //                             //     if ((d.getFullYear() == dt1.getFullYear() || d.getFullYear() == dt2.getFullYear()) &&
        //                             //         (d.getUTCMonth() == dt1.getUTCMonth() || d.getUTCMonth() == dt2.getUTCMonth())) {
        //                             //         i++;
        //                             //         let temp_date = new Date(d);
        //                             //         temp_date.setDate(temp_date.getDate() + 1)
        //                             //         if (temp_date.getUTCMonth() == to.getUTCMonth() && temp_date.getUTCDate() == to.getUTCDate()) i++;
        //                             //     }
        //                             // }
        //                             j += parseInt(element.days);
        //                         }
        //                     }
        //                 }
        //                 console.log('finale check ', i, ' ', j, ' ', no_days);
        //                 i += parseInt(no_days);
        //                 j += parseInt(no_days);
        //                 console.log(i, ' ', type_data.per_month, ' ', frequency, ' ', j);
        //                 console.log(i > type_data.per_month, ' ', frequency < j);
        //                 if (i > type_data.per_month || frequency < j)
        //                     return res
        //                         .status(409)
        //                         .send(
        //                             common_files.response_function(
        //                                 res,
        //                                 409,
        //                                 false,
        //                                 'You have crossed your ' +
        //                                     req.body.type +
        //                                     ' Month Frequency/Year Frequency',
        //                                 'You have crossed your ' +
        //                                     req.body.type +
        //                                     ' Month Frequency/Year Frequency',
        //                             ),
        //                         );
        //             }
        //         } else {
        //             console.log(frequency < no_days);
        //             if (frequency < no_days)
        //                 return res
        //                     .status(409)
        //                     .send(
        //                         common_files.response_function(
        //                             res,
        //                             409,
        //                             false,
        //                             'You have crossed your ' + req.body.type + ' Frequency',
        //                             'You have crossed your ' + req.body.type + ' Frequency',
        //                         ),
        //                     );
        //         }
        //     }
        // }
        // if (
        //     constant.LEAVE_TYPE.LEAVE == req.body.type ||
        //     constant.LEAVE_TYPE.ONDUTY == req.body.type
        // ) {
        //     console.log(no_days, ' ', type_data.per_month, ' ', frequency);
        //     let per_month_in_leave = 0;
        //     const from = dateTimeLocalFormatter(dt1);
        //     from.setDate(from.getDate() + 1);
        //     let temp_month = from.getUTCMonth();
        //     if (type_data.entitlement != 'na') {
        //         for (
        //             let d = dateTimeLocalFormatter(from);
        //             d < dateTimeLocalFormatter(dt2);
        //             d.setDate(d.getDate() + 1)
        //         ) {
        //             if (
        //                 type_data.weekend_consideration != undefined &&
        //                 type_data.weekend_consideration == false
        //             ) {
        //                 // console.log(d.getUTCMonth(), ' ', temp_month)
        //                 if (d.getUTCMonth() == temp_month) {
        //                     const temp_date = dateTimeLocalFormatter(d);
        //                     temp_date.setDate(temp_date.getDate() + 1);
        //                     if (temp_date.getUTCDay() != 5 && temp_date.getUTCDay() != 6) {
        //                         per_month_in_leave++;
        //                         if (
        //                             temp_date.getUTCMonth() == dt2.getUTCMonth() &&
        //                             temp_date.getUTCDate() == dt2.getUTCDate()
        //                         ) {
        //                             per_month_in_leave++;
        //                         }
        //                     }
        //                 } else {
        //                     console.log(
        //                         per_month_in_leave > type_data.per_month,
        //                         ' s',
        //                         type_data.per_month,
        //                     );
        //                     console.log((per_month_in_leave, ' ', type_data.per_month));
        //                     if (per_month_in_leave > type_data.per_month)
        //                         return res
        //                             .status(409)
        //                             .send(
        //                                 common_files.response_function(
        //                                     res,
        //                                     409,
        //                                     false,
        //                                     'You have crossed your ' +
        //                                         req.body.type +
        //                                         ' Month Frequency/Year Frequency',
        //                                     'You have crossed your ' +
        //                                         req.body.type +
        //                                         ' Month Frequency/Year Frequency',
        //                                 ),
        //                             );
        //                     temp_month = d.getUTCMonth();
        //                     per_month_in_leave = 1;
        //                 }
        //             } else {
        //                 if (d.getUTCMonth() == temp_month) {
        //                     per_month_in_leave++;
        //                     const temp_date = dateTimeLocalFormatter(d);
        //                     temp_date.setDate(temp_date.getDate() + 1);
        //                     if (
        //                         temp_date.getUTCMonth() == dt2.getUTCMonth() &&
        //                         temp_date.getUTCDate() == dt2.getUTCDate()
        //                     ) {
        //                         per_month_in_leave++;
        //                     }
        //                 } else {
        //                     if (per_month_in_leave > type_data.per_month)
        //                         return res
        //                             .status(409)
        //                             .send(
        //                                 common_files.response_function(
        //                                     res,
        //                                     409,
        //                                     false,
        //                                     'You have crossed your ' +
        //                                         req.body.type +
        //                                         ' Month Frequency/Year Frequency',
        //                                     'You have crossed your ' +
        //                                         req.body.type +
        //                                         ' Month Frequency/Year Frequency',
        //                                 ),
        //                             );
        //                     temp_month = d.getUTCMonth();
        //                     per_month_in_leave = 1;
        //                 }
        //             }
        //             console.log('days ', per_month_in_leave);
        //         }
        //         console.log(dt1, ' ', per_month_in_leave);
        //         console.log(per_month_in_leave > type_data.per_month, ' ', frequency < no_days);
        //         if (
        //             (type_data.per_month &&
        //                 type_data.per_month != 0 &&
        //                 per_month_in_leave > type_data.per_month) ||
        //             frequency < no_days
        //         )
        //             return res
        //                 .status(409)
        //                 .send(
        //                     common_files.response_function(
        //                         res,
        //                         409,
        //                         false,
        //                         'You have crossed your ' +
        //                             req.body.type +
        //                             ' Month Frequency/Year Frequency',
        //                         'You have crossed your ' +
        //                             req.body.type +
        //                             ' Month Frequency/Year Frequency',
        //                     ),
        //                 );
        //     } else {
        //         // console.log(frequency, ' ', no_days);
        //         if (frequency < no_days)
        //             return res
        //                 .status(409)
        //                 .send(
        //                     common_files.response_function(
        //                         res,
        //                         409,
        //                         false,
        //                         'You have crossed your ' + req.body.type + ' Frequency',
        //                         'You have crossed your ' + req.body.type + ' Frequency',
        //                     ),
        //                 );
        //     }
        // }
        // Check is schedule is there, if there update it in course schedule
        if (constant.LEAVE_TYPE.PERMISSION === req.body.type && schedules && schedules.length) {
            const scheduleIds = schedules.map((ele) => ele.schedule_id.toString());
            // Course Schedule Datas
            const scheduleData = await get_list(
                course_schedule,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: scheduleIds,
                    // type: 'regular',
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'session.session_type': 1,
                    subjects: 1,
                    _program_id: 1,
                    _course_id: 1,
                    course_code: 1,
                    term: 1,
                    level_no: 1,
                    schedule_date: 1,
                    start: 1,
                    end: 1,
                    topic: 1,
                    substitute_staffs: 1,
                    staffs: 1,
                },
            );
            scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];

            const bulkUpdateObj = [];
            for (scheduleElement of schedules) {
                const scheduledElement = scheduleData.data.find(
                    (ele) => ele._id.toString() === scheduleElement.schedule_id.toString(),
                );
                if (scheduledElement) {
                    const substituteElement = scheduledElement.substitute_staffs.find(
                        (ele) => ele._assigned_to.toString() === req.body._user_id.toString(),
                    );
                    const pushObj = {
                        updateOne: {
                            filter: { _id: convertToMongoObjectId(scheduledElement._id) },
                        },
                    };
                    if (!substituteElement) {
                        if (
                            scheduleElement.substitute_staff &&
                            scheduleElement.substitute_staff.name &&
                            scheduleElement.substitute_staff._staff_id
                        )
                            pushObj.updateOne.update = {
                                $push: {
                                    substitute_staffs: {
                                        _assigned_to: convertToMongoObjectId(req.body._user_id),
                                        _substitute_staff_id: convertToMongoObjectId(
                                            scheduleElement.substitute_staff._staff_id,
                                        ),
                                        substitute_staff_name:
                                            scheduleElement.substitute_staff.name,
                                    },
                                },
                            };
                    } else {
                        if (
                            scheduleElement.substitute_staff &&
                            scheduleElement.substitute_staff.name &&
                            scheduleElement.substitute_staff._staff_id
                        ) {
                            pushObj.updateOne.update = {
                                $set: {
                                    'substitute_staffs.$[i]._substitute_staff_id':
                                        convertToMongoObjectId(
                                            scheduleElement.substitute_staff._staff_id,
                                        ),
                                    'substitute_staffs.$[i].substitute_staff_name':
                                        scheduleElement.substitute_staff.name,
                                },
                            };
                            pushObj.updateOne.arrayFilters = [
                                {
                                    'i._assigned_to': convertToMongoObjectId(req.body._user_id),
                                },
                            ];
                        }
                    }
                    if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
                }
            }
            // return res.send(bulkUpdateObj);
            if (bulkUpdateObj.length) console.log(await bulk_write(course_schedule, bulkUpdateObj));
            return res.send(bulkUpdateObj);
        }
        const query = { _id: ObjectId(req.params.id) };
        req.body.permission_hour = duration;
        req.body.days = calc_days;
        req.body.status_time = common_functions.timestampNow();
        if (req.body._leave_category_id)
            req.body.leave_category = {
                _leave_category_id: req.body._leave_category_id,
                name: req.body.category_name,
            };
        if (req.body._leave_type_id)
            req.body.leave_type = {
                _leave_type_id: req.body._leave_type_id,
                name: req.body.leave_type_name,
            };
        const doc = await base_control.update(lms_review, query, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, 'Leave Updated Successfully', doc.data);
        } else {
            common_files.com_response(res, 410, false, doc.data);
        }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update_apply_leave = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const checkQuery = { _id: ObjectId(req.params.id) };
    const checkDoc = await base_control.get(lms_review, checkQuery);
    if (checkDoc.status) {
        const query = { _id: ObjectId(req.params.id) };
        const { comments, ...reqBody } = req.body;
        let doc;
        if (comments) {
            doc = await base_control.update_condition(lms_review, query, {
                $addToSet: {
                    comments,
                },
                $set: {
                    reqBody,
                },
            });
        } else {
            doc = await base_control.update(lms_review, query, reqBody);
        }
        if (doc.status) {
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        'Apply Leave Update Successfully.',
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    }
    res.status(404).send(
        common_files.response_function(
            res,
            404,
            false,
            'Applied Leave Not found',
            'Applied Leave Not found',
        ),
    );
};

exports.update_leave = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );
    const checkQuery = { _id: ObjectId(req.params.id) };
    const checkDoc = await base_control.get(lms_review, checkQuery);
    if (checkDoc.status) {
        const query = { _id: ObjectId(req.params.id) };
        const { comments, ...reqBody } = req.body;
        let doc;
        if (comments) {
            doc = await base_control.update_condition(lms_review, query, {
                $addToSet: {
                    comments,
                },
                $set: {
                    reqBody,
                },
            });
        } else {
            doc = await base_control.update(lms_review, query, req.body);
        }
        if (doc.status) {
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        'Apply Leave Update Successfully.',
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    }
    res.status(404).send(
        common_files.response_function(
            res,
            404,
            false,
            'Applied Leave Not found',
            'Applied Leave Not found',
        ),
    );
};

exports.cancel_leave = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            // params: { institutionCalendarId },
        } = req;
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const lmsReviewData = await base_control.get(lms_review, query);
        //return res.send(lmsReviewData);
        if (!lmsReviewData.status)
            res.status(404).send(
                common_files.response_function(res, 404, false, 'Data not found', 'Data not found'),
            );

        // Need to Check is Schedule is Conducted ?
        let scheduleQuery;
        let scheduleData;
        if (lmsReviewData.data.status === APPROVE || lmsReviewData.data.status === 'approved') {
            // Remove Substitute Staff From Matching schedule
            scheduleQuery = {
                _institution_id: convertToMongoObjectId(_institution_id),
                // _institution_calendar_id: convertToMongoObjectId(
                //     lmsData.data._institution_calendar_id,
                // ),
                'staffs._staff_id': ObjectId(lmsReviewData.data._user_id),
                'staffs.status':
                    lmsReviewData.data.type === 'report_absence' ? LEAVE : lmsReviewData.data.type,
                schedule_date: {
                    $gte: dateTimeLocalFormatter(lmsReviewData.data.from),
                    $lte: dateTimeLocalFormatter(lmsReviewData.data.to),
                },
                // type: 'regular',
                isActive: true,
                isDeleted: false,
            };
            if (lmsReviewData.data.type === PERMISSION) {
                const scheduleFromDate = dateTimeLocalFormatter(lmsReviewData.data.from);
                const scheduleToDate = dateTimeLocalFormatter(lmsReviewData.data.from);
                scheduleFromDate.setHours(0);
                scheduleToDate.setHours(23);
                scheduleQuery.schedule_date.$gte = scheduleFromDate;
                scheduleQuery.schedule_date.$lte = scheduleToDate;
            }
            scheduleData = await get_list(course_schedule, scheduleQuery, {
                // 'session.session_type': 1,
                session: 1,
                program_name: 1,
                subjects: 1,
                _program_id: 1,
                _course_id: 1,
                course_code: 1,
                term: 1,
                level_no: 1,
                schedule_date: 1,
                start: 1,
                end: 1,
                topic: 1,
                substitute_staffs: 1,
                staffs: 1,
                // 'session._session_id': 1,
                type: 1,
                merge_status: 1,
                merge_with: 1,
                status: 1,
            });
            scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
            if (scheduleData.data.find((ele) => ele && ele.status !== PENDING))
                return res
                    .status(404)
                    .send(
                        common_files.response_function(
                            res,
                            404,
                            false,
                            'Session has been completed, Unable to cancel',
                            'Session has been completed, Unable to cancel',
                        ),
                    );
        }

        const obj = {
            $set: { isActive: false, status: 'Cancelled' },
            // $push: {
            //     comments: {
            //         _person_id: req.body.person_id,
            //         comment: req.body.comment,
            //     },
            // },
        };
        // const doc = { status: true };
        const doc = await base_control.update_condition(lms_review, query, obj);

        if (doc.status) {
            if (lmsReviewData.data.status === APPROVE || lmsReviewData.data.status === 'approved') {
                const notify_via = ['email'];
                // Applicant & Approver User Data
                const userQuery = {
                    user_type: STAFF,
                    status: COMPLETED,
                    isDeleted: false,
                    isActive: true,
                };
                const { data: userData } = await get_list(user, userQuery, {
                    user_id: 1,
                    name: 1,
                    email: 1,
                    mobile: 1,
                    academic_allocation: 1,
                    staff_employment_type: 1,
                });
                const applicantData = userData.find(
                    (ele) => ele._id.toString() === lmsReviewData.data._user_id.toString(),
                );
                let applicantProgram;
                let applicantProgramId;
                let applicantDepartmentId;
                if (
                    applicantData.staff_employment_type === ACADEMIC ||
                    applicantData.staff_employment_type === 'both'
                ) {
                    applicantProgram = applicantData.academic_allocation.find(
                        (ele2) => ele2.allocation_type === PRIMARY,
                    );
                    applicantProgramId = applicantProgram
                        ? applicantProgram._program_id.toString()
                        : '';
                    applicantDepartmentId = applicantProgram
                        ? applicantProgram._department_id.toString()
                        : '';
                }
                const approverData = applicantData;

                // Remove Substitute Staff From Matching schedule
                // const scheduleQuery = {
                //     _institution_id: convertToMongoObjectId(_institution_id),
                //     // _institution_calendar_id: convertToMongoObjectId(
                //     //     lmsData.data._institution_calendar_id,
                //     // ),
                //     'staffs._staff_id': ObjectId(lmsReviewData.data._user_id),
                //     'staffs.status':
                //         lmsReviewData.data.type === 'report_absence'
                //             ? LEAVE
                //             : lmsReviewData.data.type,
                //     schedule_date: {
                //         $gte: dateTimeLocalFormatter(lmsReviewData.data.from),
                //         $lte: dateTimeLocalFormatter(lmsReviewData.data.to),
                //     },
                //     // type: 'regular',
                //     isActive: true,
                //     isDeleted: false,
                // };
                // if (lmsReviewData.data.type === PERMISSION) {
                //     const scheduleFromDate = dateTimeLocalFormatter(lmsReviewData.data.from);
                //     const scheduleToDate = dateTimeLocalFormatter(lmsReviewData.data.from);
                //     scheduleFromDate.setHours(0);
                //     scheduleToDate.setHours(23);
                //     scheduleQuery.schedule_date.$gte = scheduleFromDate;
                //     scheduleQuery.schedule_date.$lte = scheduleToDate;
                // }
                // const scheduleData = await get_list(course_schedule, scheduleQuery, {
                //     // 'session.session_type': 1,
                //     session: 1,
                //     program_name: 1,
                //     subjects: 1,
                //     _program_id: 1,
                //     _course_id: 1,
                //     course_code: 1,
                //     term: 1,
                //     level_no: 1,
                //     schedule_date: 1,
                //     start: 1,
                //     end: 1,
                //     topic: 1,
                //     substitute_staffs: 1,
                //     staffs: 1,
                //     // 'session._session_id': 1,
                //     type: 1,
                //     merge_status: 1,
                //     merge_with: 1,
                // });
                // scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
                // return res.send({ scheduleData, scheduleQuery });
                const scheduleCourseId = scheduleData.data.map((ele) => ele && ele._course_id);
                // Course Data Get
                const { data: courseData } = await get_list(
                    course,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: scheduleCourseId,
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        coordinators: 1,
                    },
                );
                // return res.send(scheduleData);
                // Merge Schedule for merged schedules
                const scheduleWithMerge = clone(scheduleData.data);
                scheduleData.data = mergeSchedule(scheduleData.data);
                for (scheduleElement of scheduleData.data) {
                    let title = '';
                    let subTypes = [];
                    if (scheduleElement.merge_status === false) {
                        switch (scheduleElement.type) {
                            case REGULAR:
                                title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no} - ${scheduleElement.session.session_topic}`;
                                subTypes.push(scheduleElement.session.session_type);
                                break;
                            case SUPPORT_SESSION:
                                title = scheduleElement.title;
                                subTypes.push(scheduleElement.sub_type);
                                break;
                            case EVENT:
                                title = scheduleElement.title;
                                subTypes.push(scheduleElement.sub_type);
                                break;
                            default:
                                break;
                        }
                    } else {
                        title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no}`;
                        subTypes.push(scheduleElement.session.session_type);
                        for (mergeSessionElement of scheduleElement.merge_sessions) {
                            title = title.concat(
                                `, ${mergeSessionElement.delivery_symbol}${mergeSessionElement.delivery_no}`,
                            );
                            subTypes.push(mergeSessionElement.session_type);
                        }
                    }
                    subTypes = [...new Set(subTypes)];
                    scheduleElement.mailTitle = title;
                    scheduleElement.subTypes = subTypes;
                }

                const bulkUpdateObj = [];
                // Checking scheduleData In table Mail Push
                const scheduleAlteredData = [];
                let substituteStaffIds = [];
                for (const [index, scheduleElement] of scheduleData.data.entries()) {
                    const scheduleChangesElement = scheduleElement.substitute_staffs.find(
                        (ele) =>
                            ele._assigned_to.toString() === lmsReviewData.data._user_id.toString(),
                    );
                    const scheduleAlteredObj = {
                        'S NO': index + 1,
                        'Session Type':
                            scheduleElement.type.charAt(0).toUpperCase() +
                            scheduleElement.type.slice(1),
                        Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                        From: scheduleTimeFormateChange(scheduleElement.start),
                        To: scheduleTimeFormateChange(scheduleElement.end),
                        Program: scheduleElement.program_name,
                        Course: scheduleElement.course_code,
                        course_id: scheduleElement._course_id,
                        term: scheduleElement.term,
                        Subject: scheduleElement.subjects.map((ele) => ele.subject_name).toString(),
                        Title: scheduleElement.mailTitle,
                        'Substitute Staff': '',
                    };
                    if (scheduleChangesElement) {
                        scheduleAlteredObj['Substitute Staff'] = nameFormatter(
                            scheduleChangesElement.substitute_staff_name,
                        );
                        scheduleAlteredObj.staff_id = scheduleChangesElement._substitute_staff_id;
                        substituteStaffIds.push(
                            scheduleChangesElement._substitute_staff_id.toString(),
                        );
                    }
                    scheduleAlteredData.push(scheduleAlteredObj);
                    // scheduleAlteredData.push({
                    //     'S NO': index + 1,
                    //     'Session Type':
                    //         scheduleElement.type.charAt(0).toUpperCase() +
                    //         scheduleElement.type.slice(1),
                    //     Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                    //     From: scheduleTimeFormateChange(scheduleElement.start),
                    //     To: scheduleTimeFormateChange(scheduleElement.end),
                    //     Program: scheduleElement.program_name,
                    //     Course: scheduleElement.course_code,
                    //     course_id: scheduleElement._course_id,
                    //     term: scheduleElement.term,
                    //     Subject: scheduleElement.subjects.map((ele) => ele.subject_name).toString(),
                    //     Title: scheduleElement.mailTitle,
                    //     // Topic: scheduleElement.topic ? scheduleElement.topic : '',
                    //     'Substitute Staff': nameFormatter(
                    //         scheduleChangesElement.substitute_staff_name,
                    //     ),
                    //     staff_id: scheduleChangesElement._substitute_staff_id,
                    // });
                }
                for (scheduleElement of scheduleWithMerge) {
                    const pushObj = {
                        updateOne: {
                            filter: { _id: convertToMongoObjectId(scheduleElement._id) },
                        },
                    };
                    const scheduleStaff = clone(scheduleElement.staffs);
                    const staffLoc = scheduleStaff.findIndex(
                        (ele) =>
                            ele._staff_id.toString() === lmsReviewData.data._user_id.toString(),
                    );
                    const scheduleSubstituteStaffs = clone(scheduleElement.substitute_staffs);
                    const substituteStaffSchedule = scheduleSubstituteStaffs.findIndex(
                        (ele) =>
                            ele._assigned_to.toString() === lmsReviewData.data._user_id.toString(),
                    );
                    if (staffLoc !== -1) scheduleStaff[staffLoc].status = PENDING;
                    if (substituteStaffSchedule !== -1) {
                        const substituteStaffLoc = scheduleStaff.findIndex(
                            (ele) =>
                                ele._staff_id.toString() ===
                                scheduleSubstituteStaffs[
                                    substituteStaffSchedule
                                ]._substitute_staff_id.toString(),
                        );
                        if (substituteStaffLoc) scheduleStaff.splice(substituteStaffLoc, 1);
                        scheduleSubstituteStaffs.splice(substituteStaffSchedule, 1);
                    }
                    pushObj.updateOne.update = {
                        $set: {
                            substitute_staffs: scheduleSubstituteStaffs,
                            staffs: scheduleStaff,
                        },
                    };
                    if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
                }
                // return res.send(bulkUpdateObj);
                // Remove Substitute Staff From Course Schedule
                if (bulkUpdateObj.length)
                    console.log(await bulk_write(course_schedule, bulkUpdateObj));

                // const substituteStaffIds = [
                //     ...new Set(scheduleAlteredData.map((ele) => ele.staff_id.toString())),
                // ];
                if (substituteStaffIds.length !== 0)
                    substituteStaffIds = [...new Set(substituteStaffIds)];
                // Substitute Staff Notification Push
                for (substituteElement of substituteStaffIds) {
                    const substituteStaffsSchedule = clone(
                        scheduleAlteredData.filter(
                            (ele) =>
                                ele &&
                                ele.staff_id &&
                                ele.staff_id.toString() === substituteElement.toString(),
                        ),
                    );
                    substituteStaffsSchedule.forEach((ele) => {
                        delete ele.course_id;
                        delete ele.term;
                        delete ele['Substitute Staff'];
                        delete ele.staff_id;
                    });
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === substituteElement.toString(),
                    );
                    const mailSubject = `Substitute Sessions got canceled`;
                    const mailBody = `Your substitute sessions have been canceled for the following sessions<br>`;
                    const smsContent = `Your substitute sessions have been canceled for the following sessions.`;
                    await contentScheduleMailPush(
                        substituteStaffDetails,
                        notify_via,
                        substituteStaffsSchedule,
                        mailSubject,
                        mailBody,
                        smsContent,
                        approverData,
                    );
                }
                let mailBody = `<b>${capitalize(
                    lmsReviewData.data.type,
                )} application summary-Cancelled</b><br><br>`;
                mailBody = mailBody.concat(`Staff name : ${nameFormatter(applicantData.name)}<br>`);
                mailBody = mailBody.concat(`Employee id : ${applicantData.user_id}<br>`);
                if (lmsReviewData.data.leave_category && lmsReviewData.data.leave_category.name)
                    mailBody = mailBody.concat(
                        `Category : ${lmsReviewData.data.leave_category.name}<br>`,
                    );
                if (lmsReviewData.data.leave_type && lmsReviewData.data.leave_type.name)
                    mailBody = mailBody.concat(`Type : ${lmsReviewData.data.leave_type.name}<br>`);
                mailBody = mailBody.concat(`Reason : ${lmsReviewData.data.reason}<br>`);
                mailBody = mailBody.concat(
                    `Date/Time : ${dateTimeLocalFormatInString(lmsReviewData.data.from)} `,
                );
                mailBody = mailBody.concat(
                    lmsReviewData.data.to
                        ? `and ${dateTimeLocalFormatInString(lmsReviewData.data.to)}<br>`
                        : `<br><br>`,
                );
                // To Dean, Vice Dean, Department Chairman
                const mailedStaff = [];
                const roleData = await base_control.get_list(
                    role,
                    {
                        name: { $nin: ['Super Admin', 'ADMIN'] },
                        $or: [
                            {
                                'modules.name': 'Calendar',
                                $and: [
                                    { 'modules.pages.name': 'Institution Calendar' },
                                    { 'modules.pages.actions.name': 'Create' },
                                    { 'modules.pages.actions.name': 'Add Event' },
                                    { 'modules.pages.actions.name': 'Publish' },
                                ],
                            },
                            {
                                'modules.name': 'Program Calendar',
                                $and: [
                                    { 'modules.pages.name': 'Dashboard' },
                                    { 'modules.pages.actions.name': 'Calendar Settings' },
                                    { 'modules.pages.actions.name': 'Add Course' },
                                    { 'modules.pages.actions.name': 'Review Calendar' },
                                ],
                            },
                        ],
                    },
                    { _id: 1 },
                );
                const roleIds = roleData.status
                    ? roleData.data.map((ele) => ele._id.toString())
                    : [];
                const roleAssign = await base_control.get_list(
                    role_assign,
                    {
                        'roles.isAdmin': true,
                        'roles.program._program_id': convertToMongoObjectId(applicantProgramId),
                        $or: [
                            { 'roles._role_id': roleIds },
                            {
                                'roles.department._department_id':
                                    convertToMongoObjectId(applicantDepartmentId),
                            },
                        ],
                    },
                    { _user_id: 1 },
                );
                const staffsScheduleData = clone(scheduleAlteredData);

                // let viceDeanDeptChairCoordinatorIds = roleAssign.data.map((ele) =>
                //     ele._user_id.toString(),
                // );
                // courseData
                for (courseElement of courseData) {
                    if (courseElement.coordinators && courseElement.coordinators.length) {
                        for (coordinatorsElement of courseElement.coordinators) {
                            const courseScheduleBasedTerm = clone(
                                scheduleAlteredData.filter(
                                    (ele) =>
                                        ele.course_id.toString() === courseElement._id.toString() &&
                                        ele.term === coordinatorsElement.term,
                                ),
                            );
                            if (courseScheduleBasedTerm.length !== 0) {
                                // const courseSchedule = clone(staffsScheduleData);
                                // courseSchedule.forEach((ele) => {
                                //     delete ele.course_id;
                                //     delete ele.term;
                                //     delete ele.staff_id;
                                // });
                                let subMailBody = mailBody.toString();
                                subMailBody = subMailBody.concat(
                                    `<br>List of Sessions along with Substitute staff<br>`,
                                );
                                courseScheduleBasedTerm.forEach((ele) => {
                                    delete ele.course_id;
                                    delete ele.term;
                                    delete ele.staff_id;
                                });
                                const substituteStaffDetails = userData.find(
                                    (ele) =>
                                        ele._id.toString() ===
                                        coordinatorsElement._user_id.toString(),
                                );
                                if (substituteStaffDetails) {
                                    mailedStaff.push(coordinatorsElement._user_id.toString());
                                    const mailSubject = `${capitalize(
                                        lmsReviewData.data.type,
                                    )} application summary of : ${nameFormatter(
                                        applicantData.name,
                                    )} - Cancelled`;
                                    const smsContent = `${nameFormatter(
                                        applicantData.name,
                                    )} ${capitalize(
                                        lmsReviewData.data.type,
                                    )} has been Cancelled check your mail`;
                                    await contentScheduleMailPush(
                                        substituteStaffDetails,
                                        notify_via,
                                        courseScheduleBasedTerm,
                                        mailSubject,
                                        mailBody,
                                        smsContent,
                                        approverData,
                                    );
                                }
                                // viceDeanDeptChairCoordinatorIds.push(
                                //     coordinatorsElement._user_id.toString(),
                                // );
                            }
                        }
                    }
                }
                // viceDeanDeptChairCoordinatorIds = [...new Set(viceDeanDeptChairCoordinatorIds)];
                // Vice Dean & Department Chairman Mail Push & Also to Approvers
                if (
                    applicantData.staff_employment_type === ACADEMIC ||
                    applicantData.staff_employment_type === 'both'
                ) {
                    if (staffsScheduleData.length !== 0)
                        mailBody = mailBody.concat(
                            `<br>List of Sessions along with Substitute staff<br>`,
                        );
                    let userIdsToPush = [
                        ...lmsReviewData.data.level_approvers.map((ele) =>
                            ele._person_id.toString(),
                        ),
                        ...roleAssign.data.map((ele2) => ele2._user_id.toString()),
                    ];
                    userIdsToPush = [...new Set(userIdsToPush)];
                    for (staffElement of userIdsToPush) {
                        staffsScheduleData.forEach((ele) => {
                            delete ele.course_id;
                            delete ele.term;
                            delete ele.staff_id;
                        });
                        const substituteStaffDetails = userData.find(
                            (ele) => ele._id.toString() === staffElement.toString(),
                        );
                        if (
                            substituteStaffDetails &&
                            !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                        ) {
                            mailedStaff.push(staffElement.toString());
                            const mailSubject = `${capitalize(
                                lmsReviewData.data.type,
                            )} application summary of : ${nameFormatter(
                                applicantData.name,
                            )} - Cancelled`;
                            const smsContent = `${nameFormatter(applicantData.name)} ${capitalize(
                                lmsReviewData.data.type,
                            )} has been canceled check your mail`;
                            await contentScheduleMailPush(
                                substituteStaffDetails,
                                notify_via,
                                staffsScheduleData,
                                mailSubject,
                                mailBody,
                                smsContent,
                                approverData,
                            );
                        }
                    }
                } else {
                    for (staffElement of lmsReviewData.data.level_approvers.map((ele) =>
                        ele._person_id.toString(),
                    )) {
                        const substituteStaffDetails = userData.find(
                            (ele) => ele._id.toString() === staffElement.toString(),
                        );
                        if (
                            substituteStaffDetails &&
                            !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                        ) {
                            mailedStaff.push(staffElement.toString());
                            const mailSubject = `${capitalize(
                                lmsReviewData.data.type,
                            )} application summary of : ${nameFormatter(
                                applicantData.name,
                            )} - Cancelled`;
                            const smsContent = `${nameFormatter(applicantData.name)} ${capitalize(
                                lmsReviewData.data.type,
                            )}  has been canceled check your mail`;
                            await contentScheduleMailPush(
                                substituteStaffDetails,
                                notify_via,
                                staffsScheduleData,
                                mailSubject,
                                mailBody,
                                smsContent,
                                approverData,
                            );
                        }
                    }
                }
                // Mail Push for Applicant
                const applicantMailSubject = `${capitalize(
                    lmsReviewData.data.type,
                )} application Cancelled`;
                let applicantMailBody = `Your ${capitalize(
                    lmsReviewData.data.type,
                )} application for the dates ${dateTimeLocalFormatInString(
                    lmsReviewData.data.from,
                )}`;
                applicantMailBody = applicantMailBody.concat(
                    lmsReviewData.data.to
                        ? `and ${dateTimeLocalFormatInString(
                              lmsReviewData.data.to,
                          )} has been cancelled.<br>`
                        : ` has been cancelled.<br><br>`,
                );
                if (staffsScheduleData.length)
                    applicantMailBody = applicantMailBody.concat(
                        `<br>List of Sessions along with Substitute staff<br>`,
                    );
                let applicantSmsContent = `Your ${capitalize(
                    lmsReviewData.data.type,
                )} application ${dateTimeLocalFormatInString(lmsReviewData.data.from)}`;
                applicantSmsContent = applicantSmsContent.concat(
                    lmsReviewData.data.to
                        ? `and ${dateTimeLocalFormatInString(lmsReviewData.data.to)}<br>`
                        : '',
                );
                applicantSmsContent = applicantSmsContent.concat(` has been cancelled.`);
                await contentScheduleMailPush(
                    applicantData,
                    notify_via,
                    staffsScheduleData,
                    applicantMailSubject,
                    applicantMailBody,
                    applicantSmsContent,
                    approverData,
                );

                // Mail Push to HR
                const { data: lmsSettingData } = await get(
                    lms,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        isActive: true,
                        isDeleted: false,
                    },
                    { hr_contact: 1 },
                );
                if (lmsSettingData.hr_contact) {
                    const hrDetails = {
                        name: { first: 'HR', last: ' ' },
                        email: lmsSettingData.hr_contact,
                    };
                    const mailSubject = `${capitalize(
                        lmsReviewData.data.type,
                    )} application summary of : ${nameFormatter(applicantData.name)} - cancelled`;
                    const smsContent = `${nameFormatter(applicantData.name)} ${capitalize(
                        lmsReviewData.data.type,
                    )} has been cancelled check your mail`;
                    await contentScheduleMailPush(
                        hrDetails,
                        notify_via,
                        staffsScheduleData,
                        mailSubject,
                        mailBody,
                        smsContent,
                        approverData,
                    );
                }
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        'Leave Canceled Successfully.',
                        'Leave Canceled Successfully.',
                    ),
                );
        }
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    'Unable to Cancel',
                    'Unable to Cancel',
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    'Internal Serve Issue',
                    error.toString(),
                ),
            );
    }
};
const formatDate = function (date) {
    const d = dateTimeLocalFormatter(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
};
exports.student_report_absence_insert = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _institution_calendar_id,
                _program_id,
                _user_id,
                user_type,
                type,
                from,
                to,
                reason,
                _leave_reason_doc,
                application_date,
                _leave_category_id,
                category_name,
                _person_id,
                approved_date,
                approved_comments,
                _leave_type_id,
                leave_type_name,
                notifyVia,
            },
        } = req;
        const institution_check = await get(
            institution,
            { _id: ObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const lmsSetting = await get(lms, { _institution_id: ObjectId(_institution_id) }, {});
        if (!lmsSetting.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                    ),
                );
        const dt1 = dateTimeLocalFormatter(parseInt(from));
        const dt2 = dateTimeLocalFormatter(parseInt(to));
        const leaveQuery = {
            _institution_id: ObjectId(_institution_id),
            _institution_calendar_id: ObjectId(_institution_calendar_id),
            user_type,
            _user_id: ObjectId(_user_id),
            // $or: [
            //     {
            //         from: {
            //             $gte: dt1,
            //             $lte: dt2,
            //         },
            //     },
            //     {
            //         to: {
            //             $lte: dt1,
            //             $gte: dt2,
            //         },
            //     },
            // ],
            status: { $not: { $eq: 'rejected' } },
            isActive: true,
            isDeleted: false,
        };
        const leave_check = await get_list(lms_review, leaveQuery, {});
        leave_check.data = leave_check.status ? leave_check.data : [];
        for (element of leave_check.data) {
            const from = dateTimeLocalFormatter(element.from);
            const to = dateTimeLocalFormatter(element.to);
            if ((dt1 <= from && dt2 > from) || (dt1 < to && dt2 >= to) || (dt2 > from && dt1 < to))
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            false,
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                        ),
                    );
        }
        // const duplicateCheckStatus = leave_check.data.filter(
        //     (ele) =>
        //         (ele && ele.from <= dt1 && ele.from >= dt2) || (ele.to >= dt1 && ele.to <= dt2),
        // );
        // if (duplicateCheckStatus.length !== 0) {
        //     return res
        //         .status(410)
        //         .send(
        //             response_function(
        //                 res,
        //                 410,
        //                 false,
        //                 'Duplicate on Permission/Leave/On_Duty',
        //                 'Duplicate on Permission/Leave/On_Duty',
        //             ),
        //         );
        // }
        // let type_data;
        // let frequency = 0;
        const duration = 0;
        // console.log('LOC ', dt1, ' ', dt2);
        // let no_days = 0;
        // const diff = dt2 - dt1;
        // const diffInDays = diff / (1000 * 60 * 60 * 24);
        // no_days = Math.round(diffInDays);
        // console.log('No Of days', no_days);
        const calc_days = (await dayCalculation(dt1, dt2)).withOutHolidays;
        // if (PERMISSION === type) {
        //     duration = diff_hours(dt1, dt2);
        //     type_data = lmsSetting.data.category.find(
        //         (i) => i.category_type == PERMISSION && i.category_to == user_type,
        //     );
        //     if (!type_data)
        //         return res
        //             .status(409)
        //             .send(
        //                 response_function(
        //                     res,
        //                     409,
        //                     false,
        //                      req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
        //                      req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
        //                 ),
        //             );
        //     const leaveData = leave_check.data.filter((ele) => ele.type === type);
        //     frequency = type_data.permission.permission_frequency;
        //     if (
        //         type_data.permission.permission_frequency_by === 'year' ||
        //         type_data.permission.permission_frequency_by === 'null'
        //     ) {
        //         if (leaveData.length >= frequency)
        //             return res
        //                 .status(410)
        //                 .send(
        //                     response_function(
        //                         res,
        //                         410,
        //                         false,
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                     ),
        //                 );
        //     } else if (type_data.permission.permission_frequency_by === 'month') {
        //         let i = 0;
        //         for (element of leaveData) {
        //             const from = dateTimeLocalFormatter(element.from);
        //             if (from.getUTCMonth() === dt1.getUTCMonth()) i += element.days;
        //         }
        //         if (frequency <= i)
        //             return res
        //                 .status(409)
        //                 .send(
        //                     common_files.response_function(
        //                         res,
        //                         409,
        //                         false,
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                     ),
        //                 );
        //     }
        // }
        // if (type === LEAVE) {
        //     const c_data = lmsSetting.data.category.find(
        //         (ele) => ele._id.toString() === _leave_category_id.toString(),
        //     );
        //     type_data = c_data.leave_type.find(
        //         (ele) => ele._id.toString() === _leave_type_id.toString(),
        //     );
        //     frequency = type_data.no_of_days;
        //     const coursePercentage = type_data.no_of_days;
        //     const exceededCourses = [];
        //     // Schedule Check
        //     const scheduleData = await get_list(
        //         course_schedule,
        //         {
        //             isDeleted: false,
        //             _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        //             type: REGULAR,
        //             // schedule_date: { $lte: dt2 },
        //             'students._id': convertToMongoObjectId(_user_id),
        //         },
        //         {
        //             _id: 1,
        //             schedule_date: 1,
        //             _course_id: 1,
        //             students: 1,
        //             status: 1,
        //             start: 1,
        //             end: 1,
        //             'session._session_id': 1,
        //             'session.session_type': 1,
        //         },
        //     );
        //     scheduleData.data = scheduleData.status ? scheduleData.data : [];
        //     // return res.send(scheduleData);
        //     const studentCourseIds = [
        //         ...new Set(scheduleData.data.map((ele) => ele._course_id.toString())),
        //     ];
        //     // Schedule Datas
        //     const scheduleList = clone(scheduleData.data);
        //     for (scheduleElement of scheduleList) {
        //         const stdData = scheduleElement.students.find(
        //             (ele2) => ele2._id.toString() === _user_id.toString(),
        //         );
        //         scheduleElement.students = stdData;
        //         const schedulingDate = new Date(scheduleElement.schedule_date);
        //         const otherStart = dateFormatter(
        //             schedulingDate.getFullYear(),
        //             schedulingDate.getMonth() + 1,
        //             schedulingDate.getDate(),
        //             scheduleElement.start.hour,
        //             scheduleElement.start.minute,
        //             scheduleElement.start.format,
        //         );
        //         const otherEnd = dateFormatter(
        //             schedulingDate.getFullYear(),
        //             schedulingDate.getMonth() + 1,
        //             schedulingDate.getDate(),
        //             scheduleElement.end.hour,
        //             scheduleElement.end.minute,
        //             scheduleElement.end.format,
        //         );
        //         otherStart.setMinutes(otherStart.getMinutes() + 1);
        //         otherEnd.setMinutes(otherEnd.getMinutes() - 1);
        //         if (
        //             (otherStart <= dt1 && otherEnd >= dt2) ||
        //             (otherStart <= dt2 && otherEnd >= dt1)
        //         ) {
        //             scheduleElement.students.status = LEAVE;
        //         }
        //     }
        //     for (courseElement of studentCourseIds) {
        //         const courseScheduleData = scheduleList.filter(
        //             (ele) => ele._course_id.toString() === courseElement.toString(),
        //         );
        //         const completedSchedule = courseScheduleData.filter(
        //             (ele) => ele.status === COMPLETED,
        //         );
        //         const absentSchedules = courseScheduleData.filter(
        //             (ele) =>
        //                 ele.students.status !== PENDING &&
        //                 (ele.students.status === ABSENT || ele.students.status === LEAVE),
        //         );
        //         const presentSchedules = completedSchedule.filter(
        //             (ele) => ele.students.status === PRESENT,
        //         );
        //         const denialPercentage = (absentSchedules.length / courseScheduleData.length) * 100;
        //         const courseSchedule = {
        //             _course_id: courseElement,
        //             scheduleCount: courseScheduleData.length,
        //             presentCount: presentSchedules.length,
        //             completedCount: completedSchedule.length,
        //             absentCount: absentSchedules.length,
        //             denialPercentage: denialPercentage.toFixed(2),
        //         };
        //         if (coursePercentage <= parseFloat(denialPercentage)) {
        //             exceededCourses.push(courseSchedule);
        //         }
        //     }
        //     console.log({ coursePercentage, exceededCourses });
        //     if (exceededCourses.length !== 0)
        //         return res
        //             .status(410)
        //             .send(
        //                 response_function(
        //                     res,
        //                     410,
        //                     false,
        //                     'Student Attendance Abscesses is High so unable to add Leave',
        //                     'Student Attendance Abscesses is High so unable to add Leave',
        //                 ),
        //             );
        // }
        // if (type === ONDUTY) {
        //     let ondutyCheck = false;
        //     const c_data = lmsSetting.data.category.find(
        //         (ele) => ele._id.toString() === _leave_category_id.toString(),
        //     );
        //     type_data = c_data.leave_type.find(
        //         (ele) => ele._id.toString() === _leave_type_id.toString(),
        //     );
        //     const weekendConsideration =
        //         type_data.weekend_consideration === undefined
        //             ? false
        //             : type_data.weekend_consideration;
        //     const dayData = await dayCalculation(dt1, dt2);
        //     calc_days = weekendConsideration ? dayData.withHolidays : dayData.withOutHolidays;
        //     frequency = type_data.no_of_days;
        //     console.log({ dt1, dt2, type_data, frequency, calc_days });
        //     const leaveData = leave_check.data.filter(
        //         (ele) =>
        //             ele.type === type &&
        //             ele.leave_type._leave_type_id &&
        //             ele.leave_type._leave_type_id.toString() == _leave_type_id.toString(),
        //     );
        //     if (frequency < calc_days) ondutyCheck = true;
        //     else if (type_data.entitlement === 'month') {
        //         let i = calc_days;
        //         if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
        //             i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
        //             ondutyCheck = frequency < i;
        //             console.log({ frequency, i });
        //         } else {
        //             const diffMonthDates = await diffMonthDateCalculation(
        //                 dt1,
        //                 dt2,
        //                 leaveData,
        //                 weekendConsideration,
        //             );
        //             ondutyCheck =
        //                 frequency < diffMonthDates.fromMonthDates ||
        //                 frequency < diffMonthDates.toMonthDates;
        //             console.log({ frequency, diffMonthDates });
        //         }
        //     } else if (type_data.entitlement === 'year') {
        //         let totalDays = calc_days;
        //         for (element of leaveData) {
        //             totalDays += element.days;
        //         }
        //         console.log('Check ', totalDays, frequency < totalDays, frequency > totalDays);
        //         if (type_data.per_month === 0 || frequency < totalDays) {
        //             ondutyCheck = frequency < totalDays;
        //         } else {
        //             if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
        //                 let i = calc_days;
        //                 i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
        //                 ondutyCheck = type_data.per_month < i || frequency < i;
        //                 console.log({ perMonth: type_data.per_month, frequency, i });
        //             } else {
        //                 const diffMonthDates = await diffMonthDateCalculation(
        //                     dt1,
        //                     dt2,
        //                     leaveData,
        //                     weekendConsideration,
        //                 );
        //                 ondutyCheck =
        //                     frequency < diffMonthDates.fromMonthDates ||
        //                     frequency < diffMonthDates.toMonthDates ||
        //                     (type_data.per_month < diffMonthDates.fromMonthDates &&
        //                         type_data.per_month < diffMonthDates.toMonthDates);
        //                 console.log({ perMonth: type_data.per_month, frequency, diffMonthDates });
        //             }
        //         }
        //     }
        //     if (ondutyCheck)
        //         return res
        //             .status(410)
        //             .send(
        //                 response_function(
        //                     res,
        //                     410,
        //                     false,
        //                     'You have crossed your ' + type + ' Month Frequency/Year Frequency',
        //                     'You have crossed your ' + type + ' Month Frequency/Year Frequency',
        //                 ),
        //             );
        // }
        // let calc_days = no_days;
        // console.log(type_data);
        // return res.send(type_data);
        const obj = {
            _institution_id: ObjectId(req.headers._institution_id),
            _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
            _program_id,
            _user_id: ObjectId(req.body._user_id),
            user_type: req.body.user_type,
            type: req.body.type,
            from: req.body.from,
            to: req.body.to,
            days: calc_days == 0 ? req.body.days : calc_days,
            permission_hour: duration,
            reason: req.body.reason,
            leave_category: {
                _leave_category_id: req.body._leave_category_id,
                name: req.body.category_name,
            },
            leave_type: {
                _leave_type_id: req.body._leave_type_id,
                name: req.body.leave_type_name,
            },
            _leave_reason_doc: req.body._leave_reason_doc,
            application_date: req.body.application_date,
            approved_by: {
                _person_id: ObjectId(req.body._person_id),
                date: req.body.approved_date,
            },
            comments: [
                {
                    _person_id: ObjectId(req.body._person_id),
                    comment: req.body.approved_comments,
                },
            ],
        };
        // return res.send(obj);
        // const doc = { status: true };
        const doc = await base_control.insert(lms_review, obj);
        if (doc.status) {
            //Course Schedule set leave for sessions for student
            //Course Schedule leave set for student
            let csData = {};
            if (req.body.type === constant.LEAVE_TYPE.LEAVE || req.body.type === ONDUTY) {
                csData = await base_control.get_list(
                    course_schedule,
                    {
                        _institution_id: ObjectId(req.headers._institution_id),
                        _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                        //schedule_date: { $in: [new Date(req.body.from), new Date(req.body.to)] },
                        schedule_date: {
                            $gte: dateTimeLocalFormatter(parseInt(req.body.from)),
                            $lte: dateTimeLocalFormatter(parseInt(req.body.to)),
                        },
                        isDeleted: false,
                        'students._id': ObjectId(req.body._user_id),
                    },
                    { _id: 1, _course_id: 1, schedule_date: 1, term: 1, students: 1 },
                );
                if (csData.status) {
                    const bulk_data = [];
                    csData.data.forEach((eleCS) => {
                        // console.log('LEAVE/ONDUTY ', eleCS._id);
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCS._id),
                                },
                                update: {
                                    $set: {
                                        'students.$[i].status': req.body.type,
                                        'students.$[i].primaryStatus': req.body.type,
                                    },
                                },
                                arrayFilters: [{ 'i._id': ObjectId(req.body._user_id) }],
                            },
                        });
                    });
                    if (bulk_data.length > 0)
                        console.log(await base_control.bulk_write(course_schedule, bulk_data));
                    //return res.send(bulk_data);
                }
            } else if (req.body.type === constant.LEAVE_TYPE.PERMISSION) {
                const fromDate = dateTimeLocalFormatter(parseInt(req.body.from));
                const toDate = dateTimeLocalFormatter(parseInt(req.body.to));
                const fromDateOnly = formatDate(fromDate);
                const toDateOnly = formatDate(toDate);
                csData = await base_control.get_list(
                    course_schedule,
                    {
                        _institution_id: ObjectId(req.headers._institution_id),
                        _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                        schedule_date: {
                            $gte: fromDateOnly,
                            $lte: toDateOnly,
                        },
                        isDeleted: false,
                        'students._id': ObjectId(req.body._user_id),
                    },
                    {
                        _id: 1,
                        _course_id: 1,
                        term: 1,
                        schedule_date: 1,
                        start: 1,
                        end: 1,
                        students: 1,
                    },
                );
                if (csData.status) {
                    const bulk_data = [];
                    for (scheduleElement of csData.data) {
                        const schedulingDate = new Date(scheduleElement.schedule_date);
                        const otherStart = dateFormatter(
                            schedulingDate.getFullYear(),
                            schedulingDate.getMonth() + 1,
                            schedulingDate.getDate(),
                            scheduleElement.start.hour,
                            scheduleElement.start.minute,
                            scheduleElement.start.format,
                        );
                        const otherEnd = dateFormatter(
                            schedulingDate.getFullYear(),
                            schedulingDate.getMonth() + 1,
                            schedulingDate.getDate(),
                            scheduleElement.end.hour,
                            scheduleElement.end.minute,
                            scheduleElement.end.format,
                        );
                        otherStart.setMinutes(otherStart.getMinutes() + 1);
                        otherEnd.setMinutes(otherEnd.getMinutes() - 1);
                        if (
                            (otherStart <= fromDate && otherEnd >= toDate) ||
                            (otherStart <= toDate && otherEnd >= fromDate)
                        ) {
                            bulk_data.push({
                                updateOne: {
                                    filter: {
                                        _id: ObjectId(scheduleElement._id),
                                    },
                                    update: {
                                        $set: {
                                            'students.$[i].status': req.body.type,
                                            'students.$[i].primaryStatus': req.body.type,
                                        },
                                    },
                                    arrayFilters: [{ 'i._id': ObjectId(req.body._user_id) }],
                                },
                            });
                        }
                    }
                    if (bulk_data.length > 0)
                        console.log(await base_control.bulk_write(course_schedule, bulk_data));
                }
            }

            //Send Mail to Student & Course Coordinator About their Leave/Permission/On_Duty
            const courseUserIds = [];
            if (csData.status) {
                const scheduleCourseId = csData.data.map((ele) => ele && ele._course_id);
                // Course Data Get
                const { data: courseData } = await get_list(
                    course,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: { $in: scheduleCourseId },
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        coordinators: 1,
                    },
                );

                for (courseElement of courseData) {
                    if (courseElement.coordinators && courseElement.coordinators.length) {
                        for (coordinatorsElement of courseElement.coordinators) {
                            const courseScheduleData = csData.data.find(
                                (ele) => ele._course_id.toString() === courseElement._id.toString(),
                            );
                            if (
                                courseScheduleData &&
                                courseScheduleData.term === coordinatorsElement.term
                            )
                                courseUserIds.push(coordinatorsElement._user_id);
                        }
                    }
                }
            }
            const ids = clone(courseUserIds);
            ids.push(req.body._user_id);
            const query_c = { _id: ids };
            const UserData = await get_list(user, query_c, {
                user_id: 1,
                name: 1,
                email: 1,
                mobile: 1,
                user_type: 1,
            });

            // Student Mail Push
            const studentReceiverData = UserData.data.find(
                (ele) => ele._id.toString() === _user_id.toString(),
            );
            const studentMailSubject = `${capitalize(type)} Entries`;
            const studentSmsContent = `Dear, ${nameFormatter(
                studentReceiverData.name,
            )}, ${capitalize(
                type,
            )} Entry is reported in the Digischeduler, For more information please check the mail.`;
            let studentMailBody = `<br>`;
            if (category_name)
                studentMailBody = studentMailBody.concat(`Category : ${category_name}<br>`);
            if (leave_type_name)
                studentMailBody = studentMailBody.concat(`Type : ${leave_type_name}<br>`);
            studentMailBody = studentMailBody.concat(
                `Start date : ${dateTimeLocalFormatInString(parseInt(req.body.from))}<br>`,
            );
            studentMailBody = studentMailBody.concat(
                `End date : ${dateTimeLocalFormatInString(parseInt(req.body.to))}<br>`,
            );
            studentMailBody = studentMailBody.concat(
                `<br><br>Entry is reported in the Digischeduler<br>`,
            );
            await studentContentPush(
                studentReceiverData,
                notifyVia,
                studentMailSubject,
                studentMailBody,
                studentSmsContent,
            );
            if (courseUserIds.length !== 0) {
                for (courseUser of courseUserIds) {
                    const staffReceiverData = UserData.data.find(
                        (ele) => ele._id.toString() === courseUser.toString(),
                    );
                    if (staffReceiverData) {
                        const staffMailSubject = `Student ${capitalize(type)} Entries`;
                        const staffSmsContent = `Dear, ${nameFormatter(
                            staffReceiverData.name,
                        )}, ${capitalize(
                            type,
                        )} Entry is reported in the Digischeduler, For more information please check the mail.`;
                        let staffMailBody = `<br>`;
                        staffMailBody = staffMailBody.concat(
                            `Student name : ${nameFormatter(studentReceiverData.name)}<br>`,
                        );
                        staffMailBody = staffMailBody.concat(
                            `Academic no : ${studentReceiverData.user_id}<br>`,
                        );
                        if (category_name)
                            staffMailBody = staffMailBody.concat(`Category : ${category_name}<br>`);
                        if (leave_type_name)
                            staffMailBody = staffMailBody.concat(`Type : ${leave_type_name}<br>`);
                        staffMailBody = staffMailBody.concat(
                            `Start date : ${dateTimeLocalFormatInString(
                                parseInt(req.body.from),
                            )}<br>`,
                        );
                        staffMailBody = staffMailBody.concat(
                            `End date : ${dateTimeLocalFormatInString(parseInt(req.body.to))}<br>`,
                        );
                        staffMailBody = staffMailBody.concat(
                            `<br><br>Entry is reported in the Digischeduler<br>`,
                        );
                        await studentContentPush(
                            staffReceiverData,
                            notifyVia,
                            staffMailSubject,
                            staffMailBody,
                            staffSmsContent,
                        );
                    }
                }
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(res, 200, true, 'Added successfully', doc.data),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.student_report_absence_update = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { id: rowId },
            body: {
                _institution_calendar_id,
                _program_id,
                _user_id,
                user_type,
                type,
                from,
                to,
                reason,
                _leave_reason_doc,
                application_date,
                _leave_category_id,
                category_name,
                _person_id,
                approved_date,
                approved_comments,
                _leave_type_id,
                leave_type_name,
                notifyVia,
            },
        } = req;
        // console.log(rowId);
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const lmsSetting = await get(lms, { _institution_id: ObjectId(_institution_id) }, {});
        if (!lmsSetting.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                    ),
                );
        const dt1 = dateTimeLocalFormatter(parseInt(from));
        const dt2 = dateTimeLocalFormatter(parseInt(to));
        const leaveQuery = {
            // _id: { $not: { $eq: rowId } },
            _institution_id: ObjectId(_institution_id),
            _institution_calendar_id: ObjectId(_institution_calendar_id),
            user_type,
            _user_id: ObjectId(_user_id),
            status: { $not: { $eq: 'rejected' } },
            isActive: true,
            isDeleted: false,
        };
        const leave_check = await get_list(lms_review, leaveQuery, {});
        leave_check.data = leave_check.status ? leave_check.data : [];
        const registerData = leave_check.data.find(
            (ele) => ele && ele._id.toString() === rowId.toString(),
        );
        const registerFrom = dateTimeLocalFormatter(registerData.from);
        const registerTo = dateTimeLocalFormatter(registerData.to);
        // console.log(leave_check.data.length);
        leave_check.data = clone(
            leave_check.data.filter((ele) => ele && ele._id.toString() !== rowId.toString()),
        );
        // console.log(leave_check.data.length);
        for (element of leave_check.data) {
            const from = dateTimeLocalFormatter(element.from);
            const to = dateTimeLocalFormatter(element.to);
            if ((dt1 <= from && dt2 > from) || (dt1 < to && dt2 >= to) || (dt2 > from && dt1 < to))
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            false,
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                        ),
                    );
        }
        // const duplicateCheckStatus = leave_check.data.filter(
        //     (ele) =>
        //         ele &&
        //         ele._id.toString() !== rowId.toString() &&
        //         ((ele.from <= dt1 && ele.from >= dt2) || (ele.to >= dt1 && ele.to <= dt2)),
        // );
        // if (duplicateCheckStatus.length !== 0) {
        //     return res
        //         .status(410)
        //         .send(
        //             response_function(
        //                 res,
        //                 410,
        //                 false,
        //                 'Duplicate on Permission/Leave/On_Duty',
        //                 'Duplicate on Permission/Leave/On_Duty',
        //             ),
        //         );
        // }
        // let type_data;
        // let frequency = 0;
        const duration = 0;
        // console.log('LOC ', dt1, ' ', dt2);
        // let no_days = 0;
        // const diff = dt2 - dt1;
        // const diffInDays = diff / (1000 * 60 * 60 * 24);
        // no_days = Math.round(diffInDays);
        // console.log('No Of days', no_days);
        const calc_days = (await dayCalculation(dt1, dt2)).withOutHolidays;
        // if (PERMISSION === type) {
        //     duration = diff_hours(dt1, dt2);
        //     type_data = lmsSetting.data.category.find(
        //         (i) => i.category_type == PERMISSION && i.category_to == user_type,
        //     );
        //     if (!type_data)
        //         return res
        //             .status(409)
        //             .send(
        //                 response_function(
        //                     res,
        //                     409,
        //                     false,
        //                      req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
        //                      req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
        //                 ),
        //             );
        //     const leaveData = leave_check.data.filter((ele) => ele.type === type);
        //     frequency = type_data.permission.permission_frequency;
        //     if (
        //         type_data.permission.permission_frequency_by === 'year' ||
        //         type_data.permission.permission_frequency_by === 'null'
        //     ) {
        //         if (leaveData.length >= frequency)
        //             return res
        //                 .status(410)
        //                 .send(
        //                     response_function(
        //                         res,
        //                         410,
        //                         false,
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                     ),
        //                 );
        //     } else if (type_data.permission.permission_frequency_by === 'month') {
        //         let i = 0;
        //         for (element of leaveData) {
        //             const from = dateTimeLocalFormatter(element.from);
        //             if (from.getUTCMonth() === dt1.getUTCMonth()) i += element.days;
        //         }
        //         if (frequency <= i)
        //             return res
        //                 .status(409)
        //                 .send(
        //                     common_files.response_function(
        //                         res,
        //                         409,
        //                         false,
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                          req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
        //                     ),
        //                 );
        //     }
        // }
        // if (type === LEAVE) {
        //     const c_data = lmsSetting.data.category.find(
        //         (ele) => ele._id.toString() === _leave_category_id.toString(),
        //     );
        //     type_data = c_data.leave_type.find(
        //         (ele) => ele._id.toString() === _leave_type_id.toString(),
        //     );
        //     frequency = type_data.no_of_days;
        //     const coursePercentage = type_data.no_of_days;
        //     const exceededCourses = [];
        //     // Schedule Check
        //     const scheduleData = await get_list(
        //         course_schedule,
        //         {
        //             isDeleted: false,
        //             _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
        //             type: REGULAR,
        //             // schedule_date: { $lte: dt2 },
        //             'students._id': convertToMongoObjectId(_user_id),
        //         },
        //         {
        //             _id: 1,
        //             schedule_date: 1,
        //             _course_id: 1,
        //             students: 1,
        //             status: 1,
        //             start: 1,
        //             end: 1,
        //             'session._session_id': 1,
        //             'session.session_type': 1,
        //         },
        //     );
        //     scheduleData.data = scheduleData.status ? scheduleData.data : [];
        //     const studentCourseIds = [
        //         ...new Set(scheduleData.data.map((ele) => ele._course_id.toString())),
        //     ];
        //     // Schedule Datas
        //     const scheduleList = clone(scheduleData.data);
        //     for (scheduleElement of scheduleList) {
        //         const stdData = scheduleElement.students.find(
        //             (ele2) => ele2._id.toString() === _user_id.toString(),
        //         );
        //         const schedulingDate = new Date(scheduleElement.schedule_date);
        //         const schedulingDateLocal = dateTimeLocalFormatter(scheduleElement.schedule_date);
        //         if (
        //             (registerFrom <= schedulingDateLocal && registerTo >= schedulingDateLocal) ||
        //             (registerFrom >= schedulingDateLocal && registerTo <= schedulingDateLocal)
        //         )
        //             stdData.status = PENDING;
        //         scheduleElement.students = stdData;
        //         const otherStart = dateFormatter(
        //             schedulingDate.getFullYear(),
        //             schedulingDate.getMonth() + 1,
        //             schedulingDate.getDate(),
        //             scheduleElement.start.hour,
        //             scheduleElement.start.minute,
        //             scheduleElement.start.format,
        //         );
        //         const otherEnd = dateFormatter(
        //             schedulingDate.getFullYear(),
        //             schedulingDate.getMonth() + 1,
        //             schedulingDate.getDate(),
        //             scheduleElement.end.hour,
        //             scheduleElement.end.minute,
        //             scheduleElement.end.format,
        //         );
        //         otherStart.setMinutes(otherStart.getMinutes() + 1);
        //         otherEnd.setMinutes(otherEnd.getMinutes() - 1);
        //         if (
        //             (otherStart <= dt1 && otherEnd >= dt2) ||
        //             (otherStart <= dt2 && otherEnd >= dt1)
        //         ) {
        //             scheduleElement.students.status = LEAVE;
        //         }
        //     }
        //     for (courseElement of studentCourseIds) {
        //         const courseScheduleData = scheduleList.filter(
        //             (ele) => ele._course_id.toString() === courseElement.toString(),
        //         );
        //         const completedSchedule = courseScheduleData.filter(
        //             (ele) => ele.status === COMPLETED,
        //         );
        //         const absentSchedules = courseScheduleData.filter(
        //             (ele) =>
        //                 ele.students.status !== PENDING &&
        //                 (ele.students.status === ABSENT || ele.students.status === LEAVE),
        //         );
        //         const presentSchedules = completedSchedule.filter(
        //             (ele) => ele.students.status === PRESENT,
        //         );
        //         const denialPercentage = (absentSchedules.length / courseScheduleData.length) * 100;
        //         const courseSchedule = {
        //             _course_id: courseElement,
        //             scheduleCount: courseScheduleData.length,
        //             presentCount: presentSchedules.length,
        //             completedCount: completedSchedule.length,
        //             absentCount: absentSchedules.length,
        //             denialPercentage: denialPercentage.toFixed(2),
        //         };
        //         if (coursePercentage <= parseFloat(denialPercentage)) {
        //             exceededCourses.push(courseSchedule);
        //         }
        //     }
        //     // console.log({ coursePercentage, exceededCourses });
        //     if (exceededCourses.length !== 0)
        //         return res
        //             .status(410)
        //             .send(
        //                 response_function(
        //                     res,
        //                     410,
        //                     false,
        //                     'Student Attendance Absence is High so unable to add Leave',
        //                     'Student Attendance Absence is High so unable to add Leave',
        //                 ),
        //             );
        // }
        // if (type === ONDUTY) {
        //     let ondutyCheck = false;
        //     const c_data = lmsSetting.data.category.find(
        //         (ele) => ele._id.toString() === _leave_category_id.toString(),
        //     );
        //     type_data = c_data.leave_type.find(
        //         (ele) => ele._id.toString() === _leave_type_id.toString(),
        //     );
        //     const weekendConsideration =
        //         type_data.weekend_consideration === undefined
        //             ? false
        //             : type_data.weekend_consideration;
        //     const dayData = await dayCalculation(dt1, dt2);
        //     calc_days = weekendConsideration ? dayData.withHolidays : dayData.withOutHolidays;
        //     frequency = type_data.no_of_days;
        //     console.log({ dt1, dt2, type_data, frequency, calc_days });
        //     const leaveData = leave_check.data.filter(
        //         (ele) =>
        //             ele.type === type &&
        //             ele.leave_type._leave_type_id &&
        //             ele.leave_type._leave_type_id.toString() == _leave_type_id.toString(),
        //     );
        //     if (frequency < calc_days) ondutyCheck = true;
        //     else if (type_data.entitlement === 'month') {
        //         let i = calc_days;
        //         if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
        //             i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
        //             ondutyCheck = frequency < i;
        //             console.log({ frequency, i });
        //         } else {
        //             const diffMonthDates = await diffMonthDateCalculation(
        //                 dt1,
        //                 dt2,
        //                 leaveData,
        //                 weekendConsideration,
        //             );
        //             ondutyCheck =
        //                 frequency < diffMonthDates.fromMonthDates ||
        //                 frequency < diffMonthDates.toMonthDates;
        //             console.log({ frequency, diffMonthDates });
        //         }
        //     } else if (type_data.entitlement === 'year') {
        //         let totalDays = calc_days;
        //         for (element of leaveData) {
        //             totalDays += element.days;
        //         }
        //         console.log('Check ', totalDays, frequency < totalDays, frequency > totalDays);
        //         if (type_data.per_month === 0 || frequency < totalDays) {
        //             ondutyCheck = frequency < totalDays;
        //         } else {
        //             if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
        //                 let i = calc_days;
        //                 i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
        //                 ondutyCheck = type_data.per_month < i || frequency < i;
        //                 console.log({ perMonth: type_data.per_month, frequency, i });
        //             } else {
        //                 const diffMonthDates = await diffMonthDateCalculation(
        //                     dt1,
        //                     dt2,
        //                     leaveData,
        //                     weekendConsideration,
        //                 );
        //                 ondutyCheck =
        //                     frequency < diffMonthDates.fromMonthDates ||
        //                     frequency < diffMonthDates.toMonthDates ||
        //                     (type_data.per_month < diffMonthDates.fromMonthDates &&
        //                         type_data.per_month < diffMonthDates.toMonthDates);
        //                 console.log({ perMonth: type_data.per_month, frequency, diffMonthDates });
        //             }
        //         }
        //     }
        //     if (ondutyCheck)
        //         return res
        //             .status(410)
        //             .send(
        //                 response_function(
        //                     res,
        //                     410,
        //                     false,
        //                     'You have crossed your ' + type + ' Month Frequency/Year Frequency',
        //                     'You have crossed your ' + type + ' Month Frequency/Year Frequency',
        //                 ),
        //             );
        // }
        // console.log(req.body.days, calc_days);
        const obj = {
            _institution_id: req.headers._institution_id,
            _institution_calendar_id: req.body._institution_calendar_id,
            _program_id,
            _user_id: req.body._user_id,
            user_type: req.body.user_type,
            type: req.body.type,
            from: req.body.from,
            to: req.body.to,
            days: calc_days == 0 ? req.body.days : calc_days,
            permission_hour: duration,
            reason: req.body.reason,
            leave_category: {
                _leave_category_id: req.body._leave_category_id,
                name: req.body.category_name,
            },
            leave_type: {
                _leave_type_id: req.body._leave_type_id,
                name: req.body.leave_type_name,
            },
            _leave_reason_doc: req.body._leave_reason_doc,
            application_date: req.body.application_date,
            approved_by: {
                _person_id: req.body._person_id,
                date: req.body.approved_date,
            },
            comments: [
                {
                    _person_id: req.body._person_id,
                    comment: req.body.approved_comments,
                },
            ],
        };
        // return res.send(obj);
        const query = { _id: ObjectId(req.params.id) };
        // const doc = { status: true };
        const doc = await base_control.update(lms_review, query, obj);
        if (doc.status) {
            // Updating Student Status
            const oldSchedule = await get_list(
                course_schedule,
                {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                    schedule_date: {
                        $gte: registerData.from,
                        $lte: registerData.to,
                    },
                    isDeleted: false,
                    'students._id': ObjectId(req.body._user_id),
                },
                { _id: 1, _course_id: 1, schedule_date: 1, students: 1 },
            );
            let csData = {};
            if (req.body.type === constant.LEAVE_TYPE.LEAVE || req.body.type === ONDUTY) {
                if (oldSchedule.status) {
                    const bulk_data = [];
                    for (scheduleElement of oldSchedule.data) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(scheduleElement._id),
                                },
                                update: {
                                    $set: {
                                        'students.$[i].status': PENDING,
                                        'students.$[i].primaryStatus': PENDING,
                                    },
                                },
                                arrayFilters: [{ 'i._id': ObjectId(req.body._user_id) }],
                            },
                        });
                    }
                    if (bulk_data.length > 0)
                        await base_control.bulk_write(course_schedule, bulk_data);
                }
                // Updating new dates
                csData = await get_list(
                    course_schedule,
                    {
                        _institution_id: ObjectId(req.headers._institution_id),
                        _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                        //schedule_date: { $in: [new Date(req.body.from), new Date(req.body.to)] },
                        schedule_date: {
                            $gte: dateTimeLocalFormatter(parseInt(req.body.from)),
                            $lte: dateTimeLocalFormatter(parseInt(req.body.to)),
                        },
                        isDeleted: false,
                        'students._id': ObjectId(req.body._user_id),
                    },
                    { _id: 1, _course_id: 1, term: 1, schedule_date: 1, students: 1 },
                );
                if (csData.status) {
                    const bulk_data = [];
                    csData.data.forEach((eleCS) => {
                        console.log('LEAVE/ONDUTY ', eleCS._id);
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(eleCS._id),
                                },
                                update: {
                                    $set: {
                                        'students.$[i].status': req.body.type,
                                        'students.$[i].primaryStatus': req.body.type,
                                    },
                                },
                                arrayFilters: [{ 'i._id': ObjectId(req.body._user_id) }],
                            },
                        });
                    });
                    if (bulk_data.length > 0)
                        await base_control.bulk_write(course_schedule, bulk_data);
                }
            } else if (req.body.type === constant.LEAVE_TYPE.PERMISSION) {
                if (oldSchedule.status) {
                    const bulk_data = [];
                    for (scheduleElement of oldSchedule.data) {
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(scheduleElement._id),
                                },
                                update: {
                                    $set: {
                                        'students.$[i].status': PENDING,
                                        'students.$[i].primaryStatus': PENDING,
                                    },
                                },
                                arrayFilters: [{ 'i._id': ObjectId(req.body._user_id) }],
                            },
                        });
                    }
                    if (bulk_data.length > 0)
                        await base_control.bulk_write(course_schedule, bulk_data);
                }
                // Updating new dates
                const fromDate = dateTimeLocalFormatter(parseInt(req.body.from));
                const toDate = dateTimeLocalFormatter(parseInt(req.body.to));
                const fromDateOnly = formatDate(fromDate);
                const toDateOnly = formatDate(toDate);
                csData = await base_control.get_list(
                    course_schedule,
                    {
                        _institution_id: ObjectId(req.headers._institution_id),
                        _institution_calendar_id: ObjectId(req.body._institution_calendar_id),
                        schedule_date: {
                            $gte: fromDateOnly,
                            $lte: toDateOnly,
                        },
                        isDeleted: false,
                        'students._id': ObjectId(req.body._user_id),
                    },
                    {
                        _id: 1,
                        _course_id: 1,
                        term: 1,
                        schedule_date: 1,
                        start: 1,
                        end: 1,
                        students: 1,
                    },
                );
                if (csData.status) {
                    const bulk_data = [];
                    for (scheduleElement of csData.data) {
                        const schedulingDate = new Date(scheduleElement.schedule_date);
                        const otherStart = dateFormatter(
                            schedulingDate.getFullYear(),
                            schedulingDate.getMonth() + 1,
                            schedulingDate.getDate(),
                            scheduleElement.start.hour,
                            scheduleElement.start.minute,
                            scheduleElement.start.format,
                        );
                        const otherEnd = dateFormatter(
                            schedulingDate.getFullYear(),
                            schedulingDate.getMonth() + 1,
                            schedulingDate.getDate(),
                            scheduleElement.end.hour,
                            scheduleElement.end.minute,
                            scheduleElement.end.format,
                        );
                        otherStart.setMinutes(otherStart.getMinutes() + 1);
                        otherEnd.setMinutes(otherEnd.getMinutes() - 1);
                        if (
                            (otherStart <= fromDate && otherEnd >= toDate) ||
                            (otherStart <= toDate && otherEnd >= fromDate)
                        ) {
                            bulk_data.push({
                                updateOne: {
                                    filter: {
                                        _id: ObjectId(scheduleElement._id),
                                    },
                                    update: {
                                        $set: {
                                            'students.$[i].status': req.body.type,
                                            'students.$[i].primaryStatus': req.body.type,
                                        },
                                    },
                                    arrayFilters: [{ 'i._id': ObjectId(req.body._user_id) }],
                                },
                            });
                        }
                    }
                    if (bulk_data.length > 0)
                        await base_control.bulk_write(course_schedule, bulk_data);
                }
            }

            //Send Mail to Student About their Leave/Permission/On_Duty
            // const query_c = { _id: ObjectId(req.body._user_id) };
            // const student_data = await base_control.get(user, query_c, {
            //     name: 1,
            //     email: 1,
            //     mobile: 1,
            // });

            //Send Mail to Student & Course Coordinator About their Leave/Permission/On_Duty
            const courseUserIds = [];
            if (csData.status) {
                const scheduleCourseId = csData.data.map((ele) => ele && ele._course_id);
                // Course Data Get
                const { data: courseData } = await get_list(
                    course,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: { $in: scheduleCourseId },
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        coordinators: 1,
                    },
                );

                for (courseElement of courseData) {
                    if (courseElement.coordinators && courseElement.coordinators.length) {
                        for (coordinatorsElement of courseElement.coordinators) {
                            const courseScheduleData = csData.data.find(
                                (ele) => ele._course_id.toString() === courseElement._id.toString(),
                            );
                            if (
                                courseScheduleData &&
                                courseScheduleData.term === coordinatorsElement.term
                            )
                                courseUserIds.push(coordinatorsElement._user_id);
                        }
                    }
                }
            }
            const ids = clone(courseUserIds);
            ids.push(req.body._user_id);
            const query_c = { _id: ids };
            const UserData = await get_list(user, query_c, {
                user_id: 1,
                name: 1,
                email: 1,
                mobile: 1,
                user_type: 1,
            });

            // Student Mail Push
            const studentReceiverData = UserData.data.find(
                (ele) => ele._id.toString() === _user_id.toString(),
            );
            const studentMailSubject = `${capitalize(type)} Entries`;
            const studentSmsContent = `Dear, ${nameFormatter(
                studentReceiverData.name,
            )}, ${capitalize(
                type,
            )} Entry is reported in the Digischeduler, For more information please check the mail.`;
            let studentMailBody = `<br>`;
            if (category_name)
                studentMailBody = studentMailBody.concat(`Category : ${category_name}<br>`);
            if (leave_type_name)
                studentMailBody = studentMailBody.concat(`Type : ${leave_type_name}<br>`);
            studentMailBody = studentMailBody.concat(
                `Start date : ${dateTimeLocalFormatInString(parseInt(req.body.from))}<br>`,
            );
            studentMailBody = studentMailBody.concat(
                `End date : ${dateTimeLocalFormatInString(parseInt(req.body.to))}<br>`,
            );
            studentMailBody = studentMailBody.concat(
                `<br><br>Entry is reported in the Digischeduler<br>`,
            );
            await studentContentPush(
                studentReceiverData,
                notifyVia,
                studentMailSubject,
                studentMailBody,
                studentSmsContent,
            );
            if (courseUserIds.length !== 0) {
                for (courseUser of courseUserIds) {
                    const staffReceiverData = UserData.data.find(
                        (ele) => ele._id.toString() === courseUser.toString(),
                    );
                    if (staffReceiverData) {
                        const staffMailSubject = `Student ${capitalize(type)} Entries`;
                        const staffSmsContent = `Dear, ${nameFormatter(
                            staffReceiverData.name,
                        )}, ${capitalize(
                            type,
                        )} Entry is reported in the Digischeduler, For more information please check the mail.`;
                        let staffMailBody = `<br>`;
                        staffMailBody = staffMailBody.concat(
                            `Student name : ${nameFormatter(studentReceiverData.name)}<br>`,
                        );
                        staffMailBody = staffMailBody.concat(
                            `Academic no : ${studentReceiverData.user_id}<br>`,
                        );
                        if (category_name)
                            staffMailBody = staffMailBody.concat(`Category : ${category_name}<br>`);
                        if (leave_type_name)
                            staffMailBody = staffMailBody.concat(`Type : ${leave_type_name}<br>`);
                        staffMailBody = staffMailBody.concat(
                            `Start date : ${dateTimeLocalFormatInString(
                                parseInt(req.body.from),
                            )}<br>`,
                        );
                        staffMailBody = staffMailBody.concat(
                            `End date : ${dateTimeLocalFormatInString(parseInt(req.body.to))}<br>`,
                        );
                        staffMailBody = staffMailBody.concat(
                            `<br><br>Entry is reported in the Digischeduler<br>`,
                        );
                        await studentContentPush(
                            staffReceiverData,
                            notifyVia,
                            staffMailSubject,
                            staffMailBody,
                            staffSmsContent,
                        );
                    }
                }
            }
            // return res.send(approvers_datas);
            // const from = dateTimeLocalFormatter(parseInt(req.body.from));
            // const to = dateTimeLocalFormatter(parseInt(req.body.to));
            // const name = student_data.data.name.middle
            //     ? student_data.data.name.first +
            //       ' ' +
            //       student_data.data.name.middle +
            //       ' ' +
            //       student_data.data.name.last
            //     : student_data.data.name.first + ' ' + student_data.data.name.last;
            // console.log(req.body.notifyVia);
            // if (req.body.notifyVia && req.body.notifyVia.includes(constant.NOTIFY_VIA.MOBILE)) {
            //     const msg =
            //         'Dear ' +
            //         name +
            //         ', Your ' +
            //         req.body.type +
            //         ' from ' +
            //         from +
            //         ' to ' +
            //         to +
            //         ' dates Registered in DigiScheduler.';
            //     if (student_data.data.mobile)
            //         common_functions.send_sms(student_data.data.mobile, msg);
            // }
            // if (req.body.notifyVia && req.body.notifyVia.includes(constant.NOTIFY_VIA.EMAIL)) {
            //     const message =
            //         'Dear ' +
            //         name +
            //         '<br>Your ' +
            //         req.body.type +
            //         ' from ' +
            //         from +
            //         ' to ' +
            //         to +
            //         ' dates Registered in DigiScheduler.<br><br> Best regards Ibn Sina National College for Medical Studies';
            //     const to_email = student_data.data.email;
            //     const subject = 'Student Leave Register';
            //     common_functions.send_email(to_email, subject, message);
            // }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.delete_report_student_absence = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _id: ObjectId(req.params.id) };
        const leave_check = await get(lms_review, query, {});
        const obj = { isDeleted: true };
        // const doc = { status: true };
        const doc = await base_control.update(lms_review, query, obj);
        if (doc.status) {
            const fromDate = dateTimeLocalFormatter(leave_check.data.from);
            const toDate = dateTimeLocalFormatter(leave_check.data.to);
            const fromDateOnly = formatDate(fromDate);
            const toDateOnly = formatDate(toDate);
            // const checkFromDate = leave_check.data.type === PERMISSION ? fromDateOnly : fromDate;
            // const checkToDate = leave_check.data.type === PERMISSION ? toDateOnly : toDate;
            // Updating Student Status
            const oldSchedule = await get_list(
                course_schedule,
                {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _institution_calendar_id: ObjectId(leave_check.data._institution_calendar_id),
                    schedule_date: {
                        $gte: fromDateOnly,
                        $lte: toDateOnly,
                    },
                    // status: { $ne: COMPLETED },
                    isDeleted: false,
                    'students._id': ObjectId(leave_check.data._user_id),
                },
                {
                    _id: 1,
                    _course_id: 1,
                    schedule_date: 1,
                    start: 1,
                    end: 1,
                    students: 1,
                    status: 1,
                },
            );
            if (oldSchedule.status) {
                const bulk_data = [];
                for (scheduleElement of oldSchedule.data) {
                    const scheduling_date = new Date(scheduleElement.schedule_date);
                    const start = dateFormatter(
                        scheduling_date.getFullYear(),
                        scheduling_date.getMonth() + 1,
                        scheduling_date.getDate(),
                        scheduleElement.start.hour,
                        scheduleElement.start.minute,
                        scheduleElement.start.format,
                    );
                    const end = dateFormatter(
                        scheduling_date.getFullYear(),
                        scheduling_date.getMonth() + 1,
                        scheduling_date.getDate(),
                        scheduleElement.end.hour,
                        scheduleElement.end.minute,
                        scheduleElement.end.format,
                    );
                    start.setMinutes(start.getMinutes() + 1);
                    end.setMinutes(end.getMinutes() - 1);
                    let scheduleCheck = true;
                    if ((fromDate <= start && toDate >= end) || (fromDate <= end && toDate >= end))
                        scheduleCheck = false;
                    const studentSessionStatus =
                        scheduleElement.status === COMPLETED ? ABSENT : PENDING;
                    const studentData = scheduleElement.students.find(
                        (ele) => ele && ele._id.toString() === leave_check.data._user_id.toString(),
                    );
                    if (studentData && studentData.status !== PRESENT)
                        bulk_data.push({
                            updateOne: {
                                filter: {
                                    _id: ObjectId(scheduleElement._id),
                                },
                                update: {
                                    $set: {
                                        'students.$[i].status': studentSessionStatus,
                                        'students.$[i].primaryStatus': studentSessionStatus,
                                    },
                                },
                                arrayFilters: [{ 'i._id': ObjectId(leave_check.data._user_id) }],
                            },
                        });
                }
                if (bulk_data.length > 0) await base_control.bulk_write(course_schedule, bulk_data);
            }
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(410)
            .send(common_files.response_function(res, 410, false, req.t('DELETE_ERROR'), []));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.get_report_student_absence = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const aggre = [
            {
                $match: {
                    _id: ObjectId(req.params.id),
                    user_type: constant.EVENT_WHOM.STUDENT,
                    isDeleted: false,
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: '_user_id',
                    foreignField: '_id',
                    as: 'user_data',
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'approved_by._person_id',
                    foreignField: '_id',
                    as: 'staff_data',
                },
            },
        ];
        const doc = await base_control.get_aggregate(lms_review, aggre);
        if (doc.status) {
            if (
                doc.data[0]._leave_reason_doc &&
                doc.data[0]._leave_reason_doc.length &&
                doc.data[0]._leave_reason_doc !== null &&
                doc.data[0]._leave_reason_doc !== 'null'
            )
                doc.data[0]._leave_reason_doc_url = await common_functions.getSignedURL(
                    doc.data[0]._leave_reason_doc,
                );
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('STUDENT_REPORT_ABSENCE_DETAILS'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(410)
            .send(common_files.response_function(res, 410, false, req.t('NO_RECORDS'), []));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

// const dateFormatChange = function (date) {
//     const d = new Date(date);
//     const format = [d.getFullYear(), d.getMonth() + 1, d.getDate()].join('-');
//     return format;
// };

// const dateTimeFormatChange = function (date) {
//     const d = new Date(date);
//     const format =
//         [d.getFullYear(), d.getMonth() + 1, d.getDate()].join('-') +
//         ' ' +
//         [d.getHours(), d.getMinutes(), d.getSeconds()].join(':');
//     return format;
// };

// exports.update_leave_flow_process = async (req, res) => {
//     let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
//     if (!institution_check.status)
//         return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found",  req.t('INSTITUTION_NOT_FOUND')));

//     let checkQuery = { _id: Object(req.params.id) };
//     let checkDoc = await base_control.get(lms_review, checkQuery);
//     if (!checkDoc.status) res.status(404).send(common_files.response_function(res, 404, false, "Data not found", 'Data not found'));

//     //console.log(checkDoc.data._user_id)

//     //User Data
//     let query_u = { _id: ObjectId(checkDoc.data._user_id) };
//     let user_data = await base_control.get(user, query_u, {});

//     //Approver Data
//     let query_a = { _id: ObjectId(req.body._person_id) };
//     let approver_data = await base_control.get(user, query_a, {});
//     //console.log(user_data)

//     let query = { _id: Object(req.params.id) };
//     let obj = {};
//     if (req.body.leave_flow_type == constant.LEAVE_FLOW_TYPE.REVIEW) {
//         obj = {
//             $push: {
//                 comments: {
//                     _person_id: req.body.person_id,
//                     comment: req.body.comment
//                 }
//             },
//             $set: {
//                 review: req.body.leave_flow_type_status,
//                 payment_status: req.body.payment_status
//             }
//         };
//     }
//     else if (req.body.leave_flow_type == constant.LEAVE_FLOW_TYPE.FORWARD) {
//         obj = {
//             $push: {
//                 comments: {
//                     _person_id: req.body.person_id,
//                     comment: req.body.comment
//                 }
//             },
//             $set: {
//                 forwarded: req.body.leave_flow_type_status,
//                 payment_status: req.body.payment_status
//             }
//         };
//     }
//     else if (req.body.leave_flow_type == constant.LEAVE_FLOW_TYPE.APPROVE) {
//         obj = {
//             $push: {
//                 comments: {
//                     _person_id: req.body.person_id,
//                     comment: req.body.comment
//                 },
//                 notifyVia: req.body.notify_via
//             },
//             $set: {
//                 approval: req.body.leave_flow_type_status,
//                 payment_status: req.body.payment_status
//             }
//         };
//     }
//     else { }
//     let doc = await base_control.update_condition(lms_review, query, obj);
//     if (doc.status) {
//         if (req.body.leave_flow_type == constant.LEAVE_FLOW_TYPE.APPROVE) {
//             let from = "";
//             let to = "";
//             if (checkDoc.data.type == constant.LEAVE_TYPE.PERMISSION) {
//                 from = dateTimeFormatChange(checkDoc.data.from);
//                 to = dateTimeFormatChange(checkDoc.data.to);
//             }
//             else {
//                 from = dateFormatChange(checkDoc.data.from);
//                 to = dateFormatChange(checkDoc.data.to);
//             }
//             if (req.body.notify_via.includes(constant.NOTIFY_VIA.MOBILE)) {
//                 let msg = "Dear " + user_data.data.name.first + " " + user_data.data.name.last + ", Your " + checkDoc.data.type + " application for " + from + " and " + to + " dates has been approved by approver " + approver_data.data.name.first + " " + approver_data.data.name.last;
//                 common_functions.send_sms(user_data.data.mobile, msg);
//             }
//             if (req.body.notify_via.includes(constant.NOTIFY_VIA.EMAIL)) {
//                 let message = "Dear " + user_data.data.name.first + " " + user_data.data.name.last + ", \n Your " + checkDoc.data.type + " application for " + from + " and " + to + " dates has been approved by approver " + approver_data.data.name.first + " " + approver_data.data.name.last + " \n\n Best regards Ibn Sina National College for Medical Studies";
//                 let to_email = user_data.data.email;
//                 let subject = "Staff Report Absence";
//                 common_functions.send_email(to_email, subject, message);
//             }
//         }
//         return res.status(200).send(common_files.response_function(res, 200, true, "Updated Successfully.", doc.data));
//     }
//     return res.status(404).send(common_files.response_function(res, 404, false, "Error", doc.data));
// }
exports.user_leave_overview = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            status: { $not: { $eq: 'rejected' } },
            type: req.params.type,
            user_type: req.params.user_type,
            _user_id: ObjectId(req.params.user_id),
            _institution_calendar_id: ObjectId(req.params.inst_cal_id),
            isDeleted: false,
            isActive: true,
        };
        const leave_list = await base_control.get_list(lms_review, query, {
            leave_category: 1,
            leave_type: 1,
            days: 1,
        });
        if (!leave_list.status)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('USER_IS_NOT_TAKEN_ANY_LEAVE'),
                        [],
                    ),
                );

        const cat_data = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id) },
            {},
        );
        if (!cat_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        //return res.send(cat_data);
        let obj = {};
        const new_leave = [];
        const leave_details = [];
        for (let i = 0; i < leave_list.data.length; i++) {
            if (leave_list.data[i].leave_category._leave_category_id != undefined)
                new_leave.push(leave_list.data[i]);
        }
        for (let l = 0; l < new_leave.length; l++) {
            const cat_ind = cat_data.data.category.findIndex((ele) =>
                ele._id.equals(new_leave[l].leave_category._leave_category_id),
            );
            if (cat_ind == -1) continue;
            const leave_ind = cat_data.data.category[cat_ind].leave_type.findIndex((ele) =>
                ele._id.equals(new_leave[l].leave_type._leave_type_id),
            );
            if (leave_ind == -1) continue;
            obj = {
                category_id: new_leave[l].leave_category._leave_category_id,
                leave_type_id: new_leave[l].leave_type._leave_type_id,
                days: new_leave[l].days,
                leave_type: cat_data.data.category[cat_ind].leave_type[leave_ind].type_name,
                no_of_days: cat_data.data.category[cat_ind].leave_type[leave_ind].no_of_days,
                entitlement: cat_data.data.category[cat_ind].leave_type[leave_ind].entitlement,
                per_month: cat_data.data.category[cat_ind].leave_type[leave_ind].per_month,
            };
            leave_details.push(obj);
        }
        let leave_type = leave_details.map((ele) => ele.leave_type);
        leave_type = [...new Set(leave_type)];

        let new_obj = {};
        const new_arr = [];
        for (let lt = 0; lt < leave_type.length; lt++) {
            const data = leave_details.filter((ele) => ele.leave_type == leave_type[lt]);
            let count = 0;
            for (let d = 0; d < data.length; d++) {
                count += data[d].days;
            }
            new_obj = {
                category_id: data[0].category_id,
                leave_type_id: data[0].leave_type_id,
                leave_type: data[0].leave_type,
                no_of_days: data[0].no_of_days,
                leave_taken: count,
                entitlement: data[0].entitlement,
                per_month: data[0].per_month,
            };
            new_arr.push(new_obj);
        }
        res.status(200).send(
            common_files.response_function(res, 200, true, req.t('USER_LEAVE_OVERVIEW'), new_arr),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
/* exports.leave_search = async (req, res) => {
    let institution_check = await base_control.get(institution, { _id: ObjectId(req.headers._institution_id) }, { _id: 1 });
    if (!institution_check.status)
        return res.status(404).send(common_files.response_function(res, 404, false, "Institution Not found",  req.t('INSTITUTION_NOT_FOUND')));
    let doc = await base_control.get_list(lms_review, {});
    if (doc.status) {
        common_files.com_response(res, 200, true, "Applied Leave List", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
} */
exports.leave_search = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const search_data = req.params.text;
        let regex = '';
        if (search_data.length != 0) {
            regex = new RegExp(search_data, 'i');
        }
        console.log(regex);
        const aggre = [
            {
                $match: {
                    // _institution_calendar_id: ObjectId('5f61f7d46ee2ab693a173c8d'),
                    type: 'leave',
                    user_type: 'staff',
                    isDeleted: false,
                },
            },
            {
                $lookup: {
                    from: 'users',
                    localField: '_user_id',
                    foreignField: '_id',
                    as: 'user_data',
                },
            },
            {
                $lookup: {
                    from: 'institution_calendars',
                    localField: '_institution_calendar_id',
                    foreignField: '_id',
                    as: 'inst_cal',
                },
            },
            { $unwind: { path: '$user_data', preserveNullAndEmptyArrays: true } },
            {
                $match: {
                    $or: [
                        { 'user_data.name.first': regex },
                        { 'user_data.name.middle': regex },
                        { 'user_data.name.last': regex },
                        { 'user_data.user_id': regex },
                    ],
                },
            },
            {
                $group: {
                    _id: '$_id',
                    applied: { $first: '$applied' },
                    review: { $first: '$review' },
                    forwarded: { $first: '$forwarded' },
                    approval: { $first: '$approval' },
                    notifyVia: { $first: '$notifyVia' },
                    isActive: { $first: '$isActive' },
                    isDeleted: { $first: '$isDeleted' },
                    _institution_calendar_id: { $first: '$_institution_calendar_id' },
                    user_type: { $first: '$user_type' },
                    _user_id: { $first: '$_user_id' },
                    type: { $first: '$type' },
                    from: { $first: '$from' },
                    to: { $first: '$to' },
                    days: { $first: '$days' },
                    reason: { $first: '$reason' },
                    comments: { $first: '$comments' },
                    leave_category: { $first: '$leave_category' },
                    leave_type: { $first: '$leave_type' },
                    _leave_reason_doc: { $first: '$_leave_reason_doc' },
                    payment_status: { $first: '$payment_status' },
                    session: { $first: '$session' },
                    createdAt: { $first: '$createdAt' },
                    updatedAt: { $first: '$updatedAt' },
                    __v: { $first: '$__v' },
                    user_data: { $push: '$user_data' },
                },
            },
        ];
        const doc = await base_control.get_aggregate(lms_review, aggre);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        let pending = [];
        let approved = [];
        let rejected = [];
        let cancelled = [];
        if (req.params.role == constant.REVIEWER) {
            pending = doc.data.filter((ele) => ele.review == 'pending' && ele.isActive == true);
            approved = doc.data.filter((ele) => ele.review == 'approved' && ele.isActive == true);
            rejected = doc.data.filter((ele) => ele.review == 'rejected' && ele.isActive == true);
            cancelled = doc.data.filter(
                (ele) =>
                    (ele.review == 'pending' ||
                        ele.review == 'approved' ||
                        ele.review == 'rejected') &&
                    ele.isActive == false,
            );
            for (let i = 0; i < cancelled.length; i++) {
                rejected.push(cancelled[i]);
            }
        }
        if (req.params.role == constant.FORWARDER) {
            pending = doc.data.filter(
                (ele) =>
                    ele.forwarded == 'pending' && ele.review == 'approved' && ele.isActive == true,
            );
            approved = doc.data.filter(
                (ele) => ele.forwarded == 'approved' && ele.isActive == true,
            );
            rejected = doc.data.filter(
                (ele) => ele.forwarded == 'rejected' && ele.isActive == true,
            );
            cancelled = doc.data.filter(
                (ele) =>
                    (ele.forwarded == 'pending' ||
                        ele.forwarded == 'approved' ||
                        ele.forwarded == 'rejected') &&
                    ele.isActive == false,
            );
            for (let i = 0; i < cancelled.length; i++) {
                rejected.push(cancelled[i]);
            }
        }
        if (req.params.role == constant.APPROVER) {
            pending = doc.data.filter(
                (ele) =>
                    ele.approval == 'pending' &&
                    ele.forwarded == 'approved' &&
                    ele.isActive == true,
            );
            approved = doc.data.filter((ele) => ele.approval == 'approved' && ele.isActive == true);
            rejected = doc.data.filter((ele) => ele.approval == 'rejected' && ele.isActive == true);
            cancelled = doc.data.filter(
                (ele) =>
                    (ele.approval == 'pending' ||
                        ele.approval == 'approved' ||
                        ele.approval == 'rejected') &&
                    ele.isActive == false,
            );
            for (let i = 0; i < cancelled.length; i++) {
                rejected.push(cancelled[i]);
            }
        }
        const obj = [
            {
                pending,
                approved,
                rejected,
                //"cancelled": cancelled
            },
        ];
        doc.data = obj;
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('LEAVE_LIST'), doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};
exports.permission_overview = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const lms_check = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false },
            {},
        );
        const doc = [];
        if (lms_check.status) {
            const user_loc = lms_check.data.category.findIndex(
                (i) =>
                    i.category_to == req.params.user_type &&
                    i.category_type == constant.LEAVE_TYPE.PERMISSION,
            );
            if (user_loc != -1) doc.push(lms_check.data.category[user_loc].permission);
        }
        if (doc.length == 0)
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('PERMISSION_SETTINGS_NOT_FOUND'),
                        [],
                    ),
                );

        let query = {};
        if (doc[0].permission_frequency_by == 'month') {
            const date = new Date();
            const startDayCurrentMonth = new Date(date.getFullYear(), date.getMonth(), 1);
            const endDayCurrentMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);
            query = {
                _institution_calendar_id: ObjectId(req.params.inst_cal_id),
                type: constant.LEAVE_TYPE.PERMISSION,
                user_type: req.params.user_type,
                status: { $not: { $eq: 'rejected' } },
                _user_id: ObjectId(req.params.user_id),
                from: { $gt: startDayCurrentMonth },
                to: { $lte: endDayCurrentMonth },
                isDeleted: false,
                isActive: true,
            };
            //console.log(query)
        }
        if (doc[0].permission_frequency_by == 'year')
            query = {
                _institution_calendar_id: ObjectId(req.params.inst_cal_id),
                type: constant.LEAVE_TYPE.PERMISSION,
                user_type: req.params.user_type,
                _user_id: ObjectId(req.params.user_id),
                isDeleted: false,
                isActive: true,
            };
        const leave_details = await base_control.get_list(lms_review, query);
        // if (!leave_details.status) return res.status(200).send(common_files.response_function(res, 200, true, "Leave Details not found", []));
        // let permission_taken = 0;
        // for (let i = 0; i < leave_details.data.length; i++) {
        //     if (leave_details.data[i].permission_hour != undefined) {
        //         permission_taken += leave_details.data[i].permission_hour
        //     }
        // }
        obj = [
            {
                permission_taken: leave_details.status == true ? leave_details.data.length : 0,
                permission_hours: doc[0].permission_hours,
                permission_frequency: doc[0].permission_frequency,
                permission_frequency_by: doc[0].permission_frequency_by,
            },
        ];
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, 'Permission Overview', obj));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.insert_apply_leave = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                _institution_calendar_id,
                _user_id,
                user_type,
                type,
                from,
                to,
                reason,
                _leave_reason_doc,
                application_date,
                _leave_category_id,
                category_name,
                _person_id,
                approved_date,
                approved_comments,
                _leave_type_id,
                leave_type_name,
                notifyVia,
                schedules,
            },
        } = req;
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const lmsSetting = await get(lms, { _institution_id: ObjectId(_institution_id) }, {});
        if (!lmsSetting.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                        req.t('ERROR_UNABLE_TO_FIND_LEAVE_SETTING'),
                    ),
                );
        const dt1 = dateTimeLocalFormatter(parseInt(from));
        const dt2 = dateTimeLocalFormatter(parseInt(to));
        console.log('From To Dates ', dt1, ' ', dt2);
        const leaveQuery = {
            _institution_id: ObjectId(_institution_id),
            // _institution_calendar_id: ObjectId(_institution_calendar_id),
            user_type,
            _user_id: ObjectId(_user_id),
            status: { $not: { $eq: 'rejected' } },
            isActive: true,
            isDeleted: false,
        };
        const leave_check = await get_list(lms_review, leaveQuery, {});
        leave_check.data = leave_check.status ? leave_check.data : [];
        for (element of leave_check.data) {
            const from = dateTimeLocalFormatter(element.from);
            const to = dateTimeLocalFormatter(element.to);
            if ((dt1 <= from && dt2 > from) || (dt1 < to && dt2 >= to) || (dt2 > from && dt1 < to))
                return res
                    .status(409)
                    .send(
                        common_files.response_function(
                            res,
                            409,
                            false,
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                            req.t('DUPLICATE_ON_REPORT_ABSENCE_PERMISSION'),
                        ),
                    );
        }
        // const duplicateCheckStatus = leave_check.data.filter(
        //     (ele) =>
        //         ele && ((ele.from <= dt1 && ele.from >= dt2) || (ele.to >= dt1 && ele.to <= dt2)),
        // );
        // if (duplicateCheckStatus.length !== 0) {
        //     return res
        //         .status(410)
        //         .send(
        //             response_function(
        //                 res,
        //                 410,
        //                 false,
        //                 'Duplicate on Permission/Leave/On_Duty',
        //                 'Duplicate on Permission/Leave/On_Duty',
        //             ),
        //         );
        // }
        let type_data;
        let frequency = 0;
        let duration = 0;
        let calc_days = 0;
        let no_days = 0;
        const diff = dt2 - dt1;
        const diffInDays = diff / (1000 * 60 * 60 * 24);
        no_days = Math.round(diffInDays);
        console.log('No Of days', no_days);
        if (PERMISSION === type) {
            duration = diff_hours(dt1, dt2);
            type_data = lmsSetting.data.category.find(
                (i) => i.category_type == PERMISSION && i.category_to == user_type,
            );
            if (!type_data)
                return res
                    .status(409)
                    .send(
                        response_function(
                            res,
                            409,
                            false,
                            req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
                            req.t('ERROR_UNABLE_TO_FIND_PERMISSION_SETTING'),
                        ),
                    );
            const leaveData = leave_check.data.filter((ele) => ele.type === type);
            frequency = type_data.permission.permission_frequency;
            if (
                type_data.permission.permission_frequency_by === 'year' ||
                type_data.permission.permission_frequency_by === 'null'
            ) {
                if (leaveData.length >= frequency)
                    return res
                        .status(410)
                        .send(
                            response_function(
                                res,
                                410,
                                false,
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                            ),
                        );
            } else if (type_data.permission.permission_frequency_by === 'month') {
                let i = 0;
                for (element of leaveData) {
                    const from = dateTimeLocalFormatter(element.from);
                    if (from.getUTCMonth() === dt1.getUTCMonth()) i += element.days;
                }
                if (frequency <= i)
                    return res
                        .status(409)
                        .send(
                            common_files.response_function(
                                res,
                                409,
                                false,
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                                req.t('YOU_HAVE_CROSSED_YOUR_PERMISSION_FREQUENCY'),
                            ),
                        );
            }
        }
        //Check Date are in Academic Calendar
        const institution_calendar_data = await base_control.get(
            institution_calendar,
            { _id: ObjectId(req.body._institution_calendar_id) },
            {},
        );
        if (!institution_calendar_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER'),
                        req.t('ERROR_UNABLE_TO_FIND_INSTITUTION_CALENDER'),
                    ),
                );
        const ins_from = dateTimeLocalFormatter(institution_calendar_data.data.start_date);
        const ins_to = dateTimeLocalFormatter(institution_calendar_data.data.end_date);
        console.log('Ins calendar dates ', ins_from, ' ', ins_to);
        const from_dates = dateTimeLocalFormatter(dt1);
        from_dates.setUTCDate(from_dates.getUTCDate() + 1);
        if (!(ins_from < from_dates && ins_to > from_dates && ins_from < dt2 && ins_to > dt2))
            return res
                .status(409)
                .send(
                    common_files.response_function(
                        res,
                        409,
                        false,
                        req.t('DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER'),
                        req.t('DATES_ARE_AWAY_FROM_ACADEMIC_CALENDER'),
                    ),
                );

        let payment_status = constant.PAYMENT_STATUS.PAID;
        if (type === LEAVE || type === ONDUTY) {
            let ondutyCheck = false;
            const c_data = lmsSetting.data.category.find(
                (ele) => ele._id.toString() === _leave_category_id.toString(),
            );
            type_data = c_data.leave_type.find(
                (ele) => ele._id.toString() === _leave_type_id.toString(),
            );
            payment_status = type_data.payment;
            const weekendConsideration =
                type_data.weekend_consideration === undefined
                    ? false
                    : type_data.weekend_consideration;
            // const dayData = await dayCalculation(dt1, dt2);
            // calc_days = weekendConsideration ? dayData.withHolidays : dayData.withOutHolidays;
            const totalDays = await getTotalDays({
                startDate: dt1,
                endDate: dt2,
                isWeekendConsider: weekendConsideration,
                leaveType: type,
                _institution_id: ObjectId(_institution_id),
            });
            calc_days = totalDays;
            frequency = type_data.no_of_days;
            console.log({ dt1, dt2, type_data, frequency, calc_days });
            const leaveData = leave_check.data.filter(
                (ele) =>
                    ele.type === type &&
                    ele.leave_type._leave_type_id &&
                    ele.leave_type._leave_type_id.toString() == _leave_type_id.toString(),
            );
            if (
                (dt1.getUTCMonth() === dt2.getUTCMonth() && frequency < calc_days) ||
                (dt1.getUTCMonth() !== dt2.getUTCMonth() && frequency * 2 < calc_days)
            )
                ondutyCheck = true;
            else if (type_data.entitlement === 'month') {
                let i = calc_days;
                if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
                    i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
                    ondutyCheck = frequency < i;
                    console.log({ frequency, i });
                } else {
                    const diffMonthDates = await diffMonthDateCalculation(
                        dt1,
                        dt2,
                        leaveData,
                        weekendConsideration,
                    );
                    ondutyCheck =
                        frequency < diffMonthDates.fromMonthDates ||
                        frequency < diffMonthDates.toMonthDates;
                    console.log({ frequency, diffMonthDates });
                }
            } else if (type_data.entitlement === 'year') {
                let totalDays = calc_days;
                for (element of leaveData) {
                    totalDays += element.days;
                }
                console.log('Check ', totalDays, frequency < totalDays, frequency > totalDays);
                if (type_data.per_month === 0 || frequency < totalDays) {
                    ondutyCheck = frequency < totalDays;
                } else {
                    if (dt1.getUTCMonth() === dt2.getUTCMonth()) {
                        let i = calc_days;
                        i += await noOfDayCalculation(dt1, dt2, leaveData, weekendConsideration);
                        ondutyCheck = type_data.per_month < i || frequency < i;
                        console.log({ perMonth: type_data.per_month, frequency, i });
                    } else {
                        const diffMonthDates = await diffMonthDateCalculation(
                            dt1,
                            dt2,
                            leaveData,
                            weekendConsideration,
                        );
                        ondutyCheck =
                            frequency < diffMonthDates.fromMonthDates ||
                            frequency < diffMonthDates.toMonthDates ||
                            (type_data.per_month < diffMonthDates.fromMonthDates &&
                                type_data.per_month < diffMonthDates.toMonthDates);
                        console.log({ perMonth: type_data.per_month, frequency, diffMonthDates });
                    }
                }
            }
            if (ondutyCheck)
                return res
                    .status(410)
                    .send(
                        response_function(
                            res,
                            410,
                            false,
                            req.t('YOU_HAVE_CROSSED_YOUR') +
                                type +
                                req.t('MONTH_FREQUENCY_YEAR_FREQUENCY'),
                            req.t('YOU_HAVE_CROSSED_YOUR') +
                                type +
                                req.t('MONTH_FREQUENCY_YEAR_FREQUENCY'),
                        ),
                    );
        }
        // Check is schedule is there, if there update it in course schedule
        if (constant.LEAVE_TYPE.PERMISSION === req.body.type && schedules && schedules.length) {
            const scheduleIds = schedules.map((ele) => ele.schedule_id.toString());
            // Course Schedule Datas
            const scheduleData = await get_list(
                course_schedule,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: scheduleIds,
                    // type: 'regular',
                    isActive: true,
                    isDeleted: false,
                },
                {
                    'session.session_type': 1,
                    subjects: 1,
                    _program_id: 1,
                    _course_id: 1,
                    course_code: 1,
                    term: 1,
                    level_no: 1,
                    schedule_date: 1,
                    start: 1,
                    end: 1,
                    topic: 1,
                    substitute_staffs: 1,
                    staffs: 1,
                },
            );
            scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];

            const bulkUpdateObj = [];
            for (scheduleElement of schedules) {
                const scheduledElement = scheduleData.data.find(
                    (ele) => ele._id.toString() === scheduleElement.schedule_id.toString(),
                );
                if (scheduledElement) {
                    const substituteElement = scheduledElement.substitute_staffs.find(
                        (ele) => ele._assigned_to.toString() === req.body._user_id.toString(),
                    );
                    const pushObj = {
                        updateOne: {
                            filter: { _id: convertToMongoObjectId(scheduledElement._id) },
                        },
                    };
                    if (!substituteElement) {
                        if (
                            scheduleElement.substitute_staff &&
                            scheduleElement.substitute_staff.name &&
                            scheduleElement.substitute_staff._staff_id
                        )
                            pushObj.updateOne.update = {
                                $push: {
                                    substitute_staffs: {
                                        _assigned_to: req.body._user_id,
                                        _substitute_staff_id:
                                            scheduleElement.substitute_staff._staff_id,
                                        substitute_staff_name:
                                            scheduleElement.substitute_staff.name,
                                    },
                                },
                            };
                    } else {
                        if (
                            scheduleElement.substitute_staff &&
                            scheduleElement.substitute_staff.name &&
                            scheduleElement.substitute_staff._staff_id
                        ) {
                            pushObj.updateOne.update = {
                                $set: {
                                    'substitute_staffs.$[i]._substitute_staff_id':
                                        scheduleElement.substitute_staff._staff_id,
                                    'substitute_staffs.$[i].substitute_staff_name':
                                        scheduleElement.substitute_staff.name,
                                },
                            };
                            pushObj.updateOne.arrayFilters = [
                                {
                                    'i._assigned_to': convertToMongoObjectId(req.body._user_id),
                                },
                            ];
                        }
                    }
                    if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
                }
            }
            if (bulkUpdateObj.length) console.log(await bulk_write(course_schedule, bulkUpdateObj));
        }
        const objs = {
            _institution_id: req.headers._institution_id,
            _institution_calendar_id: req.body._institution_calendar_id,
            user_type: 'staff',
            _user_id: req.body._user_id,
            type: req.body.type,
            from: parseInt(req.body.from),
            to: parseInt(req.body.to),
            days: calc_days == 0 ? req.body.days : calc_days,
            permission_hour: duration,
            reason: req.body.reason,
            comments: req.body.comments,
            leave_category: {
                _leave_category_id: req.body._leave_category_id,
                name: req.body.category_name,
            },
            leave_type: {
                _leave_type_id: req.body._leave_type_id,
                name: req.body.leave_type_name,
            },
            _leave_reason_doc: req.body._leave_reason_doc,
            status: 'applied',
            status_time: common_functions.timestampNow(),
            payment_status,
            session: req.body.session,
        };
        // return res.send(objs);
        const doc = await base_control.insert(lms_review, objs);
        if (doc.status) {
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('LEAVE_APPLIED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('ERROR_UNABLE_TO_FIND'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.list_leave_user_type_and_leave_type = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const user_get = await base_control.get(
            user,
            { _id: ObjectId(req.params.user_id) },
            { _id: 1 },
        );
        if (!user_get.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('USER_NOT_FOUND'),
                        user_get.data,
                    ),
                );

        const user_role_assign = await base_control.get(
            role_assign,
            { _user_id: ObjectId(req.params.user_id) },
            {},
        );
        if (!user_role_assign.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('USER_ROLE_NOT_FOUND'),
                        user_role_assign.data,
                    ),
                );

        const role_ind = user_role_assign.data.roles.findIndex(
            (i) => i._role_id.toString() === req.params.role_id.toString(),
        );
        const role_id = user_role_assign.data.roles[role_ind]._role_id;
        const role_program = user_role_assign.data.roles[role_ind].program.map((i) =>
            ObjectId(i._program_id),
        );
        let role_department = [];
        if (user_role_assign.data.roles[role_ind].department.length != 0)
            role_department = user_role_assign.data.roles[role_ind].department.map((i) =>
                ObjectId(i._department_id),
            );
        const lms_setting = await base_control.get(
            lms,
            { _institution_id: ObjectId(req.headers._institution_id) },
            { leave_approval: 1 },
        );
        if (!lms_setting.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_LEAVE_SETTING_FOUND'),
                        req.t('NO_LEAVE_SETTING_FOUND'),
                    ),
                );

        if (!role_id)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_ROLE'),
                        req.t('UNABLE_TO_FIND_ROLE'),
                    ),
                );
        let approver_loc = -1;
        lms_setting.data.leave_approval.forEach((element, index) => {
            if (
                approver_loc == -1 &&
                ((element.role &&
                    element.role._roles_id &&
                    element.role._roles_id.toString() === role_id.toString()) ||
                    (element._staff_id &&
                        element._staff_id.toString() === req.params.user_id.toString()))
            )
                approver_loc = index;
        });
        if (approver_loc == -1)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_LEAVES_FOUND'),
                        req.t('NO_LEAVES_FOUND'),
                    ),
                );
        approver_level = lms_setting.data.leave_approval[approver_loc].level_no;
        const type_approvers = lms_setting.data.leave_approval.filter(
            (i) =>
                i.staff_type.toString() ==
                lms_setting.data.leave_approval[approver_loc].staff_type.toString(),
        );

        //Control Hierarchy level Staff Application
        const level_role_ids = [];
        const staff_levels = [];
        lms_setting.data.leave_approval.forEach((element) => {
            if (
                element.level_no >= approver_level &&
                element.staff_type.toString() ==
                    lms_setting.data.leave_approval[approver_loc].staff_type.toString()
            ) {
                if (element.role && element.role._roles_id)
                    level_role_ids.push(ObjectId(element.role._roles_id));
                else if (element._staff_id) staff_levels.push(ObjectId(element._staff_id));
            }
        });
        const lms_role_query = {
            $or: [
                { roles: { $elemMatch: { _role_id: { $in: level_role_ids } } } },
                { _user_id: { $in: staff_levels } },
            ],
        };
        const lms_role_data = await base_control.get_list(role_assign, lms_role_query, {
            _user_id: 1,
        });
        let upperUsers = lms_role_data.status
            ? lms_role_data.data.map((i) => ObjectId(i._user_id))
            : [];
        if (type_approvers.length === approver_level /*  && lms_role_data.status */)
            upperUsers = upperUsers.filter(
                (ele) => ele.toString() !== req.params.user_id.toString(),
            );
        const aggre = [];
        if (upperUsers.length != 0) aggre.push({ $match: { _user_id: { $nin: upperUsers } } });
        aggre.push(
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _institution_calendar_id: ObjectId(req.params.inst_cal_id),
                    isDeleted: false,
                    // status: { $not: { $eq: 'approve' } },
                    // isActive: true,
                },
            },
            { $match: { type: req.params.type } },
            {
                $lookup: {
                    from: 'users',
                    localField: '_user_id',
                    foreignField: '_id',
                    as: 'staff',
                },
            },
            { $unwind: { path: '$staff', preserveNullAndEmptyArrays: true } },
        );
        // if (user_get.data.staff_employment_type == 'administrator') {
        if (lms_setting.data.leave_approval[approver_loc].staff_type.toString() == 'admin') {
            aggre.push({ $match: { 'staff.staff_employment_type': 'administration' } });
        } else if (
            lms_setting.data.leave_approval[approver_loc].staff_type.toString() == 'academic' ||
            lms_setting.data.leave_approval[approver_loc].staff_type.toString() == 'both'
            // user_get.data.staff_employment_type == 'academic' ||
            // user_get.data.staff_employment_type == 'both'
        ) {
            //Need to filter Program Details based on Approver based
            aggre.push(
                {
                    $match: {
                        $or: [
                            { 'staff.staff_employment_type': 'both' },
                            { 'staff.staff_employment_type': 'academic' },
                        ],
                    },
                },
                {
                    $unwind: {
                        path: '$staff.academic_allocation',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                { $match: { 'staff.academic_allocation.allocation_type': 'primary' } },
                {
                    $lookup: {
                        from: constant.DIGI_PROGRAM,
                        localField: 'staff.academic_allocation._program_id',
                        foreignField: '_id',
                        as: 'staff.academic_allocation.program',
                    },
                },
                {
                    $unwind: {
                        path: '$staff.academic_allocation.program',
                        preserveNullAndEmptyArrays: true,
                    },
                },
                { $addFields: { 'staff.program': '$staff.academic_allocation.program.name' } },
            );
            if (role_program.length !== 0) {
                aggre.push({
                    $match: { 'staff.academic_allocation._program_id': { $in: role_program } },
                });
            }
            if (role_department.length != 0) {
                aggre.push({
                    $match: {
                        'staff.academic_allocation._department_id': { $in: role_department },
                    },
                });
            }
        }
        aggre.push(
            { $sort: { _id: -1 } },
            {
                $project: {
                    _id: 1,
                    _institution_calendar_id: 1,
                    isActive: 1,
                    _user_id: 1,
                    type: 1,
                    from: 1,
                    to: 1,
                    days: 1,
                    permission_hour: 1,
                    comments: 1,
                    leave_category: 1,
                    leave_type: 1,
                    status: 1,
                    reason: 1,
                    status_time: 1,
                    payment_status: 1,
                    session: 1,
                    level_approvers: 1,
                    _leave_reason_doc: 1,
                    'staff.name': 1,
                    'staff.user_id': 1,
                    'staff.gender': 1,
                    'staff.role': 1,
                    'staff.staff_employment_type': 1,
                    'staff.academic_allocation': 1,
                    'staff.program': 1,
                    createdAt: 1,
                },
            },
        );
        // return res.send(aggre);
        const doc = await base_control.get_aggregate(lms_review, aggre);
        if (!doc.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('SERVER_ERROR'),
                        doc.data,
                    ),
                );
        for (docElement of doc.data) {
            if (
                docElement._leave_reason_doc &&
                docElement._leave_reason_doc.length &&
                docElement._leave_reason_doc !== null &&
                docElement._leave_reason_doc !== 'null'
            ) {
                docElement._leave_reason_doc_url = await common_functions.getSignedURL(
                    docElement._leave_reason_doc,
                );
            }
        }
        const pending = [];
        const reviewed = [];
        const rejected = [];
        // const cancelled = [];
        let role = 'reviewer';
        if (approver_level == 1) {
            doc.data.forEach((element) => {
                if (element.status == 'Cancelled') {
                    rejected.push(element);
                } else if (element.level_approvers.length == 0) {
                    pending.push(element);
                } else {
                    const level_ind = element.level_approvers.findIndex(
                        (i) => i.level_no == approver_level,
                    );
                    if (
                        level_ind !== -1 &&
                        element.level_approvers[level_ind].status == 'approve' &&
                        element.isActive === true
                    ) {
                        reviewed.push(element);
                    } else {
                        rejected.push(element);
                    }
                }
            });
        } else {
            // console.log(approver_level - 1);
            let upper_users = [];
            if (approver_level - 1 != -1) {
                const previous_level = lms_setting.data.leave_approval.findIndex(
                    (i) =>
                        i.staff_type.toString() ===
                            lms_setting.data.leave_approval[approver_loc].staff_type.toString() &&
                        i.level_no == approver_level - 1,
                );
                // console.log(previous_level);
                // console.log(lms_setting.data.leave_approval[previous_level]);
                if (
                    lms_setting.data.leave_approval[previous_level].approver_association !==
                    'Others'
                ) {
                    if (
                        previous_level != -1 &&
                        lms_setting.data.leave_approval[previous_level].role &&
                        lms_setting.data.leave_approval[previous_level].role._roles_id
                    ) {
                        const lms_role_query = {
                            roles: {
                                $elemMatch: {
                                    _role_id: ObjectId(
                                        lms_setting.data.leave_approval[previous_level].role
                                            ._roles_id,
                                    ),
                                },
                            },
                        };
                        const lms_role_datas = await base_control.get_list(
                            role_assign,
                            lms_role_query,
                            {
                                _user_id: 1,
                            },
                        );
                        if (lms_role_datas.status) {
                            upper_users = lms_role_datas.data.map((i) => ObjectId(i._user_id));
                        }
                    }
                } else {
                    if (
                        previous_level != -1 &&
                        lms_setting.data.leave_approval[previous_level]._staff_id
                    ) {
                        upper_users.push(
                            convertToMongoObjectId(
                                lms_setting.data.leave_approval[previous_level]._staff_id,
                            ),
                        );
                    }
                }
            }
            doc.data.forEach((element) => {
                const pre_level_ind = element.level_approvers.findIndex(
                    (i) => i.level_no == approver_level - 1,
                );
                if (pre_level_ind != -1) {
                    if (element.level_approvers[pre_level_ind].status == 'approve') {
                        const level_ind = element.level_approvers.findIndex(
                            (i) => i.level_no == approver_level,
                        );
                        if (level_ind != -1) {
                            if (
                                element.level_approvers[level_ind].status == 'approve' &&
                                element.isActive === true
                            ) {
                                reviewed.push(element);
                            } else {
                                rejected.push(element);
                            }
                        } else if (element.status == 'Cancelled') {
                            rejected.push(element);
                        } else if (!element.status || element.status !== 'approve') {
                            pending.push(element);
                        }
                    }
                } else {
                    // console.log(
                    //     upper_users.length,
                    //     ' ',
                    //     upper_users.findIndex((i) => i.toString() == element._user_id.toString()),
                    // );
                    // console.log(
                    //     element._user_id.toString() === req.params.user_id.toString(),
                    //     ' ',
                    //     type_approvers.length === approver_level,
                    // );
                    if (
                        (upper_users.length != 0 &&
                            upper_users.findIndex(
                                (i) => i.toString() == element._user_id.toString(),
                            ) != -1) ||
                        (element._user_id.toString() === req.params.user_id.toString() &&
                            type_approvers.length === approver_level)
                    ) {
                        const level_ind = element.level_approvers.findIndex(
                            (i) => i.level_no == approver_level,
                        );
                        if (level_ind != -1) {
                            if (
                                element.level_approvers[level_ind].status == 'approve' &&
                                element.isActive === true
                            ) {
                                reviewed.push(element);
                            } else {
                                rejected.push(element);
                            }
                        } else if (element.status == 'Cancelled') {
                            rejected.push(element);
                        } else if (!element.status || element.status !== 'approve') {
                            pending.push(element);
                        }
                    }
                }
            });
        }
        role = type_approvers.length == approver_level ? 'approver' : 'reviewer';
        const data = [
            {
                pending,
                reviewed,
                rejected,
            },
        ];
        return res.status(200).send({
            status_code: 200,
            status: true,
            message: req.t('STAFF_LEAVE_LIST'),
            role,
            data,
        });
        // return res.status(200).send(common_files.response_function(res, 200, true, "Staff Leave List", data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('SERVER_ERROR'),
                    error.toString(),
                ),
            );
    }
};

exports.update_leave_flow_process = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                comment,
                _person_id,
                _role_id,
                leave_flow_type_status,
                schedules,
                notify_via,
                payment_status,
            },
            params: { lmsReviewId },
        } = req;
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        // return res.send(schedules);
        const { status: lmsReviewStatus, data: lmsReviewData } = await get(
            lms_review,
            { _id: convertToMongoObjectId(lmsReviewId) },
            {},
        );
        if (!lmsReviewStatus)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('LMS_REVIEW_DATA_NOT_FOUND'),
                        req.t('LMS_REVIEW_DATA_NOT_FOUND'),
                    ),
                );

        // Applicant & Approver User Data
        const userQuery = {
            // _id: {
            //     $in: [
            //         convertToMongoObjectId(lmsReviewData._user_id),
            //         convertToMongoObjectId(_person_id),
            //     ],
            // },
            user_type: STAFF,
            status: COMPLETED,
            isDeleted: false,
            isActive: true,
        };
        const { data: userData } = await get_list(user, userQuery, {
            user_id: 1,
            name: 1,
            email: 1,
            mobile: 1,
            academic_allocation: 1,
            staff_employment_type: 1,
        });
        const applicantData = userData.find(
            (ele) => ele._id.toString() === lmsReviewData._user_id.toString(),
        );
        let applicantProgram;
        let applicantProgramId;
        let applicantDepartmentId;
        if (
            applicantData.staff_employment_type === ACADEMIC ||
            applicantData.staff_employment_type === 'both'
        ) {
            applicantProgram = applicantData.academic_allocation.find(
                (ele2) => ele2.allocation_type === PRIMARY,
            );
            applicantProgramId = applicantProgram ? applicantProgram._program_id.toString() : '';
            applicantDepartmentId = applicantProgram
                ? applicantProgram._department_id.toString()
                : '';
        }
        const approverData = userData.find((ele) => ele._id.toString() === _person_id.toString());
        const { status: lmsSettingStatus, data: lmsSettingData } = await get(
            lms,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                isActive: true,
                isDeleted: false,
            },
            { leave_approval: 1, hr_contact: 1 },
        );
        if (!lmsSettingStatus)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('NO_LEAVE_SETTING_FOUND'),
                        req.t('NO_LEAVE_SETTING_FOUND'),
                    ),
                );
        // Need to check Staff Role is valid or Staff Id is an Approver
        const approverLevel = lmsSettingData.leave_approval.find(
            (ele) =>
                (ele &&
                    ele.role._roles_id &&
                    _role_id &&
                    ele.role._roles_id.toString() === _role_id.toString()) ||
                (ele && ele._staff_id && ele._staff_id.toString() === _person_id.toString()),
        );
        if (!approverLevel /*  || (approverLevel && approverLevel.level_no === -1) */)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('APPROVER_NOT_FOUND_CHECK_LOGGED_IN_ROLE'),
                        req.t('APPROVER_NOT_FOUND_CHECK_LOGGED_IN_ROLE'),
                    ),
                );
        const type_approvers = lmsSettingData.leave_approval.filter(
            (ele) => ele && ele.staff_type.toString() === approverLevel.staff_type.toString(),
        );
        const sortedApprovers = type_approvers.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.level_no) > parseInt(b.level_no)) {
                comparison = 1;
            } else if (parseInt(a.level_no) < parseInt(b.level_no)) {
                comparison = -1;
            }
            return comparison;
        });

        const finalApproverLevel = Math.max(...sortedApprovers.map((ele) => ele.level_no));
        const lmsStatus =
            finalApproverLevel === approverLevel.level_no
                ? leave_flow_type_status
                : 'Level ' + (approverLevel.level_no + 1) + ' Pending';

        if (leave_flow_type_status === REJECTED) {
            const reviewObject = {
                $push: {
                    level_approvers: {
                        _person_id: convertToMongoObjectId(_person_id),
                        level_no: parseInt(approverLevel.level_no),
                        comment,
                        status: leave_flow_type_status,
                        comment_time: common_functions.timestampNow(),
                    },
                },
                $set: {
                    status: leave_flow_type_status,
                    status_time: common_functions.timestampNow(),
                },
            };
            const mailBody =
                leave_flow_type_status === REJECTED
                    ? `${REJECTED}`
                    : `approved and Substitute staff's are assigned.`;
            await contentMailPush(
                lmsReviewData.type,
                applicantData,
                approverData,
                mailBody,
                comment,
                lmsReviewData.from,
                lmsReviewData.to,
            );
            await update_condition(
                lms_review,
                { _id: convertToMongoObjectId(lmsReviewId) },
                reviewObject,
            );
            // Need to remove substitute staff in schedule if there
            return res
                .status(200)
                .send(
                    response_function(
                        res,
                        200,
                        true,
                        req.t('UPDATED_SUCCESSFULLY'),
                        req.t('UPDATED_SUCCESSFULLY'),
                    ),
                );
        }
        // return res.send({ lmsStatus, approverLevel });
        // Notification Push
        const scheduleAlteredData = [];
        const mailedStaff = [];
        let roleData = {};
        let roleIds = {};
        let roleAssign = { data: [] };
        let staffsScheduleData = [];
        let mailBody;
        mailBody = `<b>${capitalize(lmsReviewData.type)} application summary-Approved</b><br><br>`;
        mailBody = mailBody.concat(`Staff name : ${nameFormatter(applicantData.name)}<br>`);
        mailBody = mailBody.concat(`Employee id : ${applicantData.user_id}<br>`);
        if (lmsReviewData.leave_category && lmsReviewData.leave_category.name)
            mailBody = mailBody.concat(`Category : ${lmsReviewData.leave_category.name}<br>`);
        if (lmsReviewData.leave_type && lmsReviewData.leave_type.name)
            mailBody = mailBody.concat(`Type : ${lmsReviewData.leave_type.name}<br>`);
        mailBody = mailBody.concat(`Reason : ${lmsReviewData.reason}<br>`);
        mailBody = mailBody.concat(
            `Date/Time : ${dateTimeLocalFormatInString(lmsReviewData.from)} `,
        );
        mailBody = mailBody.concat(
            lmsReviewData.to
                ? `and ${dateTimeLocalFormatInString(lmsReviewData.to)}<br>`
                : `<br><br>`,
        );
        if (
            leave_flow_type_status === 'approve' &&
            req.body.notify_via &&
            lmsStatus === 'approve' &&
            (applicantData.staff_employment_type === ACADEMIC ||
                applicantData.staff_employment_type === 'both')
        ) {
            // To Dean, Vice Dean, Department Chairman
            roleData = await base_control.get_list(
                role,
                {
                    name: { $nin: ['Super Admin', 'ADMIN'] },
                    $or: [
                        {
                            'modules.name': 'Calendar',
                            $and: [
                                { 'modules.pages.name': 'Institution Calendar' },
                                { 'modules.pages.actions.name': 'Create' },
                                { 'modules.pages.actions.name': 'Add Event' },
                                { 'modules.pages.actions.name': 'Publish' },
                            ],
                        },
                        {
                            'modules.name': 'Program Calendar',
                            $and: [
                                { 'modules.pages.name': 'Dashboard' },
                                { 'modules.pages.actions.name': 'Calendar Settings' },
                                { 'modules.pages.actions.name': 'Add Course' },
                                { 'modules.pages.actions.name': 'Review Calendar' },
                            ],
                        },
                    ],
                },
                { _id: 1 },
            );
            roleIds = roleData.status ? roleData.data.map((ele) => ele._id.toString()) : [];
            roleAssign = await base_control.get_list(
                role_assign,
                {
                    'roles.isAdmin': true,
                    'roles.program._program_id': convertToMongoObjectId(applicantProgramId),
                    $or: [
                        { 'roles._role_id': roleIds },
                        {
                            'roles.department._department_id':
                                convertToMongoObjectId(applicantDepartmentId),
                        },
                    ],
                },
                { _user_id: 1 },
            );
            staffsScheduleData = clone(scheduleAlteredData);
        }

        if (schedules && schedules.length) {
            const scheduleIds = schedules.map((ele) => ele.schedule_id.toString());
            // Course Schedule Datas
            const scheduleData = await get_list(
                course_schedule,
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _id: scheduleIds,
                    // type: 'regular',
                    isActive: true,
                    isDeleted: false,
                },
                {
                    // 'session.session_type': 1,
                    subjects: 1,
                    program_name: 1,
                    _program_id: 1,
                    _course_id: 1,
                    course_code: 1,
                    session: 1,
                    type: 1,
                    title: 1,
                    sub_type: 1,
                    merge_status: 1,
                    merge_with: 1,
                    merge_sessions: [],
                    term: 1,
                    level_no: 1,
                    schedule_date: 1,
                    start: 1,
                    end: 1,
                    topic: 1,
                    substitute_staffs: 1,
                    staffs: 1,
                },
            );
            scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
            // return res.send(scheduleData);
            const bulkUpdateObj = [];
            for (scheduleElement of schedules) {
                const scheduledElement = scheduleData.data.find(
                    (ele) => ele._id.toString() === scheduleElement.schedule_id.toString(),
                );
                if (scheduledElement) {
                    const scheduleStaff = scheduledElement.staffs;
                    if (scheduledElement.staffs && scheduledElement.staffs.length > 1) {
                        const availableStaffs = scheduleStaff.filter(
                            (ele) => ele.status === PENDING,
                        );
                        if (
                            scheduleElement.substitute_staff === undefined &&
                            availableStaffs.length < 2
                        )
                            return res
                                .status(410)
                                .send(
                                    common_files.response_function(
                                        res,
                                        410,
                                        true,
                                        `${scheduledElement.session.session_topic} is need Substitute Staff`,
                                        scheduledElement,
                                    ),
                                );
                    }

                    const substituteElement = scheduledElement.substitute_staffs.find(
                        (ele) => ele._assigned_to.toString() === lmsReviewData._user_id.toString(),
                    );
                    const pushObj = {
                        updateOne: {
                            filter: { _id: convertToMongoObjectId(scheduledElement._id) },
                        },
                    };
                    if (!substituteElement) {
                        if (
                            scheduleElement.substitute_staff &&
                            scheduleElement.substitute_staff.name &&
                            scheduleElement.substitute_staff._staff_id
                        )
                            pushObj.updateOne.update = {
                                $push: {
                                    substitute_staffs: {
                                        _assigned_to: lmsReviewData._user_id,
                                        _substitute_staff_id:
                                            scheduleElement.substitute_staff._staff_id,
                                        substitute_staff_name:
                                            scheduleElement.substitute_staff.name,
                                    },
                                },
                            };
                        if (
                            leave_flow_type_status === 'approve' &&
                            req.body.notify_via &&
                            lmsStatus === 'approve'
                        ) {
                            const staffLoc = scheduleStaff.findIndex(
                                (ele) =>
                                    ele._staff_id.toString() === lmsReviewData._user_id.toString(),
                            );
                            scheduleStaff[staffLoc].status = lmsReviewData.type;
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            ) {
                                scheduleStaff.push({
                                    staff_name: scheduleElement.substitute_staff.name,
                                    _staff_id: scheduleElement.substitute_staff._staff_id,
                                    status: PENDING,
                                });
                                pushObj.updateOne.update = {
                                    $set: {
                                        staffs: scheduleStaff,
                                    },
                                    $push: {
                                        substitute_staffs: {
                                            _assigned_to: lmsReviewData._user_id,
                                            _substitute_staff_id:
                                                scheduleElement.substitute_staff._staff_id,
                                            substitute_staff_name:
                                                scheduleElement.substitute_staff.name,
                                        },
                                    },
                                };
                            } else {
                                pushObj.updateOne.update = {
                                    $set: {
                                        staffs: scheduleStaff,
                                    },
                                };
                            }
                        }
                    } else {
                        if (
                            leave_flow_type_status === 'approve' &&
                            req.body.notify_via &&
                            lmsStatus === 'approve'
                        ) {
                            const staffLoc = scheduleStaff.findIndex(
                                (ele) =>
                                    ele._staff_id.toString() === lmsReviewData._user_id.toString(),
                            );
                            scheduleStaff[staffLoc].status = lmsReviewData.type;
                            if (
                                scheduleElement.substitute_staff &&
                                scheduleElement.substitute_staff.name &&
                                scheduleElement.substitute_staff._staff_id
                            )
                                scheduleStaff.push({
                                    staff_name: scheduleElement.substitute_staff.name,
                                    _staff_id: scheduleElement.substitute_staff._staff_id,
                                    status: PENDING,
                                });
                        }
                        let staffSet = {
                            staffs: scheduleStaff,
                        };
                        if (
                            scheduleElement.substitute_staff &&
                            scheduleElement.substitute_staff.name &&
                            scheduleElement.substitute_staff._staff_id
                        )
                            staffSet = {
                                'substitute_staffs.$[i]._substitute_staff_id':
                                    scheduleElement.substitute_staff._staff_id,
                                'substitute_staffs.$[i].substitute_staff_name':
                                    scheduleElement.substitute_staff.name,
                                staffs: scheduleStaff,
                            };
                        pushObj.updateOne.update = {
                            $set: staffSet,
                        };
                        pushObj.updateOne.arrayFilters = [
                            {
                                'i._assigned_to': convertToMongoObjectId(lmsReviewData._user_id),
                            },
                        ];
                    }
                    if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
                }
            }
            // return res.send({ schedules, bulkUpdateObj });
            if (bulkUpdateObj.length) console.log(await bulk_write(course_schedule, bulkUpdateObj));

            scheduleData.data = mergeSchedule(scheduleData.data);
            for (scheduleElement of scheduleData.data) {
                let title = '';
                let subTypes = [];
                if (scheduleElement.merge_status === false) {
                    switch (scheduleElement.type) {
                        case REGULAR:
                            title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no} - ${scheduleElement.session.session_topic}`;
                            subTypes.push(scheduleElement.session.session_type);
                            break;
                        case SUPPORT_SESSION:
                            title = scheduleElement.title;
                            subTypes.push(scheduleElement.sub_type);
                            break;
                        case EVENT:
                            title = scheduleElement.title;
                            subTypes.push(scheduleElement.sub_type);
                            break;
                        default:
                            break;
                    }
                } else {
                    title = `${scheduleElement.session.delivery_symbol}${scheduleElement.session.delivery_no}`;
                    subTypes.push(scheduleElement.session.session_type);
                    for (mergeSessionElement of scheduleElement.merge_sessions) {
                        title = title.concat(
                            `, ${mergeSessionElement.delivery_symbol}${mergeSessionElement.delivery_no}`,
                        );
                        subTypes.push(mergeSessionElement.session_type);
                    }
                }
                subTypes = [...new Set(subTypes)];
                scheduleElement.mailTitle = title;
                scheduleElement.subTypes = subTypes;
            }

            // Notification Push
            if (
                leave_flow_type_status === 'approve' &&
                req.body.notify_via &&
                lmsStatus === 'approve'
            ) {
                const scheduleCourseId = scheduleData.data.map((ele) => ele && ele._course_id);
                // Course Data Get
                const { data: courseData } = await get_list(
                    course,
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _id: scheduleCourseId,
                        isActive: true,
                        isDeleted: false,
                    },
                    {
                        coordinators: 1,
                    },
                );

                // Checking scheduleData In table Mail Push
                let substituteStaffIds = [];
                for (const [index, scheduleElement] of scheduleData.data.entries()) {
                    const scheduleChangesElement = schedules.find(
                        (ele) =>
                            ele.schedule_id.toString() === scheduleElement._id.toString() &&
                            ele.substitute_staff &&
                            ele.substitute_staff.name,
                    );
                    const scheduleAlteredObj = {
                        'S NO': index + 1,
                        'Session Type':
                            scheduleElement.type.charAt(0).toUpperCase() +
                            scheduleElement.type.slice(1),
                        Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                        From: scheduleTimeFormateChange(scheduleElement.start),
                        To: scheduleTimeFormateChange(scheduleElement.end),
                        Program: scheduleElement.program_name,
                        Course: scheduleElement.course_code,
                        course_id: scheduleElement._course_id,
                        term: scheduleElement.term,
                        Subject: scheduleElement.subjects.map((ele) => ele.subject_name).toString(),
                        Title: scheduleElement.mailTitle,
                        'Substitute Staff': '',
                    };
                    if (scheduleChangesElement) {
                        scheduleAlteredObj['Substitute Staff'] = nameFormatter(
                            scheduleChangesElement.substitute_staff.name,
                        );
                        scheduleAlteredObj.staff_id =
                            scheduleChangesElement.substitute_staff._staff_id;
                        substituteStaffIds.push(
                            scheduleChangesElement.substitute_staff._staff_id.toString(),
                        );
                    }
                    scheduleAlteredData.push(scheduleAlteredObj);
                    // scheduleAlteredData.push({
                    //     'S NO': index + 1,
                    //     'Session Type':
                    //         scheduleElement.type.charAt(0).toUpperCase() +
                    //         scheduleElement.type.slice(1),
                    //     Date: scheduleDateFormateChange(scheduleElement.schedule_date),
                    //     From: scheduleTimeFormateChange(scheduleElement.start),
                    //     To: scheduleTimeFormateChange(scheduleElement.end),
                    //     Program: scheduleElement.program_name,
                    //     Course: scheduleElement.course_code,
                    //     course_id: scheduleElement._course_id,
                    //     term: scheduleElement.term,
                    //     Subject: scheduleElement.subjects.map((ele) => ele.subject_name).toString(),
                    //     Title: scheduleElement.mailTitle,
                    //     // Topic: scheduleElement.topic ? scheduleElement.topic : '',
                    //     'Substitute Staff': nameFormatter(
                    //         scheduleChangesElement.substitute_staff.name,
                    //     ),
                    //     staff_id: scheduleChangesElement.substitute_staff._staff_id,
                    // });
                }
                substituteStaffIds = [...new Set(substituteStaffIds)];
                // Substitute Staff Notification Push
                for (substituteElement of substituteStaffIds) {
                    const substituteStaffsSchedule = clone(
                        scheduleAlteredData.filter(
                            (ele) =>
                                ele &&
                                ele.staff_id &&
                                ele.staff_id.toString() === substituteElement.toString(),
                        ),
                    );
                    substituteStaffsSchedule.forEach((ele) => {
                        delete ele.course_id;
                        delete ele.term;
                        delete ele['Substitute Staff'];
                        delete ele.staff_id;
                    });
                    const substituteStaffDetails = userData.find(
                        (ele) => ele._id.toString() === substituteElement.toString(),
                    );
                    const mailSubject = `Assigned as a Substitute staff`;
                    const mailBody =
                        substituteStaffsSchedule.length !== 0
                            ? `You have been assigned as a Substitute staff for the following sessions<br><br><b>Substitute for ${nameFormatter(
                                  applicantData.name,
                              )}</b><br>`
                            : `<br>`;
                    const smsContent = `You have been assigned as a Substitute staff; kindly check your mail to know more
            information about the sessions.`;
                    await contentScheduleMailPush(
                        substituteStaffDetails,
                        notify_via,
                        substituteStaffsSchedule,
                        mailSubject,
                        mailBody,
                        smsContent,
                        approverData,
                    );
                }

                staffsScheduleData = clone(scheduleAlteredData);
                // courseData
                for (courseElement of courseData) {
                    if (courseElement.coordinators && courseElement.coordinators.length) {
                        for (coordinatorsElement of courseElement.coordinators) {
                            const courseScheduleBasedTerm = clone(
                                scheduleAlteredData.filter(
                                    (ele) =>
                                        ele.course_id.toString() === courseElement._id.toString() &&
                                        ele.term === coordinatorsElement.term,
                                ),
                            );
                            if (courseScheduleBasedTerm.length !== 0) {
                                // const courseSchedule = clone(staffsScheduleData);
                                // courseSchedule.forEach((ele) => {
                                //     delete ele.course_id;
                                //     delete ele.term;
                                //     delete ele.staff_id;
                                // });
                                let subMailBody = mailBody.toString();
                                subMailBody = subMailBody.concat(
                                    `<br>List of Sessions along with Substitute staff<br>`,
                                );
                                courseScheduleBasedTerm.forEach((ele) => {
                                    delete ele.course_id;
                                    delete ele.term;
                                    delete ele.staff_id;
                                });
                                const substituteStaffDetails = userData.find(
                                    (ele) =>
                                        ele._id.toString() ===
                                        coordinatorsElement._user_id.toString(),
                                );
                                if (substituteStaffDetails) {
                                    mailedStaff.push(coordinatorsElement._user_id.toString());
                                    let subMailBody = mailBody.toString();
                                    subMailBody = subMailBody.concat(
                                        `<br>List of Sessions along with Substitute staff<br>`,
                                    );
                                    const mailSubject = `${capitalize(
                                        lmsReviewData.type,
                                    )} application summary of : ${nameFormatter(
                                        applicantData.name,
                                    )}`;
                                    const smsContent = `${nameFormatter(
                                        applicantData.name,
                                    )} ${capitalize(
                                        lmsReviewData.type,
                                    )} has been approved check your mail`;
                                    await contentScheduleMailPush(
                                        substituteStaffDetails,
                                        notify_via,
                                        courseScheduleBasedTerm,
                                        mailSubject,
                                        mailBody,
                                        smsContent,
                                        approverData,
                                    );
                                }
                            }
                        }
                    }
                }
            }
        }

        const sets =
            lmsStatus == leave_flow_type_status
                ? {
                      status: lmsStatus,
                      payment_status,
                      status_time: common_functions.timestampNow(),
                  }
                : {
                      status:
                          leave_flow_type_status == 'approve' ? lmsStatus : leave_flow_type_status,
                      status_time: common_functions.timestampNow(),
                  };
        const query = { _id: convertToMongoObjectId(lmsReviewId) };
        const obj = {
            $push: {
                level_approvers: {
                    _person_id: convertToMongoObjectId(_person_id),
                    level_no: parseInt(approverLevel.level_no),
                    comment,
                    status: leave_flow_type_status,
                    comment_time: common_functions.timestampNow(),
                },
            },
            $set: sets,
        };
        const doc = await base_control.update_condition(lms_review, query, obj);
        if (doc.status) {
            // Notification Push
            if (
                leave_flow_type_status === 'approve' &&
                req.body.notify_via &&
                lmsStatus === 'approve'
            ) {
                // Vice Dean & Department Chairman Mail Push & Also to Approvers
                if (
                    applicantData.staff_employment_type === ACADEMIC ||
                    applicantData.staff_employment_type === 'both'
                ) {
                    let subMailBody = mailBody.toString();
                    if (staffsScheduleData.length !== 0)
                        subMailBody = subMailBody.concat(
                            `<br>List of Sessions along with Substitute staff<br>`,
                        );
                    let userIdsToPush = [
                        ...lmsReviewData.level_approvers.map((ele) => ele._person_id.toString()),
                    ];
                    if (roleAssign.status)
                        userIdsToPush = [
                            ...userIdsToPush,
                            ...roleAssign.data.map((ele2) => ele2._user_id.toString()),
                        ];
                    userIdsToPush.push(_person_id.toString()); // Adding Final Approver for Mail Push
                    userIdsToPush = [...new Set(userIdsToPush)];
                    for (staffElement of userIdsToPush) {
                        staffsScheduleData.forEach((ele) => {
                            delete ele.course_id;
                            delete ele.term;
                            delete ele.staff_id;
                        });
                        const substituteStaffDetails = userData.find(
                            (ele) => ele._id.toString() === staffElement.toString(),
                        );
                        if (
                            substituteStaffDetails &&
                            !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                        ) {
                            mailedStaff.push(staffElement.toString());
                            const mailSubject = `${capitalize(
                                lmsReviewData.type,
                            )} application summary of : ${nameFormatter(applicantData.name)}`;
                            const smsContent = `${nameFormatter(applicantData.name)} ${capitalize(
                                lmsReviewData.type,
                            )} has been approved check your mail`;
                            await contentScheduleMailPush(
                                substituteStaffDetails,
                                notify_via,
                                staffsScheduleData,
                                mailSubject,
                                mailBody,
                                smsContent,
                                approverData,
                            );
                        }
                    }
                } else {
                    let userIdsToPush =
                        lmsReviewData.level_approvers && lmsReviewData.level_approvers.length !== 0
                            ? lmsReviewData.level_approvers.map(
                                  (ele) => ele && ele._person_id.toString(),
                              )
                            : [];
                    userIdsToPush.push(_person_id.toString()); // Adding Final Approver for Mail Push
                    userIdsToPush = [...new Set(userIdsToPush)];
                    for (staffElement of userIdsToPush) {
                        const substituteStaffDetails = userData.find(
                            (ele) => ele._id.toString() === staffElement.toString(),
                        );
                        if (
                            substituteStaffDetails &&
                            !mailedStaff.find((ele) => ele.toString() === staffElement.toString())
                        ) {
                            mailedStaff.push(staffElement.toString());
                            const mailSubject = `${capitalize(
                                lmsReviewData.type,
                            )} application summary of : ${nameFormatter(applicantData.name)}`;
                            const smsContent = `${nameFormatter(applicantData.name)} ${capitalize(
                                lmsReviewData.type,
                            )} has been approved check your mail`;
                            await contentScheduleMailPush(
                                substituteStaffDetails,
                                notify_via,
                                staffsScheduleData,
                                mailSubject,
                                mailBody,
                                smsContent,
                                approverData,
                            );
                        }
                    }
                }

                // Mail Push for Applicant
                const applicantMailSubject = `${capitalize(lmsReviewData.type)} application Status`;
                let applicantMailBody = `Your ${capitalize(
                    lmsReviewData.type,
                )} application ${dateTimeLocalFormatInString(lmsReviewData.from)}`;
                applicantMailBody = applicantMailBody.concat(
                    lmsReviewData.to
                        ? `and ${dateTimeLocalFormatInString(
                              lmsReviewData.to,
                          )} has been approved<br>`
                        : `<br><br>`,
                );
                if (staffsScheduleData.length)
                    applicantMailBody = applicantMailBody.concat(
                        `<br>List of Sessions along with Substitute staff<br>`,
                    );
                let applicantSmsContent = `Your ${capitalize(
                    lmsReviewData.type,
                )} application ${dateTimeLocalFormatInString(lmsReviewData.from)}`;
                applicantSmsContent = applicantSmsContent.concat(
                    lmsReviewData.to
                        ? `and ${dateTimeLocalFormatInString(lmsReviewData.to)}<br>`
                        : '',
                );
                applicantSmsContent = applicantSmsContent.concat(` has been approved.`);
                await contentScheduleMailPush(
                    applicantData,
                    notify_via,
                    staffsScheduleData,
                    applicantMailSubject,
                    applicantMailBody,
                    applicantSmsContent,
                    approverData,
                );

                // Mail Push to HR
                if (lmsSettingData.hr_contact) {
                    const hrDetails = {
                        name: { first: 'HR', last: ' ' },
                        email: lmsSettingData.hr_contact,
                    };
                    const mailSubject = `${capitalize(
                        lmsReviewData.type,
                    )} application summary of : ${nameFormatter(applicantData.name)}`;
                    const smsContent = `${nameFormatter(applicantData.name)} ${capitalize(
                        lmsReviewData.type,
                    )} has been approved check your mail`;
                    await contentScheduleMailPush(
                        hrDetails,
                        notify_via,
                        staffsScheduleData,
                        mailSubject,
                        mailBody,
                        smsContent,
                        approverData,
                    );
                }
            }
            return res
                .status(200)
                .send(
                    common_files.response_function(
                        res,
                        200,
                        true,
                        req.t('UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        }
        return res
            .status(404)
            .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};

exports.update_leave_flow_processOld = async (req, res) => {
    const {
        headers: { _institution_id },
        body: { comment, _person_id, leave_flow_type_status, schedules },
        params: { id },
    } = req;
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );

    const checkQuery = { _id: ObjectId(req.params.id) };
    const checkDoc = await base_control.get(lms_review, checkQuery);
    if (!checkDoc.status)
        res.status(404).send(
            common_files.response_function(res, 404, false, 'Data not found', 'Data not found'),
        );

    //User Data
    const query_u = { _id: ObjectId(checkDoc.data._user_id) };
    const user_data = await base_control.get(user, query_u, {});

    //Approver Data
    const query_a = { _id: ObjectId(req.body._person_id) };
    const approver_data = await base_control.get(user, query_a, {});

    const user_role_assign = await base_control.get(
        role_assign,
        { _user_id: ObjectId(req.body._person_id) },
        {},
    );
    if (!user_role_assign.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('USER_NOT_FOUND'),
                    user_get.data,
                ),
            );

    let role_id = null;
    user_role_assign.data.roles.forEach((element) => {
        if (element.isDefault) role_id = element._role_id;
    });
    const lms_setting = await base_control.get(
        lms,
        { _institution_id: ObjectId(req.headers._institution_id) },
        { leave_approval: 1 },
    );
    if (!lms_setting.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('NO_LEAVE_SETTING_FOUND'),
                    req.t('NO_LEAVE_SETTING_FOUND'),
                ),
            );
    // Need to check Staff Role is valid
    let approver_loc = -1;
    lms_setting.data.leave_approval.forEach((element, index) => {
        if (
            (element.role &&
                element.role._roles_id &&
                role_id &&
                element.role._roles_id.toString() == role_id.toString()) ||
            (element._staff_id && element._staff_id.toString() === req.body._person_id.toString())
        )
            approver_loc = index;
    });
    // let approver_loc = lms_setting.data.leave_approval.findIndex(i => (i.role._roles_id).toString() == (role_id).toString())
    if (approver_loc == -1)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    'No Leaves Found',
                    'No Leaves Found',
                ),
            );
    const approver_level = lms_setting.data.leave_approval[approver_loc].level_no;
    const type_approvers = lms_setting.data.leave_approval.filter(
        (i) =>
            i.staff_type.toString() ==
            lms_setting.data.leave_approval[approver_loc].staff_type.toString(),
    );
    // console.log(approver_level);
    // console.log(type_approvers);
    const sorted = type_approvers.sort((a, b) => {
        let comparison = 0;
        if (parseInt(a.level_no) > parseInt(b.level_no)) {
            comparison = 1;
        } else if (parseInt(a.level_no) < parseInt(b.level_no)) {
            comparison = -1;
        }
        return comparison;
    });
    const approver_current_loc =
        lms_setting.data.leave_approval[approver_loc].staff_type.toString() === 'academic'
            ? sorted.findIndex((i) => i.role._roles_id.toString() == role_id.toString())
            : sorted.findIndex(
                  (i) => i._staff_id && i._staff_id.toString() == req.body._person_id.toString(),
              );
    const status =
        sorted.length - 1 != approver_current_loc
            ? 'Level ' + (sorted[approver_current_loc].level_no + 1) + ' Pending'
            : req.body.leave_flow_type_status;
    if (schedules && schedules.length) {
        const scheduleIds = schedules.map((ele) => ele.schedule_id.toString());
        // Course Schedule Datas
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: scheduleIds,
                // type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                substitute_staffs: 1,
                staffs: 1,
            },
        );
        scheduleData.data = scheduleData.status ? clone(scheduleData.data) : [];
        const bulkUpdateObj = [];
        for (scheduleElement of schedules) {
            const scheduledElement = scheduleData.data.find(
                (ele) => ele._id.toString() === scheduleElement.schedule_id.toString(),
            );
            if (scheduledElement) {
                const substituteElement = scheduledElement.substitute_staffs.find(
                    (ele) => ele._assigned_to.toString() === checkDoc.data._user_id.toString(),
                );
                const pushObj = {
                    updateOne: {
                        filter: { _id: convertToMongoObjectId(scheduledElement._id) },
                    },
                };
                if (!substituteElement) {
                    if (
                        scheduleElement.substitute_staff &&
                        scheduleElement.substitute_staff.name &&
                        scheduleElement.substitute_staff._staff_id
                    )
                        pushObj.updateOne.update = {
                            $push: {
                                substitute_staffs: {
                                    _assigned_to: checkDoc.data._user_id,
                                    _substitute_staff_id:
                                        scheduleElement.substitute_staff._staff_id,
                                    substitute_staff_name: scheduleElement.substitute_staff.name,
                                },
                            },
                        };
                    if (
                        leave_flow_type_status === 'approve' &&
                        req.body.notify_via &&
                        status === 'approve'
                    )
                        pushObj.updateOne.update.$push.staffs = {
                            staff_name: scheduleElement.substitute_staff.name,
                            _staff_id: scheduleElement.substitute_staff._staff_id,
                        };
                } else {
                    const scheduleStaff = scheduledElement.staffs;
                    if (
                        leave_flow_type_status === 'approve' &&
                        req.body.notify_via &&
                        status === 'approve'
                    ) {
                        const staffLoc = scheduleStaff.findIndex(
                            (ele) => ele._staff_id.toString() === checkDoc.data._user_id.toString(),
                        );
                        scheduleStaff[staffLoc].status = checkDoc.data.type;
                        scheduleStaff.push({
                            staff_name: scheduleElement.substitute_staff.name,
                            _staff_id: scheduleElement.substitute_staff._staff_id,
                        });
                    }
                    if (
                        scheduleElement.substitute_staff &&
                        scheduleElement.substitute_staff.name &&
                        scheduleElement.substitute_staff._staff_id
                    ) {
                        pushObj.updateOne.update = {
                            $set: {
                                'substitute_staffs.$[i]._substitute_staff_id':
                                    scheduleElement.substitute_staff._staff_id,
                                'substitute_staffs.$[i].substitute_staff_name':
                                    scheduleElement.substitute_staff.name,
                                staffs: scheduleStaff,
                            },
                        };
                        pushObj.updateOne.arrayFilters = [
                            {
                                'i._assigned_to': convertToMongoObjectId(checkDoc.data._user_id),
                            },
                        ];
                    }
                }
                if (pushObj.updateOne.update) bulkUpdateObj.push(pushObj);
            }
        }
        if (bulkUpdateObj.length) console.log(await bulk_write(course_schedule, bulkUpdateObj));
    }
    const sets =
        status == req.body.leave_flow_type_status
            ? {
                  status,
                  payment_status: req.body.payment_status,
                  status_time: common_functions.timestampNow(),
              }
            : {
                  status:
                      req.body.leave_flow_type_status == 'approve'
                          ? status
                          : req.body.leave_flow_type_status,
                  status_time: common_functions.timestampNow(),
              };
    const query = { _id: ObjectId(req.params.id) };
    const obj = {
        $push: {
            level_approvers: {
                _person_id: ObjectId(req.body._person_id),
                level_no: parseInt(approver_level),
                comment: req.body.comment,
                status: req.body.leave_flow_type_status,
                comment_time: common_functions.timestampNow(),
            },
        },
        $set: sets,
    };
    const doc = await base_control.update_condition(lms_review, query, obj);
    if (doc.status) {
        if (req.body.leave_flow_type_status == 'approve' && req.body.notify_via) {
            const from = checkDoc.data.from;
            const to = checkDoc.data.to;
            // if (checkDoc.data.type == constant.LEAVE_TYPE.PERMISSION) {
            //     from = dateTimeFormatChange(checkDoc.data.from);
            //     to = dateTimeFormatChange(checkDoc.data.to);
            // } else {
            //     from = dateFormatChange(checkDoc.data.from);
            //     to = dateFormatChange(checkDoc.data.to);
            // }
            if (req.body.notify_via && req.body.notify_via.includes(constant.NOTIFY_VIA.MOBILE)) {
                const msg =
                    'Dear ' +
                    user_data.data.name.first +
                    ' ' +
                    user_data.data.name.last +
                    ', Your ' +
                    checkDoc.data.type +
                    ' application for ' +
                    from +
                    ' and ' +
                    to +
                    ' dates has been approved by approver ' +
                    approver_data.data.name.first +
                    ' ' +
                    approver_data.data.name.last;
                common_functions.send_sms(user_data.data.mobile, msg);
            }
            if (req.body.notify_via && req.body.notify_via.includes(constant.NOTIFY_VIA.EMAIL)) {
                const message =
                    'Dear ' +
                    user_data.data.name.first +
                    ' ' +
                    user_data.data.name.last +
                    ', \n Your ' +
                    checkDoc.data.type +
                    ' application for ' +
                    from +
                    ' and ' +
                    to +
                    ' dates has been approved by approver ' +
                    approver_data.data.name.first +
                    ' ' +
                    approver_data.data.name.last +
                    emailRegardsContent();
                const to_email = user_data.data.email;
                const subject = 'Your ' + checkDoc.data.leave_type.name + ' Status';
                common_functions.send_email(to_email, subject, message);
            }

            //Send Mail to Approvers
            const approver_ids = checkDoc.data.level_approvers.map((i) => ObjectId(i._person_id));
            approver_ids.push(ObjectId(req.body._person_id));

            const query_c = { _id: { $in: approver_ids } };
            const approvers_datas = await base_control.get_list(user, query_c, {
                name: 1,
                email: 1,
                mobile: 1,
            });
            // console.log(approvers_datas);
            approvers_datas.data.forEach((element) => {
                const name = element.name.middle
                    ? element.name.first + ' ' + element.name.middle + ' ' + element.name.last
                    : element.name.first + ' ' + element.name.last;
                const applicant_name = user_data.data.name.middle
                    ? user_data.data.name.first +
                      ' ' +
                      user_data.data.name.middle +
                      ' ' +
                      user_data.data.name.last
                    : user_data.data.name.first + ' ' + user_data.data.name.last;
                const approver_name = approver_data.data.name.middle
                    ? approver_data.data.name.first +
                      ' ' +
                      approver_data.data.name.middle +
                      ' ' +
                      approver_data.data.name.last
                    : approver_data.data.name.first + ' ' + approver_data.data.name.last;
                if (
                    req.body.notify_via &&
                    req.body.notify_via.includes(constant.NOTIFY_VIA.MOBILE)
                ) {
                    const msg =
                        'Dear ' +
                        name +
                        ', ' +
                        applicant_name +
                        ' - ' +
                        checkDoc.data.type +
                        ' application for ' +
                        from +
                        ' and ' +
                        to +
                        ' dates has been approved by Approver ' +
                        approver_name;
                    if (element.mobile) common_functions.send_sms(element.mobile, msg);
                }
                if (
                    req.body.notify_via &&
                    req.body.notify_via.includes(constant.NOTIFY_VIA.EMAIL)
                ) {
                    const message =
                        'Dear ' +
                        name +
                        '<br>' +
                        applicant_name +
                        ' - ' +
                        checkDoc.data.type +
                        ' application for ' +
                        from +
                        ' and ' +
                        to +
                        ' dates has been approved by Approver ' +
                        approver_name +
                        emailRegardsContent();
                    const to_email = element.email;
                    const subject = 'Staff ' + checkDoc.data.type + ' Status';
                    common_functions.send_email(to_email, subject, message);
                }
            });
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    }
    return res
        .status(404)
        .send(common_files.response_function(res, 404, false, req.t('SERVER_ERROR'), doc.data));
};

exports.list_student_leaves = async (req, res) => {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );

    const query = {
        user_type: 'student',
        _institution_calendar_id: ObjectId(req.params._institution_calendar_id),
        _program_id: req.query.id ? ObjectId(req.query.id) : undefined,
        isDeleted: false,
    };
    const populate = { path: '_user_id', select: { name: 1, user_id: 1 } };
    const doc = await lms_review.find(query, {}).populate(populate).sort({ _id: -1 });
    // console.log(doc);
    // const doc = await base_control.get_list_populate(lms_review, query, {}, populate);
    // let doc = await base_control.get_list(lms_review, query, {});
    if (doc.length) {
        return res
            .status(200)
            .send(common_files.response_function(res, 200, true, req.t('APPLIED_LEAVE_LIST'), doc));
    }
    return res
        .status(404)
        .send(common_files.response_function(res, 404, false, req.t('NO_DATA_FOUND'), doc));
};
exports.getStudentListByProgramId = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { inst_cal_id, programId },
        } = req;
        //const skips = Number(req.query.limit * (req.query.pageNo - 1));
        const pageNo = req.query.pageNo ? Number(req.query.pageNo) : 0;
        const limits = Number(req.query.limit) || 0;

        console.time('studentGroupData');
        const studentGroupData = await base_control.get_list(
            student_group,
            {
                // _institution_id: ObjectId(_institution_id),
                _institution_calendar_id: ObjectId(inst_cal_id),
                'master._program_id': ObjectId(programId),
                isDeleted: false,
                isActive: true,
            },
            {
                _id: 1,
                // _institution_id: 1,
                _institution_calendar_id: 1,
                master: 1,
                'groups.courses.setting.session_setting.groups._student_ids': 1,
            },
        );
        console.timeEnd('studentGroupData');
        if (!studentGroupData.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        let studentIds = [];
        for (studentGroupElement of studentGroupData.data) {
            for (groupElement of studentGroupElement.groups) {
                for (courseElement of groupElement.courses) {
                    for (settingElement of courseElement.setting) {
                        for (sessionSettingElement of settingElement.session_setting) {
                            for (groupSettingElement of sessionSettingElement.groups) {
                                if (groupSettingElement._student_ids.length)
                                    studentIds = [
                                        ...studentIds,
                                        ...groupSettingElement._student_ids.map((studentId) =>
                                            studentId.toString(),
                                        ),
                                    ];
                            }
                        }
                    }
                }
            }
        }
        studentIds = [...new Set(studentIds)];
        const { data, total_pages } = common_files.paginator(studentIds, pageNo, limits);

        // Search
        const agg = [
            {
                $match: {
                    _id: {
                        $in:
                            req.query.search && req.query.search.length !== 0
                                ? studentIds.map((studentId) => ObjectId(studentId))
                                : data.map((studentId) => ObjectId(studentId)),
                    },
                },
            },
        ];
        if (req.query.search && req.query.search.length !== 0) {
            const regex = new RegExp(req.query.search, 'i');
            agg.push({
                $match: {
                    $or: [
                        { 'name.first': regex },
                        { 'name.middle': regex },
                        { 'name.last': regex },
                        { email: regex },
                        { user_id: regex },
                        { batch: regex },
                        { 'program.program_no': regex },
                        { 'department.department_title': regex },
                        { 'subject.title': regex },
                        { 'employment.user_type': regex },
                        { 'employment.user_employment_type': regex },
                        { 'program.name': regex },
                        { gender: regex },
                        { mobile: regex },
                        { 'address.nationality_id': regex },
                        { 'student_program.name': regex },
                    ],
                },
            });
        }
        agg.push({
            $project: {
                _id: 1,
                name: 1,
                user_type: 1,
                user_id: 1,
                gender: 1,
                email: 1,
                role: 1,
            },
        });
        console.time('userData');
        const userData = await base_control.get_aggregate(user, agg);
        if (!userData.status) userData.data = [];
        console.timeEnd('userData');

        if (limits) {
            return res
                .status(200)
                .send(
                    common_files.list_all_response_function(
                        res,
                        200,
                        true,
                        req.t('STUDENT_LIST'),
                        studentIds.length,
                        total_pages,
                        pageNo,
                        userData.data,
                    ),
                );
        }
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('STUDENT_LIST'), studentsArr),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('SERVER_ERROR'), error.toString()));
    }
};
