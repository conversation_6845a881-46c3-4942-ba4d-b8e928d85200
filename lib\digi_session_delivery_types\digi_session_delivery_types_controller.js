const constant = require('../utility/constants');
// const institution = require('mongoose').model(constant.DIGI_INSTITUTE);
const institution = require('mongoose').model(constant.INSTITUTION);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const digi_session_delivery_types = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const digi_course = require('mongoose').model(constant.DIGI_COURSE);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const { allSessionDeliveryTypesDatas, clearItem } = require('../../service/cache.service');

// Updating Session Types Flat Caching Data
const updateSessionTypeFlatCacheData = async () => {
    clearItem('allSessionDeliveryTypes');
    await allSessionDeliveryTypesDatas();
};

async function insert(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        // check duplicate
        const session_name = [
            req.body.session_name,
            req.body.session_name.charAt(0).toUpperCase() + req.body.session_name.slice(1),
            req.body.session_name.toUpperCase(),
            req.body.session_name.toLowerCase(),
        ];
        const session_symbol = [
            req.body.session_symbol,
            req.body.session_symbol.toUpperCase(),
            req.body.session_symbol.toLowerCase(),
        ];
        const course_data = await base_control.get(
            digi_session_delivery_types,
            {
                session_name: { $in: session_name },
                session_symbol: { $in: session_symbol },
                _institution_id: ObjectId(req.headers._institution_id),
                program_id: ObjectId(req.body.program_id),
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (course_data.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        true,
                        req.t('DUPLICATE_SESSION_NAME_SESSION_SYMBOL'),
                        req.t('DUPLICATE_SESSION_NAME_SESSION_SYMBOL'),
                    ),
                );

        const obj = {
            _institution_id: req.headers._institution_id,
            _program_id: req.body.program_id,
            program_name: req.body.program_name,
            session_name: req.body.session_name,
            session_symbol: req.body.session_symbol,
            contact_hour_per_credit_hour: req.body.contact_hour_per_credit_hour,
            duration_per_contact_hour: req.body.duration_per_contact_hour,
            session_duration: req.body.session_duration,
        };
        // console.log(req.body)
        const doc = await base_control.insert(digi_session_delivery_types, obj);
        updateSessionTypeFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('SESSION_TYPE_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ADD_SESSION_TYPES'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function update_session_type(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        // check duplicate
        const session_name = [
            req.body.session_name,
            req.body.session_name.charAt(0).toUpperCase() + req.body.session_name.slice(1),
            req.body.session_name.toUpperCase(),
            req.body.session_name.toLowerCase(),
        ];
        const session_symbol = [
            req.body.session_symbol,
            req.body.session_symbol.toUpperCase(),
            req.body.session_symbol.toLowerCase(),
        ];
        const course_data = await base_control.get(
            digi_session_delivery_types,
            {
                session_name: { $in: session_name },
                session_symbol: { $in: session_symbol },
                _institution_id: ObjectId(req.headers._institution_id),
                program_id: ObjectId(req.body.program_id),
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (course_data.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        true,
                        req.t('DUPLICATE_SESSION_NAME_SESSION_SYMBOL'),
                        req.t('DUPLICATE_SESSION_NAME_SESSION_SYMBOL'),
                    ),
                );

        const obj = {
            session_name: req.body.session_name,
            session_symbol: req.body.session_symbol,
            contact_hour_per_credit_hour: req.body.contact_hour_per_credit_hour,
            duration_per_contact_hour: req.body.duration_per_contact_hour,
            session_duration: req.body.session_duration,
        };
        console.log(req.body.session_duration);
        const query = { _id: Object(req.params.id) };
        const doc = await base_control.update(digi_session_delivery_types, query, obj);
        updateSessionTypeFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('SESSION_TYPE_UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_UPDATE_DEPARTMENT'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        //let query = { _program_id: ObjectId(req.params.program_id) };
        const query = { _program_id: ObjectId(req.params.program_id), isDeleted: false };
        const doc = await base_control.get_list(digi_session_delivery_types, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));

        const delivery_types_arr = [];

        //    delivery_types_arr = doc.data[0].delivery_types.filter(ele => ele.isDeleted == false);

        for (let i = 0; i < doc.data.length; i++) {
            const delivery_type_list = doc.data[i].delivery_types.filter(
                (ele) => ele.isDeleted == false,
            );
            const obj = {
                _id: doc.data[i]._id,
                isActive: doc.data[i].isActive,
                isDeleted: doc.data[i].isDeleted,
                _program_id: doc.data[i]._program_id,
                program_name: doc.data[i].program_name,
                session_name: doc.data[i].session_name,
                session_symbol: doc.data[i].session_symbol,
                contact_hour_per_credit_hour: doc.data[i].contact_hour_per_credit_hour,
                duration_per_contact_hour: doc.data[i].duration_per_contact_hour,
                session_duration: doc.data[i].session_duration,
                delivery_types: delivery_type_list,
                createdAt: doc.data[i].createdAt,
                updatedAt: doc.data[i].updatedAt,
            };
            delivery_types_arr.push(obj);
        }
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('SESSION_TYPE_LIST'),
                    delivery_types_arr,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function delete_session_type(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Check is Department Subject is Associated with Any course
        const check_query = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            $or: [
                {
                    credit_hours: {
                        $elemMatch: { isDeleted: false, _session_id: ObjectId(req.params.id) },
                    },
                },
            ],
        };
        const check_data = await base_control.get_list(digi_course, check_query, { _id: 1 });
        if (check_data.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('THIS_SESSION_TYPE_ASSOCIATED_WITH_COURSES'),
                        req.t('THIS_SESSION_TYPE_ASSOCIATED_WITH_COURSES'),
                    ),
                );

        const query = { _id: ObjectId(req.params.id) };
        const obj = { isDeleted: true };
        const doc = await base_control.update_condition(digi_session_delivery_types, query, obj);
        updateSessionTypeFlatCacheData();
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SESSION_TYPE_DELETED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function insert_delivery_type(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query_session_type_data = {
            _id: ObjectId(req.params.session_type_id),
            isDeleted: false,
        };
        let doc = await base_control.get(digi_session_delivery_types, query_session_type_data, {});
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('SORRY_NO_SESSION_TYPE_DATA_SO_YOU_CAN_NOT_ADD_DELIVERY_TYPE'),
                        [],
                    ),
                );

        // check duplicate
        const delivery_name = [
            req.body.delivery_name,
            req.body.delivery_name.charAt(0).toUpperCase() + req.body.delivery_name.slice(1),
            req.body.delivery_name.toUpperCase(),
            req.body.delivery_name.toLowerCase(),
        ];
        const delivery_symbol = [
            req.body.delivery_symbol,
            req.body.delivery_symbol.toUpperCase(),
            req.body.delivery_symbol.toLowerCase(),
        ];
        const duplicate_check = await base_control.get(
            digi_session_delivery_types,
            {
                delivery_types: {
                    $elemMatch: {
                        delivery_name: { $in: delivery_name },
                        delivery_symbol: { $in: delivery_symbol },
                    },
                },
                // delivery_types: { $elemMatch: { delivery_symbol: { $in: delivery_symbol } } },
                _institution_id: ObjectId(req.headers._institution_id),
                program_id: ObjectId(req.body.program_id),
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (duplicate_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        true,
                        req.t('DUPLICATE_DELIVERY_TYPE_NAME_SYMBOL'),
                        req.t('DUPLICATE_DELIVERY_TYPE_NAME_SYMBOL'),
                    ),
                );

        const query = { _id: ObjectId(doc.data._id), isDeleted: false };
        const objs = {
            $push: {
                delivery_types: {
                    delivery_name: req.body.delivery_name.trim(),
                    delivery_symbol: req.body.delivery_symbol,
                    delivery_duration: req.body.delivery_duration,
                },
            },
        };
        doc = await base_control.update_condition(digi_session_delivery_types, query, objs);
        updateSessionTypeFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('DELIVERY_TYPE_ADDED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ADD_DELIVERY_TYPE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function update_delivery_type(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query_session_type_data = {
            _id: ObjectId(req.params.session_type_id),
            isDeleted: false,
        };
        const doc = await base_control.get(
            digi_session_delivery_types,
            query_session_type_data,
            {},
        );
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('SORRY_NO_SESSION_TYPE_DATA_SO_YOU_CAN_NOT_ADD_DELIVERY_TYPE'),
                        [],
                    ),
                );

        // check duplicate
        const delivery_name = [
            req.body.delivery_name,
            req.body.delivery_name.charAt(0).toUpperCase() + req.body.delivery_name.slice(1),
            req.body.delivery_name.toUpperCase(),
            req.body.delivery_name.toLowerCase(),
        ];
        const delivery_symbol = [
            req.body.delivery_symbol,
            req.body.delivery_symbol.toUpperCase(),
            req.body.delivery_symbol.toLowerCase(),
        ];
        const duplicate_check = await base_control.get(
            digi_session_delivery_types,
            {
                delivery_types: {
                    $elemMatch: {
                        _id: { $ne: ObjectId(req.params.delivery_type_id) },
                        delivery_name: { $in: delivery_name },
                        delivery_symbol: { $in: delivery_symbol },
                    },
                },
                // delivery_types: { $elemMatch: { delivery_name: { $in: delivery_name } } },
                // delivery_types: { $elemMatch: { delivery_symbol: { $in: delivery_symbol } } },
                _institution_id: ObjectId(req.headers._institution_id),
                program_id: ObjectId(req.body.program_id),
                isDeleted: false,
                isActive: true,
            },
            { _id: 1 },
        );
        if (duplicate_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        true,
                        req.t('DUPLICATE_DELIVERY_TYPE_NAME_SYMBOL'),
                        req.t('DUPLICATE_DELIVERY_TYPE_NAME_SYMBOL'),
                    ),
                );

        const query = { _id: ObjectId(doc.data._id), isDeleted: false };
        const objs = {
            $set: {
                'delivery_types.$[i].delivery_name': req.body.delivery_name.trim(),
                'delivery_types.$[i].delivery_symbol': req.body.delivery_symbol,
                'delivery_types.$[i].delivery_duration': req.body.delivery_duration,
            },
        };
        const filter = {
            arrayFilters: [
                {
                    'i._id': req.params.delivery_type_id,
                },
            ],
        };
        const doc_upd = await base_control.update_condition_array_filter(
            digi_session_delivery_types,
            query,
            objs,
            filter,
        );
        updateSessionTypeFlatCacheData();
        if (doc_upd.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('DELIVERY_TYPE_UPDATED_SUCCESSFULLY'),
                        req.t('DELIVERY_TYPE_UPDATED_SUCCESSFULLY'),
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_UPDATE_DELIVERY_TYPE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function delete_delivery_type(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const query_session_type_data = {
            _id: ObjectId(req.params.session_type_id),
            isDeleted: false,
        };
        const doc = await base_control.get(
            digi_session_delivery_types,
            query_session_type_data,
            {},
        );
        if (!doc.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('SORRY_NO_SESSION_TYPE_DATA_SO_YOU_CAN_NOT_ADD_DELIVERY_TYPE'),
                        [],
                    ),
                );

        //Check is Department Subject is Associated with Any course
        const check_query = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            $or: [
                // {
                //     credit_hours: {
                //         $elemMatch: { _session_id: ObjectId(req.params.session_type_id) },
                //     },
                // },
                {
                    credit_hours: {
                        $elemMatch: {
                            delivery_type: {
                                $elemMatch: { _delivery_id: ObjectId(req.params.delivery_type_id) },
                            },
                        },
                    },
                },
            ],
        };
        const check_data = await base_control.get_list(digi_course, check_query, { _id: 1 });
        if (check_data.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('THIS_DELIVERY_TYPE_ASSOCIATED_WITH_COURSES'),
                        req.t('THIS_DELIVERY_TYPE_ASSOCIATED_WITH_COURSES'),
                    ),
                );

        const query = { _id: ObjectId(doc.data._id), isDeleted: false };
        const objs = {
            $pull: { delivery_types: { _id: ObjectId(req.params.delivery_type_id) } },
        };
        const doc_upd = await base_control.update_condition(
            digi_session_delivery_types,
            query,
            objs,
        );
        updateSessionTypeFlatCacheData();
        if (doc_upd.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('DELIVERY_TYPE_DELETED_SUCCESSFULLY'),
                        req.t('DELIVERY_TYPE_DELETED_SUCCESSFULLY'),
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_DELETE_DELIVERY_TYPE'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function validate_data_check_session_type(
    req,
    res,
    data,
    collection_datas,
    duplicate_in_excel,
) {
    const message = [];
    const obj = {};
    const { program_data, session_delivery_types_data } = collection_datas;

    //Excel Duplicate
    if (duplicate_in_excel) message.push(req.t('DUPLICATE_IN_EXCEL'));

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) =>
            ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase() &&
            ele.code.toLowerCase() == data.Program_Code.trim().toLowerCase(),
    );
    if (program_name_ind == -1) message.push(req.t('CHECK_PROGRAM_NAME'));
    else {
        obj.program_id = program_data.data[program_name_ind]._id;
        obj.program_name = program_data.data[program_name_ind].name;
        /* obj.session_name = data.Session_Name;
        obj.session_symbol = data.Session_Symbol;
        obj.contact_hour_per_credit_hour = data.contact_hour_per_credit_hour;
        obj.session_duration = data.session_duration; */

        //Session Name Check
        if (data.Session_Name && data.Session_Symbol) {
            if (session_delivery_types_data.status) {
                const sess_del_type_data = session_delivery_types_data.data.filter(
                    (ele) =>
                        ele._program_id.toString() ==
                        program_data.data[program_name_ind]._id.toString(),
                );
                const session_type_ind = sess_del_type_data.findIndex(
                    (ele) =>
                        ele.session_name.trim().toLowerCase() ==
                            data.Session_Name.trim().toLowerCase() &&
                        ele.session_symbol.toLowerCase() ==
                            data.Session_Symbol.trim().toLowerCase(),
                );
                if (session_type_ind != -1)
                    message.push(req.t('CHECK_SESSION_NAME_AND_SESSION_SYMBOL_ALREADY_EXIST'));
            }
        } else message.push(req.t('SESSION_NAME_OR_SESSION_SYMBOL_IS_EMPTY'));

        //contact hours per credit hour Validation
        if (!Number.isInteger(data.Contact_Hours_per_Credit_Hour))
            message.push(req.t('CHECK_CONTACT_HOURS_PER_CREDIT_HOUR_SHOULD_BE_INTEGER'));

        //Session_Duration_Minutes Validation
        if (data.Session_Duration_Minutes != '') {
            if (!Number.isInteger(data.Session_Duration_Minutes))
                message.push(req.t('CHECK_SESSION_DURATION_SHOULD_BE_INTEGER'));
        }
    }
    return { message, data: obj, other_datas: {} }; //Message for data check api   //Remaining datas for Import api
}
const checkDuplicateSessionNameInExcel = function (delivery_type_excel_data, session_name) {
    const delivery_type_name_arr = delivery_type_excel_data.map((ele) =>
        ele.Session_Name.trim().toLowerCase(),
    );
    let flag = 0;
    const first_ind = delivery_type_name_arr.indexOf(session_name.trim().toLowerCase());
    const last_ind = delivery_type_name_arr.lastIndexOf(session_name.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const field_empty_validation = function (import_data, optional_field) {
    const validation_data = [];
    for (data of import_data) {
        const message = [];
        for (const key in data) {
            if (optional_field.includes(key)) continue;
            if (data[key].toString() == '') message.push(key + ' is required');
        }
        if (message.length > 0) validation_data.push({ data, message });
    }
    if (validation_data.length == 0) return { status: false, message: validation_data };
    return { status: true, message: validation_data };
};
async function data_check_session_type(req, res) {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Session Delivery Type List
        //let query_session_delivery_types = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
        const query_session_delivery_types = { isDeleted: false };
        const session_delivery_types_data = await base_control.get_list(
            digi_session_delivery_types,
            query_session_delivery_types,
            {},
        );
        // if (!session_delivery_types_data.status)
        //     return res
        //         .status(409)
        //         .send(
        //             common_files.response_function(
        //                 res,
        //                 409,
        //                 false,
        //                 'Session and delivery type not found',
        //                 [],
        //             ),
        //         );

        const collection_datas = { program_data, session_delivery_types_data };

        //Empty Validation
        const optional_field = ['Session_Duration_Minutes'];
        const empty_validation_check = field_empty_validation(
            req.body.session_type,
            optional_field,
        );
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.session_type) {
            const excel_dup_status = checkDuplicateSessionNameInExcel(
                req.body.session_type,
                data.Session_Name,
            );
            const session_type_duplicate_status = excel_dup_status == 1;
            if (session_type_duplicate_status) {
                //Duplicate in Excel
                validation = await validate_data_check_session_type(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check_session_type(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });

            //return res.send({ course_code_dup_status, course_name_dup_status })
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const local_data_check_session_type = async function (req, res) {
    const collections_error_message = [];
    //Program List
    const query_program = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const program_data = await base_control.get_list(program, query_program, {});
    if (!program_data.status)
        return res
            .status(409)
            .send(common_files.response_function(res, 409, false, req.t('PROGRAM_NOT_FOUND'), []));

    // Session Delivery Type List
    //let query_session_delivery_types = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
    const query_session_delivery_types = { isDeleted: false };
    const session_delivery_types_data = await base_control.get_list(
        digi_session_delivery_types,
        query_session_delivery_types,
        {},
    );
    // if (!session_delivery_types_data.status)
    //     return res
    //         .status(409)
    //         .send(
    //             common_files.response_function(
    //                 res,
    //                 409,
    //                 false,
    //                 'Session and delivery type not found',
    //                 [],
    //             ),
    //         );

    const collection_datas = { program_data, session_delivery_types_data };
    const valid_data = [];
    const invalid_data = [];
    const import_data = [];
    for (data of req.body.session_type) {
        const excel_dup_status = checkDuplicateSessionNameInExcel(
            req.body.session_type,
            data.Session_Name,
        );
        const session_type_duplicate_status = excel_dup_status == 1;
        if (session_type_duplicate_status) {
            //Duplicate in Excel
            validation = await validate_data_check_session_type(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = true),
            );
        } else {
            //No Duplicate in Excel
            validation = await validate_data_check_session_type(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = false),
            );
        }
        if (validation.message.length > 0) invalid_data.push({ data, message: validation.message });
        else valid_data.push({ data, message: validation.message });

        import_data.push(validation.data);
    }
    const d = {
        valid_data,
        invalid_data,
        other_error_message: collections_error_message,
        import_data,
        other_datas: {},
    };
    if (invalid_data.length > 0) return { status: false, data: d };
    return { status: true, data: d };
};
async function import_session_type(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const data_check = await local_data_check_session_type(req, res);
        //return res.send(data_check)
        if (!data_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                    ),
                );

        let obj = {};
        const session_type_arr = [];
        for (let i = 0; i < req.body.session_type.length; i++) {
            obj = {
                insertOne: {
                    document: {
                        _institution_id: ObjectId(req.headers._institution_id),
                        _program_id: data_check.data.import_data[i].program_id,
                        program_name: data_check.data.import_data[i].program_name,
                        session_name: req.body.session_type[i].Session_Name.trim(),
                        session_symbol: req.body.session_type[i].Session_Symbol.trim(),
                        contact_hour_per_credit_hour:
                            req.body.session_type[i].Contact_Hours_per_Credit_Hour,
                        session_duration: req.body.session_type[i].Session_Duration_Minutes,
                        duration_per_contact_hour:
                            req.body.session_type[i].Duration_Per_Contact_Hour,
                    },
                },
            };
            session_type_arr.push(obj);
        }
        //return res.send(session_type_arr)
        const doc = await base_control.bulk_write(digi_session_delivery_types, session_type_arr);
        updateSessionTypeFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('SESSION_TYPE_IMPORTED_SUCCESSFULLY'),
                        session_type_arr,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    410,
                    false,
                    req.t('UNABLE_TO_IMPORT_SESSION_TYPE'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function validate_data_check_delivery_type(
    req,
    res,
    data,
    collection_datas,
    duplicate_in_excel,
) {
    const message = [];
    const obj = {};
    const { program_data, session_delivery_types_data } = collection_datas;

    //Excel Duplicate Check
    if (duplicate_in_excel) message.push(req.t('DUPLICATE_IN_EXCEL'));

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) =>
            ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase() &&
            ele.code.toLowerCase() == data.Program_Code.trim().toLowerCase(),
    );
    if (program_name_ind == -1) message.push(req.t('CHECK_PROGRAM_NAME'));
    else {
        obj.program_id = program_data.data[program_name_ind]._id;
        obj.program_name = program_data.data[program_name_ind].name;

        //Session Name Check
        const sess_del_type_data = session_delivery_types_data.data.filter(
            (ele) =>
                ele._program_id.toString() == program_data.data[program_name_ind]._id.toString(),
        );
        const session_type_ind = sess_del_type_data.findIndex(
            (ele) => ele.session_name.toLowerCase() == data.Session_Name.trim().toLowerCase(),
        );
        if (session_type_ind == -1) message.push(req.t('SESSION_NAME_NOT_FOUND'));
        else {
            obj.session_id = sess_del_type_data[session_type_ind]._id;

            //Delivery Name check
            if (data.Delivery_Name != '' && data.Delivery_Symbol != '') {
                const sess_del_type_ind = sess_del_type_data[
                    session_type_ind
                ].delivery_types.findIndex(
                    (ele) =>
                        ele.delivery_name.toLowerCase() ==
                            data.Delivery_Name.trim().toLowerCase() &&
                        ele.delivery_symbol.toLowerCase() ==
                            data.Delivery_Symbol.trim().toLowerCase(),
                );
                if (sess_del_type_ind != -1) message.push(req.t('DELIVERY_NAME_ALREADY_EXIST'));
            } else message.push(req.t('DELIVERY_NAME_OR_DELIVERY_SYMBOL_IS_EMPTY'));
        }
    }
    //Delivery Duration Validation
    if (!Number.isInteger(data.Delivery_Duration_Minutes))
        message.push(req.t('CHECK_DELIVERY_DURATION_SHOULD_BE_INTEGER'));
    return { message, data: obj, other_datas: {} }; //Message for data check api   //Remaining datas for Import api
}
const checkDuplicateDeliveryNameInExcel = function (delivery_type_excel_data, delivery_name) {
    const delivery_type_name_arr = delivery_type_excel_data.map((ele) =>
        ele.Delivery_Name.trim().toLowerCase(),
    );
    let flag = 0;
    const first_ind = delivery_type_name_arr.indexOf(delivery_name.trim().toLowerCase());
    const last_ind = delivery_type_name_arr.lastIndexOf(delivery_name.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const checkDuplicateDeliverySymbolInExcel = function (delivery_type_excel_data, delivery_symbol) {
    const delivery_type_name_arr = delivery_type_excel_data.map((ele) =>
        ele.Delivery_Symbol.trim().toLowerCase(),
    );
    let flag = 0;
    const first_ind = delivery_type_name_arr.indexOf(delivery_symbol.trim().toLowerCase());
    const last_ind = delivery_type_name_arr.lastIndexOf(delivery_symbol.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
async function data_check_delivery_type(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Session Delivery Type List
        //let query_session_delivery_types = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
        const query_session_delivery_types = { isDeleted: false };
        const session_delivery_types_data = await base_control.get_list(
            digi_session_delivery_types,
            query_session_delivery_types,
            {},
        );
        if (!session_delivery_types_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'),
                        [],
                    ),
                );

        const collection_datas = { program_data, session_delivery_types_data };
        //Empty Validation
        const optional_field = [];
        const empty_validation_check = field_empty_validation(
            req.body.delivery_type,
            optional_field,
        );
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.delivery_type) {
            //Grouping by session_name
            const delivery_type_data = req.body.delivery_type.reduce((r, a) => {
                r[a.Session_Name] = [...(r[a.Session_Name] || []), a];
                return r;
            }, {});
            const excel_dup_status_delivery_name = checkDuplicateDeliveryNameInExcel(
                delivery_type_data[data.Session_Name],
                data.Delivery_Name,
            );
            const excel_dup_status_delivery_symbol = checkDuplicateDeliverySymbolInExcel(
                delivery_type_data[data.Session_Name],
                data.Delivery_Symbol,
            );

            if (excel_dup_status_delivery_name || excel_dup_status_delivery_symbol) {
                //Duplicate in Excel
                validation = await validate_data_check_delivery_type(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check_delivery_type(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });

            //return res.send({ course_code_dup_status, course_name_dup_status })
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const local_data_check_delivery_type = async function (req, res) {
    const collections_error_message = [];
    //Program List
    const query_program = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const program_data = await base_control.get_list(program, query_program, {});
    if (!program_data.status) collections_error_message.push(req.t('PROGRAM_NOT_FOUND'));

    // Session Delivery Type List
    //let query_session_delivery_types = { _institution_id: ObjectId(req.headers._institution_id), isDeleted: false }
    const query_session_delivery_types = { isDeleted: false };
    const session_delivery_types_data = await base_control.get_list(
        digi_session_delivery_types,
        query_session_delivery_types,
        {},
    );
    if (!session_delivery_types_data.status)
        collections_error_message.push(req.t('SESSION_AND_DELIVERY_TYPE_NOT_FOUND'));

    const collection_datas = { program_data, session_delivery_types_data };
    const valid_data = [];
    const invalid_data = [];
    const import_data = [];
    const delivery_type_data = [];
    for (data of req.body.delivery_type) {
        const excel_dup_status = checkDuplicateDeliveryNameInExcel(
            req.body.delivery_type,
            data.Delivery_Name,
        );
        const delivery_type_duplicate_status = excel_dup_status == 1;
        if (delivery_type_duplicate_status) {
            //Duplicate in Excel
            validation = await validate_data_check_delivery_type(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = true),
            );
        } else {
            //No Duplicate in Excel
            validation = await validate_data_check_delivery_type(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = false),
            );
        }
        if (validation.message.length > 0) invalid_data.push({ data, message: validation.message });
        else {
            valid_data.push({ data, message: validation.message });
            delivery_type_data.push({
                session_id: validation.data.session_id,
                Program_Name: data.Program_Name,
                Program_Code: data.Program_Code,
                Session_Name: data.Session_Name,
                Delivery_Name: data.Delivery_Name,
                Delivery_Symbol: data.Delivery_Symbol,
                Delivery_Duration_Minutes: data.Delivery_Duration_Minutes,
            });
        }

        import_data.push(validation.data);
    }
    const d = {
        valid_data,
        invalid_data,
        other_error_message: collections_error_message,
        import_data,
        other_datas: { delivery_type_data },
    };
    if (invalid_data.length > 0) return { status: false, data: d };
    return { status: true, data: d };
};
async function import_delivery_type(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const data_check = await local_data_check_delivery_type(req, res);
        //return res.send(data_check.data)
        if (!data_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        data_check.data,
                    ),
                );

        //Grouping by session_name
        const delivery_type_data = data_check.data.other_datas.delivery_type_data.reduce((r, a) => {
            r[a.Session_Name] = [...(r[a.Session_Name] || []), a];
            return r;
        }, {});

        //        return res.send(delivery_type_data)

        let obj = {};
        // let doc = '';
        const bulk = [];
        if (delivery_type_data.length === 0)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DELIVERY_TYPE_DATA_NOT_FOUND'),
                        req.t('DELIVERY_TYPE_DATA_NOT_FOUND'),
                    ),
                );
        for (const key in delivery_type_data) {
            if (Object.prototype.hasOwnProperty.call(delivery_type_data, key)) {
                const delivery_type_arr = [];
                let id = '';
                for (data of delivery_type_data[key]) {
                    obj = {
                        delivery_name: data.Delivery_Name.trim(),
                        delivery_symbol: data.Delivery_Symbol.trim(),
                        delivery_duration: data.Delivery_Duration_Minutes,
                    };
                    delivery_type_arr.push(obj);
                    id = data.session_id;
                }
                bulk.push({
                    updateOne: {
                        filter: {
                            _program_id: ObjectId(data_check.data.import_data[0].program_id),
                            _id: ObjectId(id),
                        },
                        update: {
                            $push: {
                                delivery_types: {
                                    $each: delivery_type_arr,
                                },
                            },
                        },
                    },
                });
            }
        }

        if (bulk.length > 0) await digi_session_delivery_types.bulkWrite(bulk);
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DELIVERY_TYPE_IMPORTED_SUCCESSFULLY'),
                    req.t('DELIVERY_TYPE_IMPORTED_SUCCESSFULLY'),
                ),
            );
        /*return res.status(404).send(common_files.response_function(res, 404, false, 'Unable to import delivery type', 'Unable to import delivery type')); */
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
}

async function delivery_list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await base_control.get_list(
            digi_session_delivery_types,
            { isDeleted: false },
            {},
        );
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));

        const delivery_types_arr = [];
        for (let i = 0; i < doc.data.length; i++) {
            const delivery_type_list = doc.data[i].delivery_types.filter(
                (ele) => ele.isDeleted == false,
            );
            const obj = {
                _id: doc.data[i]._id,
                isActive: doc.data[i].isActive,
                isDeleted: doc.data[i].isDeleted,
                _program_id: doc.data[i]._program_id,
                program_name: doc.data[i].program_name,
                session_name: doc.data[i].session_name,
                session_symbol: doc.data[i].session_symbol,
                contact_hour_per_credit_hour: doc.data[i].contact_hour_per_credit_hour,
                duration_per_contact_hour: doc.data[i].duration_per_contact_hour,
                session_duration: doc.data[i].session_duration,
                delivery_types: delivery_type_list,
                createdAt: doc.data[i].createdAt,
                updatedAt: doc.data[i].updatedAt,
            };
            delivery_types_arr.push(obj);
        }
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('SESSION_TYPE_LIST'),
                    delivery_types_arr,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function sessionTypeDeliveryCourseChange(req, res) {
    try {
        const sessionTypeData = await digi_session_delivery_types
            .find({ isDeleted: false }, {})
            .lean();
        const courseList = await digi_course
            .find({ isDeleted: false, isActive: true }, { credit_hours: 1 })
            .lean();
        const sessionDeliveryData = sessionTypeData
            .map((sessionTypeElement) => sessionTypeElement.delivery_types)
            .flat();
        const bulkWriteData = [];
        for (const courseElement of courseList) {
            const courseCredit = [];
            for (const creditElement of courseElement.credit_hours) {
                const courseDelivery = [];
                for (const deliveryElement of creditElement.delivery_type) {
                    if (deliveryElement.delivery_type.indexOf('/') !== -1) {
                        const deliveryFromSession = sessionDeliveryData.find(
                            (sessionDeliveryElement) =>
                                sessionDeliveryElement._id.toString() ===
                                deliveryElement._delivery_id.toString(),
                        );
                        if (deliveryFromSession) {
                            deliveryElement.delivery_type = deliveryFromSession.delivery_name;
                        }
                    }
                    if (deliveryElement.delivery_symbol.indexOf('/') !== -1) {
                        const deliveryFromSession = sessionDeliveryData.find(
                            (sessionDeliveryElement) =>
                                sessionDeliveryElement._id.toString() ===
                                deliveryElement._delivery_id.toString(),
                        );
                        if (deliveryFromSession) {
                            deliveryElement.delivery_symbol = deliveryFromSession.delivery_symbol;
                        }
                    }
                    deliveryElement.duration = creditElement.type_name === 'Practical' ? 100 : 50;
                    courseDelivery.push(deliveryElement);
                }
                creditElement.delivery_type = courseDelivery;
                creditElement.duration_per_contact_hour = 50;
                creditElement.duration = creditElement.type_name === 'Practical' ? 100 : 50;
                courseCredit.push(creditElement);
            }
            bulkWriteData.push({
                updateOne: {
                    filter: {
                        _id: common_files.convertToMongoObjectId(courseElement._id),
                    },
                    update: {
                        $set: {
                            credit_hours: courseCredit,
                        },
                    },
                },
            });
        }
        console.log(await digi_course.bulkWrite(bulkWriteData));

        return res.status(200).send(
            common_files.response_function(res, 200, true, req.t('SESSION_TYPE_LIST'), {
                bulkWriteData,
                sessionDeliveryData,
                // sessionTypeData,
                // courseList,
            }),
        );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

module.exports = {
    insert,
    list,
    delete_session_type,
    update_session_type,
    insert_delivery_type,
    update_delivery_type,
    delete_delivery_type,
    import_session_type,
    data_check_session_type,
    import_delivery_type,
    data_check_delivery_type,
    delivery_list,
    sessionTypeDeliveryCourseChange,
};
