/* eslint-disable no-useless-escape */
const Joi = require('joi');
const { comResponse } = require('../../utility/common');
const constants = require('../../utility/constants');
const constant = require('../../utility/constants');
const {
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
} = require('../../utility/enums');
const authLoginBodySchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                email: Joi.string()
                    .email({ minDomainSegments: 2, tlds: { allow: ['com', 'net'] } })
                    .required()
                    .error((error) => {
                        return error;
                    }),
                password: Joi.string()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                user_type: Joi.string()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                device_type: Joi.string()
                    .valid(constant.IOS, constant.ANDROID, constant.WEB)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(false),
    })
    .unknown(true);

const authLoggedInBodySchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                user_type: Joi.string()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                fcm_token: Joi.string().error((error) => {
                    return error;
                }),
                device_type: Joi.string()
                    .valid(constant.IOS, constant.ANDROID, constant.WEB)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(false),
    })
    .unknown(true);

const userIdValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'USER_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const updateUserValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'USER_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                user_type: Joi.string().error((error) => {
                    return error;
                }),
                employee_id: Joi.string()
                    .when('user_type', {
                        is: 'staff',
                        then: Joi.string().trim().alphanum().min(1),
                    })
                    .error((error) => {
                        return '_EMPLOYEE_ID_REQUIRED';
                    }),
                academicNo: Joi.string()
                    .when('user_type', {
                        is: 'student',
                        then: Joi.string().trim().alphanum().min(1),
                    })
                    .error((error) => {
                        return 'ACADEMIC_NO_ALPHA_REQUIRED';
                    }),
                firstName: Joi.string()
                    .trim()
                    .min(1)

                    .error((error) => {
                        return 'FIRST_NAME_REQUIRED';
                    }),
                middleName: Joi.string()
                    .allow('')
                    .trim()
                    .error((error) => {
                        return 'MIDDLE_NAME_REQUIRED';
                    }),
                lastName: Joi.string()
                    .trim()
                    .min(1)

                    .error((error) => {
                        return 'LAST_NAME_REQUIRED';
                    }),
                familyName: Joi.string()
                    .allow('')
                    .trim()
                    .error((error) => {
                        return 'FAMILY_NAME_REQUIRED';
                    }),
                email: Joi.string()
                    .trim()
                    .regex(
                        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                    )
                    .error(() => {
                        return 'EMAIL_IS_NOT_A_VALID_MAIL_ADDRESS_DO_VERIFY';
                    }),
                gender: Joi.string()
                    .trim()
                    .valid(
                        'MALE',
                        'FEMALE',
                        'Male',
                        'Female',
                        constant.GENDER.MALE,
                        constant.GENDER.FEMALE,
                    )

                    .error((error) => {
                        return error;
                    }),
                nationality_id: Joi.string()
                    .trim()
                    .min(5)
                    .max(25)
                    .error((error) => {
                        return 'NATIONALITY_ID_MIN_MAX_REQUIRED';
                    }),
                programNo: Joi.string()
                    .when('user_type', {
                        is: 'student',
                        then: Joi.string().trim().min(3).max(10),
                    })
                    .error((error) => {
                        return 'PROGRAM_NO_REQUIRED';
                    }),
                enrollmentYear: Joi.string()
                    .trim()
                    .error((error) => {
                        return 'ENROLLMENT_YEAR_REQUIRED';
                    }),
                batch: Joi.string()
                    .trim()
                    .error((error) => {
                        return 'BATCH_REQUIRED';
                    }),
                mobile: Joi.number()
                    .allow('')
                    .error(() => 'MOBILE_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);

const activeInActiveValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'USER_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                isActive: Joi.boolean()
                    .required()
                    .error(() => 'MOBILE_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);

const isDefaultValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            email: Joi.string()
                .required()
                .error(() => {
                    return 'EMAIL_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                isDefault: Joi.boolean()
                    .required()
                    .error(() => 'IS_DEFAULT_REQUIRED'),
            })
            .unknown(true),
    })
    .unknown(true);

const userImportSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                user_type: Joi.string()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                userData: Joi.array().items(
                    Joi.object().keys({
                        employee_id: Joi.string()
                            .when('user_type', {
                                is: 'staff',
                                then: Joi.string().trim().alphanum().min(1).required(),
                            })
                            .error((error) => {
                                return req.t('_EMPLOYEE_ID_REQUIRED');
                            }),
                        // academic_no: Joi.string()
                        //     .when('user_type', {
                        //         is: 'student',
                        //         then: Joi.string().trim().alphanum().min(1).required(),
                        //     })
                        //     .error((error) => {
                        //         return req.t('ACADEMIC_NO_ALPHA_REQUIRED');
                        //     }),
                        first_name: Joi.string()
                            .trim()
                            .min(1)
                            .error((error) => {
                                return req.t('FIRST_NAME_REQUIRED');
                            }),
                        middle_name: Joi.string()
                            .allow('')
                            .trim()
                            .error((error) => {
                                return req.t('MIDDLE_NAME_REQUIRED');
                            }),
                        last_name: Joi.string()
                            .trim()
                            .min(1)
                            .error((error) => {
                                return req.t('LAST_NAME_REQUIRED');
                            }),
                        family_name: Joi.string()
                            .allow('')
                            .trim()
                            .error((error) => {
                                return req.t('FAMILY_NAME_REQUIRED');
                            }),
                        email: Joi.string()
                            .trim()
                            .regex(
                                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
                            )
                            .error(() => {
                                return req.t('EMAIL_IS_NOT_A_VALID_MAIL_ADDRESS_DO_VERIFY');
                            }),
                        gender: Joi.string()
                            .trim()
                            .valid(
                                'MALE',
                                'FEMALE',
                                'Male',
                                'Female',
                                constant.GENDER.MALE,
                                constant.GENDER.FEMALE,
                                constant.GENDER.BOTH,
                            )
                            .error((error) => {
                                return error;
                            }),
                        nationality_id: Joi.string()
                            .trim()
                            .min(5)
                            .max(25)
                            .error((error) => {
                                return req.t('NATIONALITY_ID_MIN_MAX_REQUIRED');
                            }),
                        program_no: Joi.string()
                            .when('user_type', {
                                is: 'student',
                                then: Joi.string().trim().min(3).max(10).required(),
                            })
                            .error((error) => {
                                return req.t('PROGRAM_NO_REQUIRED');
                            }),
                        enrollment_year: Joi.string()
                            .trim()
                            .error((error) => {
                                return req.t('ENROLLMENT_YEAR_REQUIRED');
                            }),
                        batch: Joi.string()
                            .trim()
                            .error((error) => {
                                return req.t('BATCH_REQUIRED');
                            }),
                    }),
                ),
            })
            .unknown(false),
    })
    .unknown(true);
const userRegistrationSchema = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                user_type: Joi.string()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                setMode: Joi.string()
                    .valid(
                        'password',
                        'basicDetails',
                        'profileInformation',
                        'vaccineDetails',
                        'profileVaccineInformation',
                    )
                    .required()
                    .error((error) => {
                        return error;
                    }),
                newPassword: Joi.string().when('setMode', {
                    is: 'password',
                    then: Joi.required(),
                }),
                edited: Joi.boolean().error((error) => {
                    return error;
                }),
                isAdmin: Joi.boolean().error((error) => {
                    return error;
                }),
                forgotPassword: Joi.boolean().error((error) => {
                    return error;
                }),
                updateObject: Joi.alternatives()
                    .when('setMode', {
                        is: 'basicDetails',
                        then: Joi.object().keys({
                            user_id: Joi.string()
                                .allow('')
                                .error((error) => {
                                    return error;
                                }),
                            email: Joi.string()
                                .allow('')
                                .error((error) => {
                                    return error;
                                }),
                            gender: Joi.string()
                                .allow('')
                                .error((error) => {
                                    return error;
                                }),

                            code: Joi.string()
                                .allow('')
                                .error((error) => {
                                    return error;
                                }),
                            no: Joi.string()
                                .allow('')
                                .error((error) => {
                                    return error;
                                }),
                            firstName: Joi.string()
                                .allow('')
                                .error((error) => {
                                    return error;
                                }),
                            middleName: Joi.string()
                                .allow('')
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            lastName: Joi.string()
                                .allow('')
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            familyName: Joi.string()
                                .allow('')
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            program: Joi.string()
                                .allow('')
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            batch: Joi.string()
                                .allow('')
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            enrollmentYear: Joi.string()
                                .allow('')
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                        }),
                        otherwise: Joi.optional(),
                    })
                    .when('setMode', {
                        is: 'profileInformation',
                        then: Joi.object().keys({
                            passportNo: Joi.string().error((error) => {
                                return error;
                            }),
                            dob: Joi.string().error((error) => {
                                return error;
                            }),
                            nationalityId: Joi.string().error((error) => {
                                return error;
                            }),
                            _nationality_id: Joi.string().error((error) => {
                                return error;
                            }),

                            officeExtension: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            officeRoomNo: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            parentGuardianPhoneNo: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            parentGuardianEmailId: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            spouseGuardianPhoneNo: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            buildingStreetName: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            floorNo: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            country: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            district: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            countryId: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            districtId: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            city: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            zipCode: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                            unit: Joi.string()
                                .optional()
                                .error((error) => {
                                    return error;
                                }),
                        }),
                        otherwise: Joi.optional(),
                    })
                    .when('setMode', {
                        is: 'vaccineDetails',
                        then: Joi.array().items(
                            Joi.object().keys({
                                _category_id: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return error;
                                    }),
                                vaccineDetails: Joi.array().items(
                                    Joi.object().keys({
                                        _vaccine_id: Joi.string()
                                            .optional()
                                            .error((error) => {
                                                return error;
                                            }),
                                        universalNumber: Joi.string()
                                            .optional()
                                            .error((error) => {
                                                return error;
                                            }),
                                        vaccineCentre: Joi.string()
                                            .optional()
                                            .error((error) => {
                                                return error;
                                            }),
                                        country: Joi.string()
                                            .optional()
                                            .error((error) => {
                                                return error;
                                            }),
                                        city: Joi.string()
                                            .optional()
                                            .error((error) => {
                                                return error;
                                            }),
                                        dosageTaken: Joi.array(),
                                        boosterTaken: Joi.array(),
                                        doses: Joi.array().items(
                                            Joi.object().keys({
                                                name: Joi.string()
                                                    .optional()
                                                    .error((error) => {
                                                        return error;
                                                    }),
                                                date: Joi.string()
                                                    .optional()
                                                    .error((error) => {
                                                        return error;
                                                    }),
                                                batchNo: Joi.string()
                                                    .optional()
                                                    .error((error) => {
                                                        return error;
                                                    }),
                                            }),
                                        ),
                                        booster: Joi.array().items(
                                            Joi.object().keys({
                                                name: Joi.string()
                                                    .optional()
                                                    .error((error) => {
                                                        return error;
                                                    }),
                                                date: Joi.string()
                                                    .optional()
                                                    .error((error) => {
                                                        return error;
                                                    }),
                                                batchNo: Joi.string()
                                                    .optional()
                                                    .error((error) => {
                                                        return error;
                                                    }),
                                            }),
                                        ),
                                    }),
                                ),
                            }),
                        ),
                    })
                    .when('setMode', {
                        is: 'profileVaccineInformation',
                        then: Joi.object().keys({
                            profileDetails: Joi.object().keys({
                                passportNo: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return error;
                                    }),
                                dob: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return error;
                                    }),
                                nationalityId: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return error;
                                    }),
                                _nationality_id: Joi.string()
                                    .required()
                                    .error((error) => {
                                        return error;
                                    }),
                                officeExtension: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                officeRoomNo: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                buildingStreetName: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                floorNo: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                country: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                district: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                countryId: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                districtId: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                city: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                zipCode: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                unit: Joi.string()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                            }),
                            vaccineConfiguration: Joi.array().items(
                                Joi.object().keys({
                                    _category_id: Joi.string()
                                        .required()
                                        .error((error) => {
                                            return error;
                                        }),
                                    vaccineDetails: Joi.array().items(
                                        Joi.object().keys({
                                            _vaccine_id: Joi.string()
                                                .optional()
                                                .error((error) => {
                                                    return error;
                                                }),
                                            universalNumber: Joi.string()
                                                .optional()
                                                .error((error) => {
                                                    return error;
                                                }),
                                            vaccineCentre: Joi.string()
                                                .optional()
                                                .error((error) => {
                                                    return error;
                                                }),
                                            country: Joi.string()
                                                .optional()
                                                .error((error) => {
                                                    return error;
                                                }),
                                            city: Joi.string()
                                                .optional()
                                                .error((error) => {
                                                    return error;
                                                }),
                                            dosageTaken: Joi.array(),
                                            boosterTaken: Joi.array(),
                                            doses: Joi.array().items(
                                                Joi.object().keys({
                                                    name: Joi.string()
                                                        .optional()
                                                        .error((error) => {
                                                            return error;
                                                        }),
                                                    date: Joi.string()
                                                        .optional()
                                                        .error((error) => {
                                                            return error;
                                                        }),
                                                    batchNo: Joi.string()
                                                        .optional()
                                                        .error((error) => {
                                                            return error;
                                                        }),
                                                }),
                                            ),
                                            booster: Joi.array().items(
                                                Joi.object().keys({
                                                    name: Joi.string()
                                                        .optional()
                                                        .error((error) => {
                                                            return error;
                                                        }),
                                                    date: Joi.string()
                                                        .optional()
                                                        .error((error) => {
                                                            return error;
                                                        }),
                                                    batchNo: Joi.string()
                                                        .optional()
                                                        .error((error) => {
                                                            return error;
                                                        }),
                                                }),
                                            ),
                                        }),
                                    ),
                                }),
                            ),
                        }),
                        otherwise: Joi.optional(),
                    }),
            })
            .unknown(false),
    })
    .unknown(true);

const institutionIdGetValidation = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _institution_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const designationIdGetValidation = Joi.object()
    .keys({
        query: Joi.object()
            .keys({
                searchKey: Joi.string().error(() => 'SEARCHKEY_REQUIRED'),
                sortName: Joi.string().error(() => 'SORTNAME_REQUIRED'),
                sortId: Joi.string().error(() => 'SORTID_REQUIRED'),
                designation: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'INSTITUTION_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const updateDesignationValidator = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                updateDesignation: Joi.array().items(
                    Joi.object().keys({
                        id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'ID_REQUIRED';
                            }),
                        designation: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'DESIGNATION_ID_REQUIRED';
                            }),
                    }),
                ),
                type: Joi.string()
                    .valid('selective', 'all')
                    .required()
                    .error(() => {
                        return 'TYPE_REQUIRED';
                    }),
                designation: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'DESIGNATION_ID_REQUIRED';
                    }),
                previousDesignation: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'PREVIOUS_DESIGNATION_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const usersDocumentUploadValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _user_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                isReSubmitted: Joi.boolean().error((error) => {
                    return error;
                }),
                isRegistered: Joi.boolean().error((error) => {
                    return error;
                }),
                isGlobalMandatoryStatus: Joi.boolean()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                isRejectHandlingStatus: Joi.boolean()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                updateObject: Joi.array().items(
                    Joi.object().keys({
                        _category_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error((error) => {
                                return error;
                            }),
                        document: Joi.array().items(
                            Joi.object().keys({
                                _document_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .required()
                                    .error((error) => {
                                        return error;
                                    }),
                                globalMandatory: Joi.boolean()
                                    .optional()
                                    .error((error) => {
                                        return error;
                                    }),
                                url: Joi.array().items(
                                    Joi.object().keys({
                                        value: Joi.string()
                                            .required()
                                            .error((error) => {
                                                return error;
                                            }),
                                        flagged: Joi.boolean().error((error) => {
                                            return error;
                                        }),
                                        edited: Joi.boolean().error((error) => {
                                            return error;
                                        }),
                                        globalMandatoryEdited: Joi.boolean()
                                            .optional()
                                            .error((error) => {
                                                return error;
                                            }),
                                        status: Joi.string()
                                            .optional()
                                            .allow('')
                                            .error((error) => {
                                                return error;
                                            }),
                                    }),
                                ),
                            }),
                        ),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const usersSingleDocumentUploadValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _user_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                updateObject: Joi.object().keys({
                    _category_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    document: Joi.object().keys({
                        _document_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error((error) => {
                                return error;
                            }),
                        url: Joi.object().keys({
                            _url_id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error((error) => {
                                    return error;
                                }),
                            value: Joi.string().error((error) => {
                                return error;
                            }),
                            flagged: Joi.boolean().error((error) => {
                                return error;
                            }),
                            edited: Joi.boolean().error((error) => {
                                return error;
                            }),
                        }),
                    }),
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const getUsersDocumentUploadValidator = Joi.object()
    .keys({
        params: Joi.object()
            .keys({
                _user_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error(() => {
                        return 'USER_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);
const usersBiometricUploadValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _user_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'USER_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                updateObject: Joi.array().items(Joi.string()),
                flagged: Joi.boolean().error((error) => {
                    return error;
                }),
                edited: Joi.boolean().error((error) => {
                    return error;
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const verifyUserDataValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            userData: Joi.array().items(
                Joi.object().keys({
                    employeeId: Joi.string()
                        .allow('')
                        .error(() => 'EMPLOYEE_ID_REQUIRED'),
                    email: Joi.string()
                        .allow('')
                        .error(() => 'EMAIL_REQUIRED'),
                    mobile: Joi.number()
                        .allow('')
                        .error(() => 'MOBILE_REQUIRED'),
                    code: Joi.string()
                        .allow('')
                        .error(() => 'MOBILE_REQUIRED'),
                    nationalityId: Joi.string()
                        .allow('')
                        .error(() => 'NATIONALITY_ID_REQUIRED'),
                    gender: Joi.string().error(() => 'GENDER_VALIDATION'),
                    first_name: Joi.string()
                        .allow('')
                        .error(() => 'FIRST_NAME_REQUIRED'),
                    last_name: Joi.string()
                        .allow('')
                        .error(() => 'LAST_NAME_REQUIRED'),
                    middle_name: Joi.string()
                        .allow('')
                        .error(() => 'MIDDLE_NAME_REQUIRED'),
                    family_name: Joi.string()
                        .optional()
                        .allow('')
                        .error((error) => {
                            return error;
                        }),
                }),
            ),
        }),
    })
    .unknown(true);

const verifyStudentDataValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            userData: Joi.array().items(
                Joi.object().keys({
                    academicNo: Joi.string()
                        .allow('')
                        .error(() => 'EMPLOYEE_ID_REQUIRED'),
                    email: Joi.string()
                        .allow('')
                        .error(() => 'EMAIL_REQUIRED'),
                    mobile: Joi.number()
                        .allow('')
                        .error(() => 'MOBILE_REQUIRED'),
                    code: Joi.string()
                        .allow('')
                        .error(() => 'CODE_REQUIRED'),
                    programName: Joi.string()
                        .allow('')
                        .error(() => 'PROGRAM_NAME_REQUIRED'),
                    batch: Joi.string()
                        .allow('')
                        .error(() => 'BATCH_REQUIRED'),
                    nationalityId: Joi.string()
                        .allow('')
                        .error(() => 'NATIONALITY_ID_REQUIRED'),
                    gender: Joi.string().error(() => 'GENDER_VALIDATION'),
                    first_name: Joi.string()
                        .allow('')
                        .error(() => 'FIRST_NAME_REQUIRED'),
                    last_name: Joi.string()
                        .allow('')
                        .error(() => 'LAST_NAME_REQUIRED'),
                    middle_name: Joi.string()
                        .allow('')
                        .error(() => 'MIDDLE_NAME_REQUIRED'),
                    family_name: Joi.string()
                        .optional()
                        .allow('')
                        .error((error) => {
                            return error;
                        }),
                }),
            ),
        }),
    })
    .unknown(true);

const listUserDataValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            query: Joi.object().keys({
                tab: Joi.string()
                    .valid(
                        constant.USER_STATUS.REGISTERED,
                        constant.USER_STATUS.INACTIVE,
                        constant.USER_STATUS.REGISTRATION_PENDING,
                    )
                    .required(() => 'TAB_REQUIRED'),
                searchKey: Joi.string().required(() => 'SEARCHKEY_REQUIRED'),
                sortName: Joi.string().required(() => 'SORTNAME_REQUIRED'),
                sortId: Joi.string().required(() => 'SORTID_REQUIRED'),
                subTab: Joi.string().error(() => 'SUB_TAB'),
                userType: Joi.string()
                    .valid(constant.DC_STAFF, constant.DC_STUDENT)
                    .error(() => 'USER_TAB'),
                gender: Joi.string(),
                program: Joi.array(),
                batch: Joi.string(),
                yearFrom: Joi.string(),
                yearTo: Joi.string(),
            }),
        }),
    })
    .unknown(true);

const academicAllocationValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'USER_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                designation: Joi.string().error((error) => {
                    return error;
                }),
                _designation_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .error((error) => {
                        return error;
                    }),
                academicAllocation: Joi.array().items(
                    Joi.object({
                        allocation_type: Joi.string()
                            .valid(constant.PRIMARY, constant.AUXILIARY)
                            .error((error) => {
                                return error;
                            }),
                        departmentType: Joi.string()
                            .valid(constant.ACADEMIC, constant.ADMIN)
                            .error((error) => {
                                return error;
                            }),
                        programName: Joi.string().error((error) => {
                            return error;
                        }),
                        departmentName: Joi.string().error((error) => {
                            return error;
                        }),
                        _program_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        _department_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .required()
                            .error((error) => {
                                return error;
                            }),
                        departmentSubject: Joi.array().items(
                            Joi.object({
                                _department_subject_id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error((error) => {
                                        return error;
                                    }),
                                subjectName: Joi.string().error((error) => {
                                    return error;
                                }),
                            }),
                        ),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const employmentScheduleValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'USER_ID_REQUIRED';
                }),
        }),
        body: Joi.object()
            .keys({
                user_employment_type: Joi.string()
                    .valid(constant.FULL_TIME, constant.PART_TIME)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                staffModeType: Joi.string()
                    .valid(constant.COMMON, constant.INDEPENDENT)
                    .error((error) => {
                        return error;
                    }),
                daysTimeType: Joi.string()
                    .valid(constant.COMMON, constant.INDEPENDENT)
                    .error((error) => {
                        return error;
                    }),
                scheduleType: Joi.string()
                    .valid(constant.BY_DATE, constant.BY_DAY)
                    .error((error) => {
                        return error;
                    }),
                scheduleByDate: Joi.array().items(
                    Joi.object().keys({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'USER_ID_REQUIRED';
                            }),
                        daysTimeType: Joi.string()
                            .valid(constant.COMMON, constant.INDEPENDENT)
                            .required()
                            .error((error) => {
                                return error;
                            }),
                        staffModeType: Joi.string()
                            .valid(constant.COMMON, constant.INDEPENDENT)
                            .required()
                            .error((error) => {
                                return error;
                            }),
                        startDate: Joi.string().error((error) => {
                            return error;
                        }),
                        endDate: Joi.string().error((error) => {
                            return error;
                        }),
                        schedule: Joi.array().items(
                            Joi.object().keys({
                                _id: Joi.string()
                                    .alphanum()
                                    .length(24)
                                    .error(() => {
                                        return 'USER_ID_REQUIRED';
                                    }),
                                days: Joi.array()
                                    .items(
                                        Joi.string().valid(
                                            MONDAY,
                                            TUESDAY,
                                            WEDNESDAY,
                                            THURSDAY,
                                            FRIDAY,
                                            SATURDAY,
                                            SUNDAY,
                                        ),
                                    )
                                    .error((error) => {
                                        return error;
                                    }),
                                startTime: Joi.string().error((error) => {
                                    return error;
                                }),
                                endTime: Joi.string().error((error) => {
                                    return error;
                                }),
                                mode: Joi.array().items(
                                    Joi.string()
                                        .valid(constant.ONLINE, constant.ON_SITE, constant.BOTH)
                                        .error((error) => {
                                            return error;
                                        }),
                                ),
                            }),
                        ),
                    }),
                ),
                schedule: Joi.array().items(
                    Joi.object().keys({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error(() => {
                                return 'USER_ID_REQUIRED';
                            }),
                        days: Joi.array()
                            .items(
                                Joi.string().valid(
                                    MONDAY,
                                    TUESDAY,
                                    WEDNESDAY,
                                    THURSDAY,
                                    FRIDAY,
                                    SATURDAY,
                                    SUNDAY,
                                ),
                            )
                            .error((error) => {
                                return error;
                            }),
                        startTime: Joi.string().error((error) => {
                            return error;
                        }),
                        endTime: Joi.string().error((error) => {
                            return error;
                        }),
                        mode: Joi.array().items(
                            Joi.string()
                                .valid(constant.ONLINE, constant.ON_SITE, constant.BOTH)
                                .error((error) => {
                                    return error;
                                }),
                        ),
                    }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

const userSendMailValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            tab: Joi.string()
                .valid(constant.ALL, constant.SELECTED)
                .required()
                .error((error) => {
                    return error;
                }),
        }),
        body: Joi.object()
            .keys({
                id: Joi.array().items(
                    Joi.string()
                        .alphanum()
                        .length(24)
                        .error(() => {
                            return 'USER_ID_REQUIRED';
                        }),
                ),
                message: Joi.string().error((error) => {
                    return error;
                }),
            })
            .unknown(true),
    })
    .unknown(true);

const flagUnflagUserValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            field: Joi.string()
                .required()
                .error((error) => {
                    return error;
                }),
            _category_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'CATEGORY_ID_REQUIRED';
                }),
            _document_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'DOC_ID_REQUIRED';
                }),
            _url_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'URL_ID_REQUIRED';
                }),
            isFlagged: Joi.string().valid('true', 'false'),
        }),
        body: Joi.object()
            .keys({
                userId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'USER_ID_REQUIRED';
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const approvalRejectUserValidator = Joi.object()
    .keys({
        query: Joi.object().keys({
            type: Joi.string()
                .valid('all', 'single')
                .required()
                .error((error) => {
                    return error;
                }),
            field: Joi.string().error((error) => {
                return error;
            }),
            _category_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'CATEGORY_ID_REQUIRED';
                }),
            _document_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'DOC_ID_REQUIRED';
                }),
            _url_id: Joi.string()
                .alphanum()
                .length(24)
                .error(() => {
                    return 'URL_ID_REQUIRED';
                }),
            isFlagged: Joi.string().valid('true', 'false'),
            status: Joi.string().valid('approve', 'reject'),
        }),
        body: Joi.object()
            .keys({
                userId: Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'USER_ID_REQUIRED';
                    }),
                globalMandatoryEdited: Joi.boolean()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                globalMandatory: Joi.boolean()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                isGlobalMandatoryStatus: Joi.boolean()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
                isRejectHandlingStatus: Joi.boolean()
                    .optional()
                    .error((error) => {
                        return error;
                    }),
            })
            .unknown(true),
    })
    .unknown(true);

const verifyingStatusValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            userId: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error((error) => {
                    return error;
                }),
        }),
        body: Joi.object()
            .keys({
                statusType: Joi.string().required().valid(constant.VALID, constant.INVALID),
            })
            .unknown(true),
    })
    .unknown(true);

const multipleStatusVerifying = Joi.object()
    .keys({
        body: Joi.object()
            .keys({
                statusType: Joi.string().required().valid(constant.VALID, constant.INVALID),
                userType: Joi.string()
                    .valid(constant.DC_STAFF, constant.DC_STUDENT)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                id: Joi.array().items(
                    Joi.string()
                        .alphanum()
                        .length(24)
                        .error(() => {
                            return 'USER_ID_REQUIRED';
                        }),
                ),
            })
            .unknown(true),
    })
    .unknown(true);

module.exports = {
    authLoggedInBodySchema,
    authLoginBodySchema,
    userIdValidator,
    updateUserValidator,
    userImportSchema,
    userRegistrationSchema,
    institutionIdGetValidation,
    usersDocumentUploadValidator,
    getUsersDocumentUploadValidator,
    usersBiometricUploadValidator,
    verifyUserDataValidator,
    listUserDataValidator,
    academicAllocationValidator,
    userSendMailValidator,
    flagUnflagUserValidator,
    approvalRejectUserValidator,
    verifyingStatusValidator,
    verifyStudentDataValidator,
    employmentScheduleValidator,
    usersSingleDocumentUploadValidator,
    multipleStatusVerifying,
    designationIdGetValidation,
    activeInActiveValidator,
    updateDesignationValidator,
    isDefaultValidator,
};
