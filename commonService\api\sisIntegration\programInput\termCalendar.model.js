const mongoose = require('mongoose');
const { TERM_CALENDAR } = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const termCalendarSchemas = new Schema(
    {
        isActive: {
            type: Boolean,
            default: true,
        },
        collegeCode: { type: String },
        collegeType: { type: String },
        termCode: { type: String },
        academicYear: { type: String },
        term: { type: String },
        startDate: { type: Date },
        endDate: { type: Date },
        institutionCalendarIds: [ObjectId],
    },
    { timestamps: true },
);

module.exports = mongoose.model(TERM_CALENDAR, termCalendarSchemas);
