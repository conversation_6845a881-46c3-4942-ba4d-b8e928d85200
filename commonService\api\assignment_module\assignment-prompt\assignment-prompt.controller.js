const assignmentPrompt = require('./assignment-prompt.model');
const assignmentPromptAnswerSchema = require('../assignment_prompt_answer/assignment_prompt_answer.model');
const assignmentRubricsSchema = require('../assignment_rubrics/assignment-rubrics.model');

const { convertToMongoObjectId } = require('../../../utility/common');
const { getSignedUrl } = require('../assignment-settings/assignment-settings.util');
const { base64encode } = require('nodejs-base64');
exports.addAssignmentPrompt = async ({ headers = {}, body = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            score,
            singleAnswer,
            category,
            parentPromptId,
            childPromptIds,
            subjectId,
            _id,
            assignmentId,
            rubricsId,
            learningOutcome,
            showTaxonomy,
            taxonomyIds,
        } = body;
        let assignmentUpdate;
        if (_id) {
            assignmentUpdate = await assignmentPrompt.updateOne(
                { _id: convertToMongoObjectId(_id) },
                {
                    $set: {
                        category,
                        assignmentId,
                        parentPromptId,
                        singleAnswer,
                        ...(childPromptIds && {
                            childPromptIds,
                        }),
                        score,
                        subjectId,
                        rubricsId,
                        learningOutcome,
                        showTaxonomy,
                        taxonomyIds,
                    },
                },
            );
            assignmentUpdate._id = convertToMongoObjectId(_id);
        } else {
            const insertObject = {
                _institution_id,
                subjectId,
                category,
                singleAnswer,
                assignmentId,
                parentPromptId,
                childPromptIds,
                score,
                rubricsId,
                learningOutcome,
                showTaxonomy,
                taxonomyIds,
            };
            assignmentUpdate = await assignmentPrompt.create(insertObject);
        }
        if (parentPromptId) {
            const checkParentPromptId = await assignmentPrompt
                .findOne(
                    {
                        _id: convertToMongoObjectId(parentPromptId),
                    },
                    { _id: 1, score: 1, childPromptIds: 1 },
                )
                .lean();
            let parentChildIds = [];
            if (
                checkParentPromptId.childPromptIds &&
                checkParentPromptId.childPromptIds.length > 0
            ) {
                parentChildIds = checkParentPromptId.childPromptIds.map((element) =>
                    element.toString(),
                );
            }
            const checkingChildIdWithParent = parentChildIds.find(
                (element) => element.toString() === assignmentUpdate._id.toString(),
            );
            if (!checkingChildIdWithParent) {
                await assignmentPrompt.updateOne(
                    { _id: convertToMongoObjectId(parentPromptId) },
                    {
                        $push: {
                            childPromptIds: assignmentUpdate._id,
                        },
                    },
                );
            }
        }
        const getChildPromptIds = await assignmentPrompt.findOne(
            { _id: convertToMongoObjectId(assignmentUpdate._id) },
            { childPromptIds: 1 },
        );
        if (!assignmentUpdate) return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        return {
            statusCode: 200,
            message: 'UPDATED_SUCCESSFULLY',
            data: {
                _id: assignmentUpdate._id,
                childPromptIds: getChildPromptIds.childPromptIds,
                parentPromptId,
                assignmentId,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getAssignmentPrompt = async ({ query = {} }) => {
    try {
        const { promptId, subjectId } = query;
        const getAssignmentPrompt = await assignmentPrompt
            .findOne({
                _id: convertToMongoObjectId(promptId),
            })
            .populate({ path: 'rubricsId', select: { name: 1 } })
            .lean();
        if (getAssignmentPrompt.childPromptIds) {
            const getAssignmentChildPrompt = await assignmentPrompt
                .find({
                    _id: { $in: getAssignmentPrompt.childPromptIds },
                })
                .lean();
            getAssignmentPrompt.childPromptIds = getAssignmentChildPrompt;
        }
        if (!getAssignmentPrompt) return { statusCode: 404, message: 'NO_DATA_FOUND' };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getAssignmentPrompt,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.getAssignmentPromptWithoutAnswer = async ({ query = {} }) => {
    try {
        const { promptId, subjectId } = query;
        const getAssignmentPrompt = await assignmentPrompt
            .findOne(
                {
                    _id: convertToMongoObjectId(promptId),
                },
                {
                    'score.choiceType.choices.isCorrect': 0,
                    'score.singleAnswer.answers': 0,
                },
            )
            .lean();
        getAssignmentPrompt.score.fillInTheBlanks.answers.forEach((answer, index) => {
            answer.forEach((nestedAns, index2) => {
                getAssignmentPrompt.score.fillInTheBlanks.answers[index][index2] =
                    base64encode(nestedAns);
            });
        });
        getAssignmentPrompt.score.fillInTheBlanks.options.forEach((answer, index) => {
            answer.forEach((nestedAns, index2) => {
                getAssignmentPrompt.score.fillInTheBlanks.options[index][index2] =
                    base64encode(nestedAns);
            });
        });
        if (getAssignmentPrompt.childPromptIds) {
            const getAssignmentChildPrompt = await assignmentPrompt
                .find(
                    {
                        _id: { $in: getAssignmentPrompt.childPromptIds },
                    },
                    {
                        'score.choiceType.choices.isCorrect': 0,
                        'score.singleAnswer.answers': 0,
                    },
                )
                .lean();
            getAssignmentPrompt.childPromptIds = getAssignmentChildPrompt;
            getAssignmentChildPrompt.forEach((getAssignmentPrompt) => {
                getAssignmentPrompt.score.fillInTheBlanks.answers.forEach((answer, index) => {
                    answer.forEach((nestedAns, index2) => {
                        getAssignmentPrompt.score.fillInTheBlanks.answers[index][index2] =
                            base64encode(nestedAns);
                    });
                });
            });
            getAssignmentChildPrompt.forEach((getAssignmentPrompt) => {
                getAssignmentPrompt.score.fillInTheBlanks.options.forEach((answer, index) => {
                    answer.forEach((nestedAns, index2) => {
                        getAssignmentPrompt.score.fillInTheBlanks.options[index][index2] =
                            base64encode(nestedAns);
                    });
                });
            });
        }
        if (!getAssignmentPrompt) return { statusCode: 404, message: 'NO_DATA_FOUND' };
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: getAssignmentPrompt,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deleteAssignmentPrompt = async ({ query = {} }) => {
    try {
        const { promptId } = query;
        const AssignmentPrompt = await assignmentPrompt
            .findOne({
                _id: convertToMongoObjectId(promptId),
            })
            .lean();
        if (!AssignmentPrompt) return { statusCode: 404, message: 'NO_DATA_FOUND' };

        const assignmentPromptDelete = await assignmentPrompt.deleteOne({
            _id: AssignmentPrompt._id,
        });
        if (AssignmentPrompt.parentPromptId) {
            const assignmentExistingPromptIdRemove = await assignmentPrompt.updateOne(
                { _id: AssignmentPrompt.parentPromptId },
                {
                    $pull: { childPromptIds: AssignmentPrompt._id },
                },
            );
            if (!assignmentExistingPromptIdRemove)
                return { statusCode: 410, message: 'FAILED_TO_DELETE' };
        }

        if (!assignmentPromptDelete) return { statusCode: 410, message: 'FAILED_TO_DELETE' };
        return { statusCode: 200, message: 'DELETED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getListAssignmentPrompt = async ({
    _institution_id,
    assignmentId,
    promptIds,
    isAnswerNeeded,
}) => {
    const aggregateStages = [
        {
            $match: {
                isDeleted: false,
                _institution_id: convertToMongoObjectId(_institution_id),
                assignmentId: convertToMongoObjectId(assignmentId),
            },
        },
        {
            $group: {
                _id: null,
                objects: {
                    $push: {
                        k: { $toString: '$_id' },
                        v: '$$ROOT',
                    },
                },
            },
        },
        {
            $replaceRoot: {
                newRoot: {
                    $arrayToObject: '$objects',
                },
            },
        },
    ];
    const allPrompts = await assignmentPrompt.aggregate(aggregateStages);
    const rubricsIds = [];
    if (allPrompts[0]) {
        for (const promptElement of Object.values(allPrompts[0])) {
            if (promptElement.rubricsId)
                rubricsIds.push(convertToMongoObjectId(promptElement.rubricsId));
        }
    }
    let rubricsData = [];
    if (rubricsIds) {
        rubricsData = await assignmentRubricsSchema.aggregate([
            {
                $match: {
                    _id: { $in: rubricsIds },
                },
            },
            {
                $project: {
                    name: 1,
                },
            },
            {
                $group: {
                    _id: null,
                    objects: {
                        $push: {
                            k: { $toString: '$_id' },
                            v: '$$ROOT',
                        },
                    },
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $arrayToObject: '$objects',
                    },
                },
            },
        ]);
    }

    const constructData = async (promptId) => {
        const promptAssignment = allPrompts[0][promptId];
        if (!promptAssignment) return {};

        if (promptAssignment.score.hint) {
            for (const hintAttachment of promptAssignment.score.hint.attachments) {
                hintAttachment.signedUrl = await getSignedUrl(hintAttachment.url);
            }
        }
        if (promptAssignment.score.instructionAttachment) {
            for (const attachment of promptAssignment.score.instructionAttachment.attachments) {
                attachment.signedUrl = await getSignedUrl(attachment.url);
            }
        }
        if (promptAssignment.score.choiceType) {
            for (const choices of promptAssignment.score.choiceType.choices) {
                for (const choicesAttachments of choices.attachments) {
                    choicesAttachments.signedUrl = await getSignedUrl(choicesAttachments.url);
                }
            }
        }
        if (promptAssignment.score.singleAnswer) {
            for (const singles of promptAssignment.score.singleAnswer.answers) {
                for (const singleAttachments of singles.attachments) {
                    singleAttachments.signedUrl = await getSignedUrl(singleAttachments.url);
                }
            }
        }
        if (promptAssignment.score.matchTheFollowing) {
            for (const matchLeft of promptAssignment.score.matchTheFollowing.leftValues) {
                for (const leftAttachments of matchLeft.attachments) {
                    leftAttachments.signedUrl = await getSignedUrl(leftAttachments.url);
                }
            }

            for (const matchRight of promptAssignment.score.matchTheFollowing.rightValues) {
                for (const rightAttachments of matchRight.attachments) {
                    rightAttachments.signedUrl = await getSignedUrl(rightAttachments.url);
                }
            }
        }
        if (promptAssignment.score.fillInTheBlanks && !isAnswerNeeded) {
            const { answers, options } = promptAssignment.score.fillInTheBlanks;
            if (answers)
                answers.forEach((answerElement, index) => {
                    answerElement.forEach((nestedAnswer, index2) => {
                        answers[index][index2] = base64encode(nestedAnswer);
                    });
                });
            if (options)
                options.forEach((optionElement, index) => {
                    optionElement.forEach((nestedOption, index2) => {
                        options[index][index2] = base64encode(nestedOption);
                    });
                });
        }

        if (promptAssignment.childPromptIds)
            promptAssignment.childPromptIds = await Promise.all(
                promptAssignment.childPromptIds.map(async (childPromptElement) => {
                    const data = await constructData(childPromptElement);
                    return data;
                }),
            );
        if (promptAssignment.rubricsId) {
            const individualRubrics = rubricsData[0][promptAssignment.rubricsId];
            if (individualRubrics)
                promptAssignment.rubricsId = {
                    _id: individualRubrics._id,
                    name: individualRubrics.name,
                };
        } else {
            promptAssignment.rubricsId = {};
        }
        return promptAssignment;
    };
    const listsAssignmentPrompt = await Promise.all(
        promptIds.map(async (promptElement) => {
            const data = await constructData(promptElement);
            return data;
        }),
    );

    return { allPrompts, listsAssignmentPrompt };
};
exports.listsAssignmentPrompt = async ({ body = {}, headers = {} }) => {
    try {
        const { subjectId, assignmentId, promptIds, page, limit, showRootPromptOnly } = body;
        const { _institution_id } = headers;
        const { listsAssignmentPrompt } = await getListAssignmentPrompt({
            _institution_id,
            assignmentId,
            promptIds,
            isAnswerNeeded: false,
        });
        if (!listsAssignmentPrompt) return { statusCode: 404, message: 'NO_DATA_FOUND' };

        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { listsAssignmentPrompt },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.listsAssignmentPromptWithoutAnswer = async ({ body = {}, headers = {} }) => {
    try {
        const {
            studentId,
            subjectId,
            assignmentId,
            promptIds,
            page,
            limit,
            showRootPromptOnly,
            isAnswerNeeded,
            staffId,
        } = body;
        const { _institution_id } = headers;
        const assignmentQuery = { _institution_id };
        if (subjectId) {
            assignmentQuery.subjectId = subjectId;
        }
        if (assignmentId) {
            assignmentQuery.assignmentId = assignmentId;
        }

        const { allPrompts, listsAssignmentPrompt } = await getListAssignmentPrompt({
            _institution_id,
            assignmentId,
            promptIds,
            isAnswerNeeded,
        });
        if (listsAssignmentPrompt.length === 0)
            return { statusCode: 404, message: 'NO_DATA_FOUND' };
        const totalDoc = await assignmentPrompt.find(assignmentQuery).countDocuments().exec();

        let assignmentPromptAnswer = [];
        if (studentId) {
            const assignmentAnswerQuery = { _institution_id, studentId, isDeleted: false };
            if (assignmentId) {
                assignmentAnswerQuery.assignmentId = assignmentId;
            }
            assignmentAnswerQuery.assignmentPromptId = {
                $in: Object.keys(allPrompts[0]).map((promptId) => convertToMongoObjectId(promptId)),
            };
            assignmentPromptAnswer = await assignmentPromptAnswerSchema
                .find(assignmentAnswerQuery)
                .lean();
            if (staffId) {
                for (const promptAnswerElement of assignmentPromptAnswer) {
                    promptAnswerElement.staffEvaluation =
                        promptAnswerElement.staffEvaluation.filter(
                            (staffData) => staffData.staffId.toString() === staffId.toString(),
                        );
                    const choiceType = promptAnswerElement.choiceType;
                    if (choiceType && choiceType.length) {
                        const selectedChoice = choiceType[0].attachments;
                        if (selectedChoice && selectedChoice.length)
                            for (const choiceElement of selectedChoice)
                                if (choiceElement.url && choiceElement.signedUrl)
                                    choiceElement.signedUrl = await getSignedUrl(choiceElement.url);
                    }
                    const singleAnswer = promptAnswerElement.singleAnswer;
                    if (singleAnswer && singleAnswer.length) {
                        const selectedChoice = singleAnswer[0].attachments;
                        if (selectedChoice && selectedChoice.length)
                            for (const choiceElement of selectedChoice)
                                if (choiceElement.url && choiceElement.signedUrl)
                                    choiceElement.signedUrl = await getSignedUrl(choiceElement.url);
                    }
                    const fillInTheBlanks = promptAnswerElement.fillInTheBlanks;
                    if (fillInTheBlanks && fillInTheBlanks.length) {
                        const selectedChoice = fillInTheBlanks[0].attachments;
                        if (selectedChoice && selectedChoice.length)
                            for (const choiceElement of selectedChoice)
                                if (choiceElement.url && choiceElement.signedUrl)
                                    choiceElement.signedUrl = await getSignedUrl(choiceElement.url);
                    }
                    const matchTheFollowing = promptAnswerElement.matchTheFollowing;
                    if (matchTheFollowing && matchTheFollowing.length) {
                        const selectedChoice = matchTheFollowing[0].attachments;
                        if (selectedChoice && selectedChoice.length)
                            for (const choiceElement of selectedChoice)
                                if (choiceElement.url && choiceElement.signedUrl)
                                    choiceElement.signedUrl = await getSignedUrl(choiceElement.url);
                    }
                }
            }
        }
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: {
                totalDoc,
                listsAssignmentPrompt,
                assignmentPromptAnswer,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
exports.deletePromptRubric = async ({ body = {} }) => {
    try {
        const { promptId } = body;
        const id = promptId.map((prompt) => convertToMongoObjectId(prompt));
        const assignmentPromptData = await assignmentPrompt
            .updateMany(
                {
                    _id: { $in: id },
                },
                {
                    $unset: {
                        rubricsId: '',
                    },
                },
            )
            .lean();
        if (!assignmentPromptData)
            return { statusCode: 404, message: 'ERROR IN UPDATING ASSIGNMENT PROMPT RUBRICS' };
        return { statusCode: 200, message: 'ASSIGNMENT PROMPT RUBRICS UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
