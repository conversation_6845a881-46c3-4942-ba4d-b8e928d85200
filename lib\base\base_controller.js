const constants = require('../utility/constants');
module.exports = {
    list: async (Model, limit, page, query, project) => {
        let response_obj = {};
        try {
            let doc = null;
            let perPage = parseInt(limit > 0 ? limit : 10);
            let pageNo = parseInt(page > 0 ? page : 1);

            doc = await Model.find(query, project)
                .skip(perPage * (pageNo - 1))
                .limit(perPage);
            const totalDoc = await Model.find(query).countDocuments().exec();
            if (!doc) {
                response_obj = {
                    status: false,
                    data: doc,
                };
            } else {
                response_obj = {
                    status: true,
                    totalDoc: totalDoc,
                    totalPages: Math.ceil(totalDoc / perPage),
                    currentPage: pageNo,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    distinct_list: async (Model, col, query) => {
        let response_obj = {};
        try {
            let doc = null;

            doc = await Model.distinct(col, query);

            if (!doc) {
                response_obj = {
                    status: false,
                    data: doc,
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get: async (Model, query, project) => {
        let response_obj = null;
        try {
            let doc = await Model.findOne(query, project);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },
    get_populate: async (Model, query, project, pop) => {
        let response_obj = null;
        try {
            let doc = await Model.findOne(query, project).lean().populate(pop);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_sort_limt: async (Model, query, project, sort, limit) => {
        let response_obj = null;
        try {
            let doc = await Model.find(query, project).sort(sort).limit(limit);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    insert: async (Model, objects) => {
        let response_obj = null;
        try {
            const doc = await Model.create(objects);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'adding error',
                };
            } else {
                response_obj = {
                    status: true,
                    data: 'Added successfully',
                    responses: doc,
                };
                // console.log(doc);
            }
        } catch (error) {
            let objs = error;
            // console.log(error);
            // console.log(error.name);
            // console.log(error.code);
            // console.log(error.keyValue);
            // console.log(error.message);
            // console.log(error.name === 'MongoError' && error.code === 11000);
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update: async (Model, object_id, objects) => {
        let response_obj = null;
        try {
            // console.log(object_id, objects);
            const doc = await Model.updateOne({ _id: object_id }, { $set: objects });
            // console.log(doc);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Updating error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'Updating successfully',
                        responses: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Updating error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update_condition: async (Model, cond, objects) => {
        let response_obj = null;
        try {
            // console.log(object_id, objects);
            const doc = await Model.updateOne(cond, objects);
            // console.log(doc);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Updating error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'Updating successfully',
                        responses: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Updating error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update_condition_array_filter: async (Model, cond, objects, filter) => {
        let response_obj = null;
        try {
            // console.log(object_id, objects);
            const doc = await Model.updateOne(cond, objects, filter);
            // console.log(doc);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Updating error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'Updating successfully',
                        responses: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Updating error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update_many: async (Model, object_id, objects) => {
        let response_obj = null;
        try {
            // console.log(object_id, objects);
            const doc = await Model.updateMany({ _id: object_id }, objects);
            // console.log(doc);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Updating error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'Updating successfully',
                        responses: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Updating error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update_many_with_array_filter: async (Model, query, object, array_filter) => {
        let response_obj = null;
        try {
            // console.log(object_id, objects);
            const doc = await Model.updateMany(query, object, array_filter);
            // console.log(doc);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Updating error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'Updating successfully',
                        responses: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Updating error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update_push_pull: async (Model, object_id, objects) => {
        let response_obj = null;
        try {
            const doc = await Model.updateOne({ _id: object_id }, objects);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'IDs pushed error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'IDs Pushed/Pulled successfully',
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'IDs Pushed/Pulled error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    update_push_pull_many: async (Model, object_id, objects) => {
        let response_obj = null;
        try {
            const doc = await Model.updateMany(object_id, objects);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'IDs pushed error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'IDs Pushed/Pulled successfully',
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'IDs Pushed/Pulled error check parsing data',
                    };
                }
            }
        } catch (error) {
            let objs = error;
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    delete: async (Model, object_id) => {
        let response_obj = null;
        try {
            const doc = await Model.updateOne({ _id: object_id }, { $set: { isDeleted: true } });
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Delete error',
                };
            } else {
                if (doc.modifiedCount > 0) {
                    response_obj = {
                        status: true,
                        data: 'Deleted successfully',
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Delete error check parsing data',
                    };
                }
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_aggregate: async (Model, aggre) => {
        let response_obj = null;
        try {
            const doc = await Model.aggregate(aggre);
            const totalDoc = await Model.find({ isDeleted: false }).countDocuments().exec();
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Aggregate error',
                };
            } else {
                if (doc.length > 0) {
                    response_obj = {
                        status: true,
                        totalDoc: totalDoc,
                        data: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: doc,
                        // data: 'Error check parshing data : ' + doc
                    };
                }
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_aggregate_with_count: async (Model, aggre, query) => {
        let response_obj = null;
        try {
            const doc = await Model.aggregate(aggre);
            const totalDoc = await Model.find(query).countDocuments().exec();
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Aggregate error',
                };
            } else {
                //if (doc.length > 0) {
                response_obj = {
                    status: true,
                    totalDoc: totalDoc,
                    data: doc,
                };
                // } else {
                //     response_obj = {
                //         status: false,
                //         data: doc
                //         // data: 'Error check parshing data : ' + doc
                //     };
                // }
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_aggregate_with_empty: async (Model, aggre) => {
        let response_obj = null;
        try {
            const doc = await Model.aggregate(aggre);
            const totalDoc = await Model.find({ isDeleted: false }).countDocuments().exec();
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Aggregate error',
                };
            } else {
                //if (doc.length > 0) {
                response_obj = {
                    status: true,
                    totalDoc: totalDoc,
                    data: doc,
                };
                // } else {
                //     response_obj = {
                //         status: false,
                //         data: doc
                //         // data: 'Error check parshing data : ' + doc
                //     };
                // }
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_count: async (Model, aggre) => {
        let response_obj = null;
        // console.log('Count ', aggre);
        try {
            const totalDoc = await Model.find(aggre).countDocuments().exec();
            response_obj = {
                status: true,
                data: totalDoc,
            };
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_aggregate_duplicate_check: async (Model, aggre) => {
        let response_obj = null;
        try {
            const doc = await Model.aggregate(aggre);
            const totalDoc = await Model.find({ isDeleted: false }).countDocuments().exec();
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Aggregate error',
                };
            } else {
                response_obj = {
                    status: true,
                    totalDoc: totalDoc,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_list: async (Model, query, project) => {
        let response_obj = null;
        try {
            // console.log(query, project);
            // console.log(query[0].$match.title);
            let doc = await Model.find(query, project);
            // console.log(doc);
            if (doc.length == 0) {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_list_populate: async (Model, query, project, pop) => {
        let response_obj = null;
        try {
            // console.log(query, project);
            // console.log(query[0].$match.title);
            let doc = await Model.find(query, project).populate(pop);
            // console.log(doc);
            if (doc.length == 0) {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_list_sort: async (Model, query, project, sorts) => {
        let response_obj = null;
        try {
            // console.log(query, project);
            // console.log(query[0].$match.title);
            let doc = await Model.find(query, project).sort(sorts);
            // console.log(doc);
            if (doc.length == 0) {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            } else {
                response_obj = {
                    status: true,
                    data: doc,
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    check_id: async (Model, query) => {
        let response_obj = null;
        try {
            var ch = query._id.$in;
            // console.log(query);
            // console.log(Array.isArray(ch));
            // console.log((query._id.$in));
            let doc = await Model.find(query);
            // console.log(query._id.$in.length, ' : ', doc.length);
            // console.log(doc);
            if (!Array.isArray(ch)) {
                if (doc.length != 0) {
                    response_obj = {
                        status: true,
                        data: doc,
                    };
                } else {
                    response_obj = {
                        status: false,
                        data: 'Not found',
                    };
                }
            } else if (doc.length != 0 && query._id.$in.length == doc.length) {
                response_obj = {
                    status: true,
                    data: doc,
                };
            } else {
                response_obj = {
                    status: false,
                    data: 'Not found',
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    get_aggregate_with_id_match: async (Model, aggre, doc_aggre) => {
        let response_obj = null;
        try {
            const doc = await Model.aggregate(aggre);
            const totalDoc = await Model.find(doc_aggre).countDocuments().exec();
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Aggregate error',
                };
            } else {
                response_obj = {
                    status: true,
                    totalDoc: totalDoc,
                    data: doc,
                };
                // if (doc.length > 0) {
                //     response_obj = {
                //         status: true,
                //         'totalDoc': totalDoc,
                //         data: doc
                //     };
                // } else {
                //     response_obj = {
                //         status: false,
                //         data: doc
                //         // data: 'Error check parshing data : ' + doc
                //     };
                // }
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    bulk_write: async (Model, objects) => {
        let response_obj = null;
        try {
            const doc = await Model.bulkWrite(objects);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'adding error',
                };
            } else {
                response_obj = {
                    status: true,
                    data: 'Added successfully',
                    responses: doc,
                };
                // console.log(doc);
            }
        } catch (error) {
            let objs = error;
            // console.log(error);
            // console.log(error.name);
            // console.log(error.code);
            // console.log(error.keyValue);
            // console.log(error.message);
            // console.log(error.name === 'MongoError' && error.code === 11000);
            if (error.name === 'MongoError' && error.code === 11000) {
                objs = {
                    message: 'Duplicate value',
                    field: error.keyValue,
                };
            } else {
                objs = 'Error catch : ' + error;
            }
            response_obj = {
                status: false,
                data: objs,
            };
        } finally {
            return response_obj;
        }
    },

    hard_delete: async (Model, object_id) => {
        let response_obj = null;
        try {
            const doc = await Model.deleteMany(object_id);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Delete error',
                };
            } else {
                response_obj = {
                    status: true,
                    data: 'Deleted successfully',
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    hard_delete_aggregate_with_id_match: async (Model, aggre) => {
        let response_obj = null;
        try {
            const doc = await Model.remove(aggre);
            if (!doc) {
                response_obj = {
                    status: false,
                    data: 'Delete error',
                };
            } else {
                response_obj = {
                    status: true,
                    data: 'Deleted successfully',
                };
            }
        } catch (error) {
            response_obj = {
                status: false,
                data: 'Error catch : ' + error,
            };
        } finally {
            return response_obj;
        }
    },

    // From DA
    // get all data with and with out pagination
    dsGetAll: async (Model, query, project, limit, pageNo) => {
        let response = {};
        try {
            let doc = [];
            const totalDoc = await Model.find(query).countDocuments().exec(); // get all data count
            if (limit && pageNo) {
                // get all data with limit and pages
                const perPage = parseInt(limit) > 0 ? parseInt(limit) : 10; // get page limit
                const pages = parseInt(pageNo) > 0 ? parseInt(pageNo) : 1; // get pages count
                doc = await Model.find(query, project)
                    .skip(perPage * (pages - 1))
                    .limit(perPage);
                response = {
                    // get all data with limit and pages response
                    success: true,
                    message: constants.DS_DATA_RETRIEVED,
                    totalCount: totalDoc,
                    totalPages: Math.ceil(totalDoc / perPage),
                    currentPage: pageNo,
                    data: doc,
                };
            } else {
                doc = await Model.find(query, project); // get all data
                response = {
                    // get all data response
                    success: true,
                    message: constants.DS_DATA_RETRIEVED,
                    data: doc,
                };
            }
            return response;
        } catch (error) {
            throw Error(error);
        }
    },
    // get all data with sort with out limit and pages
    dsGetAllWithSort: async (Model, query, project, sort) => {
        let response = null;
        try {
            const doc = await Model.find(query, project).sort(sort);
            response = {
                data: doc,
                message: constants.DS_DATA_RETRIEVED,
            };
            return response;
        } catch (error) {
            throw Error(error);
        }
    },
    // get all data with sort with out limit and pages
    dsGetAllWithSortAsJSON: async (Model, query, project, sort) => {
        let response = null;
        try {
            const doc = await Model.find(query, project).sort(sort).lean();
            response = {
                data: doc,
                message: constants.DS_DATA_RETRIEVED,
            };
            return response;
        } catch (error) {
            throw Error(error);
        }
    },
    // get single data
    dsGet: async (Model, query, project) => {
        try {
            const doc = await Model.findOne(query, project);
            let response = {};
            if (doc) {
                response = {
                    success: true,
                    data: doc,
                    message: constants.DS_DATA_RETRIEVED,
                };
            } else {
                response = {
                    success: false,
                    message: constants.DS_NOT_FOUND,
                };
            }
            return response;
        } catch (error) {
            throw Error(error);
        }
    },
    // insert data
    dsInsert: async (Model, data) => {
        let response = null;
        try {
            const doc = await Model.create(data);
            if (!doc) {
                response = {
                    success: false,
                    message: constants.DS_ADD_FAILED,
                };
            } else {
                response = {
                    success: true,
                    message: constants.DS_ADDED,
                    data: doc,
                };
            }
            return response;
        } catch (error) {
            if (error.name === 'MongoError' && error.code === 11000) {
                return {
                    message: constants.DS_DUPLICATE_FOUND,
                    success: false,
                };
            }
            throw Error(error);
        }
    },
    // update data - set new data
    dsUpdate: async (Model, id, data) => {
        let response = null;
        try {
            const doc = await Model.updateOne(
                {
                    _id: id,
                },
                {
                    $set: data,
                },
            );
            if (!doc) {
                response = {
                    success: false,
                    message: constants.DS_UPDATE_FAILED,
                };
            } else if (doc.modifiedCount > 0) {
                response = {
                    success: true,
                    message: constants.DS_UPDATED,
                };
            } else {
                response = {
                    success: false,
                    message: constants.DS_NOTHING_TO_UPDATE,
                };
            }
            return response;
        } catch (error) {
            if (error.name === 'MongoError' && error.code === 11000) {
                return {
                    message: constants.DS_DUPLICATE_FOUND,
                    success: false,
                };
            }
            throw Error(error);
        }
    },
    dsUpdateMany: async (Model, query, data) => {
        let response = null;
        try {
            const doc = await Model.updateMany(query, data, { multi: true });
            if (!doc) {
                response = {
                    success: false,
                    message: constants.DS_UPDATE_FAILED,
                };
            } else if (doc.modifiedCount > 0) {
                response = {
                    success: true,
                    message: constants.DS_UPDATED,
                };
            } else {
                response = {
                    success: false,
                    message: constants.DS_NOTHING_TO_UPDATE,
                };
            }
            return response;
        } catch (error) {
            if (error.name === 'MongoError' && error.code === 11000) {
                return {
                    message: constants.DS_DUPLICATE_FOUND,
                    success: false,
                };
            }
            throw Error(error);
        }
    },
    dsCustomUpdate: async (Model, query, project, arrayFilter) => {
        let response = null;
        try {
            let doc;
            if (arrayFilter) {
                doc = await Model.updateOne(query, project, {
                    arrayFilters: arrayFilter,
                });
            } else {
                doc = await Model.updateOne(query, project);
            }
            if (!doc) {
                response = {
                    success: false,
                    message: constants.DS_UPDATE_FAILED,
                };
            } else if (doc.modifiedCount > 0) {
                response = {
                    success: true,
                    message: constants.DS_UPDATED,
                };
            } else {
                response = {
                    success: false,
                    message: constants.DS_NOTHING_TO_UPDATE,
                };
            }
            return response;
        } catch (error) {
            if (error.name === 'MongoError' && error.code === 11000) {
                return {
                    message: constants.DS_DUPLICATE_FOUND,
                    success: false,
                };
            }
            throw Error(error);
        }
    },
    // to get updated document on return
    dsCustomFindOneAndUpdate: async (Model, query, project, arrayFilter = []) => {
        let response = null;
        try {
            const doc = await Model.findOneAndUpdate(query, project, {
                new: true,
                arrayFilters: arrayFilter,
            });
            if (!doc) {
                response = {
                    success: false,
                    statusCode: 500,
                    message: constants.DS_UPDATE_FAILED,
                };
            } else {
                response = {
                    success: true,
                    data: doc,
                    message: constants.DS_UPDATED,
                };
            }
            return response;
        } catch (error) {
            if (error.name === 'MongoError' && error.code === 11000) {
                return {
                    success: false,
                    statusCode: 409,
                    message: constants.DS_DUPLICATE_FOUND,
                };
            }
            throw Error(error);
        }
    },
    // Delete one
    dsDeleteOne: async (Model, objectId) => {
        let response = null;
        try {
            const doc = await Model.updateOne(
                {
                    _id: objectId,
                },
                {
                    $set: {
                        isDeleted: true,
                    },
                },
            );
            if (!doc || doc.modifiedCount === 0) {
                response = {
                    success: false,
                    message: constants.DS_DELETE_FAILED,
                };
            } else if (doc.modifiedCount > 0) {
                response = {
                    success: true,
                    message: constants.DS_DELETED,
                };
            }
            return response;
        } catch (error) {
            throw Error(error);
        }
    },
    // hard delete - clear from db
    dsDeleteAll: async (Model, query) => {
        let response;
        try {
            const doc = await Model.deleteMany(query);
            if (!doc) {
                response = {
                    success: false,
                    message: constants.DS_DELETE_FAILED,
                };
            } else {
                response = {
                    success: true,
                    message: constants.DS_DELETED,
                };
            }
            return response;
        } catch (error) {
            throw Error(error);
        }
    },
    // get count
    dsGetCount: async (Model, query = {}) => {
        try {
            const count = await Model.find(query, { _id: 1 }).countDocuments();
            return count;
        } catch (error) {
            throw Error(error);
        }
    },
};
