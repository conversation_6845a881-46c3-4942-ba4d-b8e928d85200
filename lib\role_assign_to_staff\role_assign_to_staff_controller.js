const constant = require('../utility/constants');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const role_assign_to_staff_model = require('./role_assign_to_staff_model');
const role_assign_to_staff = require('mongoose').model(constant.ROLE_ASSIGN_TO_STAFF);

exports.insert = async (req, res) => {
    const doc = await base_control.insert(role_assign_to_staff, req.body);
    if (doc.status) {
        common_files.com_response(
            res,
            201,
            true,
            req.t('STAFF_ASSIGNED_TO_ROLE_SUCCESSFULLY'),
            doc.responses,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};
exports.update = async (req, res) => {
    const object_id = req.params.id;
    const doc = await base_control.update(role_assign_to_staff, object_id, req.body);
    if (doc.status) {
        common_files.com_response(res, 201, true, req.t('UPDATE_SUCCESSFULLY'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};
exports.delete = async (req, res) => {
    const id = req.params.id;
    const doc = await base_control.delete(role_assign_to_staff, id);
    if (doc.status) {
        common_files.com_response(res, 201, true, req.t('DELETED_SUCCESSFULLY'), doc.data);
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};
