const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    getTermDetails,
    getEnrollStudentDetails,
    getCourseDetails,
    getStudentGroupStudentPull,
    getStudentGroupStudentPullWithGroup,
    getStudentGroupForAllCalendar,
    programStudentRefresh,
    courseScheduleSync,
} = require('./programInput.controller');

router.get('/termDetails', catchAsync(getTermDetails));
router.get('/enrollStudentDetails', catchAsync(getEnrollStudentDetails));
router.get('/courseDetails', catchAsync(getCourseDetails));
router.post('/studentGroupStudentPull', catchAsync(getStudentGroupStudentPull));
router.post(
    '/getStudentGroupStudentPullWithGroup',
    catchAsync(getStudentGroupStudentPullWithGroup),
);
router.post('/getStudentGroupForAllCalendar', catchAsync(getStudentGroupForAllCalendar));
router.post('/courseScheduleSync', catchAsync(courseScheduleSync));
// Student Refresh in Course Schedules Course Wise
router.post('/studentRefresh', catchAsync(programStudentRefresh));
module.exports = router;
