let institution_formate = require('../institution/institution_formate');

module.exports = {
    position: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                position_title: element.position_title,
                institution: institution_formate.institution_ID_Only(element.institution),
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    },

    position_ID: (doc) => {
        let obj = {
            _id: doc._id,
            position_title: doc.position_title,
            institution: institution_formate.institution_ID_Only(doc.institution),
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    position_ID_Only: (doc) => {
        let obj = {
            _id: doc._id,
            position_title: doc.position_title,
            institution: doc._institution_id,
            isDeleted: doc.isDeleted
        }
        return obj;
    },

    position_ID_Array_Only: (doc) => {
        let formate_obj = [];
        doc.forEach(element => {
            let obj = {
                _id: element._id,
                position_title: element.position_title,
                institution: element._institution_id,
                isDeleted: element.isDeleted
            }
            formate_obj.push(obj);
        });
        return formate_obj;
    }
}