const { convertToMongoObjectId, query } = require('../../../utility/common');
const { LOCAL, GENDER } = require('../../../utility/constants');
const { BASIC_DATA_FROM } = require('../../../utility/util_keys');
const sisAxios = require('../../../utility/sis.axios');
const getCalendarTermList = async ({ termCode, filterKey }) => {
    try {
        const termList = [];
        let calendarTermList = [];
        if (BASIC_DATA_FROM === LOCAL) {
            const termDetails = require('../serviceDatas/termDetails');
            calendarTermList = termCode
                ? termDetails.filter((termDetailsElement) => termDetailsElement[2] === termCode)
                : termDetails;
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
            const dateFormatter = () => {
                const dateFormate = new Date();
                return dateFormate.toLocaleDateString('en-GB', {
                    day: 'numeric',
                    month: 'numeric',
                    year: 'numeric',
                });
            };
            let sqlQueryWhere = '';
            let sqlQueryWhereParams = {};
            if (filterKey) {
                sqlQueryWhereParams = {
                    startDt: dateFormatter(),
                };
                sqlQueryWhere = `(TO_DATE(:startDt, 'DD/MM/YYYY') BETWEEN TERM_BEGIN_DT AND TERM_END_DT) OR TERM_BEGIN_DT >= TO_DATE(:startDt, 'DD/MM/YYYY')`;
            }
            const termDetails = await sisAxios({
                url: '/course-details/meta-list',
                method: 'POST',
                data: {
                    queryType: 'terms_with_where',
                    whereClause: sqlQueryWhere,
                    params: sqlQueryWhereParams,
                },
            })
                .then((resp) => resp.data)
                .catch(function (error) {
                    console.error(error);
                    return [];
                });
            calendarTermList = termDetails && termDetails.rows ? termDetails.rows : [];
        }
        for (termElement of calendarTermList) {
            termList.push({
                collegeCode: termElement[0],
                collegeType: termElement[1],
                termCode: termElement[2],
                academicYear: termElement[3],
                term: termElement[4],
                startDate: termElement[5],
                endDate: termElement[6],
            });
        }
        return termList;
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getEnrollStudentList = async ({ termCode, courseId, classNo, filterKey }) => {
    try {
        const enrollStudentList = [];
        if (BASIC_DATA_FROM === LOCAL) {
            const enrollStudentDetails = require('../serviceDatas/enrollStudentDetails');
            const enrollStudentDetailList =
                termCode || courseId || classNo
                    ? enrollStudentDetails.filter(
                          (studentDetailElement) =>
                              (termCode
                                  ? studentDetailElement[10].toString() === termCode.toString()
                                  : true) &&
                              (courseId
                                  ? studentDetailElement[7].toString() === courseId.toString()
                                  : true) &&
                              (classNo
                                  ? studentDetailElement[9].toString() === classNo.toString()
                                  : true),
                      )
                    : enrollStudentDetails.splice(0, 100);
            for (studentDetails of enrollStudentDetailList) {
                enrollStudentList.push({
                    studentId: studentDetails[0],
                    name: studentDetails[1],
                    gender: studentDetails[2],
                    programStatus: studentDetails[3],
                    academic: studentDetails[4],
                    program: studentDetails[5],
                    academicPlan: studentDetails[6],
                    courseId: studentDetails[7],
                    courseOfferNo: studentDetails[8],
                    classNo: studentDetails[9],
                    termCode: studentDetails[10],
                    subject: studentDetails[11],
                    catalogNo: studentDetails[12],
                });
            }
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
            let sqlQueryWhere = `STRM = '${termCode}' AND ENRL_STATUS_REASON = 'ENRL'`;
            if (classNo) {
                sqlQueryWhere += ` AND CLASS_NBR = '${classNo}'`;
            }
            if (courseId) {
                sqlQueryWhere += ` AND CRSE_ID = '${courseId}'`;
            }
            // sqlQueryWhere = `STRM = '2222' AND CRSE_ID = '667871'`;
            console.info({ type: 'enroll', sqlQueryWhere });
            const enrollStudentDetailList = await sisAxios({
                url: '/course-details',
                method: 'POST',
                data: {
                    queryType: 'enroll_with_where',
                    whereClause: sqlQueryWhere,
                    params: {},
                },
            })
                .then((resp) => resp.data)
                .catch(function (error) {
                    console.error(error);
                    return [];
                });
            for (studentDetails of enrollStudentDetailList) {
                enrollStudentList.push({
                    studentId: studentDetails[0],
                    name: studentDetails[1],
                    gender: studentDetails[2],
                    programStatus: studentDetails[3],
                    academic: studentDetails[4],
                    program: studentDetails[5],
                    academicPlan: studentDetails[6],
                    courseId: studentDetails[7],
                    courseOfferNo: studentDetails[8],
                    classNo: studentDetails[9],
                    termCode: studentDetails[10],
                    subject: studentDetails[11],
                    catalogNo: studentDetails[12],
                });
            }
        }
        console.log('Enroll Student Count ', enrollStudentList.length);
        return enrollStudentList;
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseList = async ({ filterKey, termCode, startDate, endDate, subject, catalogNo }) => {
    try {
        const courseList = [];
        const dateFormatter = (inwardDate) => {
            const dateFormate = new Date(inwardDate);
            return dateFormate.toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'numeric',
                year: 'numeric',
            });
        };
        if (BASIC_DATA_FROM === LOCAL) {
            const courseDetails = require('../serviceDatas/courseDetails');
            let courseDetailsList = [];
            if (filterKey) {
                if (filterKey === 'termCode')
                    courseDetailsList = courseDetails.filter((courseElement) => {
                        return courseElement[2] === termCode;
                    });
                else if (filterKey === 'query')
                    courseDetailsList = courseDetails.filter((courseElement) => {
                        return (
                            dateFormatter(courseElement[16]) === dateFormatter(startDate) &&
                            dateFormatter(courseElement[17]) === dateFormatter(endDate) &&
                            courseElement[7] === subject &&
                            courseElement[8] === catalogNo
                        );
                    });
            } else courseDetails.splice(0, 100);
            for (courseDetailElement of courseDetailsList) {
                courseList.push({
                    courseId: courseDetailElement[0],
                    courseOfferNo: courseDetailElement[1],
                    courseTerm: courseDetailElement[2],
                    sessionNo: courseDetailElement[3],
                    section: courseDetailElement[4],
                    collegeCode: courseDetailElement[5],
                    academic: courseDetailElement[6],
                    subject: courseDetailElement[7],
                    catalogNo: courseDetailElement[8],
                    courseCode: `${courseDetailElement[7]} ${courseDetailElement[8]}`,
                    academicPlan: courseDetailElement[9],
                    classNo: courseDetailElement[10],
                    associatedCode: courseDetailElement[11],
                    academicOrg: courseDetailElement[12],
                    campus: courseDetailElement[13],
                    location: courseDetailElement[15],
                    startDate: courseDetailElement[16],
                    endDate: courseDetailElement[17],
                    courseName: courseDetailElement[18],
                    courseDescription: courseDetailElement[19],
                    courseDescriptionLong: courseDetailElement[20],
                });
            }
            // const enrollStudentDetailList =
            //     termCode && courseId
            //         ? enrollStudentDetails.filter(
            //               (studentDetailElement) =>
            //                   studentDetailElement[10].toString() === termCode.toString() &&
            //                   studentDetailElement[7].toString() === courseId.toString(),
            //           )
            //         : enrollStudentDetails.splice(0, 100);
            // for (studentDetails of enrollStudentDetailList) {
            //     enrollStudentList.push({
            //         studentId: studentDetails[0],
            //         name: studentDetails[1],
            //         gender: studentDetails[2],
            //         programStatus: studentDetails[3],
            //         academic: studentDetails[4],
            //         program: studentDetails[5],
            //         academicPlan: studentDetails[6],
            //         courseId: studentDetails[7],
            //         courseOfferNo: studentDetails[8],
            //         classNo: studentDetails[9],
            //         termCode: studentDetails[10],
            //         subject: studentDetails[11],
            //         catalogNo: studentDetails[12],
            //     });
            // }
        } else {
            let sqlQueryWhere = '';
            let sqlQueryWhereParams = {};
            if (filterKey) {
                if (filterKey === 'termCode') {
                    sqlQueryWhere = `STRM = ${termCode}`;
                    // sqlQueryWhere = `STRM =2222 AND CATALOG_NBR = :catalogNbr AND SUBJECT = :subject`;
                    // sqlQueryWhereParams = {
                    //     catalogNbr: 314,
                    //     subject: 'CS',
                    // };
                } else if (filterKey === 'query') {
                    sqlQueryWhere = `START_DT = TO_DATE(:startDt, 'DD/MM/YYYY') AND END_DT = TO_DATE(:endDt, 'DD/MM/YYYY') AND CATALOG_NBR = :catalogNbr AND SUBJECT = :subject`;
                    sqlQueryWhereParams = {
                        startDt: dateFormatter(startDate),
                        endDt: dateFormatter(endDate),
                        catalogNbr: parseInt(catalogNo),
                        subject: subject.toUpperCase(),
                        // catalogNbr: 314,
                        // subject: 'CS',
                    };
                }
            }
            console.info({ type: 'courseDetails', sqlQueryWhere, sqlQueryWhereParams });
            // AXIOS Call has to be here for Get Data from Other Environments
            const courseDetailsList = await sisAxios({
                url: '/course-details',
                method: 'POST',
                data: {
                    queryType: 'course_with_where',
                    whereClause: sqlQueryWhere,
                    params: sqlQueryWhereParams,
                },
            })
                .then((resp) => resp.data)
                .catch(function (error) {
                    console.error(error);
                    return [];
                });
            for (courseDetailElement of courseDetailsList) {
                courseList.push({
                    courseId: courseDetailElement[0],
                    courseOfferNo: courseDetailElement[1],
                    courseTerm: courseDetailElement[2],
                    sessionNo: courseDetailElement[3],
                    section: courseDetailElement[4],
                    collegeCode: courseDetailElement[5],
                    academic: courseDetailElement[6],
                    subject: courseDetailElement[7],
                    catalogNo: courseDetailElement[8].trim(),
                    courseCode: `${courseDetailElement[7]} ${courseDetailElement[8]}`,
                    academicPlan: courseDetailElement[9],
                    classNo: courseDetailElement[10],
                    deliveryCode: courseDetailElement[11],
                    classType: courseDetailElement[12],
                    associatedCode: courseDetailElement[13],
                    academicOrg: courseDetailElement[14],
                    campus: courseDetailElement[15],
                    groupNo: courseDetailElement[4].slice(-2),
                    gender:
                        courseDetailElement[15] === 'MMB01'
                            ? GENDER.MALE
                            : courseDetailElement[15] === 'MMG01'
                            ? GENDER.FEMALE
                            : GENDER.BOTH,
                    location: courseDetailElement[16],
                    startDate: courseDetailElement[17],
                    endDate: courseDetailElement[18],
                    effStatus: courseDetailElement[19],
                    courseName: courseDetailElement[20],
                    courseDescription: courseDetailElement[21],
                    // courseDescriptionLong: courseDetailElement[21],
                });
            }
        }
        console.log('Course Count ', courseList.length);
        return courseList;
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getCourseScheduleSettingList = async ({ termCode, courseId, classNo, filterKey }) => {
    try {
        const courseScheduleSettingList = [];
        if (BASIC_DATA_FROM === LOCAL) {
            // Local
        } else {
            // AXIOS Call has to be here for Get Data from Other Environments
            let sqlQueryWhere = `STRM = '${termCode}' AND CRSE_ID = '${courseId}' AND ENRL_TOT IS NOT NULL AND ENRL_TOT != 0 AND MEETING_TIME_START IS NOT NULL AND EMAIL_ADDR IS NOT NULL AND MEETING_TIME_END IS NOT NULL AND CLASS_NBR IN (${classNo.join(
                ', ',
            )})`;
            if (filterKey === 'termCode') {
                sqlQueryWhere = `STRM = '${termCode}' AND ENRL_TOT IS NOT NULL AND ENRL_TOT != 0 AND MEETING_TIME_START IS NOT NULL AND EMAIL_ADDR IS NOT NULL AND MEETING_TIME_END IS NOT NULL AND CLASS_NBR IN (${classNo.join(
                    ', ',
                )})`;
            } else if (filterKey === 'query') {
                sqlQueryWhere = `STRM = '${termCode}' AND CRSE_ID = '${courseId}' AND ENRL_TOT IS NOT NULL AND ENRL_TOT != 0 AND MEETING_TIME_START IS NOT NULL AND EMAIL_ADDR IS NOT NULL AND MEETING_TIME_END IS NOT NULL AND CLASS_NBR IN (${classNo.join(
                    ', ',
                )})`;
            }
            // sqlQueryWhere = `STRM = '2222' AND CRSE_ID = '667871'`;
            console.info({ type: 'enroll', sqlQueryWhere });
            const scheduleSettingData = await sisAxios({
                url: '/course-details',
                method: 'POST',
                data: {
                    queryType: 'class_info_with_where',
                    whereClause: sqlQueryWhere,
                    params: {},
                },
            })
                .then((resp) => resp.data)
                .catch(function (error) {
                    console.error(error);
                    return [];
                });
            const timeConvertor = (timeElement) => {
                const [hours, minutes, seconds, microseconds] = timeElement.split('.');
                // Create a new Date object with the parsed time values
                const timeDate = new Date();
                timeDate.setHours(Number(hours) - 3); // Adjust for UTC+3
                timeDate.setMinutes(Number(minutes));
                timeDate.setSeconds(Number(seconds));
                timeDate.setMilliseconds(Number(microseconds / 1000)); // Convert microseconds to milliseconds
                const timeSplit = timeElement.split('.').map(Number);
                let format = 'AM';
                if (timeSplit[0] >= 12) {
                    format = 'PM';
                    if (timeSplit[0] > 12) {
                        timeSplit[0] -= 12;
                    }
                }
                return {
                    timeDate,
                    timeSplit: { hour: timeSplit[0], minute: timeSplit[1], format },
                };
            };
            for (scheduleSettingElement of scheduleSettingData) {
                const startTime = timeConvertor(scheduleSettingElement[17]);
                const endTime = timeConvertor(scheduleSettingElement[18]);
                let facilityId = scheduleSettingElement[16]
                    ? scheduleSettingElement[16].trim()
                    : '';
                if (facilityId.endsWith('.')) {
                    facilityId = facilityId.slice(0, -1);
                }
                courseScheduleSettingList.push({
                    courseId: scheduleSettingElement[0],
                    courseOfferNo: scheduleSettingElement[1],
                    termCode: scheduleSettingElement[2],
                    sessionCode: scheduleSettingElement[3],
                    classSection: scheduleSettingElement[4],
                    groupNo: scheduleSettingElement[4].slice(-2),
                    academicGroup: scheduleSettingElement[5],
                    subject: scheduleSettingElement[6],
                    catalogNbr: scheduleSettingElement[7],
                    academicCareer: scheduleSettingElement[8],
                    description: scheduleSettingElement[9],
                    classNo: scheduleSettingElement[10],
                    deliveryCode: scheduleSettingElement[11],
                    classType: scheduleSettingElement[12],
                    enrollTotal: scheduleSettingElement[13],
                    campus: scheduleSettingElement[14],
                    gender:
                        scheduleSettingElement[14] === 'MMB01'
                            ? GENDER.MALE
                            : scheduleSettingElement[14] === 'MMG01'
                            ? GENDER.FEMALE
                            : GENDER.BOTH,
                    classMTGNbr: scheduleSettingElement[15],
                    facilityId,
                    startTime: scheduleSettingElement[17],
                    startDateTime: startTime.timeDate,
                    start: startTime.timeSplit,
                    endTime: scheduleSettingElement[18],
                    endDateTime: endTime.timeDate,
                    end: endTime.timeSplit,
                    audiStamp: scheduleSettingElement[19],
                    monday: scheduleSettingElement[20],
                    tuesday: scheduleSettingElement[21],
                    wednesday: scheduleSettingElement[22],
                    thursday: scheduleSettingElement[23],
                    friday: scheduleSettingElement[24],
                    saturday: scheduleSettingElement[25],
                    sunday: scheduleSettingElement[26],
                    staffId: scheduleSettingElement[27],
                    staffName: scheduleSettingElement[28],
                    staffEmail: scheduleSettingElement[29].toLowerCase().trim(),
                });
            }
        }
        console.log('Course Schedule Setting Count ', courseScheduleSettingList.length);
        return courseScheduleSettingList;
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getCalendarTermList,
    getEnrollStudentList,
    getCourseList,
    getCourseScheduleSettingList,
};
