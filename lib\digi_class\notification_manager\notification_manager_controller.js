const constant = require('../../utility/constants');
const user = require('mongoose').model(constant.USER);
const notification = require('../../models/notification');
const base_control = require('../../base/base_controller');
const common_files = require('../../utility/common');
const ObjectId = common_files.convertToMongoObjectId;
const { sendResponse, sendErrorResponse } = require('../../utility/common');

exports.notification_list = async (req, res) => {
    try {
        const cond = {
            $or: [
                { 'staffs._id': ObjectId(req.headers._user_id) },
                { 'students._id': ObjectId(req.headers._user_id) },
            ],
            isDeleted: false,
        };
        const doc = await base_control.list(
            notification,
            req.query.limit,
            req.query.pageNo,
            cond,
            {},
            { createdAt: -1 },
        );
        if (!doc.status) return sendResponse(res, 200, false, req.t('NOTIFICATIONS_NOT_FOUND'), []);
        const response = common_files.list_all_response_function(
            res,
            200,
            true,
            'Notification list',
            doc.totalDoc,
            doc.totalPages,
            doc.currentPage,
            doc.data,
        );
        return res.status(200).send(response);
    } catch (error) {
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.notification_delete = async (req, res) => {
    try {
        const query = { $in: req.body.ids };
        const cond = { $set: { isDeleted: true } };
        const doc = await base_control.update_many(notification, query, cond);
        if (!doc.status)
            return sendResponse(res, 200, false, req.t('NOTIFICATION_NOT_REMOVED'), null);
        return sendResponse(res, 200, true, req.t('NOTIFICATION_REMOVED'));
    } catch (error) {
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};

exports.notification_undo = async (req, res) => {
    try {
        const query = { _id: { $in: req.body.ids } };
        const cond = { $set: { isDeleted: false } };
        const doc = await base_control.update_many(notification, query, cond);
        if (!doc.status)
            return sendResponse(res, 200, false, req.t('NOTIFICATION_NOT_REMOVED'), null);
        return sendResponse(res, 200, true, req.t('NOTIFICATION_UNDO'));
    } catch (error) {
        return sendErrorResponse(res, 500, false, req.t('SERVER_ERROR'), error.toString());
    }
};
