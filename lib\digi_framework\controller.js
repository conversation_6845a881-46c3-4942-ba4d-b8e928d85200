const constants = require('../utility/constants');
const institution = require('mongoose').model(constants.INSTITUTION);
const Framework = require('mongoose').model(constants.FRAMEWORK);
const Course = require('mongoose').model(constants.DIGI_COURSE);
const digi_curriculum = require('mongoose').model(constants.DIGI_CURRICULUM);

const {
    dsGetAllWithSort,
    dsGet,
    dsGetAll,
    dsInsert,
    dsUpdate,
    dsGetCount,
    dsDeleteOne,
    dsGetAllWithSortAsJSON,
} = require('../base/base_controller');
const {
    sendResult,
    sendErrorResponse,
    mergeStandardWithCurriculum,
    sendResultWithRequest,
    sendErrorResponseWithRequest,
    clone,
} = require('../utility/common');
const FrameworkFormat = require('./formate');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;

const project = { name: 1, code: 1, domains: 1, order: 1 };
const serverError = constants.DS_INTERNAL_SERVER_ERROR;
const cs = (string) => string.toString();

exports.getAll = async (req, res) => {
    const query = { isDeleted: false };
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const result = await dsGetAllWithSort(Framework, query, project, { order: 1 });
        const response = [];
        for (const framework of result.data) {
            const tempFrame = { mapped_count: 0, ...framework._doc };
            const where = { 'framework._id': ObjectId(tempFrame._id) };
            const select = { _id: 1, course_name: 1 };
            const curriculums = await digi_curriculum.find(where, select).countDocuments();
            tempFrame.mapped_count = curriculums;
            response.push(tempFrame);
        }
        const formattedResponse = FrameworkFormat.all(response);
        sendResultWithRequest(req, res, 200, result.message, formattedResponse);
    } catch (e) {
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.get = async (req, res) => {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { id } = req.params;
        const query = { _id: id, isDeleted: false };
        const result = await dsGet(Framework, query, project);
        if (result.success) {
            const response = FrameworkFormat.single(result.data);
            sendResult(res, 200, result.message, response);
        } else sendResult(res, 404, result.message);
    } catch (e) {
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.insert = async (req, res) => {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { name, code, domains } = req.body;
        const _institution_id = req.headers._institution_id;
        const existingDoc = await dsGet(Framework, { name, isDeleted: false }, { name: 1 });
        if (existingDoc.success) sendErrorResponse(res, 409, constants.DS_NAME_ALREADY_EXISTS);
        else {
            const doc = await dsGetAll(Framework, {}, { name: 1 });
            const order = doc.data.length;
            const data = { _institution_id, name, code, domains, order };
            console.log(data);
            const result = await dsInsert(Framework, data);
            if (result.success) sendResultWithRequest(req, res, 200, result.message);
            else sendResultWithRequest(req, res, 500, result.message);
        }
    } catch (e) {
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.update = async (req, res) => {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const objectId = req.params.id;
        const { name, code, domains, mode, updateFor } = req.body;
        const query = { _id: objectId };
        const idCheck = await dsGetCount(Framework, query);
        // is assigned course start
        const where = { 'framework._id': objectId, isDeleted: false };
        const select = { _id: 1, course_name: 1 };
        const courses = await Course.find(where, select).countDocuments();
        if (courses > 0) {
            const message = mode || 'delete';
            const For = updateFor || 'Domain';
            return res
                .status(410)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        500,
                        false,
                        `${For} ` + req.t('ASSIGNED_TO_COURSES_CANNOT_BE') + ` ${message}d`,
                        `${For} ` + req.t('ASSIGNED_TO_COURSES_CANNOT_BE') + ` ${message}d`,
                    ),
                );
        }
        // is assigned course end
        if (idCheck > 0) {
            const data = { name, code, domains };
            const result = await dsUpdate(Framework, objectId, data);
            if (result.success) sendResultWithRequest(req, res, 200, result.message);
            else sendResultWithRequest(req, res, 404, constants.DS_NOT_FOUND);
        } else sendErrorResponse(res, 404, constants.DS_INVALID_ID);
    } catch (e) {
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.delete = async (req, res) => {
    try {
        const institution_check = await base_control.get(institution, {
            _id: req.headers._institution_id,
            isDeleted: false,
        });
        if (!institution_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        500,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const objectId = req.params.id;
        const query = { _id: objectId };
        const idCheck = await dsGetCount(Framework, query);
        const where = { 'framework._id': objectId, isDeleted: false };
        const select = { _id: 1, course_name: 1 };
        const courses = await Course.find(where, select).countDocuments();
        if (courses > 0) {
            res.status(410).send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('FRAMEWORK_ASSIGNED_TO_COURSES_CAN_NOT_BE_DELETED'),
                    req.t('FRAMEWORK_ASSIGNED_TO_COURSES_CAN_NOT_BE_DELETED'),
                ),
            );
        } else {
            if (idCheck > 0) {
                const result = await dsDeleteOne(Framework, objectId);
                if (result.success) sendResult(res, 200, result.message);
                else sendResult(res, 404, constants.DS_NOT_FOUND);
            } else {
                sendErrorResponse(res, 404, constants.DS_INVALID_ID);
            }
        }
    } catch (e) {
        sendErrorResponse(res, 500, serverError, e);
    }
};

exports.upsert_standard_range_settings = async (req, res) => {
    const { framework_id, setting_id, standard_range_setting } = req.body;
    Framework.findOne({ _id: framework_id }, function (err, framework) {
        if (err) sendErrorResponse(res, 500, serverError, err);
        let setting = framework.standard_range_settings.id(setting_id);
        // is setting exist start
        const { domain_id, year, year_program } = standard_range_setting;
        if (!setting) {
            framework.standard_range_settings.forEach((sRS) => {
                if (
                    cs(domain_id) === cs(sRS.domain_id) &&
                    cs(year) === cs(sRS.year) &&
                    cs(year_program) === cs(sRS.year_program)
                )
                    setting = framework.standard_range_settings.id(sRS._id);
            });
        }
        // is setting exist end
        if (setting) {
            setting.set(standard_range_setting);
            framework.save();
            sendResult(res, 200, framework);
        } else {
            framework.standard_range_settings.push(standard_range_setting);
            framework.save(function (err, insertResult) {
                if (err) sendErrorResponse(res, 500, serverError, err);
                sendResult(res, 200, insertResult);
            });
        }
    });
};

exports.filter_curriculum_year_program_standard_range_settings = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const aggregate = [
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    isDeleted: false,
                    isActive: true,
                },
            },
            {
                $lookup: {
                    from: 'digi_programs',
                    localField: '_program_id',
                    foreignField: '_id',
                    as: 'program_data',
                },
            },
        ];
        const AllCurriculum = await base_control.get_aggregate(digi_curriculum, aggregate);
        if (!AllCurriculum.status)
            return sendErrorResponseWithRequest(req, res, 410, 'Error', 'Error', []);

        const curriculumWithFramework = [];
        const curriculumWithoutFramework = [];
        AllCurriculum.data.forEach((curriculum) => {
            if (curriculum.framework._id) curriculumWithFramework.push(curriculum);
            else curriculumWithoutFramework.push(curriculum);
        });
        const curriculumWithFrameworkCopy = clone(curriculumWithFramework);
        curriculumWithFrameworkCopy.forEach((curriculum) => {
            curriculum.year_level.forEach((year) => {
                if (year._pre_requisite_id) {
                    const preRequisiteCurriculum = curriculumWithoutFramework.find(
                        (cWF) => cs(cWF._id) === cs(year._pre_requisite_id),
                    );
                    if (preRequisiteCurriculum) {
                        const preRequisiteCurriculumCopy = clone(preRequisiteCurriculum);
                        preRequisiteCurriculumCopy.framework = curriculum.framework;
                        const isExistCurriculum = curriculumWithFramework.find(
                            (cWF) =>
                                cs(cWF._id) === cs(preRequisiteCurriculumCopy._id) &&
                                cs(cWF.framework._id) ===
                                    cs(preRequisiteCurriculumCopy.framework._id),
                        );
                        if (!isExistCurriculum) {
                            curriculumWithFramework.push(preRequisiteCurriculumCopy);
                        }
                    }
                }
            });
        });

        const query_framework = { isDeleted: false };
        const framework_data = await base_control.get_list(Framework, query_framework, {});
        if (!framework_data.status)
            return sendErrorResponseWithRequest(req, res, 410, 'Error', 'Error', []);
        const new_framework_data = [];
        //return res.send(doc.data);
        for (const { _doc: framework } of framework_data.data) {
            const curriculum_data = curriculumWithFramework.filter(function (curriculum) {
                if (curriculum.framework._id && cs(curriculum.framework._id) == cs(framework._id))
                    return curriculum;
            });
            //split year program
            const curriculums = [];
            for (curriculum of curriculum_data) {
                if (curriculum.program_data[0].program_type == 'program')
                    curriculum.year_program = curriculum.end_at;
                else {
                    curriculum.year_program = curriculum.end_at.toString() + '_PRE';
                }
                curriculums.push(curriculum);
            }
            const group_year_program = {};
            for (curriculum of curriculums) {
                if (!group_year_program[curriculum.year_program]) {
                    group_year_program[curriculum.year_program] = { curriculum_data: [] };
                    group_year_program[curriculum.year_program].curriculum_data.push(curriculum);
                } else group_year_program[curriculum.year_program].curriculum_data.push(curriculum);
            }
            framework.curriculums = group_year_program;
            console.log(framework);
            new_framework_data.push(framework);
        }
        res.status(200).send(
            common_files.responseFunctionWithRequest(
                req,
                200,
                true,
                req.t('FRAMEWORK_COMMON_STANDARD_RANGE_SETTINGS'),
                new_framework_data,
            ),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
};

exports.curriculum_standard_range_settings = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return sendErrorResponse(res, 404, req.t('INSTITUTION_NOT_FOUND'), 'Error', []);

        const aggregate = [
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _program_id: ObjectId(req.params.program_id),
                    isDeleted: false,
                    isActive: true,
                },
            },
            {
                $lookup: {
                    from: 'digi_programs',
                    localField: '_program_id',
                    foreignField: '_id',
                    as: 'program_data',
                },
            },
        ];
        const allCurriculums = await base_control.get_aggregate(digi_curriculum, aggregate);
        if (!allCurriculums.status) return sendErrorResponse(res, 410, 'Error', 'Error', []);
        const curriculumWithoutFramework = [];
        const curriculumWithFramework = [];
        allCurriculums.data.forEach((curriculum) => {
            if (curriculum.framework._id) curriculumWithFramework.push(curriculum);
            else curriculumWithoutFramework.push(curriculum);
        });

        if (curriculumWithoutFramework.length) {
            const curriculumWithoutFrameworkIDs = curriculumWithoutFramework.map((cWF) => cWF._id);
            const sharedCurriculums = (
                await dsGetAllWithSortAsJSON(digi_curriculum, {
                    isDeleted: false,
                    _program_id: { $ne: ObjectId(req.params.program_id) },
                    'framework._id': { $exists: true },
                    'year_level._pre_requisite_id': { $in: curriculumWithoutFrameworkIDs },
                })
            ).data;
            if (sharedCurriculums.length) {
                curriculumWithoutFramework.forEach((cWithoutF) => {
                    sharedCurriculums.forEach((sCurriculum) => {
                        const isExists = sCurriculum.year_level.find(
                            (year) => cs(year._pre_requisite_id) === cs(cWithoutF._id),
                        );
                        if (isExists) {
                            const cWithoutFCopy = clone(cWithoutF);
                            cWithoutFCopy.framework = sCurriculum.framework;
                            if (
                                !curriculumWithFramework.find(
                                    (c) =>
                                        cs(c._id) === cs(cWithoutFCopy._id) &&
                                        cs(c.framework._id) === cs(cWithoutFCopy.framework._id),
                                )
                            ) {
                                curriculumWithFramework.push(cWithoutFCopy);
                            }
                        }
                    });
                });
            }
        }
        const curriculums = [];
        for (curriculum of curriculumWithFramework) {
            if (curriculum.program_data[0].program_type == 'program')
                curriculum.year_program = curriculum.end_at;
            else curriculum.year_program = curriculum.end_at.toString() + '_PRE';
            curriculums.push(curriculum);
        }

        const query_framework = { isDeleted: false };
        const framework_data = await base_control.get_list(Framework, query_framework, {});
        if (!framework_data.status) return sendErrorResponse(res, 410, 'Error', 'Error', []);

        const new_framework_data = [];
        for (const { _doc: framework } of framework_data.data) {
            const a_curriculums = curriculums.filter(function (curriculum) {
                if (curriculum.framework._id && cs(curriculum.framework._id) === cs(framework._id))
                    return curriculum;
            });
            let isOneYearProgram;
            a_curriculums.forEach((curriculum) => {
                curriculum.standard_range_settings = mergeStandardWithCurriculum(
                    clone(framework.standard_range_settings),
                    'curriculum',
                    curriculum,
                );
                if (curriculum.start_at === curriculum.end_at) {
                    isOneYearProgram = true;
                    curriculum.standard_range_settings = curriculum.standard_range_settings.filter(
                        (srSetting) => !srSetting.year_program.includes('_PRE'),
                    );
                }
            });
            framework.curriculum_data = a_curriculums;
            if (isOneYearProgram)
                framework.standard_range_settings = framework.standard_range_settings.filter(
                    (srSetting) => !srSetting.year_program.includes('_PRE'),
                );
            if (a_curriculums.length) new_framework_data.push(framework);
        }
        res.status(200).send(
            common_files.response_function(
                res,
                200,
                true,
                req.t('CURRICULUM_STANDARD_RANGE_SETTINGS'),
                new_framework_data,
            ),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
};
