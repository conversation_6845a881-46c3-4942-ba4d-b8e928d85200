const express = require('express');
const route = express.Router();
const day_group = require('../day_group/day_group_controller');
const validater = require('./day_group_validator');
route.post('/list', day_group.list_values);
route.get('/:id', validater.day_group_id, day_group.list_id);
route.get('/', day_group.list);
route.post('/', validater.day_group, day_group.insert);
route.put('/:id', validater.day_group_id, validater.day_group, day_group.update);
route.delete('/:id', validater.day_group_id, day_group.delete);
module.exports = route;