const {
    allProgramDatas,
    allCurriculumDatas,
    allCourseList,
    allSessionDeliveryTypesDatas,
    allSessionOrderDatas,
    allDepartmentSubjectList,
    allProgramCalendarDatas,
    allStudentGroupYesterday,
} = require('./cache.service');

const { scheduleDateFormateChange } = require('../lib/utility/common_functions');

exports.importCacheDataOnload = async () => {
    await allProgramDatas();
    await allCurriculumDatas();
    await allCourseList();
    await allSessionDeliveryTypesDatas();
    await allSessionOrderDatas();
    await allDepartmentSubjectList();
    await allProgramCalendarDatas();
    await allStudentGroupYesterday('', scheduleDateFormateChange(new Date()));
};
