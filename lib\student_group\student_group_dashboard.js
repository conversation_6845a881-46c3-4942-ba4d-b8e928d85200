const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const ObjectId = common_files.convertToMongoObjectId;
const constant = require('../utility/constants');
const student_group = require('mongoose').model(constant.STUDENT_GROUP);
const institution = require('mongoose').model(constant.INSTITUTION);
// const program = require('mongoose').model(constant.PROGRAM);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const session_order = require('mongoose').model(constant.DIGI_SESSION_ORDER);
// const session_type = require('mongoose').model(constant.SESSION_TYPE);
const session_type = require('mongoose').model(constant.DIGI_SESSION_DELIVERY_TYPES);
const user = require('mongoose').model(constant.USER);
const {
    removeStudentSchedule,
    addingStudentSchedule,
    updateStudentGroupFlatCacheData,
} = require('./student_group_services');

exports.dashboard = async (req, res) => {
    try {
        const program_check = await base_control.get(
            program,
            { _id: ObjectId(req.params.program), isDeleted: false },
            {},
        );
        if (!program_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        const institution_calendar_check = await base_control.get(
            institution_calendar,
            { _id: ObjectId(req.params.institution), isDeleted: false },
            {},
        );
        //Check Calendar Publish Status
        const program_calendar_check = await base_control.get(
            program_calendar,
            {
                _program_id: ObjectId(req.params.program),
                _institution_calendar_id: ObjectId(req.params.institution),
                status: 'published',
                isDeleted: false,
            },
            {},
        );
        if (!program_calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t(
                            'THERE_IS_NO_PROGRAM_CALENDAR_STILL_PUBLISHED_IN_CURRENT_ACADEMIC_YEAR',
                        ),
                        req.t(
                            'THERE_IS_NO_PROGRAM_CALENDAR_STILL_PUBLISHED_IN_CURRENT_ACADEMIC_YEAR',
                        ),
                    ),
                );
        const student_group_check = await base_control.get_list(
            student_group,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                'master._program_id': ObjectId(req.params.program),
                _institution_calendar_id: ObjectId(req.params.institution),
                isDeleted: false,
            },
            {},
        );
        const delivery_type = await base_control.get_list(
            session_type,
            {},
            {
                session_name: 1,
                session_symbol: 1,
                delivery_types: 1,
            },
        );
        if (!student_group_check.status) {
            const year_levels = [];
            const years = [];
            let levels = [];
            const yrs = [];
            let crs_data = [];
            program_calendar_check.data.level.forEach((element) => {
                year_levels.push({
                    year: element.year,
                    level_no: element.level_no,
                    term: element.term,
                    curriculum: element.curriculum,
                    rotation: element.rotation,
                    rotation_count: element.rotation_count,
                    rotation_course: element.rotation_course,
                    course: element.course,
                });
            });
            for (const element of year_levels) {
                levels = [];
                if (yrs.indexOf(element.year) === -1) {
                    for (const sub_element of year_levels) {
                        if (element.year === sub_element.year) {
                            let course_data;
                            if (sub_element.rotation === 'yes' && sub_element.rotation_course) {
                                course_data = sub_element.rotation_course[0].course;
                            } else {
                                course_data = sub_element.course;
                            }
                            crs_data = [];
                            const courses_ids = course_data.map((i) => i._course_id);
                            const course_session_order = await base_control.get_list(
                                session_order,
                                { ...common_files.query, _course_id: courses_ids },
                                {
                                    _course_id: 1,
                                    'session_flow_data._session_id': 1,
                                    'session_flow_data.delivery_symbol': 1,
                                },
                            );
                            for (course_element of course_data) {
                                const delivery = [];
                                if (course_session_order.status) {
                                    const cso = [];
                                    for (course_session_element of course_session_order.data) {
                                        course_session_element.session_flow_data.forEach(
                                            (cso_element) => {
                                                if (
                                                    course_session_element._course_id.toString() ===
                                                    course_element._course_id.toString()
                                                )
                                                    cso.push({
                                                        _session_id: cso_element._session_id,
                                                        delivery_symbol:
                                                            cso_element.delivery_symbol,
                                                    });
                                            },
                                        );
                                    }
                                    const symbol = cso.filter(
                                        (thing, index, self) =>
                                            index ===
                                            self.findIndex(
                                                (t) => t.delivery_symbol === thing.delivery_symbol,
                                            ),
                                    );
                                    symbol.forEach((sym_element) => {
                                        const delivery_data =
                                            delivery_type.data[
                                                delivery_type.data.findIndex(
                                                    (i) =>
                                                        i._id.toString() ===
                                                        sym_element._session_id.toString(),
                                                )
                                            ];
                                        const delivery_loc = delivery_data.delivery_types.findIndex(
                                            (i) =>
                                                i.delivery_symbol === sym_element.delivery_symbol,
                                        );
                                        console.log(
                                            'Student Group Delivery validation ',
                                            sym_element.delivery_symbol,
                                            delivery_loc,
                                        );
                                        delivery.push({
                                            session_type: delivery_data.session_name,
                                            name: delivery_data.delivery_types[delivery_loc]
                                                .delivery_name,
                                            symbol: delivery_data.delivery_types[delivery_loc]
                                                .delivery_symbol,
                                        });
                                    });
                                }
                                crs_data.push({
                                    _course_id: course_element._course_id,
                                    course_name: course_element.courses_name,
                                    course_no: course_element.courses_number,
                                    course_type: course_element.model,
                                    session_types: delivery,
                                    versionNo: course_element.versionNo || 1,
                                    versioned: course_element.versioned || false,
                                    versionName: course_element.versionName || '',
                                    versionedFrom: course_element.versionedFrom || null,
                                });
                            }
                            const group_name =
                                institution_calendar_check.data.calendar_name +
                                '-' +
                                program_check.data.name.substring(0, 1) +
                                'P' +
                                '-' +
                                // (sub_element.term.toLowerCase() === 'regular' ? 'RT' : 'IT') +
                                (sub_element.term.substring(0, 1).toUpperCase() + 'T') +
                                '-' +
                                sub_element.year.replace('year', '') +
                                'Y' +
                                '-' +
                                program_check.data.name.substring(0, 1) +
                                'P:' +
                                sub_element.curriculum +
                                '-' +
                                sub_element.level_no.replace('Level ', '') +
                                'L';
                            let group_mode = 'course';
                            if (
                                program_check.data.program_type ===
                                constant.PROGRAM_TYPE.PREREQUISITE
                            ) {
                                group_mode = 'foundation';
                            }
                            if (sub_element.rotation === 'yes') {
                                group_mode = 'rotation';
                            }
                            levels.push({
                                group_name,
                                group_mode,
                                level: sub_element.level_no,
                                term: sub_element.term,
                                curriculum: sub_element.curriculum,
                                rotation: sub_element.rotation,
                                rotation_count: sub_element.rotation_count,
                                courses: crs_data,
                            });
                        }
                    }
                    yrs.push(element.year);
                    years.push({
                        insertOne: {
                            document: {
                                _institution_id: req.headers._institution_id,
                                _institution_calendar_id: ObjectId(req.params.institution),
                                master: {
                                    _program_id: program_check.data._id,
                                    program_no: program_check.data.code,
                                    program_name: program_check.data.name,
                                    year: element.year,
                                },
                                groups: levels,
                            },
                        },
                    });
                }
            }
            const data_create = await base_control.bulk_write(student_group, years);
            if (!data_create.status)
                return res
                    .status(410)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('STUDENT_GROUP_CREATIONS'),
                            data_create.data,
                        ),
                    );
            updateStudentGroupFlatCacheData();
        } else {
            // Program Calendar Newly Added Course Check
            const programCalendarCourses = [];
            for (levelElement of common_files.clone(program_calendar_check.data.level)) {
                if (levelElement.rotation && levelElement.rotation === 'yes') {
                    for (rotationElement of levelElement.rotation_course) {
                        for (courseElement of rotationElement.course) {
                            programCalendarCourses.push({
                                level_no: levelElement.level_no,
                                term: levelElement.term,
                                rotation: levelElement.rotation,
                                rotation_count: rotationElement.rotation_count,
                                _course_id: courseElement._course_id,
                                courses_name: courseElement.courses_name,
                                courses_number: courseElement.courses_number,
                                versionNo: courseElement.versionNo || 1,
                                versioned: courseElement.versioned || false,
                                versionName: courseElement.versionName || '',
                                versionedFrom: courseElement.versionedFrom || null,
                                model: courseElement.model,
                            });
                        }
                    }
                } else
                    for (courseElement of levelElement.course) {
                        programCalendarCourses.push({
                            level_no: levelElement.level_no,
                            term: levelElement.term,
                            _course_id: courseElement._course_id,
                            courses_name: courseElement.courses_name,
                            courses_number: courseElement.courses_number,
                            versionNo: courseElement.versionNo || 1,
                            versioned: courseElement.versioned || false,
                            versionName: courseElement.versionName || '',
                            versionedFrom: courseElement.versionedFrom || null,
                            model: courseElement.model,
                        });
                    }
            }
            const newlyAddedCourses = [];
            const newlyAddedCourseIds = [];
            for (studentGroupElement of common_files.clone(student_group_check.data)) {
                for (groupElement of studentGroupElement.groups) {
                    for (programCalendarCourseElement of programCalendarCourses) {
                        if (
                            programCalendarCourseElement.level_no.toString() ===
                                groupElement.level.toString() &&
                            programCalendarCourseElement.term === groupElement.term
                        ) {
                            if (
                                !groupElement.courses.find(
                                    (courseElement) =>
                                        programCalendarCourseElement._course_id.toString() ===
                                        courseElement._course_id.toString(),
                                ) &&
                                !newlyAddedCourseIds.includes(
                                    programCalendarCourseElement._course_id.toString(),
                                )
                            ) {
                                newlyAddedCourses.push({
                                    ...programCalendarCourseElement,
                                    studentGroupId: studentGroupElement._id,
                                });
                                newlyAddedCourseIds.push(
                                    programCalendarCourseElement._course_id.toString(),
                                );
                            }
                        }
                    }
                }
            }
            if (newlyAddedCourses.length) {
                const bulkWriteData = [];
                const courseSessionOrders = await session_order.find(
                    {
                        _course_id: {
                            $in: newlyAddedCourses.map((courseElement) =>
                                common_files.convertToMongoObjectId(courseElement._course_id),
                            ),
                        },
                        isDeleted: false,
                        isActive: true,
                    },
                    {
                        _course_id: 1,
                        'session_flow_data._session_id': 1,
                        'session_flow_data.delivery_symbol': 1,
                    },
                );
                for (courseElement of newlyAddedCourses) {
                    const delivery = [];
                    if (courseSessionOrders) {
                        const cso = [];
                        for (course_session_element of courseSessionOrders) {
                            course_session_element.session_flow_data.forEach((cso_element) => {
                                if (
                                    course_session_element._course_id.toString() ===
                                    courseElement._course_id.toString()
                                )
                                    cso.push({
                                        _session_id: cso_element._session_id,
                                        delivery_symbol: cso_element.delivery_symbol,
                                    });
                            });
                        }
                        const symbol = cso.filter(
                            (thing, index, self) =>
                                index ===
                                self.findIndex((t) => t.delivery_symbol === thing.delivery_symbol),
                        );
                        symbol.forEach((sym_element) => {
                            const delivery_data =
                                delivery_type.data[
                                    delivery_type.data.findIndex(
                                        (i) =>
                                            i._id.toString() === sym_element._session_id.toString(),
                                    )
                                ];
                            const delivery_loc = delivery_data.delivery_types.findIndex(
                                (i) => i.delivery_symbol === sym_element.delivery_symbol,
                            );
                            delivery.push({
                                session_type: delivery_data.session_name,
                                name: delivery_data.delivery_types[delivery_loc].delivery_name,
                                symbol: delivery_data.delivery_types[delivery_loc].delivery_symbol,
                            });
                        });
                    }
                    bulkWriteData.push({
                        updateOne: {
                            filter: {
                                _id: common_files.convertToMongoObjectId(
                                    courseElement.studentGroupId,
                                ),
                            },
                            update: {
                                $push: {
                                    'groups.$[level].courses': {
                                        _course_id: courseElement._course_id,
                                        course_name: courseElement.courses_name,
                                        course_no: courseElement.courses_number,
                                        course_type: courseElement.model,
                                        versionNo: courseElement.versionNo,
                                        versioned: courseElement.versioned,
                                        versionName: courseElement.versionName,
                                        versionedFrom: courseElement.versionedFrom,
                                        session_types: delivery,
                                    },
                                    'groups.$[level].students.$[].course_group_status': {
                                        _course_id: courseElement._course_id,
                                        status: 'pending',
                                    },
                                },
                            },
                            arrayFilters: [
                                {
                                    'level.level': courseElement.level_no,
                                    'level.term': courseElement.term,
                                },
                            ],
                        },
                    });
                }
                if (bulkWriteData.length) await student_group.bulkWrite(bulkWriteData);
                updateStudentGroupFlatCacheData();
            }
            const courseData = [];
            let courseIds = [];
            for (sgYearElement of student_group_check.data) {
                for (groupElement of sgYearElement.groups) {
                    const emptySession = groupElement.courses.filter(
                        (ele) => ele.session_types.length === 0,
                    );
                    if (emptySession.length !== 0) {
                        courseIds = [...courseIds, ...emptySession.map((ele) => ele._course_id)];
                        courseData.push({
                            _id: sgYearElement._id,
                            _group_id: groupElement._id,
                            level: groupElement.level,
                            term: groupElement.term,
                            courses: emptySession,
                        });
                    }
                }
            }
            if (courseIds.length) {
                courseIds.forEach((ele, index) => {
                    courseIds[index] = ObjectId(ele);
                });
                const course_session_order = await base_control.get_list(
                    session_order,
                    { ...common_files.query, _course_id: { $in: courseIds } },
                    {
                        _course_id: 1,
                        'session_flow_data._session_id': 1,
                        'session_flow_data.delivery_symbol': 1,
                    },
                );
                const courseUpdateData = [];
                if (course_session_order.status) {
                    for (courseLevelElement of courseData) {
                        for (courseElement of courseLevelElement.courses) {
                            const courseSessions = course_session_order.data.find(
                                (ele) =>
                                    ele._course_id.toString() ===
                                    courseElement._course_id.toString(),
                            );
                            if (courseSessions) {
                                const delivery = [];
                                const cso = courseSessions.session_flow_data.map((cso_element) => {
                                    return {
                                        _session_id: cso_element._session_id,
                                        delivery_symbol: cso_element.delivery_symbol,
                                    };
                                });
                                const symbol = cso.filter(
                                    (thing, index, self) =>
                                        index ===
                                        self.findIndex(
                                            (t) => t.delivery_symbol === thing.delivery_symbol,
                                        ),
                                );
                                symbol.forEach((sym_element) => {
                                    const delivery_data =
                                        delivery_type.data[
                                            delivery_type.data.findIndex(
                                                (i) =>
                                                    i._id.toString() ===
                                                    sym_element._session_id.toString(),
                                            )
                                        ];
                                    const delivery_loc = delivery_data.delivery_types.findIndex(
                                        (i) => i.delivery_symbol === sym_element.delivery_symbol,
                                    );
                                    delivery.push({
                                        session_type: delivery_data.session_name,
                                        name: delivery_data.delivery_types[delivery_loc]
                                            .delivery_name,
                                        symbol: delivery_data.delivery_types[delivery_loc]
                                            .delivery_symbol,
                                    });
                                });
                                courseUpdateData.push({
                                    updateOne: {
                                        filter: {
                                            _id: ObjectId(courseLevelElement._id),
                                        },
                                        update: {
                                            $set: {
                                                'groups.$[level].courses.$[course].session_types':
                                                    delivery,
                                            },
                                        },
                                        arrayFilters: [
                                            { 'level._id': ObjectId(courseLevelElement._group_id) },
                                            {
                                                'course._course_id': ObjectId(
                                                    courseElement._course_id,
                                                ),
                                            },
                                        ],
                                    },
                                });
                            }
                        }
                    }
                }
                if (courseUpdateData.length) {
                    await base_control.bulk_write(student_group, courseUpdateData);
                    updateStudentGroupFlatCacheData();
                }
            }
        }
        const student_group_datas = await student_group
            .find({
                _institution_id: ObjectId(req.headers._institution_id),
                'master._program_id': ObjectId(req.params.program),
                _institution_calendar_id: ObjectId(req.params.institution),
                isDeleted: false,
            })
            .populate({
                path: 'groups.courses._course_id',
                select: {
                    versionedCourseIds: 1,
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                },
            })
            .lean();
        if (!student_group_datas || !student_group_datas.length)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('THERE_IS_NO_STUDENT_GROUP_IN_CURRENT_ACADEMIC_YEAR'),
                        req.t('THERE_IS_NO_STUDENT_GROUP_IN_CURRENT_ACADEMIC_YEAR'),
                    ),
                );
        let response = {};
        let levels = [];
        // student_group_datas.data.forEach((element) => {
        //     const regu = [];
        //     const inter = [];
        //     element.groups.forEach((sub_element) => {
        //         if (sub_element.term === 'regular')
        //             regu.push({
        //                 group_name: sub_element.group_name,
        //                 group_mode: sub_element.group_mode,
        //                 level: sub_element.level,
        //                 curriculum: sub_element.curriculum,
        //                 term: sub_element.term,
        //                 rotation: sub_element.rotation,
        //                 rotation_count: sub_element.rotation_count,
        //                 courses: sub_element.courses,
        //             });
        //         if (program_check.data.no_term !== 1) {
        //             if (sub_element.term === 'interim')
        //                 inter.push({
        //                     group_name: sub_element.group_name,
        //                     group_mode: sub_element.group_mode,
        //                     level: sub_element.level,
        //                     curriculum: sub_element.curriculum,
        //                     term: sub_element.term,
        //                     rotation: sub_element.rotation,
        //                     rotation_count: sub_element.rotation_count,
        //                     courses: sub_element.courses,
        //                 });
        //         }
        //     });
        //     const objs = {
        //         _id: element._id,
        //         year: element.master.year,
        //         regular: regu,
        //     };
        //     if (program_check.data.no_term !== 1) {
        //         const sorted = inter.sort((a, b) => {
        //             let comparison = 0;
        //             if (parseInt(a.level) > parseInt(b.level)) {
        //                 comparison = -1;
        //             } else if (parseInt(a.level) < parseInt(b.level)) {
        //                 comparison = 1;
        //             }
        //             return comparison;
        //         });
        //         Object.assign(objs, { interim: sorted });
        //     }
        //     levels.push(objs);
        // });
        student_group_datas.forEach((element) => {
            const objs = {
                _id: element._id,
                year: element.master.year,
                batch: [],
            };

            for (termElement of program_check.data.term) {
                const yearLevel = [];
                element.groups.forEach((sub_element) => {
                    if (sub_element.term.toLowerCase() === termElement.term_name.toLowerCase())
                        yearLevel.push({
                            group_name: sub_element.group_name,
                            group_mode: sub_element.group_mode,
                            level: sub_element.level,
                            curriculum: sub_element.curriculum,
                            term: sub_element.term,
                            rotation: sub_element.rotation,
                            rotation_count: sub_element.rotation_count,
                            courses: sub_element.courses.map((courseElement) => {
                                return {
                                    ...courseElement,
                                    versionedCourseIds:
                                        courseElement._course_id.versionedCourseIds || [],
                                    versionNo: courseElement._course_id.versionNo || 1,
                                    versioned: courseElement._course_id.versioned || false,
                                    versionName: courseElement._course_id.versionName || '',
                                    versionedFrom: courseElement._course_id.versionedFrom || null,
                                    _course_id: courseElement._course_id._id,
                                };
                            }),
                        });
                });
                objs.batch.push({
                    term: termElement.term_name,
                    level: yearLevel,
                });
            }
            levels.push(objs);
        });
        levels = levels.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.year) > parseInt(b.year)) {
                comparison = 1;
            } else if (parseInt(a.year) < parseInt(b.year)) {
                comparison = -1;
            }
            return comparison;
        });
        response = {
            program_name: program_check.data.name,
            // term: program_check.data.no_term !== 1 ? 'interim' : 'regular',
            term: program_check.data.term,
            year: levels,
        };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENT_GROUP_DASHBOARD'),
                    response,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    true,
                    req.t('STUDENT_GROUP_DASHBOARD_CRASHED'),
                    error.toString(),
                ),
            );
    }
};

exports.program_ic_list_dashboard = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const program_cri = [];
        const program_data = await base_control.get_list_sort(
            program,
            { isDeleted: false },
            { program_type: 1, name: 1, code: 1, no_term: 1, term: 1 },
            { name: -1 },
        );
        for (program_element of program_data.data) {
            // let term = false;
            // if (program_element.no_term && program_element.no_term !== 1) term = true;
            program_cri.push({
                _id: program_element._id,
                name: program_element.name,
                program_no: program_element.code,
                version: '-',
                term: program_element.term,
                program_type: program_element.program_type,
            });
        }
        const sorting = program_cri.sort((a) =>
            a.program_type.indexOf('pre_requisite') > -1 ? -1 : 1,
        );
        const ins_cal = await base_control.get_list_sort(
            institution_calendar,
            { isDeleted: false, calendar_type: constant.PRIMARY, status: constant.PUBLISHED },
            { calendar_name: 1, start_date: 1, end_date: 1 },
            { updatedAt: -1 },
        );
        const resp = {
            programs: sorting,
            institution_calendar: ins_cal.status ? ins_cal.data : [],
        };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENT_GROUP_PROGRAM_INSTITUTION_CALENDAR_LIST'),
                    resp,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    true,
                    req.t('STUDENT_GROUP_PROGRAM_INSTITUTION_CALENDAR_LIST_CRASHED'),
                    error.toString(),
                ),
            );
    }
};

exports.course_group_lists = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params._id),
            isDeleted: false,
        };
        const student_group_data = await base_control.get(student_group, query, {});
        if (!student_group_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const student_group_row = student_group_data.data.groups.findIndex(
            (i) =>
                i.term.toString() === req.params.batch.toString() &&
                i.level.toString() === req.params.level.toString(),
        );
        if (student_group_row === -1)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                        req.t('UNABLE_TO_FIND_LEVEL_OR_BATCH'),
                    ),
                );
        const course_loc = student_group_data.data.groups[student_group_row].courses.findIndex(
            (i) => i._course_id.toString() === req.params.course.toString(),
        );
        const course_datas = student_group_data.data.groups[student_group_row].courses[course_loc];
        const male_group = [];
        const female_group = [];
        const both_group = [];
        course_datas.setting.forEach((element) => {
            const session_delivery = [];
            element.session_setting.forEach((sub_element) => {
                const grps = [];
                for (let i = 1; i <= sub_element.no_of_group; i++) grps.push('G' + i);
                session_delivery.push({
                    session_type: sub_element.session_type,
                    groups: grps,
                });
            });
            if (element.gender === constant.GENDER.MALE) {
                male_group.push({
                    group_name: element._group_no,
                    delivery_groups: session_delivery,
                });
            } else if (element.gender === constant.GENDER.FEMALE) {
                female_group.push({
                    group_name: element._group_no,
                    delivery_groups: session_delivery,
                });
            } else if (element.gender === constant.GENDER.BOTH) {
                both_group.push({
                    group_name: element._group_no,
                    delivery_groups: session_delivery,
                });
            }
        });
        const resp = {
            _id: course_datas._id,
            _course_id: course_datas._course_id,
            course_name: course_datas.course_name,
            course_no: course_datas.course_no,
            course_type: course_datas.course_type,
            versionNo: course_datas.versionNo || 1,
            versioned: course_datas.versioned || false,
            versionName: course_datas.versionName || '',
            versionedFrom: course_datas.versionedFrom || null,
            session_types: course_datas.session_types,
            groups: {
                male: male_group,
                female: female_group,
                both: both_group,
            },
        };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENT_GROUP_PROGRAM_INSTITUTION_CALENDAR_LIST'),
                    resp,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    true,
                    req.t('STUDENT_GROUP_PROGRAM_INSTITUTION_CALENDAR_LIST_CRASHED'),
                    error.toString(),
                ),
            );
    }
};

exports.student_global_search = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const program_check = await base_control.get(
            program,
            { _id: ObjectId(req.params.program), isDeleted: false },
            {},
        );
        const institution_calendar_check = await base_control.get(
            institution_calendar,
            { _id: ObjectId(req.params.institution), isDeleted: false },
            {},
        );
        if (!program_check.status && !institution_calendar_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('ERROR_PROGRAM/INSTITUTION_CALENDAR_ID_NOT_MATCH'),
                        req.t('CHECK_PARSING_REFERENCE_ID'),
                    ),
                );
        // const student_group_data = await base_control.get_list(
        //     student_group,
        //     {
        //         _institution_id: ObjectId(req.headers._institution_id),
        //         'master._program_id': ObjectId(req.params.program),
        //         _institution_calendar_id: ObjectId(req.params.institution),
        //         isDeleted: false,
        //     },
        //     {},
        // );
        const studentGroupData = await student_group
            .find({
                _institution_id: ObjectId(req.headers._institution_id),
                'master._program_id': ObjectId(req.params.program),
                _institution_calendar_id: ObjectId(req.params.institution),
                isDeleted: false,
            })
            .populate({
                path: 'groups.courses._course_id',
                select: {
                    versionNo: 1,
                    versioned: 1,
                    versionName: 1,
                    versionedFrom: 1,
                    versionedCourseIds: 1,
                },
            });
        if (!studentGroupData || !studentGroupData.length)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const query = {
            user_id: req.params.academic_no,
            user_type: constant.EVENT_WHOM.STUDENT,
            isDeleted: false,
            isActive: true,
        };
        const user_data = await base_control.get(user, query, {});
        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    ),
                );
        const levels = [];
        studentGroupData.forEach((master_element) => {
            master_element.groups.forEach((element) => {
                if (
                    element.students.findIndex(
                        (i) => i._student_id.toString() === user_data.data._id.toString(),
                    ) !== -1
                ) {
                    const level_data = {
                        _id: master_element._id,
                        year: master_element.master.year,
                        level: element.level,
                        term: element.term,
                        mode: element.group_mode,
                    };
                    let master_group_flag = false;
                    let individual_flag = false;
                    let ungrouped_flag = false;
                    if (
                        element.group_mode === constant.STUDENT_GROUP_MODE.FYD ||
                        element.group_mode === constant.STUDENT_GROUP_MODE.ROTATION
                    ) {
                        const grps = [];
                        master_group_flag = element.ungrouped.indexOf(user_data.data._id) !== -1;
                        if (element.group_mode === constant.STUDENT_GROUP_MODE.FYD) {
                            const group_setting =
                                element.group_setting[
                                    element.group_setting.findIndex(
                                        (i) => i.gender === user_data.data.gender,
                                    )
                                ];
                            if (group_setting)
                                group_setting.groups.forEach((group_element) => {
                                    grps.push({
                                        group_no: group_element.group_no,
                                        present:
                                            group_element._student_ids.indexOf(
                                                user_data.data._id.toString(),
                                            ) !== -1,
                                    });
                                });
                            Object.assign(level_data, { master_groups: grps });
                        } else {
                            element.rotation_group_setting.forEach((group_element) => {
                                if (group_element.gender === user_data.data.gender)
                                    grps.push({
                                        group_no: group_element.group_no,
                                        present:
                                            group_element._student_ids.indexOf(
                                                user_data.data._id.toString(),
                                            ) !== -1,
                                    });
                            });
                            Object.assign(level_data, { master_groups: grps });
                        }
                        const course_data = [];
                        element.courses.forEach((course_element) => {
                            if (course_element.course_type !== 'elective') {
                                const session_delivery = [];
                                let flag = false;
                                let ind_group_no = -1;
                                ungrouped_flag = false;
                                if (level_data.master_groups)
                                    individual_flag =
                                        level_data.master_groups.findIndex(
                                            (i) => i.present === true,
                                        ) === -1;
                                grps.forEach((grp_element) => {
                                    course_element.setting.forEach((c_setting_element) => {
                                        // // Need to add ungrouped data
                                        if (
                                            c_setting_element.gender.toString() ===
                                                user_data.data.gender.toString() &&
                                            grp_element.group_no.toString() ===
                                                c_setting_element._group_no.toString()
                                        ) {
                                            if (
                                                !ungrouped_flag &&
                                                c_setting_element.ungrouped.indexOf(
                                                    user_data.data._id.toString(),
                                                ) !== -1
                                            ) {
                                                ungrouped_flag = true;
                                            }
                                            let delivery_groups = [];
                                            c_setting_element.session_setting.forEach(
                                                (c_session_element) => {
                                                    delivery_groups = [];
                                                    c_session_element.groups.forEach(
                                                        (c_gru_element) => {
                                                            delivery_groups.push({
                                                                group_no: c_gru_element.group_no,
                                                                present:
                                                                    c_gru_element._student_ids.indexOf(
                                                                        user_data.data._id.toString(),
                                                                    ) !== -1,
                                                            });
                                                            if (
                                                                c_gru_element._student_ids.indexOf(
                                                                    user_data.data._id,
                                                                ) !== -1
                                                            ) {
                                                                flag = true;
                                                                ind_group_no = grp_element.group_no; // ungrouped_flag = false;
                                                            }
                                                        },
                                                    );
                                                    // console.log(individual_flag, ' ', !master_group_flag, ' ', ind_group_no, ' ', grp_element.group_no);
                                                    if (individual_flag && !master_group_flag) {
                                                        if (
                                                            ind_group_no.toString() ===
                                                            grp_element.group_no.toString()
                                                        )
                                                            session_delivery.push({
                                                                master_group_no:
                                                                    grp_element.group_no,
                                                                session_type:
                                                                    c_session_element.session_type,
                                                                groups: delivery_groups,
                                                            });
                                                    } else {
                                                        session_delivery.push({
                                                            master_group_no: grp_element.group_no,
                                                            session_type:
                                                                c_session_element.session_type,
                                                            groups: delivery_groups,
                                                        });
                                                    }
                                                },
                                            );
                                        }
                                    });
                                });
                                // console.log(individual_flag, ' ', session_delivery.length, ' ', flag, ' ', ungrouped_flag);
                                if (individual_flag) {
                                    if (session_delivery.length !== 0 && (flag || ungrouped_flag)) {
                                        const cro_data = {
                                            _course_id: course_element._course_id?._id,
                                            course_name: course_element.course_name,
                                            course_no: course_element.course_no,
                                            course_type: course_element.course_type,
                                            versionNo: course_element._course_id?.versionNo || 1,
                                            versioned:
                                                course_element._course_id?.versioned || false,
                                            versionName:
                                                course_element._course_id?.versionName || '',
                                            versionedFrom:
                                                course_element._course_id?.versionedFrom || null,
                                            versionedCourseIds:
                                                course_element._course_id?.versionedCourseIds || [],
                                        };
                                        if (flag)
                                            Object.assign(cro_data, { delivery: session_delivery });
                                        Object.assign(cro_data, { ungrouped: ungrouped_flag });
                                        course_data.push(cro_data);
                                    } else if (ungrouped_flag) {
                                        const cro_data = {
                                            _course_id: course_element._course_id?._id,
                                            course_name: course_element.course_name,
                                            course_no: course_element.course_no,
                                            course_type: course_element.course_type,
                                            versionNo: course_element._course_id?.versionNo || 1,
                                            versioned:
                                                course_element._course_id?.versioned || false,
                                            versionName:
                                                course_element._course_id?.versionName || '',
                                            versionedFrom:
                                                course_element._course_id?.versionedFrom || null,
                                            versionedCourseIds:
                                                course_element._course_id?.versionedCourseIds || [],
                                        };
                                        if (flag)
                                            Object.assign(cro_data, { delivery: session_delivery });
                                        Object.assign(cro_data, { ungrouped: ungrouped_flag });
                                        course_data.push(cro_data);
                                    }
                                } else {
                                    if (session_delivery.length !== 0 || flag || ungrouped_flag) {
                                        const cro_data = {
                                            _course_id: course_element._course_id?._id,
                                            course_name: course_element.course_name,
                                            course_no: course_element.course_no,
                                            course_type: course_element.course_type,
                                            versionNo: course_element._course_id?.versionNo || 1,
                                            versioned:
                                                course_element._course_id?.versioned || false,
                                            versionName:
                                                course_element._course_id?.versionName || '',
                                            versionedFrom:
                                                course_element._course_id?.versionedFrom || null,
                                            versionedCourseIds:
                                                course_element._course_id?.versionedCourseIds || [],
                                        };
                                        if (flag)
                                            Object.assign(cro_data, { delivery: session_delivery });
                                        Object.assign(cro_data, { ungrouped: ungrouped_flag });
                                        course_data.push(cro_data);
                                    }
                                }
                            } else {
                                const session_delivery = [];
                                let flag = false;
                                ungrouped_flag = false;
                                course_element.setting.forEach((c_setting_element) => {
                                    if (c_setting_element.gender === user_data.data.gender) {
                                        // Need to add ungrouped data
                                        if (
                                            !ungrouped_flag &&
                                            c_setting_element.ungrouped.indexOf(
                                                user_data.data._id,
                                            ) !== -1
                                        ) {
                                            ungrouped_flag = true;
                                        }
                                        let delivery_groups = [];
                                        c_setting_element.session_setting.forEach(
                                            (c_session_element) => {
                                                delivery_groups = [];
                                                c_session_element.groups.forEach(
                                                    (c_gru_element) => {
                                                        delivery_groups.push({
                                                            group_no: c_gru_element.group_no,
                                                            present:
                                                                c_gru_element._student_ids.indexOf(
                                                                    user_data.data._id,
                                                                ) !== -1,
                                                        });
                                                        if (
                                                            c_gru_element._student_ids.indexOf(
                                                                user_data.data._id,
                                                            ) !== -1
                                                        )
                                                            flag = true;
                                                    },
                                                );
                                                session_delivery.push({
                                                    session_type: c_session_element.session_type,
                                                    groups: delivery_groups,
                                                });
                                            },
                                        );
                                    }
                                });
                                if (session_delivery.length !== 0 && (flag || ungrouped_flag)) {
                                    const cro_data = {
                                        _course_id: course_element._course_id?._id,
                                        course_name: course_element.course_name,
                                        course_no: course_element.course_no,
                                        course_type: course_element.course_type,
                                        versionNo: course_element._course_id?.versionNo || 1,
                                        versioned: course_element._course_id?.versioned || false,
                                        versionName: course_element._course_id?.versionName || '',
                                        versionedFrom:
                                            course_element._course_id?.versionedFrom || null,
                                        versionedCourseIds:
                                            course_element._course_id?.versionedCourseIds || [],
                                    };
                                    if (flag)
                                        Object.assign(cro_data, { delivery: session_delivery });
                                    Object.assign(cro_data, { ungrouped: ungrouped_flag });
                                    course_data.push(cro_data);
                                }
                            }
                        });
                        if (course_data.length !== 0)
                            Object.assign(level_data, { courses: course_data });
                    } else {
                        const course_data = [];
                        element.courses.forEach((course_element) => {
                            const session_delivery = [];
                            let flag = false;
                            ungrouped_flag = false;
                            course_element.setting.forEach((c_setting_element) => {
                                if (c_setting_element.gender === user_data.data.gender) {
                                    // Need to add ungrouped data
                                    if (
                                        !ungrouped_flag &&
                                        c_setting_element.ungrouped.indexOf(
                                            user_data.data._id.toString(),
                                        ) !== -1
                                    ) {
                                        ungrouped_flag = true;
                                    }
                                    let delivery_groups = [];
                                    c_setting_element.session_setting.forEach(
                                        (c_session_element) => {
                                            delivery_groups = [];
                                            c_session_element.groups.forEach((c_gru_element) => {
                                                delivery_groups.push({
                                                    group_no: c_gru_element.group_no,
                                                    present:
                                                        c_gru_element._student_ids.indexOf(
                                                            user_data.data._id,
                                                        ) !== -1,
                                                });
                                                if (
                                                    c_gru_element._student_ids.indexOf(
                                                        user_data.data._id,
                                                    ) !== -1
                                                )
                                                    flag = true;
                                            });
                                            session_delivery.push({
                                                session_type: c_session_element.session_type,
                                                groups: delivery_groups,
                                            });
                                        },
                                    );
                                }
                            });
                            if (session_delivery.length !== 0 && (flag || ungrouped_flag)) {
                                const cro_data = {
                                    _course_id: course_element._course_id?._id,
                                    course_name: course_element.course_name,
                                    course_no: course_element.course_no,
                                    course_type: course_element.course_type,
                                    versionNo: course_element._course_id?.versionNo || 1,
                                    versioned: course_element._course_id?.versioned || false,
                                    versionName: course_element._course_id?.versionName || '',
                                    versionedFrom: course_element._course_id?.versionedFrom || null,
                                    versionedCourseIds:
                                        course_element._course_id?.versionedCourseIds || [],
                                };
                                if (flag) Object.assign(cro_data, { delivery: session_delivery });
                                Object.assign(cro_data, { ungrouped: ungrouped_flag });
                                course_data.push(cro_data);
                            }
                        });
                        if (course_data.length !== 0)
                            Object.assign(level_data, { courses: course_data });
                    }
                    // console.log(individual_flag, ' ', element.ungrouped.findIndex(i => (i).toString() === (user_data.data._id).toString()));
                    if (
                        individual_flag &&
                        level_data.master_groups &&
                        element.ungrouped.findIndex(
                            (i) => i.toString() === user_data.data._id.toString(),
                        ) === -1
                    )
                        delete level_data.master_groups;
                    // if (level_data.courses)
                    Object.assign(level_data, { master_ungrouped: master_group_flag });
                    levels.push(level_data);
                }
            });
        });
        const resp = {
            _id: user_data.data._id,
            academic_no: user_data.data.user_id,
            name: user_data.data.name,
            gender: user_data.data.gender,
            term: user_data.data.batch || '',
            levels,
        };
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENT_SEARCH'),
                    resp,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    true,
                    req.t('STUDENT_SEARCH'),
                    error.toString(),
                ),
            );
    }
};

exports.student_global_edit = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const user_query = {
            _id: ObjectId(req.body._student_id),
            user_type: constant.EVENT_WHOM.STUDENT,
            isDeleted: false,
            isActive: true,
        };
        const user_data = await base_control.get(user, user_query, {});
        if (!user_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                        req.t('STUDENT_ACADEMIC_NO_NOT_FOUND'),
                    ),
                );
        let obj = { status: true, data: [] };
        let objs;
        let filter;
        let query;
        let excess = 0;
        let capacity = 0;
        let strength = 0;
        const dat = [];
        let loop_index = 0;
        for (element of req.body.data) {
            console.log(element);
            query = {
                _institution_id: ObjectId(req.headers._institution_id),
                _id: ObjectId(element._id),
                isDeleted: false,
            };
            const student_group_data = await base_control.get(student_group, query, {});
            const group_loc = student_group_data.data.groups.findIndex(
                (i) => i.term === element.batch && i.level.toString() === element.level.toString(),
            );
            if (group_loc === -1)
                return res
                    .status(400)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            400,
                            false,
                            req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                            req.t('CHECK_LEVEL_NO_ITS_WRONG'),
                        ),
                    );
            if (element.mode === constant.STUDENT_GROUP_MODE.FYD) {
                const setting_loc = student_group_data.data.groups[
                    group_loc
                ].group_setting.findIndex((i) => i.gender === user_data.data.gender);
                if (setting_loc === -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                                req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                            ),
                        );
                setting_group_loc = student_group_data.data.groups[group_loc].group_setting[
                    setting_loc
                ].groups.findIndex((i) => parseInt(i.group_no) === parseInt(element.new_group_no));
                if (setting_group_loc === -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('STUDENT_GROUP_NOT_FOUND_ON_NEW_GROUP'),
                                req.t('STUDENT_GROUP_NOT_FOUND_ON_NEW_GROUP'),
                            ),
                        );
                capacity =
                    student_group_data.data.groups[group_loc].group_setting[setting_loc]
                        .no_of_student;
                strength =
                    student_group_data.data.groups[group_loc].group_setting[setting_loc].groups[
                        setting_group_loc
                    ]._student_ids.length + 1;
                excess = student_group_data.data.groups[group_loc].group_excess_count
                    ? student_group_data.data.groups[group_loc].group_excess_count
                    : 0;
                if (!(strength <= excess + capacity))
                    return res
                        .status(410)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                410,
                                false,
                                req.t(
                                    'STUDENT_CAPACITY_EXCEEDED_IN_FOUNDATION_GROUP_INCREASE_EXTRA_ALLOWED',
                                ),
                                req.t(
                                    'STUDENT_CAPACITY_EXCEEDED_IN_FOUNDATION_GROUP_INCREASE_EXTRA_ALLOWED',
                                ),
                            ),
                        );

                //Check is this student in that particular group
                const check_locs = student_group_data.data.groups[group_loc].group_setting[
                    setting_loc
                ].groups.findIndex((i) => parseInt(i.group_no) === parseInt(element.old_group_no));
                const un_group_checks = student_group_data.data.groups[
                    group_loc
                ].ungrouped.findIndex((i) => i.toString() === req.body._student_id.toString());
                if (check_locs !== -1) {
                    if (
                        student_group_data.data.groups[group_loc].group_setting[setting_loc].groups[
                            check_locs
                        ]._student_ids.findIndex(
                            (i) => i.toString() === req.body._student_id.toString(),
                        ) !== -1
                    ) {
                        objs = {
                            $pull: {
                                'groups.$[k].ungrouped': ObjectId(req.body._student_id),
                                'groups.$[k].group_setting.$[i].groups.$[j]._student_ids': ObjectId(
                                    req.body._student_id,
                                ),
                            },
                            $push: {
                                'groups.$[k].group_setting.$[i].groups.$[l]._student_ids': ObjectId(
                                    req.body._student_id,
                                ),
                            },
                        };
                        filter = {
                            arrayFilters: [
                                { 'k.term': element.batch, 'k.level': element.level },
                                { 'i.gender': user_data.data.gender },
                                { 'j.group_no': parseInt(element.old_group_no) },
                                { 'l.group_no': parseInt(element.new_group_no) },
                            ],
                        };
                        // dat.push({ objs: objs, filters: filter });
                        obj = await base_control.update_condition_array_filter(
                            student_group,
                            query,
                            objs,
                            filter,
                        );
                    }
                } else if (un_group_checks !== -1) {
                    objs = {
                        $pull: {
                            'groups.$[k].ungrouped': ObjectId(req.body._student_id),
                            // 'groups.$[k].group_setting.$[i].groups.$[j]._student_ids': req.body._student_id
                        },
                        $push: {
                            'groups.$[k].group_setting.$[i].groups.$[l]._student_ids': ObjectId(
                                req.body._student_id,
                            ),
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'k.term': element.batch, 'k.level': element.level },
                            { 'i.gender': user_data.data.gender },
                            // { 'j.group_no': element.old_group_no },
                            { 'l.group_no': parseInt(element.new_group_no) },
                        ],
                    };
                    obj = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                }

                //Need to removes student from course also
                let doc = { status: true, data: [] };
                for (const course_element of student_group_data.data.groups[group_loc].courses) {
                    const gro =
                        course_element.setting[
                            course_element.setting.findIndex(
                                (i) =>
                                    i._group_no.toString() === element.old_group_no.toString() &&
                                    i.gender === user_data.data.gender,
                            )
                        ];
                    if (gro) {
                        if (gro.ungrouped.indexOf(req.body._student_id.toString()) !== -1) {
                            objs = {
                                $pull: {
                                    'groups.$[k].courses.$[i].setting.$[j].ungrouped': ObjectId(
                                        req.body._student_id,
                                    ),
                                },
                                $push: {
                                    'groups.$[k].courses.$[i].setting.$[l].ungrouped': ObjectId(
                                        req.body._student_id,
                                    ),
                                },
                            };
                            filter = {
                                arrayFilters: [
                                    { 'k.term': element.batch, 'k.level': element.level },
                                    { 'i._course_id': ObjectId(course_element._course_id) },
                                    {
                                        'j.gender': user_data.data.gender,
                                        'j._group_no': user_data.data.gender(element.old_group_no),
                                    },
                                    {
                                        'l.gender': user_data.data.gender,
                                        'l._group_no': user_data.data.gender(element.new_group_no),
                                    },
                                ],
                            };
                            doc = await base_control.update_condition_array_filter(
                                student_group,
                                query,
                                objs,
                                filter,
                            );
                        } else {
                            gro.session_setting.forEach(async (sub_element) => {
                                sub_element.groups.forEach(async (group_element) => {
                                    const stud_id =
                                        group_element._student_ids[
                                            group_element._student_ids.findIndex(
                                                (i) =>
                                                    i.toString() ===
                                                    req.body._student_id.toString(),
                                            )
                                        ];
                                    if (stud_id) {
                                        objs = {
                                            $pull: {
                                                'groups.$[k].courses.$[i].setting.$[j].session_setting.$[n].groups.$[l]._student_ids':
                                                    ObjectId(req.body._student_id),
                                            },
                                        };
                                        filter = {
                                            arrayFilters: [
                                                {
                                                    'k.term': element.batch,
                                                    'k.level': element.level,
                                                },
                                                {
                                                    'i._course_id': ObjectId(
                                                        course_element._course_id,
                                                    ),
                                                },
                                                {
                                                    'j._group_no': parseInt(gro._group_no),
                                                    'j.gender': user_data.data.gender,
                                                },
                                                { 'n.session_type': sub_element.session_type },
                                                { 'l.group_no': parseInt(group_element.group_no) },
                                                // { 'm.gender': user_data.data.gender, 'm._group_no': element.new_group_no }
                                            ],
                                        };
                                        // dat.push({ objs: objs, filters: filter });
                                        doc = await base_control.update_condition_array_filter(
                                            student_group,
                                            query,
                                            objs,
                                            filter,
                                        );
                                        console.log(doc);
                                    }
                                });
                            });
                            // objs = {
                            //     $push: { 'groups.$[k].courses.$[i].setting.$[m].ungrouped': req.body._student_id }
                            // };
                            // filter = {
                            //     arrayFilters: [
                            //         { 'k.term': element.batch, 'k.level': element.level },
                            //         { 'i._course_id': element._course_id },
                            //         { 'm.gender': user_data.data.gender, 'm._group_no': element.new_group_no }
                            //     ]
                            // };
                            // dat.push({ objs: objs, filters: filter });
                            // doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
                        }
                        // Course Schedule Changes
                        await removeStudentSchedule(
                            student_group_data.data._institution_id,
                            student_group_data.data._institution_calendar_id,
                            element.batch,
                            element.level,
                            course_element._course_id,
                            [req.body._student_id],
                        );
                    }
                }
                // return res.send(dat);
                //Notify User about group change
                const student_loc_status = student_group_data.data.groups[
                    group_loc
                ].students.findIndex(
                    (i) => i._student_id.toString() === req.body._student_id.toString(),
                );
                if (
                    student_group_data.data.groups[group_loc].students[student_loc_status]
                        .master_group_status !== 'pending'
                ) {
                    const institution_calendar_data = await base_control.get(
                        institution_calendar,
                        {
                            _id: ObjectId(student_group_data.data._institution_calendar_id),
                            isDeleted: false,
                        },
                        { calendar_name: 1 },
                    );
                    const level_name =
                        student_group_data.data.groups[group_loc].rotation === 'yes'
                            ? 'Rotation'
                            : 'Foundation';
                    // let user_data = await base_control.get(user, { _id: ObjectId(req.body._student_id), user_type: constant.EVENT_WHOM.STUDENT, 'isDeleted': false }, { name: 1, user_id: 1, email: 1, mobile: 1 });
                    const email_message =
                        '<p>Dear v_name,<br>' +
                        common_fun.emailGreetingContent() +
                        'This email is to inform  that your ' +
                        level_name +
                        ' year group for the academic year <b>v_academic</b> has been changed as <b>' +
                        level_name +
                        ' group group_no</b>. For more details, kindly visit the Academic Affairs Office.<br><br>' +
                        'Thank you, <br>' +
                        common_fun.emailRegardsContent() +
                        '</p>';
                    const sms_message =
                        'Your ' +
                        level_name +
                        ' group is changed. Please check your registered email for details. For any query, visit Academic Affairs Office';
                    const name = user_data.data.name.middle
                        ? user_data.data.name.first +
                          ' ' +
                          user_data.data.name.middle +
                          ' ' +
                          user_data.data.name.last
                        : user_data.data.name.first + ' ' + user_data.data.name.last;
                    const replace_data = {
                        v_name: name,
                        v_academic: institution_calendar_data.data.calendar_name,
                        group_no: element.new_group_no,
                    };
                    const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                    const message = email_message.replace(re, (matched) => replace_data[matched]);
                    if (user_data.data.email !== undefined && user_data.data.email !== '')
                        common_fun.send_email(
                            user_data.data.email,
                            'DigiClass Alert - Your ' + level_name + ' Groups Change',
                            message,
                        );
                    const msg = 'DigiClass - ' + sms_message;
                    if (user_data.data.mobile !== undefined && user_data.data.mobile !== '')
                        common_fun.send_sms(user_data.data.mobile, msg);
                }
            } else if (element.mode === constant.STUDENT_GROUP_MODE.COURSE) {
                const student_group_course = student_group_data.data.groups[
                    group_loc
                ].courses.findIndex(
                    (i) => i._course_id.toString() === element._course_id.toString(),
                );
                if (student_group_course === -1)
                    return res
                        .status(400)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                400,
                                false,
                                req.t('COURSE_NOT_FOUND'),
                                req.t('COURSE_NOT_FOUND'),
                            ),
                        );
                let student_master_group;
                if (element.master_group) {
                    student_master_group = student_group_data.data.groups[group_loc].courses[
                        student_group_course
                    ].setting.findIndex(
                        (i) =>
                            parseInt(i._group_no) === parseInt(element.master_group) &&
                            i.gender === user_data.data.gender,
                    );
                } else {
                    student_master_group = student_group_data.data.groups[group_loc].courses[
                        student_group_course
                    ].setting.findIndex((i) => i.gender === user_data.data.gender);
                }
                if (student_master_group === -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('NO_SESSION_SETTINGS_ADDED'),
                                req.t('NO_SESSION_SETTINGS_ADDED'),
                            ),
                        );
                const course_session_setting =
                    student_group_data.data.groups[group_loc].courses[student_group_course].setting[
                        student_master_group
                    ].session_setting;
                if (course_session_setting.length === 0)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('NO_SESSION_GROUPS_CREATED'),
                                req.t('NO_SESSION_GROUPS_CREATED'),
                            ),
                        );
                const validation = [];
                for (let index = 0; index < element.delivery_group.length; index++) {
                    const elements = element.delivery_group[index].session_type;
                    const grp_nos = element.delivery_group[index].group_no;
                    const session_type_ind =
                        course_session_setting[
                            course_session_setting.findIndex(
                                (i) =>
                                    i.session_type === element.delivery_group[index].session_type,
                            )
                        ];
                    if (session_type_ind === undefined)
                        return res
                            .status(404)
                            .send(
                                common_files.responseFunctionWithRequest(
                                    req,
                                    404,
                                    false,
                                    req.t('SESSION_TYPE_NOT_MATCHING'),
                                    req.t('SESSION_TYPE_NOT_MATCHING'),
                                ),
                            );
                    capacity = session_type_ind.no_of_student;
                    excess = student_group_data.data.groups[group_loc].course_excess_count
                        ? student_group_data.data.groups[group_loc].course_excess_count
                        : 0;
                    const new_grp_ind = session_type_ind.groups.findIndex(
                        (i) => i.group_no === grp_nos,
                    );
                    if (new_grp_ind === -1)
                        return res
                            .status(404)
                            .send(
                                common_files.responseFunctionWithRequest(
                                    req,
                                    404,
                                    false,
                                    req.t('GROUP_NO_NOT_FOUND'),
                                    req.t('GROUP_NO_NOT_FOUND'),
                                ),
                            );
                    strength = session_type_ind.groups[new_grp_ind]._student_ids.length + 1;
                    if (!(strength <= excess + capacity))
                        validation.push({
                            _course_id: element._course_id,
                            session_type: elements,
                            message: 'capacity exceeded increase extra allowed',
                        });
                }
                if (validation.length !== 0)
                    return res
                        .status(410)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                410,
                                false,
                                req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                                validation,
                            ),
                        );
                for (const del_element of element.delivery_group) {
                    const session_types =
                        course_session_setting[
                            course_session_setting.findIndex(
                                (i) => i.session_type === del_element.session_type,
                            )
                        ];
                    let group_index = 0;
                    session_types.groups.findIndex((sub_element) => {
                        if (
                            sub_element._student_ids.findIndex(
                                (i) => i.toString() === req.body._student_id.toString(),
                            ) !== -1
                        ) {
                            group_index = sub_element.group_no;
                        }
                    });
                    if (group_index !== -1) {
                        objs = {
                            $pull: {
                                'groups.$[i].courses.$[j].setting.$[k].ungrouped': ObjectId(
                                    req.body._student_id,
                                ),
                                'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                                    ObjectId(req.body._student_id),
                            },
                        };
                        filter = {
                            arrayFilters: [
                                { 'i.term': element.batch, 'i.level': element.level },
                                { 'j._course_id': ObjectId(element._course_id) },
                                element.master_group
                                    ? {
                                          'k.gender': user_data.data.gender,
                                          'k._group_no': element.master_group,
                                      }
                                    : { 'k.gender': user_data.data.gender },
                                { 'l.session_type': del_element.session_type },
                                { 'm.group_no': group_index },
                            ],
                        };
                        dat.push({ objs, filters: filter });
                        obj = await base_control.update_condition_array_filter(
                            student_group,
                            query,
                            objs,
                            filter,
                        );
                    }
                    objs = {
                        $push: {
                            'groups.$[i].courses.$[j].setting.$[k].session_setting.$[l].groups.$[m]._student_ids':
                                ObjectId(req.body._student_id),
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'i.term': element.batch, 'i.level': element.level },
                            { 'j._course_id': ObjectId(element._course_id) },
                            element.master_group
                                ? {
                                      'k.gender': user_data.data.gender,
                                      'k._group_no': element.master_group,
                                  }
                                : { 'k.gender': user_data.data.gender },
                            { 'l.session_type': del_element.session_type },
                            { 'm.group_no': del_element.group_no },
                        ],
                    };
                    dat.push({ objs, filters: filter });
                    obj = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                }
                // Course Schedule Changes
                await removeStudentSchedule(
                    student_group_data.data._institution_id,
                    student_group_data.data._institution_calendar_id,
                    element.batch,
                    element.level,
                    element._course_id,
                    [req.body._student_id],
                );
                await addingStudentSchedule({
                    _institution_id: student_group_data.data._institution_id,
                    institutionCalendarId: student_group_data.data._institution_calendar_id,
                    studentGroup:
                        student_group_data.data.groups[group_loc].courses[student_group_course]
                            .setting[student_master_group],
                    studentData: student_group_data.data.groups[group_loc].students,
                    studentIds: [req.body._student_id],
                    deliveryGroups: element.delivery_group,
                    courseId: element._course_id,
                });
                //Need to send Notification to Student
                const student_loc_status = student_group_data.data.groups[
                    group_loc
                ].students.findIndex(
                    (i) => i._student_id.toString() === req.body._student_id.toString(),
                );
                const temp = student_group_data.data.groups[group_loc].students[student_loc_status];
                // if (temp.course_group_status !== 'pending') {
                const course_status = temp.course_group_status.findIndex(
                    (i) => i._course_id.toString() === element._course_id.toString(),
                );
                if (
                    course_status !== -1 &&
                    temp.course_group_status[course_status].status !== 'pending'
                ) {
                    // let user_data = await base_control.get(user, { _id: ObjectId(req.body._student_id), user_type: constant.EVENT_WHOM.STUDENT, 'isDeleted': false }, { name: 1, user_id: 1, email: 1, mobile: 1 });
                    const email_message =
                        '<p>Dear v_name,<br>' +
                        common_fun.emailGreetingContent() +
                        'This email is to inform that your <b>course_code</b> course groups have been changed as follows:<br>' +
                        'course_delivery<br>' +
                        'For more details, kindly visit the Academic Affairs Office.<br><br>Thank you,<br>' +
                        common_fun.emailRegardsContent() +
                        '</p>';
                    const sms_message =
                        'Your course group for ' +
                        student_group_data.data.groups[group_loc].courses[student_group_course]
                            .course_name +
                        ' - ' +
                        student_group_data.data.groups[group_loc].courses[student_group_course]
                            .course_no +
                        ' is changed. Please check your registered email for details. For any query, contact course coordinator';

                    let delivery_tags = '';
                    for (elements of element.delivery_group) {
                        delivery_tags =
                            delivery_tags +
                            'Delivery : ' +
                            elements.session_type +
                            ' - ' +
                            ' Group : ' +
                            elements.group_no +
                            '<br>';
                    }
                    const name = user_data.data.name.middle
                        ? user_data.data.name.first +
                          ' ' +
                          user_data.data.name.middle +
                          ' ' +
                          user_data.data.name.last
                        : user_data.data.name.first + ' ' + user_data.data.name.last;
                    const replace_data = {
                        v_name: name,
                        course_code:
                            student_group_data.data.groups[group_loc].courses[student_group_course]
                                .course_name +
                            ' - ' +
                            student_group_data.data.groups[group_loc].courses[student_group_course]
                                .course_no,
                        course_delivery: delivery_tags,
                    };
                    const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                    const mail_message = email_message.replace(
                        re,
                        (matched) => replace_data[matched],
                    );
                    if (user_data.data.email !== undefined && user_data.data.email !== '')
                        common_fun.send_email(
                            user_data.data.email,
                            'DigiClass Alert - Your Course ' +
                                student_group_data.data.groups[group_loc].courses[
                                    student_group_course
                                ].course_name +
                                ' - ' +
                                student_group_data.data.groups[group_loc].courses[
                                    student_group_course
                                ].course_no +
                                ' Groups Changed',
                            mail_message,
                        );
                    const message = 'DigiClass - ' + sms_message;
                    if (user_data.data.mobile !== undefined && user_data.data.mobile !== '')
                        common_fun.send_sms(user_data.data.mobile, message);
                }
            } else if (element.mode === constant.STUDENT_GROUP_MODE.ROTATION) {
                const setting_loc = student_group_data.data.groups[
                    group_loc
                ].rotation_group_setting.findIndex(
                    (i) =>
                        i.gender === user_data.data.gender &&
                        parseInt(i.group_no) === parseInt(element.new_group_no),
                );
                if (setting_loc === -1)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                                req.t('STUDENT_GROUP_NOT_FOUND_ON_GENDER'),
                            ),
                        );
                capacity =
                    student_group_data.data.groups[group_loc].rotation_group_setting[setting_loc]
                        .no_of_student;
                strength =
                    student_group_data.data.groups[group_loc].rotation_group_setting[setting_loc]
                        ._student_ids.length + 1;
                excess = student_group_data.data.groups[group_loc].group_excess_count
                    ? student_group_data.data.groups[group_loc].group_excess_count
                    : 0;
                if (!(strength <= excess + capacity))
                    return res
                        .status(410)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                410,
                                false,
                                req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                                req.t('STUDENT_CAPACITY_EXCEEDED_INCREASE_EXTRA_ALLOWED'),
                            ),
                        );

                const check_locs = student_group_data.data.groups[
                    group_loc
                ].rotation_group_setting.findIndex(
                    (i) =>
                        i.gender === user_data.data.gender &&
                        parseInt(i.group_no) === parseInt(element.old_group_no),
                );
                const un_group_checks = student_group_data.data.groups[
                    group_loc
                ].ungrouped.findIndex((i) => i.toString() === req.body._student_id.toString());
                if (check_locs !== -1) {
                    if (
                        student_group_data.data.groups[group_loc].rotation_group_setting[
                            check_locs
                        ]._student_ids.findIndex(
                            (i) => i.toString() === req.body._student_id.toString(),
                        ) !== -1
                    ) {
                        objs = {
                            $pull: {
                                'groups.$[k].ungrouped': ObjectId(req.body._student_id),
                                'groups.$[k].rotation_group_setting.$[j]._student_ids': ObjectId(
                                    req.body._student_id,
                                ),
                            },
                            $push: {
                                'groups.$[k].rotation_group_setting.$[l]._student_ids': ObjectId(
                                    req.body._student_id,
                                ),
                            },
                        };
                        filter = {
                            arrayFilters: [
                                { 'k.term': element.batch, 'k.level': element.level },
                                {
                                    'j.gender': user_data.data.gender,
                                    'j.group_no': parseInt(element.old_group_no),
                                },
                                {
                                    'l.gender': user_data.data.gender,
                                    'l.group_no': parseInt(element.new_group_no),
                                },
                            ],
                        };
                        obj = await base_control.update_condition_array_filter(
                            student_group,
                            query,
                            objs,
                            filter,
                        );
                    }
                } else if (un_group_checks !== -1) {
                    objs = {
                        $pull: {
                            'groups.$[k].ungrouped': ObjectId(req.body._student_id),
                            // 'groups.$[k].rotation_group_setting.$[j]._student_ids': req.body._student_id,
                        },
                        $push: {
                            'groups.$[k].rotation_group_setting.$[l]._student_ids': ObjectId(
                                req.body._student_id,
                            ),
                        },
                    };
                    filter = {
                        arrayFilters: [
                            { 'k.term': element.batch, 'k.level': element.level },
                            // { 'j.gender': user_data.data.gender, 'j.group_no': element.old_group_no },
                            {
                                'l.gender': user_data.data.gender,
                                'l.group_no': parseInt(element.new_group_no),
                            },
                        ],
                    };
                    obj = await base_control.update_condition_array_filter(
                        student_group,
                        query,
                        objs,
                        filter,
                    );
                }
                //Need to removes student from course also
                for (const course_element of student_group_data.data.groups[group_loc].courses) {
                    const gro =
                        course_element.setting[
                            course_element.setting.findIndex(
                                (i) =>
                                    parseInt(i._group_no) === parseInt(element.old_group_no) &&
                                    i.gender === user_data.data.gender,
                            )
                        ];
                    if (gro) {
                        if (gro.ungrouped.indexOf(req.body._student_id.toString()) !== -1) {
                            objs = {
                                $pull: {
                                    'groups.$[k].courses.$[i].setting.$[j].ungrouped': ObjectId(
                                        req.body._student_id,
                                    ),
                                },
                                $push: {
                                    'groups.$[k].courses.$[i].setting.$[l].ungrouped': ObjectId(
                                        req.body._student_id,
                                    ),
                                },
                            };
                            filter = {
                                arrayFilters: [
                                    { 'k.term': element.batch, 'k.level': element.level },
                                    { 'i._course_id': ObjectId(course_element._course_id) },
                                    {
                                        'j.gender': user_data.data.gender,
                                        'j._group_no': parseInt(element.old_group_no),
                                    },
                                    {
                                        'l.gender': user_data.data.gender,
                                        'l._group_no': parseInt(element.new_group_no),
                                    },
                                ],
                            };
                            doc = await base_control.update_condition_array_filter(
                                student_group,
                                query,
                                objs,
                                filter,
                            );
                        } else {
                            gro.session_setting.forEach(async (sub_element) => {
                                sub_element.groups.forEach(async (group_element) => {
                                    const stud_id =
                                        group_element._student_ids[
                                            group_element._student_ids.findIndex(
                                                (i) =>
                                                    i.toString() ===
                                                    req.body._student_id.toString(),
                                            )
                                        ];
                                    if (stud_id) {
                                        objs = {
                                            $pull: {
                                                'groups.$[k].courses.$[i].setting.$[j].session_setting.$[n].groups.$[l]._student_ids':
                                                    ObjectId(req.body._student_id),
                                            },
                                        };
                                        filter = {
                                            arrayFilters: [
                                                {
                                                    'k.term': element.batch,
                                                    'k.level': element.level,
                                                },
                                                {
                                                    'i._course_id': ObjectId(
                                                        course_element._course_id,
                                                    ),
                                                },
                                                {
                                                    'j._group_no': parseInt(gro._group_no),
                                                    'j.gender': user_data.data.gender,
                                                },
                                                { 'n.session_type': sub_element.session_type },
                                                { 'l.group_no': parseInt(group_element.group_no) },
                                                // { 'm._group_no': element.new_group_no }
                                            ],
                                        };
                                        doc = await base_control.update_condition_array_filter(
                                            student_group,
                                            query,
                                            objs,
                                            filter,
                                        );
                                    }
                                });
                            });
                            // objs = {
                            //     $push: { 'groups.$[k].courses.$[i].setting.$[m].ungrouped': req.body._student_id }
                            // };
                            // filter = {
                            //     arrayFilters: [
                            //         { 'k.term': element.batch, 'k.level': element.level },
                            //         { 'i._course_id': element._course_id },
                            //         { 'm.gender': user_data.data.gender, 'm._group_no': element.new_group_no }
                            //     ]
                            // };
                            // doc = await base_control.update_condition_array_filter(student_group, query, objs, filter);
                        }
                        // Course Schedule Changes
                        await removeStudentSchedule(
                            student_group_data.data._institution_id,
                            student_group_data.data._institution_calendar_id,
                            element.batch,
                            element.level,
                            course_element._course_id,
                            [req.body._student_id],
                        );
                    }
                }

                //Notify User about group change
                const student_loc_status = student_group_data.data.groups[
                    group_loc
                ].students.findIndex(
                    (i) => i._student_id.toString() === req.body._student_id.toString(),
                );
                if (
                    student_group_data.data.groups[group_loc].students[student_loc_status]
                        .master_group_status !== 'pending'
                ) {
                    const institution_calendar_data = await base_control.get(
                        institution_calendar,
                        {
                            _id: ObjectId(student_group_data.data._institution_calendar_id),
                            isDeleted: false,
                        },
                        { calendar_name: 1 },
                    );
                    const level_name =
                        student_group_data.data.groups[group_loc].rotation === 'yes'
                            ? 'Rotation'
                            : 'Foundation';
                    // let user_data = await base_control.get(user, { _id: ObjectId(req.body._student_id), user_type: constant.EVENT_WHOM.STUDENT, 'isDeleted': false }, { name: 1, user_id: 1, email: 1, mobile: 1 });
                    const email_message =
                        '<p>Dear v_name,<br>' +
                        common_fun.emailGreetingContent() +
                        'This email is to inform  that your ' +
                        level_name +
                        ' year group for the academic year <b>v_academic</b> has been changed as <b>' +
                        level_name +
                        ' group group_no</b>. For more details, kindly visit the Academic Affairs Office.<br><br>' +
                        'Thank you, <br>' +
                        common_fun.emailRegardsContent() +
                        '</p>';
                    const sms_message =
                        'Your ' +
                        level_name +
                        ' group is changed. Please check your registered email for details. For any query, visit Academic Affairs Office';
                    const name = user_data.data.name.middle
                        ? user_data.data.name.first +
                          ' ' +
                          user_data.data.name.middle +
                          ' ' +
                          user_data.data.name.last
                        : user_data.data.name.first + ' ' + user_data.data.name.last;
                    const replace_data = {
                        v_name: name,
                        v_academic: institution_calendar_data.data.calendar_name,
                        group_no: element.new_group_no,
                    };
                    const re = new RegExp(Object.keys(replace_data).join('|'), 'gi');
                    const message = email_message.replace(re, (matched) => replace_data[matched]);
                    if (user_data.data.email !== undefined && user_data.data.email !== '')
                        common_fun.send_email(
                            user_data.data.email,
                            'DigiClass Alert - Your ' + level_name + ' Groups Change',
                            message,
                        );
                    const msg = 'DigiClass - ' + sms_message;
                    if (user_data.data.mobile !== undefined && user_data.data.mobile !== '')
                        common_fun.send_sms(user_data.data.mobile, msg);
                }
            }
            if (req.body.data.length === loop_index + 1) {
                if (!obj.status)
                    return res
                        .status(404)
                        .send(
                            common_files.responseFunctionWithRequest(
                                req,
                                404,
                                false,
                                req.t('ERROR_UNABLE_TO_SET_SETTING_PLS_RETRY'),
                                req.t('UNABLE_TO_SET_SETTING_PLS_RETRY'),
                            ),
                        );
                updateStudentGroupFlatCacheData();
                return res
                    .status(200)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            200,
                            true,
                            req.t('STUDENTS_GLOBAL_EDIT_SUCCESS'),
                            req.t('STUDENTS_GLOBAL_EDIT_SUCCESS'),
                        ),
                    );
            }
            loop_index++;
        }
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    true,
                    req.t('STUDENT_SEARCH'),
                    error.toString(),
                ),
            );
    }
};

exports.hard_delete = async (req, res) => {
    try {
        const student_group_check = await base_control.hard_delete(student_group, {
            _institution_id: ObjectId(req.headers._institution_id),
            'master._program_id': ObjectId(req.body._program_id),
            _institution_calendar_id: ObjectId(req.body._institution_id),
            isDeleted: false,
        });
        if (!student_group_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('UNABLE_TO_REMOVED_STUDENTS_GROUP'),
                        req.t('UNABLE_TO_REMOVED_STUDENTS_GROUP'),
                    ),
                );
        updateStudentGroupFlatCacheData();
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('STUDENTS_GROUP_REMOVED_SUCCESS'),
                    req.t('STUDENTS_GROUP_REMOVED_SUCCESS'),
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    true,
                    req.t('HARD_DELETE_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
