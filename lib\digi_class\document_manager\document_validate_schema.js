const Joi = require('joi');
const constant = require('../../utility/constants');
// get document schema
function getDocumentSchema() {
    const schema = {
        params: {
            userId: Joi.string().length(24).required(),
        },
        query: {
            limit: Joi.number(),
            pageNo: Joi.number(),
            search: Joi.string(),
            type: Joi.string().valid(constant.DS_TYPE_STUDENT).optional(),
            subTab: Joi.string().optional(),
            tab: Joi.string().optional(),
            courseAdmin: Joi.boolean().optional(),
            remote: Joi.boolean().optional(),
        },
    };
    return schema;
}
// get course document schema
function getCourseDocumentSchema() {
    const schema = {
        params: {
            userId: Joi.string().length(24).required(),
            courseId: Joi.string().length(24).required(),
        },
        query: {
            limit: Joi.number(),
            pageNo: Joi.number(),
            search: Joi.string(),
            type: Joi.string().valid(constant.DS_TYPE_STUDENT).optional(),
            subTab: Joi.string().optional(),
            tab: Joi.string().optional(),
            _program_id: Joi.string().length(24).optional(),
            year_no: Joi.string().optional(),
            level_no: Joi.string().optional(),
            term: Joi.string().optional(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            _institution_calendar_id: Joi.string().length(24).optional(),
            courseAdmin: Joi.boolean().optional(),
            remote: Joi.boolean().optional(),
        },
    };
    return schema;
}
// get course and session document schema
function getCourseSessionDocumentSchema() {
    const schema = {
        params: {
            userId: Joi.string().length(24).required(),
            courseId: Joi.string().length(24).required(),
            sessionId: Joi.string().length(24).required(),
        },
        query: {
            limit: Joi.number().required(),
            pageNo: Joi.number(),
            search: Joi.string(),
            type: Joi.string().valid(constant.DS_TYPE_STUDENT).optional(),
            subTab: Joi.string().optional(),
            tab: Joi.string().optional(),
            _program_id: Joi.string().length(24).optional(),
            year_no: Joi.string().optional(),
            level_no: Joi.string().optional(),
            term: Joi.string().optional(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            _institution_calendar_id: Joi.string().length(24).optional(),
            remote: Joi.boolean().optional(),
        },
    };
    return schema;
}
// get document by session schema
function getDocumentBySessionSchema() {
    const schema = {
        body: Joi.object().keys({
            sessions: Joi.array().required(),
        }),
        params: {
            userId: Joi.string().length(24).required(),
        },
    };
    return schema;
}
// create document schema
function createDocumentSchema() {
    const schema = {
        body: Joi.object()
            .keys({
                name: Joi.string().required(),
                courseId: Joi.string().length(24).required(),
                sessions: Joi.array().optional(),
                type: Joi.string().required(),
                url: Joi.string().optional(),
                // file: Joi.string().optional(),
                courseAdmin: Joi.boolean().optional(),
                _program_id: Joi.string().length(24),
                year_no: Joi.string(),
                term: Joi.string(),
                level_no: Joi.string(),
                rotation: Joi.string().optional(),
                rotation_count: Joi.number().optional(),
                _institution_calendar_id: Joi.string().length(24),
            })
            .unknown(),
        params: {
            userId: Joi.string().length(24).required(),
        },
    };
    return schema;
}
// update document schema
function updateDocumentSchema() {
    const schema = {
        body: Joi.object().keys({
            starred: Joi.boolean(),
            name: Joi.string(),
            sessions: Joi.array(),
        }),
        params: {
            id: Joi.string().length(24).required(),
            userId: Joi.string().length(24).required(),
        },
    };
    return schema;
}
// delete document schema
function deleteDocumentSchema() {
    const schema = {
        params: {
            id: Joi.string().length(24).required(),
        },
    };
    return schema;
}
// select session schema
function selectSessionsSchema() {
    const schema = {
        params: {
            id: Joi.string().length(24).required(),
        },
        query: {
            userId: Joi.string().length(24).required(),
            institutionCalendarId: Joi.string().length(24).optional(),
            programId: Joi.string().length(24).optional(),
            yearNo: Joi.string().optional(),
            levelNo: Joi.string().optional(),
            term: Joi.string().optional(),
            mergedStatus: Joi.string().optional(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.string().optional(),
            courseAdmin: Joi.boolean().optional(),
        },
    };
    return schema;
}

// get course and session document schema
function getCourseSessionDocumentWithFilterSchema() {
    const schema = {
        params: {
            userId: Joi.string().length(24).required(),
            courseId: Joi.string().length(24).required(),
            sessionId: Joi.string().length(24).required(),
        },
        query: {
            limit: Joi.number(),
            pageNo: Joi.number(),
            search: Joi.string(),
            type: Joi.string()
                .valid(constant.DS_TYPE_STUDENT, constant.EVENT_WHOM.STAFF)
                .optional(),
            subTab: Joi.string().optional(),
            tab: Joi.string().optional(),
            institutionCalendarId: Joi.string().length(24).required(),
            filter: Joi.string().required(),
            term: Joi.string(),
            _program_id: Joi.string().length(24),
            year_no: Joi.string(),
            level_no: Joi.string(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
            remote: Joi.boolean().optional(),
        },
    };
    return schema;
}

// get course and session document schema
function getCourseDocumentWithFilterSchema() {
    const schema = {
        params: {
            userId: Joi.string().length(24).required(),
            courseId: Joi.string().length(24).required(),
        },
        query: {
            limit: Joi.number(),
            pageNo: Joi.number(),
            search: Joi.string(),
            type: Joi.string()
                .valid(constant.DS_TYPE_STUDENT, constant.EVENT_WHOM.STAFF)
                .optional(),
            subTab: Joi.string().optional(),
            tab: Joi.string().optional(),
            institutionCalendarId: Joi.string().length(24).required(),
            filter: Joi.string().required(),
            term: Joi.string(),
            _program_id: Joi.string().length(24),
            year_no: Joi.string(),
            level_no: Joi.string(),
            rotation: Joi.string().optional(),
            rotation_count: Joi.number().optional(),
        },
    };
    return schema;
}
// activity or document access schema
function activityorDocumentAccessSchema() {
    const schema = {
        body: Joi.object().keys({
            activityId: Joi.string().length(24).optional(),
            documentId: Joi.string().when('activityId', {
                is: null,
                then: Joi.string().length(24).required(),
            }),
            groupNames: Joi.string().optional(),
            type: Joi.string().valid(constant.DS_TYPE_STUDENT, constant.EVENT_WHOM.STAFF),
            _institution_calendar_id: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().length(24).required(),
            }),
            _course_id: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().length(24).required(),
            }),
            _program_id: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().length(24).required(),
            }),
            year_no: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().required(),
            }),
            level_no: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().required(),
            }),
            term: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().required(),
            }),
            rotation: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().required(),
            }),
            rotation_count: Joi.string().when('type', {
                is: constant.DS_TYPE_STUDENT,
                then: Joi.string().optional(),
            }),
        }),
        params: {
            userId: Joi.string().length(24).required(),
        },
    };
    return schema;
}
module.exports = {
    getDocumentSchema: getDocumentSchema(),
    getCourseDocumentSchema: getCourseDocumentSchema(),
    getCourseSessionDocumentSchema: getCourseSessionDocumentSchema(),
    getDocumentBySessionSchema: getDocumentBySessionSchema(),
    createDocumentSchema: createDocumentSchema(),
    updateDocumentSchema: updateDocumentSchema(),
    deleteDocumentSchema: deleteDocumentSchema(),
    selectSessionsSchema: selectSessionsSchema(),
    getCourseSessionDocumentWithFilterSchema: getCourseSessionDocumentWithFilterSchema(),
    getCourseDocumentWithFilterSchema: getCourseDocumentWithFilterSchema(),
    activityorDocumentAccessSchema: activityorDocumentAccessSchema(),
};
