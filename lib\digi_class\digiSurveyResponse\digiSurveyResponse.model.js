const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    DIGI_SURVEY_RESPONSE,
    INSTITUTION_CALENDAR,
    USER,
    DIGI_SURVEY,
    DIGI_PROGRAM,
    DIGI_COURSE,
} = require('../../utility/constants');

const digiSurveyResponseSchema = Schema({
    institutionId: { type: ObjectId },
    institutionCalendarId: { type: ObjectId, ref: INSTITUTION_CALENDAR },
    surveyId: { type: ObjectId, ref: DIGI_SURVEY },
    programId: { type: ObjectId, ref: DIGI_PROGRAM },
    term: { type: String },
    yearNo: {
        type: String,
    },
    levelNo: {
        type: String,
    },
    courseId: {
        type: ObjectId,
        ref: DIGI_COURSE,
    },
    userId: { type: ObjectId, ref: USER },
    userType: { type: String },
    gender: { type: String },
    sectionNo: { type: String },
    questionNo: { type: String },
    questionType: { type: String },
    answer: { type: Schema.Types.Mixed },
    questionId: { type: String },
});

module.exports = model(DIGI_SURVEY_RESPONSE, digiSurveyResponseSchema);
