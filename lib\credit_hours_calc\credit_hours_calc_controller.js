let constant = require('../utility/constants');
var credit_hours_calc = require('mongoose').model(constant.CREDIT_HOURS_CALC);
var program = require('mongoose').model(constant.PROGRAM);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const credit_hours_calc_formate = require('./credit_hours_calc_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let skips = Number(req.query.limit * (req.query.pageNo - 1));
    let limits = Number(req.query.limit) || 0;
    let aggre = [
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $skip: skips }, { $limit: limits },
        { $sort: { updatedAt: -1 } }
    ];
    let doc = await base_control.get_aggregate(credit_hours_calc, aggre);
    if (doc.status) {
        let totalPages = Math.ceil(doc.totalDoc / limits);
        common_files.list_all_response(res, 200, true, "credit_hours_calc list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ credit_hours_calc_formate.credit_hours_calc(doc.data));
        // common_files.list_all_response(res, 200, true, "credit_hours_calc list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* credit_hours_calc_formate.credit_hours_calc(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let aggre = [
        { $match: { '_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
    ];
    let doc = await base_control.get_aggregate(credit_hours_calc, aggre);
    if (doc.status) {
        common_files.com_response(res, 200, true, "credit_hours_calc details", /* doc.data */credit_hours_calc_formate.credit_hours_calc_ID(doc.data[0]));
        // common_files.com_response(res, 200, true, "credit_hours_calc details", doc.data/* credit_hours_calc_formate.credit_hours_calc_ID(doc.data[0]) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let docs;
    let program_ids = [];
    let status, datas;
    let doc_update = { status: true };
    await req.body.data.forEach(element => {
        if (program_ids.indexOf(element._program_id) == -1) {
            program_ids.push(element._program_id);
        }
    });
    let checks = await base_control.check_id(program, { _id: { $in: program_ids }, 'isDeleted': false });
    if (checks.status) {
        await req.body.data.forEach(async (doc, index) => {

            let objects = {
                session_type: doc.session_type,
                credit_hours: doc.credit_hours,
                contact_hours: doc.contact_hours,
                per_session: doc.per_session,
                _program_id: doc._program_id
            };
            if (doc.id == '' && doc.id.length == 0) {
                docs = await base_control.insert(credit_hours_calc, objects);
                if (docs.status && doc_update.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            } else {
                docs = await base_control.update(credit_hours_calc, doc.id, objects);
                if (docs.status) {
                    status = true;
                    datas = docs;
                } else {
                    datas = docs;
                    status = false;
                }
            }
            if (req.body.data.length == index + 1) {
                if (status) {

                    let aggre = [
                        { $match: { 'isDeleted': false } },
                        { $sort: { contact_hours: 1 } }
                    ];
                    let doc = await base_control.get_aggregate(credit_hours_calc, aggre);
                    common_files.com_response(res, 201, true, "credit_hours_calc Added successfully", doc);
                } else {
                    common_files.com_response(res, 500, false, "Error", docs.data);
                }
            }
        });
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.update = async (req, res) => {
    let checks = { status: true }
    if (req.body._program_id != undefined) {
        checks = await base_control.check_id(program, { _id: { $in: req.body._program_id }, 'isDeleted': false });
    }
    if (checks.status) {
        let object_id = req.params.id;
        let doc = await base_control.update(credit_hours_calc, object_id, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "credit_hours_calc update successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error id not match", 'Check Parshing reference ID');
    }
}

exports.delete = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.delete(credit_hours_calc, object_id);
    if (doc.status) {
        common_files.com_response(res, 201, true, "credit_hours_calc deleted successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(credit_hours_calc, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "credit_hours_calc List", credit_hours_calc_formate.credit_hours_calc_ID_Array_Only(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};

exports.list_program = async (req, res) => {
    // let skips = Number(req.query.limit * (req.query.pageNo - 1));
    // let limits = Number(req.query.limit) || 0;
    let id = req.params.id;
    let aggre = [
        { $match: { session_type: 'Theory' } },
        { $match: { '_program_id': ObjectId(id) } },
        { $match: { 'isDeleted': false } },
        { $lookup: { from: constant.PROGRAM, localField: '_program_id', foreignField: '_id', as: 'program' } },
        { $unwind: { path: '$program', preserveNullAndEmptyArrays: true } },
        { $sort: { updatedAt: 1 } },
        // { $skip: skips }, 
        { $limit: 1 }
    ];
    let doc_aggre = { 'isDeleted': false, '_program_id': ObjectId(id) };
    let doc = await base_control.get_aggregate_with_id_match(credit_hours_calc, aggre, doc_aggre);
    aggre[0].$match.session_type = 'Practical';
    let doc2 = await base_control.get_aggregate_with_id_match(credit_hours_calc, aggre, doc_aggre);
    aggre[0].$match.session_type = 'Clinical';
    let doc3 = await base_control.get_aggregate_with_id_match(credit_hours_calc, aggre, doc_aggre);
    let calc = [];
    calc.push(doc.data[0]);
    calc.push(doc2.data[0]);
    calc.push(doc3.data[0]);

    if (doc.status) {
        // let totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "credit_hours_calc list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ credit_hours_calc_formate.credit_hours_calc(doc.data));
        // common_files.list_all_response(res, 200, true, "credit_hours_calc list", doc.totalDoc, totalPages, Number(req.query.pageNo), doc.data /* credit_hours_calc_formate.credit_hours_calc(doc.data) */);
        // common_files.com_response(res, 200, true, "credit_hours_calc list", /* calc */ credit_hours_calc_formate.credit_hours_calc(calc));
        common_files.com_response(res, 200, true, "credit_hours_calc list", calc /* credit_hours_calc_formate.credit_hours_calc(doc.data) */);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};