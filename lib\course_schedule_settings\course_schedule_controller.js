const courseScheduleSettings = require('../models/course_schedule_setting');
const institution = require('../models/institution');
const studentGroups = require('../models/student_group');
const users = require('../models/user');

const { responseFunctionWithRequest, convertToMongoObjectId } = require('../utility/common');
const {
    get_list,
    insert,
    get,
    update_condition_array_filter,
    update_condition,
} = require('../base/base_controller');
const {
    DS_DATA_RETRIEVED,
    DS_NO_DATA_FOUND,
    DS_ADDED,
    DS_ADD_FAILED,
} = require('../utility/constants');

// get extra and curricular &  break timing
exports.getExtraCurricularAndBreakTiming = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { programId, instCalId },
        } = req;
        const settingQuery = { _institution_id, isDeleted: false };
        //const settingProject = { 'programs.extraCurricularAndBreakTiming': 1 };

        const courseScheduleSettingData = await get(courseScheduleSettings, settingQuery);
        if (!courseScheduleSettingData.status)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, false, DS_NO_DATA_FOUND, []));
        const programInd = courseScheduleSettingData.data.programs.findIndex(
            (ele) => ele._program_id.toString() === programId.toString(),
        );

        if (programInd == -1)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, false, 'No Records', []));
        const extracurricular_break_timing_data = courseScheduleSettingData.data.programs[
            programInd
        ].extraCurricularAndBreakTiming.filter(
            (ele) =>
                ele.isDeleted === false &&
                ele._institution_calendar_id &&
                ele._institution_calendar_id.toString() === instCalId.toString(),
        );
        // if (!extracurricular_break_timing_data.status)
        //     return res.status(200).send(responseFunctionWithRequest(req, 200, true, DS_NO_DATA_FOUND, []));
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    DS_DATA_RETRIEVED,
                    extracurricular_break_timing_data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};

// create extra and curricular &  break timing
exports.createExtraCurricularAndBreakTiming = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            body: {
                title,
                gender,
                mode,
                days,
                type,
                startTime,
                endTime,
                allowCourseCoordinatesToEdit,
            },
            query: { programId, instCalId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const courseScheduleSettingsCheck = await get(courseScheduleSettings, {
            _institution_id: convertToMongoObjectId(_institution_id),
        });

        let obj = {};
        let doc = '';
        if (!courseScheduleSettingsCheck.status) {
            //Insert
            obj = {
                _institution_id,
                programs: {
                    _program_id: programId,
                    extraCurricularAndBreakTiming: {
                        _institution_calendar_id: instCalId,
                        title,
                        gender,
                        mode,
                        type,
                        days,
                        startTime,
                        endTime,
                        allowCourseCoordinatesToEdit,
                    },
                },
            };
            doc = await insert(courseScheduleSettings, obj);
        } else {
            const programInd = courseScheduleSettingsCheck.data.programs.findIndex(
                (ele) => ele._program_id.toString() === programId.toString(),
            );
            const query = { _institution_id: convertToMongoObjectId(_institution_id) };
            if (programInd != -1) {
                /* //Check already exist
                const extraCurricularBreakingInd = courseScheduleSettingsCheck.data.programs[
                    programInd
                ].extraCurricularAndBreakTiming.findIndex(
                    (ele) =>
                        ele.isDeleted === false &&
                        ele.title.toLowerCase() === title.toLowerCase() &&
                        ele._institution_calendar_id &&
                        ele._institution_calendar_id.toString() === instCalId.toString(),
                );
                if (extraCurricularBreakingInd != -1)
                    return res
                        .status(410)
                        .send(responseFunctionWithRequest(req, 410, false, 'Title already exist')); */
                //Program Present
                obj = {
                    $push: {
                        'programs.$[p].extraCurricularAndBreakTiming': {
                            _institution_calendar_id: instCalId,
                            title,
                            gender,
                            mode,
                            type,
                            days,
                            startTime,
                            endTime,
                            allowCourseCoordinatesToEdit,
                        },
                    },
                };
                filter = {
                    arrayFilters: [{ 'p._program_id': programId }],
                };
                doc = await update_condition_array_filter(
                    courseScheduleSettings,
                    query,
                    obj,
                    filter,
                );
            } else {
                //Program not Present
                obj = {
                    $push: {
                        programs: {
                            _program_id: programId,
                            extraCurricularAndBreakTiming: {
                                _institution_calendar_id: instCalId,
                                title,
                                gender,
                                mode,
                                type,
                                days,
                                startTime,
                                endTime,
                                allowCourseCoordinatesToEdit,
                            },
                        },
                    },
                };
                doc = await update_condition(courseScheduleSettings, query, obj);
            }
        }
        if (doc.status)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, DS_ADDED, doc.data));
        return res.status(410).send(responseFunctionWithRequest(req, 410, false, DS_ADD_FAILED));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};

// create extra and curricular &  break timing
exports.updateExtraCurricularAndBreakTiming = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { programId, id },
            body: {
                title,
                gender,
                mode,
                days,
                type,
                startTime,
                endTime,
                allowCourseCoordinatesToEdit,
            },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'Institution Not found'));
        const query = { _institution_id: convertToMongoObjectId(req.headers._institution_id) };
        const obj = {
            $set: {
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].title': title,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].gender': gender,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].mode': mode,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].days': days,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].type': type,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].startTime': startTime,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].endTime': endTime,
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].allowCourseCoordinatesToEdit':
                    allowCourseCoordinatesToEdit,
            },
        };
        filter = {
            arrayFilters: [{ 'pid._program_id': programId }, { 'rid._id': id }],
        };
        const doc = await update_condition_array_filter(courseScheduleSettings, query, obj, filter);
        if (doc.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(req, 200, true, 'Updated successfully', doc.data),
                );
        return res
            .status(410)
            .send(responseFunctionWithRequest(req, 410, false, 'Unable to Update ', doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};

// create extra and curricular &  break timing
exports.deleteExtraCurricularAndBreakTiming = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { programId, id },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'Institution Not found'));
        const query = { _institution_id: convertToMongoObjectId(req.headers._institution_id) };
        const obj = {
            $set: {
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].isDeleted': true,
            },
        };
        filter = {
            arrayFilters: [{ 'pid._program_id': programId }, { 'rid._id': id }],
        };
        const doc = await update_condition_array_filter(courseScheduleSettings, query, obj, filter);
        if (doc.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(req, 200, true, 'Deleted successfully', doc.data),
                );
        return res
            .status(410)
            .send(responseFunctionWithRequest(req, 410, false, 'Unable to delete ', doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};
exports.enableDisableExtraCurricularAndBreakTiming = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { programId, id },
            body: { activeStatus },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'Institution Not found'));
        const query = { _institution_id: convertToMongoObjectId(req.headers._institution_id) };
        const obj = {
            $set: {
                'programs.$[pid].extraCurricularAndBreakTiming.$[rid].isActive': activeStatus,
            },
        };
        filter = {
            arrayFilters: [{ 'pid._program_id': programId }, { 'rid._id': id }],
        };
        const doc = await update_condition_array_filter(courseScheduleSettings, query, obj, filter);
        if (doc.status)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(req, 200, true, 'Updated successfully', doc.data),
                );
        return res
            .status(410)
            .send(responseFunctionWithRequest(req, 410, false, 'Unable to update ', doc.data));
    } catch (error) {
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};
exports.events = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { programId, instCalId, courseId },
        } = req;
        const institutionCheck = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!institutionCheck.status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        'Institution Not found',
                        'Institution Not found',
                    ),
                );
        const userData = await get_list(users, { user_type: 'student', isDeleted: false });
        if (!userData.status)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'User Not found'));

        const studentGroupData = await get_list(
            studentGroups,
            {
                _institution_calendar_id: convertToMongoObjectId(instCalId),
                'master._program_id': convertToMongoObjectId(programId),
            },
            {},
        );
        if (!studentGroupData.status)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'Student group Not found'));
        let courseInd = -1;
        let group_data = '';
        const groups = [];

        //return res.send(studentGroupData.data);
        for (SGData of studentGroupData.data) {
            for (group of SGData.groups) {
                courseInd = group.courses.findIndex(
                    (ele) => ele._course_id.toString() == courseId.toString(),
                );
                if (courseInd != -1) {
                    group_data = group;
                    break;
                }
            }
            if (courseInd != -1) break;
        }

        if (courseInd == -1)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'Course Not found'));
        const course_settings = group_data.courses[courseInd].setting;
        for (data of course_settings) {
            for (session_setting of data.session_setting) {
                for (group of session_setting.groups) {
                    const userDetails = [];
                    for (studentId of group._student_ids) {
                        const userInd = userData.data.findIndex(
                            (ele) => ele._id.toString() == studentId.toString(),
                        );
                        if (userInd != -1)
                            userDetails.push({
                                _id: userData.data[userInd]._id,
                                academicNo: userData.data[userInd].user_id,
                                studentName: userData.data[userInd].name.first,
                            });
                        else
                            userDetails.push({
                                _id: userData.data[userInd]._id,
                                academicNo: 'Not Found',
                                studentName: 'Not Found',
                            });
                    }
                    groups.push({
                        group_name: group.group_name,
                        student_ids: group._student_ids,
                        userDetails,
                    });
                }
            }
        }

        if (groups.length == 0)
            return res
                .status(404)
                .send(responseFunctionWithRequest(req, 404, false, 'Student group Not found'));
        return res
            .status(200)
            .send(responseFunctionWithRequest(req, 200, true, 'Group data', groups));
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(responseFunctionWithRequest(req, 500, false, 'Error Catch', error.toString()));
    }
};
