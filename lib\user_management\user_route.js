const express = require('express');
const route = express.Router();
const user = require('./user_controller');
const validator = require('./user_validator');
const file_upload = require('../utility/file_upload');
const multer = require('multer');
const { authMiddleware, serviceAuth } = require('../../middleware');
const catchAsync = require('../utility/catch-async');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

const staff_upload = file_upload.uploadfile.fields([
    { name: '_employee_id_doc', maxCount: 1 },
    { name: '_nationality_id_doc', maxCount: 1 },
    { name: '_address_doc', maxCount: 1 },
    { name: '_degree_doc', maxCount: 1 },
    { name: '_appointment_order_doc', maxCount: 1 },
]);

const user_upload = file_upload.uploadfile.fields([
    { name: '_school_certificate_doc', maxCount: 1 },
    { name: '_entrance_exam_certificate_doc', maxCount: 1 },
    { name: '_admission_order_doc', maxCount: 1 },
    { name: '_college_id_doc', maxCount: 1 },
    { name: '_nationality_id_doc', maxCount: 1 },
    { name: '_address_doc', maxCount: 1 },
]);

const staff_student_upload = file_upload.uploadfile.fields([
    { name: '_school_certificate_doc', maxCount: 1 },
    { name: '_entrance_exam_certificate_doc', maxCount: 1 },
    { name: '_admission_order_doc', maxCount: 1 },
    { name: '_college_id_doc', maxCount: 1 },
    { name: '_employee_id_doc', maxCount: 1 },
    { name: '_nationality_id_doc', maxCount: 1 },
    { name: '_address_doc', maxCount: 1 },
    { name: '_degree_doc', maxCount: 1 },
    { name: '_appointment_order_doc', maxCount: 1 },
]);

const face_upload = file_upload.uploadfile3.fields([
    { name: 'center', maxCount: 1 },
    { name: 'left', maxCount: 1 },
    { name: 'right', maxCount: 1 },
    { name: 'up', maxCount: 1 },
]);

route.post('/listUser', serviceAuth, user.IntegrationUserSync);
route.get(
    '/authLoggedIn/:userId',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    user.loggedInUserDetails,
);
route.get(
    '/getDocument/:userId/:documentKey',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:profile_view',
            'user_management:staff_management:registration_pending:submitted:profile_view',
            'user_management:staff_management:registration_pending:invalid:profile_view',
            'user_management:staff_management:registration_pending:valid:profile_view',
            'user_management:staff_management:registration_pending:mismatch:profile_view',
            'user_management:staff_management:registration_pending:expired:view',
            'user_management:student_management:registered:profile_view',
            'user_management:student_management:registration_pending:submitted:profile_view',
            'user_management:student_management:registration_pending:mismatch:profile_view',
            'user_management:student_management:registration_pending:mismatch:profile_view',
            'user_management:student_management:registration_pending:invalid:profile_view',
            'user_management:student_management:registration_pending:valid:profile_view',
            'user_management:student_management:registration_pending:valid:profile_view',
        ]),
    ],
    user.getDocumentById,
);
route.get(
    '/automation_get_otp',
    authMiddleware,
    [userPolicyAuthentication([])],
    user.automation_get_otp,
);
route.get('/staff_list', authMiddleware, [userPolicyAuthentication([])], user.staff_list);
route.get(
    '/student_search/:academic_no',
    authMiddleware,
    [userPolicyAuthentication([])],
    user.student_search,
);
route.get(
    '/user_img_get/:id',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:student_management:registered:biometric_view',
            'user_management:student_management:registered:biometric_edit',
            'user_management:staff_management:registered:biometric_view',
            'user_management:staff_management:registered:biometric_edit',
        ]),
    ],
    user.user_img_get,
);
route.get(
    '/staff_profile_details/:id',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:profile_view',
            'user_management:staff_management:registration_pending:submitted:profile_view',
            'user_management:staff_management:registration_pending:invalid:profile_view',
            'user_management:staff_management:registration_pending:valid:profile_view',
            'user_management:staff_management:registration_pending:mismatch:profile_view',
        ]),
    ],
    user.staff_profile_details,
);
route.get(
    '/user_search/:user_type/:tab/:text',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:inactive:search',
            'user_management:staff_management:registration_pending:all:search',
            'user_management:staff_management:registration_pending:imported:search',
            'user_management:staff_management:registration_pending:invited:search',
            'user_management:staff_management:registration_pending:invalid:search',
            'user_management:staff_management:registration_pending:valid:search',
            'user_management:staff_management:registration_pending:submitted:search',
            'user_management:staff_management:registration_pending:mismatch:search',
            'user_management:staff_management:registration_pending:expired:search',
            'user_management:student_management:inactive:search',
            'user_management:student_management:registration_pending:all:search',
            'user_management:student_management:registration_pending:imported:search',
            'user_management:student_management:registration_pending:invited:search',
            'user_management:student_management:registration_pending:invalid:search',
            'user_management:student_management:registration_pending:valid:search',
            'user_management:student_management:registration_pending:submitted:search',
            'user_management:student_management:registration_pending:mismatch:search',
            'user_management:student_management:registration_pending:expired:search',
        ]),
    ],
    user.user_search_aggregate,
);
route.get(
    '/get_all/:user_type/:tab',
    authMiddleware,
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:view',
            'user_management:staff_management:view',
            'user_management:staff_management:registered:view',
            'user_management:staff_management:inactive:view',
            'user_management:staff_management:registration_pending:view',
            'user_management:staff_management:registration_pending:imported:view',
            'user_management:staff_management:registration_pending:invited:view',
            'user_management:staff_management:registration_pending:invalid:view',
            'user_management:staff_management:registration_pending:valid:view',
            'user_management:staff_management:registration_pending:submitted:view',
            'user_management:staff_management:registration_pending:mismatch:view',
            'user_management:staff_management:registration_pending:expired:view',
            'user_management:student_management:view',
            'user_management:student_management:registered:view',
            'user_management:student_management:inactive:view',
            'user_management:student_management:registration_pending:view',
            'user_management:student_management:registration_pending:imported:view',
            'user_management:student_management:registration_pending:invited:view',
            'user_management:student_management:registration_pending:invalid:view',
            'user_management:student_management:registration_pending:valid:view',
            'user_management:student_management:registration_pending:submitted:view',
            'user_management:student_management:registration_pending:mismatch:view',
            'user_management:student_management:registration_pending:expired:view',
        ]),
    ],
    user.list_all,
);
route.get(
    '/get_all_user',
    authMiddleware,
    [
        userPolicyAuthentication([
            'leave_management:leave_settings:student_lms:leave:warnings_&_notifications_view',
        ]),
    ],
    user.list_all_user,
);
route.get(
    '/export/:user_type/:tab',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:view',
            'user_management:student_management:view',
        ]),
    ],
    // validator.fields,
    function (req, res, next) {
        user.exportUsers(req, res)
            .then(({ data = {} }) => {
                res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
                res.setHeader(
                    'Content-Type',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                res.setHeader('Content-Disposition', `attachment; filename=${data.fileName}`);
                data.wb.xlsx
                    .write(res)
                    .then(() => {
                        res.end();
                    })
                    .catch((err) => {
                        next(err);
                    });
            })
            .catch((err) => {
                next(err);
            });
    },
);
route.get(
    '/get_id/:user_type/:tab',
    authMiddleware,
    [userPolicyAuthentication([])],
    user.table_get_id,
);
route.get(
    '/list_filter/:program/:time/:type',
    authMiddleware,
    [userPolicyAuthentication([])],
    user.list_filter,
); //No need
route.get('/dashboard/:id', authMiddleware, [userPolicyAuthentication([])], user.dashboard);
route.get(
    '/user_details/:id',
    authMiddleware,
    [userPolicyAuthentication(['leave_management:student_register:profile_view'])],
    user.user_details,
);
route.get('/user_dashboard', authMiddleware, [userPolicyAuthentication([])], user.user_dashboard); //Need to verify
route.get('/:id', authMiddleware, [userPolicyAuthentication([])], validator.user_id, user.list_id); //No need
route.get(
    '/mobile_verify/:value',
    authMiddleware,
    [userPolicyAuthentication([])],
    user.get_mobile_mail,
); //Add staff or student number verification
route.get(
    '/:user_type/:id',
    authMiddleware,
    [
        userPolicyAuthentication([
            defaultPolicy.SIGNUP,
            'user_management:staff_management:registered:profile_view',
            'user_management:staff_management:registration_pending:submitted:profile_view',
            'user_management:staff_management:registration_pending:mismatch:profile_view',
            'user_management:staff_management:registration_pending:mismatch:profile_view',
            'user_management:staff_management:registration_pending:invalid:profile_view',
            'user_management:staff_management:registration_pending:valid:profile_view',
            'user_management:staff_management:registration_pending:valid:profile_view',
            'user_management:staff_management:registration_pending:imported:view',
            'user_management:staff_management:registration_pending:invited:view',
            'user_management:staff_management:registration_pending:invalid:view',
            'user_management:staff_management:registration_pending:valid:view',
            'user_management:staff_management:registration_pending:submitted:view',
            'user_management:staff_management:registration_pending:mismatch:view',
            'user_management:staff_management:registration_pending:expired:view',
            'user_management:student_management:registered:profile_view',
            'user_management:student_management:registration_pending:submitted:profile_view',
            'user_management:student_management:registration_pending:mismatch:profile_view',
            'user_management:student_management:registration_pending:mismatch:profile_view',
            'user_management:student_management:registration_pending:invalid:profile_view',
            'user_management:student_management:registration_pending:valid:profile_view',
            'user_management:student_management:registration_pending:valid:profile_view',
            'user_management:student_management:registration_pending:imported:view',
            'user_management:student_management:registration_pending:invited:view',
            'user_management:student_management:registration_pending:invalid:view',
            'user_management:student_management:registration_pending:valid:view',
            'user_management:student_management:registration_pending:submitted:view',
            'user_management:student_management:registration_pending:mismatch:view',
            'user_management:student_management:registration_pending:expired:view',
        ]),
    ],
    validator.users_get,
    user.list_id,
);
// route.get('/', user.list);
route.post('/', authMiddleware, [userPolicyAuthentication([])], user.insert);
route.get('/user_list/:filter', authMiddleware, [userPolicyAuthentication([])], user.user_list);
route.put(
    '/user_edit',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:profile_edit',
            'user_management:staff_management:registration_pending:invalid:profile_edit',
            'user_management:staff_management:registration_pending:valid:profile_edit',
            'user_management:student_management:registered:profile_edit',
            'user_management:student_management:registration_pending:invalid:profile_edit',
            'user_management:student_management:registration_pending:valid:profile_edit',
        ]),
    ],
    validator.user_edit,
    user.user_edit,
);
route.put(
    '/:id' /* , validator.user_id, validator.user */,
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registration_pending:imported:edit',
            'user_management:staff_management:registration_pending:invited:edit',
            'user_management:staff_management:registration_pending:invalid:edit',
            'user_management:staff_management:registration_pending:valid:edit',
            'user_management:staff_management:registration_pending:expired:edit',

            'user_management:student_management:registration_pending:imported:edit',
            'user_management:student_management:registration_pending:invited:edit',
            'user_management:student_management:registration_pending:invalid:edit',
            'user_management:student_management:registration_pending:valid:edit',
            'user_management:student_management:registration_pending:expired:edit',
        ]),
    ],
    user.update,
);
route.delete(
    '/:id',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registration_pending:imported:delete',
            'user_management:staff_management:registration_pending:invited:delete',
            'user_management:staff_management:registration_pending:expired:delete',
            'user_management:student_management:registration_pending:imported:delete',
            'user_management:student_management:registration_pending:invited:delete',
            'user_management:student_management:registration_pending:expired:delete',
        ]),
    ],
    validator.user_id,
    user.delete,
);
route.post(
    '/import',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registration_pending:all:add_bulk',
            'user_management:student_management:registration_pending:all:add_bulk',
        ]),
    ],
    validator.user_import,
    user.user_staff_student_import,
);
route.post(
    '/single_insert' /* , validator.user_import */,
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registration_pending:all:add_single',
            'user_management:student_management:registration_pending:all:add_single',
        ]),
    ],
    user.single_insert,
);
// route.post('/staff_student_import'/* , validator.user_import */, user.user_staff_student_import);
route.post('/signup', validator.signup, user.signup);
route.post('/login_otp', validator.login, user.user_login);
route.post(
    '/university_user_login',
    [userPolicyAuthentication([])],
    validator.university_user_login,
    user.university_user_login,
);
route.post(
    '/set_password',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    validator.set_password,
    user.set_password,
);
route.post(
    '/register_mobile',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    validator.mobile_register,
    user.register_mobile,
);
route.post(
    '/send_mobile_otp',
    authMiddleware,
    [
        userPolicyAuthentication([
            defaultPolicy.SIGNUP,
            'user_management:staff_management:registered:profile_edit',
            'user_management:student_management:registered:profile_edit',
        ]),
    ],
    validator.mobile_register,
    user.send_mobile_otp,
);
route.post(
    '/otp_verify',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP, defaultPolicy.FORGET])],
    validator.otp_verify,
    user.otp_verify,
);
route.post(
    '/change_mobile',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:profile_edit',
            'user_management:staff_management:registration_pending:invalid:profile_edit',
            'user_management:staff_management:registration_pending:valid:profile_edit',
            'user_management:student_management:registered:profile_edit',
            'user_management:student_management:registration_pending:invalid:profile_edit',
            'user_management:student_management:registration_pending:valid:profile_edit',
        ]),
    ],
    validator.change_mobile,
    user.change_mobile,
);
route.post(
    '/profile_update' /* , validator.profile_update */,
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    user.profile_update,
);
route.post(
    '/profile_document_update',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    (req, res, next) => {
        user_upload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                console.log('AWS error', err);
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    validator.profile_document_update,
    user.profile_document_update,
);
route.post(
    '/profile_staff_document_update',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP])],
    (req, res, next) => {
        staff_upload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                console.log('error', err);
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    validator.profile_staff_document_update,
    user.profile_staff_document_update,
);

route.post(
    '/user_profile_validation',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registration_pending:submitted:update_valid/invalid',
            'user_management:staff_management:registration_pending:mismatch:update_invalid',
            'user_management:student_management:registration_pending:submitted:update_valid/invalid',
            'user_management:student_management:registration_pending:mismatch:update_invalid',
        ]),
    ],
    validator.user_profile_validation,
    user.profile_status_update,
);
route.post(
    '/user_mail_push',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registration_pending:imported:send_mail',
            'user_management:staff_management:registration_pending:invited:send_mail',
            'user_management:staff_management:registration_pending:invalid:send_mail',
            'user_management:staff_management:registration_pending:valid:send_mail',
            'user_management:staff_management:registration_pending:expired:send_mail',
            'user_management:student_management:registration_pending:imported:send_mail',
            'user_management:student_management:registration_pending:invited:send_mail',
            'user_management:student_management:registration_pending:invalid:send_mail',
            'user_management:student_management:registration_pending:valid:send_mail',
            'user_management:student_management:registration_pending:expired:send_mail',
        ]),
    ],
    user.user_mail_push,
);
route.post(
    '/user_face_bio_register',
    authMiddleware,
    [userPolicyAuthentication([])],
    validator.user_face_bio_register,
    user.user_face_bio_register,
);
route.post(
    '/user_academic_allocation',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:academic_edit',
            'user_management:staff_management:registration_pending:invalid:academic_edit',
            'user_management:staff_management:registration_pending:valid:academic_edit',
        ]),
    ],
    validator.user_academic_allocation,
    user.user_academic_allocation,
);
route.post(
    '/user_employment',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:employment_edit',
            'user_management:staff_management:registration_pending:invalid:employment_edit',
            'user_management:staff_management:registration_pending:valid:employment_edit',
        ]),
    ],
    user.user_employment,
);
route.post(
    '/active_inactive',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:inactive:update_status',
            'user_management:student_management:inactive:update_status',
        ]),
    ],
    validator.user_active_inactive,
    user.user_active_inactive,
);

route.post(
    '/user_finger_registration',
    authMiddleware,
    [userPolicyAuthentication([])],
    validator.user_finger_registration,
    user.user_finger_registration,
);

route.post(
    '/user_face_registration',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:biometric_edit',
            'user_management:staff_management:invalid:biometric_edit',
            'user_management:student_management:registered:biometric_edit',
            'user_management:student_management:invalid:biometric_edit',
        ]),
    ],
    (req, res, next) => {
        face_upload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                console.log('AWS error : ', err);
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    validator.user_face_registration,
    user.user_face_registration,
);

route.post(
    '/staff_doc_edit',
    authMiddleware,
    [
        userPolicyAuthentication([
            'user_management:staff_management:registered:profile_edit',
            'user_management:staff_management:registration_pending:invalid:profile_edit',
            'user_management:staff_management:registration_pending:valid:profile_edit',
            'user_management:student_management:registered:profile_edit',
            'user_management:student_management:registration_pending:invalid:profile_edit',
            'user_management:student_management:registration_pending:valid:profile_edit',
        ]),
    ],
    (req, res, next) => {
        staff_student_upload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                console.log('error', err);
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    validator.profile_staff_student_document_update,
    user.staff_doc_edit,
);

route.post(
    '/user_program_adding',
    authMiddleware,
    [userPolicyAuthentication([])],
    validator.user_program_add_delete,
    user.user_program_adding,
);

//Forget Password
route.post('/forget_send_otp', user.forget_send_otp);
route.post(
    '/forget_set_password',
    authMiddleware,
    [userPolicyAuthentication([defaultPolicy.SIGNUP, defaultPolicy.FORGET])],
    user.forget_set_password,
);

const util_token = require('../utility/token.util');
route.post('/refresh_token', util_token.verifyRefreshToken);

route.post(
    '/userBiometricRegister',
    authMiddleware,
    (req, res, next) => {
        face_upload(req, res, (err) => {
            if (err instanceof multer.MulterError) {
                return res.status(500).send('Multer Error in uploading');
            }
            if (err) {
                console.log('AWS error : ', err);
                return res.status(500).send('Unknown Error occurred uploading');
            }
            next();
        });
    },
    // validator.user_face_registration,
    catchAsync((req) => user.userBiometricRegister(req)),
);

module.exports = route;
