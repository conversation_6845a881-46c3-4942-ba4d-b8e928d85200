const constant = require('../utility/constants');
// const institution_calendar = require('mongoose').model(constant.INSTITUTION_CALENDAR);
const program_calendar = require('mongoose').model(constant.PROGRAM_CALENDAR);
const calendar_event = require('mongoose').model(constant.CALENDAR_EVENT);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const program_formate = require('./program_calendar_event_formate');
// const program_calendar = require('../models/program_calendar');
const ObjectId = common_files.convertToMongoObjectId;
// const credit_hours_individual = require('mongoose').model(constant.CREDIT_HOURS_INDIVIDUAL);

const { clearItem, allProgramCalendarDatas } = require('../../service/cache.service');

// Updating Program Calendar Flat Caching Data
const updateProgramCalendarFlatCacheData = async () => {
    clearItem('allProgramCalendar');
    await allProgramCalendarDatas();
};

const termRegExp = (termValue) => {
    return { $regex: new RegExp(termValue, 'i') };
};

exports.list = async (req, res) => {
    const skips = Number(req.query.limit * (req.query.pageNo - 1));
    const limits = Number(req.query.limit) || 0;
    const aggre = [
        { $match: { isDeleted: false, isActive: true, event_calendar: constant.CALENDAR.PROGRAM } },
        // { $lookup: { from: constant.PROGRAM_CALENDAR, localField: '_calendar_id', foreignField: '_id', as: 'program_calendar' } },
        // { $unwind: '$program_calendar' },
        { $skip: skips },
        { $limit: limits },
    ];
    const query = { isDeleted: false, event_calendar: constant.CALENDAR.PROGRAM };
    const doc = await base_control.get_aggregate_with_id_match(calendar_event, aggre, query);
    if (doc.status) {
        const totalPages = Math.ceil(doc.totalDoc / limits);
        // common_files.list_all_response(res, 200, true, "program_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ program_formate.program_calendar_event(doc.data));
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_EVENT_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* program_formate.program_calendar_event(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_id = async (req, res) => {
    const id = req.params.id;
    const aggre = [
        { $match: { _id: ObjectId(id) } },
        { $match: { isDeleted: false, isActive: true, event_calendar: constant.CALENDAR.PROGRAM } },
        {
            $lookup: {
                from: constant.PROGRAM_CALENDAR,
                localField: '_calendar_id',
                foreignField: '_id',
                as: 'program_calendar',
            },
        },
        { $unwind: { path: '$program_calendar', preserveNullAndEmptyArrays: true } },
    ];
    const doc = await base_control.get_aggregate(calendar_event, aggre);
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_EVENT_DETAILS'),
            /* doc.data */ program_formate.program_calendar_event_ID(doc.data[0]),
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_event_date_filter = async (req, res) => {
    const id = ObjectId(req.params.id);
    const start = new Date(req.params.start);
    const end = new Date(req.params.end);
    const aggre = [
        { $match: { _calendar_id: ObjectId(id) } },
        { $match: { isDeleted: false, isActive: true, event_calendar: constant.CALENDAR.PROGRAM } },
        { $match: { event_date: { $gte: start }, end_date: { $lte: end } } },
        {
            $project: {
                'event_name.first_language': 1,
                event_date: 1,
                start_time: 1,
                end_date: 1,
                end_time: 1,
            },
        },
    ];
    const doc = await base_control.get_aggregate(calendar_event, aggre);
    if (doc.status) {
        // common_files.com_response(res, 200, true, "Program_calendar_Event_details", /* doc.data */program_formate.program_calendar_event_ID(doc.data[0]));
        common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_EVENT_DETAILS'),
            doc.data /* program_formate.program_calendar_event_ID(doc.data[0]) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.insert = async (req, res) => {
    const level_id = [];
    const query = [
        { $match: { _id: ObjectId(req.body._calendar_id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        {
            $match: {
                'level.year': req.body.year,
                'level.term': termRegExp(req.body.batch),
            },
        },
        {
            $group: {
                _id: '$level.year',
                id: {
                    $first: '$_id',
                },
                year: { $first: '$level.year' },
                level: {
                    $push: '$level',
                },
            },
        },
        { $addFields: { _id: '$id' } },
    ];
    const pc_year_data = await base_control.get_aggregate(program_calendar, query);
    if (pc_year_data.status) {
        pc_year_data.data[0].level.forEach((element) => {
            if (element.start_date != undefined && element.end_date != undefined) {
                // console.log(Date.parse(new Date(req.body.start_time)));
                // if (Date.parse(element.start_date) <= Date.parse(new Date(req.body.start_time)) && Date.parse(element.end_date) >= Date.parse(new Date(req.body.end_time))) {
                if (
                    Date.parse(element.start_date) <= Date.parse(new Date(req.body.event_date)) &&
                    Date.parse(element.end_date) >= Date.parse(new Date(req.body.end_date))
                ) {
                    level_id.push(element._id);
                }
            }
        });
        if (level_id.length) {
            const objs = {
                event_calendar: req.body.event_calendar,
                event_type: req.body.event_type,
                event_name: {
                    first_language: req.body.event_name.first_language,
                },
                event_date: req.body.event_date,
                start_time: req.body.start_time,
                end_time: req.body.end_time,
                end_date: req.body.end_date,
                _calendar_id: req.body._calendar_id,
            };

            const doc = await base_control.insert(calendar_event, objs);
            if (doc.status) {
                const up_cond = {
                    _id: req.body._calendar_id,
                    'level.year': req.body.year,
                };
                const set_obj = {
                    // $push: { 'level.$[i]._event_id': doc.responses._id }
                    $push: {
                        'level.$[i].events': [
                            {
                                _event_id: doc.responses._id,
                                event_type: req.body.event_type,
                                event_name: req.body.event_name.first_language,
                                event_date: req.body.event_date,
                                start_time: req.body.start_time,
                                end_time: req.body.end_time,
                                end_date: req.body.end_date,
                            },
                        ],
                    },
                };
                const filter = {
                    arrayFilters: [{ 'i._id': { $in: level_id } }],
                };
                const push_event = await base_control.update_condition_array_filter(
                    program_calendar,
                    up_cond,
                    set_obj,
                    filter,
                );
                updateProgramCalendarFlatCacheData();
                if (push_event.status) {
                    // console.log(level_id);
                    // console.log(push_event);
                    common_files.com_response(
                        res,
                        201,
                        true,
                        req.t('PROGRAM_CALENDAR_EVENT_ADDED_SUCCESSFULLY'),
                        push_event.data,
                    );
                } else {
                    await base_control.delete(calendar_event, ObjectId(doc.responses._id));
                    common_files.com_response(
                        res,
                        500,
                        false,
                        req.t('UNABLE_TO_ADD_EVENTS_IN_CALENDAR_CHECK_EVENT_DATE'),
                        req.t('UNABLE_TO_ADD_EVENTS_IN_CALENDAR_CHECK_EVENT_DATE'),
                    );
                }
            } else {
                common_files.com_response(
                    res,
                    500,
                    false,
                    req.t('UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME'),
                    req.t('UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME'),
                );
            }
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('EVENT_DATE_NOT_MATCH'),
                req.t('CHECK_EVENT_DATES'),
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.update = async (req, res) => {
    const checks = await base_control.check_id(program_calendar, {
        _id: req.body._calendar_id,
        isDeleted: false,
    });
    if (checks.status) {
        const object_id = req.body._event_id;
        const objs = {};
        const pc_event = {};
        if (req.body.event_type != undefined) {
            Object.assign(objs, { event_type: req.body.event_type });
            Object.assign(pc_event, { 'level.$[i].events.$[j].event_type': req.body.event_type });
        }
        if (req.body.event_name != undefined) {
            if (req.body.event_name.first_language != undefined) {
                Object.assign(objs, {
                    'event_name.first_language': req.body.event_name.first_language,
                });
                Object.assign(pc_event, {
                    'level.$[i].events.$[j].event_name': req.body.event_name.first_language,
                });
            }
        }
        if (req.body.event_date != undefined) {
            Object.assign(objs, { event_date: req.body.event_date });
            Object.assign(pc_event, { 'level.$[i].events.$[j].event_date': req.body.event_date });
        }
        if (req.body.start_time != undefined) {
            Object.assign(objs, { start_time: req.body.start_time });
            Object.assign(pc_event, { 'level.$[i].events.$[j].start_time': req.body.start_time });
        }
        if (req.body.end_time != undefined) {
            Object.assign(objs, { end_time: req.body.end_time });
            Object.assign(pc_event, { 'level.$[i].events.$[j].end_time': req.body.end_time });
        }
        if (req.body.end_date != undefined) {
            Object.assign(objs, { end_date: req.body.end_date });
            Object.assign(pc_event, { 'level.$[i].events.$[j].end_date': req.body.end_date });
        }

        const doc = await base_control.update(calendar_event, object_id, objs);
        const up_cond = {
            _id: req.body._calendar_id,
            'level.level_no': req.body.level_no,
        };
        const filter = {
            arrayFilters: [
                { 'i.level_no': req.body.level_no },
                { 'j._event_id': req.body._event_id },
            ],
        };
        const push_event = await base_control.update_condition_array_filter(
            program_calendar,
            up_cond,
            pc_event,
            filter,
        );
        updateProgramCalendarFlatCacheData();
        if (doc.status && push_event.status) {
            common_files.com_response(
                res,
                201,
                true,
                req.t('PROGRAM_CALENDAR_EVENT_UPDATE_SUCCESSFULLY'),
                push_event.data,
            );
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_UNABLE_TO_UPDATE_EVENT'),
                push_event.data,
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.delete_event = async (req, res) => {
    const object_id = ObjectId(req.body._id);
    const event_id = ObjectId(req.body._event_id);
    const query = { _id: object_id };
    const cond = { $pull: { 'level.$[i].events': { _event_id: event_id } } };
    const arr_filter = {
        arrayFilters: [{ 'i.level_no': req.body.level_no, 'i.term': termRegExp(req.body.batch) }],
    };
    const doc = await base_control.update_condition_array_filter(
        program_calendar,
        query,
        cond,
        arr_filter,
    );
    updateProgramCalendarFlatCacheData();
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('EVENT_REMOVED_SUCCESSFULLY_IN_PROGRAM_CALENDAR'),
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.course_event_delete = async (req, res) => {
    const up_cond = {
        _id: ObjectId(req.body._calendar_id),
    };
    const cond = {
        $pull: {
            'level.$[i].course.$[j].courses_events': { _event_id: ObjectId(req.body._event_id) },
        },
    };
    const filter = {
        arrayFilters: [
            { 'i.level_no': req.body.level_no, 'i.term': termRegExp(req.body.batch) },
            { 'j._course_id': ObjectId(req.body._course_id) },
        ],
    };

    const doc = await base_control.update_condition_array_filter(
        program_calendar,
        up_cond,
        cond,
        filter,
    );
    updateProgramCalendarFlatCacheData();
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('EVENT_REMOVED_SUCCESSFULLY_IN_PROGRAM_CALENDAR'),
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.rotation_course_event_delete = async (req, res) => {
    const up_cond = {
        _id: ObjectId(req.body._calendar_id),
    };
    const cond = {
        $pull: {
            'level.$[i].rotation_course.$[l].course.$[j].courses_events': {
                _event_id: ObjectId(req.body._event_id),
            },
        },
    };
    const filter = {
        arrayFilters: [
            { 'i.level_no': req.body.level_no, 'i.term': termRegExp(req.body.batch) },
            { 'j._course_id': ObjectId(req.body._course_id) },
            { 'l.rotation_count': req.body.rotation_count },
        ],
    };
    const doc = await base_control.update_condition_array_filter(
        program_calendar,
        up_cond,
        cond,
        filter,
    );
    updateProgramCalendarFlatCacheData();
    if (doc.status) {
        common_files.com_response(
            res,
            200,
            true,
            req.t('EVENT_REMOVED_SUCCESSFULLY_IN_PROGRAM_CALENDAR'),
            doc.data,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
    }
};

exports.list_values = async (req, res) => {
    if (req.body.field != undefined) {
        let proj = '{ _id: 1';
        req.body.field.forEach((element) => {
            proj += ', ' + element + ' : 1';
        });
        proj += '}';
        const query = { isDeleted: false, event_calendar: constant.CALENDAR.PROGRAM };
        const doc = await base_control.get_list(calendar_event, query, proj);
        if (doc.status) {
            common_files.com_response(
                res,
                200,
                true,
                req.t('PROGRAM_CALENDAR_EVENT_LIST'),
                program_formate.program_calendar_event_ID_Array_Only(doc.data),
            );
        } else {
            common_files.com_response(res, 500, false, req.t('ERROR'), doc.data);
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
            req.t('ERROR_PARSE_FIELD_IN_BODY'),
        );
    }
};

exports.calendar_event = async (req, res) => {
    let aggre = [];
    let skips;
    let limits;
    let id = req.params.id;
    if (req.query.limit != undefined || req.query.pageNo != undefined) {
        skips = Number(req.query.limit * (req.query.pageNo - 1));
        limits = Number(req.query.limit) || 0;
        id = req.params.id;
        aggre = [
            { $match: { event_calendar: constant.CALENDAR.PROGRAM, _calendar_id: ObjectId(id) } },
            { $match: { isDeleted: false, isActive: true } },
            { $sort: { updatedAt: -1 } },
            { $skip: skips },
            { $limit: limits },
        ];
    } else {
        aggre = [
            { $match: { event_calendar: constant.CALENDAR.PROGRAM, _calendar_id: ObjectId(id) } },
            { $match: { isDeleted: false, isActive: true } },
            { $sort: { updatedAt: -1 } },
        ];
    }
    const query = {
        isDeleted: false,
        event_calendar: constant.CALENDAR.PROGRAM,
        _calendar_id: ObjectId(id),
    };
    const doc = await base_control.get_aggregate_with_id_match(calendar_event, aggre, query);
    if (doc.status) {
        let totalPages;
        if (req.query.limit != undefined || req.query.pageNo != undefined) {
            totalPages = Math.ceil(doc.totalDoc / limits);
        } else {
            totalPages = 1;
        }
        // common_files.list_all_response(res, 200, true, "institution_calendar_event list", doc.totalDoc, totalPages, Number(req.query.pageNo), /* doc.data */ institution_formate.institution_calendar_event(doc.data));
        common_files.list_all_response(
            res,
            200,
            true,
            req.t('INSTITUTION_CALENDAR_EVENT_LIST'),
            doc.totalDoc,
            totalPages,
            Number(req.query.pageNo),
            doc.data /* institution_formate.institution_calendar_event(doc.data) */,
        );
    } else {
        common_files.com_response(res, 500, false, req.t('THERE_IS_NO_EVENT_DATA_FOUND'), doc.data);
    }
};

exports.event_sync = async (req, res) => {
    let status = false;
    const datas = [];
    const query = [
        { $match: { _id: ObjectId(req.body._calendar_id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.year': req.body.year, 'level.term': termRegExp(req.body.batch) } },
        {
            $group: {
                _id: '$level.year',
                id: {
                    $first: '$_id',
                },
                year: { $first: '$level.year' },
                level: {
                    $push: '$level',
                },
            },
        },
        { $addFields: { _id: '$id' } },
    ];
    const pc_year_data = await base_control.get_aggregate(program_calendar, query);
    const event_checks = await base_control.check_id(calendar_event, {
        _id: { $in: req.body._event_id },
        isDeleted: false,
    });
    if (pc_year_data.status && event_checks.status) {
        event_checks.data.forEach(async (element, index) => {
            let level_id = null;
            pc_year_data.data[0].level.forEach((year_element) => {
                if (year_element.events != undefined) {
                    if (
                        year_element.events.findIndex(
                            (i) => i._event_id.toString() == element._id.toString(),
                        ) == -1
                    ) {
                        if (
                            year_element.start_date != undefined &&
                            year_element.end_date != undefined
                        ) {
                            if (
                                Date.parse(year_element.start_date) <=
                                    Date.parse(element.event_date) &&
                                Date.parse(year_element.end_date) >= Date.parse(element.end_date)
                            ) {
                                level_id = year_element._id;
                            }
                        }
                    } else {
                        datas.push(element._id);
                    }
                } else {
                    if (
                        year_element.start_date != undefined &&
                        year_element.end_date != undefined
                    ) {
                        if (
                            Date.parse(year_element.start_date) <= Date.parse(element.event_date) &&
                            Date.parse(year_element.end_date) >= Date.parse(element.end_date)
                        ) {
                            level_id = year_element._id;
                        }
                    }
                }
            });
            if (level_id != null) {
                const up_cond = {
                    _id: req.body._calendar_id,
                    'level.year': req.body.year,
                };
                const set_obj = {
                    $push: {
                        'level.$[i].events': [
                            {
                                _event_id: element._id,
                                event_type: element.event_type,
                                event_name: element.event_name.first_language,
                                event_date: element.event_date,
                                start_time: element.start_time,
                                end_time: element.end_time,
                                end_date: element.end_date,
                            },
                        ],
                    },
                };
                const filter = {
                    arrayFilters: [{ 'i._id': level_id }],
                };
                const push_event = await base_control.update_condition_array_filter(
                    program_calendar,
                    up_cond,
                    set_obj,
                    filter,
                );
                if (push_event.status) {
                    status = true;
                } else {
                    status = false;
                    datas.push(element._id);
                }
            } else {
                datas.push(element._id);
            }
            if (event_checks.data.length == index + 1) {
                updateProgramCalendarFlatCacheData();
                if (status) {
                    let resp;
                    if (datas.length != 0) {
                        resp = req.t(
                            'Program_calendar_Please_check_Some_Event_date/Already_present_in_that_Year',
                        );
                    } else {
                        resp = req.t('PROGRAM_CALENDAR_EVENT_SYNCED_SUCCESSFULLY');
                    }
                    common_files.com_response(res, 200, true, resp, resp);
                } else {
                    common_files.com_response(
                        res,
                        200,
                        false,
                        req.t('UNABLE_TO_SYNC_EVENT_ID->') +
                            datas +
                            req.t('PLEASE_CHECK_EVENT_DATE/ALREADY_EVENT_PRESENT_IN_THAT_YEAR'),
                        req.t('UNABLE_TO_SYNC_EVENT_ID->') +
                            datas +
                            req.t('PLEASE_CHECK_EVENT_DATE/ALREADY_EVENT_PRESENT_IN_THAT_YEAR'),
                    );
                }
            }
        });
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.copy_event = async (req, res) => {
    let status = true;
    const datas = [];
    const checks = await base_control.get_list_sort(
        program_calendar,
        {
            _id: { $in: req.body._calendar_id },
            isDeleted: false,
            'level.term': termRegExp(req.body.batch),
        },
        {},
        { 'level.level_no': 1 },
    );
    if (checks.status) {
        const from = [];
        let date_check = false;
        const to_year_level = [];
        let from_year = null;
        checks.data[0].level.forEach(async (element) => {
            if (element.year == req.body.from_year) {
                from.push(element);
                if (element.start_date != undefined && element.end_date != undefined) {
                    date_check = true;
                } else {
                    date_check = false;
                }
            }
            const datas = [];
            if (from_year == null || from_year != element.year) {
                from_year = element.year;
                checks.data[0].level.forEach(async (sub_element) => {
                    if (from_year == sub_element.year) {
                        datas.push(sub_element);
                    }
                });
                if (req.body.to_year.indexOf(from_year) != -1) {
                    to_year_level.push({
                        year: element.year,
                        level: datas,
                    });
                }
            }
        });
        let i = 0;
        if (date_check) {
            to_year_level.forEach(async (element, index) => {
                if (element.level.length == from.length) {
                    element.level.forEach(async (sub_element) => {
                        if (i == 2) {
                            i = 0;
                        }
                        const objs = {
                            'level.$.start_date': from[i].start_date,
                            'level.$.end_date': from[i].end_date,
                            'level.$._event_id': from[i]._event_id,
                        };
                        const up_cond = {
                            _id: ObjectId(req.body._calendar_id),
                            'level.level_no': sub_element.level_no,
                            'level.term': termRegExp(req.body.batch),
                        };
                        i++;
                        const updates = await base_control.update_condition(
                            program_calendar,
                            up_cond,
                            objs,
                        );
                        if (updates.status) {
                            status = true;
                        } else {
                            status = false;
                            datas.push(sub_element.level_no);
                        }
                    });
                } else {
                    if (element.level.length == 1) {
                        const events_id = from[0]._event_id.concat(from[1]._event_id);
                        const objs = {
                            'level.$.start_date': from[0].start_date,
                            'level.$.end_date': from[1].end_date,
                            'level.$._event_id': events_id,
                        };
                        const up_cond = {
                            _id: ObjectId(req.body._calendar_id),
                            'level.level_no': element.level[0].level_no,
                            'level.term': termRegExp(req.body.batch),
                        };
                        i++;
                        const updates = await base_control.update_condition(
                            program_calendar,
                            up_cond,
                            objs,
                        );
                        if (updates.status) {
                            status = true;
                        } else {
                            status = false;
                            datas.push(element.level[0].level_no);
                        }
                    }
                }
                if (to_year_level.length == index + 1) {
                    updateProgramCalendarFlatCacheData();
                    if (status) {
                        common_files.com_response(
                            res,
                            201,
                            true,
                            req.t('PROGRAM_CALENDAR_EVENT_SYNCED_SUCCESSFULLY'),
                            req.t('PROGRAM_CALENDAR_EVENT_SYNCED_SUCCESSFULLY'),
                        );
                    } else {
                        common_files.com_response(
                            res,
                            500,
                            false,
                            req.t('UNABLE_TO_COPY_EVENT_THESE_LEVEL_->') + datas,
                            req.t('UNABLE_TO_COPY_EVENT_THESE_LEVEL_->') + datas,
                        );
                    }
                }
            });
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('ERROR_YEAR_LEVEL_NOT_MATCHING'),
                req.t('CHECK_YEAR_LEVEL_NOT_MATCHING'),
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.year_event_date_filter = async (req, res) => {
    const id = ObjectId(req.params.id);
    const start = new Date(req.params.start);
    const end = new Date(req.params.end);
    const aggre = [
        { $match: { _id: ObjectId(id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.level_no': req.params.level_no } },
        // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'events' } },
        { $unwind: { path: '$level.events', preserveNullAndEmptyArrays: true } },
        {
            $match: {
                'level.events.event_date': { $gte: start },
                'level.events.end_date': { $lte: end },
            },
        },
        {
            $addFields: {
                event: {
                    _id: '$level.events._id',
                    _event_id: '$level.events._event_id',
                    event_name: { first_language: '$level.events.event_name' },
                    event_type: '$level.events.event_type',
                    event_date: '$level.events.event_date',
                    start_time: '$level.events.start_time',
                    end_time: '$level.events.end_time',
                    end_date: '$level.events.end_date',
                },
            },
        },
        { $group: { _id: '$_id', event: { $push: '$event' } } },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    const aggre2 = [
        { $match: { _calendar_id: ObjectId(req.params.course), isDeleted: false, isActive: true } },
        { $match: { event_date: { $gte: start }, end_date: { $lte: end } } },
        {
            $project: {
                _event_id: '$_id',
                event_type: 1,
                event_name: 1,
                event_date: 1,
                start_time: 1,
                end_time: 1,
                end_date: 1,
            },
        },
    ];
    const course_event = await base_control.get_aggregate(calendar_event, aggre2);
    let events = 'No Events found';
    if (doc.status) {
        events = doc.data[0].event;
        if (course_event.status) {
            events = doc.data[0].event.concat(course_event.data);
        }
    } else {
        if (course_event.status) {
            events = course_event.data;
        }
    }
    await common_files.com_response(
        res,
        200,
        true,
        req.t('PROGRAM_CALENDAR_EVENT_DETAILS'),
        events /* program_formate.program_calendar_event_ID(doc.data[0]) */,
    );
};

exports.course_event_date_filter = async (req, res) => {
    const id = ObjectId(req.params.id);
    const start = new Date(req.params.start);
    const end = new Date(req.params.end);
    const aggre = [
        { $match: { _id: ObjectId(id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.level_no': req.params.level_no } },
        // { $lookup: { from: constant.CALENDAR_EVENT, localField: 'level._event_id', foreignField: '_id', as: 'events' } },
        { $unwind: { path: '$level.events', preserveNullAndEmptyArrays: true } },
        {
            $match: {
                'level.events.event_date': { $gte: start },
                'level.events.end_date': { $lte: end },
            },
        },
        {
            $addFields: {
                event: {
                    _id: '$level.events._id',
                    _event_id: '$level.events._event_id',
                    event_name: { first_language: '$level.events.event_name' },
                    event_type: '$level.events.event_type',
                    event_date: '$level.events.event_date',
                    start_time: '$level.events.start_time',
                    end_time: '$level.events.end_time',
                    end_date: '$level.events.end_date',
                },
            },
        },
        { $group: { _id: '$_id', event: { $push: '$event' } } },
    ];
    const doc = await base_control.get_aggregate(program_calendar, aggre);
    const aggre2 = [
        { $match: { _calendar_id: ObjectId(req.params.course), isDeleted: false, isActive: true } },
        { $match: { start_time: { $gte: start }, end_time: { $lte: end } } },
        {
            $project: {
                _event_id: '$_id',
                event_type: 1,
                event_name: 1,
                event_date: 1,
                start_time: 1,
                end_time: 1,
                end_date: 1,
            },
        },
    ];
    const course_event = await base_control.get_aggregate(calendar_event, aggre2);
    let events = [];
    if (doc.status) {
        events = doc.data[0].event;
        if (course_event.status) {
            events = doc.data[0].event.concat(course_event.data);
        }
    } else {
        if (course_event.status) {
            events = course_event.data;
        }
    }
    if (course_event.status) {
        await common_files.com_response(
            res,
            200,
            true,
            req.t('PROGRAM_CALENDAR_COURSE_EVENT_DETAILS'),
            events /* program_formate.program_calendar_event_ID(doc.data[0]) */,
        );
    } else {
        await common_files.com_response(
            res,
            404,
            false,
            req.t('UNABLE_TO_FIND_PROGRAM_CALENDAR_COURSE_EVENTS'),
            req.t('UNABLE_TO_FIND_PROGRAM_CALENDAR_COURSE_EVENTS'),
        );
    }
};

exports.course_event_insert = async (req, res) => {
    let level_id = null;
    const query = [
        { $match: { _id: ObjectId(req.body._calendar_id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.year': req.body.year, 'level.term': termRegExp(req.body.batch) } },
        {
            $group: {
                _id: '$level.year',
                id: {
                    $first: '$_id',
                },
                year: { $first: '$level.year' },
                level: {
                    $push: '$level',
                },
            },
        },
        { $addFields: { _id: '$id' } },
    ];
    const pc_year_data = await base_control.get_aggregate(program_calendar, query);
    if (pc_year_data.status) {
        pc_year_data.data[0].level.forEach((element) => {
            if (element.start_date != undefined && element.end_date != undefined) {
                // if (Date.parse(element.start_date) <= Date.parse(new Date(req.body.start_time)) && Date.parse(element.end_date) >= Date.parse(new Date(req.body.end_time))) {
                if (
                    Date.parse(element.start_date) <= Date.parse(new Date(req.body.event_date)) &&
                    Date.parse(element.end_date) >= Date.parse(new Date(req.body.end_date))
                ) {
                    level_id = element._id;
                }
            }
        });
        if (level_id != null) {
            const objs = {
                event_calendar: req.body.event_calendar,
                year: req.body.year,
                event_type: req.body.event_type,
                event_name: {
                    first_language: req.body.event_name.first_language,
                },
                event_date: req.body.event_date,
                start_time: req.body.start_time,
                end_time: req.body.end_time,
                end_date: req.body.end_date,
                _calendar_id: req.body._course_id,
            };

            const doc = await base_control.insert(calendar_event, objs);
            console.log(doc);
            if (doc.status) {
                common_files.com_response(
                    res,
                    201,
                    true,
                    req.t('PROGRAM_CALENDAR_COURSE_EVENT_ADDED_SUCCESSFULLY'),
                    doc.data,
                );
            } else {
                common_files.com_response(
                    res,
                    500,
                    false,
                    req.t('UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME'),
                    req.t('UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME'),
                );
            }
        } else {
            common_files.com_response(
                res,
                500,
                false,
                req.t('EVENT_DATE_NOT_MATCH'),
                req.t('CHECK_EVENT_DATES'),
            );
        }
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};

exports.course_event_edit = async (req, res) => {
    const checks = await base_control.check_id(program_calendar, {
        _id: req.body._calendar_id,
        isDeleted: false,
    });
    if (!checks.status)
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    const object_id = req.body._event_id;
    const objs = {};
    const pc_event = {};
    if (req.body.event_type != undefined) {
        Object.assign(objs, { event_type: req.body.event_type });
        Object.assign(pc_event, {
            'level.$[i].course.$[j].courses_events.$[k].event_type': req.body.event_type,
        });
    }
    if (req.body.event_name != undefined) {
        if (req.body.event_name.first_language != undefined) {
            Object.assign(objs, {
                'event_name.first_language': req.body.event_name.first_language,
            });
            Object.assign(pc_event, {
                'level.$[i].course.$[j].courses_events.$[k].event_name':
                    req.body.event_name.first_language,
            });
        }
    }
    if (req.body.event_date != undefined) {
        Object.assign(objs, { event_date: req.body.event_date });
        Object.assign(pc_event, {
            'level.$[i].course.$[j].courses_events.$[k].event_date': req.body.event_date,
        });
    }
    if (req.body.start_time != undefined) {
        Object.assign(objs, { start_time: req.body.start_time });
        Object.assign(pc_event, {
            'level.$[i].course.$[j].courses_events.$[k].start_time': req.body.start_time,
        });
    }
    if (req.body.end_time != undefined) {
        Object.assign(objs, { end_time: req.body.end_time });
        Object.assign(pc_event, {
            'level.$[i].course.$[j].courses_events.$[k].end_time': req.body.end_time,
        });
    }
    if (req.body.end_date != undefined) {
        Object.assign(objs, { end_date: req.body.end_date });
        Object.assign(pc_event, {
            'level.$[i].course.$[j].courses_events.$[k].end_date': req.body.end_date,
        });
    }
    await base_control.update(calendar_event, object_id, objs);
    const up_cond = {
        _id: ObjectId(req.body._calendar_id),
    };
    const filter = {
        arrayFilters: [
            { 'i.level_no': req.body.level_no, 'i.term': termRegExp(req.body.batch) },
            { 'j._course_id': ObjectId(req.body._course_id) },
            { 'k._event_id': ObjectId(req.body._event_id) },
        ],
    };
    console.log(up_cond, ' ', pc_event, ' ', filter);
    const push_event = await base_control.update_condition_array_filter(
        program_calendar,
        up_cond,
        pc_event,
        filter,
    );
    updateProgramCalendarFlatCacheData();

    if (!push_event.status)
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_UNABLE_TO_UPDATE_EVENT'),
            push_event.data,
        );

    common_files.com_response(
        res,
        200,
        true,
        req.t('PROGRAM_CALENDAR_EVENT UPDATE SUCCESSFULLY'),
        push_event.data,
    );
};

exports.rotation_course_event_edit = async (req, res) => {
    const checks = await base_control.check_id(program_calendar, {
        _id: req.body._calendar_id,
        isDeleted: false,
    });
    if (!checks.status)
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    const pc_event = {};
    const object_id = req.body._event_id;
    const objs = {};
    if (req.body.event_type != undefined) {
        Object.assign(objs, { event_type: req.body.event_type });
        Object.assign(pc_event, {
            'level.$[i].rotation_course.$[l].course.$[j].courses_events.$[k].event_type':
                req.body.event_type,
        });
    }
    if (req.body.event_name != undefined) {
        if (req.body.event_name.first_language != undefined) {
            Object.assign(objs, {
                'event_name.first_language': req.body.event_name.first_language,
            });
            Object.assign(pc_event, {
                'level.$[i].rotation_course.$[l].course.$[j].courses_events.$[k].event_name':
                    req.body.event_name.first_language,
            });
        }
    }
    if (req.body.event_date != undefined) {
        Object.assign(objs, { event_date: req.body.event_date });
        Object.assign(pc_event, {
            'level.$[i].rotation_course.$[l].course.$[j].courses_events.$[k].event_date':
                req.body.event_date,
        });
    }
    if (req.body.start_time != undefined) {
        Object.assign(objs, { start_time: req.body.start_time });
        Object.assign(pc_event, {
            'level.$[i].rotation_course.$[l].course.$[j].courses_events.$[k].start_time':
                req.body.start_time,
        });
    }
    if (req.body.end_time != undefined) {
        Object.assign(objs, { end_time: req.body.end_time });
        Object.assign(pc_event, {
            'level.$[i].rotation_course.$[l].course.$[j].courses_events.$[k].end_time':
                req.body.end_time,
        });
    }
    if (req.body.end_date != undefined) {
        Object.assign(objs, { end_date: req.body.end_date });
        Object.assign(pc_event, {
            'level.$[i].rotation_course.$[l].course.$[j].courses_events.$[k].end_date':
                req.body.end_date,
        });
    }
    const up_cond = {
        _id: ObjectId(req.body._calendar_id),
    };
    const filter = {
        arrayFilters: [
            { 'i.level_no': req.body.level_no, 'i.term': termRegExp(req.body.batch) },
            { 'j._course_id': ObjectId(req.body._course_id) },
            { 'k._event_id': ObjectId(req.body._event_id) },
            { 'l.rotation_count': req.body.rotation_count },
        ],
    };
    await base_control.update(calendar_event, object_id, objs);
    // console.log(up_cond, ' ', pc_event, ' ', filter);
    const push_event = await base_control.update_condition_array_filter(
        program_calendar,
        up_cond,
        pc_event,
        filter,
    );
    updateProgramCalendarFlatCacheData();

    if (!push_event.status)
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_UNABLE_TO_UPDATE_EVENT'),
            push_event.data,
        );

    common_files.com_response(
        res,
        200,
        true,
        req.t('PROGRAM_CALENDAR_EVENT UPDATE SUCCESSFULLY'),
        push_event.data,
    );
};

exports.interim_event_insert = async (req, res) => {
    try {
        let status = false;
        const datas = [];
        const level_id = [];
        const query = [
            { $match: { _id: ObjectId(req.body._calendar_id), isDeleted: false } },
            { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
            { $match: { 'level.year': req.body.year } },
        ];
        if (req.body.batch != 'both') {
            query.push({ $match: { 'level.term': termRegExp(req.body.batch) } });
        }
        query.push(
            {
                $group: {
                    _id: '$level.year',
                    id: {
                        $first: '$_id',
                    },
                    year: { $first: '$level.year' },
                    level: {
                        $push: '$level',
                    },
                },
            },
            { $addFields: { _id: '$id' } },
        );
        const pc_year_data = await base_control.get_aggregate(program_calendar, query);
        if (!pc_year_data.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        req.t('PROGRAM_NOT_FOUND'),
                    ),
                );
        pc_year_data.data[0].level.forEach((element) => {
            if (element.start_date != undefined && element.end_date != undefined) {
                if (
                    Date.parse(element.start_date) <= Date.parse(new Date(req.body.event_date)) &&
                    Date.parse(element.end_date) >= Date.parse(new Date(req.body.end_date))
                ) {
                    level_id.push(element._id);
                }
            }
        });
        if (level_id.length == 0)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('EVENT_DATE_NOT_MATCH'),
                        req.t('CHECK_EVENT_DATES'),
                    ),
                );
        const objs = {
            event_calendar: req.body.event_calendar,
            year: req.body.year,
            event_type: req.body.event_type,
            event_name: {
                first_language: req.body.event_name.first_language,
            },
            event_date: req.body.event_date,
            start_time: req.body.start_time,
            end_time: req.body.end_time,
            end_date: req.body.end_date,
            _calendar_id: req.body._calendar_id,
        };
        const doc = await base_control.insert(calendar_event, objs);
        if (!doc.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        410,
                        false,
                        req.t('UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME'),
                        req.t('UNABLE_TO_CREATE_EVENT_RETRY_AFTER_SOME_TIME'),
                    ),
                );
        const up_cond = {
            _id: req.body._calendar_id,
            'level.year': req.body.year,
        };
        const set_obj = {
            $push: {
                'level.$[i].events': [
                    {
                        _event_id: doc.responses._id,
                        event_type: req.body.event_type,
                        event_name: req.body.event_name.first_language,
                        event_date: req.body.event_date,
                        start_time: req.body.start_time,
                        end_time: req.body.end_time,
                        end_date: req.body.end_date,
                    },
                ],
            },
        };
        for (id_element of level_id) {
            const filter = {
                arrayFilters: [{ 'i._id': id_element }],
            };
            const push_event = await base_control.update_condition_array_filter(
                program_calendar,
                up_cond,
                set_obj,
                filter,
            );
            if (push_event.status) {
                status = true;
            } else {
                await base_control.delete(calendar_event, ObjectId(doc.responses._id));
                status = false;
                datas.push(id_element);
            }
        }
        updateProgramCalendarFlatCacheData();
        if (status) {
            let resp;
            if (datas.length != 0) {
                resp = 'Program calendar Please check Some Event date/Already present in that Year';
            } else {
                resp = 'Program_calendar Event Added successfully';
            }
            return res.status(200).send(common_files.response_function(res, 200, true, resp, resp));
        }
        return res
            .status(403)
            .send(
                common_files.response_function(
                    res,
                    403,
                    false,
                    'Unable to Add Event ID-> ' +
                        datas +
                        ' Please check Event date/Already event present in that Year',
                    'Unable to Add Event ID->' +
                        datas +
                        ' Please check Event date/Already event present in that Year',
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.response_function(
                    res,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.interim_event_sync = async (req, res) => {
    let status = true;
    const datas = [];
    const query = [
        { $match: { _id: ObjectId(req.body._calendar_id), isDeleted: false } },
        { $unwind: { path: '$level', preserveNullAndEmptyArrays: true } },
        { $match: { 'level.year': req.body.year } },
    ];
    if (req.body.batch != 'both') {
        query.push({ $match: { 'level.term': termRegExp(req.body.batch) } });
    }
    query.push(
        {
            $group: {
                _id: '$level.year',
                id: {
                    $first: '$_id',
                },
                year: { $first: '$level.year' },
                level: {
                    $push: '$level',
                },
            },
        },
        { $addFields: { _id: '$id' } },
    );
    const pc_year_data = await base_control.get_aggregate(program_calendar, query);
    const event_checks = await base_control.check_id(calendar_event, {
        _id: { $in: req.body._event_id },
        isDeleted: false,
    });
    if (pc_year_data.status && event_checks.status) {
        event_checks.data.forEach(async (element, index) => {
            const level_id = [];
            pc_year_data.data[0].level.forEach((year_element) => {
                if (year_element.events != undefined) {
                    if (
                        year_element.events.findIndex(
                            (i) => i._event_id.toString() == element._id.toString(),
                        ) == -1
                    ) {
                        if (
                            year_element.start_date != undefined &&
                            year_element.end_date != undefined
                        ) {
                            if (
                                Date.parse(year_element.start_date) <=
                                    Date.parse(element.event_date) &&
                                Date.parse(year_element.end_date) >= Date.parse(element.end_date)
                            ) {
                                level_id.push(year_element._id);
                            }
                        }
                    } else {
                        datas.push(element._id);
                    }
                } else {
                    if (
                        year_element.start_date != undefined &&
                        year_element.end_date != undefined
                    ) {
                        if (
                            Date.parse(year_element.start_date) <= Date.parse(element.event_date) &&
                            Date.parse(year_element.end_date) >= Date.parse(element.end_date)
                        ) {
                            level_id.push(year_element._id);
                        }
                    }
                }
            });
            if (level_id.length != 0) {
                const up_cond = {
                    _id: req.body._calendar_id,
                    'level.year': req.body.year,
                };
                // let set_obj = {
                //     $push: { 'level.$[i]._event_id': ObjectId(element._id) }
                // }
                const set_obj = {
                    // $push: { 'level.$[i]._event_id': doc.responses._id }
                    $push: {
                        'level.$[i].events': [
                            {
                                _event_id: element._id,
                                event_type: element.event_type,
                                event_name: element.event_name.first_language,
                                event_date: element.event_date,
                                start_time: element.start_time,
                                end_time: element.end_time,
                                end_date: element.end_date,
                            },
                        ],
                    },
                };
                level_id.forEach(async (id_element) => {
                    const filter = {
                        arrayFilters: [{ 'i._id': id_element }],
                    };
                    const push_event = await base_control.update_condition_array_filter(
                        program_calendar,
                        up_cond,
                        set_obj,
                        filter,
                    );
                    if (push_event.status) {
                        status = true;
                    } else {
                        status = false;
                        datas.push(element._id);
                    }
                });
            } else {
                datas.push(element._id);
            }
            if (event_checks.data.length == index + 1) {
                updateProgramCalendarFlatCacheData();
                if (status) {
                    let resp;
                    if (datas.length != 0) {
                        resp = req.t(
                            'PROGRAM_CALENDAR_PLEASE_CHECK_SOME_EVENT_DATE/ALREADY_PRESENT_IN_THAT_YEAR',
                        );
                    } else {
                        resp = req.t('PROGRAM_CALENDAR_EVENT_SYNCED_SUCCESSFULLY');
                    }
                    await common_files.com_response(res, 200, true, resp, resp);
                } else {
                    common_files.com_response(
                        res,
                        200,
                        false,
                        req.t('Unable to Sync Event ID-> ') +
                            datas +
                            ' Please check Event date/Already event present in that Year',
                        'Unable to Sync Event ID->' +
                            datas +
                            ' Please check Event date/Already event present in that Year',
                    );
                }
            }
        });
    } else {
        common_files.com_response(
            res,
            500,
            false,
            req.t('ERROR_ID_NOT_MATCH'),
            req.t('CHECK_PARSING_REFERENCE_ID'),
        );
    }
};
