const Joi = require('joi');
const constant = require('../../utility/constants');

const curriculumIdValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
        }),
        body: Joi.object().keys({
            yearIds: Joi.array().items(
                Joi.string()
                    .alphanum()
                    .length(24)
                    .error(() => {
                        return 'YEAR_ID_REQUIRED';
                    }),
            ),
        }),
    })
    .unknown(true);

const listAllCurriculumValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const yearId = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'YEAR_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const institutionId = Joi.object()
    .keys({
        params: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'INSTITUTION_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const levelId = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'LEVEL_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const addCurriculumValidator = Joi.object()
    .keys({
        body: Joi.object().keys({
            _institution_id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => {
                    return error;
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .error((error) => {
                    return error;
                }),
            curriculumName: Joi.string()
                .min(2)
                .max(100)
                .required()
                .error((error) => {
                    return error;
                }),
            hourAs: Joi.string()
                .valid(constant.TOTAL, constant.SPLIT_UP)
                .required()
                .error((error) => {
                    return error;
                }),
            valueAs: Joi.string()
                .valid(constant.FIXED_VALUE, constant.RANGE_VALUES)
                .required()
                .error((error) => {
                    return error;
                }),
            startWeek: Joi.number().error((error) => {
                return error;
            }),
            endWeek: Joi.number().error((error) => {
                return error;
            }),
            programStartAt: Joi.number().error((error) => {
                return error;
            }),
            programEndAt: Joi.number().error((error) => {
                return error;
            }),
            credit: Joi.array()
                .items(
                    Joi.object().keys({
                        min: Joi.number()
                            .integer()
                            .error((error) => {
                                return error;
                            }),
                        max: Joi.number()
                            .integer()
                            .error((error) => {
                                return error;
                            }),
                        sessionType: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                )
                .error((error) => {
                    return error;
                }),
            yearLevel: Joi.array().items(
                Joi.object({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                    preRequisiteName: Joi.string()
                        .min(2)
                        .max(100)
                        .error((error) => {
                            return error;
                        }),
                    certificate: Joi.object().keys({
                        certificateName: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                    _preRequisite_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                    yType: Joi.string()
                        .min(1)
                        .max(100)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                    noOfLevel: Joi.number()
                        .min(0)
                        .required()
                        .error(() => 'NO_OF_LEVEL'),
                    startWeek: Joi.number().error((error) => {
                        return error;
                    }),
                    endWeek: Joi.number().error((error) => {
                        return error;
                    }),
                    levels: Joi.array().items(
                        Joi.object({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error((error) => {
                                    return error;
                                }),
                            levelName: Joi.string()
                                .min(1)
                                .max(100)
                                .required()
                                .error((error) => {
                                    return error;
                                }),
                            startWeek: Joi.number().error((error) => {
                                return error;
                            }),
                            endWeek: Joi.number().error((error) => {
                                return error;
                            }),
                        }),
                    ),
                }),
            ),
            isPhaseLevel: Joi.boolean().error((error) => {
                return error;
            }),
            withoutLevel: Joi.boolean().error((error) => {
                return error;
            }),
        }),
    })
    .unknown(true);

const updateCurriculumValidator = Joi.object()
    .keys({
        params: Joi.object().keys({
            id: Joi.string()
                .alphanum()
                .required()
                .length(24)
                .error(() => 'CURRICULUM_ID_REQUIRED'),
        }),
        body: Joi.object().keys({
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error((error) => {
                    return error;
                }),
            curriculumName: Joi.string()
                .min(2)
                .max(100)
                .error((error) => {
                    return error;
                }),
            hourAs: Joi.string()
                .valid(constant.TOTAL, constant.SPLIT_UP)
                .required()
                .error((error) => {
                    return error;
                }),
            valueAs: Joi.string()
                .valid(constant.FIXED_VALUE, constant.RANGE_VALUES)
                .required()
                .error((error) => {
                    return error;
                }),
            startWeek: Joi.number().error((error) => {
                return error;
            }),
            endWeek: Joi.number().error((error) => {
                return error;
            }),
            programStartAt: Joi.number().error((error) => {
                return error;
            }),
            programEndAt: Joi.number().error((error) => {
                return error;
            }),
            framework: Joi.array()
                .items(
                    Joi.object({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        frameworkName: Joi.string()
                            .min(2)
                            .max(100)
                            .error((error) => {
                                return error;
                            }),
                        _framework_id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                    }),
                )
                .error((error) => {
                    return error;
                }),
            credit: Joi.array()
                .items(
                    Joi.object().keys({
                        _id: Joi.string()
                            .alphanum()
                            .length(24)
                            .error((error) => {
                                return error;
                            }),
                        min: Joi.number()
                            .integer()
                            .error((error) => {
                                return error;
                            }),
                        max: Joi.number()
                            .integer()
                            .error((error) => {
                                return error;
                            }),
                        sessionType: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                )
                .error((error) => {
                    return error;
                }),

            yearLevel: Joi.array().items(
                Joi.object({
                    _id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                    preRequisiteName: Joi.string()
                        .min(2)
                        .max(100)
                        .error((error) => {
                            return error;
                        }),
                    _preRequisite_id: Joi.string()
                        .alphanum()
                        .length(24)
                        .error((error) => {
                            return error;
                        }),
                    certificate: Joi.object().keys({
                        certificateName: Joi.string().error((error) => {
                            return error;
                        }),
                    }),
                    yType: Joi.string()
                        .min(1)
                        .max(100)
                        .error((error) => {
                            return error;
                        }),
                    startWeek: Joi.number().error((error) => {
                        return error;
                    }),
                    endWeek: Joi.number().error((error) => {
                        return error;
                    }),
                    noOfLevel: Joi.number()
                        .min(0)
                        .error(() => 'NO_OF_LEVEL'),
                    levels: Joi.array().items(
                        Joi.object({
                            _id: Joi.string()
                                .alphanum()
                                .length(24)
                                .error((error) => {
                                    return error;
                                }),
                            levelName: Joi.string()
                                .min(1)
                                .max(100)
                                .error((error) => {
                                    return error;
                                }),
                            startWeek: Joi.number().error((error) => {
                                return error;
                            }),
                            endWeek: Joi.number().error((error) => {
                                return error;
                            }),
                        }),
                    ),
                }),
            ),
            isPhaseLevel: Joi.boolean().error((error) => {
                return error;
            }),
            withoutLevel: Joi.boolean().error((error) => {
                return error;
            }),
        }),
    })
    .unknown(true);
const selectiveCourseConfigValidation = Joi.object()
    .keys({
        params: Joi.object().keys({
            _curriculum_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
        }),
        body: Joi.object().keys({
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            _year_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'YEAR_ID_REQUIRED';
                }),
            _level_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'LEVEL_ID_REQUIRED';
                }),
            noOfSelection: Joi.number()
                .required()
                .error(() => {
                    return 'NO_OF_SELECTION';
                }),
            hoursType: Joi.string()
                .valid(constant.SELECTIVE_HOUR_TYPE.TOTAL, constant.SELECTIVE_HOUR_TYPE.SPLIT_UP)
                .required()
                .error(() => {
                    return 'HOURS_TYPE_MUST_BE';
                }),
            selectives: Joi.array().items(
                Joi.number()
                    .required()
                    .error(() => {
                        return 'SELECTIVE_MIN_VALUE_REQUIRED';
                    }),
            ),
        }),
    })
    .unknown(true);
const selectiveDeleteSchema = Joi.object()
    .keys({
        params: Joi.object().keys({
            _curriculum_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
            _selective_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'SELECTIVE_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);

const selectiveGetSchema = Joi.object()
    .keys({
        params: Joi.object().keys({
            _curriculum_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'CURRICULUM_ID_REQUIRED';
                }),
            _program_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'PROGRAM_ID_REQUIRED';
                }),
            _year_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'YEAR_ID_REQUIRED';
                }),
            _level_id: Joi.string()
                .alphanum()
                .length(24)
                .required()
                .error(() => {
                    return 'LEVEL_ID_REQUIRED';
                }),
        }),
    })
    .unknown(true);
module.exports = {
    curriculumIdValidator,
    yearId,
    levelId,
    addCurriculumValidator,
    updateCurriculumValidator,
    institutionId,
    listAllCurriculumValidator,
    selectiveCourseConfigValidation,
    selectiveDeleteSchema,
    selectiveGetSchema,
};
