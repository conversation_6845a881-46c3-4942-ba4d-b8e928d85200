const https = require('https');
const fs = require('fs');
const courseSchedules = require('../../models/course_schedule');
const CourseScheduleSetting = require('../../models/course_schedule_setting');
const Document = require('../../models/document_manager');
const Activity = require('../../models/activities');
const users = require('../../models/user');
const StudentGroups = require('../../models/student_group');
const Course = require('../../models/digi_course');
const { distinct_list, get, bulk_write, update } = require('../../base/base_controller');
const {
    convertToMongoObjectId,
    fileFormatSubTabFilter,
    calculateTeamsDuration,
} = require('../../utility/common');
const { participantDuration } = require('../../../config/index');
const { logger, BUCKET_DOCUMENT } = require('../../utility/util_keys');
const {
    scheduleDateTimeFormatter,
    dateTimeLocalFormatter,
    getSignedURL,
    differenceInMinutes,
    addMinutes,
} = require('../../utility/common_functions');
const constant = require('../../utility/constants');
const { uploadFileToAws, removeFolder } = require('../../utility/question_file_upload');
const { getCourses } = require('../course_session/course_session_service');
// const { PM } = require('../../utility/enums');
const {
    getZoomUUIDList,
    getZoomRecordList,
    getVideoLink,
    tokenGeneration,
    downloadZoomVideo,
    deleteZoomRecordList,
    getZoomParticipantsList,
    getZoomHostUserList,
    createHostZoomMeeting,
    createParticipantURL,
    hostZoomMeetingEnd,
    getZoomMeetingDuration,
} = require('../../../service/zoom.service');
const {
    teamsTokenGeneration,
    teamsUserListByEmail,
    createTeamsMeetingAPI,
    getTeamsParticipantList,
    getTeamsMeetingOccurences,
} = require('../../../service/teams.service');
const {
    PRESENT,
    ABSENT,
    DC_STAFF,
    DC_STUDENT,
    REMOTE_PLATFORM: { ZOOM, TEAMS },
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
} = require('../../utility/constants');
const { localLogger } = require('../../utility/locallogger');
const { s3FileURL } = require('../../utility/file_upload');
// const InstitutionCalendars = require('../../utility/institution_calendar');
const leavePermissionOnDuty = [ONDUTY, LEAVE, PERMISSION];

const getInfraById = async (infraIds) => {
    try {
        csQuery = { 'programs.remoteScheduling._id': { $in: infraIds } };
        csProject = { 'programs.remoteScheduling': 1 };

        return await CourseScheduleSetting.find(csQuery, csProject);
    } catch (error) {
        throw new Error(error);
    }
};
const DocumentCreations = async (datas) => {
    try {
        const documents = datas.map(function (DocsElements) {
            const data = {
                type: DocsElements.type,
                name: DocsElements.name,
                url: DocsElements.url,
                _program_id: DocsElements._program_id,
                year_no: DocsElements.year_no,
                level_no: DocsElements.level_no,
                term: DocsElements.term,
                rotation: DocsElements.rotation,
                _institution_calendar_id: DocsElements._institution_calendar_id,
                remote: true,
            };
            if (DocsElements.rotation_count) {
                data.rotation_count = DocsElements.rotation_count;
            }
            if (DocsElements.courseAdmin === 'true') {
                data.courseAdmin = true;
            }
            if (DocsElements.type !== 'url') {
                data.url = DocsElements.file;
            }
            if (DocsElements._course_id) {
                data._course_id = DocsElements._course_id;
            }
            if (DocsElements.sessions) {
                const sessionIds = DocsElements.sessions.map((session) => {
                    return {
                        type: session.type,
                        _id: convertToMongoObjectId(session._id),
                    };
                });
                data.sessionOrScheduleIds = sessionIds;
            }
            if (DocsElements.title) {
                data.title = DocsElements.title;
            }
            if (DocsElements._user_id) {
                data._user_id = DocsElements._user_id;
            }

            return {
                insertOne: { document: data },
            };
        });

        if (documents.length > 0) {
            const result = await Document.bulkWrite(documents);

            if (result.insertedIds) {
                return { status: true, insertedIds: result.insertedIds };
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};
const multipleDocumentCreations = async (datas) => {
    try {
        const updateDocument = [];
        for (const DocsElements of datas) {
            const documentData = {
                type: DocsElements.type,
                name: DocsElements.name,
                url: DocsElements.url,
                _program_id: DocsElements._program_id,
                year_no: DocsElements.year_no,
                level_no: DocsElements.level_no,
                term: DocsElements.term,
                rotation: DocsElements.rotation,
                _institution_calendar_id: DocsElements._institution_calendar_id,
                remote: true,
            };
            if (DocsElements.rotation_count) {
                documentData.rotation_count = DocsElements.rotation_count;
            }
            if (DocsElements.courseAdmin === 'true') {
                documentData.courseAdmin = true;
            }
            if (DocsElements.type !== 'url') {
                documentData.url = DocsElements.files.map((fileElement) => {
                    const location = fileElement.location;
                    // Use a regular expression to capture the timestamp
                    const match = location.match(/\/(\d+)-/);
                    // Extract the captured timestamp
                    const timestamp = match ? match[1] : null;
                    const fileName =
                        timestamp + '-' + fileElement.originalname.replace(/[^0-9a-zA-Z.]/g, '_');
                    return s3FileURL({
                        filePath: BUCKET_DOCUMENT,
                        fileName,
                    });
                });
            } else {
                documentData.url = [DocsElements.url];
            }
            if (DocsElements._course_id) {
                documentData._course_id = DocsElements._course_id;
            }
            if (DocsElements.sessions) {
                const sessionIds = DocsElements?.sessions?.map((session) => {
                    return {
                        type: session.type,
                        _id: convertToMongoObjectId(session._id),
                    };
                });
                documentData.sessionOrScheduleIds = sessionIds;
            }
            if (DocsElements.title) {
                documentData.title = DocsElements.title;
            }
            if (DocsElements._user_id) {
                documentData._user_id = DocsElements._user_id;
            }
            documentData.url.forEach((urlItem, index) => {
                const documentName =
                    documentData.url.length === 1
                        ? documentData.name
                        : `${documentData.name} ${Number(index) + 1}`;

                updateDocument.push({
                    insertOne: {
                        document: {
                            type: documentData.type,
                            name: documentName,
                            rotation_count: documentData.rotation_count,
                            courseAdmin: documentData.courseAdmin,
                            sessionOrScheduleIds: documentData.sessionOrScheduleIds,
                            title: documentData.title,
                            _user_id: documentData._user_id,
                            url: urlItem,
                            _course_id: documentData._course_id,
                            _program_id: documentData._program_id,
                            year_no: documentData.year_no,
                            level_no: documentData.level_no,
                            term: documentData.term,
                            rotation: documentData.rotation,
                            _institution_calendar_id: documentData._institution_calendar_id,
                            remote: documentData.remote,
                        },
                    },
                });
            });
        }

        if (updateDocument.length > 0) {
            const documentResponse = await Document.bulkWrite(updateDocument);
            if (documentResponse.insertedIds) {
                return { status: true, insertedIds: documentResponse.insertedIds };
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};
const getCoursesForActivity = async (staffId, courseAdmin, headerInstitutionCalendarId) => {
    try {
        // const { institutionCalendarIds } = await InstitutionCalendars({ _institution_id });
        const courseScheduleQuery = { schedule_date: { $exists: true }, isDeleted: false };

        if (courseAdmin === 'true') {
            // check course admin verification
            const courses = await Course.find({
                'coordinators._user_id': convertToMongoObjectId(staffId),
            });

            if (courses.length) {
                const currentCourseIds = courses.map((course) => course._id);
                if (currentCourseIds.length > 0) {
                    courseScheduleQuery._course_id = { $in: currentCourseIds };
                } else {
                    courseScheduleQuery.$or = [
                        { 'staffs._staff_id': convertToMongoObjectId(staffId) },
                        { 'students._id': convertToMongoObjectId(staffId) },
                    ];
                }
            }
        } else {
            courseScheduleQuery.$or = [
                { 'staffs._staff_id': convertToMongoObjectId(staffId) },
                { 'students._id': convertToMongoObjectId(staffId) },
            ];
        }
        // courseScheduleQuery._institution_calendar_id = { $in: institutionCalendarIds };
        courseScheduleQuery._institution_calendar_id = convertToMongoObjectId(
            headerInstitutionCalendarId,
        );
        const coursesList = await courseSchedules.find(courseScheduleQuery, {
            course_name: 1,
            course_code: 1,
            _program_id: 1,
            _institution_calendar_id: 1,
            year_no: 1,
            program_name: 1,
            level_no: 1,
            rotation: 1,
            rotation_count: 1,
            term: 1,
            _id: '$_course_id',
        });
        let coursesLists = [];
        coursesList.filter(function (coursesListsEntry) {
            const chkCoursesList = coursesLists.findIndex(
                (coursesListEntry) =>
                    coursesListEntry.course_name === coursesListsEntry.course_name &&
                    coursesListEntry.course_code == coursesListsEntry.course_code &&
                    coursesListEntry._program_id.toString() ===
                        coursesListsEntry._program_id.toString() &&
                    coursesListEntry._institution_calendar_id.toString() ===
                        coursesListsEntry._institution_calendar_id.toString() &&
                    coursesListEntry.year_no === coursesListsEntry.year_no &&
                    coursesListEntry.level_no === coursesListsEntry.level_no &&
                    coursesListEntry.rotation === coursesListsEntry.rotation &&
                    coursesListEntry.rotation_count === coursesListsEntry.rotation_count &&
                    coursesListEntry.term == coursesListsEntry.term &&
                    coursesListEntry._id.toString() === coursesListsEntry._id.toString(),
            );
            if (chkCoursesList <= -1) {
                coursesLists.push(coursesListsEntry);
            }
            return null;
        });
        coursesLists = coursesLists.filter((coursesList) => coursesList.rotation_count !== null);
        return coursesLists;
    } catch (error) {
        throw new Error(error);
    }
};
const getDocumentList = async (
    userId,
    limit,
    page,
    search,
    tab,
    subTab,
    type,
    courseAdmin,
    _institution_calendar_id,
    remote,
) => {
    try {
        const sessionIdsList = [];
        const getCourseInfo = await getCoursesForActivity(
            userId,
            courseAdmin,
            _institution_calendar_id,
        );
        const DocumentQuery = { isDeleted: false };
        if (getCourseInfo.length > 0) {
            const getCourseParams = [];
            for (const getCourseParam of getCourseInfo) {
                const courseParam = {
                    _institution_calendar_id: getCourseParam._institution_calendar_id,
                    _program_id: getCourseParam._program_id,
                    term: getCourseParam.term,
                    year_no: getCourseParam.year_no,
                    level_no: getCourseParam.level_no,
                    _course_id: getCourseParam._id,
                    rotation: getCourseParam.rotation,
                };
                if (getCourseParam.rotation_count) {
                    courseParam.rotation_count = getCourseParam.rotation_count;
                }
                getCourseParams.push(courseParam);
            }

            const cQuery = { isDeleted: false };
            if (type === DC_STUDENT) {
                cQuery['students._id'] = convertToMongoObjectId(userId);
            } else {
                cQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
            cQuery.$or = getCourseParams;
            const courseSchedule = await courseSchedules.find(cQuery, {
                _id: 1,
                _course_id: 1,
                course_name: 1,
                course_code: 1,
                session: 1,
            });
            // course based documents loading without session based
            /*  const courseSessionIds = [];
            for (cSchedule of courseSchedule) {
                if (cSchedule.session && cSchedule.session._session_id) {
                    courseSessionIds.push({
                        'sessionOrScheduleIds._id': cSchedule.session._session_id,
                    });
                } else {
                    courseSessionIds.push({ 'sessionOrScheduleIds._id': cSchedule._id });
                }
            }
            if (courseSessionIds.length > 0) {
                DocumentQuery.$or = courseSessionIds;
            } */

            DocumentQuery._course_id = {
                $in: [
                    ...new Set(
                        courseSchedule.map((scheduleElement) =>
                            scheduleElement._course_id.toString(),
                        ),
                    ),
                ],
            };
            let staffIdList;
            if (search) {
                const staffIds = await users.find(
                    {
                        $or: [
                            { 'name.first': new RegExp(search, 'i') },
                            { 'name.last': new RegExp(search, 'i') },
                        ],
                    },
                    { _id: 1, name: 1 },
                );
                staffIdList = staffIds.map((userId) => userId._id);
            }
            if (search) {
                DocumentQuery.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { _user_id: { $in: staffIdList } },
                ];
            }
            if (tab) {
                DocumentQuery.starred = convertToMongoObjectId(userId);
            }
            if (subTab) {
                DocumentQuery.url = fileFormatSubTabFilter(subTab);
            }
            if (_institution_calendar_id) {
                DocumentQuery._institution_calendar_id =
                    convertToMongoObjectId(_institution_calendar_id);
            }
            if (remote) {
                DocumentQuery.remote = true;
            }
            // get query document data
            let data;
            let totalDoc;
            let totalPages;
            let currentPage;
            if (limit && page) {
                // make perPage and pageNo
                const perPage = parseInt(limit > 0 ? limit : 10);
                const pageNo = parseInt(page > 0 ? page : 1);
                data = await Document.find(DocumentQuery)
                    .sort({ createdAt: -1 })
                    .skip(perPage * (pageNo - 1))
                    .limit(perPage);
                //get all document
                totalDoc = await Document.find(DocumentQuery).countDocuments().exec();
                // filter pages
                totalPages = Math.ceil(totalDoc / perPage);
                currentPage = pageNo;
            } else {
                data = await Document.find(query).sort({ createdAt: -1 });
            }

            const sessionIds = [];
            const suportEvents = [];
            const suportSessions = [];
            for (const element of data) {
                // const matchURLData = element.url ? element.url.match(/amazonaws.com/g) : null;
                if (
                    // matchURLData !== null ||
                    element.type === 'file' &&
                    element.url /* &&
                    element.url.split('/') */
                ) {
                    element.url = await getSignedURL(element.url);
                }
                await element.sessionOrScheduleIds.forEach(async (datas) => {
                    if (datas.type === constant.SCHEDULE_TYPES.REGULAR) {
                        if (datas._id && sessionIds.indexOf(datas._id.toString()) < 0) {
                            sessionIds.push(datas._id.toString());
                        }
                    }
                    if (datas.type === constant.SCHEDULE_TYPES.EVENT) {
                        if (datas._id && suportEvents.indexOf(datas._id.toString()) < 0) {
                            suportEvents.push(datas._id.toString());
                        }
                    }
                    if (datas.type === constant.SCHEDULE_TYPES.SUPPORT_SESSION) {
                        if (datas._id && suportSessions.indexOf(datas._id.toString()) < 0) {
                            suportSessions.push(datas._id.toString());
                        }
                    }
                });
            }

            const userIds = data.map((userId) => userId._user_id);

            const sessions = sessionIds.map((session) => convertToMongoObjectId(session));

            const supportEvent = suportEvents.map((events) => convertToMongoObjectId(events));

            const suportSession = suportSessions.map((events) => convertToMongoObjectId(events));

            const staffList = await users.find({ _id: { $in: userIds } }, { _id: 1, name: 1 });

            const doc = await distinct_list(courseSchedules, 'session', {
                isDeleted: false,
                'session._session_id': { $in: sessions },
            });

            const docSupport = await courseSchedules.find(
                { _id: { $in: supportEvent } },
                { _id: 1, title: 1 },
            );
            const docSession = await courseSchedules.find(
                { _id: { $in: suportSession } },
                { _id: 1, title: 1 },
            );
            const documents = data.map(function (Docs) {
                Items = {
                    _id: Docs._id,
                    type: Docs.type,
                    name: Docs.name,
                    url: Docs.url,
                    _course_id: Docs._course_id,
                    _program_id: Docs._program_id,
                    year_no: Docs.year_no,
                    level_no: Docs.level_no,
                    term: Docs.term,
                    rotation: Docs.rotation,
                    rotation_count: Docs.rotation_count,
                    _institution_calendar_id: Docs._institution_calendar_id,
                    createdAt: Docs.createdAt,
                    updatedAt: Docs.updatedAt,
                    courseAdmin: Docs.courseAdmin,
                    remote: Docs.remote,
                };

                const courseDetails = getCourseInfo.find(
                    (courseData) => courseData._id.toString() === Docs._course_id.toString(),
                );
                if (courseDetails) {
                    Items.courseDetail = {
                        course_code: courseDetails.course_code,
                        course_name: courseDetails.course_name,
                        _course_id: courseDetails._id,
                    };
                }

                Items.starred = !!Docs.starred.find((userIds) => userIds.toString() === userId);

                const sessionDatas = [];
                if (Docs.sessionOrScheduleIds) {
                    Docs.sessionOrScheduleIds.map((session_id) => {
                        const sessionDetails = doc.data.find(
                            (session) =>
                                session._session_id &&
                                session._session_id.toString() === session_id.id.toString(),
                        );
                        const supportDetails = docSupport.find(
                            (session) =>
                                session._id && session._id.toString() === session_id.id.toString(),
                        );

                        const supportSessionDetails = docSession.find(
                            (session) =>
                                session._id && session._id.toString() === session_id.id.toString(),
                        );

                        if (sessionDetails) {
                            sessionDatas.push({
                                _id: sessionDetails._session_id,
                                delivery_symbol: sessionDetails.delivery_symbol,
                                delivery_no: sessionDetails.delivery_no,
                                session_type: sessionDetails.session_type,
                                session_topic: sessionDetails.session_topic,
                                type: constant.SCHEDULE_TYPES.REGULAR,
                            });
                        }
                        if (supportDetails) {
                            sessionDatas.push({
                                _id: supportDetails._id,
                                title: supportDetails.title,
                                type: constant.SCHEDULE_TYPES.EVENT,
                            });
                        }
                        if (supportSessionDetails) {
                            sessionDatas.push({
                                _id: supportSessionDetails._id,
                                title: supportSessionDetails.title,
                                type: constant.SCHEDULE_TYPES.SUPPORT_SESSION,
                            });
                        }
                    });
                }
                const userDetails = staffList.find(
                    (user) =>
                        user._id &&
                        Docs._user_id &&
                        user._id.toString() === Docs._user_id.toString(),
                );
                if (userDetails) {
                    Items.UploadedBy = userDetails;
                }
                Items.sessions = sessionDatas;
                return Items;
            });
            if (limit && page) {
                return { totalDoc, totalPages, currentPage, documents };
            }
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documents: [] };
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseDocumentList = async (
    userId,
    courseId,
    limit,
    page,
    search,
    tab,
    subTab,
    type,
    _program_id,
    courseAdmin,
    remote,
) => {
    try {
        let query = { isDeleted: false };
        let courseDatas;
        let courseIdsList;
        const sessionIdsList = [];
        let staffIdList;
        if (search) {
            const staffIds = await users.find(
                {
                    $or: [
                        { 'name.first': new RegExp(search, 'i') },
                        { 'name.last': new RegExp(search, 'i') },
                    ],
                },
                { _id: 1, name: 1 },
            );
            staffIdList = staffIds.map((userId) => userId._id);
        }
        if (type === 'student') {
            // This comment is used to show directly course based instead of session based
            /*   if (type === 'student') {
                courseDatas = await courseSchedules.find(
                    {
                        'students._id': convertToMongoObjectId(userId),
                        _course_id: convertToMongoObjectId(courseId),
                        isDeleted: false,
                    },
                    { _id: 1, _course_id: 1, course_name: 1, course_code: 1, session: 1 },
                );

                courseIdsList = courseDatas.map((courseId) => courseId._course_id);

                for (courseSchedule of courseDatas) {
                    if (courseSchedule.session && courseSchedule.session._session_id) {
                        sessionIdsList.push(courseSchedule.session._session_id);
                    } else {
                        sessionIdsList.push(courseSchedule._id);
                    }
                }
            } */

            if (search) {
                if (type === 'student') {
                    query = {
                        // This comment is used to show directly course based instead of session based
                        // _course_id: { $in: courseIdsList },
                        _course_id: convertToMongoObjectId(courseId),
                        $or: [
                            { name: { $regex: search, $options: 'i' } },
                            { _user_id: { $in: staffIdList } },
                        ],
                        isDeleted: false,
                    };
                } else {
                    query = {
                        _course_id: convertToMongoObjectId(courseId),
                        //name: { $regex: search },
                        $or: [
                            { name: { $regex: search, $options: 'i' } },
                            { _user_id: { $in: staffIdList } },
                        ],
                        isDeleted: false,
                    };
                }
            } else {
                if (type === 'student') {
                    query = {
                        _course_id: { $in: courseIdsList },
                        isDeleted: false,
                    };
                } else {
                    query = {
                        _course_id: convertToMongoObjectId(courseId),
                        isDeleted: false,
                    };
                }
            }
            if (tab === 'true') {
                query.starred = convertToMongoObjectId(userId);
            }
            if (subTab) {
                query.url = fileFormatSubTabFilter(subTab);
            }

            if (_program_id) {
                query._program_id = convertToMongoObjectId(_program_id);
                query._course_id = convertToMongoObjectId(courseId);
            }
        } else {
            // This comment is used to show directly course based instead of session based
            /* const cQuery = {
                _course_id: convertToMongoObjectId(courseId),
                level_no,
                year_no,
                term,
                rotation,
                _program_id: convertToMongoObjectId(_program_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                isDeleted: false,
            };
            if (rotation_count) {
                cQuery.rotation_count = rotation_count;
            }
            if (courseAdmin !== 'true') {
                cQuery['staffs._staff_id'] = convertToMongoObjectId(userId);
            }
             const courseSchedule = await courseSchedules.find(cQuery, {
                _id: 1,
                _course_id: 1,
                course_name: 1,
                course_code: 1,
                session: 1,
            });

             const sessionIds = [];
            for (cSchedule of courseSchedule) {
                if (cSchedule.session && cSchedule.session._session_id) {
                    sessionIds.push({ 'sessionOrScheduleIds._id': cSchedule.session._session_id });
                } else {
                    sessionIds.push({ 'sessionOrScheduleIds._id': cSchedule._id });
                }
            }
           if (sessionIds.length > 0) {
                   query.$or = sessionIds;
            } */
            if (tab === 'true') {
                query.starred = convertToMongoObjectId(userId);
            }
            if (subTab) {
                query.url = fileFormatSubTabFilter(subTab);
            }
            if (search) {
                query.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { _user_id: { $in: staffIdList } },
                ];
            }
            if (_program_id) {
                query._program_id = convertToMongoObjectId(_program_id);
                query._course_id = convertToMongoObjectId(courseId);
            }
        }
        if (remote) {
            query.remote = true;
        }
        // get query document data
        let data;
        let totalDoc;
        let totalPages;
        let currentPage;
        if (limit && page) {
            // make perPage and pageNo
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            data = await Document.find(query)
                .sort({ createdAt: -1 })
                .skip(perPage * (pageNo - 1))
                .limit(perPage);
            //get all document
            totalDoc = await Document.find(query).countDocuments().exec();
            // filter pages
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        } else {
            data = await Document.find(query).sort({ createdAt: -1 });
        }

        const sessionIds = [];
        const suportEvents = [];
        const suportSessions = [];
        const courseIds = [];
        // await data.forEach(async (element) => {
        for (element of data) {
            // const matchURLData = element.url ? element.url.match(/amazonaws.com/g) : null;
            localLogger.info(
                { Document: element },
                'courseWise Document -> getCourseDocumentList -> Complete Data',
            );
            if (
                // matchURLData !== null ||
                element.type === 'file' &&
                element.url /* &&
                element.url.split('/') */
            ) {
                localLogger.info(
                    { url: element.url },
                    'courseWise Document -> getCourseDocumentList -> Before Sign',
                );
                element.url = await getSignedURL(element.url);
                localLogger.info(
                    { url: element.url },
                    'courseWise Document -> getCourseDocumentList -> After Sign',
                );
            }
            await element.sessionOrScheduleIds.forEach(async (datas) => {
                if (datas.type === constant.SCHEDULE_TYPES.REGULAR) {
                    if (datas._id && sessionIds.indexOf(datas._id.toString()) < 0) {
                        sessionIds.push(datas._id.toString());
                    }
                }
                if (datas.type === constant.SCHEDULE_TYPES.EVENT) {
                    if (datas._id && suportEvents.indexOf(datas._id.toString()) < 0) {
                        suportEvents.push(datas._id.toString());
                    }
                }
                if (datas.type === constant.SCHEDULE_TYPES.SUPPORT_SESSION) {
                    if (datas._id && suportSessions.indexOf(datas._id.toString()) < 0) {
                        suportSessions.push(datas._id.toString());
                    }
                }
            });
        }
        // });
        const userIds = data.map((userId) => userId._user_id);

        const staffList = await users.find({ _id: { $in: userIds } }, { _id: 1, name: 1 });

        const sessions = sessionIds.map((session) => convertToMongoObjectId(session));

        const supportEvent = suportEvents.map((events) => convertToMongoObjectId(events));

        const suportSession = suportSessions.map((events) => convertToMongoObjectId(events));

        const doc = await distinct_list(courseSchedules, 'session', {
            isDeleted: false,
            'session._session_id': { $in: sessions },
        });

        const docSupport = await courseSchedules.find(
            { _id: { $in: supportEvent } },
            { _id: 1, title: 1 },
        );
        const docSession = await courseSchedules.find(
            { _id: { $in: suportSession } },
            { _id: 1, title: 1 },
        );
        const documents = data.map(function (Docs) {
            Items = {
                _id: Docs._id,
                type: Docs.type,
                name: Docs.name,
                url: Docs.url,
                _course_id: Docs._course_id,
                _program_id: Docs._program_id,
                year_no: Docs.year_no,
                level_no: Docs.level_no,
                term: Docs.term,
                rotation: Docs.rotation,
                rotation_count: Docs.rotation_count,
                _institution_calendar_id: Docs._institution_calendar_id,
                createdAt: Docs.createdAt,
                updatedAt: Docs.updatedAt,
                courseAdmin: Docs.courseAdmin,
                remote: Docs.remote,
            };
            Items.starred = !!Docs.starred.find((userIds) => userIds.toString() === userId);
            const sessionDatas = [];
            if (Docs.sessionOrScheduleIds) {
                Docs.sessionOrScheduleIds.map((session_id) => {
                    const sessionDetails = doc.data.find(
                        (session) =>
                            session._session_id &&
                            session._session_id.toString() === session_id.id.toString(),
                    );
                    const supportDetails = docSupport.find(
                        (session) =>
                            session._id && session._id.toString() === session_id.id.toString(),
                    );

                    const supportSessionDetails = docSession.find(
                        (session) =>
                            session._id && session._id.toString() === session_id.id.toString(),
                    );
                    if (sessionDetails) {
                        sessionDatas.push({
                            _id: sessionDetails._session_id,
                            delivery_symbol: sessionDetails.delivery_symbol,
                            delivery_no: sessionDetails.delivery_no,
                            session_type: sessionDetails.session_type,
                            session_topic: sessionDetails.session_topic,
                            type: constant.SCHEDULE_TYPES.REGULAR,
                        });
                    }
                    if (supportDetails) {
                        sessionDatas.push({
                            _id: supportDetails._id,
                            title: supportDetails.title,
                            type: constant.SCHEDULE_TYPES.EVENT,
                        });
                    }
                    if (supportSessionDetails) {
                        sessionDatas.push({
                            _id: supportSessionDetails._id,
                            title: supportSessionDetails.title,
                            type: constant.SCHEDULE_TYPES.SUPPORT_SESSION,
                        });
                    }
                });
            }
            const userDetails = staffList.find(
                (user) =>
                    user._id && Docs._user_id && user._id.toString() === Docs._user_id.toString(),
            );
            if (userDetails) {
                Items.UploadedBy = userDetails;
            }
            Items.sessions = sessionDatas;
            return Items;
        });
        if (limit && page) {
            return { totalDoc, totalPages, currentPage, documents };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documents };
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseAdminDocuments = async (
    userId,
    courseId,
    limit,
    page,
    search,
    tab,
    subTab,
    type,
) => {
    try {
        const query = {};
        let courseDatas;
        let courseIdsList;
        const sessionIdsList = [];
        const courseQuery = {
            'coordinators._user_id': convertToMongoObjectId(userId),
        };
        if (courseId) {
            courseQuery._id = convertToMongoObjectId(courseId);
        }
        const courses = await Course.find(courseQuery);

        if (courses.length) {
            const currentCourseIds = courses.map((course) => course._id);
            if (currentCourseIds) {
                query._course_id = { $in: currentCourseIds };
                query.isDeleted = false;
            }
        } else {
            return { totalDoc: 0, totalPages: 0, currentPage: 0, documents: [] };
        }
        if (search) {
            query.name = { $regex: search, $options: 'i' };
        }
        if (tab) {
            query.starred = convertToMongoObjectId(userId);
        }
        if (subTab) {
            query.url = fileFormatSubTabFilter(subTab);
        }
        // get query document data
        let data;
        let totalDoc;
        let totalPages;
        let currentPage;
        if (limit && page) {
            // make perPage and pageNo
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            data = await Document.find(query)
                .sort({ createdAt: -1 })
                .skip(perPage * (pageNo - 1))
                .limit(perPage);
            //get all document
            totalDoc = await Document.find(query).countDocuments().exec();
            // filter pages
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        } else {
            data = await Document.find(query).sort({ createdAt: -1 });
        }

        const sessionIds = [];
        const suportEvents = [];
        const suportSessions = [];
        const courseIds = [];
        await data.forEach(async (element) => {
            // if (element.type === 'file' && element.url && element.url.split('/')) {
            //     element.url = await getSignedURL(element.url);
            // }
            await element.sessionOrScheduleIds.forEach(async (datas) => {
                if (datas.type === constant.SCHEDULE_TYPES.REGULAR) {
                    if (datas._id && sessionIds.indexOf(datas._id.toString()) < 0) {
                        sessionIds.push(datas._id.toString());
                    }
                }
                if (datas.type === constant.SCHEDULE_TYPES.EVENT) {
                    if (datas._id && suportEvents.indexOf(datas._id.toString()) < 0) {
                        suportEvents.push(datas._id.toString());
                    }
                }
                if (datas.type === constant.SCHEDULE_TYPES.SUPPORT_SESSION) {
                    if (datas._id && suportSessions.indexOf(datas._id.toString()) < 0) {
                        suportSessions.push(datas._id.toString());
                    }
                }
            });
        });
        const userIds = data.map((userId) => userId._user_id);

        const staffList = await users.find({ _id: { $in: userIds } }, { _id: 1, name: 1 });

        const sessions = sessionIds.map((session) => convertToMongoObjectId(session));

        const supportEvent = suportEvents.map((events) => convertToMongoObjectId(events));

        const suportSession = suportSessions.map((events) => convertToMongoObjectId(events));

        const doc = await distinct_list(courseSchedules, 'session', {
            isDeleted: false,
            'session._session_id': { $in: sessions },
        });

        const docSupport = await courseSchedules.find(
            { _id: { $in: supportEvent } },
            { _id: 1, title: 1 },
        );
        const docSession = await courseSchedules.find(
            { _id: { $in: suportSession } },
            { _id: 1, title: 1 },
        );
        const documents = data.map(function (Docs) {
            Items = {
                _id: Docs._id,
                type: Docs.type,
                name: Docs.name,
                url: Docs.url,
                _course_id: Docs._course_id,
                createdAt: Docs.createdAt,
                updatedAt: Docs.updatedAt,
                courseAdmin: Docs.courseAdmin,
            };
            Items.starred = !!Docs.starred.find((userIds) => userIds.toString() === userId);
            const sessionDatas = [];
            if (Docs.sessionOrScheduleIds) {
                Docs.sessionOrScheduleIds.map((session_id) => {
                    const sessionDetails = doc.data.find(
                        (session) =>
                            session._session_id &&
                            session._session_id.toString() === session_id.id.toString(),
                    );
                    const supportDetails = docSupport.find(
                        (session) =>
                            session._id && session._id.toString() === session_id.id.toString(),
                    );

                    const supportSessionDetails = docSession.find(
                        (session) =>
                            session._id && session._id.toString() === session_id.id.toString(),
                    );
                    if (sessionDetails) {
                        sessionDatas.push({
                            _id: sessionDetails._session_id,
                            delivery_symbol: sessionDetails.delivery_symbol,
                            delivery_no: sessionDetails.delivery_no,
                            session_type: sessionDetails.session_type,
                            session_topic: sessionDetails.session_topic,
                            type: constant.SCHEDULE_TYPES.REGULAR,
                        });
                    }
                    if (supportDetails) {
                        sessionDatas.push({
                            _id: supportDetails._id,
                            title: supportDetails.title,
                            type: constant.SCHEDULE_TYPES.EVENT,
                        });
                    }
                    if (supportSessionDetails) {
                        sessionDatas.push({
                            _id: supportSessionDetails._id,
                            title: supportSessionDetails.title,
                            type: constant.SCHEDULE_TYPES.SUPPORT_SESSION,
                        });
                    }
                });
            }
            const userDetails = staffList.find(
                (user) =>
                    user._id && Docs._user_id && user._id.toString() === Docs._user_id.toString(),
            );
            if (userDetails) {
                Items.UploadedBy = userDetails;
            }
            Items.sessions = sessionDatas;
            return Items;
        });
        if (limit && page) {
            return { totalDoc, totalPages, currentPage, documents };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documents };
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseSessionDocumentList = async (
    userId,
    courseId,
    sessionId,
    limit,
    page,
    search,
    tab,
    subTab,
    type,
    _program_id,
    remote,
) => {
    try {
        let query;
        let staffIdList;
        if (search) {
            const staffIds = await users.find(
                {
                    $or: [
                        { 'name.first': new RegExp(search, 'i') },
                        { 'name.last': new RegExp(search, 'i') },
                    ],
                },
                { _id: 1, name: 1 },
            );
            staffIdList = staffIds.map((userId) => userId._id);
        }
        if (search) {
            query = {
                _course_id: convertToMongoObjectId(courseId),
                'sessionOrScheduleIds._id': convertToMongoObjectId(sessionId),
                $or: [
                    { name: { $regex: search, $options: 'i' } },
                    { _user_id: { $in: staffIdList } },
                ],
                isDeleted: false,
            };
        } else {
            query = {
                _course_id: convertToMongoObjectId(courseId),
                'sessionOrScheduleIds._id': convertToMongoObjectId(sessionId),
                isDeleted: false,
            };
        }
        if (tab) {
            query.starred = convertToMongoObjectId(userId);
        }
        if (subTab) {
            query.url = fileFormatSubTabFilter(subTab);
        }
        if (_program_id) {
            query._program_id = convertToMongoObjectId(_program_id);
        }
        if (remote) {
            query.remote = true;
        }
        // get query document data
        let data;
        let totalDoc;
        let totalPages;
        let currentPage;
        if (limit && page) {
            // make perPage and pageNo
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            data = await Document.find(query)
                .sort({ createdAt: -1 })
                .skip(perPage * (pageNo - 1))
                .limit(perPage);
            //get all document
            totalDoc = await Document.find(query).countDocuments().exec();
            // filter pages
            totalPages = Math.ceil(totalDoc / perPage);
            currentPage = pageNo;
        } else {
            data = await Document.find(query).sort({ createdAt: -1 });
        }

        const sessionIds = [];
        const suportEvents = [];
        const suportSessions = [];
        // await data.forEach(async (element) => {
        for (element of data) {
            // const matchURLData = element.url.match(/amazonaws.com/g);
            if (
                // matchURLData !== null ||
                element.type === 'file' &&
                element.url /* &&
                element.url.split('/') */
            ) {
                element.url = await getSignedURL(element.url);
            }
            await element.sessionOrScheduleIds.forEach(async (datas) => {
                if (datas.type === constant.SCHEDULE_TYPES.REGULAR) {
                    if (datas._id && sessionIds.indexOf(datas._id.toString()) < 0) {
                        sessionIds.push(datas._id.toString());
                    }
                }
                if (datas.type === constant.SCHEDULE_TYPES.EVENT) {
                    if (datas._id && suportEvents.indexOf(datas._id.toString()) < 0) {
                        suportEvents.push(datas._id.toString());
                    }
                }
                if (datas.type === constant.SCHEDULE_TYPES.SUPPORT_SESSION) {
                    if (datas._id && suportSessions.indexOf(datas._id.toString()) < 0) {
                        suportSessions.push(datas._id.toString());
                    }
                }
            });
        }
        // });
        const userIds = data.map((userId) => userId._user_id);

        const staffList = await users.find({ _id: { $in: userIds } }, { _id: 1, name: 1 });
        const sessions = sessionIds.map((session) => convertToMongoObjectId(session));

        const supportEvent = suportEvents.map((events) => convertToMongoObjectId(events));

        const suportSession = suportSessions.map((events) => convertToMongoObjectId(events));

        const doc = await distinct_list(courseSchedules, 'session', {
            isDeleted: false,
            'session._session_id': { $in: sessions },
        });

        const docSupport = await courseSchedules.find(
            { _id: { $in: supportEvent } },
            { _id: 1, title: 1 },
        );
        const docSession = await courseSchedules.find(
            { _id: { $in: suportSession } },
            { _id: 1, title: 1 },
        );
        const documents = data.map(function (Docs) {
            Items = {
                _id: Docs._id,
                type: Docs.type,
                name: Docs.name,
                url: Docs.url,
                _course_id: Docs._course_id,
                _program_id: Docs._program_id,
                year_no: Docs.year_no,
                level_no: Docs.level_no,
                term: Docs.term,
                rotation: Docs.rotation,
                rotation_count: Docs.rotation_count,
                _institution_calendar_id: Docs._institution_calendar_id,
                createdAt: Docs.createdAt,
                updatedAt: Docs.updatedAt,
                courseAdmin: Docs.courseAdmin,
                remote: Docs.remote,
            };
            Items.starred = !!Docs.starred.find((userIds) => userIds.toString() === userId);
            const sessionDatas = [];
            if (Docs.sessionOrScheduleIds) {
                Docs.sessionOrScheduleIds.map((session_id) => {
                    const sessionDetails = doc.data.find(
                        (session) =>
                            session._session_id &&
                            session._session_id.toString() === session_id.id.toString(),
                    );
                    const supportDetails = docSupport.find(
                        (session) =>
                            session._id && session._id.toString() === session_id.id.toString(),
                    );
                    const supportSessionDetails = docSession.find(
                        (session) =>
                            session._id && session._id.toString() === session_id.id.toString(),
                    );
                    if (sessionDetails) {
                        sessionDatas.push({
                            _id: sessionDetails._session_id,
                            delivery_symbol: sessionDetails.delivery_symbol,
                            delivery_no: sessionDetails.delivery_no,
                            session_type: sessionDetails.session_type,
                            session_topic: sessionDetails.session_topic,
                            type: constant.SCHEDULE_TYPES.REGULAR,
                        });
                    }
                    if (supportDetails) {
                        sessionDatas.push({
                            _id: supportDetails._id,
                            title: supportDetails.title,
                            type: constant.SCHEDULE_TYPES.EVENT,
                        });
                    }
                    if (supportSessionDetails) {
                        sessionDatas.push({
                            _id: supportSessionDetails._id,
                            title: supportSessionDetails.title,
                            type: constant.SCHEDULE_TYPES.SUPPORT_SESSION,
                        });
                    }
                });
            }
            const userDetails = staffList.find(
                (user) =>
                    user._id && Docs._user_id && user._id.toString() === Docs._user_id.toString(),
            );
            if (userDetails) {
                Items.UploadedBy = userDetails;
            }
            Items.sessions = sessionDatas;
            return Items;
        });
        if (limit && page) {
            return { totalDoc, totalPages, currentPage, documents };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documents };
    } catch (error) {
        throw new Error(error);
    }
};

const getAllSessionList = async (
    courseId,
    userId,
    _institution_calendar_id,
    _program_id,
    year_no,
    level_no,
    term,
    mergedStatus,
    rotation,
    rotation_count,
    courseAdmin,
) => {
    try {
        const cond = {
            isDeleted: false,
        };
        if (courseAdmin !== 'true') {
            cond.$or = [
                { 'staffs._staff_id': convertToMongoObjectId(userId) },
                { 'students._id': convertToMongoObjectId(userId) },
            ];
        }

        if (courseId) {
            cond._course_id = convertToMongoObjectId(courseId);
        }

        if (_program_id) {
            cond._program_id = convertToMongoObjectId(_program_id);
            cond.year_no = year_no;
            cond.level_no = level_no;
            cond.term = term;
            cond.rotation = rotation;
            if (rotation_count) {
                cond.rotation_count = rotation_count;
            }
            cond._institution_calendar_id = convertToMongoObjectId(_institution_calendar_id);
        }
        const proj = { _course_id: 1, session: 1, _id: 1, title: 1, type: 1 };
        const courseScheduleData = await courseSchedules
            .find(cond, proj)
            .sort({ 'session.s_no': 1 })
            .lean();
        const sessionIds = [];
        const scheduleIds = [];
        const list = [];
        courseScheduleData.forEach((courseScheduleDatum) => {
            const { session, _id, title, type } = courseScheduleDatum;
            if (session) {
                const { _session_id } = session;
                if (session && _session_id && !sessionIds.includes(_session_id.toString())) {
                    sessionIds.push(_session_id.toString());
                    list.push({
                        _id: session._session_id,
                        s_no: session.s_no,
                        delivery_symbol: session.delivery_symbol,
                        delivery_no: session.delivery_no,
                        session_type: session.session_type,
                        session_topic: session.session_topic,
                        type: constant.SCHEDULE_TYPES.REGULAR,
                    });
                }
            }
            if (title && !sessionIds.includes(_id.toString())) {
                scheduleIds.push(_id.toString());
                list.push({ _id, title, type });
            }
        });
        return list;
    } catch (error) {
        throw new Error(error);
    }
};
const getZoomRecordLists = async () => {
    try {
        csQuery = {
            $or: [{ zoomRecord: { $exists: false } }, { zoomRecord: false }],
            sessionDetail: { $exists: true },
            mode: constant.TIME_GROUP_BOOKING_TYPE.REMOTE,
            status: constant.COMPLETED,
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            zoomDetail: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.find(csQuery, csProject);
        if (!courseSchedule) {
            console.log('Course schedules not found');
        }
        //getting Infra Ids details
        const infraIds = courseSchedule.map((infraIds) => infraIds._infra_id);
        //getting Infra Datas as per condition
        const infraDetails = await getInfraById(infraIds);
        const Documents = [];
        const courseZoomRecords = [];
        const recordingDetails = [];
        // Looping Course Schedules
        for (schedule of courseSchedule) {
            console.log('Course Schedule ID: ' + schedule._id);
            // check course Schedules to go only remote
            let PartIncrement = 1;
            if (schedule.mode === constant.TIME_GROUP_BOOKING_TYPE.REMOTE) {
                // getting Meeting Details
                let infraDetail = infraDetails
                    .map((a) => a.programs)
                    .flat()
                    .map((b) =>
                        b.remoteScheduling.filter(
                            (i) =>
                                schedule._infra_id &&
                                i._id.toString() === schedule._infra_id.toString(),
                        ),
                    )
                    .flat()
                    .shift();
                if (infraDetail) {
                    //Checking Infra Data available or not
                    infraDetail = JSON.parse(JSON.stringify(infraDetail));
                    if (
                        infraDetail &&
                        infraDetail.apiKey &&
                        infraDetail.apiSecretKey &&
                        infraDetail.meetingId
                    ) {
                        // Generate the Token
                        const tokenGenerate = await tokenGeneration({
                            API_KEY: infraDetail.apiKey,
                            API_SECRET_KEY: infraDetail.apiSecretKey,
                        });
                        console.log('Token Generated: ' + tokenGenerate);
                        //Getting Meeting UUID lists using this function
                        const getMeetingUuid = await getZoomUUIDList({
                            meetingId: schedule.zoomDetail.zoomMeetingId.replace(/\s/g, ''),
                            token: tokenGenerate,
                        });
                        //Checking Zoom Meeting Array list
                        if (getMeetingUuid.meetings) {
                            // Looping in to multiple Meeting list
                            for (meetingUUID of getMeetingUuid.meetings) {
                                //getting Schedule Date,Start and End Time
                                const {
                                    start: {
                                        hour: startHour,
                                        minute: startMinute,
                                        format: startFormat,
                                    },
                                    end: { hour: endHour, minute: endMinute, format: endFormat },
                                    schedule_date,
                                } = schedule;
                                const startDateAndTimeFormatter = scheduleDateTimeFormatter(
                                    schedule_date,
                                    {
                                        hour: startHour,
                                        minute: startMinute,
                                        format: startFormat,
                                    },
                                );

                                const endDateAndTimeFormatter = scheduleDateTimeFormatter(
                                    schedule_date,
                                    {
                                        hour: endHour,
                                        minute: endMinute,
                                        format: endFormat,
                                    },
                                );

                                const meetingTimeToLocal = dateTimeLocalFormatter(
                                    meetingUUID.start_time,
                                );

                                console.log(meetingTimeToLocal);
                                console.log(startDateAndTimeFormatter);
                                console.log(endDateAndTimeFormatter);
                                // Checking Zoom Start time between Schedule Start and End Time

                                if (
                                    startDateAndTimeFormatter <= meetingTimeToLocal &&
                                    endDateAndTimeFormatter >= meetingTimeToLocal
                                ) {
                                    console.log('inside time condition');
                                    //getting Zoom recored lists
                                    const getRecordingData = await getZoomRecordList({
                                        meetingUuId: encodeURIComponent(
                                            encodeURIComponent(meetingUUID.uuid),
                                        ),
                                        token: tokenGenerate,
                                    });

                                    if (getRecordingData.recording_files) {
                                        // Looping all recorded files
                                        for (getRecording of getRecordingData.recording_files) {
                                            // Checking MP4 Extension
                                            if (getRecording.file_extension === 'MP4') {
                                                recordingDetails.push({
                                                    token: tokenGenerate,
                                                    recordingId: getRecording.id,
                                                    meetingId: encodeURIComponent(
                                                        encodeURIComponent(meetingUUID.uuid),
                                                    ),
                                                });
                                                const url =
                                                    getRecording.download_url +
                                                    '?access_token=' +
                                                    tokenGenerate; // link to file you want to download
                                                const path = './public/documents/'; // where to save a file
                                                //Checking Multiple Parts like Part-1,Part-2
                                                let Part;
                                                if (getMeetingUuid.meetings.length > 1) {
                                                    Part = 'Part - ' + PartIncrement;
                                                } else {
                                                    Part = '';
                                                }
                                                // get first recorded URL from zoom
                                                const getVideoLists = await getVideoLink({
                                                    url,
                                                });
                                                //Writng Topic Names
                                                let topicName;
                                                if (schedule.session.session_topic) {
                                                    topicName =
                                                        schedule.session.session_topic.replace(
                                                            /[^a-zA-Z ]/g,
                                                            '',
                                                        ) +
                                                        ' ' +
                                                        Part +
                                                        '.mp4';
                                                } else {
                                                    topicName =
                                                        schedule.title.replace(/[^a-zA-Z ]/g, '') +
                                                        ' ' +
                                                        Part +
                                                        '.mp4';
                                                }
                                                // Getting Final URL to Upload in to AWS
                                                console.log('before download');
                                                const fileLocation = path + topicName;
                                                const zoomFile = await downloadZoomVideo({
                                                    url: getVideoLists.request.uri.href,
                                                    fileLocation,
                                                });
                                                console.log('after download');
                                                //Inserting zoomRecords  in to Documents
                                                const sessions = [];
                                                if (
                                                    schedule.type ===
                                                    constant.SCHEDULE_TYPES.REGULAR
                                                ) {
                                                    sessions.push({
                                                        type: schedule.type,
                                                        _id: schedule.session._session_id,
                                                    });
                                                } else {
                                                    sessions.push({
                                                        type: schedule.type,
                                                        _id: schedule._id,
                                                    });
                                                }
                                                console.log(
                                                    'Zoom file MP4 Local location: ' + zoomFile,
                                                );
                                                DocumentsData = {
                                                    url: zoomFile,
                                                    topicName,
                                                    type: 'url',
                                                    sessions,
                                                    name: topicName,
                                                    _course_id: schedule._course_id,
                                                    _user_id: schedule.sessionDetail.startBy,
                                                    record: true,
                                                };
                                                Documents.push(DocumentsData);
                                                //Pushing zoomRecord to update in to CourseSchedules
                                                courseZoomRecords.push({
                                                    updateOne: {
                                                        filter: {
                                                            _id: schedule._id,
                                                        },
                                                        update: {
                                                            $set: {
                                                                zoomRecord: true,
                                                            },
                                                        },
                                                    },
                                                });

                                                PartIncrement++;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        //uploading in to Documents
        if (Documents.length > 0) {
            for (const document of Documents) {
                const { url, topicName } = document;
                console.log('ZOOM file Before Upload: ' + url);
                const chkFilePath = await uploadFileToAws(url, topicName);
                console.log('ZOOM file After Upload: ' + chkFilePath);
                document.url = chkFilePath;
            }
            Documents.forEach((Document) => removeFolder(Document.topicName));
            const doc = await DocumentCreations(Documents);
            if (doc.status) {
                for (const records of recordingDetails) {
                    await deleteZoomRecordList(records);
                }
                console.log('Documents Uploaded Successfully');
            } else {
                console.log('Documents Not Uploading due to Error in bulk insert ');
            }
        }
        //Bulk updating in to course schedules
        if (courseZoomRecords.length > 0) {
            const doc = await bulk_write(courseSchedules, courseZoomRecords);
            if (doc.status) {
                console.log('Course zoomRecord Updated Successfully');
            } else {
                console.log('Course zoomRecord Not Updated ');
            }
        }
    } catch (error) {
        throw new Error(error);
    }
};
const getZoomUsersDuration = async (coursescheduleIds) => {
    try {
        logger.info(
            'documentManagerService -> getZoomUsersDuration -> %s - start',
            coursescheduleIds,
        );
        csQuery = {
            $or: [{ zoomDuration: { $exists: false } }, { zoomDuration: false }],
            _id: { $in: coursescheduleIds },
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            students: 1,
            staffs: 1,
            scheduleEndDateAndTime: 1,
            scheduleStartDateAndTime: 1,
            zoomDetail: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.find(csQuery, csProject);
        if (!courseSchedule) {
            return { status: 0, msg: 'Course schedules not found' };
        }
        //getting Infra Ids details
        const infraIds = courseSchedule.map((infraIds) => infraIds._infra_id);
        //getting Infra Datas as per condition
        const infraDetails = await getInfraById(infraIds);

        // Looping Course Schedules
        for (schedule of courseSchedule) {
            const gettingDuration = [];
            // check course Schedules to go only remote
            const PartIncrement = 1;
            let participantCount = 0;
            let adminDuration = 0;
            if (
                schedule.mode === constant.TIME_GROUP_BOOKING_TYPE.REMOTE &&
                schedule.zoomDetail &&
                schedule.zoomDetail.zoomMeetingId
            ) {
                // getting Meeting Details
                let infraDetail = infraDetails
                    .map((a) => a.programs)
                    .flat()
                    .map((b) =>
                        b.remoteScheduling.filter(
                            (i) =>
                                schedule._infra_id &&
                                i._id.toString() === schedule._infra_id.toString(),
                        ),
                    )
                    .flat()
                    .shift();
                if (infraDetail) {
                    //Checking Infra Data available or not
                    infraDetail = JSON.parse(JSON.stringify(infraDetail));
                    if (
                        infraDetail &&
                        infraDetail.apiKey &&
                        infraDetail.apiSecretKey &&
                        infraDetail.meetingId
                    ) {
                        // Generate the Token
                        const tokenGenerate = await tokenGeneration({
                            API_KEY: infraDetail.apiKey,
                            API_SECRET_KEY: infraDetail.apiSecretKey,
                        });
                        //Getting whole meeting Duration
                        const meetingDuration = await getZoomMeetingDuration({
                            meetingId: encodeURIComponent(
                                encodeURIComponent(schedule.zoomDetail.zoomUuid),
                            ),
                            token: tokenGenerate,
                        });
                        if (meetingDuration.duration) {
                            //Getting Meeting UUID lists using this function
                            const getMeetingUuid = await getZoomUUIDList({
                                meetingId: schedule.zoomDetail.zoomMeetingId.replace(/\s/g, ''),
                                token: tokenGenerate,
                            });

                            logger.info(
                                'documentManagerService -> getZoomUsersDuration -> %s - tokenGenerate',
                                tokenGenerate,
                            );

                            //Checking Zoom Meeting Array list
                            if (getMeetingUuid.meetings) {
                                // Looping in to multiple Meeting list
                                for (meetingUUID of getMeetingUuid.meetings) {
                                    if (meetingDuration.duration) {
                                        //getting Schedule Date,Start and End Time
                                        // Checking Zoom Start time between Schedule Start and End Time

                                        if (
                                            new Date(schedule.scheduleStartDateAndTime) <=
                                                new Date(meetingUUID.start_time) &&
                                            new Date(schedule.scheduleEndDateAndTime) >=
                                                new Date(meetingUUID.start_time)
                                        ) {
                                            const meetingAdminDurationCalculation =
                                                await getZoomMeetingDuration({
                                                    meetingId: encodeURIComponent(
                                                        encodeURIComponent(meetingUUID.uuid),
                                                    ),
                                                    token: tokenGenerate,
                                                });

                                            adminDuration += Math.floor(
                                                (new Date(
                                                    meetingAdminDurationCalculation.end_time,
                                                ).getTime() -
                                                    new Date(
                                                        meetingAdminDurationCalculation.start_time,
                                                    ).getTime()) /
                                                    1000,
                                            );
                                            let participantListPush = [];
                                            //getting Zoom recored lists
                                            let participantList = await getZoomParticipantsList({
                                                meetingUuId: encodeURIComponent(
                                                    encodeURIComponent(meetingUUID.uuid),
                                                ),
                                                token: tokenGenerate,
                                                page_size: 300,
                                                next_page_token: '',
                                            });

                                            if (participantList.participants) {
                                                participantListPush = participantList.participants;
                                                if (participantList.page_count !== 1) {
                                                    //  let flat = [];
                                                    for (
                                                        let i = 2;
                                                        i <= participantList.page_count;
                                                        i++
                                                    ) {
                                                        participantList =
                                                            await getZoomParticipantsList({
                                                                meetingUuId: encodeURIComponent(
                                                                    encodeURIComponent(
                                                                        meetingUUID.uuid,
                                                                    ),
                                                                ),
                                                                token: tokenGenerate,
                                                                page_size: 300,
                                                                next_page_token:
                                                                    participantList.next_page_token,
                                                            });

                                                        participantListPush =
                                                            participantListPush.concat(
                                                                participantList.participants,
                                                            );
                                                    }
                                                }
                                            }
                                            if (participantListPush) {
                                                // Looping all participant list
                                                const uniqueParticipantList = [
                                                    ...new Map(
                                                        participantListPush.map((item) => [
                                                            item.user_email,
                                                            item,
                                                        ]),
                                                    ).values(),
                                                ];
                                                participantCount = uniqueParticipantList.length;

                                                for (getparticipants of participantListPush) {
                                                    // getting Duration
                                                    if (
                                                        getparticipants.duration === 0 ||
                                                        getparticipants.duration
                                                    ) {
                                                        const getIndexOfUsersTimeDuration =
                                                            gettingDuration.findIndex(
                                                                (obj) =>
                                                                    obj.email ===
                                                                    getparticipants.user_email,
                                                            );
                                                        if (getIndexOfUsersTimeDuration === -1) {
                                                            gettingDuration.push({
                                                                name: getparticipants.name,
                                                                email: getparticipants.user_email,
                                                                duration: getparticipants.duration,
                                                            });
                                                        } else {
                                                            const existingUsersDuration =
                                                                gettingDuration[
                                                                    getIndexOfUsersTimeDuration
                                                                ].duration;
                                                            gettingDuration[
                                                                getIndexOfUsersTimeDuration
                                                            ].duration =
                                                                existingUsersDuration +
                                                                getparticipants.duration;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // }
                //getting all staffs
                const staffIds = schedule.staffs.map((staffId) => staffId._staff_id);
                if (
                    staffIds.length > 0 &&
                    gettingDuration.length > 0 &&
                    adminDuration &&
                    participantCount === gettingDuration.length
                ) {
                    const adminDurationPush = [];
                    //update duration in staff
                    for (const staffId of staffIds) {
                        adminDurationPush.push({
                            updateOne: {
                                filter: {
                                    'staffs._staff_id': convertToMongoObjectId(staffId),
                                    _id: schedule._id,
                                },
                                update: {
                                    $set: {
                                        'staffs.$[i].duration': adminDuration,
                                    },
                                },
                                arrayFilters: [{ 'i._staff_id': convertToMongoObjectId(staffId) }],
                            },
                        });
                    }
                    if (adminDurationPush.length > 0) {
                        const doc = await bulk_write(courseSchedules, adminDurationPush);
                        if (doc.status) {
                            console.log('Course Staffs Duration Updated Successfully');
                        } else {
                            console.log('Course Staffs Duration Not Updated ');
                        }
                    }
                }
                //getting all students
                const studentIds = schedule.students
                    .filter(
                        (studentElement) => !leavePermissionOnDuty.includes(studentElement.status),
                    )
                    .map((studentId) => studentId._id);
                //sum of duplicate durations
                if (
                    studentIds.length > 0 &&
                    gettingDuration.length > 0 &&
                    adminDuration &&
                    participantCount === gettingDuration.length
                ) {
                    const sumOfDuration = [];
                    const getEmail = [];
                    const durationPush = [];
                    if (gettingDuration.length > 0) {
                        gettingDuration.reduce(function (res, value) {
                            // const splittedEmail = value.name.replace(/(^.*\(|\).*$)/g, '');
                            const splittedEmail = value.email;
                            if (!res[splittedEmail]) {
                                res[splittedEmail] = { email: splittedEmail, duration: 0 };
                                sumOfDuration.push(res[splittedEmail]);
                            }
                            res[splittedEmail].duration += value.duration;
                            return res;
                        }, {});
                    }

                    const studentList = await users.find(
                        { _id: { $in: studentIds }, isActive: true, isDeleted: false },
                        { _id: 1, name: 1, email: 1 },
                    );

                    for (const students of studentList) {
                        const result = sumOfDuration.find(
                            (studentSum) => studentSum.email === students.email,
                        );
                        if (result) {
                            let status = '';
                            const percentage = (100 * result.duration) / adminDuration;
                            // const IndividualStudentData = schedule.students.find(
                            //     (studentElement) =>
                            //         studentElement._id.toString() === students._id.toString(),
                            // );
                            // console.log(
                            //     parseInt(participantDuration) >= percentage,
                            //     IndividualStudentData,
                            //     IndividualStudentData === PRESENT,
                            // );
                            // const status =
                            //     parseInt(participantDuration) >= percentage &&
                            //     IndividualStudentData === PRESENT
                            //         ? PRESENT
                            //         : ABSENT;

                            const IndividualStudentData = schedule.students.find(
                                (studentElement) =>
                                    studentElement._id.toString() === students._id.toString(),
                            );

                            if (
                                parseInt(participantDuration) >= percentage ||
                                !IndividualStudentData.join_url
                            ) {
                                status = ABSENT;
                            } else {
                                status = PRESENT;
                            }
                            durationPush.push({
                                updateOne: {
                                    filter: {
                                        'students._id': convertToMongoObjectId(students._id),
                                        _id: schedule._id,
                                    },
                                    update: {
                                        $set: {
                                            'students.$[i].status': status,
                                            'students.$[i].duration':
                                                result.duration >= adminDuration
                                                    ? adminDuration
                                                    : result.duration,
                                            'students.$[i].percentage':
                                                percentage >= 100 ? 100 : percentage,
                                            'zoomDetail.zoomTotalDuration': adminDuration,
                                            /* zoomDuration:
                                                (await addMinutes(
                                                    schedule.scheduleEndDateAndTime,
                                                    10,
                                                )) <= new Date(new Date().toISOString()), */
                                            zoomDuration: true,
                                        },
                                    },
                                    arrayFilters: [
                                        { 'i._id': convertToMongoObjectId(students._id) },
                                    ],
                                },
                            });
                        } else {
                            durationPush.push({
                                updateOne: {
                                    filter: {
                                        'students._id': convertToMongoObjectId(students._id),
                                        _id: schedule._id,
                                    },
                                    update: {
                                        $set: {
                                            'students.$[i].status': ABSENT,
                                            'students.$[i].duration': 0,
                                            'zoomDetail.zoomTotalDuration': adminDuration,
                                            /*   zoomDuration:
                                                (await addMinutes(
                                                    schedule.scheduleEndDateAndTime,
                                                    10,
                                                )) <= new Date(new Date().toISOString()), */
                                            zoomDuration: true,
                                        },
                                    },
                                    arrayFilters: [
                                        { 'i._id': convertToMongoObjectId(students._id) },
                                    ],
                                },
                            });
                        }
                    }

                    //Bulk updating in to course schedules
                    if (durationPush.length > 0) {
                        const doc = await bulk_write(courseSchedules, durationPush);
                        if (doc.status) {
                            console.log('Course Students Duration Updated Successfully');
                        } else {
                            console.log('Course Students Duration Not Updated ');
                        }
                    }
                }
            }
        }
        logger.info(
            'documentManagerService -> getZoomUsersDuration -> %s - End',
            coursescheduleIds,
        );
    } catch (error) {
        throw new Error(error);
    }
};

const createMeeting = async (coursescheduleIds) => {
    try {
        logger.info('documentManagerService -> createMeeting -> %s - start', coursescheduleIds);
        csQuery = {
            'zoomDetail.zoomStartUrl': { $exists: false },
            _id: coursescheduleIds,
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            students: 1,
            scheduleEndDateAndTime: 1,
            scheduleStartDateAndTime: 1,
            zoomDetail: 1,
            merge_with: 1,
            merge_status: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.findOne(csQuery, csProject);
        if (!courseSchedule) {
            return { status: 0, msg: 'Course schedules not matching as per conditions' };
        }

        let scheduleIds = [courseSchedule._id];
        if (courseSchedule.merge_status) {
            const mergeScheduleIds = courseSchedule.merge_with.map((mw) =>
                convertToMongoObjectId(mw.schedule_id),
            );
            scheduleIds = scheduleIds.concat(mergeScheduleIds);
        }

        const courseZoomDatas = [];
        //getting Infra Ids details
        const infraIds = courseSchedule._infra_id;
        //getting Infra Datas as per condition
        const infraDetails = await getInfraById([infraIds]);

        let infraDetail = infraDetails
            .map((a) => a.programs)
            .flat()
            .map((b) =>
                b.remoteScheduling.filter(
                    (i) =>
                        courseSchedule._infra_id &&
                        i._id.toString() === courseSchedule._infra_id.toString(),
                ),
            )
            .flat()
            .shift();
        if (infraDetail) {
            //Checking Infra Data available or not
            infraDetail = JSON.parse(JSON.stringify(infraDetail));
            if (infraDetail && infraDetail.apiKey && infraDetail.apiSecretKey) {
                // Generate the Token
                const tokenGenerate = await tokenGeneration({
                    API_KEY: infraDetail.apiKey,
                    API_SECRET_KEY: infraDetail.apiSecretKey,
                });
                //Getting Meeting UUID lists using this function
                const getZoomUserList = await getZoomHostUserList({
                    token: tokenGenerate,
                });
                const UserResult = getZoomUserList.users.find(
                    (userList) =>
                        userList.email === infraDetail.associatedEmail /*  && userList.role_id ===
                        '0' */,
                );
                if (UserResult) {
                    const duration = differenceInMinutes(
                        courseSchedule.scheduleStartDateAndTime,
                        courseSchedule.scheduleEndDateAndTime,
                    );
                    let topicName;
                    if (courseSchedule.session.session_topic) {
                        topicName = courseSchedule.session.session_topic;
                    } else {
                        topicName = courseSchedule.title;
                    }

                    const length = 100;
                    topicName =
                        topicName.length > length
                            ? topicName.substring(0, length - 3) + '...'
                            : topicName;

                    //creating createHostZoomMeeting
                    const ZoomMeeting = await createHostZoomMeeting({
                        userId: UserResult.id,
                        params: {
                            topic: topicName,
                            type: 2,
                            start_time: courseSchedule.scheduleStartDateAndTime,
                            password: infraDetail.passCode,
                            duration,
                            agenda: topicName,
                            settings: {
                                host_video: false,
                                registrants_email_notification: false,
                                participant_video: false,
                                approval_type: 0, // Added 0 for allowing student to register in zoom // added 2 for disabling registraion in sdk
                            },
                        },
                        token: tokenGenerate,
                    });
                    for (const scheduleId of scheduleIds) {
                        courseZoomDatas.push({
                            updateOne: {
                                filter: {
                                    _id: scheduleId,
                                },
                                update: {
                                    $set: {
                                        'zoomDetail.zoomMeetingId': ZoomMeeting.id,
                                        'zoomDetail.zoomUuid': ZoomMeeting.uuid,
                                        'zoomDetail.zoomStartUrl': ZoomMeeting.start_url,
                                        'zoomDetail.passCode': infraDetail.passCode,
                                    },
                                },
                            },
                        });
                    }

                    //Bulk updating in to course schedules
                    if (courseZoomDatas.length > 0) {
                        const doc = await bulk_write(courseSchedules, courseZoomDatas);
                        if (doc.status) {
                            console.log('Course courseZoomDatas Updated Successfully');
                            return { start_url: ZoomMeeting.start_url, status: 1 };
                        }
                        console.log('Course courseZoomDatas Not Updated ');
                        return { start_url: '', status: 0 };
                    }
                }
            }
        }

        logger.info('documentManagerService -> createMeeting -> %s - end', coursescheduleIds);
    } catch (error) {
        throw new Error(error);
    }
};

const createStudentJoinUrl = async (coursescheduleIds, studentId) => {
    try {
        logger.info(
            'documentManagerService -> createStudentJoinUrl -> %s - start',
            coursescheduleIds,
        );
        csQuery = {
            'zoomDetail.zoomStartUrl': { $exists: true },
            _id: coursescheduleIds,
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            students: 1,
            scheduleEndDateAndTime: 1,
            scheduleStartDateAndTime: 1,
            zoomDetail: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.findOne(csQuery, csProject);

        const studentDetails = await users.findOne(
            { _id: convertToMongoObjectId(studentId) },
            { _id: 1, name: 1, email: 1 },
        );

        if (!courseSchedule) {
            return { status: 0, msg: 'Course schedules join url not found' };
        }

        if (!studentDetails) {
            return { status: 0, msg: 'Student not found' };
        }

        const studentUrl = [];
        //getting Infra Ids details
        const infraIds = courseSchedule._infra_id;
        //getting Infra Datas as per condition
        const infraDetails = await getInfraById([infraIds]);

        let infraDetail = infraDetails
            .map((a) => a.programs)
            .flat()
            .map((b) =>
                b.remoteScheduling.filter(
                    (i) =>
                        courseSchedule._infra_id &&
                        i._id.toString() === courseSchedule._infra_id.toString(),
                ),
            )
            .flat()
            .shift();
        if (infraDetail) {
            //Checking Infra Data available or not
            infraDetail = JSON.parse(JSON.stringify(infraDetail));
            if (infraDetail && infraDetail.apiKey && infraDetail.apiSecretKey) {
                // Generate the Token
                const tokenGenerate = await tokenGeneration({
                    API_KEY: infraDetail.apiKey,
                    API_SECRET_KEY: infraDetail.apiSecretKey,
                });
                logger.info({ tokenGenerate }, 'Zoom Student Token');
                logger.debug(
                    {
                        meetingId: courseSchedule.zoomDetail.zoomMeetingId,
                        params: {
                            email: studentDetails.email,
                            first_name: studentDetails.name.first,
                            last_name: studentDetails.name.last,
                            auto_approve: true,
                        },
                        token: tokenGenerate,
                    },
                    'Zoom Creating Participation',
                );
                //creating createHostZoomMeeting
                const participantURL = await createParticipantURL({
                    meetingId: courseSchedule.zoomDetail.zoomMeetingId,
                    params: {
                        email: studentDetails.email,
                        first_name: studentDetails.name.first,
                        last_name: studentDetails.name.last,
                        auto_approve: true,
                    },
                    token: tokenGenerate,
                });
                logger.info({ participantURL }, 'Zoom participantURL');

                studentUrl.push({
                    updateOne: {
                        filter: {
                            'students._id': studentDetails._id,
                            _id: courseSchedule._id,
                        },
                        update: {
                            $set: {
                                'students.$[i].join_url': participantURL.join_url,
                            },
                        },
                        arrayFilters: [{ 'i._id': convertToMongoObjectId(studentDetails._id) }],
                    },
                });

                //Bulk updating in to course schedules
                if (studentUrl.length > 0) {
                    const doc = await bulk_write(courseSchedules, studentUrl);
                    if (doc.status) {
                        console.log('join URL Updated Successfully');
                        return { join_url: participantURL.join_url, status: 1 };
                    }
                    console.log('join URL Not Updated ');
                    return { join_url: '', status: 0 };
                }
            }
        }

        logger.info(
            'documentManagerService -> createStudentJoinUrl -> %s - end',
            coursescheduleIds,
        );
    } catch (error) {
        throw new Error(error);
    }
};

const getCourseSessionDocumentListWithFilter = async (
    userId,
    courseId,
    sessionId,
    limit,
    page,
    search,
    tab,
    subTab,
    institutionCalendarId,
    filter,
    term,
    _program_id,
    year_no,
    level_no,
    rotation,
    rotation_count,
    type,
    remote,
) => {
    try {
        const scheduleQuery = {
            // $or: [
            //     {
            //         'students._id': convertToMongoObjectId(userId),
            //     },
            //     {
            //         'staffs._staff_id': convertToMongoObjectId(userId),
            //     },
            // ],
            _course_id: convertToMongoObjectId(courseId),
            // eslint-disable-next-line no-dupe-keys
            $or: [
                { _id: convertToMongoObjectId(sessionId) },
                { 'session._session_id': convertToMongoObjectId(sessionId) },
            ],
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            isDeleted: false,
            isActive: true,
        };
        if (term) scheduleQuery.term = term;
        if (_program_id) scheduleQuery._program_id = convertToMongoObjectId(_program_id);
        if (year_no) scheduleQuery.year_no = year_no;
        if (level_no) scheduleQuery.level_no = level_no;
        if (rotation_count) scheduleQuery.rotation_count = rotation_count;
        const scheduleDatas = await courseSchedules.find(scheduleQuery, {
            _id: 1,
            session: 1,
            title: 1,
            type: 1,
            staffs: 1,
        });
        const scheduleStaffIds = scheduleDatas
            .map((scheduleElement) =>
                scheduleElement.staffs.map((scheduleELement) =>
                    convertToMongoObjectId(scheduleELement._staff_id),
                ),
            )
            .flat();
        const staffSchedule = scheduleDatas.find((scheduleElement) =>
            scheduleElement.staffs.find((scheduleELement) =>
                convertToMongoObjectId(scheduleELement._staff_id),
            ),
        );
        const query = {
            _course_id: convertToMongoObjectId(courseId),
            'sessionOrScheduleIds._id': convertToMongoObjectId(sessionId),
            isDeleted: false,
        };
        // query._user_id =
        //     filter === 'my'
        //         ? { $in: scheduleStaffIds }
        //         : {
        //               $nin: scheduleStaffIds,
        //           };
        if (type === DC_STAFF)
            query._user_id =
                filter === 'my'
                    ? convertToMongoObjectId(userId)
                    : {
                          $ne: convertToMongoObjectId(userId),
                      };
        if (search) {
            query.name = { $regex: search, $options: 'i' };
        }
        if (tab) {
            query.starred = convertToMongoObjectId(userId);
        }
        if (subTab) {
            query.url = fileFormatSubTabFilter(subTab);
        }
        if (term) query.term = term;
        if (_program_id) scheduleQuery._program_id = _program_id;
        if (year_no) query.year_no = year_no;
        if (level_no) query.level_no = level_no;
        if (rotation_count) query.rotation_count = rotation_count;
        if (remote) query.remote = true;
        // const documentProject = {
        //     _id: 1,
        //     createdAt: 1,
        //     updatedAt: 1,
        //     starred: 1,
        //     _user_id: 1,
        //     type: 1,
        //     name: 1,
        //     url: 1,
        //     _course_id: 1,
        // };
        let documentList;
        // let totalDoc;
        // let totalPages;
        // let currentPage;
        if (limit && page) {
            // const perPage = parseInt(limit > 0 ? limit : 10);
            // const pageNo = parseInt(page > 0 ? page : 1);
            documentList = await Document.find(query)
                .populate({ path: '_user_id', select: { _id: 1, name: 1 } })
                .sort({ createdAt: -1 });
            // .skip(perPage * (pageNo - 1))
            // .limit(perPage);
            // totalDoc = await Document.find(query, documentProject).countDocuments().exec();
            // totalPages = Math.ceil(totalDoc / perPage);
            // currentPage = pageNo;
        } else {
            documentList = await Document.find(query)
                .sort({ createdAt: -1 })
                .populate({ path: '_user_id', select: { _id: 1, name: 1 } });
        }
        const documentData = [];
        for (documentElement of documentList) {
            const singleDocument = {
                _id: documentElement._id,
                type: documentElement.type,
                name: documentElement.name,
                url: await getSignedURL(documentElement.url),
                _course_id: documentElement._course_id,
                _program_id: documentElement._program_id,
                year_no: documentElement.year_no,
                level_no: documentElement.level_no,
                term: documentElement.term,
                rotation: documentElement.rotation,
                rotation_count: documentElement.rotation_count,
                createdAt: documentElement.createdAt,
                updatedAt: documentElement.updatedAt,
                remote: documentElement.remote,
            };
            singleDocument.starred = !!documentElement.starred.find(
                (userIds) => userIds.toString() === userId.toString(),
            );
            singleDocument.UploadedBy = documentElement._user_id;
            singleDocument.sessions = [
                staffSchedule
                    ? staffSchedule.type === constant.SCHEDULE_TYPES.REGULAR
                        ? {
                              _id: staffSchedule.session._session_id,
                              delivery_symbol: staffSchedule.session.delivery_symbol,
                              delivery_no: staffSchedule.session.delivery_no,
                              session_type: staffSchedule.session.session_type,
                              session_topic: staffSchedule.session.session_topic,
                              type: constant.SCHEDULE_TYPES.REGULAR,
                          }
                        : {
                              _id: staffSchedule._id,
                              type: staffSchedule.type,
                              title: supportDetails.title,
                          }
                    : {},
            ];
            documentData.push(singleDocument);
        }
        if (limit && page) {
            const page_no = page > 0 ? parseInt(page) : 1;
            const page_size = limit;
            const totalPages = Math.ceil(documentData.length / page_size);
            const slicedDocuments = documentData.slice(
                (parseInt(page_no) - 1) * parseInt(page_size),
                parseInt(page_no) * parseInt(page_size),
            );
            return {
                totalDoc: documentData.length,
                totalPages,
                currentPage: page_no,
                documentData: slicedDocuments,
            };
            // return { totalDoc, totalPages, currentPage, documentData };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documentData };
    } catch (error) {
        logger.error(error, 'documentManagerService -> documentSessionListing -> Error');
        throw new Error(error);
    }
};

const getCourseDocumentListWithFilter = async (
    userId,
    courseId,
    limit,
    page,
    search,
    tab,
    subTab,
    institutionCalendarId,
    filter,
    type,
    term,
    _program_id,
    year_no,
    level_no,
    rotation,
    rotation_count,
) => {
    try {
        let userFilter = {};
        if (type === DC_STAFF)
            // eslint-disable-next-line no-unused-expressions
            filter === 'my'
                ? (userFilter = {
                      'staffs._staff_id': convertToMongoObjectId(userId),
                  })
                : (userFilter = {
                      'staffs._staff_id': { $ne: convertToMongoObjectId(userId) },
                  });
        else userFilter = { 'students._id': convertToMongoObjectId(userId) };
        const scheduleQuery = {
            ...userFilter,
            // $or: [
            //     {
            //         'students._id': convertToMongoObjectId(userId),
            //     },
            //     {
            //         'staffs._staff_id': convertToMongoObjectId(userId),
            //     },
            // ],
            _course_id: convertToMongoObjectId(courseId),
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            isDeleted: false,
            isActive: true,
        };
        if (term) scheduleQuery.term = term;
        if (_program_id) scheduleQuery._program_id = convertToMongoObjectId(_program_id);
        if (year_no) scheduleQuery.year_no = year_no;
        if (level_no) scheduleQuery.level_no = level_no;
        if (rotation_count) scheduleQuery.rotation_count = rotation_count;
        const scheduleDatas = await courseSchedules.find(scheduleQuery, {
            _id: 1,
            session: 1,
            title: 1,
            type: 1,
            staffs: 1,
            students: 1,
        });
        // const scheduleStaffIds = scheduleDatas.staffs.map((scheduleELement) =>
        //     convertToMongoObjectId(scheduleELement._staff_id),
        // );
        const query = {
            _course_id: convertToMongoObjectId(courseId),
            // 'sessionOrScheduleIds._id': convertToMongoObjectId(sessionId),
            isDeleted: false,
        };
        if (type === DC_STAFF)
            query._user_id =
                filter === 'my'
                    ? convertToMongoObjectId(userId)
                    : {
                          $ne: convertToMongoObjectId(userId),
                      };
        if (search) {
            query.name = { $regex: search, $options: 'i' };
        }
        if (tab) {
            query.starred = convertToMongoObjectId(userId);
        }
        if (subTab) {
            query.url = fileFormatSubTabFilter(subTab);
        }
        if (term) query.term = term;
        if (_program_id) scheduleQuery._program_id = convertToMongoObjectId(_program_id);
        if (year_no) query.year_no = year_no;
        if (level_no) query.level_no = level_no;
        if (rotation_count) query.rotation_count = rotation_count;
        let documentList;
        if (limit && page) {
            // const perPage = parseInt(limit > 0 ? limit : 10);
            // const pageNo = parseInt(page > 0 ? page : 1);
            documentList = await Document.find(query)
                .populate({ path: '_user_id', select: { _id: 1, name: 1 } })
                .sort({ createdAt: -1 });
            // .skip(perPage * (pageNo - 1))
            // .limit(perPage);
            // totalDoc = await Document.find(query, documentProject).countDocuments().exec();
            // totalPages = Math.ceil(totalDoc / perPage);
            // currentPage = pageNo;
        } else {
            documentList = await Document.find(query)
                .sort({ createdAt: -1 })
                .populate({ path: '_user_id', select: { _id: 1, name: 1 } });
        }
        const documentData = [];
        for (documentElement of documentList) {
            const scheduleDataElement = scheduleDatas.filter((scheduleELement) =>
                documentElement.sessionOrScheduleIds.find(
                    (sessionElement) =>
                        sessionElement._id.toString() === scheduleELement._id.toString() ||
                        (scheduleELement.session &&
                            scheduleELement.session._session_id &&
                            sessionElement._id.toString() ===
                                scheduleELement.session._session_id.toString() &&
                            (filter === 'my'
                                ? type === DC_STAFF
                                    ? scheduleELement.staffs.find(
                                          (staffElement) =>
                                              staffElement._staff_id.toString() ===
                                              userId.toString(),
                                      )
                                    : scheduleELement.students.find(
                                          (studentElement) =>
                                              studentElement._id.toString() === userId.toString(),
                                      )
                                : type === DC_STAFF
                                ? !scheduleELement.staffs.find(
                                      (staffElement) =>
                                          staffElement._staff_id.toString() === userId.toString(),
                                  )
                                : !scheduleELement.students.find(
                                      (studentElement) =>
                                          studentElement._id.toString() === userId.toString(),
                                  ))),
                ),
            );
            if (scheduleDataElement.length !== 0) {
                const singleDocument = {
                    _id: documentElement._id,
                    type: documentElement.type,
                    name: documentElement.name,
                    url: await getSignedURL(documentElement.url),
                    _course_id: documentElement._course_id,
                    _program_id: documentElement._program_id,
                    year_no: documentElement.year_no,
                    level_no: documentElement.level_no,
                    term: documentElement.term,
                    rotation: documentElement.rotation,
                    rotation_count: documentElement.rotation_count,
                    createdAt: documentElement.createdAt,
                    updatedAt: documentElement.updatedAt,
                };
                singleDocument.starred = !!documentElement.starred.find(
                    (userIds) => userIds.toString() === userId.toString(),
                );
                singleDocument.UploadedBy = documentElement._user_id;
                singleDocument.sessions = scheduleDataElement.map((scheduleDataElement) =>
                    scheduleDataElement.type === constant.SCHEDULE_TYPES.REGULAR
                        ? {
                              _id: scheduleDataElement.session._session_id,
                              delivery_symbol: scheduleDataElement.session.delivery_symbol,
                              delivery_no: scheduleDataElement.session.delivery_no,
                              session_type: scheduleDataElement.session.session_type,
                              session_topic: scheduleDataElement.session.session_topic,
                              type: constant.SCHEDULE_TYPES.REGULAR,
                          }
                        : {
                              _id: scheduleDataElement._id,
                              type: scheduleDataElement.type,
                              title: scheduleDataElement.title,
                          },
                );
                documentData.push(singleDocument);
            }
        }
        if (limit && page) {
            const page_no = page > 0 ? parseInt(page) : 1;
            const page_size = limit;
            const totalPages = Math.ceil(documentData.length / page_size);
            const slicedDocuments = documentData.slice(
                (parseInt(page_no) - 1) * parseInt(page_size),
                parseInt(page_no) * parseInt(page_size),
            );
            return {
                totalDoc: documentData.length,
                totalPages,
                currentPage: page_no,
                documentData: slicedDocuments,
            };
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documentData };
    } catch (error) {
        logger.error(error, 'documentManagerService -> getCourseDocumentListWithFilter -> Error');
        throw new Error(error);
    }
};

const endingZoomMeeting = async (coursescheduleId) => {
    try {
        logger.info('documentManagerService -> endingZoomMeeting -> %s - start', coursescheduleId);
        csQuery = {
            _id: { $in: coursescheduleId },
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            students: 1,
            scheduleEndDateAndTime: 1,
            scheduleStartDateAndTime: 1,
            zoomDetail: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.find(csQuery, csProject);
        if (!courseSchedule) {
            return { status: 0, msg: 'Course schedules not found' };
        }
        for (const schedule of courseSchedule) {
            const infraIds = schedule._infra_id;
            //getting Infra Datas as per condition
            const infraDetails = await getInfraById([infraIds]);

            let infraDetail = infraDetails
                .map((a) => a.programs)
                .flat()
                .map((b) =>
                    b.remoteScheduling.filter(
                        (i) =>
                            schedule._infra_id &&
                            i._id.toString() === schedule._infra_id.toString(),
                    ),
                )
                .flat()
                .shift();
            if (infraDetail) {
                //Checking Infra Data available or not
                infraDetail = JSON.parse(JSON.stringify(infraDetail));
                if (infraDetail && infraDetail.apiKey && infraDetail.apiSecretKey) {
                    // Generate the Token
                    const tokenGenerate = await tokenGeneration({
                        API_KEY: infraDetail.apiKey,
                        API_SECRET_KEY: infraDetail.apiSecretKey,
                    });
                    //creating createHostZoomMeeting
                    const EndingZoom = await hostZoomMeetingEnd({
                        meetingId: schedule.zoomDetail.zoomMeetingId,
                        params: {
                            action: 'end',
                        },
                        token: tokenGenerate,
                    });

                    const doc = await update(
                        courseSchedules,
                        convertToMongoObjectId(schedule._id),
                        {
                            'zoomDetail.meetingStatus': constant.COMPLETED,
                        },
                    );
                }
            }
        }

        logger.info('documentManagerService -> endingZoomMeeting -> %s - end', coursescheduleId);
    } catch (error) {
        throw new Error(error);
    }
};
const courseParamsUpdate = async () => {
    try {
        const sessionIdsList = [];

        const getCourseInfo = await getCourses();

        documentList = await Document.find({ term: { $exists: false } });

        const courseDocumentParams = [];
        for (documentElement of documentList) {
            const courseDetails = getCourseInfo.find(
                (courses) =>
                    courses._id === documentElement._course_id &&
                    courses._institution_calendar_id === documentElement._institution_calendar_id,
            );
            if (courseDetails) {
                const documentUpdateQuery = {
                    _program_id: courseDetails._program_id,
                    year_no: courseDetails.year_no,
                    level_no: courseDetails.level_no,
                    term: courseDetails.term,
                    rotation: courseDetails.rotation,
                };
                if (courseDetails.rotation_count) {
                    documentUpdateQuery.rotation_count = courseDetails.rotation_count;
                }
                courseDocumentParams.push({
                    updateOne: {
                        filter: {
                            _id: documentElement._id,
                        },
                        update: {
                            $set: documentUpdateQuery,
                        },
                    },
                });
            }
        }
        //Bulk updating in to course schedules
        if (courseDocumentParams.length > 0) {
            const doc = await bulk_write(Document, courseDocumentParams);
            if (doc.status) {
                return { msg: 'Course params updated Successfully' };
            }
            return { msg: 'Course params not updated Successfully' };
        }
        return { msg: 'Document not available to update' };
    } catch (error) {
        throw new Error(error);
    }
};
const checkStudentActivityOrDocument = async (data) => {
    try {
        if (data.documentId) {
            document = await Document.findOne({
                _id: convertToMongoObjectId(data.documentId),
                _course_id: convertToMongoObjectId(data._course_id),
            });
            if (!document) return { status: false, msg: 'No Document data' };
            const sessionIds = document.sessionOrScheduleIds.map((session) => session._id);
            const query = {
                $or: [
                    { _id: { $in: sessionIds } },
                    { 'session._session_id': { $in: sessionIds } },
                    { _course_id: convertToMongoObjectId(data._course_id) },
                ],
                isDeleted: false,
                isActive: true,
            };
            if (data.type === DC_STUDENT) {
                query['students._id'] = convertToMongoObjectId(data.userId);
            }
            if (data.type === DC_STAFF) {
                query['staffs._staff_id'] = convertToMongoObjectId(data.userId);
            }
            const courseSchedule = await courseSchedules
                .find(query, { student_groups: 1, _id: 1 })
                .lean();

            if (data.groupNames) {
                const courseScheduleGroups = [];
                for (schedule of courseSchedule) {
                    if (schedule.student_groups) {
                        for (studentGroup of schedule.student_groups) {
                            if (studentGroup.group_name) {
                                let groupName = studentGroup.group_name.split('-').slice(-2);
                                groupName = groupName[1]
                                    ? groupName[0] + '-' + groupName[1]
                                    : groupName[0];
                                if (courseScheduleGroups.indexOf(groupName) == -1) {
                                    courseScheduleGroups.push(groupName);
                                }
                            }
                        }
                    }
                }
                const assignedGroupNames = data.groupNames.replace(/ *\([^)]*\) */g, '');
                const assignedStudentGroups = assignedGroupNames.split(',');
                for (assignedStudentGroup of assignedStudentGroups) {
                    const chkGroup = courseScheduleGroups.find(
                        (group) => group === assignedStudentGroup,
                    );
                    if (chkGroup) {
                        return { status: true, msg: 'User found' };
                    }
                }
                return { status: false, msg: 'User not found' };
            }
            if (courseSchedule.length > 0) {
                return { status: true, msg: 'User found' };
            }
            return { status: false, msg: 'User not found' };
        }
        if (data.activityId) {
            activity = await Activity.findOne({
                _id: convertToMongoObjectId(data.activityId),
                courseId: convertToMongoObjectId(data._course_id),
            });
            if (!activity) return { status: false, msg: 'No Activity data found' };
            if (data.type === DC_STAFF) {
                const sessionIds = activity.sessionFlowIds.map((session) => session._id);
                const query = {
                    _course_id: convertToMongoObjectId(data._course_id),
                    'staffs._staff_id': convertToMongoObjectId(data.userId),
                    isDeleted: false,
                    isActive: true,
                };
                const courseSchedule = await courseSchedules
                    .find(query, { student_groups: 1, _id: 1 })
                    .lean();
                if (data.groupNames) {
                    const courseScheduleGroups = [];
                    for (schedule of courseSchedule) {
                        if (schedule.student_groups) {
                            for (studentGroup of schedule.student_groups) {
                                if (studentGroup.group_name) {
                                    let groupName = studentGroup.group_name.split('-').slice(-2);
                                    groupName = groupName[1]
                                        ? groupName[0] + '-' + groupName[1]
                                        : groupName[0];
                                    if (courseScheduleGroups.indexOf(groupName) == -1) {
                                        courseScheduleGroups.push(groupName);
                                    }
                                }
                            }
                        }
                    }

                    const assignedGroupNames = data.groupNames.replace(/ *\([^)]*\) */g, '');
                    const assignedStudentGroups = assignedGroupNames.split(',');
                    for (assignedStudentGroup of assignedStudentGroups) {
                        const chkGroup = courseScheduleGroups.find(
                            (group) => group === assignedStudentGroup,
                        );
                        if (chkGroup) {
                            return {
                                status: true,
                                msg: 'User found',
                                activityStatus: activity.status,
                            };
                        }
                    }
                    return { status: false, msg: 'User not found' };
                }

                if (courseSchedule.length > 0) {
                    return { status: true, msg: 'User found', activityStatus: activity.status };
                }
                return { status: false, msg: 'User not found' };
            }
            if (data.type === DC_STUDENT) {
                if (activity.scheduleIds && activity.scheduleIds.length > 0) {
                    const scheduleIds = activity.scheduleIds.map((scheduleId) => scheduleId);

                    const cSchedule = await courseSchedules
                        .find(
                            {
                                _id: { $in: scheduleIds },
                                'students._id': convertToMongoObjectId(data.userId),
                            },
                            { student_groups: 1, _id: 1, students: 1 },
                        )
                        .lean();
                    if (cSchedule.length > 0) {
                        return { status: true, msg: 'User found', activityStatus: activity.status };
                    }
                    return { status: false, msg: 'User not found' };
                }
                return { status: false, msg: 'Schedule not found' };
            } // Student Check End
        }
        return { status: false, msg: 'Document Id or Activity Id not found' };
    } catch (error) {
        throw new Error(error);
    }
};
const createTeamsMeeting = async (coursescheduleIds, staffId) => {
    try {
        logger.info(
            'documentManagerService -> createTeamsMeeting -> %s - start',
            coursescheduleIds,
        );
        csQuery = {
            'teamsDetail.teamsStartUrl': { $exists: false },
            _id: coursescheduleIds,
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            students: 1,
            scheduleEndDateAndTime: 1,
            scheduleStartDateAndTime: 1,
            zoomDetail: 1,
            merge_with: 1,
            merge_status: 1,
            teamsDetail: 1,
            remotePlatform: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.findOne(csQuery, csProject);
        if (!courseSchedule) {
            return { status: 0, msg: 'Course schedules not matching as per conditions' };
        }

        let scheduleIds = [courseSchedule._id];
        if (courseSchedule.merge_status) {
            const mergeScheduleIds = courseSchedule.merge_with.map((mw) =>
                convertToMongoObjectId(mw.schedule_id),
            );
            scheduleIds = scheduleIds.concat(mergeScheduleIds);
        }

        const courseTeamsDatas = [];
        //getting Infra Ids details
        if (courseSchedule.remotePlatform === TEAMS) {
            // Generate the Token
            const tokenGenerate = await teamsTokenGeneration();

            const staffDetails = await users.findOne(
                { _id: convertToMongoObjectId(staffId) },
                { _id: 1, name: 1, email: 1 },
            );

            const getZoomUserList = await teamsUserListByEmail({
                token: tokenGenerate.access_token,
                email: staffDetails.email,
            });

            const UserResult = getZoomUserList.value.find(
                (userList) =>
                    userList.mail.toLowerCase().toString() ===
                    staffDetails.email.toLowerCase().toString() /*  && userList.role_id ===
                        '0' */,
            );
            if (UserResult) {
                const duration = differenceInMinutes(
                    courseSchedule.scheduleStartDateAndTime,
                    courseSchedule.scheduleEndDateAndTime,
                );
                let topicName;
                if (courseSchedule.session.session_topic) {
                    topicName =
                        courseSchedule.session.delivery_symbol +
                        '' +
                        courseSchedule.session.delivery_no +
                        ' ' +
                        courseSchedule.session.session_topic;
                } else {
                    topicName = courseSchedule.title;
                }

                //creating createTeamsMeeting
                const teamsMeeting = await createTeamsMeetingAPI({
                    params: {
                        startDateTime: courseSchedule.scheduleStartDateAndTime,
                        endDateTime: courseSchedule.scheduleEndDateAndTime,
                        subject: topicName,
                        recordAutomatically: true,
                    },
                    UserId: UserResult.id,
                    token: tokenGenerate.access_token,
                });
                if (teamsMeeting && teamsMeeting.id) {
                    for (const scheduleId of scheduleIds) {
                        courseTeamsDatas.push({
                            updateOne: {
                                filter: {
                                    _id: scheduleId,
                                },
                                update: {
                                    $set: {
                                        'teamsDetail.teamMeetingId': teamsMeeting.id,
                                        'teamsDetail.teamsStartUrl': teamsMeeting.joinUrl,
                                        'teamsDetail.threadId': teamsMeeting.chatInfo.threadId,
                                        'teamsDetail.teamsUserId': UserResult.id,
                                    },
                                },
                            },
                        });
                    }

                    //Bulk updating in to course schedules
                    if (courseTeamsDatas.length > 0) {
                        const doc = await bulk_write(courseSchedules, courseTeamsDatas);
                        if (doc.status) {
                            // returning join url
                            return { start_url: teamsMeeting.joinUrl, status: 1 };
                        }
                    }
                }
            }
        }
        logger.info('documentManagerService -> createTeamsMeeting -> %s - end', coursescheduleIds);
        return { start_url: '', status: 0 };
    } catch (error) {
        throw new Error(error);
    }
};
const getTeamsUsersDuration = async (coursescheduleIds) => {
    try {
        logger.info(
            'documentManagerService -> getTeamsUsersDuration -> %s - start',
            coursescheduleIds,
        );
        csQuery = {
            $or: [{ teamsDuration: { $exists: false } }, { teamsDuration: false }],
            _id: { $in: coursescheduleIds },
        };

        csProject = {
            _id: 1,
            _infra_id: 1,
            _course_id: 1,
            zoomRecord: 1,
            mode: 1,
            session: 1,
            schedule_date: 1,
            title: 1,
            start: 1,
            end: 1,
            sessionDetail: 1,
            type: 1,
            students: 1,
            staffs: 1,
            scheduleEndDateAndTime: 1,
            scheduleStartDateAndTime: 1,
            zoomDetail: 1,
            teamsDetail: 1,
            remotePlatform: 1,
        };
        // Course Schedule Querying
        const courseSchedule = await courseSchedules.find(csQuery, csProject);
        if (!courseSchedule) {
            return { status: 0, msg: 'Course schedules not found' };
        }

        // Looping Course Schedules
        for (schedule of courseSchedule) {
            const calculateTotalDuration = [];
            // check course Schedules to go only remote
            if (
                schedule.mode === constant.TIME_GROUP_BOOKING_TYPE.REMOTE &&
                schedule.remotePlatform === TEAMS &&
                schedule.teamsDetail.teamMeetingId
            ) {
                // Generate the Token
                const tokenGenerate = await teamsTokenGeneration();
                //Getting whole meeting Duration
                const meetingOccurences = await getTeamsMeetingOccurences({
                    meetingId: schedule.teamsDetail.teamMeetingId,
                    token: tokenGenerate.access_token,
                    userId: schedule.teamsDetail.teamsUserId,
                });
                const usersTimeDuration = [];
                const getSeconds = Math.abs(
                    (new Date(schedule.sessionDetail.stop_time).getTime() -
                        new Date(schedule.sessionDetail.start_time).getTime()) /
                        1000,
                );
                if (meetingOccurences.value.length > 0) {
                    for (meetingOccurence of meetingOccurences.value) {
                        if (meetingOccurence.totalParticipantCount != 0) {
                            if (
                                new Date(meetingOccurence.meetingStartDateTime) >
                                    new Date(schedule.sessionDetail.start_time) &&
                                new Date(meetingOccurence.meetingStartDateTime) <
                                    new Date(schedule.sessionDetail.stop_time)
                            ) {
                                const meetingOccurenceId = meetingOccurence.id;

                                if (meetingOccurence.totalParticipantCount > 0) {
                                    const participantLists = await getTeamsParticipantList({
                                        meetingId: schedule.teamsDetail.teamMeetingId,
                                        token: tokenGenerate.access_token,
                                        reportId: meetingOccurenceId,
                                        userId: schedule.teamsDetail.teamsUserId,
                                    });
                                    if (
                                        participantLists.attendanceRecords &&
                                        participantLists.attendanceRecords.length > 0
                                    ) {
                                        for (participantList of participantLists.attendanceRecords) {
                                            //Find index of specific object using findIndex method.
                                            if (
                                                !participantList.emailAddress ||
                                                !participantList.emailAddress.length
                                            )
                                                continue;
                                            const getIndexOfUsersTimeDuration =
                                                usersTimeDuration.findIndex(
                                                    (obj) =>
                                                        obj.email === participantList.emailAddress,
                                                );
                                            if (getIndexOfUsersTimeDuration === -1) {
                                                const Teamsduration = calculateTeamsDuration(
                                                    schedule.sessionDetail.start_time,
                                                    schedule.sessionDetail.stop_time,
                                                    participantList,
                                                );

                                                usersTimeDuration.push({
                                                    duration: Teamsduration,
                                                    email: participantList.emailAddress,
                                                });
                                            } else {
                                                const Teamsduration = calculateTeamsDuration(
                                                    schedule.sessionDetail.start_time,
                                                    schedule.sessionDetail.stop_time,
                                                    participantList,
                                                );

                                                const existingUsersDuration =
                                                    usersTimeDuration[getIndexOfUsersTimeDuration]
                                                        .duration;
                                                usersTimeDuration[
                                                    getIndexOfUsersTimeDuration
                                                ].duration = existingUsersDuration + Teamsduration;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //Find index of specific object using findIndex method.
                    const getIndexOfcalculatedTotalDuration = calculateTotalDuration.findIndex(
                        (obj) => obj.scheduleId === schedule._id.toString(),
                    );
                    if (getIndexOfcalculatedTotalDuration === -1) {
                        calculateTotalDuration.push({
                            totalDuration: getSeconds,
                            scheduleId: schedule._id.toString(),
                            users: usersTimeDuration,
                        });
                    } else {
                        const existingDuration =
                            calculateTotalDuration[getIndexOfcalculatedTotalDuration].totalDuration;
                        calculateTotalDuration[getIndexOfcalculatedTotalDuration].totalDuration =
                            existingDuration + getSeconds;
                        calculateTotalDuration[getIndexOfcalculatedTotalDuration].users =
                            usersTimeDuration;
                    }
                }

                const getTotalDuration = calculateTotalDuration.find(
                    (duration) => duration.scheduleId === schedule._id.toString(),
                );
                const staffIds = schedule.staffs.map((staffId) => staffId._staff_id);

                if (
                    staffIds.length > 0 &&
                    calculateTotalDuration.length > 0 &&
                    getTotalDuration &&
                    getTotalDuration.totalDuration
                ) {
                    const adminDurationPush = [];
                    //update duration in staff
                    for (const staffId of staffIds) {
                        adminDurationPush.push({
                            updateOne: {
                                filter: {
                                    'staffs._staff_id': convertToMongoObjectId(staffId),
                                    _id: schedule._id,
                                },
                                update: {
                                    $set: {
                                        'staffs.$[i].duration': getTotalDuration.totalDuration,
                                    },
                                },
                                arrayFilters: [{ 'i._staff_id': convertToMongoObjectId(staffId) }],
                            },
                        });
                    }
                    if (adminDurationPush.length > 0) {
                        const doc = await bulk_write(courseSchedules, adminDurationPush);
                    }
                }

                //getting all students
                const studentIds = schedule.students
                    .filter(
                        (studentElement) => !leavePermissionOnDuty.includes(studentElement.status),
                    )
                    .map((studentId) => studentId._id);
                //sum of duplicate durations
                if (
                    studentIds.length > 0 &&
                    calculateTotalDuration.length > 0 &&
                    getTotalDuration &&
                    getTotalDuration.totalDuration
                ) {
                    const adminDuration = getTotalDuration.totalDuration;
                    const getEmail = [];
                    const durationPush = [];

                    const studentList = await users.find(
                        { _id: { $in: studentIds }, isActive: true, isDeleted: false },
                        { _id: 1, name: 1, email: 1 },
                    );

                    for (const students of studentList) {
                        const result = getTotalDuration.users.find(
                            (studentDetail) =>
                                studentDetail.email.toLowerCase() === students.email.toLowerCase(),
                        );
                        if (result) {
                            let status = '';
                            const percentage =
                                (100 * parseFloat(result.duration)) / parseFloat(adminDuration);

                            const IndividualStudentData = schedule.students.find(
                                (studentElement) =>
                                    studentElement._id.toString() === students._id.toString(),
                            );

                            if (
                                parseFloat(participantDuration) >= percentage ||
                                !IndividualStudentData.join_url
                            ) {
                                status = ABSENT;
                            } else {
                                status = PRESENT;
                            }
                            durationPush.push({
                                updateOne: {
                                    filter: {
                                        'students._id': convertToMongoObjectId(students._id),
                                        _id: schedule._id,
                                    },
                                    update: {
                                        $set: {
                                            'students.$[i].status': status,
                                            'students.$[i].duration':
                                                result.duration >= getTotalDuration.totalDuration
                                                    ? getTotalDuration.totalDuration
                                                    : result.duration,
                                            'students.$[i].percentage':
                                                percentage >= 100 ? 100 : percentage,
                                            'teamsDetail.teamsTotalDuration':
                                                getTotalDuration.totalDuration,
                                        },
                                    },
                                    arrayFilters: [
                                        {
                                            'i._id': convertToMongoObjectId(students._id),
                                            'i.mode': 'auto',
                                        },
                                    ],
                                },
                            });
                        } else {
                            durationPush.push({
                                updateOne: {
                                    filter: {
                                        'students._id': convertToMongoObjectId(students._id),
                                        _id: schedule._id,
                                    },
                                    update: {
                                        $set: {
                                            'students.$[i].status': ABSENT,
                                            'students.$[i].duration': 0,
                                            'teamsDetail.teamsTotalDuration':
                                                getTotalDuration.totalDuration,
                                        },
                                    },
                                    arrayFilters: [
                                        {
                                            'i._id': convertToMongoObjectId(students._id),
                                            'i.mode': 'auto',
                                        },
                                    ],
                                },
                            });
                        }
                    }

                    //Bulk updating in to course schedules
                    if (durationPush.length > 0) {
                        const doc = await bulk_write(courseSchedules, durationPush);
                    }
                }
            }
        }
        logger.info(
            'documentManagerService -> getTeamsUsersDuration -> %s - End',
            coursescheduleIds,
        );
    } catch (error) {
        throw new Error(error);
    }
};

const getUserRecentDocumentList = async (
    userId,
    limit,
    page,
    search,
    tab,
    subTab,
    type,
    _institution_calendar_id,
) => {
    try {
        const { getUserCourseLists } = require('../course_session/course_session_service');
        const userCourseInfo = await getUserCourseLists({
            userId,
            type,
            institutionCalendarId: convertToMongoObjectId(_institution_calendar_id),
        });
        const DocumentQuery = { isDeleted: false };
        if (userCourseInfo.length > 0) {
            DocumentQuery._course_id = {
                $in: [
                    ...new Set(
                        userCourseInfo.map((scheduleElement) => scheduleElement._id.toString()),
                    ),
                ],
            };
            let staffIdList;
            if (search) {
                const staffIds = await users.find(
                    {
                        $or: [
                            { 'name.first': new RegExp(search, 'i') },
                            { 'name.last': new RegExp(search, 'i') },
                        ],
                    },
                    { _id: 1 },
                );
                staffIdList = staffIds.map((userId) => userId._id);
            }
            if (search) {
                DocumentQuery.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { _user_id: { $in: staffIdList } },
                ];
            }
            if (tab) {
                DocumentQuery.starred = convertToMongoObjectId(userId);
            }
            if (subTab) {
                DocumentQuery.url = fileFormatSubTabFilter(subTab);
            }
            if (_institution_calendar_id) {
                DocumentQuery._institution_calendar_id =
                    convertToMongoObjectId(_institution_calendar_id);
            }
            const perPage = parseInt(limit > 0 ? limit : 10);
            const pageNo = parseInt(page > 0 ? page : 1);
            console.time('userRecentDocuments');
            const userRecentDocuments = await Document.find(DocumentQuery)
                .sort({ createdAt: -1 })
                .skip(perPage * (pageNo - 1))
                .limit(perPage)
                .populate({ path: '_user_id', select: { _id: 1, name: 1 } });

            const totalDoc = await Document.find(DocumentQuery).countDocuments().exec();
            console.timeEnd('userRecentDocuments');
            const totalPages = Math.ceil(totalDoc / perPage);
            const currentPage = pageNo;
            let sessionIds = [];
            let supportEventId = [];
            for (const documentElement of userRecentDocuments) {
                // const matchURLData = documentElement.url
                //     ? documentElement.url.match(/amazonaws.com/g)
                //     : null;
                if (
                    // matchURLData !== null ||
                    documentElement.type === 'file' &&
                    documentElement.url /* &&
                    documentElement.url.split('/') */
                ) {
                    documentElement.url = await getSignedURL(documentElement.url);
                }
                if (
                    // matchURLData !== null ||
                    documentElement.type === 'file' &&
                    documentElement.urls /* &&
                    documentElement.url.split('/') */
                ) {
                    documentElement.urls = await Promise.all(
                        documentElement.urls.map(async (url) => {
                            return await getSignedURL(url);
                        }),
                    );
                }
                sessionIds = [
                    ...sessionIds,
                    ...documentElement.sessionOrScheduleIds
                        .filter(
                            (sessionScheduleIdElement) =>
                                sessionScheduleIdElement.type === constant.SCHEDULE_TYPES.REGULAR,
                        )
                        .map((sessionScheduleIdElement) => sessionScheduleIdElement._id.toString()),
                ];
                supportEventId = [
                    ...supportEventId,
                    ...documentElement.sessionOrScheduleIds
                        .filter(
                            (sessionScheduleIdElement) =>
                                sessionScheduleIdElement.type !== constant.SCHEDULE_TYPES.REGULAR,
                        )
                        .map((sessionScheduleIdElement) => sessionScheduleIdElement._id.toString()),
                ];
            }
            sessionIds = [...new Set(sessionIds)];
            supportEventId = [...new Set(supportEventId)];
            const documentScheduleDetails = await courseSchedules.find(
                {
                    isDeleted: false,
                    $or: [
                        {
                            'session._session_id': { $in: sessionIds },
                        },
                        {
                            _id: { $in: supportEventId },
                        },
                    ],
                },
                {
                    'session._session_id': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    title: 1,
                },
            );
            const documents = userRecentDocuments.map(function (Docs) {
                Items = {
                    _id: Docs._id,
                    type: Docs.type,
                    name: Docs.name,
                    url: Docs.url,
                    _course_id: Docs._course_id,
                    _program_id: Docs._program_id,
                    year_no: Docs.year_no,
                    level_no: Docs.level_no,
                    term: Docs.term,
                    rotation: Docs.rotation,
                    rotation_count: Docs.rotation_count,
                    _institution_calendar_id: Docs._institution_calendar_id,
                    createdAt: Docs.createdAt,
                    updatedAt: Docs.updatedAt,
                    courseAdmin: Docs.courseAdmin,
                    remote: Docs.remote,
                };

                const courseDetails = userCourseInfo.find(
                    (courseData) => courseData._id.toString() === Docs._course_id.toString(),
                );
                if (courseDetails) {
                    Items.courseDetail = {
                        course_code: courseDetails.course_code,
                        course_name: courseDetails.course_name,
                        _course_id: courseDetails._id,
                        versionNo: courseDetails.versionNo || 1,
                        versioned: courseDetails.versioned || false,
                        versionName: courseDetails.versionName || '',
                        versionedFrom: courseDetails.versionedFrom || null,
                        versionedCourseIds: courseDetails.versionedCourseIds || [],
                    };
                }
                Items.starred = !!Docs.starred.find((userIds) => userIds.toString() === userId);
                const sessionDatas = [];
                if (Docs.sessionOrScheduleIds) {
                    Docs.sessionOrScheduleIds.map((session_id) => {
                        const sessionDetails = documentScheduleDetails.find(
                            (session) =>
                                session.session._session_id &&
                                session.session._session_id.toString() ===
                                    session_id._id.toString(),
                        );
                        const supportSessionDetails = documentScheduleDetails.find(
                            (session) =>
                                session._id && session._id.toString() === session_id._id.toString(),
                        );
                        if (sessionDetails) {
                            sessionDatas.push({
                                _id: sessionDetails.session._session_id,
                                delivery_symbol: sessionDetails.session.delivery_symbol,
                                delivery_no: sessionDetails.session.delivery_no,
                                // session_type: sessionDetails.session_type,
                                // session_topic: sessionDetails.session_topic,
                                type: session_id.type,
                            });
                        }
                        if (supportSessionDetails) {
                            sessionDatas.push({
                                _id: supportSessionDetails._id,
                                title: supportSessionDetails.title,
                                type: session_id.type,
                            });
                        }
                    });
                }
                Items.UploadedBy = Docs._user_id;
                Items.sessions = sessionDatas;
                return Items;
            });
            if (limit && page) {
                return { totalDoc, totalPages, currentPage, documents };
            }
        }
        return { totalDoc: 0, totalPages: 0, currentPage: 0, documents: [] };
    } catch (error) {
        throw new Error(error);
    }
};

const scheduleRemoteSessionStart = async ({ courseScheduleData, _staff_id }) => {
    try {
        logger.info(
            { courseScheduleData },
            'documentManagerService -> createMeeting -> %s - %s - start',
            courseScheduleData._id,
            _staff_id,
        );
        if (courseScheduleData.remotePlatform === TEAMS) {
            // Generate the Token
            const tokenGenerate = await teamsTokenGeneration();
            const staffDetails = await users.findOne(
                { _id: convertToMongoObjectId(_staff_id) },
                { email: 1 },
            );
            const getZoomUserList = await teamsUserListByEmail({
                token: tokenGenerate.access_token,
                email: staffDetails.email,
            });
            const UserResult = getZoomUserList.value.find(
                (userList) =>
                    userList.mail.toLowerCase().toString() ===
                    staffDetails.email.toLowerCase().toString(),
            );
            if (UserResult) {
                let topicName = courseScheduleData.session.session_topic
                    ? courseScheduleData.session.delivery_symbol +
                      '' +
                      courseScheduleData.session.delivery_no +
                      ' ' +
                      courseScheduleData.session.session_topic
                    : courseScheduleData.title;
                topicName = topicName
                    ? topicName.length > 100
                        ? topicName.substring(0, 100 - 3) + '...'
                        : topicName
                    : '';
                //creating createTeamsMeeting
                const teamsMeeting = await createTeamsMeetingAPI({
                    params: {
                        startDateTime: courseScheduleData.scheduleStartDateAndTime,
                        endDateTime: courseScheduleData.scheduleEndDateAndTime,
                        subject: topicName,
                        recordAutomatically: true,
                    },
                    UserId: UserResult.id,
                    token: tokenGenerate.access_token,
                });
                if (teamsMeeting && teamsMeeting.id) {
                    logger.info(
                        { courseScheduleData },
                        'documentManagerService -> createMeeting -> %s - end',
                        courseScheduleData._id,
                    );
                    console.log('Course courseMSTeamDatas Updated Successfully');
                    return {
                        status: true,
                        teamsDetail: {
                            teamMeetingId: teamsMeeting.id,
                            teamsStartUrl: teamsMeeting.joinUrl,
                            threadId: teamsMeeting.chatInfo.threadId,
                            teamsUserId: UserResult.id,
                        },
                    };
                }
            }
        }
        //getting Infra Datas as per condition
        const infraDetails = await getInfraById([courseScheduleData._infra_id]);
        let infraDetail = infraDetails
            .map((a) => a.programs)
            .flat()
            .map((b) =>
                b.remoteScheduling.filter(
                    (i) =>
                        courseScheduleData._infra_id &&
                        i._id.toString() === courseScheduleData._infra_id.toString(),
                ),
            )
            .flat()
            .shift();
        if (infraDetail) {
            //Checking Infra Data available or not
            infraDetail = JSON.parse(JSON.stringify(infraDetail));
            if (infraDetail && infraDetail.apiKey && infraDetail.apiSecretKey) {
                // Generate the Token
                const tokenGenerate = await tokenGeneration({
                    API_KEY: infraDetail.apiKey,
                    API_SECRET_KEY: infraDetail.apiSecretKey,
                });
                //Getting Meeting UUID lists using this function
                const getZoomUserList = await getZoomHostUserList({
                    token: tokenGenerate,
                });
                const UserResult = getZoomUserList.users.find(
                    (userList) => userList.email === infraDetail.associatedEmail,
                );
                if (UserResult) {
                    const duration = differenceInMinutes(
                        courseScheduleData.scheduleStartDateAndTime,
                        courseScheduleData.scheduleEndDateAndTime,
                    );
                    let topicName = courseScheduleData.session.session_topic
                        ? courseScheduleData.session.delivery_symbol +
                          '' +
                          courseScheduleData.session.delivery_no +
                          ' ' +
                          courseScheduleData.session.session_topic
                        : courseScheduleData.title;
                    topicName = topicName
                        ? topicName.length > 100
                            ? topicName.substring(0, 100 - 3) + '...'
                            : topicName
                        : '';
                    //creating createHostZoomMeeting
                    const ZoomMeeting = await createHostZoomMeeting({
                        userId: UserResult.id,
                        params: {
                            topic: topicName,
                            type: 2,
                            start_time: courseScheduleData.scheduleStartDateAndTime,
                            password: infraDetail.passCode,
                            duration,
                            agenda: topicName,
                            settings: {
                                host_video: false,
                                registrants_email_notification: false,
                                participant_video: false,
                                approval_type: 0, // Added 0 for allowing student to register in zoom // added 2 for disabling registraion in sdk
                            },
                        },
                        token: tokenGenerate,
                    });
                    logger.info(
                        { courseScheduleData },
                        'documentManagerService -> createMeeting -> %s - end',
                        courseScheduleData._id,
                    );
                    console.log('Course courseZoomDatas Updated Successfully');
                    return {
                        status: true,
                        zoomDetail: {
                            zoomMeetingId: ZoomMeeting.id,
                            zoomUuid: ZoomMeeting.uuid,
                            zoomStartUrl: ZoomMeeting.start_url,
                            passCode: infraDetail.passCode,
                        },
                    };
                }
            }
        }
        logger.info(
            { courseScheduleData },
            'documentManagerService -> createMeeting -> %s - end',
            courseScheduleData._id,
        );
        return { status: false };
    } catch (error) {
        throw new Error(error);
    }
};

module.exports = {
    getDocumentList,
    getCourseDocumentList,
    getCourseSessionDocumentList,
    getAllSessionList,
    getZoomRecordLists,
    getZoomUsersDuration,
    DocumentCreations,
    createMeeting,
    createStudentJoinUrl,
    getCourseSessionDocumentListWithFilter,
    endingZoomMeeting,
    getCourseAdminDocuments,
    getCourseDocumentListWithFilter,
    courseParamsUpdate,
    checkStudentActivityOrDocument,
    getCoursesForActivity,
    createTeamsMeeting,
    getTeamsUsersDuration,
    getUserRecentDocumentList,
    scheduleRemoteSessionStart,
    multipleDocumentCreations,
};
