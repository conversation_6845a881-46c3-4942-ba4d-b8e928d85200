const mongoose = require('mongoose');
const {
    ASSIGNMENT_REPORT,
    INSTITUTION_CALENDAR,
    DIGI_COURSE,
    ASSIGNMENT_REPORT_TYPE,
} = require('../../../utility/constants');
const { DIGI_PROGRAM, USER } = require('../../../../lib/utility/constants');
const Schema = mongoose.Schema;
const objectId = mongoose.Types.ObjectId;

const assignmentReportSchema = new Schema(
    {
        _institution_id: {
            type: objectId,
        },
        _institution_calendar_id: {
            type: objectId,
            ref: INSTITUTION_CALENDAR,
        },
        year: {
            type: String,
        },
        term: {
            type: String,
        },
        level: {
            type: String,
        },
        programId: {
            type: objectId,
            ref: DIGI_PROGRAM,
        },
        courseId: {
            type: objectId,
            ref: DIGI_COURSE,
        },
        staffId: {
            type: objectId,
            ref: USER,
        },
        programName: {
            type: String,
        },
        courseName: {
            type: String,
        },
        rotation_count: {
            type: Number,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        course_code: {
            type: String,
            ref: DIGI_COURSE,
        },
        reportType: {
            type: String,
            default: ASSIGNMENT_REPORT_TYPE,
        },
        assignments: {
            summative: [
                {
                    assignmentName: {
                        type: String,
                    },
                    assignmentId: {
                        type: objectId,
                    },
                    assignmentMark: {
                        type: Number,
                    },
                    assignmentType: {
                        type: String,
                    },
                },
            ],
            formativeGraded: [
                {
                    assignmentName: {
                        type: String,
                    },
                    assignmentId: {
                        type: objectId,
                    },
                    assignmentMark: {
                        type: Number,
                    },
                    assignmentType: {
                        type: String,
                    },
                },
            ],
            formativeUngraded: [
                {
                    assignmentName: {
                        type: String,
                    },
                    assignmentId: {
                        type: objectId,
                    },
                    assignmentMark: {
                        type: Number,
                    },
                    assignmentType: {
                        type: String,
                    },
                },
            ],
            ratGraded: [
                {
                    assignmentName: {
                        type: String,
                    },
                    assignmentId: {
                        type: objectId,
                    },
                    assignmentMark: {
                        type: Number,
                    },
                    assignmentType: {
                        type: String,
                    },
                },
            ],
            ratUngraded: [
                {
                    assignmentName: {
                        type: String,
                    },
                    assignmentId: {
                        type: objectId,
                    },
                    assignmentMark: {
                        type: Number,
                    },
                    assignmentType: {
                        type: String,
                    },
                },
            ],
        },
        equivalence: {
            isActive: {
                type: Boolean,
                default: false,
            },
            equivalenceMark: {
                type: Number,
            },
        },
        extraMarks: {
            isActive: {
                type: Boolean,
                default: false,
            },
            markType: {
                type: String,
                enum: ['all', 'specific', 'male', 'female'],
            },
            mark: {
                type: Number,
            },
            position: {
                type: String,
                enum: ['before', 'after'],
            },
        },
        finalReport: {
            markAdjustment: {
                type: Boolean,
                default: false,
            },
            deductMark: {
                type: Boolean,
                default: false,
            },
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(ASSIGNMENT_REPORT, assignmentReportSchema);
