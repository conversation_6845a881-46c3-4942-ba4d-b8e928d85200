const express = require('express');
const router = express.Router();
const catchAsync = require('../../../utility/catch-async');
const {
    listAssignmentComments,
    updateAssignmentComments,
    deleteAssignmentComments,
} = require('./assignment_comments.controller');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.get(
    '/list/:assignmentId/:studentId',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(listAssignmentComments),
);
router.post(
    '/update',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(updateAssignmentComments),
);
router.delete(
    '/delete/:_assignment_id/:_student_id/:commentId',
    catchAsync(deleteAssignmentComments),
);
module.exports = router;
