const express = require('express');
const route = express.Router();

// controller
const {
    getExtraCurricularAndBreakTiming,
    createExtraCurricularAndBreakTiming,
    updateExtraCurricularAndBreakTiming,
    deleteExtraCurricularAndBreakTiming,
    enableDisableExtraCurricularAndBreakTiming,
    events,
} = require('./course_schedule_controller');

// validate schema
const {
    createExtraCurricularBreakTiming: { body: createExtraCurricularBreakTimingBodySchema },
    updateExtraCurricularBreakTiming: { body: updateExtraCurricularBreakTimingBodySchema },
} = require('./course_schedule_validate_schema');
// validate

const { validate } = require('../../middleware/validation');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.get('/events/:programId/:instCalId/:courseId', [userPolicyAuthentication([])], events);
route.get(
    '/extraCurricular-breakTiming/:programId/:instCalId',
    [
        userPolicyAuthentication([
            'schedule_management:extra_curricular_and_break:list_view',
            'schedule_management:extra_curricular_and_break:break:view',
            'schedule_management:extra_curricular_and_break:extra_curricular:view',
        ]),
    ],
    getExtraCurricularAndBreakTiming,
);

route.post(
    '/extraCurricular-breakTiming',
    [
        userPolicyAuthentication([
            'schedule_management:extra_curricular_and_break:break:add',
            'schedule_management:extra_curricular_and_break:extra_curricular:add',
        ]),
    ],
    validate([{ schema: createExtraCurricularBreakTimingBodySchema, property: 'body' }]),
    createExtraCurricularAndBreakTiming,
);

route.put(
    '/extraCurricular-breakTiming/:id/:programId',
    [
        userPolicyAuthentication([
            'schedule_management:extra_curricular_and_break:break:edit',
            'schedule_management:extra_curricular_and_break:extra_curricular:edit',
        ]),
    ],
    validate([{ schema: createExtraCurricularBreakTimingBodySchema, property: 'body' }]),
    updateExtraCurricularAndBreakTiming,
);
route.put(
    '/extraCurricular-breakTiming/enable-disable/:id/:programId',
    [
        userPolicyAuthentication([
            'schedule_management:extra_curricular_and_break:break:enable/disable',
            'schedule_management:extra_curricular_and_break:extra_curricular:enable/disable',
        ]),
    ],
    validate([{ schema: updateExtraCurricularBreakTimingBodySchema, property: 'body' }]),
    enableDisableExtraCurricularAndBreakTiming,
);
route.delete(
    '/extraCurricular-breakTiming/:id/:programId',
    [
        userPolicyAuthentication([
            'schedule_management:extra_curricular_and_break:break:delete',
            'schedule_management:extra_curricular_and_break:extra_curricular:delete',
        ]),
    ],
    deleteExtraCurricularAndBreakTiming,
);

module.exports = route;
