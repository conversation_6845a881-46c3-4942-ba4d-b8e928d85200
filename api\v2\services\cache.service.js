const flatCache = require('flat-cache');
const path = require('path');
const { PUBLISHED } = require('../utility/constants');
const { logger } = require('../utility/util_keys');
const institutionCalendar = require('../components/institution-calendar/institution-calendar.model');
const cacheFolderPath = '../public/.cache';

const insCalenderV2CacheID = 'v2institutionCalendar';

const cacheDate = ({ cacheId, key, data }) => {
    const cache = flatCache.load(key, path.resolve(cacheFolderPath));
    cache.setKey(cacheId.toString(), JSON.stringify(data));
    cache.save(); // very important, if you don't save no changes will be persisted.
};

const getInstitutionCalendar = async () => {
    let cache = flatCache.load(insCalenderV2CacheID, path.resolve(cacheFolderPath));
    let cachedData = cache.getKey(insCalenderV2CacheID);
    if (!cachedData) {
        logger.info(
            'service --> cache.service --> getInstitutionCalendar --> Get Institution Calendar from DB',
        );
        const icData = await institutionCalendar
            .findOne(
                { isDeleted: false, status: PUBLISHED },
                {
                    _id: 1,
                    calendar_name: 1,
                    start_date: 1,
                    end_date: 1,
                },
            )
            .sort({ _id: -1 })
            .limit(1)
            .lean();
        cacheDate({
            cacheId: insCalenderV2CacheID,
            key: insCalenderV2CacheID,
            data: icData,
        });
        cache = flatCache.load(insCalenderV2CacheID, path.resolve(cacheFolderPath));
        cachedData = cache.getKey(insCalenderV2CacheID);
    } else {
        logger.info(
            'service --> cache.service --> getInstitutionCalendar --> Get cached Institution Calendar v2 cache',
        );
    }
    return cachedData ? JSON.parse(cachedData) : undefined;
};

module.exports = {
    getInstitutionCalendar,
};
