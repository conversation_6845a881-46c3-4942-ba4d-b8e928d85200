const Joi = require('joi');
const objectId = Joi.string().alphanum().length(24).required();
const optionalObjectId = Joi.string().alphanum().length(24).optional();
exports.attendanceAddRowValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
        body: Joi.object({
            lateConfig: Joi.object({
                lateType: Joi.string().optional(),
                range: Joi.array()
                    .items({
                        _id: optionalObjectId,
                        startRange: Joi.number().optional(),
                        endRange: Joi.number().optional(),
                        marks: Joi.number().optional(),
                        lateLabel: Joi.string().optional(),
                        noOfLate: Joi.number().optional(),
                        noOfAbsent: Joi.number().optional(),
                        short_code: Joi.string().optional(),
                    })
                    .optional(),
            }),
            attendanceMarkConfig: Joi.object({
                basedGrandMarks: Joi.boolean(),
                range: Joi.array()
                    .items({
                        _id: optionalObjectId,
                        startRange: Joi.number().optional(),
                        endRange: Joi.number().optional(),
                        marks: Joi.number().optional(),
                    })
                    .optional(),
                equalGrandMarks: Joi.boolean(),
            }),
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
        body: req.body,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
exports.getAttendanceConfigValidator = (req, res, next) => {
    const schema = Joi.object({
        headers: Joi.object({
            _institution_id: objectId,
        }).unknown(true),
    });
    const { error } = schema.validate({
        headers: req.headers,
    });
    if (error) {
        const errors = error.details.map((e) => e.message).join(',');
        return res.status(400).json(errors);
    }
    next();
};
