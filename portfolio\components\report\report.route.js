const router = require('express').Router();
const { validate } = require('../../common/middlewares/validation');
const catchAsync = require('../../../lib/utility/catch-async');
const ReportController = require('./report.controller');
const {
    generatePortfolioReportSchema,
    getPortfolioReportSchema,
    addExtraMarkSchema,
    updateMarkSettingsSchema,
    updateReportSettingsSchema,
    updateComponentWisePassSettingsSchema,
    getIndividualStudentReportSchema,
    getPortfolioComponentReportSchema,
} = require('./report.validation');

router.post(
    '/',
    validate(generatePortfolioReportSchema),
    catchAsync(ReportController.generatePortfolioReport),
);

router.get(
    '/',
    validate(getPortfolioReportSchema),
    catchAsync(ReportController.getPortfolioReport),
);

router.post('/extra-mark', validate(addExtraMarkSchema), catchAsync(ReportController.addExtraMark));

router.put(
    '/setting-mark',
    validate(updateMarkSettingsSchema),
    catchAsync(ReportController.updateMarkSettings),
);

router.put(
    '/setting',
    validate(updateReportSettingsSchema),
    catchAsync(ReportController.updateReportSettings),
);

router.put(
    '/component-pass-settings',
    validate(updateComponentWisePassSettingsSchema),
    catchAsync(ReportController.updateComponentWisePassSettings),
);

router.get(
    '/individual-student-report',
    validate(getIndividualStudentReportSchema),
    catchAsync(ReportController.getIndividualStudentReport),
);

router.get(
    '/component-report',
    validate(getPortfolioComponentReportSchema),
    catchAsync(ReportController.getPortfolioComponentReport),
);

module.exports = router;
