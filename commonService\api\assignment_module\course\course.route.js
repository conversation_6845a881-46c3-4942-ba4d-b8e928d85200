const router = require('express').Router();
const catchAsync = require('../../../utility/catch-async');
const {
    getCourses,
    getSubjects,
    getCloSlo,
    getCoursesForStaff,
    getTaxonomy,
} = require('./course.controller');
const {
    listCourseValidator,
    listSubjectsValidator,
    getCloSloValidator,
    getCoursesForStaffValidator,
    searchTaxonomyValidator,
} = require('./course.validator');
const { validate } = require('../../../../middleware/validation');
const {
    userPolicyAuthentication,
    defaultPolicy,
} = require('../../../../middleware/policy.middleware');

router.get(
    '/list-courses',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: listCourseValidator, property: 'query' }]),
    catchAsync(getCourses),
);
router.get(
    '/list-subjects',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: listSubjectsValidator, property: 'query' }]),
    catchAsync(getSubjects),
);
router.post(
    '/get-clo-slo',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    validate([{ schema: getCloSloValidator, property: 'body' }]),
    catchAsync(getCloSlo),
);
router.get(
    '/list-courses-for-staff',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getCoursesForStaffValidator, property: 'query' }]),
    catchAsync(getCoursesForStaff),
);

router.get(
    '/get-taxonomy',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: searchTaxonomyValidator, property: 'query' }]),
    catchAsync(getTaxonomy),
);

module.exports = router;
