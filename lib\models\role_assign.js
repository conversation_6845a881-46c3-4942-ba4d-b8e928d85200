let mongoose = require('mongoose');
let Schema = mongoose.Schema;
let constant = require('../utility/constants');

let role_assign = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        _user_id: {
            type: Schema.Types.ObjectId,
            ref: constant.USER,
        },
        user_name: {
            first: String,
            middle: String,
            last: String,
            family: String,
        },
        roles: [
            {
                _role_id: {
                    type: Schema.Types.ObjectId,
                    ref: constant.ROLE,
                },
                role_name: {
                    type: String,
                },
                program: [
                    {
                        _program_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.PROGRAM,
                        },
                        program_name: {
                            type: String,
                        },
                    },
                ],
                department: [
                    {
                        _department_id: {
                            type: Schema.Types.ObjectId,
                            ref: constant.DEPARTMENT,
                        },
                        department_title: {
                            type: String,
                        },
                    },
                ],
                isDefault: {
                    type: Boolean,
                    default: false,
                },
                isAdmin: {
                    type: Boolean,
                    default: false,
                },
            },
        ],
        report_to: {
            _user_id: {
                type: Schema.Types.ObjectId,
                ref: constant.USER,
            },
            name: {
                first: String,
                middle: String,
                last: String,
                family: String,
            },
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.ROLE_ASSIGN, role_assign);
