const student_leave_register = require('./student_leave_register_model');
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const common_fun = require('../utility/common_functions');
const student_leave_register_formate = require('./student_leave_register_formate');
const ObjectId = require('mongodb').ObjectID;
const program = require('../models/program');
const constant = require('../utility/constants');
const student_leave_register_doc = require('mongoose').model(constant.STUDENT_LEAVE_REGISTER);
const user = require('mongoose').model(constant.USER);
exports.insert = async (req, res) => {
    let doc = await base_control.insert(student_leave_register_doc, req.body);
    if (doc.status) {
        common_files.com_response(res, 201, true, "Student Leave Registered Successfully", doc.responses);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};
exports.getStudentData = async (req, res) => {
    let id = req.params.id;
    let query = { _id: id };
    let project = {};
    let doc = await base_control.get(student_leave_register_doc, query, project);
    if (doc.status) {
        common_files.com_response(res, 200, true, "Student Leave Register Data", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};
exports.notifyStudent = async (req, res) => {
    let id = req.body.id;
    let query = { _id: id };
    let project = {};
    let doc = await base_control.get(student_leave_register_doc, query, project);
    if (doc.status) {
        //Get Student Data to send email and sms
        let query_student = { _id: doc.data.student_id };
        let project_student = {};
        let doc_student = await base_control.get(user, query_student, project_student);
        
        if(doc_student.status){
            req.body.message = "Test Message";

            let messages = '<p>' + req.body.message + '</p>';
            if (req.body.notify_via.indexOf('email') != -1) {
                const mailOptions = {
                    to: doc_student.data.email,
                    subject: 'Digi Scheduler Student Leave Notify',
                    messages: messages,
                };
                await common_fun.sending_mail(mailOptions, (error) => {
                    if (error) {
                        console.log('err', error);
                    }
                    if (!error) {
                        console.log('mail sent : ' + stf_mail);
                    } else {
                        console.log('Mail Error : ', error);
                    }
                });
            }
            if (req.body.notify_via.indexOf('sms') != -1) {
                let sms_messages = 'DigiScheduler ' + req.body.message;
                function sentMessageCallback(error, response, body) {
                    if (!error && common_fun.checkSMSResponse(response, body)) {
                        //console.log('sms sent :' + student_do);
                    } else {
                        console.log('sms error : ', error);
                    }
                }
                common_fun.send_message(doc_student.data.mobile, sms_messages, sentMessageCallback);
            }
            if (req.body.notify_via.indexOf('digischeduler') != -1) {
                //Send Notification to DigiScheduler FCM
            }
            if (req.body.notify_via.indexOf('digiclass') != -1) {
                //Send Notification to DigiScheduler FCMF
            }
            common_files.com_response(res, 200, true, "Notify Student Successfully", doc_student.data);
        }
        else{
            common_files.com_response(res, 500, false, "Error", doc_student.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
}



