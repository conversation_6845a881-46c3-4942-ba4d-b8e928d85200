const userSchema = require('../models/user');
const studentGroupSchema = require('../models/student_group');
const programSchema = require('../models/digi_programs');
const institutionSchema = require('../models/institution');
const globalSettingSchema = require('../global_session_settings/global_session_settings_model');

const {
    getSignedURL,
    send_email,
    getFourDigitOTP,
    getAppCodeByDomain,
} = require('../utility/common_functions');
const { getPaginationValues } = require('../utility/pagination');
const { hashSync, compare } = require('bcryptjs');
const {
    OTPGreetings,
    userRegisterSubject,
    generateExpiryDate,
    userApplicationRejectorMessage,
    userApplicationApprovedMessage,
} = require('./userRegister.service');
const {
    APPROVED,
    STUDENTS,
    REJECT,
    COMPLETED,
    SUBMITTED,
    DONE,
    PENDING,
    INITIATED,
} = require('../utility/constants');
const {
    SERVICES: {
        DC_URL,
        REACT_APP_COUNTRY_CODE,
        REACT_APP_COUNTRY_CODE_LENGTH,
        REACT_APP_INSTITUTION_ID,
        SELF_REGISTRATION_PROGRAM,
    },
    logger,
} = require('../utility/util_keys');
const { convertToMongoObjectId } = require('../utility/common');
const { digiAuthService } = require('../../digi-auth');
const { generateAuthTokens } = require('../utility/token.util');
const { defaultPolicy } = require('../../middleware/policy.middleware');

const programDetails = async () => {
    const programName = await programSchema
        .find({ isDeleted: false }, { name: 1, 'term.term_name': 1, code: 1 })
        .lean();
    if (!programName) {
        return {
            statusCode: 404,
            message: 'NO_DATA_FOUND',
        };
    }
    return {
        statusCode: 200,
        message: 'DATA_RETRIEVED',
        data: programName,
    };
};

const verifyEmail = async ({ body }) => {
    try {
        const { email, forgotPassword } = body;
        const alteredEmail = email.toLowerCase();
        const userVerificationData = await userSchema
            .findOne(
                { email: alteredEmail, isDeleted: false },
                { password: 1, approvalStatus: 1, user_type: 1 },
            )
            .lean();
        const currentOTP = getFourDigitOTP();
        let userId = '';
        if (userVerificationData) {
            if (
                userVerificationData.user_type === STUDENTS
                    ? userVerificationData.approvalStatus === APPROVED
                    : true
            ) {
                return {
                    statusCode: 400,
                    message: 'YOUR_REGISTRATION_COMPLETED',
                    data: { key: 'Your Registration Completed, Do Login' },
                };
            }
            if (userVerificationData.password && forgotPassword != true) {
                return {
                    statusCode: 400,
                    message: 'FOUND_DUPLICATE_EMAIL',
                    data: {
                        key: 'Found duplicate Email',
                        userId: userVerificationData._id,
                        tokens: await generateAuthTokens({
                            userId: userVerificationData._id,
                            _id: userVerificationData._id,
                            userType: defaultPolicy.SIGNUP,
                            userRoleData: [],
                        }),
                    },
                };
            }
            const updateUser = await userSchema.updateOne(
                { email: alteredEmail },
                {
                    $set: {
                        otp: { no: currentOTP, expiry_date: generateExpiryDate() },
                        user_type: STUDENTS,
                    },
                },
            );
            if (updateUser.nModified === 0) {
                return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
            }
            userId = userVerificationData._id;
        }
        if (!userVerificationData) {
            const newUser = await userSchema.create({
                user_type: STUDENTS,
                email: alteredEmail,
                otp: { no: currentOTP, expiry_date: generateExpiryDate() },
                user_id: alteredEmail,
            });
            userId = newUser._id;
        }
        send_email(alteredEmail, userRegisterSubject, `${OTPGreetings}\n${currentOTP}`);
        const singUpToken = await generateAuthTokens({
            userId,
            _id: userId,
            userType: defaultPolicy.SIGNUP,
            userRoleData: [],
        });
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { userId, tokens: singUpToken },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const verifyUserOTP = async ({ body }) => {
    try {
        const { userId, otp } = body;
        const userData = await userSchema.findOne({ _id: userId }, { otp: 1 }).lean();
        if (!userData) {
            return {
                statusCode: 404,
                message: 'EMAIL_ID_NOT_FOUND',
            };
        }
        const { expiry_date, no } = userData.otp;
        // Check if the provided OTP matches the stored OTP
        if (parseInt(no) !== parseInt(otp)) {
            return {
                statusCode: 400,
                message: 'INVALID_OTP',
            };
        }
        // Check if the OTP is expired
        if (new Date(expiry_date) < new Date()) {
            return {
                statusCode: 400,
                message: 'OTP_EXPIRES',
                data: { key: 'OTP Expires' },
            };
        }
        // If OTP is valid and matches
        return {
            statusCode: 200,
            message: 'OTP_VERIFIED_SUCCESSFULLY',
            data: {
                key: 'OTP verified successfully',
                tokens: await generateAuthTokens({
                    userId,
                    _id: userId,
                    userType: defaultPolicy.SIGNUP,
                }),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userPasswordVerification = async ({ body }) => {
    try {
        const { userId, userPassword } = body;
        const userData = await userSchema.findOne({ _id: userId }, { password: 1 }).lean();
        if (!userData) {
            return { statusCode: 404, message: 'EMAIL_ID_NOT_FOUND' };
        }
        if (!(await compare(userPassword, userData.password))) {
            return {
                statusCode: 401,
                message: 'PASSWORD_NOT_MATCH',
            };
        }
        return {
            statusCode: 200,
            data: {
                tokens: await generateAuthTokens({
                    userId,
                    _id: userId,
                    userType: defaultPolicy.SIGNUP,
                }),
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getUserDetails = async ({ body = {}, headers = {} }) => {
    try {
        const { userId } = body;
        // const { _institution_id } = headers;

        const userData = await userSchema
            .findOne(
                { _id: userId },
                {
                    biometric_data: 1,
                    dob: 1,
                    email: 1,
                    gender: 1,
                    mobile: 1,
                    name: 1,
                    user_id: 1,
                    batch: 1,
                    program: 1,
                },
            )
            .populate({ path: 'program._program_id', select: { name: 1 } })
            .lean();
        if (!userData) {
            return { statusCode: 404, message: 'NOT_FOUND' };
        }
        userData.REACT_APP_COUNTRY_CODE = REACT_APP_COUNTRY_CODE;
        userData.REACT_APP_COUNTRY_CODE_LENGTH = REACT_APP_COUNTRY_CODE_LENGTH;

        // student facial
        const globalSetting = await globalSettingSchema
            .findOne(
                {},
                {
                    studentFacial: '$basicDetails.studentFacial',
                    selfRegistrationDocument: '$basicDetails.selfRegistrationDocument',
                },
            )
            .lean();
        userData.studentFacial =
            globalSetting?.studentFacial !== undefined ? globalSetting.studentFacial : true;
        userData.selfRegistrationDocument =
            globalSetting?.selfRegistrationDocument !== undefined
                ? globalSetting.selfRegistrationDocument
                : true;

        userData.SELF_REGISTRATION_PROGRAM = SELF_REGISTRATION_PROGRAM;
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: userData };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateUserDetails = async ({ body }) => {
    try {
        const {
            userId,
            face,
            document,
            gender,
            mobile,
            name,
            password: plainPassword,
            user_id,
            approvalStatus,
            program,
            term,
        } = body;
        const existingNumber = await userSchema
            .findOne(
                {
                    isDeleted: false,
                    $or: [{ mobile }, { user_id }],
                    _id: { $ne: userId },
                },
                { mobile: 1, user_id: 1 },
            )
            .lean();
        if (existingNumber) {
            if (parseInt(existingNumber.mobile) === parseInt(mobile)) {
                return {
                    statusCode: 400,
                    message: 'MOBILE_NUMBER_ALREADY_REGISTERED',
                    data: { errorKey: 'mobileNo' },
                };
            }
            if (existingNumber.user_id === user_id) {
                return {
                    statusCode: 400,
                    message: 'USER_ID_ALREADY_REGISTERED',
                    data: { errorKey: 'userId' },
                };
            }
        }
        const updateQuery = {
            $set: {
                gender,
                mobile,
                name,
                approvalStatus,
                password: hashSync(plainPassword, 10),
                user_id,
                status: SUBMITTED,
                program,
                batch: term,
            },
        };
        if (face !== 'null') {
            updateQuery.$set['biometric_data.face'] = face;
        }
        if (document !== 'null') {
            updateQuery.$set['biometric_data.userRegisterDocument'] = document;
        }
        const updateUser = await userSchema.updateOne({ _id: userId }, updateQuery);
        if (updateUser.nModified === 0) {
            return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: { key: 'Data retrieved' } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const approvalUserList = async ({ body }) => {
    try {
        const { status, userSearchKey = '' } = body;
        const query = {
            approvalStatus: status,
            isDeleted: false,
            ...(userSearchKey &&
                userSearchKey.trim !== '' && {
                    $or: [
                        { 'name.first': { $regex: userSearchKey, $options: 'i' } },
                        { 'name.last': { $regex: userSearchKey, $options: 'i' } },
                        { 'name.family': { $regex: userSearchKey, $options: 'i' } },
                        { 'name.middle': { $regex: userSearchKey, $options: 'i' } },
                        { user_id: { $regex: userSearchKey, $options: 'i' } },
                        { email: { $regex: userSearchKey, $options: 'i' } },
                        { gender: { $regex: userSearchKey, $options: 'i' } },
                    ],
                }),
        };

        const pagination = getPaginationValues(body);
        const totalDocument = await userSchema.countDocuments(query);
        const userData = await userSchema
            .find(query, {
                user_id: 1,
                email: 1,
                biometric_data: 1,
                gender: 1,
                mobile: 1,
                name: 1,
                approvalStatus: 1,
                batch: 1,
                program: 1,
            })
            .sort({ createdAt: -1 })
            .skip(pagination.skip)
            .limit(pagination.limit)
            .populate({ path: 'program._program_id', select: { name: 1 } })
            .lean();
        return {
            statusCode: 200,
            message: 'DATA_RETRIEVED',
            data: { users: userData, totalDocument },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const userRegistrationApproveReject = async ({ body, headers }) => {
    try {
        const { userId, approvalStatus: status, Reason } = body;
        const userData = await userSchema
            .findOne(
                { _id: userId },
                {
                    biometric_data: 1,
                    user_type: 1,
                    name: 1,
                    gender: 1,
                    password: 1,
                    email: 1,
                    user_id: 1,
                },
            )
            .lean();
        const alteredEmail = userData.email.toLowerCase();
        const query = { _id: userId };
        if (status === APPROVED) {
            const appCode = getAppCodeByDomain(headers);
            await digiAuthService
                .importUser({
                    users: [
                        {
                            _id: userId,
                            userType: userData.user_type.toUpperCase(),
                            user_id: userData.user_id,
                            email: alteredEmail,
                            name: userData.name,
                            gender: userData.gender,
                        },
                    ],
                    appCode,
                })
                .then(async () => {
                    await digiAuthService
                        .syncUser({
                            employeeOrAcademicId: userData.user_id,
                            facial: userData.biometric_data.face,
                            password: userData.password,
                            userId,
                            ...(userData?.biometric_data?.facialTrained && {
                                facialTrained: userData.biometric_data.facialTrained,
                            }),
                        })
                        .then(async () => {
                            if (!userData?.biometric_data?.facialTrained)
                                await digiAuthService.faceTrain({
                                    employeeOrAcademicId: userData.user_id,
                                });
                            return true;
                        })
                        .catch((err) => {
                            logger.error(err);
                        });
                })
                .catch((err) => {
                    logger.error(err);
                });

            const updateUser = await userSchema.updateOne(query, {
                $set: {
                    approvalStatus: APPROVED,
                    status: COMPLETED,
                    'verification.data': DONE,
                    'verification.face': true,
                    'verification.email': true,
                    'verification.mobile': true,
                },
            });
            if (updateUser.nModified === 0) {
                return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
            }
            send_email(alteredEmail, userRegisterSubject, userApplicationApprovedMessage);
        } else if (status === REJECT) {
            const updateUser = await userSchema.updateOne(query, {
                $set: { approvalStatus: REJECT },
            });
            if (updateUser.nModified === 0) {
                return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
            }
            send_email(
                alteredEmail,
                userRegisterSubject,
                userApplicationRejectorMessage + Reason + '\n\n' + DC_URL + '/auth/register',
            );
        }
        return { statusCode: 200, message: 'USER_PROFILE_UPDATED_SUCCESSFULLY' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const signedURLForBiometricData = async ({ body }) => {
    try {
        const { fileUrl } = body;
        const SignedURL = await getSignedURL(fileUrl);
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: SignedURL };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateUserDetailInApprovalFlow = async ({ body }) => {
    try {
        const { userId, name, gender, user_id, mobile, program, term } = body;
        const existingNumber = await userSchema
            .findOne(
                {
                    isDeleted: false,
                    $or: [{ mobile }, { user_id }],
                    _id: { $ne: userId },
                },
                { mobile: 1, user_id: 1 },
            )
            .lean();
        if (existingNumber) {
            if (parseInt(existingNumber.mobile) === parseInt(mobile)) {
                return {
                    statusCode: 400,
                    message: 'MOBILE_NUMBER_ALREADY_REGISTERED',
                    data: { errorKey: 'mobileNo' },
                };
            }
            if (existingNumber.user_id === user_id) {
                return {
                    statusCode: 400,
                    message: 'USER_ID_ALREADY_REGISTERED',
                    data: { errorKey: 'userId' },
                };
            }
        }
        const updateQuery = {
            $set: {
                ...(gender && { gender }),
                ...(mobile && { mobile }),
                ...(name && { name }),
                ...(user_id && { user_id }),
                ...(program && { program }),
                ...(term && { batch: term }),
            },
        };
        if (Object.keys(updateQuery.$set).length === 0) {
            return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        }
        const updateUser = await userSchema.updateOne({ _id: userId }, updateQuery);
        if (updateUser.nModified === 0) {
            return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        }
        return {
            statusCode: 200,
            message: 'UPDATED_SUCCESSFULLY',
            data: { key: 'Data retrieved' },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDocument = async ({ body }) => {
    try {
        const { userId, passKey } = body;
        const institutionData = await institutionSchema.findOne(
            {
                _id: convertToMongoObjectId(REACT_APP_INSTITUTION_ID),
            },
            { userRegistrationFacePassKey: 1 },
        );
        if (
            institutionData &&
            institutionData.userRegistrationFacePassKey &&
            institutionData.userRegistrationFacePassKey.toString() !== passKey.toString()
        ) {
            return { statusCode: 400, message: 'PASSWORD_NOT_MATCH' };
        }
        const userData = await userSchema
            .findOne(
                { _id: userId },
                {
                    biometric_data: 1,
                },
            )
            .lean();
        if (userData && userData.biometric_data) {
            const signedFaceURLs = await Promise.all(
                userData.biometric_data.face.map((faceUrlElement) => getSignedURL(faceUrlElement)),
            );
            const signedDocumentURL = userData.biometric_data.userRegisterDocument
                ? await getSignedURL(userData.biometric_data.userRegisterDocument)
                : '';
            return {
                statusCode: 200,
                message: 'DATA_RETRIEVED',
                data: { signedFaceURLs, signedDocumentURL },
            };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteUser = async ({ body }) => {
    try {
        const { userId } = body;
        const studentGroupStatus = await studentGroupSchema
            .findOne(
                {
                    'groups.students._student_id': convertToMongoObjectId(userId),
                },
                { _id: 1 },
            )
            .lean();
        if (studentGroupStatus) {
            return {
                statusCode: 410,
                message: 'FAILED_TO_UPDATE',
                data: { errorKey: 'studentMappedInCourse' },
            };
        }
        const userData = await userSchema.findOne({ _id: userId }, { user_id: 1 }).lean();
        digiAuthService
            .userRemove({ employeeOrAcademicId: userData.user_id })
            .then(() => {})
            .catch((err) => {
                logger.error(err);
                return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
            });
        const deleteUser = await userSchema.deleteOne({
            _id: userId,
            approvalStatus: { $in: [PENDING, APPROVED, REJECT, INITIATED] },
        });

        if (deleteUser.deletedCount === 0) {
            return { statusCode: 410, message: 'FAILED_TO_UPDATE' };
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
module.exports = {
    verifyEmail,
    verifyUserOTP,
    userPasswordVerification,
    getUserDetails,
    updateUserDetails,
    approvalUserList,
    userRegistrationApproveReject,
    signedURLForBiometricData,
    getDocument,
    deleteUser,
    programDetails,
    updateUserDetailInApprovalFlow,
};
