const express = require('express');
const router = express.Router();
const { validate } = require('../../middleware/validation');
const catchAsync = require('../utility/catch-async');
const {
    updateCourseWiseFacialSettings,
    getCourseWiseFacialData,
    getCourseWiseUserDetails,
} = require('./courseWiseSetting.controller');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');
const {
    getCourseWiseFacialDataValidator,
    updateCourseWiseFacialSettingValidator,
} = require('./courseWiseSetting.validator');

router.put(
    '/courseWise-setting',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: updateCourseWiseFacialSettingValidator, property: 'body' }]),
    catchAsync(updateCourseWiseFacialSettings),
);
router.get(
    '/getCourseWise-settings',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    validate([{ schema: getCourseWiseFacialDataValidator, property: 'query' }]),
    catchAsync(getCourseWiseFacialData),
);
router.get('/getCourseWise-userDetails', catchAsync(getCourseWiseUserDetails));
module.exports = router;
