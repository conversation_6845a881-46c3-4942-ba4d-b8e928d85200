const Role = require('./role.model');
const Institution = require('../../institution/institution.model');

const { UNIVERSITY } = require('../../../utility/enums');

const checkIfInstitutionExists = async ({ _institution_id = '' }) => {
    const institutionCheck = await Institution.findOne(
        { _id: _institution_id, isDeleted: false },
        { _id: 1 },
    );

    if (!institutionCheck) {
        return false;
    }
    return true;
};

const checkIfParentExists = async ({ _parent_id = '' }) => {
    const collegeCheck = await Institution.findOne(
        { _id: _parent_id, isDeleted: false, type: UNIVERSITY },
        { _id: 1 },
    );

    if (!collegeCheck) {
        return false;
    }

    return true;
};

const checkIfRoleNameExists = async ({ _institution_id = '', _parent_id = '', roleName = '' }) => {
    const regex = new RegExp(['^', roleName, '$'].join(''), 'i');
    const roleList = await Role.findOne({ name: regex }, { _id: 1 })
        .findAllRoles({ _institution_id, _parent_id })
        .lean();

    if (roleList.length) {
        return false;
    }

    return true;
};

module.exports = {
    checkIfInstitutionExists,
    checkIfParentExists,
    checkIfRoleNameExists,
};
