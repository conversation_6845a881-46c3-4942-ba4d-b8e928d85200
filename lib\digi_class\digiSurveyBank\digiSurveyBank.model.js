const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    USER,
    DIGI_SURVEY_BANK,
    DS_PROGRAM_KEY,
    DS_COURSE_KEY,
    DS_INSTITUTION_KEY,
    ACTIVE,
    INACTIVE,
    DRAFT,
    FIRST,
    LAST,
    RATING_SCALE_LABEL: { STANDARD, DYNAMIC },
} = require('../../utility/constants');

const ratingScaleSchema = new Schema({
    ratingScaleType: { type: String, enum: [STANDARD, DYNAMIC] },
    startRate: { type: Number },
    totalEndRate: { type: Number }, //its required in FE to known total option in dynamic rating scale.
    ratingValues: [
        {
            value: { type: Number },
            text: { type: String },
        },
    ],
});

const digiSurveyBankSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        isTemplate: { type: Boolean, default: false }, //if false its personal survey else its template survey
        surveyName: { type: String },
        surveyDescription: { type: String },
        createdBy: { type: ObjectId, ref: USER },
        surveyLevel: { type: String, enum: [DS_PROGRAM_KEY, DS_COURSE_KEY, DS_INSTITUTION_KEY] },
        surveyType: { type: String },
        learningOutcome: { type: Boolean, default: false },
        outcomeSectionPlacement: { type: String, enum: [FIRST, LAST] },
        ratingScaleConfiguration: {
            generalSectionSettings: ratingScaleSchema,
            outcomeSectionSettings: ratingScaleSchema,
        },
        manageQuestionTags: { type: Boolean, default: false },
        mapLearningOutcomes: { type: Boolean, default: false },
        status: { type: String, enum: [ACTIVE, INACTIVE, DRAFT], default: ACTIVE },
        questions: { type: Schema.Types.Mixed },
        cloneName: { type: String, default: '' },
        isEdited: { type: Boolean, default: false },
        isDeleted: { type: Boolean, default: false },
    },
    { timestamps: true },
);

module.exports = model(DIGI_SURVEY_BANK, digiSurveyBankSchema);
