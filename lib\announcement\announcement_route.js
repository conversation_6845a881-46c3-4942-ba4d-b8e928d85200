const router = require('express').Router();
const catchAsync = require('../utility/catch-async');
const {
    createAnnouncement,
    listManageAnnouncement,
    revokeStatus,
    uploadFile,
    listBoardAnnouncement,
    editAnnouncement,
    userFilterType,
    singleListAnnouncement,
    userViewedNotification,
    userViewedNotificationList,
    generateUrl,
    receiveNotificationCount,
    publishAnnouncement,
    announcementUserList,
    programList,
    singleProgramDetails,
    selectUserPrograms,
    getUserSelectedProgram,
    newCreateAnnouncement,
    singleCourseList,
    individualStudentList,
    individualStaffList,
    digiconnectSpecificUser,
    getUserSetting,
    getSingleAnnouncementDetails,
    newListManageAnnouncement,
    editNewAnnouncement,
    revokeAnnouncement,
    immediatePublish,
    deleteAnnouncement,
} = require('./announcement_controller');
const {
    listManageAnnouncementValidator,
    revokeStatusValidator,
    listBoardAnnouncementValidator,
    userFilterTypeValidation,
    singleListAnnouncementValidation,
    userViewedNotificationValidation,
    userViewedNotificationListValidation,
    receiveNotificationCountValidation,
} = require('./announcement_validator');
const { uploadDocument } = require('./announcement_service');
const { userPolicyAuthentication, defaultPolicy } = require('../../middleware/policy.middleware');

router.post('/createAnnouncement', catchAsync(createAnnouncement));
router.get(
    '/listManageAnnouncement',
    listManageAnnouncementValidator,
    catchAsync(listManageAnnouncement),
);
router.get(
    '/listBoardAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    listBoardAnnouncementValidator,
    catchAsync(listBoardAnnouncement),
);
router.put('/revokeStatus', revokeStatusValidator, catchAsync(revokeStatus));
router.post('/upload', uploadDocument, catchAsync(uploadFile));
router.put('/editAnnouncement', catchAsync(editAnnouncement));
router.get('/publishAnnouncement', catchAsync(publishAnnouncement));
router.get(
    '/userFilterType',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    userFilterTypeValidation,
    catchAsync(userFilterType),
);
router.get(
    '/singleListAnnouncement',
    singleListAnnouncementValidation,
    catchAsync(singleListAnnouncement),
);
router.put(
    '/userViewedNotification',
    userViewedNotificationValidation,
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(userViewedNotification),
);
router.get(
    '/userViewedNotificationList',
    userViewedNotificationListValidation,
    catchAsync(userViewedNotificationList),
);
router.get('/generateUrl', catchAsync(generateUrl));
router.get(
    '/receiveNotificationCount',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    receiveNotificationCountValidation,
    catchAsync(receiveNotificationCount),
);

//announcement phase two
router.get(
    '/announcementUserList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(announcementUserList),
);
router.get(
    '/programList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(programList),
);
router.get('/singleProgramDetails', catchAsync(singleProgramDetails));
router.post('/selectUserPrograms', catchAsync(selectUserPrograms));
router.get(
    '/getUserSelectedProgram',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getUserSelectedProgram),
);
router.post(
    '/newCreateAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(newCreateAnnouncement),
);
router.get(
    '/singleCourseList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(singleCourseList),
);
router.get(
    '/individualStudentList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(individualStudentList),
);
router.get(
    '/individualStaffList',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(individualStaffList),
);
router.get(
    '/digiconnectSpecificUser',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(digiconnectSpecificUser),
);
router.get(
    '/getUserSetting',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(getUserSetting),
);
router.get(
    '/getSingleAnnouncementDetails',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF, defaultPolicy.DC_STUDENT])],
    catchAsync(getSingleAnnouncementDetails),
);
router.get(
    '/newListManageAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(newListManageAnnouncement),
);
router.put(
    '/editNewAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(editNewAnnouncement),
);
router.put(
    '/revokeAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(revokeAnnouncement),
);
router.get(
    '/immediatePublish',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(immediatePublish),
);
router.delete(
    '/deleteAnnouncement',
    [userPolicyAuthentication([defaultPolicy.DC_STAFF])],
    catchAsync(deleteAnnouncement),
);
module.exports = router;
