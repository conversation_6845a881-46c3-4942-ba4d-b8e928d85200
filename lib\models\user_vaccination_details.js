const mongoose = require('mongoose');
const { Schema } = mongoose;
const {
    INSTITUTION,
    USER,
    USER_VACCINATION_DETAILS,
    VACCINATION,
    EVENT_WHOM: { STUDENT, STAFF, BOTH },
} = require('../utility/constants');

const user_vaccination_details_schema = new Schema(
    {
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: INSTITUTION,
            required: true,
        },
        _user_id: {
            type: Schema.Types.ObjectId,
            ref: USER,
            required: true,
        },
        user_type: {
            type: String,
            enum: [STUDENT, STAFF, BOTH],
        },
        vaccination_status: {
            type: Boolean,
            default: false,
        },
        vaccination_type_id: {
            type: Schema.Types.ObjectId,
            ref: VACCINATION,
        },
        vaccination_type: {
            type: String,
            trim: true,
        },
        dosage_taken: [
            {
                dosage_count: { type: Number },
                date: { type: Date },
            },
        ],
        immunity_period_status: {
            type: <PERSON>olean,
            default: false,
        },
        immunity_period_end_at: { type: Date },
        proof_for_verification: { type: String, trim: true },
        ug_documents: { type: String, trim: true },
        vaccination_documents: { type: String, trim: true },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = mongoose.model(USER_VACCINATION_DETAILS, user_vaccination_details_schema);
