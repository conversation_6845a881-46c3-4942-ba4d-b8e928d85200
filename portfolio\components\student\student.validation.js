const { Joi } = require('../../common/middlewares/validation');

// Common schema
const objectId = Joi.string().hex().length(24);

const getStudentCoursesSchema = Joi.object({
    query: Joi.object({
        institutionCalendarId: objectId.required(),
    }),
}).unknown(true);

const getStudentPortfolioSchema = Joi.object({
    query: Joi.object({
        programId: objectId.required(),
        courseId: objectId.required(),
        institutionCalendarId: objectId.required(),
    }),
}).unknown(true);

const updateStudentPortfolioSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
        componentId: objectId.required(),
    }),
    body: Joi.object({
        component: Joi.object().required(),
    }),
}).unknown(true);

const getGlobalRubricSchema = Joi.object({
    query: Joi.object({
        portfolioId: objectId.required(),
        componentId: objectId.required(),
        childId: objectId.required(),
        studentId: objectId.required(),
    }),
}).unknown(true);

module.exports = {
    getStudentCoursesSchema,
    getStudentPortfolioSchema,
    updateStudentPortfolioSchema,
    getGlobalRubricSchema,
};
