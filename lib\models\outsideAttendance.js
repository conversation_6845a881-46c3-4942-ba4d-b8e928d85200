const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;
const { OUTSIDE_ATTENDANCE } = require('../utility/constants');
const outsideAttendanceSchema = new Schema(
    {
        _institution_id: { type: ObjectId },
        scheduledId: { type: ObjectId },
        studentId: { type: ObjectId },
        imageUrl: { type: String },
        studentIds: [
            {
                type: ObjectId,
            },
        ],
        staffId: { type: ObjectId },
    },
    {
        timestamps: true,
    },
);
module.exports = mongoose.model(OUTSIDE_ATTENDANCE, outsideAttendanceSchema);
