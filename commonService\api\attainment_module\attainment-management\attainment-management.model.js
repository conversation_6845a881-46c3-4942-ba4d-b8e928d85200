const mongoose = require('mongoose');
const {
    ATTAINMENT_MANAGEMENT,
    RANGE,
    FIXED,
    EQUAL,
    GREATER_EQUAL,
} = require('../../../utility/constants');
const Schema = mongoose.Schema;
const ObjectId = mongoose.Types.ObjectId;

const nodes = new Schema({
    typeName: { type: String },
    typeId: { type: ObjectId },
    nodeName: { type: String },
    weightage: Number,
    isActive: { type: Boolean, default: true },
    subTree: [
        {
            typeName: { type: String },
            typeId: { type: ObjectId },
            nodeName: { type: String },
            weightage: Number,
            isActive: { type: Boolean, default: true },
            node: [
                {
                    typeName: { type: String },
                    typeId: { type: ObjectId },
                    nodeName: { type: String },
                    weightage: Number,
                    isActive: { type: Boolean, default: true },
                },
            ],
        },
    ],
});

const evaluationPlanSchema = new Schema({
    outComeType: { type: String },
    tree: [nodes],
});

const typeObjects = {
    typeName: { type: String },
    typeId: { type: ObjectId },
    nodeName: { type: String },
    nodeId: { type: String },
};

const typeNodes = new Schema({
    ...typeObjects,
    benchMark: { type: String },
    subTree: [
        {
            ...typeObjects,
            benchMark: { type: String },
            node: [
                {
                    ...typeObjects,
                    benchMark: { type: String },
                },
            ],
        },
    ],
});

const levelNodes = new Schema({
    ...typeObjects,
    subTree: [
        {
            ...typeObjects,
            node: [
                {
                    ...typeObjects,
                    nodeName: { type: String },
                },
            ],
        },
    ],
});

const levelValues = new Schema({
    level: { type: String },
    min: Number,
    max: Number,
    condition: { type: String, enum: [EQUAL, GREATER_EQUAL] },
    percentage: Number,
});

const levelSchema = new Schema({
    ...typeObjects,
    subTree: [
        {
            ...typeObjects,
            levelValues: [levelValues],
            node: [
                {
                    ...typeObjects,
                    levelValues: [levelValues],
                },
            ],
        },
    ],
    levelValues: [levelValues],
});

const levelColors = new Schema({
    level: { type: String },
    color: { type: String },
});

const attainmentLevelSchema = new Schema({
    outComeType: { type: String },
    start: Number,
    end: Number,
    valuesAs: { type: String, enum: [RANGE, FIXED] },
    nodes: [levelNodes],
    levels: [levelSchema],
    targetBenchMark: [typeNodes],
    manageTargetBenchMark: {
        valuesType: String,
        outComes: [
            {
                outComeId: ObjectId,
                values: Number,
            },
        ],
    },
    levelColors: [levelColors],
});

const assessmentManagementSchemas = new Schema(
    {
        isActive: {
            type: Boolean,
            default: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        _institution_id: {
            type: ObjectId,
        },
        _program_id: {
            type: ObjectId,
        },
        courseUpdate: {
            type: Boolean,
            default: false,
        },
        type: { type: String, enum: ['program', 'course'] },
        _institution_calendar_id: {
            type: ObjectId,
        },
        _course_id: {
            type: ObjectId,
        },
        levelNo: String,
        term: String,
        regulationYear: String,
        regulationName: String,
        curriculumName: String,
        _curriculum_id: { type: ObjectId },
        outcomes: [String],
        evaluationPlan: [evaluationPlanSchema],
        attainmentLevel: [attainmentLevelSchema],
        assessmentSelections: [
            {
                institutionCalendarId: { type: ObjectId },
                courseId: { type: ObjectId },
                assessmentIds: [{ type: ObjectId }],
            },
        ],
        programSelections: [
            {
                typeName: String,
                typeId: { type: ObjectId },
                coTreeId: { type: ObjectId },
                mode: { type: String, enum: ['co', 'assessment'] },
                assessmentIds: [{ type: ObjectId }],
            },
        ],
        programCourseList: [
            {
                // year: String,
                levelNo: String,
                term: String,
                courseId: ObjectId,
                status: Boolean,
            },
        ],
    },
    { timestamps: true },
);

module.exports = mongoose.model(ATTAINMENT_MANAGEMENT, assessmentManagementSchemas);
