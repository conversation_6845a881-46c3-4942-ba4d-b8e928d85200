const CourseService = require('./course.service');

const getListOfCoursesByUser = async ({
    headers: { user_id: userId },
    query: { type = 'student', institutionCalendarId },
}) => {
    const courseLists = await CourseService.getListOfCoursesByUser({
        userId,
        type,
        institutionCalendarId,
    });

    return { statusCode: 200, data: courseLists };
};

const getStudentGroupsByCourse = async ({
    query: {
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
        institutionId,
    } = {},
}) => {
    const studentGroups = await CourseService.getStudentGroupsByCourse({
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
        institutionId,
    });

    return { statusCode: 200, data: studentGroups };
};

const getFacultyListByCourse = async ({
    query: {
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
        institutionId,
    } = {},
}) => {
    const faculties = await CourseService.getFacultyListByCourse({
        programId,
        year,
        level,
        rotation,
        rotationCount,
        term,
        courseId,
        institutionCalendarId,
        institutionId,
    });

    return { statusCode: 200, data: faculties };
};

const getPrograms = async () => {
    const programs = await CourseService.getPrograms();

    return { statusCode: 200, data: programs };
};

const getCoursesByProgram = async ({ query: { programId } }) => {
    const courses = await CourseService.getCoursesByProgram({ programId });

    return { statusCode: 200, data: courses };
};

const getDeliveryTypesByCourse = async ({ query: { courseId } }) => {
    const deliveryTypes = await CourseService.getDeliveryTypesByCourse({ courseId });

    return { statusCode: 200, data: deliveryTypes };
};

const getPortfolio = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        rotation,
        rotationCount,
    },
}) => {
    const portfolio = await CourseService.getPortfolio({
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        rotation,
        rotationCount,
    });

    return { statusCode: 200, data: portfolio };
};

module.exports = {
    getListOfCoursesByUser,
    getStudentGroupsByCourse,
    getFacultyListByCourse,
    getPrograms,
    getCoursesByProgram,
    getDeliveryTypesByCourse,
    getPortfolio,
};
