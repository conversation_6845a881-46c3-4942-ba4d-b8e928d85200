const { Schema, model } = require('mongoose');

const { MEASURABLE_TYPE } = require('../../common/utils/constants');

const schema = new Schema(
    {
        name: { type: String, trim: true },
        code: { type: String, trim: true },
        isDeleted: { type: Boolean, default: false },
    },
    { timestamps: true },
);

schema.index({ code: 1, isDeleted: 1 }, { unique: true });

module.exports = model(MEASURABLE_TYPE, schema);
