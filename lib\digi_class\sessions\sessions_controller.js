const base_control = require('../../base/base_controller');
// const common_files = require('../../utility/common');
const moment = require('moment');
const CourseSchedule = require('../../models/course_schedule');
const ScheduleAttendanceModel = require('../../models/schedule_attendance');
//const course = require('mongoose').model(constant.COURSE);
const { v4: uuidv4 } = require('uuid');
const institution = require('../../models/institution');
const courseSchema = require('../../models/digi_course');
const user = require('../../models/user');
const push = require('../../utility/notification_push');
const {
    timestampNow,
    dateTimeLocalFormatter,
    capitalize,
    getSignedURL,
} = require('../../utility/common_functions');
const { PM } = require('../../utility/enums');
const {
    sendResponse,
    updateWithFilter,
    getSchedule,
    // zoomMeetingCreation,
    // zoomMeetingUpdate,
    zoomMeetingEnd,
    makeTitleAndMessage,
    endSession,
    getOutSideSignedUrl,
} = require('./session_service');
// const {
//     createZoomUser,
//     getZoomUserList,
//     getZoomMeetingParticipantsList,
//     createZoomMeeting,
// } = require('../../../service/zoom.service');

const {
    ONGOING,
    COMPLETED,
    PENDING,
    PRESENT,
    MISSED,
    // ABSENT,
    TIME_GROUP_BOOKING_TYPE: { REMOTE },
    SCHEDULE_TYPES: { EVENT, SUPPORT_SESSION },
    LEAVE_TYPE: { ONDUTY, LEAVE, PERMISSION },
    REMOTE_PLATFORM: { ZOOM, TEAMS },
    RUNNING,
    EXCLUDE,
    WEB,
    ATTENDANCE_DEFAULT_END_TIME,
    STUDENTS,
    REMOTE_PLATFORM,
} = require('../../utility/constants');
const {
    convertToMongoObjectId,
    sendResult,
    convertToUtcFormat,
    convertToUtcTimeFormat,
    clone,
    axiosCall,
    sendResponseWithRequest,
} = require('../../utility/common');
const { dsGetAllWithSortAsJSON, update } = require('../../base/base_controller');
const cs = (string) => string.toString();
const { get_list, update_condition } = base_control;
const getJSON = dsGetAllWithSortAsJSON;
const toObjectId = convertToMongoObjectId;
const ObjectId = convertToMongoObjectId;
// const { getSchedules } = require('../dashboard/dashboard_service');
const { getCourse /* getAllCourses */ } = require('../course_session/course_session_service');
const { /* TIMEZONE, */ logger, SERVICES } = require('../../utility/util_keys');
const {
    SERVICES: { LATE_CONFIGURATION, MULTI_SCHEDULE_START },
} = require('../../utility/util_keys');
const { activityCron } = require('../activities/activities_service');
const {
    sendDashboardNotification,
    // getNotifications,
} = require('../notification/notification_service');
const {
    getZoomUsersDuration,
    createMeeting,
    createTeamsMeeting,
    createStudentJoinUrl,
    endingZoomMeeting,
    getTeamsUsersDuration,
    scheduleRemoteSessionStart,
} = require('../document_manager/document_manager_service');
const course_schedule = require('../../models/course_schedule');
const disciplinaryRemarksSchema = require('../../disciplinary_remarks/disciplinaryRemarks.model');
const {
    getLateLabelForSchedule,
    getLateAutoAndManualRange,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    filterStudentsForCourseRestriction,
} = require('../../utility/utility.service');
const leavePermissionOnDuty = [ONDUTY, LEAVE, PERMISSION];
const outsideAttendanceSchema = require('../../models/outsideAttendance');
const updateMany = async (Model, cond, objects, filter) => {
    let response_obj = null;
    try {
        let doc;
        if (filter) doc = await Model.updateMany(cond, objects, filter);
        else doc = await Model.updateMany(cond, objects);
        if (!doc) {
            response_obj = {
                status: false,
                data: 'Updating error',
            };
        } else {
            if (doc.modifiedCount > 0) {
                response_obj = {
                    status: true,
                    data: 'Updating successfully',
                    responses: doc,
                };
            } else {
                response_obj = {
                    status: false,
                    data: 'Updating error check parsing data',
                };
            }
        }
    } catch (error) {
        let objects = error;
        if (error.name === 'MongoError' && error.code === 11000) {
            objects = {
                message: 'Duplicate value',
                field: error.keyValue,
            };
        } else {
            objects = 'Error catch : ' + error;
        }
        response_obj = {
            status: false,
            data: objects,
        };
    }
    return response_obj;
};

const isExpired = (schedule_date, end, expire = null) => {
    const { hour, minute, format } = end;
    let scheduleDay = new Date(schedule_date);
    const day = scheduleDay.getDate() < 10 ? '0' + scheduleDay.getDate() : scheduleDay.getDate();
    let month = scheduleDay.getMonth() + 1;
    month = month < 10 ? '0' + month : month;
    const year = scheduleDay.getFullYear();
    const scheduleDate = year + '/' + month + '/' + day + ' ' + hour + ':' + minute + ' ' + format;
    scheduleDay = new Date(scheduleDate).getTime();
    if (expire) {
        scheduleDay = new Date(new Date(scheduleDate).getTime() + expire * 60000).getTime();
    }
    const currentDateAndTime = dateTimeLocalFormatter(new Date());
    const today = new Date(currentDateAndTime.toUTCString()).getTime();
    return today > scheduleDay;
};

exports.getRunningSchedules = async (req) => {
    try {
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            'sessionDetail.startBy': ObjectId(req.body._staff_id),
            status: ONGOING,
            isDeleted: false,
            isActive: true,
        };
        const courseSchedules = await CourseSchedule.findOne(query, { _id: 1 }).lean();
        return courseSchedules;
    } catch (error) {
        throw new Error(error);
    }
};

exports.session_start = async (req, res) => {
    try {
        const { _staff_id, mode, isLive, faceAuthentication, manualType, scheduleStartFrom } =
            req.body;
        logger.info('sessionController -> sessionStart -> %s start', _staff_id);
        const runningSessions = await this.getRunningSchedules(req);
        if (runningSessions)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ALREADY_YOU_HAVE_RUNNING_SESSION'),
                null,
            );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
            isActive: true,
        };
        const courseScheduleResult = await getSchedule({ scheduleId: req.body._id });
        if (!courseScheduleResult)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        let courseSchedule = courseScheduleResult;
        const courseDoc = await courseSchema
            .findOne(
                { _id: courseSchedule._course_id },
                {
                    'course_assigned_details._program_id': 1,
                    'course_assigned_details.year': 1,
                    'course_assigned_details.level_no': 1,
                    'course_assigned_details.auto_end_attendance_in': 1,
                },
            )
            .lean();
        const auto_end_attendance_in =
            courseDoc?.course_assigned_details?.find(
                (assignElement) =>
                    assignElement?._program_id?.toString() ===
                        courseSchedule?._program_id?.toString() &&
                    assignElement?.year?.toString() === courseSchedule?.year_no?.toString() &&
                    assignElement?.level_no?.toString() === courseSchedule?.level_no?.toString(),
            )?.auto_end_attendance_in ?? ATTENDANCE_DEFAULT_END_TIME;
        // if (!isExpired(courseSchedule.schedule_date, courseSchedule.start))
        //     return sendResponse(res, 200, false, 'start session on correct scheduled time', null);
        const studentWithSchedule = [];
        const scheduleId = courseSchedule._id;
        const sessionId = courseSchedule.session._session_id;
        if (courseSchedule.merge_status) {
            const scheduleIds = courseSchedule.merge_with.map((mw) =>
                convertToMongoObjectId(mw.schedule_id),
            );
            scheduleIds.push(convertToMongoObjectId(req.body._id));
            query._id = { $in: scheduleIds };
        }

        const sessionDetail = courseSchedule.sessionDetail;
        if (sessionDetail && mode !== 'join') {
            if (sessionDetail.attendance_mode === COMPLETED)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('ATTENDANCE_ALREADY_CLOSED'),
                    null,
                );
            if (sessionDetail.attendance_mode === ONGOING)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('ATTENDANCE_ALREADY_STARTED'),
                    null,
                );
        }
        const getSocketEventName = () => {
            const { _id } = courseSchedule;
            if (courseSchedule.session.delivery_symbol) {
                const { delivery_symbol, delivery_no } = courseSchedule.session;
                return `${_id}-${delivery_symbol}-${delivery_no}`;
            }
            const delivery_symbol = 's';
            const delivery_no = '1';
            return `${_id}-${delivery_symbol}-${delivery_no}`;
        };
        const otherSchedules = (await getJSON(CourseSchedule, query, {})).data;
        const getStaffIds = () => {
            const mergedUserIds = [];
            otherSchedules.forEach((otherSchedule) => {
                const { students, _id, session } = otherSchedule;
                let sessionId;
                if (session) {
                    sessionId = session._session_id;
                }
                students.forEach((student) => {
                    if (!studentWithSchedule.find((sWS) => cs(sWS.userId) === cs(student._id)))
                        studentWithSchedule.push({
                            userId: student._id,
                            scheduleId: _id,
                            sessionId,
                            otherSchedule,
                        });
                });
                const studentIds = otherSchedule.students.map((student) => student._id);
                const staffIds = otherSchedule.staffs.map((staff) => staff._staff_id);
                mergedUserIds.push(studentIds.concat(staffIds));
            });
            // eslint-disable-next-line prefer-spread
            const users = [].concat.apply([], mergedUserIds);
            return [...new Set(users)];
        };
        /*  if (!courseSchedule.zoomUuid && courseSchedule.mode === REMOTE) {
            await zoomMeetingUpdate(
                req.body._staff_id,
                courseSchedule._id,
                courseSchedule.start,
                courseSchedule.end,
            );
        } */
        // const staffDetails = await user.findOne({ _id: req.body._staff_id }, {});
        const scheduleStudents = courseSchedule.students;
        if (mode !== 'join') {
            const socketEventName = getSocketEventName();
            courseSchedule.students = courseSchedule.students || [];
            let ids = getStaffIds();
            ids = ids.map((id) => id.toString());
            ids = [...new Set(ids)];
            ids = await filterStudentsForCourseRestriction({
                _institution_id: req.headers._institution_id,
                _institution_calendar_id: courseSchedule._institution_calendar_id,
                programId: courseSchedule._program_id,
                courseId: courseSchedule._course_id,
                yearNo: courseSchedule.year_no,
                levelNo: courseSchedule.level_no,
                term: courseSchedule.term,
                rotationCount: courseSchedule.rotation_count,
                userIds: ids,
            });
            const nonRestrictedUserIds = new Set(ids);
            ids = ids.map((id) => convertToMongoObjectId(id));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: ids } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    biometric_data: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    device_type: 1,
                    socketEventId: 1,
                },
            );
            if (!userResult.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('STUDENTS_NOT_FOUND'),
                    null,
                );
            const student_data = [];
            const staffs = [];
            const tokens = [];
            const session_uuid = uuidv4();
            const socketIds = [];
            userResult.data.forEach((element) => {
                let socketEventIds = element.socketEventId;
                if (socketEventIds) {
                    socketEventIds = Object.values(socketEventIds);
                    socketIds.push(socketEventIds);
                }
                if (element.user_type === 'student') {
                    const studentData = scheduleStudents.find(
                        (ele) => ele && ele._id.toString() === element._id.toString(),
                    );
                    student_data.push({
                        _student_id: element._id,
                        user_id: element.user_id,
                        student_name: element.name,
                        status: studentData ? studentData.status : 'pending',
                    });
                    const mergedSchedule = studentWithSchedule.find(
                        (sWS) => cs(sWS.userId) === cs(element._id),
                    );
                    const groups = mergedSchedule.student_groups;
                    let StudentGroup = '';
                    if (groups) {
                        groups.forEach((group) => {
                            if (group.students && group.students.length) {
                                const student = group.students.find(
                                    (student) =>
                                        student._student_id.toString() === element._id.toString(),
                                );
                                if (student) StudentGroup = group.group_name.slice(id.length - 5);
                            }
                        });
                    }
                    if (
                        studentData &&
                        studentData.status &&
                        !leavePermissionOnDuty.includes(studentData.status)
                    )
                        tokens.push({
                            device_type: element.device_type,
                            token: element.fcm_token,
                            web_fcm_token: element.web_fcm_token,
                            _user_id: element._id,
                            scheduleId: mergedSchedule ? mergedSchedule.scheduleId : scheduleId,
                            sessionId: mergedSchedule ? mergedSchedule.sessionId : sessionId,
                            schedule: mergedSchedule ? mergedSchedule.otherSchedule : undefined,
                            StudentGroup,
                        });
                } else {
                    if (element._id.toString() === req.body._staff_id.toString()) {
                        staffs.push({
                            _staff_id: element._id,
                            user_id: element.user_id,
                            staff_name: element.name,
                            status: 'present',
                            mode: 'auto',
                            time: timestampNow(),
                        });
                    } else {
                        staffs.push({
                            _staff_id: element._id,
                            user_id: element.user_id,
                            staff_name: element.name,
                            status: 'pending',
                        });
                    }
                }
            });
            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            for (const studentElement of scheduleStudents) {
                if (studentElement.status === 'present') present_count++;
                else if (studentElement.status === 'absent') absent_count++;
                else if (
                    studentElement.status === LEAVE ||
                    studentElement.status === PERMISSION ||
                    studentElement.status === ONDUTY
                )
                    leave_count++;
                if (!nonRestrictedUserIds.has(studentElement._id.toString())) {
                    studentElement.isRestricted = true;
                }
            }
            const objs = {
                $set: {
                    isLive: isLive || false,
                    status: ONGOING,
                    socket_port: socketEventName,
                    uuid: session_uuid,
                    'sessionDetail.attendance_mode': ONGOING,
                    'sessionDetail.retake': false,
                    'sessionDetail.start_time': timestampNow(),
                    'sessionDetail.startBy': convertToMongoObjectId(_staff_id),
                    faceAuthentication:
                        typeof faceAuthentication !== 'undefined' ? faceAuthentication : true,
                    ...(manualType && { manualType }),
                    students: scheduleStudents,
                    scheduleStartFrom,
                },
            };
            // Get Post to open in socket
            const doc = await updateMany(CourseSchedule, query, objs);
            await updateMany(
                CourseSchedule,
                {
                    _id: query._id,
                    'staffs._staff_id': convertToMongoObjectId(_staff_id),
                },
                { $set: { 'staffs.$.status': PRESENT } },
            );
            if (!doc.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_START_SESSION'),
                    null,
                );
            const students = student_data;
            courseSchedule = await getSchedule({ scheduleId: req.body._id });

            switch (courseSchedule.remotePlatform) {
                case ZOOM:
                    if (courseSchedule.mode === REMOTE && !courseSchedule.zoomDetail.zoomStartUrl) {
                        const generateUrl = await createMeeting(courseSchedule._id);
                        if (generateUrl.status && generateUrl.status === 1) {
                            courseSchedule.zoomDetail = { zoomStartUrl: generateUrl.start_url };
                        }
                    }
                    break;
                case TEAMS:
                    if (
                        courseSchedule.mode === REMOTE &&
                        !courseSchedule.teamsDetail.teamStartUrl
                    ) {
                        const generateUrl = await createTeamsMeeting(courseSchedule._id, _staff_id);
                        if (generateUrl.status && generateUrl.status === 1) {
                            courseSchedule.teamsDetail = { teamsStartUrl: generateUrl.start_url };
                        }
                    }
                    break;
                default:
            }
            const response = {
                _id: courseSchedule._id,
                status: courseSchedule.status,
                attendance_status: courseSchedule.sessionDetail
                    ? courseSchedule.sessionDetail.attendance_mode
                    : PENDING,
                count: students.length,
                present_count,
                absent_count,
                leave_count,
                students,
                staffs,
                session_uuid,
                remotePlatform: courseSchedule.remotePlatform,
                zoomDetail: {
                    zoomStartUrl:
                        courseSchedule.zoomDetail && courseSchedule.zoomDetail.zoomStartUrl
                            ? courseSchedule.zoomDetail.zoomStartUrl
                            : '',
                },
                teamsDetail: {
                    teamsStartUrl:
                        courseSchedule.teamsDetail && courseSchedule.teamsDetail.teamsStartUrl
                            ? courseSchedule.teamsDetail.teamsStartUrl
                            : '',
                },
                scheduleStartFrom,
                auto_end_attendance_in,
            };
            if (tokens.length) {
                const { title, message } = makeTitleAndMessage(courseSchedule);
                const data = {
                    _id: courseSchedule._id,
                    sessionId: courseSchedule.session._session_id,
                    courseId: courseSchedule._course_id,
                    programId: courseSchedule._program_id,
                    rotation: courseSchedule.rotation,
                    rotation_count: courseSchedule.rotation_count,
                    institutionCalendarId: courseSchedule._institution_calendar_id,
                    yearNo: courseSchedule.year_no,
                    levelNo: courseSchedule.level_no,
                    term: courseSchedule.term,
                    mergeStatus: courseSchedule.merge_status,
                    mergeType: courseSchedule.type,
                    clickAction: 'session_start',
                    notificationType: 'session',
                    scheduleStartFrom,
                };
                // fcm.firebase_push_notification(tokens, title, message, data)
                await push.notification_push(tokens, title, message, data);
            }

            if (tokens.length) {
                let userIds = tokens.map((token) => token._user_id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ notificationCount: 1, sessions: courseSchedule });
                for (const token of tokens) {
                    const eventId = token._user_id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            //Socket with Event Mode Push
            response.socket_port = socketEventName;
            logger.info(
                'sessionController -> sessionStart -> %s Session attendance started',
                _staff_id,
            );
            courseSchedule.auto_end_attendance_in = auto_end_attendance_in;
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_ATTENDANCE_STARTED'),
                courseSchedule,
            );
        }

        const session_time = moment(timestampNow());
        const start_time = moment(courseSchedule.session.start_time);
        const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
        const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
        const duration = moment.duration(session_times.diff(start_times));
        const min = parseInt(duration.asMinutes()) % 60;
        const hours = parseInt(duration.asHours());
        let late = false;
        if (hours !== 0 || min > 10) late = true;
        if (sessionDetail.attendance_mode === 'pending')
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_STARTED'),
                null,
            );
        const students = courseSchedule.students;
        const staffs = courseSchedule.staffs;
        const staffLoc = staffs.findIndex((i) => cs(i._staff_id) === cs(_staff_id));
        staffs[staffLoc].status = 'present';
        staffs[staffLoc].mode = 'auto';
        staffs[staffLoc].time = timestampNow();
        const objs = {
            $set: { staffs },
            scheduleStartFrom,
        };
        await updateMany(CourseSchedule, query, objs);
        let present_count = 0;
        let absent_count = 0;
        let leave_count = 0;
        students.forEach((element) => {
            if (element.status === 'present') present_count++;
            else if (element.status === 'absent') absent_count++;
            else if (
                element.status === LEAVE ||
                element.status === PERMISSION ||
                element.status === ONDUTY
            )
                leave_count++;
        });

        const response = {
            is_late: late,
            status: courseSchedule.status,
            remotePlatform: courseSchedule.remotePlatform,
            attendance_status: sessionDetail ? sessionDetail.attendance_mode : PENDING,
            count: students.length,
            present_count,
            absent_count,
            leave_count,
            students,
            staffs,
            socket_port: courseSchedule.socket_port,
            zoomDetail: {
                zoomStartUrl:
                    courseSchedule.zoomDetail && courseSchedule.zoomDetail.zoomStartUrl
                        ? courseSchedule.zoomDetail.zoomStartUrl
                        : '',
            },
            teamsDetail: {
                teamsStartUrl:
                    courseSchedule.teamsDetail && courseSchedule.teamsDetail.teamsStartUrl
                        ? courseSchedule.teamsDetail.teamsStartUrl
                        : '',
            },
            scheduleStartFrom,
            auto_end_attendance_in,
        };
        logger.info('sessionController -> sessionStart -> %s end', _staff_id);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'Joined session successfully',
            response,
        );
    } catch (error) {
        logger.error(
            'sessionController -> sessionStart -> %s error : %o',
            req.body._staff_id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.student_attendance = async (req, res) => {
    try {
        const { _student_id } = req.body;
        logger.info(
            'sessionController -> studentAttendance -> %s Session attendance - start',
            _student_id,
        );
        const query = {
            _institution_id: convertToMongoObjectId(req.headers._institution_id),
            _id: convertToMongoObjectId(req.body._id),
            isDeleted: false,
            isActive: true,
        };
        const courseScheduleResult = await getSchedule({ scheduleId: req.body._id });
        if (!courseScheduleResult)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const courseSchedule = courseScheduleResult;
        if (courseSchedule.merge_status) {
            const scheduleIds = courseSchedule.merge_with.map((mw) =>
                convertToMongoObjectId(mw.schedule_id),
            );
            scheduleIds.push(convertToMongoObjectId(req.body._id));
            query._id = { $in: scheduleIds };
        }
        const getStudent = courseSchedule.students.find(
            (student) => student._id.toString() === _student_id.toString(),
        );
        const { status, sessionDetail, staffs, students, outsideCampus } = courseScheduleResult;
        const isPresent = () => {
            return students.find(
                (student) => cs(student.status === PRESENT && student._id) === cs(_student_id),
            );
        };
        if (sessionDetail && sessionDetail.attendance_mode === COMPLETED)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ATTENDANCE_PROCESS_ALREADY_COMPLETED'),
                null,
            );
        if (courseSchedule.status === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_STARTED'),
                null,
            );
        if ((outsideCampus !== undefined && outsideCampus === false) || outsideCampus === undefined)
            if (!req.body.uuid || courseSchedule.uuid.toString() !== req.body.uuid.toString())
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('SECRET_CODE_(UUID)_IS_NOT_MATCHING'),
                    null,
                );

        let joinUrl = '';
        switch (courseSchedule.remotePlatform) {
            case TEAMS:
                joinUrl = courseSchedule.teamsDetail.teamsStartUrl;
                break;
            case ZOOM:
                if (!getStudent.join_url) {
                    const getJoinUrl = await createStudentJoinUrl(courseSchedule._id, _student_id);
                    if (getJoinUrl.status === 1) {
                        joinUrl = getJoinUrl.join_url;
                    }
                } else {
                    joinUrl = getStudent.join_url;
                }
                break;
            default:
        }
        if (isPresent())
            return sendResponseWithRequest(req, res, 200, false, req.t('STUDENT_ALREADY_PRESENT'), {
                join_url: joinUrl,
                meetingId: courseSchedule.zoomDetail.zoomMeetingId,
                passCode: courseSchedule.zoomDetail.passCode,
                remotePlatform: courseSchedule.remotePlatform,
            });
        let objs = {};
        let lateTime = false;
        const isLateConfig = false;
        let lateReason;
        let lateConfigRange;
        if (LATE_CONFIGURATION === 'true') {
            const lateConfigurationData = await institution
                .findById(
                    {
                        _id: convertToMongoObjectId(req.headers._institution_id),
                    },
                    {
                        lateConfig: 1,
                    },
                )
                .lean();
            if (
                lateConfigurationData &&
                lateConfigurationData.lateConfig &&
                lateConfigurationData.lateConfig.lateAbsenceThreshold === true
            ) {
                const sessionTime = new Date(sessionDetail.start_time);
                const lateConfigTime = sessionTime.setMinutes(
                    sessionTime.getMinutes() + lateConfigurationData.lateConfig.minuteRange,
                );
                lateTime = lateConfigTime <= timestampNow();
                if (lateTime) lateReason = lateConfigurationData.lateConfig.label;
            }
            if (
                lateConfigurationData &&
                lateConfigurationData.lateConfig &&
                typeof lateConfigurationData.lateConfig.minuteRange === 'number'
            ) {
                lateConfigRange = lateConfigurationData.lateConfig.minuteRange;
            }
        }
        objs = {
            $set: {
                'students.$[i].status': lateTime === true ? 'absent' : 'present',
                'students.$[i].primaryStatus': lateTime === true ? 'absent' : 'present',
                'students.$[i].time': timestampNow(),
                'students.$[i].primaryTime': timestampNow(),
                'students.$[i].mode': 'auto',
                'students.$[i].join_url': joinUrl,
            },
        };
        if (lateTime) {
            objs.$set['students.$[i].reasonForLate'] = lateReason;
        }
        if (typeof lateConfigRange === 'number') objs.$set.lateConfigRange = lateConfigRange;
        const filter = {
            arrayFilters: [
                { 'sessionDetail.attendance_mode': ONGOING },
                { 'i._id': convertToMongoObjectId(_student_id) },
            ],
        };
        const doc = await updateMany(CourseSchedule, query, objs, filter);
        if (!doc.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_PERSISTED_ATTENDANCE'),
                null,
            );

        //send staff response socket
        // const cScheduleResult = await base_control.get(CourseSchedule, {
        //     _id: convertToMongoObjectId(req.body._id),
        //     isDeleted: false,
        // });

        const courseSchedules = (
            await getJSON(CourseSchedule, query, {
                'students._id': 1,
                'students.status': 1,
                sessionDetail: 1,
                socket_port: 1,
            })
        ).data;
        const mergedStudents = [];
        let sessionDetails;
        let socketPortId;
        courseSchedules.forEach((cSchedule) => {
            const { students, _id, sessionDetail, socket_port } = cSchedule;
            sessionDetails = sessionDetail;
            socketPortId = socket_port;
            mergedStudents.push(students);
        });
        // eslint-disable-next-line prefer-spread
        let concatMergedStudents = [].concat.apply([], mergedStudents);
        concatMergedStudents = concatMergedStudents.reduce((acc, current) => {
            const x = acc.find((item) => item._id.toString() === current._id.toString());
            if (!x) return acc.concat([current]);
            return acc;
        }, []);
        const sendSocketData = [];
        if (sessionDetails.attendance_mode === ONGOING) {
            let presentStudent = 0;
            concatMergedStudents.forEach((element) => {
                if (element.status === 'present') presentStudent++;
            });
            sendSocketData.push({ eventId: socketPortId, data: _student_id });
        }
        if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);

        // const courses = await getCourse(
        //     _student_id,
        //     courseSchedule._course_id,
        //     courseSchedule._institution_calendar_id,
        //     courseSchedule._program_id,
        //     courseSchedule.year_no,
        //     courseSchedule.level_no,
        //     courseSchedule.term,
        //     courseSchedule.rotation,
        //     courseSchedule.rotation_count,
        // );
        // let response = null;
        // if (courses.length) {
        // const course = courses[0];
        const response = {
            join_url: joinUrl,
            remotePlatform: courseSchedule.remotePlatform,
            meetingId:
                courseSchedule.remotePlatform === TEAMS
                    ? courseSchedule.teamsDetail.teamsMeetingId
                    : courseSchedule.zoomDetail.zoomMeetingId,
            passCode: courseSchedule.zoomDetail.passCode,
            isLateConfig,
            // warningCount: course.warningCount,
            // presentCount: course.presentCount,
            // totalSessions: course.totalSessions,
            // completedSessions: course.completedSessions,
        };
        // }
        if (lateTime) {
            response.isLateConfig = true;
            response.reasonOfLate = lateReason;
        }
        logger.info(
            'sessionController -> studentAttendance -> %s Session attendance marked - end',
            _student_id,
        );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SESSION_ATTENDANCE_PERSISTED_SUCCESSFULLY'),
            response,
        );
    } catch (error) {
        logger.error(
            'sessionController -> studentAttendance -> %s error : %o',
            req.body._student_id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_stop = async (req, res) => {
    try {
        const query = {
            _institution_id: convertToMongoObjectId(req.headers._institution_id),
            _id: convertToMongoObjectId(req.body._id),
            isDeleted: false,
            isActive: true,
        };
        logger.info(
            'sessionController -> SessionStop -> %s by %s - start',
            req.body._id,
            req.body._staff_id,
        );
        const data = await getSchedule({ scheduleId: req.body._id });
        if (!data)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        const { merge_status, merge_with, sessionDetail } = data;
        if (sessionDetail.attendance_mode === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_STARTED'),
                null,
            );
        if (sessionDetail.attendance_mode === COMPLETED)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_ATTENDANCE_ALREADY_CLOSED'),
                null,
            );

        if (!merge_status) {
            const { students, staffs } = data;
            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            students.forEach((element, index) => {
                if (element.status === 'present') present_count++;
                else if (element.status === 'absent' || element.status === 'pending')
                    absent_count++;
                else if (
                    element.status === LEAVE ||
                    element.status === PERMISSION ||
                    element.status === ONDUTY
                )
                    leave_count++;
                const status = element.status === 'pending' ? 'absent' : element.status;
                students[index].status = status;
                students[index].primaryStatus =
                    students[index].primaryStatus === undefined
                        ? status
                        : students[index].primaryStatus === 'pending' &&
                          !leavePermissionOnDuty.includes(status)
                        ? status
                        : students[index].primaryStatus;
            });
            staffs.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                staffs[index].status = status;
            });
            const objs = {
                $set: {
                    'sessionDetail.attendance_mode': COMPLETED,
                    'sessionDetail.stop_time': new Date(),
                    'sessionDetail.mode': req.body.mode,
                    students,
                    staffs,
                    status: ONGOING,
                },
            };
            const filter = { arrayFilters: [{ 'sessionDetail.attendance_mode': COMPLETED }] };
            const doc = await updateWithFilter(CourseSchedule, query, objs, filter);

            if (!doc.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_STOP_ATTENDANCE'),
                    null,
                );
            const count = students.length;
            const response = { count, present_count, absent_count, leave_count, students, staffs };
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_ATTENDANCE_STOPPED'),
                response,
            );
        }
        if (merge_status) {
            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            let count = 0;
            const allStudents = [];
            const allStaffs = [];
            const scheduleIds = merge_with.map((mw) => toObjectId(mw.schedule_id));
            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            const CourseSchedules = (await getJSON(CourseSchedule, query, {})).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            CourseSchedules.forEach((cSchedule) => {
                const { _id, staffs, students } = cSchedule;
                count += students.length;
                students.forEach((element, index) => {
                    if (element.status === 'present') present_count++;
                    else if (element.status === 'absent') absent_count++;
                    else if (
                        element.status === LEAVE ||
                        element.status === PERMISSION ||
                        element.status === ONDUTY
                    )
                        leave_count++;
                    allStudents.push(element);
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    students[index].status = status;
                    students[index].primaryStatus =
                        students[index].primaryStatus === undefined
                            ? status
                            : students[index].primaryStatus === 'pending' &&
                              !leavePermissionOnDuty.includes(status)
                            ? status
                            : students[index].primaryStatus;
                });
                staffs.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    staffs[index].status = status;
                    allStaffs.push(element);
                });
                bulk.find({ _id: toObjectId(_id) }).updateOne({
                    $set: {
                        'sessionDetail.attendance_mode': COMPLETED,
                        'sessionDetail.stop_time': new Date(),
                        'sessionDetail.mode': req.body.mode,
                        students,
                        staffs,
                        status: ONGOING,
                    },
                });
            });
            logger.info(
                'sessionController -> SessionStop -> %s by %s - end',
                req.body._id,
                req.body._staff_id,
            );

            const concatMergedStaffs = allStaffs.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);

            const concatMergedStudents = allStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);

            await bulk.execute((error) => {
                if (error)
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        false,
                        req.t('UNABLE_TO_STOP_ATTENDANCE'),
                        null,
                    );
            });
            const response = {
                count: concatMergedStudents.length,
                present_count,
                absent_count,
                leave_count,
            };
            response.staffs = concatMergedStaffs;
            response.students = concatMergedStudents;
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_ATTENDANCE_STOPPED'),
                response,
            );
        }
    } catch (error) {
        logger.error(
            'sessionController -> SessionStop -> %s by %s - error : %o',
            req.body._id,
            req.body._staff_id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_report = async (req, res) => {
    try {
        const query = {
            _id: convertToMongoObjectId(req.params.id),
            isDeleted: false,
            isActive: true,
        };
        logger.info('sessionController -> sessionReport -> %s - start', req.params.id);
        const tardisPop = { path: 'students.tardisId', select: { name: 1, short_code: 1 } };
        const courseSchedule = await base_control.get_populate(
            CourseSchedule,
            query,
            {},
            tardisPop,
        );
        if (!courseSchedule.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        if (courseSchedule.data.mode === REMOTE) {
            let scheduleIds = [courseSchedule.data._id];
            if (courseSchedule.data.merge_status) {
                const mergeScheduleIds = courseSchedule.data.merge_with.map((mw) =>
                    convertToMongoObjectId(mw.schedule_id),
                );
                scheduleIds = scheduleIds.concat(mergeScheduleIds);
            }
            switch (courseSchedule.data.remotePlatform) {
                case ZOOM:
                    await getZoomUsersDuration(scheduleIds);
                    break;
                case TEAMS:
                    await getTeamsUsersDuration(scheduleIds);
                    break;
                default:
            }
        }

        const courseScheduleResult = await base_control.get_populate(
            CourseSchedule,
            query,
            {},
            tardisPop,
        );
        if (!courseScheduleResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        const {
            _id,
            status,
            staffs,
            students,
            sessionDetail,
            merge_status,
            merge_with,
            zoomDetail,
            zoomDuration,
            remotePlatform,
            teamsDetail,
            teamsDuration,
        } = courseScheduleResult.data;
        if (sessionDetail) {
            const mergedStudents = [];
            if (merge_status) {
                const scheduleIds = merge_with.map((mw) => convertToMongoObjectId(mw.schedule_id));
                let mergedCourseSchedules = await CourseSchedule.find(
                    { _id: { $in: scheduleIds } },
                    {},
                ).populate(tardisPop);
                mergedCourseSchedules = mergedCourseSchedules.map(
                    (mergedCourseSchedule) => mergedCourseSchedule.students,
                );
                // eslint-disable-next-line prefer-spread
                mergedStudents.push([].concat.apply([], mergedCourseSchedules));
            }

            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            mergedStudents.push(students);
            // eslint-disable-next-line prefer-spread
            const cloneMergedStudents = [].concat.apply([], mergedStudents);

            // duplicate student removed
            const duplicateStudents = cloneMergedStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
            const users = [];
            duplicateStudents.forEach((element) => {
                users.push(convertToMongoObjectId(element._id));
            });
            staffs.forEach((element) => {
                users.push(convertToMongoObjectId(element._staff_id));
                element.status = element.status === PENDING ? 'absent' : element.status;
            });
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: users } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    isActive: 1,
                },
            );
            const usersDetails = userResult.status ? clone(userResult.data) : [];
            const staffDetails = staffs.map((staff) => {
                let userId;
                if (usersDetails.length) {
                    userId = usersDetails.find(
                        (usersDetail) => usersDetail._id.toString() === staff._staff_id.toString(),
                    );
                    if (userId) userId = userId.user_id;
                }
                return {
                    staff_name: staff.staff_name,
                    status: staff.status,
                    _id: staff._id,
                    _staff_id: staff._staff_id,
                    time: staff.time,
                    mode: staff.mode,
                    duration: staff.duration,
                    userId,
                };
            });
            const { lateDurationRange, manualLateRange, manualLateData } =
                await getLateAutoAndManualRange({
                    _institution_id: courseScheduleResult.data._institution_id,
                });
            const { lateExcludeManagement } = await checkExclude({
                _institution_id: courseScheduleResult?.data?._institution_id,
                _institution_calendar_id: courseScheduleResult?.data?._institution_calendar_id,
                courseId: courseScheduleResult?.data?._course_id,
                levelNo: courseScheduleResult?.data?.level_no,
                term: courseScheduleResult?.data?.term,
                rotationCount: courseScheduleResult?.data?.rotationCount,
            });
            const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: courseScheduleResult?.data?._institution_calendar_id,
                programId: courseScheduleResult?.data?._program_id,
                courseId: courseScheduleResult?.data?._course_id,
                levelNo: courseScheduleResult?.data?.level_no,
                term: courseScheduleResult?.data?.term,
                rotationCount: courseScheduleResult?.data?.rotationCount,
                lateExcludeManagement,
            });
            // fetch disciplinary remarks comments
            const disciplinaryRemarksData = await disciplinaryRemarksSchema
                .find(
                    {
                        scheduleId: convertToMongoObjectId(_id),
                        isDeleted: false,
                        isActive: true,
                    },
                    { studentId: 1, comment: 1, tardisId: 1 },
                )
                .populate({ path: 'tardisId', select: { name: 1, short_code: 1 } })
                .lean();
            const studentDetails = [];
            for (duplicateStudentElement of duplicateStudents) {
                const studentData = usersDetails.find(
                    (usersDetail) =>
                        usersDetail.isActive &&
                        usersDetail._id.toString() === duplicateStudentElement._id.toString() &&
                        duplicateStudentElement.status !== EXCLUDE,
                );
                if (studentData) {
                    const user = courseScheduleResult.data.students.find(
                        (studentElement) =>
                            studentElement._id.toString() ===
                            duplicateStudentElement._id.toString(),
                    );
                    const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id:
                            courseScheduleResult?.data?._institution_calendar_id,
                        programId: courseScheduleResult?.data?._program_id,
                        courseId: courseScheduleResult?.data?._course_id,
                        levelNo: courseScheduleResult?.data?.level_no,
                        term: courseScheduleResult?.data?.term,
                        rotationCount: courseScheduleResult?.data?.rotationCount,
                        lateExcludeManagement,
                        studentId: duplicateStudentElement._id,
                    }).lateExclude;
                    let lateLabel = null;
                    if (!lateExclude && !lateExcludeForStudent) {
                        const { lateLabel: retrievedLabel } = getLateLabelForSchedule({
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            schedule: courseScheduleResult.data,
                            student_data: user,
                            _institution_calendar_id:
                                courseScheduleResult?.data?._institution_calendar_id,
                            programId: courseScheduleResult?.data?._program_id,
                            courseId: courseScheduleResult?.data?._course_id,
                            levelNo: courseScheduleResult?.data?.level_no,
                            term: courseScheduleResult?.data?.term,
                            rotationCount: courseScheduleResult?.data?.rotationCount,
                            lateExcludeManagement,
                        });
                        lateLabel = retrievedLabel;
                    }
                    const matchedRemark = disciplinaryRemarksData.find(
                        (remarkElement) =>
                            remarkElement.studentId.toString() ===
                                duplicateStudentElement._id.toString() &&
                            remarkElement.tardisId?._id?.toString() ===
                                duplicateStudentElement?.tardisId?._id.toString(),
                    );
                    if (matchedRemark) {
                        duplicateStudentElement.comment = matchedRemark.comment;
                        duplicateStudentElement.remarkId = matchedRemark._id;
                        duplicateStudentElement.tardisId = { ...matchedRemark.tardisId };
                    }
                    studentDetails.push({
                        imageUrl: duplicateStudentElement.imageUrl,
                        name: duplicateStudentElement.name,
                        status: duplicateStudentElement.status,
                        tardisId: duplicateStudentElement.tardisId,
                        isRestricted: duplicateStudentElement?.isRestricted,
                        primaryStatus: duplicateStudentElement.primaryStatus,
                        _id: duplicateStudentElement._id,
                        feedBack: duplicateStudentElement.feedBack,
                        time: duplicateStudentElement.time,
                        mode: duplicateStudentElement.mode,
                        duration: duplicateStudentElement.duration,
                        percentage: duplicateStudentElement.percentage,
                        userId: studentData.user_id,
                        reasonForLate: duplicateStudentElement.reasonForLate
                            ? duplicateStudentElement.reasonForLate
                            : lateLabel
                            ? lateLabel.lateLabel
                            : null,
                        lateLabel: lateLabel ? lateLabel.lateLabel : null,
                    });

                    if (duplicateStudentElement.status === 'present') present_count++;
                    else if (
                        duplicateStudentElement.status === 'absent' ||
                        duplicateStudentElement.status === 'pending'
                    )
                        absent_count++;
                    else if (
                        duplicateStudentElement.status === LEAVE ||
                        duplicateStudentElement.status === PERMISSION ||
                        duplicateStudentElement.status === ONDUTY
                    )
                        leave_count++;
                    duplicateStudentElement.status =
                        duplicateStudentElement.status === PENDING
                            ? 'absent'
                            : duplicateStudentElement.status;
                }
            }
            const response = {
                status,
                count: studentDetails.length,
                present_count,
                absent_count,
                leave_count,
                students: studentDetails,
                staffs: staffDetails,
                sessionDetail,
                remotePlatform,
                zoomTotalDuration: zoomDetail.zoomTotalDuration ? zoomDetail.zoomTotalDuration : '',
                zoomDuration,
                teamsTotalDuration: teamsDetail.teamsTotalDuration
                    ? teamsDetail.teamsTotalDuration
                    : '',
                teamsDuration,
            };
            let total = 0;
            let count = 0;
            for (const feedBackData of students) {
                if (feedBackData.feedBack && feedBackData.feedBack.rating) {
                    total += parseInt(feedBackData.feedBack.rating);
                    count++;
                }
            }
            if (count !== 0) {
                response.feedBacks = {
                    _session_id: _id,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (total / count).toFixed(1) : 0,
                };
            }
            logger.info('sessionController -> sessionReport -> %s - end', req.params.id);
            return sendResponseWithRequest(req, res, 200, true, req.t('SESSION_REPORT'), response);
        }
        logger.info('sessionController -> sessionReport -> %s - end', req.params.id);
        return sendResponseWithRequest(req, res, 200, false, req.t('SESSION_REPORT_PENDING'), null);
    } catch (error) {
        logger.error(
            'sessionController -> sessionStart -> %s error : %o',
            req.params.id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_status = async (req, res) => {
    try {
        logger.info('sessionController -> sessionStatus -> %s - start', req.params.id);
        const aggregate = [
            {
                $match: {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _id: ObjectId(req.params.id),
                    isDeleted: false,
                    isActive: true,
                },
            },
            // {
            //     $lookup: {
            //         from: 'courses',
            //         localField: '_course_id',
            //         foreignField: '_id',
            //         as: 'courses',
            //     },
            // },
            // { $unwind: { path: '$courses', preserveNullAndEmptyArrays: true } },
            // {
            //     $lookup: {
            //         from: 'users',
            //         localField: 'staffs._staff_id',
            //         foreignField: '_id',
            //         as: 'staff',
            //     },
            // },
            // { $unwind: { path: '$staff', preserveNullAndEmptyArrays: true } },
            // {
            //     $lookup: {
            //         from: 'programs',
            //         localField: '_program_id',
            //         foreignField: '_id',
            //         as: 'programs',
            //     },
            // },
            // { $unwind: { path: '$programs', preserveNullAndEmptyArrays: true } },
            { $addFields: { student_count: { $size: '$students' } } },
            { $addFields: { document_count: 8, activity_count: 3 } },
            // { $addFields: { attendance_status: '$sessionDetail.attendance_mode' } },
            {
                $project: {
                    students: 1,
                    _staff_id: 1,
                    year: 1,
                    level: 1,
                    delivery: 1,
                    delivery_type: 1,
                    delivery_mode: 1,
                    delivery_symbol: 1,
                    session_flow_no: 1,
                    topic: 1,
                    infra: 1,
                    session_date: 1,
                    start_time: 1,
                    end_time: 1,
                    uuid: 1,
                    session: 1,
                    'courses.courses_name': 1,
                    'courses.courses_number': 1,
                    // 'staff.name': 1,
                    // 'staff._id': 1,
                    // 'staff.role': 1,
                    // attendance_status: 1,
                    student_count: 1,
                    'programs.name': 1,
                    document_count: 1,
                    activity_count: 1,
                    status: 1,
                    socket_port: 1,
                    retake: 1,
                    merge_status: 1,
                    merge_with: 1,
                    sessionDetail: 1,
                    staffs: 1,
                    course_name: 1,
                    course_code: 1,
                    year_no: 1,
                    level_no: 1,
                    program_name: 1,
                    scheduleStartDateAndTime: 1,
                    scheduleEndDateAndTime: 1,
                },
            },
            // { $sort: { start_time: 1 } },
        ];
        const courseScheduleResult = await base_control.get_aggregate(CourseSchedule, aggregate);
        if (!courseScheduleResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        let sessionStarted = true;
        if (req.query.userId) {
            const courseSchedulesStaffQuery = {
                'staffs._staff_id': convertToMongoObjectId(req.query.userId),
                isDeleted: false,
                isActive: true,
                status: { $in: [ONGOING] },
            };
            const courseSchedulesStaff = await CourseSchedule.find(courseSchedulesStaffQuery);

            if (courseSchedulesStaff && courseSchedulesStaff.length > 0) {
                sessionStarted = false;
            }
        }
        let students = [];
        let present_count = 0;
        let absent_count = 0;
        let leave_count = 0;
        let status = PENDING;
        const courseSchedule = courseScheduleResult.data[0] || null;
        if (courseSchedule) {
            students = courseSchedule.students;
            if (courseSchedule.merge_status) {
                const scheduleIds = courseSchedule.merge_with.map((mw) =>
                    toObjectId(mw.schedule_id),
                );
                scheduleIds.push(toObjectId(req.params.id));
                csQuery = {
                    _institution_id: ObjectId(req.headers._institution_id),
                    _id: ObjectId(req.params.id),
                    isDeleted: false,
                    isActive: true,
                };
                csQuery._id = { $in: scheduleIds };
                const mergedCourseSchedules = (await getJSON(CourseSchedule, csQuery, {})).data;
                mergedCourseSchedules
                    .map((mCS) => mCS.students)
                    .flat()
                    .forEach((student) => {
                        if (!students.find((std) => cs(std._id) === cs(student._id))) {
                            students.push(student);
                        }
                    });
            }
        }
        if (courseSchedule.sessionDetail) {
            status = courseSchedule.sessionDetail.attendance_mode;
        }
        students.forEach((element) => {
            if (element.status === 'present') present_count++;
            else if (element.status === 'absent') absent_count++;
            else if (
                element.status === LEAVE ||
                element.status === PERMISSION ||
                element.status === ONDUTY
            )
                leave_count++;
        });
        courseSchedule.count = students.length;
        courseSchedule.present_count = present_count;
        courseSchedule.absent_count = absent_count;
        courseSchedule.leave_count = leave_count;
        courseSchedule.attendance_status = [];
        let total = 0;
        let count = 0;
        for (const feedBackData of courseSchedule.students) {
            const existStaff = courseSchedule.staffs.find(
                (staff) => staff._staff_id.toString() === req.query.userId.toString(),
            );
            if (!(existStaff && existStaff.status === 'absent')) {
                if (feedBackData.feedBack && feedBackData.feedBack.rating) {
                    total += parseInt(feedBackData.feedBack.rating);
                    count++;
                }
            }
        }
        if (count !== 0) {
            courseSchedule.feedBacks = {
                _session_id: courseSchedule._id,
                totalFeedback: count,
                avgRating: count !== 0 ? (total / count).toFixed(1) : 0,
            };
        }
        courseSchedule.sessionStarted = sessionStarted;

        // if (req.body._staff_id) {
        //     const staffDetails = await user.findOne({ _id: req.body._staff_id }, {});
        //     if (staffDetails.zoomDetails.meetingUuid) {
        //         courseSchedule.meetingDatas = {
        //             'zoomDetails.startUrl': staffDetails.zoomDetails.startUrl,
        //             'zoomDetails.joinUrl': staffDetails.zoomDetails.joinUrl,
        //             'zoomDetails.meetingId': staffDetails.zoomDetails.meetingId,
        //             'zoomDetails.meetingUuid': staffDetails.zoomDetails.meetingUuid,
        //         };
        //     }
        // }

        // ! Staff Multi Schedule
        if (MULTI_SCHEDULE_START === 'true') {
            const multiScheduleList = await CourseSchedule.findOne(
                {
                    _id: { $ne: convertToMongoObjectId(req.params.id) },
                    isDeleted: false,
                    isActive: true,
                    merge_status: false,
                    scheduleStartDateAndTime: courseSchedule.scheduleStartDateAndTime,
                    scheduleEndDateAndTime: courseSchedule.scheduleEndDateAndTime,
                    'staffs._staff_id': convertToMongoObjectId(req.query?.userId),
                },
                { _id: 1 },
            ).lean();
            courseSchedule.multiSchedule = !!multiScheduleList;
        }

        if (status != null) courseSchedule.attendance_status = [status];
        logger.info('sessionController -> sessionStatus -> %s - end', req.params.id);
        return sendResponseWithRequest(req, res, 200, true, req.t('SESSION_GET'), courseSchedule);
    } catch (error) {
        logger.error(
            'sessionController -> sessionStatus -> %s error : %o',
            req.params.id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_change_attendance = async (req, res) => {
    try {
        const {
            _id,
            _student_ids,
            studentsTardisUpdate,
            _staff_ids,
            scheduleAttendanceIds,
            attendanceCondition,
        } = req.body;
        const { _institution_id } = req.headers;
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _id: convertToMongoObjectId(_id),
            isDeleted: false,
            isActive: true,
        };
        logger.info('sessionController -> sessionAttendanceChange -> %s - start', _id);
        const courseScheduleResult = await base_control.get(CourseSchedule, query, {});
        if (!courseScheduleResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        if (courseScheduleResult.data.status === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_STARTED'),
                null,
            );
        if (courseScheduleResult.data.sessionDetail.attendance_mode === ONGOING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ATTENDANCE_NOT_CLOSED'),
                null,
            );
        const userIds = [];
        const checkSAttendance = await ScheduleAttendanceModel.find(
            {
                scheduleId: { $in: [convertToMongoObjectId(_id)] },
            },
            { _id: 1 },
        );
        if (
            /* scheduleAttendanceIds && scheduleAttendanceIds.length &&  */ checkSAttendance.length &&
            (scheduleAttendanceIds || attendanceCondition)
        ) {
            const comparedIds = await ScheduleAttendanceModel.updateMany(
                {
                    scheduleId: { $in: [convertToMongoObjectId(_id)] },
                },
                {
                    $set: {
                        ...(scheduleAttendanceIds &&
                            scheduleAttendanceIds.length && { isCompared: scheduleAttendanceIds }),
                        ...(attendanceCondition && { attendanceCondition }),
                    },
                },
                { multi: true },
            );
        }
        if (!courseScheduleResult.data.merge_status) {
            const { sessionDetail, students, staffs, start, mode } = courseScheduleResult.data;
            _student_ids.forEach((element) => {
                const stdIndex = students.findIndex((i) => i._id.toString() === element.toString());
                if (stdIndex >= 0) {
                    userIds.push(convertToMongoObjectId(element));
                    students[stdIndex].status =
                        students[stdIndex].status === 'present' ? 'absent' : 'present';
                    students[stdIndex].mode = 'manual';
                    if (mode !== REMOTE) {
                        students[stdIndex].time = timestampNow();
                    }
                }
            });
            if (studentsTardisUpdate && studentsTardisUpdate.length) {
                studentsTardisUpdate.forEach((tardisElement) => {
                    const stdIndex = students.findIndex(
                        (studentElement) =>
                            studentElement._id.toString() === tardisElement.studentId.toString(),
                    );
                    if (stdIndex >= 0) {
                        userIds.push(convertToMongoObjectId(tardisElement.studentId));
                        students[stdIndex].tardisId = tardisElement.tardisId;
                        if (typeof tardisElement.lateExclude === 'boolean') {
                            students[stdIndex].lateExclude = tardisElement.lateExclude;
                        }
                    }
                });
            }
            _staff_ids.forEach((element) => {
                const staffIndex = staffs.findIndex(
                    (i) => i._staff_id.toString() === element.toString(),
                );
                if (staffIndex >= 0) {
                    userIds.push(convertToMongoObjectId(element));
                    staffs[staffIndex].status =
                        staffs[staffIndex].status === 'present' ? 'absent' : 'present';
                    staffs[staffIndex].time = timestampNow();
                    staffs[staffIndex].mode = 'manual';
                }
            });
            const objs = { $set: { students, staffs } };
            const scheduleStartTime = start.hour + ':' + start.minute + ' ' + start.format;
            const session_time = moment(scheduleStartTime, ['h:mm A']);
            const start_time = moment(sessionDetail.start_time);
            const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
            const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
            const duration = moment.duration(start_times.diff(session_times));
            const min = parseInt(duration.asMinutes()) % 60;
            const hours = parseInt(duration.asHours());
            const late = hours > 0 || min > 10;
            const data = { is_late: late };
            const doc = await updateMany(CourseSchedule, query, objs);
            const courseSchedule = (await base_control.get(CourseSchedule, query, {})).data;
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    biometric_data: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    device_type: 1,
                    socketEventId: 1,
                },
            );
            const socketIds = [];
            const users = userResult.data;
            if (userResult.status && users.length) {
                users.forEach((element) => {
                    let socketEventIds = element.socketEventId;
                    if (socketEventIds) {
                        socketEventIds = Object.values(socketEventIds);
                        socketIds.push(socketEventIds);
                    }
                });
            }

            if (userResult.status && users.length) {
                let userIds = users.map((token) => token._id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ sessions: courseSchedule });
                for (const user of users) {
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> sessionAttendanceChange -> %s - end', _id);
            if (!doc.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_CHANGE_ATTENDANCE'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
                data,
            );
        }
        if (courseScheduleResult.data.merge_status) {
            const scheduleIds = courseScheduleResult.data.merge_with.map((mw) =>
                toObjectId(mw.schedule_id),
            );
            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            let data;
            const mCourseSchedules = (await getJSON(CourseSchedule, query, {})).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            let mergedStudents = [];
            let mergedStaffs = [];
            mCourseSchedules.forEach((mCSchedule) => {
                const { sessionDetail, students, staffs, start, _id, mode } = mCSchedule;
                _student_ids.forEach((element) => {
                    userIds.push(convertToMongoObjectId(element));
                    const stdIndex = students.findIndex((i) => cs(i._id) === cs(element));
                    if (stdIndex >= 0) {
                        students[stdIndex].status =
                            students[stdIndex].status === 'present' ? 'absent' : 'present';
                        students[stdIndex].mode = 'manual';
                        if (mode !== REMOTE) {
                            students[stdIndex].time = timestampNow();
                        }
                    }
                });
                if (studentsTardisUpdate && studentsTardisUpdate.length) {
                    studentsTardisUpdate.forEach((tardisElement) => {
                        userIds.push(convertToMongoObjectId(tardisElement.studentId));
                        const stdIndex = students.findIndex(
                            (studentElement) =>
                                cs(studentElement._id) === cs(tardisElement.studentId),
                        );
                        if (stdIndex >= 0) {
                            students[stdIndex].tardisId = tardisElement.tardisId;
                            if (typeof tardisElement.lateExclude === 'boolean') {
                                students[stdIndex].lateExclude = tardisElement.lateExclude;
                            }
                        }
                    });
                }
                mergedStudents = [...mergedStudents, ...students];
                _staff_ids.forEach((element) => {
                    userIds.push(convertToMongoObjectId(element));
                    const staffIndex = staffs.findIndex(
                        (i) => i._staff_id.toString() === element.toString(),
                    );
                    if (staffIndex >= 0) {
                        staffs[staffIndex].status =
                            staffs[staffIndex].status === 'present' ? 'absent' : 'present';
                        staffs[staffIndex].time = timestampNow();
                        staffs[staffIndex].mode = 'manual';
                    }
                });
                mergedStaffs = [...mergedStaffs, ...staffs];
                const objs = { $set: { students, staffs } };
                const scheduleStartTime = start.hour + ':' + start.minute + ' ' + start.format;
                const session_time = moment(scheduleStartTime, ['h:mm A']);
                const start_time = moment(sessionDetail.start_time);
                const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
                const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
                const duration = moment.duration(start_times.diff(session_times));
                const min = parseInt(duration.asMinutes()) % 60;
                const hours = parseInt(duration.asHours());
                const late = hours > 0 || min > 10;
                data = { is_late: late };
                bulk.find({ _id: toObjectId(_id) }).updateOne(objs);
            });

            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    biometric_data: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    device_type: 1,
                    socketEventId: 1,
                },
            );
            const users = userResult.status ? userResult.data : [];
            const courseSchedule = (await base_control.get(CourseSchedule, query, {})).data;
            mergedStudents = mergedStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            mergedStaffs = mergedStaffs.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            courseSchedule.students = mergedStudents;
            courseSchedule.staffs = mergedStaffs;
            if (users.length) {
                let userIds = users.map((token) => token._id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ sessions: courseSchedule });
                for (const user of users) {
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> sessionAttendanceChange -> %s - end', _id);
            let bulkError;
            bulk.execute((error) => {
                bulkError = error;
            });
            if (bulkError)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_CHANGE_ATTENDANCE'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
                data,
            );
        }
    } catch (error) {
        logger.error(
            'sessionController -> sessionAttendanceChange -> %s error : %o',
            req.body._id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_close = async (req, res) => {
    try {
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isActive: true,
            isDeleted: false,
        };
        logger.info('sessionController -> sessionClose -> %s - start', req.body._id);
        let userIds = [];
        const courseScheduleResult = await base_control.get(CourseSchedule, query, {});

        if (!courseScheduleResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        const { merge_status, merge_with, mode, sessionDetail, status, _id, remotePlatform } =
            courseScheduleResult.data;

        if (mode === REMOTE && remotePlatform === REMOTE_PLATFORM.ZOOM) {
            let scheduleIds = [_id];
            if (merge_status) {
                const mergeScheduleIds = merge_with.map((mw) =>
                    convertToMongoObjectId(mw.schedule_id),
                );
                scheduleIds = scheduleIds.concat(mergeScheduleIds);
            }
            await endingZoomMeeting(scheduleIds);
            await zoomMeetingEnd(req.body._id);
        }
        if (
            mode !== REMOTE &&
            sessionDetail.attendance_mode === ONGOING &&
            courseScheduleResult.data.scheduleStartFrom != WEB
        )
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ATTENDANCE_IS_ONGOING_YOU_CANT_STOP'),
                null,
            );
        if (status === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_START'),
                null,
            );
        const socketIds = [];
        const session = courseScheduleResult.data;
        session.status = COMPLETED;
        if (!merge_status) {
            const { students, staffs } = courseScheduleResult.data;
            const presentStudents = students.filter((student) => student.status === 'present');
            students.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                students[index].status = status;
                students[index].primaryStatus =
                    students[index].primaryStatus === undefined
                        ? status
                        : students[index].primaryStatus === 'pending' &&
                          !leavePermissionOnDuty.includes(status)
                        ? status
                        : students[index].primaryStatus;
            });
            staffs.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                staffs[index].status = status;
            });
            const studentIds = presentStudents.map((student) => student._id.toString());
            const staffIds = staffs.map((staff) => staff._staff_id.toString());
            userIds = studentIds.concat(staffIds);
            const objs = {
                $set: {
                    'sessionDetail.attendance_mode': COMPLETED,
                    'sessionDetail.stop_time': timestampNow(),
                    students,
                    staffs,
                    status: COMPLETED,
                },
            };
            userIds = userIds.map((userId) => convertToMongoObjectId(userId));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    socketEventId: 1,
                },
            );
            const users = userResult.data;
            if (users.length) {
                const sendSocketData = [];
                for (const user of users) {
                    const data = JSON.stringify({ sessions: session });
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            const filter = { arrayFilters: [{ 'sessionDetails.attendance_mode': ONGOING }] };
            const doc = await updateWithFilter(CourseSchedule, query, objs, filter);
            logger.info('sessionController -> sessionClose -> %s - end', req.body._id);
            if (doc.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('SESSION_COMPLETED_SUCCESSFULLY'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_END_SESSION'),
                null,
            );
        }
        if (merge_status) {
            const scheduleIds = merge_with.map((mw) => toObjectId(mw.schedule_id));

            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            const CourseSchedules = (await getJSON(CourseSchedule, query, {})).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            CourseSchedules.forEach((cSchedule) => {
                const { _id, staffs, students } = cSchedule;
                const presentStudents = students.filter((student) => student.status === 'present');
                students.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    students[index].status = status;
                    students[index].primaryStatus =
                        students[index].primaryStatus === undefined
                            ? status
                            : students[index].primaryStatus === 'pending' &&
                              !leavePermissionOnDuty.includes(status)
                            ? status
                            : students[index].primaryStatus;
                });
                staffs.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    staffs[index].status = status;
                });
                const studentIds = presentStudents.map((student) => student._id.toString());
                const staffIds = staffs.map((staff) => staff._staff_id.toString());
                userIds.push(studentIds.concat(staffIds));
                bulk.find({ _id: toObjectId(_id) }).updateOne({
                    $set: {
                        'sessionDetail.attendance_mode': COMPLETED,
                        'sessionDetail.stop_time': timestampNow(),
                        students,
                        staffs,
                        status: COMPLETED,
                    },
                });
            });
            // eslint-disable-next-line prefer-spread
            userIds = [].concat.apply([], userIds);
            userIds = userIds.map((userId) => convertToMongoObjectId(userId));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    socketEventId: 1,
                },
            );
            const users = userResult.data;
            if (users.length) {
                const sendSocketData = [];
                for (const user of users) {
                    const data = JSON.stringify({ sessions: session });
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> sessionClose -> %s - end', req.body._id);
            bulk.execute((error) => {
                if (!error)
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('SESSION_COMPLETED_SUCCESSFULLY'),
                        null,
                    );
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_END_SESSION'),
                    null,
                );
            });
        }
    } catch (error) {
        logger.error(
            'sessionController -> sessionClose -> %s error : %o',
            req.body._id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_reset = async (req, res) => {
    try {
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params.id),
            isDeleted: false,
        };
        logger.info('sessionController -> sessionReset -> %s - start', req.params.id);
        const courseScheduleResult = await base_control.get(CourseSchedule, query, {});
        if (courseScheduleResult.data.merge_status) {
            const scheduleIds = courseScheduleResult.data.merge_with.map((mw) =>
                convertToMongoObjectId(mw.schedule_id),
            );
            scheduleIds.push(convertToMongoObjectId(req.params.id));
            query._id = { $in: scheduleIds };
        }
        if (!courseScheduleResult.status)
            return sendResult(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const objs = {
            $set: {
                status: PENDING,
                sessionDetail: {
                    retake: false,
                    attendance_mode: PENDING,
                },
                'staffs.$[].status': PENDING,
                'students.$[].status': PENDING,
                'students.$[].primaryStatus': PENDING,
                teamsDetail: {
                    meetingStatus: 'Not Started',
                    teamsRecord: false,
                },
            },
        };
        const courseScheduleUpdateResult = await updateMany(CourseSchedule, query, objs);
        const updateAlreadyRunningAttendance = await ScheduleAttendanceModel.updateMany(
            { scheduleId: { $in: [convertToMongoObjectId(req.params.id)] }, status: RUNNING },
            {
                $set: {
                    status: COMPLETED,
                },
            },
        ).lean();
        logger.info('sessionController -> sessionReset -> %s - end', req.params._id);
        if (courseScheduleUpdateResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_RESET_SUCCESSFULLY'),
                null,
            );
        return sendResponseWithRequest(
            req,
            res,
            200,
            false,
            req.t('UNABLE_TO_RESET_SESSION'),
            null,
        );
    } catch (error) {
        logger.error(
            'sessionController -> sessionReset -> %s error : %o',
            req.params._id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.student_session_status = async (req, res) => {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('INSTITUTION_NOT_FOUND'),
                null,
            );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params.id),
            isDeleted: false,
        };
        const project = {
            students: 1,
            _staff_id: 1,
            year: 1,
            level: 1,
            delivery: 1,
            delivery_type: 1,
            delivery_mode: 1,
            delivery_symbol: 1,
            session_flow_no: 1,
            topic: 1,
            infra: 1,
            session_date: 1,
            start_time: 1,
            end_time: 1,
            uuid: 1,
            status: 1,
            session: 1,
        };
        const session_check = await base_control.get(sessions, query, project);
        if (!session_check.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        let students = [];
        if (session_check.data.session.length) {
            students = session_check.data.session[0].attendees;
        }
        let status = 'pending';
        const loc = students.findIndex((i) => i._student_id.toString() === req.params.student);
        if (loc === -1)
            return sendResponseWithRequest(req, res, 200, false, req.t('STUDENT_NOT_FOUND'), null);
        status = students[loc].status;
        return sendResponseWithRequest(req, res, 200, true, req.t('SESSION_GET'), status);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_retake = async (req, res) => {
    try {
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
            isActive: true,
        };
        const {
            body: { _id, _staff_id },
        } = req;
        logger.info('sessionController -> Retake -> %s session start by %s', _id, _staff_id);
        const data = await getSchedule({ scheduleId: req.body._id });
        if (!data)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        const { merge_status, merge_with, sessionDetail, _id: scheduleId, staffs } = data;
        let staffIds = staffs.filter(
            (staff) =>
                staff._staff_id.toString() !== req.body._staff_id.toString() &&
                staff.status === PRESENT,
        );
        staffIds = staffIds.map((staffId) => staffId._staff_id);
        if (sessionDetail.attendance_mode === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_STARTED'),
                null,
            );
        if (sessionDetail.attendance_mode === ONGOING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ATTENDANCE_PROCESS_IS_ONGOING'),
                null,
            );
        if (data.status === COMPLETED)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_ENDED_UNABLE_TO_RETAKE_ATTENDANCE'),
                null,
            );
        const socketIds = [];
        if (!merge_status && sessionDetail && sessionDetail.attendance_mode === COMPLETED) {
            const courseSchedule = data;
            const students = clone(courseSchedule.students);
            const staffs = courseSchedule.staffs;
            let present_count = 0;
            const absent_count = 0;
            const student_ids = [];
            if (req.body.to === 'absent') {
                students.forEach((element) => {
                    if (
                        element.status !== 'present' &&
                        element.status !== ONDUTY &&
                        element.status !== LEAVE &&
                        element.status !== PERMISSION
                    ) {
                        student_ids.push(ObjectId(element._id));
                        element.status = 'pending';
                    } else {
                        present_count++;
                    }
                });
            } else {
                students.forEach((element) => {
                    student_ids.push(ObjectId(element._id));
                    if (element.status === 'present') {
                        delete element.time;
                        delete element.mode;
                    }
                    if (
                        element.status !== ONDUTY &&
                        element.status !== LEAVE &&
                        element.status !== PERMISSION
                    )
                        element.status = 'pending';
                });
            }
            const objs = {
                $set: {
                    'sessionDetail.attendance_mode': ONGOING,
                    'sessionDetail._start_by': ObjectId(req.body._staff_id),
                    students,
                    'sessionDetail.retake': true,
                    status: ONGOING,
                },
            };
            await update_condition(CourseSchedule, query, objs);
            const userIds = student_ids;
            /*  if (staffIds) {
                 userIds = student_ids.concat(staffIds);
             } */
            const userResult = await get_list(
                user,
                { _id: { $in: userIds } },
                { fcm_token: 1, web_fcm_token: 1, device_type: 1, socketEventId: 1 },
            );
            if (!userResult.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('STUDENTS_NOT_FOUND'),
                    null,
                );
            const tokens = [];
            const users = userResult.data;
            users.forEach((element) => {
                let socketEventIds = element.socketEventId;
                if (socketEventIds) {
                    socketEventIds = Object.values(socketEventIds);
                    socketIds.push(socketEventIds);
                }
                if ((element.fcm_token || element.web_fcm_token) && element.device_type)
                    tokens.push({
                        device_type: element.device_type,
                        token: element.fcm_token,
                        web_fcm_token: element.web_fcm_token,
                        _user_id: element._id,
                    });
            });
            if (tokens.length) {
                const { title, message } = makeTitleAndMessage(courseSchedule);
                const data = {
                    _id: courseSchedule._id,
                    sessionId: courseSchedule.session._session_id,
                    rotation: courseSchedule.rotation,
                    rotation_count: courseSchedule.rotation_count,
                    courseId: courseSchedule._course_id,
                    programId: courseSchedule._program_id,
                    institutionCalendarId: courseSchedule._institution_calendar_id,
                    yearNo: courseSchedule.year_no,
                    levelNo: courseSchedule.level_no,
                    term: courseSchedule.term,
                    mergeStatus: courseSchedule.merge_status,
                    mergeType: courseSchedule.type,
                    clickAction: 'session_start',
                };

                // fcm.firebase_push_notification(tokens, title, message, data)
                push.notification_push(tokens, title, message, data);
            }
            const response = {
                count: students.length,
                present_count,
                absent_count,
                leave_count: 0,
                students,
                staffs,
                socket_port: courseSchedule.socket_port,
                session_uuid: courseSchedule.uuid,
            };
            if (users.length) {
                let userIds = users.map((token) => token._id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ notificationCount: 1, sessions: courseSchedule });
                for (const user of users) {
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> Retake -> %s session by %s - end', _id, _staff_id);
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('RETAKE_ATTENDANCE_STARTED_SUCCESSFULLY'),
                response,
            );
        }
        if (merge_status && sessionDetail && sessionDetail.attendance_mode === COMPLETED) {
            // primary
            let courseSchedule = data;
            const students = courseSchedule.students;
            const staffs = courseSchedule.staffs;

            let count = 0;
            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            const student_ids = [];

            // with merge
            const scheduleIds = merge_with.map((mw) => toObjectId(mw.schedule_id));
            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            const CourseSchedules = (await getJSON(CourseSchedule, query)).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            const studentWithSchedule = [];
            CourseSchedules.forEach((cSchedule) => {
                const {
                    students,
                    _id,
                    session: { _session_id: sessionId },
                } = cSchedule;
                count += students.length;

                students.concat(students);
                const sessionStudents = clone(students);
                if (req.body.to === 'absent') {
                    sessionStudents.forEach((element) => {
                        if (
                            element.status === LEAVE ||
                            element.status === PERMISSION ||
                            element.status === ONDUTY
                        )
                            leave_count++;
                        if (element.status === 'absent') absent_count++;
                        if (element.status === 'present') present_count++;
                        if (element.status === 'absent') {
                            student_ids.push(ObjectId(element._id));
                            element.status = 'pending';
                            if (
                                !studentWithSchedule.find(
                                    (sWS) => cs(sWS.userId) === cs(element._id),
                                )
                            )
                                studentWithSchedule.push({
                                    userId: element._id,
                                    scheduleId: _id,
                                    sessionId,
                                    otherSchedule: cSchedule,
                                });
                            if (element.time) delete element.time;
                            if (element.mode) delete element.mode;
                        }
                    });
                } else {
                    sessionStudents.forEach((element) => {
                        student_ids.push(ObjectId(element._id));
                        if (!studentWithSchedule.find((sWS) => cs(sWS.userId) === cs(element._id)))
                            studentWithSchedule.push({
                                userId: element._id,
                                scheduleId: _id,
                                sessionId,
                                otherSchedule: cSchedule,
                            });
                        if (
                            element.status !== ONDUTY &&
                            element.status !== LEAVE &&
                            element.status !== PERMISSION
                        )
                            element.status = 'pending';
                        else leave_count++;
                        if (element.time) delete element.time;
                        if (element.mode) delete element.mode;
                    });
                }
                sessionStudents.forEach((student) => {
                    student._id = convertToMongoObjectId(student._id);
                });
                bulk.find({ _id: toObjectId(_id) }).updateOne({
                    $set: {
                        'sessionDetail.attendance_mode': ONGOING,
                        'sessionDetail._start_by': ObjectId(req.body._staff_id),
                        students: sessionStudents,
                        'sessionDetail.retake': true,
                        status: ONGOING,
                    },
                });
            });
            let bulkError;
            await bulk.execute((error) => {
                bulkError = error;
            });
            if (bulkError)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('ERROR_ON_BULK_WRITE'),
                    null,
                );
            query._id = ObjectId(req.body._id);
            courseSchedule = (await base_control.get(CourseSchedule, query, {})).data;
            const userResult = await get_list(
                user,
                { _id: { $in: student_ids } },
                { fcm_token: 1, web_fcm_token: 1, device_type: 1, socketEventId: 1 },
            );
            if (!userResult.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('STUDENTS_NOT_FOUND'),
                    null,
                );
            const tokens = [];
            const users = userResult.data;
            users.forEach((element) => {
                let socketEventIds = element.socketEventId;
                if (socketEventIds) {
                    socketEventIds = Object.values(socketEventIds);
                    socketIds.push(socketEventIds);
                }
                if ((element.fcm_token || element.web_fcm_token) && element.device_type) {
                    const mergedSchedule = studentWithSchedule.find(
                        (sWS) => cs(sWS.userId) === cs(element._id),
                    );
                    tokens.push({
                        device_type: element.device_type,
                        token: element.fcm_token,
                        web_fcm_token: element.web_fcm_token,
                        _user_id: element._id,
                        scheduleId: mergedSchedule ? mergedSchedule.scheduleId : scheduleId,
                        sessionId: mergedSchedule ? mergedSchedule.sessionId : sessionId,
                        schedule: mergedSchedule ? mergedSchedule.otherSchedule : undefined,
                    });
                }
            });
            if (tokens.length) {
                let title = 'Make your Attendance';
                if (courseSchedule.course_code) title += '(' + courseSchedule.course_code + ')';
                let message;
                if (courseSchedule.course_name) message = courseSchedule.course_name + ' : ';
                if (courseSchedule.session && courseSchedule.session.session_type) {
                    message +=
                        courseSchedule.session.session_type +
                        ' : ' +
                        courseSchedule.session.delivery_symbol +
                        courseSchedule.session.delivery_no;
                } else message += courseSchedule.title + ' : ' + courseSchedule.sub_type;
                if (courseSchedule.infra) message += ' : Room - ' + courseSchedule.infra;
                const data = {
                    _id: courseSchedule._id,
                    sessionId: courseSchedule.session._session_id,
                    courseId: courseSchedule._course_id,
                    programId: courseSchedule._program_id,
                    institutionCalendarId: courseSchedule._institution_calendar_id,
                    yearNo: courseSchedule.year_no,
                    term: courseSchedule.term,
                    levelNo: courseSchedule.level_no,
                    mergeStatus: courseSchedule.merge_status,
                    mergeType: courseSchedule.type,
                    clickAction: 'session_start',
                };

                // fcm.firebase_push_notification(tokens, title, message, data)
                push.notification_push(tokens, title, message, data);
            }

            const concatMergedStudents = students.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            const presentStudentCounts = concatMergedStudents.filter(
                (concatMergedStudent) => concatMergedStudent.status === 'present',
            );
            const response = {
                count: concatMergedStudents.length,
                present_count: presentStudentCounts.length,
                absent_count,
                leave_count,
                students: concatMergedStudents,
                staffs,
                socket_port: courseSchedule.socket_port,
                session_uuid: courseSchedule.uuid,
            };
            if (users.length) {
                let userIds = users.map((token) => token._id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ notificationCount: 1, sessions: courseSchedule });
                for (const user of users) {
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> Retake -> %s session by %s - end', _id, _staff_id);
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('RETAKE_ATTENDANCE_STARTED_SUCCESSFULLY'),
                response,
            );
        }
    } catch (error) {
        logger.error(
            'sessionController -> Retake -> %s by %s error : %o',
            req.body._id,
            req.body._staff_id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_comment = async (req, res) => {
    try {
        const { comment, _staff_id, _id } = req.body;
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(_id),
            isDeleted: false,
        };
        const courseScheduleResult = await getSchedule({ scheduleId: _id });
        if (!courseScheduleResult)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        const courseSchedule = courseScheduleResult;
        if (courseSchedule.sessionDetail.attendance_mode === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_STARTED'),
                null,
            );
        const objs = { $set: { 'staffs.$[j].comment': comment } };
        const filter = { arrayFilters: [{ 'j._staff_id': _staff_id }] };
        const doc = await updateWithFilter(CourseSchedule, query, objs, filter);
        if (!doc.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_PERSISTED_COMMENT'),
                null,
            );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SESSION_COMMENT_PERSISTED_SUCCESSFULLY'),
            null,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

const autoEndSession = async () => {
    logger.info('sessionController -> autoSessionEnd -> start');
    const schedule_date = convertToUtcFormat();
    const query = { schedule_date, isDeleted: false, isActive: true, status: ONGOING };
    const courseSchedules = await CourseSchedule.find(query);
    const endableScheduleIds = [];
    if (courseSchedules.length) {
        for (const courseSchedule of courseSchedules) {
            const { _id, schedule_date, end } = courseSchedule;
            if (isExpired(schedule_date, end, 9))
                endableScheduleIds.push(convertToMongoObjectId(_id));
        }
    }
    if (endableScheduleIds.length) {
        for (const endableScheduleId of endableScheduleIds) {
            endSession(endableScheduleId);
        }
    }
    logger.info('sessionController -> autoSessionEnd -> end');
};

const setMissedSession = async (updatableIds) => {
    try {
        const todayDate = convertToUtcFormat();
        const query = {
            schedule_date: todayDate,
            isDeleted: false,
            isActive: true,
            status: PENDING,
        };
        const objs = { $set: { status: 'missed' } };
        const filter = { arrayFilters: [{ _id: { $in: updatableIds } }] };
        const doc = await updateWithFilter(CourseSchedule, query, objs, filter);
        if (!doc.status) console.log('Unable update missed session');
        return true;
    } catch (error) {
        throw new Error(error);
    }
};

// today schedule cron job
exports.scheduleTodaySendNotification = async ({ initBy }) => {
    try {
        logger.info('sessionController -> sendNotificationBeforeSession -> start');
        activityCron();
        autoEndSession();

        const todayDate = convertToUtcFormat();
        const scheduleQuery = {
            schedule_date: todayDate,
            isDeleted: false,
            isActive: true,
            status: PENDING,
        };
        const courseSchedules = await CourseSchedule.find(scheduleQuery);
        // const missedScheduleIds = [];
        const sentScheduleIds = [];
        if (courseSchedules.length) {
            for (const courseSchedule of courseSchedules) {
                const {
                    _id,
                    start: { hour: startHour, minute: startMinute, format: startFormat },
                    schedule_date,
                    staffs,
                    end,
                    merge_status,
                    merge_with,
                } = courseSchedule;
                if (sentScheduleIds.includes(_id.toString())) continue;
                sentScheduleIds.push(_id.toString());
                const mergedSchedules = [];
                if (merge_status) {
                    merge_with.forEach((mergedWith) => {
                        const { schedule_id } = mergedWith;
                        const schedule = courseSchedules.find((entry) => {
                            return entry._id.toString() === schedule_id.toString();
                        });
                        if (schedule) mergedSchedules.push(schedule);
                    });
                }

                // if (isExpired(schedule_date, end))
                //     missedScheduleIds.push(convertToMongoObjectId(_id));

                // if merged session exists
                const startHours =
                    startFormat === PM && startHour !== 12 ? startHour + 12 : startHour;
                const startDateAndTime = new Date(
                    convertToUtcTimeFormat(schedule_date, startHours, startMinute, 0),
                );
                const ids = staffs.map((staff) => convertToMongoObjectId(staff._staff_id));
                const userResult = await base_control.get_list(
                    user,
                    { _id: { $in: ids } },
                    {
                        name: 1,
                        user_type: 1,
                        user_id: 1,
                        mobile: 1,
                        biometric_data: 1,
                        fcm_token: 1,
                        web_fcm_token: 1,
                        device_type: 1,
                        socketEventId: 1,
                    },
                );
                const tokens = [];
                const socketIds = [];
                const users = userResult.data;
                if (users.length) {
                    users.forEach((element) => {
                        let socketEventIds = element.socketEventId;
                        if (socketEventIds) {
                            socketEventIds = Object.values(socketEventIds);
                            socketIds.push(socketEventIds);
                        }
                        tokens.push({
                            device_type: element.device_type,
                            token: element.fcm_token,
                            web_fcm_token: element.web_fcm_token,
                            _user_id: element._id,
                            userId: element._id,
                            socketEventId: element.socketEventId,
                        });
                    });
                }

                const data = {
                    _id: courseSchedule._id,
                    sessionId: courseSchedule.session._session_id,
                    courseId: courseSchedule._course_id,
                    programId: courseSchedule._program_id,
                    rotation: courseSchedule.rotation,
                    rotation_count: courseSchedule.rotation_count,
                    institutionCalendarId: courseSchedule._institution_calendar_id,
                    yearNo: courseSchedule.year_no,
                    levelNo: courseSchedule.level_no,
                    mergeStatus: courseSchedule.merge_status,
                    mergeType: courseSchedule.type,
                    clickAction: 'session_start',
                    notificationType: 'session',
                };

                // prepare message and title
                let title;
                let message;
                if (courseSchedule.session && courseSchedule.session.session_type) {
                    message = capitalize(courseSchedule.mode) + ' Session \n';
                    message +=
                        courseSchedule.session.delivery_symbol +
                        courseSchedule.session.delivery_no +
                        ' ' +
                        courseSchedule.session.session_topic +
                        ' \n';
                }
                if (mergedSchedules.length) {
                    mergedSchedules.forEach((mergedSchedule) => {
                        sentScheduleIds.push(mergedSchedule._id.toString());
                        if (mergedSchedule.session && mergedSchedule.session.session_type) {
                            message +=
                                mergedSchedule.session.delivery_symbol +
                                mergedSchedule.session.delivery_no +
                                ' ' +
                                mergedSchedule.session.session_topic +
                                ' \n';
                        }
                    });
                }
                // splitted to session type based
                const sessionTypes = [EVENT, SUPPORT_SESSION];

                if (courseSchedule.type && sessionTypes.includes(courseSchedule.type)) {
                    message =
                        courseSchedule.title === SUPPORT_SESSION
                            ? 'Support Session • '
                            : 'Event • ' + capitalize(courseSchedule.mode) + ' \n';
                    message += capitalize(courseSchedule.sub_type) + ' \n';
                }

                if (
                    courseSchedule.program_name &&
                    courseSchedule.level_no &&
                    courseSchedule.course_name
                ) {
                    message +=
                        courseSchedule.program_name +
                        ' • ' +
                        courseSchedule.level_no +
                        ' • ' +
                        courseSchedule.course_name;
                }

                let currentDateAndTime = dateTimeLocalFormatter(new Date());
                currentDateAndTime = new Date(currentDateAndTime).getTime();
                const currentCornJobHours = new Date(currentDateAndTime).getHours();
                const currentCornJobMinutes = new Date(currentDateAndTime).getMinutes();
                // before 10 min send notification
                const beforeSessionDateAndTime = startDateAndTime.getTime() - 600000;
                const beforeCornJobHours = new Date(beforeSessionDateAndTime).getHours();
                const beforeCornJobMinutes = new Date(beforeSessionDateAndTime).getMinutes();

                if (
                    currentCornJobHours === beforeCornJobHours &&
                    currentCornJobMinutes === beforeCornJobMinutes
                ) {
                    if (tokens.length) {
                        data.notificationPeriod = 'before';
                        title = 'Starts in 10 minutes';
                        await push.notification_push(tokens, title, message, data);
                        sendDashboardNotification(tokens);
                    }
                }

                // OnTime send notification
                const onTimeCornJob = startDateAndTime.getTime();
                const onTimeCornJobHours = new Date(onTimeCornJob).getHours();
                const onTimeCornJobMinutes = new Date(onTimeCornJob).getMinutes();
                if (
                    currentCornJobHours === onTimeCornJobHours &&
                    currentCornJobMinutes === onTimeCornJobMinutes
                ) {
                    if (tokens.length) {
                        data.notificationPeriod = 'in';
                        title = 'Time to Start';
                        await push.notification_push(tokens, title, message, data);
                        sendDashboardNotification(tokens);
                    }
                }

                // Late session start notification
                const lateSessionCornJob = startDateAndTime.getTime() + 600000;
                const lateSessionCornJobHours = new Date(lateSessionCornJob).getHours();
                const lateSessionCornJobMinutes = new Date(lateSessionCornJob).getMinutes();
                if (
                    currentCornJobHours === lateSessionCornJobHours &&
                    currentCornJobMinutes === lateSessionCornJobMinutes
                ) {
                    if (tokens.length) {
                        data.notificationPeriod = 'after';
                        title = 'Start the Session';
                        await push.notification_push(tokens, title, message, data);
                        sendDashboardNotification(tokens);
                    }
                }
            }
        }
        logger.info('sessionController -> sendNotificationBeforeSession -> end');
    } catch (error) {
        logger.error(
            'sessionController -> sendNotificationBeforeSession -> error : %o',
            error.stack,
        );
        throw new Error(error);
    }
};
exports.session_report_with_face = async (req, res) => {
    try {
        const query = {
            _id: convertToMongoObjectId(req.params.id),
            isDeleted: false,
            isActive: true,
        };
        logger.info('sessionController -> sessionReport -> %s - start', req.params.id);
        const courseSchedule = await base_control.get(CourseSchedule, query, {});
        if (!courseSchedule.status)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        if (courseSchedule.data.mode === REMOTE) {
            let scheduleIds = [courseSchedule.data._id];
            if (courseSchedule.data.merge_status) {
                const mergeScheduleIds = courseSchedule.data.merge_with.map((mw) =>
                    convertToMongoObjectId(mw.schedule_id),
                );
                scheduleIds = scheduleIds.concat(mergeScheduleIds);
            }
            await getZoomUsersDuration(scheduleIds);
        }

        const courseScheduleResult = await base_control.get(CourseSchedule, query, {});
        if (!courseScheduleResult.status)
            return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const {
            _id,
            status,
            staffs,
            students,
            sessionDetail,
            merge_status,
            merge_with,
            zoomDetail,
            zoomDuration,
        } = courseScheduleResult.data;
        if (sessionDetail) {
            const mergedStudents = [];
            if (merge_status) {
                const scheduleIds = merge_with.map((mw) => convertToMongoObjectId(mw.schedule_id));
                let mergedCourseSchedules = await CourseSchedule.find(
                    { _id: { $in: scheduleIds } },
                    {},
                );
                mergedCourseSchedules = mergedCourseSchedules.map(
                    (mergedCourseSchedule) => mergedCourseSchedule.students,
                );
                // eslint-disable-next-line prefer-spread
                mergedStudents.push([].concat.apply([], mergedCourseSchedules));
            }

            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            mergedStudents.push(students);
            // eslint-disable-next-line prefer-spread
            const cloneMergedStudents = [].concat.apply([], mergedStudents);

            // duplicate student removed
            const duplicateStudents = cloneMergedStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) {
                    return acc.concat([current]);
                }
                return acc;
            }, []);
            const users = [];
            duplicateStudents.forEach((element) => {
                users.push(convertToMongoObjectId(element._id));
            });
            staffs.forEach((element) => {
                users.push(convertToMongoObjectId(element._staff_id));
                element.status = element.status === PENDING ? 'absent' : element.status;
            });
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: users } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    isActive: 1,
                    'biometric_data.face': 1,
                },
            );
            const usersDetails = userResult.status ? clone(userResult.data) : [];
            const staffDetails = staffs.map((staff) => {
                let userId;
                if (usersDetails.length) {
                    userId = usersDetails.find(
                        (usersDetail) => usersDetail._id.toString() === staff._staff_id.toString(),
                    );
                    if (userId) userId = userId.user_id;
                }
                return {
                    staff_name: staff.staff_name,
                    status: staff.status,
                    _id: staff._id,
                    _staff_id: staff._staff_id,
                    time: staff.time,
                    mode: staff.mode,
                    duration: staff.duration,
                    userId,
                };
            });
            let studentDetails = [];
            for (duplicateStudentElement of duplicateStudents) {
                const studentData = usersDetails.find(
                    (usersDetail) =>
                        usersDetail.isActive &&
                        usersDetail._id.toString() === duplicateStudentElement._id.toString(),
                );
                if (studentData) {
                    studentDetails.push({
                        name: duplicateStudentElement.name,
                        status: duplicateStudentElement.status,
                        _id: duplicateStudentElement._id,
                        feedBack: duplicateStudentElement.feedBack,
                        time: duplicateStudentElement.time,
                        mode: duplicateStudentElement.mode,
                        duration: duplicateStudentElement.duration,
                        percentage: duplicateStudentElement.percentage,
                        userId: studentData.user_id,
                        face:
                            studentData.biometric_data &&
                            studentData.biometric_data.face &&
                            studentData.biometric_data.face[0] &&
                            studentData.biometric_data.face[0]
                                ? await getSignedURL(studentData.biometric_data.face[0])
                                : '',
                    });
                }
                if (duplicateStudentElement.status === 'present') present_count++;
                else if (
                    duplicateStudentElement.status === 'absent' ||
                    duplicateStudentElement.status === 'pending'
                )
                    absent_count++;
                else if (
                    duplicateStudentElement.status === LEAVE ||
                    duplicateStudentElement.status === PERMISSION ||
                    duplicateStudentElement.status === ONDUTY
                )
                    leave_count++;
                duplicateStudentElement.status =
                    duplicateStudentElement.status === PENDING
                        ? 'absent'
                        : duplicateStudentElement.status;
            }
            studentDetails = studentDetails.sort((a, b) => {
                let comparison = 0;
                if (a.name.first !== undefined && b.name.first !== undefined)
                    if (a.name.first.toLowerCase() < b.name.first.toLowerCase()) {
                        comparison = -1;
                    } else if (a.name.first.toLowerCase() > b.name.first.toLowerCase()) {
                        comparison = 1;
                    }
                return comparison;
            });
            const response = {
                status,
                count: studentDetails.length,
                present_count,
                absent_count,
                leave_count,
                students: studentDetails,
                staffs: staffDetails,
                sessionDetail,
                zoomTotalDuration: zoomDetail.zoomTotalDuration ? zoomDetail.zoomTotalDuration : '',
                zoomDuration,
            };
            let total = 0;
            let count = 0;
            for (const feedBackData of students) {
                if (feedBackData.feedBack && feedBackData.feedBack.rating) {
                    total += parseInt(feedBackData.feedBack.rating);
                    count++;
                }
            }
            if (count !== 0) {
                response.feedBacks = {
                    _session_id: _id,
                    totalFeedback: count,
                    avgRating: count !== 0 ? (total / count).toFixed(1) : 0,
                };
            }
            logger.info('sessionController -> sessionReport -> %s - end', req.params.id);
            return sendResponse(res, 200, true, req.t('SESSION_REPORT'), response);
        }
        logger.info('sessionController -> sessionReport -> %s - end', req.params.id);
        return sendResponse(res, 200, false, req.t('SESSION_REPORT_PENDING'), null);
    } catch (error) {
        logger.error(
            'sessionController -> sessionStart -> %s error : %o',
            req.params.id,
            error.stack,
        );
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.session_attendance_change = async (req, res) => {
    try {
        const { _id, _student_ids, _staff_ids } = req.body;
        const { _institution_id } = req.headers;
        const query = {
            _institution_id: convertToMongoObjectId(_institution_id),
            _id: convertToMongoObjectId(_id),
            isDeleted: false,
            isActive: true,
        };
        logger.info('sessionController -> sessionAttendanceChange -> %s - start', _id);
        const courseScheduleResult = await base_control.get(CourseSchedule, query, {});
        if (!courseScheduleResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        if (courseScheduleResult.data.status === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_STARTED'),
                null,
            );
        const userIds = [];
        if (!courseScheduleResult.data.merge_status) {
            const { sessionDetail, students, staffs, start, mode } = courseScheduleResult.data;
            _student_ids.forEach((element) => {
                const stdIndex = students.findIndex((i) => i._id.toString() === element.toString());
                if (stdIndex >= 0) {
                    userIds.push(convertToMongoObjectId(element));
                    students[stdIndex].status =
                        students[stdIndex].status === 'present' ? 'absent' : 'present';
                    students[stdIndex].time = timestampNow();
                }
            });
            _staff_ids.forEach((element) => {
                const staffIndex = staffs.findIndex(
                    (i) => i._staff_id.toString() === element.toString(),
                );
                if (staffIndex >= 0) {
                    userIds.push(convertToMongoObjectId(element));
                    staffs[staffIndex].status =
                        staffs[staffIndex].status === 'present' ? 'absent' : 'present';
                    staffs[staffIndex].time = timestampNow();
                }
            });
            const objs = { $set: { students, staffs } };
            const scheduleStartTime = start.hour + ':' + start.minute + ' ' + start.format;
            const session_time = moment(scheduleStartTime, ['h:mm A']);
            const start_time = moment(sessionDetail.start_time);
            const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
            const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
            const duration = moment.duration(start_times.diff(session_times));
            const min = parseInt(duration.asMinutes()) % 60;
            const hours = parseInt(duration.asHours());
            const late = hours > 0 || min > 10;
            const data = { is_late: late };
            const doc = await updateMany(CourseSchedule, query, objs);
            const courseSchedule = (await base_control.get(CourseSchedule, query, {})).data;
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    biometric_data: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    device_type: 1,
                    socketEventId: 1,
                },
            );
            const socketIds = [];
            const users = userResult.status ? userResult.data : [];
            if (users.length) {
                users.forEach((element) => {
                    let socketEventIds = element.socketEventId;
                    if (socketEventIds) {
                        socketEventIds = Object.values(socketEventIds);
                        socketIds.push(socketEventIds);
                    }
                });
            }
            if (users.length) {
                let userIds = users.map((token) => token._id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ sessions: courseSchedule });
                for (const user of users) {
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> sessionAttendanceChange -> %s - end', _id);
            if (!doc.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_CHANGE_ATTENDANCE'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
                data,
            );
        }
        if (courseScheduleResult.data.merge_status) {
            const scheduleIds = courseScheduleResult.data.merge_with.map((mw) =>
                toObjectId(mw.schedule_id),
            );
            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            let data;
            const mCourseSchedules = (await getJSON(CourseSchedule, query, {})).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            let mergedStudents = [];
            let mergedStaffs = [];
            mCourseSchedules.forEach((mCSchedule) => {
                const { sessionDetail, students, staffs, start, _id, mode } = mCSchedule;
                _student_ids.forEach((element) => {
                    userIds.push(convertToMongoObjectId(element));
                    const stdIndex = students.findIndex((i) => cs(i._id) === cs(element));
                    if (stdIndex >= 0) {
                        students[stdIndex].status =
                            students[stdIndex].status === 'present' ? 'absent' : 'present';
                        if (mode !== REMOTE) {
                            students[stdIndex].time = timestampNow();
                        }
                    }
                });
                mergedStudents = [...mergedStudents, ...students];
                _staff_ids.forEach((element) => {
                    userIds.push(convertToMongoObjectId(element));
                    const staffIndex = staffs.findIndex(
                        (i) => i._staff_id.toString() === element.toString(),
                    );
                    if (staffIndex >= 0) {
                        staffs[staffIndex].status =
                            staffs[staffIndex].status === 'present' ? 'absent' : 'present';
                        staffs[staffIndex].time = timestampNow();
                    }
                });
                mergedStaffs = [...mergedStaffs, ...staffs];
                const objs = { $set: { students, staffs } };
                const scheduleStartTime = start.hour + ':' + start.minute + ' ' + start.format;
                const session_time = moment(scheduleStartTime, ['h:mm A']);
                const start_time = moment(sessionDetail.start_time);
                const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
                const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
                const duration = moment.duration(start_times.diff(session_times));
                const min = parseInt(duration.asMinutes()) % 60;
                const hours = parseInt(duration.asHours());
                const late = hours > 0 || min > 10;
                data = { is_late: late };
                bulk.find({ _id: toObjectId(_id) }).updateOne(objs);
            });

            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    name: 1,
                    user_type: 1,
                    user_id: 1,
                    mobile: 1,
                    biometric_data: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    device_type: 1,
                    socketEventId: 1,
                },
            );
            const users = userResult.status ? userResult.data : [];
            const courseSchedule = (await base_control.get(CourseSchedule, query, {})).data;
            mergedStudents = mergedStudents.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            mergedStaffs = mergedStaffs.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            courseSchedule.students = mergedStudents;
            courseSchedule.staffs = mergedStaffs;
            if (users.length) {
                let userIds = users.map((token) => token._id);
                userIds = [...new Set(userIds)];
                const sendSocketData = [];
                const data = JSON.stringify({ sessions: courseSchedule });
                for (const user of users) {
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }

            logger.info('sessionController -> sessionAttendanceChange -> %s - end', _id);
            let bulkError;
            bulk.execute((error) => {
                bulkError = error;
            });
            if (bulkError)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_CHANGE_ATTENDANCE'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
                data,
            );
        }
    } catch (error) {
        logger.error(
            'sessionController -> sessionAttendanceChange -> %s error : %o',
            req.body._id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_close_with_attendance = async (req, res) => {
    try {
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isActive: true,
            isDeleted: false,
        };
        logger.info('sessionController -> sessionClose -> %s - start', req.body._id);
        const courseScheduleResult = await base_control.get(CourseSchedule, query, {});
        if (!courseScheduleResult.status)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        const { merge_status, merge_with, mode, sessionDetail, status, _id } =
            courseScheduleResult.data;

        if (mode === REMOTE) {
            let scheduleIds = [_id];
            if (merge_status) {
                const mergeScheduleIds = merge_with.map((mw) =>
                    convertToMongoObjectId(mw.schedule_id),
                );
                scheduleIds = scheduleIds.concat(mergeScheduleIds);
            }
            await endingZoomMeeting(scheduleIds);
            await zoomMeetingEnd(req.body._id);
        }
        // if (mode !== REMOTE && sessionDetail.attendance_mode === ONGOING)
        //     return sendResponse(res, 200, false, "Attendance is ongoing, you can`t' stop", null);
        if (status === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_START'),
                null,
            );
        const session = courseScheduleResult.data;
        session.status = COMPLETED;
        if (!merge_status) {
            const { students, staffs } = courseScheduleResult.data;
            const presentStudents = students.filter((student) => student.status === 'present');
            students.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                students[index].status = status;
                students[index].primaryStatus =
                    students[index].primaryStatus === undefined
                        ? status
                        : students[index].primaryStatus === 'pending' &&
                          !leavePermissionOnDuty.includes(status)
                        ? status
                        : students[index].primaryStatus;
            });
            staffs.forEach((element, index) => {
                const status = element.status === 'pending' ? 'absent' : element.status;
                staffs[index].status = status;
            });
            const objs = {
                $set: {
                    'sessionDetail.attendance_mode': COMPLETED,
                    'sessionDetail.stop_time': timestampNow(),
                    students,
                    staffs,
                    status: COMPLETED,
                },
            };
            const studentIds = presentStudents.map((student) => student._id.toString());
            const staffIds = staffs.map((staff) => staff._staff_id.toString());
            userIds = studentIds.concat(staffIds);
            userIds = userIds.map((userId) => convertToMongoObjectId(userId));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    socketEventId: 1,
                },
            );
            const users = userResult.data;
            if (users.length) {
                const sendSocketData = [];
                for (const user of users) {
                    const data = JSON.stringify({ sessions: session });
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            const doc = await update_condition(CourseSchedule, query, objs);
            logger.info('sessionController -> sessionClose -> %s - end', req.body._id);
            if (doc.status)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    true,
                    req.t('SESSION_COMPLETED_SUCCESSFULLY'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_END_SESSION'),
                null,
            );
        }
        if (merge_status) {
            const scheduleIds = merge_with.map((mw) => toObjectId(mw.schedule_id));

            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            const CourseSchedules = (await getJSON(CourseSchedule, query, {})).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            CourseSchedules.forEach((cSchedule) => {
                const { _id, staffs, students } = cSchedule;
                const presentStudents = students.filter((student) => student.status === 'present');
                students.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    students[index].status = status;
                    students[index].primaryStatus =
                        students[index].primaryStatus === undefined
                            ? status
                            : students[index].primaryStatus &&
                              students[index].primaryStatus === 'pending' &&
                              !leavePermissionOnDuty.includes(status)
                            ? status
                            : students[index].primaryStatus;
                });
                staffs.forEach((element, index) => {
                    const status = element.status === 'pending' ? 'absent' : element.status;
                    staffs[index].status = status;
                });
                const studentIds = presentStudents.map((student) => student._id.toString());
                const staffIds = staffs.map((staff) => staff._staff_id.toString());
                userIds.push(studentIds.concat(staffIds));
                bulk.find({ _id: toObjectId(_id) }).updateOne({
                    $set: {
                        'sessionDetail.attendance_mode': COMPLETED,
                        'sessionDetail.stop_time': timestampNow(),
                        students,
                        staffs,
                        status: COMPLETED,
                    },
                });
            });
            // eslint-disable-next-line prefer-spread
            userIds = [].concat.apply([], userIds);
            userIds = userIds.map((userId) => convertToMongoObjectId(userId));
            const userResult = await base_control.get_list(
                user,
                { _id: { $in: userIds } },
                {
                    socketEventId: 1,
                },
            );
            const users = userResult.data;
            if (users.length) {
                const sendSocketData = [];
                for (const user of users) {
                    const data = JSON.stringify({ sessions: session });
                    const eventId = user._id;
                    sendSocketData.push({ eventId, data });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info('sessionController -> sessionClose -> %s - end', req.body._id);
            bulk.execute((error) => {
                if (!error)
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        true,
                        req.t('SESSION_COMPLETED_SUCCESSFULLY'),
                        null,
                    );
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_END_SESSION'),
                    null,
                );
            });
        }
    } catch (error) {
        logger.error(
            'sessionController -> sessionClose -> %s error : %o',
            req.body._id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.session_retake_with_attendance = async (req, res) => {
    try {
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.body._id),
            isDeleted: false,
            isActive: true,
        };
        const {
            body: { _id, _staff_id },
        } = req;
        logger.info('sessionController -> Retake -> %s session start by %s', _id, _staff_id);
        const data = await getSchedule({ scheduleId: req.body._id });
        if (!data) return sendResponse(res, 200, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        const { merge_status, merge_with, sessionDetail, _id: scheduleId, staffs } = data;
        let staffIds = staffs.filter(
            (staff) =>
                staff._staff_id.toString() !== req.body._staff_id.toString() &&
                staff.status === PRESENT,
        );
        staffIds = staffIds.map((staffId) => staffId._staff_id);
        if (sessionDetail.attendance_mode === PENDING)
            return sendResponse(res, 200, false, req.t('SESSION_NOT_STARTED'), null);
        if (data.status === COMPLETED)
            return sendResponse(
                res,
                200,
                false,
                req.t('SESSION_ENDED_UNABLE_TO_RETAKE_ATTENDANCE'),
                null,
            );
        const socketIds = [];
        if (!merge_status && sessionDetail /* && sessionDetail.attendance_mode === COMPLETED */) {
            const courseSchedule = data;
            const students = clone(courseSchedule.students);
            const staffs = courseSchedule.staffs;
            let present_count = 0;
            const absent_count = 0;
            const student_ids = [];
            if (req.body.to === 'absent') {
                students.forEach((element) => {
                    if (
                        element.status !== 'present' &&
                        element.status !== ONDUTY &&
                        element.status !== LEAVE &&
                        element.status !== PERMISSION
                    ) {
                        student_ids.push(ObjectId(element._id));
                        element.status = 'pending';
                    } else {
                        present_count++;
                    }
                });
            } else {
                students.forEach((element) => {
                    student_ids.push(ObjectId(element._id));
                    if (element.status === 'present') {
                        delete element.time;
                        delete element.mode;
                    }
                    if (
                        element.status !== ONDUTY &&
                        element.status !== LEAVE &&
                        element.status !== PERMISSION
                    )
                        element.status = 'pending';
                });
            }
            const objs = {
                $set: {
                    'sessionDetail.attendance_mode': ONGOING,
                    'sessionDetail._start_by': ObjectId(req.body._staff_id),
                    students,
                    'sessionDetail.retake': true,
                    status: ONGOING,
                },
            };
            await update_condition(CourseSchedule, query, objs);

            const response = {
                count: students.length,
                present_count,
                absent_count,
                leave_count: 0,
                students,
                staffs,
                socket_port: courseSchedule.socket_port,
                session_uuid: courseSchedule.uuid,
            };
            logger.info('sessionController -> Retake -> %s session by %s - end', _id, _staff_id);
            return sendResponse(
                res,
                200,
                true,
                req.t('RETAKE_ATTENDANCE_STARTED_SUCCESSFULLY'),
                response,
            );
        }
        if (merge_status && sessionDetail /* && sessionDetail.attendance_mode === COMPLETED */) {
            // primary
            let courseSchedule = data;
            const students = courseSchedule.students;
            const staffs = courseSchedule.staffs;

            let count = 0;
            let present_count = 0;
            let absent_count = 0;
            let leave_count = 0;
            const student_ids = [];

            // with merge
            const scheduleIds = merge_with.map((mw) => toObjectId(mw.schedule_id));
            scheduleIds.push(toObjectId(req.body._id));
            query._id = { $in: scheduleIds };
            const CourseSchedules = (await getJSON(CourseSchedule, query)).data;
            const bulk = CourseSchedule.collection.initializeOrderedBulkOp();
            const studentWithSchedule = [];
            CourseSchedules.forEach((cSchedule) => {
                const {
                    students,
                    _id,
                    session: { _session_id: sessionId },
                } = cSchedule;
                count += students.length;

                students.concat(students);
                const sessionStudents = clone(students);
                if (req.body.to === 'absent') {
                    sessionStudents.forEach((element) => {
                        if (
                            element.status === LEAVE ||
                            element.status === PERMISSION ||
                            element.status === ONDUTY
                        )
                            leave_count++;
                        if (element.status === 'absent') absent_count++;
                        if (element.status === 'present') present_count++;
                        if (element.status === 'absent') {
                            student_ids.push(ObjectId(element._id));
                            element.status = 'pending';
                            if (
                                !studentWithSchedule.find(
                                    (sWS) => cs(sWS.userId) === cs(element._id),
                                )
                            )
                                studentWithSchedule.push({
                                    userId: element._id,
                                    scheduleId: _id,
                                    sessionId,
                                    otherSchedule: cSchedule,
                                });
                            if (element.time) delete element.time;
                            if (element.mode) delete element.mode;
                        }
                    });
                } else {
                    sessionStudents.forEach((element) => {
                        student_ids.push(ObjectId(element._id));
                        if (!studentWithSchedule.find((sWS) => cs(sWS.userId) === cs(element._id)))
                            studentWithSchedule.push({
                                userId: element._id,
                                scheduleId: _id,
                                sessionId,
                                otherSchedule: cSchedule,
                            });
                        if (
                            element.status !== ONDUTY &&
                            element.status !== LEAVE &&
                            element.status !== PERMISSION
                        )
                            element.status = 'pending';
                        else leave_count++;
                        if (element.time) delete element.time;
                        if (element.mode) delete element.mode;
                    });
                }
                sessionStudents.forEach((student) => {
                    student._id = convertToMongoObjectId(student._id);
                });
                bulk.find({ _id: toObjectId(_id) }).updateOne({
                    $set: {
                        'sessionDetail.attendance_mode': ONGOING,
                        'sessionDetail._start_by': ObjectId(req.body._staff_id),
                        students: sessionStudents,
                        'sessionDetail.retake': true,
                        status: ONGOING,
                    },
                });
            });
            let bulkError;
            await bulk.execute((error) => {
                bulkError = error;
            });
            if (bulkError) return sendResponse(res, 200, false, req.t('ERROR_ON_BULK_WRITE'), null);
            query._id = ObjectId(req.body._id);
            courseSchedule = (await base_control.get(CourseSchedule, query, {})).data;

            const concatMergedStudents = students.reduce((acc, current) => {
                const x = acc.find((item) => item._id.toString() === current._id.toString());
                if (!x) return acc.concat([current]);
                return acc;
            }, []);
            const presentStudentCounts = concatMergedStudents.filter(
                (concatMergedStudent) => concatMergedStudent.status === 'present',
            );
            const response = {
                count: concatMergedStudents.length,
                present_count: presentStudentCounts.length,
                absent_count,
                leave_count,
                students: concatMergedStudents,
                staffs,
                socket_port: courseSchedule.socket_port,
                session_uuid: courseSchedule.uuid,
            };
            logger.info('sessionController -> Retake -> %s session by %s - end', _id, _staff_id);
            return sendResponse(
                res,
                200,
                true,
                req.t('RETAKE_ATTENDANCE_STARTED_SUCCESSFULLY'),
                response,
            );
        }
    } catch (error) {
        logger.error(
            'sessionController -> Retake -> %s by %s error : %o',
            req.body._id,
            req.body._staff_id,
            error.stack,
        );
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.scheduleStart = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { _staff_id, mode, isLive, _id } = req.body;
        const requestingKeys = {
            scheduleId: _id,
            staffId: _staff_id,
            mode,
            isLive,
        };
        logger.info(
            { requestingKeys },
            'sessionController -> scheduleStart -> %s start',
            _staff_id,
        );

        const scheduleQuery = {
            _id: convertToMongoObjectId(_id),
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleProject = {
            _id: 1,
            status: 1,
            schedule_date: 1,
            start: 1,
            'sessionDetail.attendance_mode': 1,
            'sessionDetail.start_time': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'session._session_id': 1,
            'session.session_topic': 1,
            'session.session_type': 1,
            'merge_with.schedule_id': 1,
            'staffs._staff_id': 1,
            'students._id': 1,
            'students.status': 1,
            remotePlatform: 1,
            mode: 1,
            socket_port: 1,
            'zoomDetail.zoomStartUrl': 1,
            'teamsDetail.teamStartUrl': 1,
            _infra_id: 1,
            scheduleStartDateAndTime: 1,
            scheduleEndDateAndTime: 1,
            title: 1,
            course_code: 1,
            course_name: 1,
            sub_type: 1,
            infra: 1,
            _course_id: 1,
            _program_id: 1,
            rotation: 1,
            rotation_count: 1,
            _institution_calendar_id: 1,
            year_no: 1,
            level_no: 1,
            term: 1,
            merge_status: 1,
            type: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await CourseSchedule.findOne(scheduleQuery, scheduleProject)
            .populate({
                path: 'merge_with.schedule_id',
                select: { 'session.delivery_symbol': 1, 'session.delivery_no': 1 },
            })
            .lean();
        console.timeEnd('courseScheduleData');
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        // if (!isExpired(courseScheduleData.schedule_date, courseScheduleData.start))
        //     return sendResponse(res, 200, false, 'start session on correct scheduled time', null);
        if (mode !== 'join') {
            if (courseScheduleData.status === ONGOING)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('ALREADY_YOU_HAVE_RUNNING_SESSION'),
                    null,
                );
            if (
                courseScheduleData.sessionDetail &&
                courseScheduleData.sessionDetail.attendance_mode
            ) {
                if (courseScheduleData.sessionDetail.attendance_mode === COMPLETED)
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        false,
                        req.t('ATTENDANCE_ALREADY_CLOSED'),
                        null,
                    );
                if (courseScheduleData.sessionDetail.attendance_mode === ONGOING)
                    return sendResponseWithRequest(
                        req,
                        res,
                        200,
                        false,
                        req.t('ATTENDANCE_ALREADY_STARTED'),
                        null,
                    );
            }
        }
        const getSocketEventName = () => {
            return courseScheduleData.session.delivery_symbol
                ? `${courseScheduleData._id}-${courseScheduleData.session.delivery_symbol}-${courseScheduleData.session.delivery_no}`
                : `${courseScheduleData._id}-P-1`;
        };
        const scheduleUpdateQuery = {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
            $or: [
                {
                    _id: convertToMongoObjectId(_id),
                },
                {
                    'merge_with.schedule_id': convertToMongoObjectId(_id),
                },
            ],
        };
        const scheduleObjectArrayFilter = {
            arrayFilters: [{ 'staffIndex._staff_id': convertToMongoObjectId(_staff_id) }],
        };
        const socketEventName = getSocketEventName();
        const scheduleResponse = {
            status: ONGOING,
            leave_count: 0,
            socket_port: socketEventName,
        };

        if (mode !== 'join') {
            const userDatas = await user.find(
                {
                    _id: {
                        $in: [
                            ...courseScheduleData.staffs.map(
                                (staffElement) => staffElement._staff_id,
                            ),
                            ...courseScheduleData.students.map(
                                (studentElement) => studentElement._id,
                            ),
                        ],
                    },
                },
                {
                    user_type: 1,
                    user_id: 1,
                    fcm_token: 1,
                    web_fcm_token: 1,
                    device_type: 1,
                    socketEventId: 1,
                },
            );
            if (!userDatas)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('STUDENTS_NOT_FOUND'),
                    null,
                );
            const tokens = [];
            const session_uuid = uuidv4();
            const socketIds = [];
            for (userElement of userDatas) {
                if (userElement.socketEventId)
                    socketIds.push(Object.values(userElement.socketEventId));
                if (userElement.user_type === 'student') {
                    tokens.push({
                        device_type: userElement.device_type,
                        token: userElement.fcm_token,
                        web_fcm_token: userElement.web_fcm_token,
                        _user_id: userElement._id,
                        scheduleId: _id,
                        sessionId: courseScheduleData.session._session_id,
                        schedule: undefined,
                        StudentGroup: '',
                    });
                }
            }
            const scheduleUpdateObject = {
                $set: {
                    isLive: isLive || false,
                    status: ONGOING,
                    socket_port: socketEventName,
                    uuid: session_uuid,
                    'sessionDetail.attendance_mode': ONGOING,
                    'sessionDetail.retake': false,
                    'sessionDetail.start_time': timestampNow(),
                    'sessionDetail.startBy': convertToMongoObjectId(_staff_id),
                    'staffs.$[staffIndex].status': PRESENT,
                    'staffs.$[staffIndex].mode': 'auto',
                    'staffs.$[staffIndex].time': timestampNow(),
                },
            };
            scheduleResponse.uuid = session_uuid;
            // Remote Schedule
            if (courseScheduleData.mode === REMOTE)
                switch (courseScheduleData.remotePlatform) {
                    case ZOOM:
                        if (!courseScheduleData.zoomDetail.zoomStartUrl) {
                            const remoteObject = await scheduleRemoteSessionStart({
                                courseScheduleData,
                            });
                            if (remoteObject.status) {
                                scheduleUpdateObject.$set.zoomDetail = remoteObject.zoomDetail;
                                scheduleResponse.zoomDetail = {
                                    zoomStartUrl: remoteObject.zoomDetail.zoomStartUrl,
                                };
                            }
                        }
                        break;
                    case TEAMS:
                        if (!courseScheduleData.teamsDetail.teamStartUrl) {
                            const remoteObject = await scheduleRemoteSessionStart(
                                courseScheduleData,
                                _staff_id,
                            );
                            if (remoteObject.status) {
                                scheduleUpdateObject.$set.teamsDetail = remoteObject.teamsDetail;
                                scheduleResponse.teamsDetail = {
                                    teamsStartUrl: remoteObject.teamsDetail.teamsStartUrl,
                                };
                            }
                        }
                        break;
                    default:
                }
            const scheduleUpdate = await course_schedule.updateMany(
                scheduleUpdateQuery,
                scheduleUpdateObject,
                scheduleObjectArrayFilter,
            );
            if (!scheduleUpdate)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_START_SESSION'),
                    null,
                );

            courseScheduleData.students.forEach((studentElement) => {
                if (
                    studentElement.status === LEAVE ||
                    studentElement.status === PERMISSION ||
                    studentElement.status === ONDUTY
                )
                    scheduleResponse.leave_count++;
            });

            if (tokens.length) {
                const { title, message } = makeTitleAndMessage(courseScheduleData);
                const notificationPushData = {
                    _id: courseScheduleData._id,
                    sessionId: courseScheduleData.session._session_id,
                    courseId: courseScheduleData._course_id,
                    programId: courseScheduleData._program_id,
                    rotation: courseScheduleData.rotation,
                    rotation_count: courseScheduleData.rotation_count,
                    institutionCalendarId: courseScheduleData._institution_calendar_id,
                    yearNo: courseScheduleData.year_no,
                    levelNo: courseScheduleData.level_no,
                    term: courseScheduleData.term,
                    mergeStatus: courseScheduleData.merge_status,
                    mergeType: courseScheduleData.type,
                    clickAction: 'session_start',
                    notificationType: 'session',
                };
                await push.notification_push(tokens, title, message, notificationPushData);
            }
            if (tokens.length) {
                const sendSocketData = [];
                const socketData = JSON.stringify({
                    notificationCount: 1,
                    sessions: courseScheduleData,
                });
                for (const token of tokens) {
                    const eventId = token._user_id;
                    sendSocketData.push({ eventId, socketData });
                }
                if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            }
            logger.info(
                'sessionController -> sessionStart -> %s Session attendance started',
                _staff_id,
            );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_ATTENDANCE_STARTED'),
                scheduleResponse,
            );
        }

        // Join mode will check later
        const session_time = moment(timestampNow());
        const start_time = moment(courseScheduleData.sessionDetail.start_time);
        const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
        const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
        const duration = moment.duration(session_times.diff(start_times));
        const min = parseInt(duration.asMinutes()) % 60;
        const hours = parseInt(duration.asHours());
        let late = false;
        if (hours !== 0 || min > 10) late = true;
        if (courseScheduleData.sessionDetail.attendance_mode === 'pending')
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_STARTED'),
                null,
            );
        courseScheduleData.students.forEach((studentElement) => {
            if (
                studentElement.status === LEAVE ||
                studentElement.status === PERMISSION ||
                studentElement.status === ONDUTY
            )
                scheduleResponse.leave_count++;
        });
        const scheduleUpdate = await course_schedule.updateMany(
            scheduleUpdateQuery,
            {
                'staffs.$[staffIndex].status': PRESENT,
                'staffs.$[staffIndex].mode': 'auto',
                'staffs.$[staffIndex].time': timestampNow(),
            },
            scheduleObjectArrayFilter,
        );
        if (!scheduleUpdate)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_START_SESSION'),
                null,
            );

        const response = {
            is_late: late,
            status: courseScheduleData.status,
            uuid: scheduleResponse.uuid,
            leave_count: scheduleResponse.leave_count,
            socket_port: socketEventName,
            zoomDetail: {
                zoomStartUrl:
                    courseScheduleData.zoomDetail && courseScheduleData.zoomDetail.zoomStartUrl
                        ? courseScheduleData.zoomDetail.zoomStartUrl
                        : '',
            },
            teamsDetail: {
                teamsStartUrl:
                    courseScheduleData.teamsDetail && courseScheduleData.teamsDetail.teamsStartUrl
                        ? courseScheduleData.teamsDetail.teamsStartUrl
                        : '',
            },
        };
        logger.info('sessionController -> sessionStart -> %s end', _staff_id);
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            'Joined session successfully',
            response,
        );
    } catch (error) {
        logger.error(
            'sessionController -> sessionStart -> %s error : %o',
            req.body._staff_id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.scheduleStudentAttendance = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { _id, _student_id, uuid } = req.body;
        const requestingKeys = {
            scheduleId: _id,
            studentId: _student_id,
            uuid,
        };
        logger.info({ requestingKeys }, 'sessionController -> scheduleStudentAttendance -> start');

        logger.info({ requestingKeys }, 'sessionController -> scheduleStudentAttendance -> end');
        return sendResponse(res, 200, true, 'Joined session successfully', []);
    } catch (error) {
        logger.error(
            'sessionController -> sessionStart -> %s error : %o',
            req.body._staff_id,
            error.stack,
        );
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.scheduleDateChange = async (req, res) => {
    try {
        const {
            body: { scheduleId, scheduleDate },
        } = req;
        const courseScheduleData = await CourseSchedule.findOne(
            { _id: convertToMongoObjectId(scheduleId) },
            { schedule_date: 1, scheduleStartDateAndTime: 1, scheduleEndDateAndTime: 1 },
        );
        if (!courseScheduleData)
            return res.status(410).send(response_function(res, 409, false, 'Schedule Not Found'));
        const userScheduleDate = new Date(scheduleDate);
        const scheduleStart = new Date(courseScheduleData.scheduleStartDateAndTime);
        const scheduleEnd = new Date(courseScheduleData.scheduleEndDateAndTime);
        const scheduleAlteredStartData = new Date(
            userScheduleDate.getFullYear(),
            userScheduleDate.getMonth(),
            userScheduleDate.getDate(),
            scheduleStart.getHours(),
            scheduleStart.getMinutes(),
            scheduleStart.getSeconds(),
        );
        const scheduleAlteredEndData = new Date(
            userScheduleDate.getFullYear(),
            userScheduleDate.getMonth(),
            userScheduleDate.getDate(),
            scheduleEnd.getHours(),
            scheduleEnd.getMinutes(),
            scheduleEnd.getSeconds(),
        );
        const scheduleUpdate = await CourseSchedule.updateOne(
            { _id: convertToMongoObjectId(scheduleId) },
            {
                $set: {
                    status:
                        new Date().toISOString() > new Date(scheduleAlteredEndData).toISOString()
                            ? MISSED
                            : PENDING,
                    schedule_date: userScheduleDate,
                    scheduleStartDateAndTime: scheduleAlteredStartData,
                    scheduleEndDateAndTime: scheduleAlteredEndData,
                },
            },
        );
        if (!scheduleUpdate) return sendResponse(res, 200, false, 'Unable to change Schedule Date');
        return sendResponse(res, 200, true, 'Schedule Date Successfully');
    } catch (error) {
        logger.error(
            'sessionController -> scheduleDateChange -> %s error : %o',
            req.params._id,
            error.stack,
        );
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.staffStudentAttendance = async (req, res) => {
    try {
        const { scheduleId, staffId, studentId, status } = req.body;
        const { _institution_id } = req.headers;
        const requestLogger = { scheduleId, staffId, studentId, status };
        logger.info(
            requestLogger,
            'sessionController -> staffStudentAttendance -> Session Staff Student Manual Attendance - start',
        );
        const scheduleQuery = {
            isDeleted: false,
            isActive: true,
            _institution_id: convertToMongoObjectId(_institution_id),
            $or: [
                { _id: convertToMongoObjectId(scheduleId) },
                { 'merge_with.schedule_id': convertToMongoObjectId(scheduleId) },
            ],
            'staffs._staff_id': convertToMongoObjectId(staffId),
            'students._id': convertToMongoObjectId(studentId),
            'sessionDetail.attendance_mode': ONGOING,
            status: ONGOING,
            mode: 'onsite',
        };
        const studentAttendanceObject = {
            $set: {
                'students.$[i].status': status,
                'students.$[i].primaryStatus': status,
                'students.$[i].time': timestampNow(),
                'students.$[i].primaryTime': timestampNow(),
                'students.$[i].mode': 'manual',
            },
        };
        const studentAttendanceFilter = {
            arrayFilters: [{ 'i._id': convertToMongoObjectId(studentId) }],
        };
        const studentAttendanceScheduleUpdate = await CourseSchedule.updateMany(
            scheduleQuery,
            studentAttendanceObject,
            studentAttendanceFilter,
        );
        if (studentAttendanceScheduleUpdate && !studentAttendanceScheduleUpdate.modifiedCount)
            return sendResponse(res, 200, false, req.t('UNABLE_TO_PERSISTED_ATTENDANCE'), null);
        logger.info(
            requestLogger,
            'sessionController -> staffStudentAttendance -> Session Staff Student Manual Attendance - End',
        );
        return sendResponse(
            res,
            200,
            true,
            req.t('SESSION_ATTENDANCE_PERSISTED_SUCCESSFULLY'),
            req.t('SESSION_ATTENDANCE_PERSISTED_SUCCESSFULLY'),
        );
    } catch (error) {
        logger.error(error.stack, 'sessionController -> studentAttendance -> error');
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};

exports.updateMissedToCompleteSession = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { schedule_id, staff_id, attendanceStatus, isRevoke } = req.body;
        logger.info('sessionController -> updateMissedToCompleteSession -> Start');
        const convertedScheduleIds =
            schedule_id && schedule_id.length
                ? schedule_id.map((scheduleElement) => convertToMongoObjectId(scheduleElement))
                : [];
        const missedSchedules = await course_schedule
            .find({
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: { $in: convertedScheduleIds },
                isDeleted: false,
                status: isRevoke ? COMPLETED : MISSED,
            })
            .lean();
        if (!missedSchedules.length)
            return sendResult(res, 404, false, req.t('THERE_IS_NO_SESSION_FOUND'), null);
        /* The below code is updating a single missed schedule in a course schedule. */
        if (
            convertedScheduleIds.length === 1 &&
            convertedScheduleIds[0].toString() === missedSchedules[0]._id.toString()
        ) {
            const updateQuery = {};
            if (isRevoke) {
                updateQuery.$set = {
                    status: MISSED,
                    'sessionDetail.attendance_mode': PENDING,
                    'staffs.$[].status': PENDING,
                    'students.$[student].status': PENDING,
                    isMissedToComplete: !isRevoke,
                };
                updateQuery.$unset = {
                    'sessionDetail.start_time': 1,
                    'sessionDetail.stop_time': 1,
                    'sessionDetail.startBy': 1,
                    'students.$[].time': 1,
                    'staffs.$[].time': 1,
                };
            } else {
                if (attendanceStatus === 'present') {
                    updateQuery.$set = {
                        'students.$[].time': missedSchedules[0].scheduleStartDateAndTime,
                    };
                }
                updateQuery.$set = {
                    status: COMPLETED,
                    'sessionDetail.attendance_mode': COMPLETED,
                    'sessionDetail.start_time': missedSchedules[0].scheduleStartDateAndTime,
                    'sessionDetail.stop_time': missedSchedules[0].scheduleEndDateAndTime,
                    'sessionDetail.startBy': staff_id,
                    'staffs.$[].status': 'present',
                    'staffs.$[].time': missedSchedules[0].scheduleStartDateAndTime,
                    'students.$[student].status': attendanceStatus,
                    isMissedToComplete: !isRevoke,
                };
            }
            const updateSingleMissedSchedule = await course_schedule.updateOne(
                { _id: { $in: convertedScheduleIds } },
                updateQuery,
                {
                    arrayFilters: [{ 'student.status': { $nin: [ONDUTY, LEAVE, PERMISSION] } }],
                },
            );
            return !updateSingleMissedSchedule.modifiedCount
                ? sendResponse(res, 400, false, req.t('UNABLE_TO_UPDATE'), null)
                : sendResponse(res, 200, true, req.t('UPDATED_SUCCESSFULLY'), null);
        }
        const bulkOperation = [];
        for (const totalSchedulesElement of missedSchedules) {
            const updateQuery = {
                _id: convertToMongoObjectId(totalSchedulesElement._id),
            };
            const updateOperation = {};
            if (isRevoke) {
                updateOperation.$set = {
                    status: MISSED,
                    'sessionDetail.attendance_mode': PENDING,
                    'staffs.$[].status': PENDING,
                    'students.$[student].status': PENDING,
                    isMissedToComplete: !isRevoke,
                };
                updateOperation.$unset = {
                    'sessionDetail.start_time': 1,
                    'sessionDetail.stop_time': 1,
                    'sessionDetail.startBy': 1,
                    'students.$[].time': 1,
                    'staffs.$[].time': 1,
                };
            } else {
                if (attendanceStatus === 'present') {
                    updateOperation.$set = {
                        'students.$[].time': totalSchedulesElement.scheduleStartDateAndTime,
                    };
                }
                updateOperation.$set = {
                    status: COMPLETED,
                    'sessionDetail.attendance_mode': COMPLETED,
                    'sessionDetail.start_time': totalSchedulesElement.scheduleStartDateAndTime,
                    'sessionDetail.stop_time': totalSchedulesElement.scheduleEndDateAndTime,
                    'sessionDetail.startBy': staff_id,
                    'staffs.$[].status': 'present',
                    'staffs.$[].time': totalSchedulesElement.scheduleStartDateAndTime,
                    'students.$[student].status': attendanceStatus,
                    isMissedToComplete: !isRevoke,
                };
            }
            bulkOperation.push({
                updateOne: {
                    filter: updateQuery,
                    update: updateOperation,
                    arrayFilters: [{ 'student.status': { $nin: [ONDUTY, LEAVE, PERMISSION] } }],
                },
            });
        }
        const bulkUpdate = await course_schedule.bulkWrite(bulkOperation);
        return !bulkUpdate.modifiedCount
            ? sendResponse(res, 400, false, req.t('UNABLE_TO_UPDATE'), null)
            : sendResponse(res, 200, true, req.t('UPDATED_SUCCESSFULLY'), null);
    } catch (error) {
        logger.error('sessionController -> updateMissedToCompleteSession -> Error', error.stack);
        return sendResponse(res, 500, false, req.t('ERROR_CATCH'), error.toString());
    }
};
exports.campusOtpVerification = async (req, res) => {
    try {
        const { scheduleId, studentId, staffId, studentOtp } = req.body;
        const verifiedOtp = await course_schedule
            .findOne(
                {
                    _id: convertToMongoObjectId(scheduleId),
                    status: ONGOING,
                    ...(studentId
                        ? { 'students._id': convertToMongoObjectId(studentId) }
                        : { 'staffs._staff_id': convertToMongoObjectId(staffId) }),
                    campusOtp: studentOtp,
                },
                { _id: 1 },
            )
            .lean();
        if (!verifiedOtp) {
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('Authentication failed'),
                null,
            );
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('Authentication successful'),
            null,
        );
    } catch (error) {
        logger.error('sessionController -> updateMissedToCompleteSession -> Error', error.stack);
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
//get campus student list
exports.campusStudentList = async (req, res) => {
    try {
        const { _id } = req.params;
        const courseScheduledData = await course_schedule
            .findOne(
                {
                    _id: convertToMongoObjectId(_id),
                },
                {
                    merge_status: 1,
                    'merge_with.schedule_id': 1,
                    'students._id': 1,
                    'students.name': 1,
                    'students.status': 1,
                },
            )
            .populate({ path: 'students._id', select: { user_id: 1 } })
            .lean();
        let scheduledStudentList = [];
        if (!courseScheduledData) {
            return sendResponseWithRequest(req, res, 200, false, req.t('No_Data'), null);
        }
        if (courseScheduledData.merge_status) {
            let scheduledId = [];
            scheduledId = courseScheduledData.merge_with.map(
                (scheduledElement) => scheduledElement.schedule_id,
            );
            if (scheduledId.length) {
                let scheduledData = [];
                const mergeScheduledData = await course_schedule
                    .find(
                        {
                            _id: {
                                $in: scheduledId.map((scheduledElement) =>
                                    convertToMongoObjectId(scheduledElement),
                                ),
                            },
                        },
                        {
                            'students._id': 1,
                            'students.name': 1,
                            'students.status': 1,
                        },
                    )
                    .populate({ path: 'students._id', select: { user_id: 1 } })
                    .lean();
                scheduledData = mergeScheduledData.map(
                    (scheduledElement) => scheduledElement.students,
                );
                scheduledData.push(courseScheduledData.students);
                scheduledStudentList = scheduledData.flat();
            }
        } else {
            scheduledStudentList = courseScheduledData.students;
        }
        if (scheduledStudentList && scheduledStudentList.length) {
            const uniqueIds = {};
            scheduledStudentList = scheduledStudentList
                .filter((studentElement) => {
                    if (!uniqueIds[studentElement._id._id]) {
                        uniqueIds[studentElement._id._id] = true;
                        return true;
                    }
                    return false;
                })
                .map((studentElement) => ({
                    _id: studentElement._id._id,
                    user_id: studentElement._id.user_id,
                    name: studentElement.name,
                    status: studentElement.status,
                }));
        }
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('Student List'),
            scheduledStudentList,
        );
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.uploadCampusImageUrl = async (req, res) => {
    try {
        const { file } = req.body;
        const imageUrl = {
            url: file,
            signedUrl: await getOutSideSignedUrl(file),
        };
        return sendResponseWithRequest(req, res, 200, true, req.t('Image url'), imageUrl);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

exports.updateStudentStatus = async (req, res) => {
    try {
        const { _institution_id } = req.headers;
        const { scheduledId, studentId, imageUrl, studentIds, type, staffId } = req.body;
        const isStudent = type === STUDENTS;
        const projection = {
            merge_status: 1,
            'merge_with.schedule_id': 1,
            [isStudent ? 'students' : 'staffs']: 1,
        };
        const courseScheduleData = await course_schedule
            .find(
                {
                    $or: [
                        { _id: convertToMongoObjectId(scheduledId) },
                        { 'merge_with.schedule_id': convertToMongoObjectId(scheduledId) },
                    ],
                },
                projection,
            )
            .lean();
        if (!courseScheduleData.length)
            return sendResponseWithRequest(req, res, 404, true, req.t('SCHEDULE_NOT_FOUND'), null);
        const bulkUpdates = courseScheduleData.map((scheduleElement) => {
            if (isStudent) {
                scheduleElement.students = scheduleElement.students.map((studentElement) => {
                    if (studentIds.includes(studentElement._id.toString())) {
                        const updateElement = {
                            ...studentElement,
                            status: PRESENT,
                            imageUrl,
                            time: new Date(),
                        };
                        if (studentId === studentElement._id.toString()) {
                            updateElement.primaryStatus = PRESENT;
                            updateElement.primaryTime = new Date();
                        }
                        return updateElement;
                    }
                    return studentElement;
                });
            } else {
                scheduleElement.staffs = scheduleElement.staffs.map((staffElement) => {
                    if (staffId === staffElement._staff_id.toString()) {
                        return {
                            ...staffElement,
                            status: PRESENT,
                            time: new Date(),
                        };
                    }
                    return staffElement;
                });
            }
            return {
                updateOne: {
                    filter: { _id: scheduleElement._id },
                    update: {
                        $set: {
                            [isStudent ? 'students' : 'staffs']:
                                scheduleElement[isStudent ? 'students' : 'staffs'],
                        },
                    },
                },
            };
        });
        if (bulkUpdates.length) {
            const bulkWriteResponse = await CourseSchedule.bulkWrite(bulkUpdates);
            if (
                bulkWriteResponse.result.writeErrors.length ||
                bulkWriteResponse.result.writeConcernErrors.length
            ) {
                return sendResponseWithRequest(
                    req,
                    res,
                    410,
                    false,
                    req.t('FAILED_TO_UPDATE'),
                    null,
                );
            }
            await outsideAttendanceSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                scheduledId: convertToMongoObjectId(scheduledId),
                imageUrl,
                ...(isStudent
                    ? { studentId: convertToMongoObjectId(studentId), studentIds }
                    : { staffId: convertToMongoObjectId(staffId) }),
            });
        }
        return sendResponseWithRequest(req, res, 200, true, req.t('UPDATED_SUCCESSFULLY'), null);
    } catch (error) {
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
exports.generateUrl = async (req, res) => {
    try {
        const { url } = req.query;
        const signedUrl = await getOutSideSignedUrl(url);
        return sendResponseWithRequest(req, res, 200, true, req.t('Image url'), signedUrl);
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
