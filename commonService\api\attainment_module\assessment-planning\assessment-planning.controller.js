const assessmentPlanningSchema = require('./assessment-planning.model');
const assessmentManagementSchema = require('../assessment-management/assessment-management.model');
const assessmentCourseProgramSchema = require('../assessment-course-program/assessment-course-program.model');
const assessmentLibrarySchema = require('../assessment-library/assessment-library.model');
const { convertToMongoObjectId, clone } = require('../../../utility/common');
const {
    getCourseList,
    getProgramYearLevelList,
    getUserCourseBasedProgramList,
} = require('./assessment-planning.service');

const getAssessmentPlanningDashboard = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, institutionCalendarId } = query;
        const yearLevelList = await getProgramYearLevelList({
            _institution_id,
            programId,
            institutionCalendarId,
        });
        const programTerms = yearLevelList.length
            ? [...new Set(yearLevelList.map((yearLevelElement) => yearLevelElement.term))]
            : [];
        const termYearLevel = [];
        for (termElement of programTerms) {
            termYearLevel.push({
                term: termElement,
                yearLevel: yearLevelList.filter(
                    (yearLevelElement) =>
                        yearLevelElement.term.toString() === termElement.toString(),
                ),
            });
        }
        return { statusCode: 200, data: termYearLevel };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAssessmentPlanning = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id } = headers;
        const { programId, institutionCalendarId, term } = query;
        let assessmentPlanning = await assessmentPlanningSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                },
                {
                    types: 1,
                },
            )
            .lean();
        if (!assessmentPlanning) {
            const assessmentTypes = await assessmentManagementSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                    },
                    {
                        types: 1,
                    },
                )
                .lean();
            if (!assessmentTypes)
                return { statusCode: 404, message: 'Assessment Setting Not Found' };
            await assessmentPlanningSchema.create({
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                term,
                types: assessmentTypes.types,
            });
            assessmentPlanning = await assessmentPlanningSchema
                .findOne(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                        _program_id: convertToMongoObjectId(programId),
                        term,
                    },
                    {
                        types: 1,
                    },
                )
                .lean();
        }
        return { statusCode: 200, data: assessmentPlanning };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleAssessmentPlanningTypes = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, assessments } = body;
        const assessmentPlanning = await assessmentPlanningSchema
            .findOne(
                {
                    _id: convertToMongoObjectId(_id),
                    _institution_id: convertToMongoObjectId(_institution_id),
                },
                {
                    types: 1,
                    _institution_calendar_id: 1,
                    _program_id: 1,
                    term: 1,
                },
            )
            .lean();
        if (!assessmentPlanning) return { statusCode: 404, message: 'NOT_FOUND' };

        // Need to check with Assessment Planning & Course-Program Area
        const toggleCheckQuery = [];
        for (assessmentElement of assessments)
            if (!assessmentElement.status)
                toggleCheckQuery.push(convertToMongoObjectId(assessmentElement.assessmentId));
        if (toggleCheckQuery.length) {
            const planResponse = {
                'types.typeName': 1,
                'types.subTypes.typeName': 1,
                'types.subTypes.isActive': 1,
                'types.subTypes.assessmentTypes._id': 1,
                'types.subTypes.assessmentTypes.isActive': 1,
                'types.subTypes.assessmentTypes.planning.isActive': 1,
            };
            const courseProgramData = await assessmentCourseProgramSchema.find(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(
                        assessmentPlanning._institution_calendar_id,
                    ),
                    _program_id: convertToMongoObjectId(assessmentPlanning._program_id),
                    term: assessmentPlanning.term,
                },
                planResponse,
            );
            const validationCheckSubTypes = [];
            for (courseProgramElement of courseProgramData) {
                for (typeElement of courseProgramElement.types) {
                    for (subTypeElement of typeElement.subTypes) {
                        if (
                            !validationCheckSubTypes.find(
                                (validationElement) =>
                                    validationElement.typeName === typeElement.typeName &&
                                    validationElement.subTypeName === subTypeElement.typeName,
                            ) &&
                            subTypeElement.isActive &&
                            subTypeElement.assessmentTypes.length
                        ) {
                            for (assessmentElement of subTypeElement.assessmentTypes) {
                                if (
                                    toggleCheckQuery.find(
                                        (assessmentIdElement) =>
                                            assessmentIdElement.toString() ===
                                            assessmentElement._id.toString(),
                                    ) &&
                                    assessmentElement.isActive &&
                                    assessmentElement.planning.length
                                ) {
                                    if (
                                        !validationCheckSubTypes.find(
                                            (validationElement) =>
                                                validationElement.typeName ===
                                                    typeElement.typeName &&
                                                validationElement.subTypeName ===
                                                    subTypeElement.typeName,
                                        )
                                    ) {
                                        validationCheckSubTypes.push({
                                            typeName: typeElement.typeName,
                                            subTypeName: subTypeElement.typeName,
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (validationCheckSubTypes.length)
                return {
                    statusCode: 410,
                    message: 'Assessment Has Planning in Program/Course Level',
                };
        }
        for (typeElement of assessmentPlanning.types) {
            for (subTypeElement of typeElement.subTypes) {
                for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                    const assessmentElement = assessments.find(
                        (assessmentIdElement) =>
                            assessmentIdElement.assessmentId.toString() ===
                            assessmentTypeElement._id.toString(),
                    );
                    if (assessmentElement) {
                        assessmentTypeElement.isActive = assessmentElement.status;
                    }
                }
            }
        }
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _id: convertToMongoObjectId(_id),
            },
            { $set: { types: assessmentPlanning.types } },
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'NOT_UPDATED' };
        return { statusCode: 200, message: 'UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addAssessment = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _id,
            typeId,
            subTypeId,
            assessmentTypeId,
            assessmentName,
            occurring,
            totalMark,
            courses,
        } = body;
        const dbUpdate = {
            $push: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning': {
                    name: assessmentName,
                    occurring,
                    totalMark,
                    courses,
                    isActive: true,
                },
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [
                { 'i._id': convertToMongoObjectId(typeId) },
                { 'j._id': convertToMongoObjectId(subTypeId) },
                { 'k._id': convertToMongoObjectId(assessmentTypeId) },
            ],
        };
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT ADDED' };
        // Adding Details with Planning & Course Program Area
        const assessmentSettingData = await assessmentPlanningSchema.findOne(
            { _id: convertToMongoObjectId(_id) },
            { types: 1, _program_id: 1, _institution_calendar_id: 1, term: 1 },
        );
        let assessmentPushObject;
        let assessmentPushQuery;
        for (typeElement of assessmentSettingData.types) {
            for (subTypesElement of typeElement.subTypes) {
                for (assessmentTypesElement of subTypesElement.assessmentTypes) {
                    const assessmentData = assessmentTypesElement.planning.find(
                        (assTypeElement) => assTypeElement.name === assessmentName,
                    );
                    if (assessmentData) {
                        assessmentPushObject = {
                            $push: {
                                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning': {
                                    _id: convertToMongoObjectId(assessmentData._id),
                                    name: assessmentName,
                                    occurring,
                                    totalMark,
                                    isActive: true,
                                },
                            },
                        };
                        assessmentPushQuery =
                            occurring === 'course'
                                ? {
                                      _institution_id: convertToMongoObjectId(_institution_id),
                                      _institution_calendar_id: convertToMongoObjectId(
                                          assessmentSettingData._institution_calendar_id,
                                      ),
                                      _program_id: convertToMongoObjectId(
                                          assessmentSettingData._program_id,
                                      ),
                                      term: assessmentSettingData.term,
                                      _course_id: {
                                          $in: courses.map((courseId) =>
                                              convertToMongoObjectId(courseId.courseId),
                                          ),
                                      },
                                      level: {
                                          $in: courses.map((courseId) => courseId.level.toString()),
                                      },
                                      type: occurring,
                                  }
                                : {
                                      _institution_id: convertToMongoObjectId(_institution_id),
                                      _institution_calendar_id: convertToMongoObjectId(
                                          assessmentSettingData._institution_calendar_id,
                                      ),
                                      _program_id: convertToMongoObjectId(
                                          assessmentSettingData._program_id,
                                      ),
                                      term: assessmentSettingData.term,
                                      type: occurring,
                                  };
                    }
                }
            }
        }
        if (assessmentPushObject && assessmentPushQuery) {
            await assessmentCourseProgramSchema.updateMany(
                assessmentPushQuery,
                assessmentPushObject,
                dbUpdateFilter,
            );
        }
        return { statusCode: 200, message: 'ASSESSMENT ADDED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateAssessment = async ({ body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const {
            _id,
            typeId,
            subTypeId,
            assessmentTypeId,
            assessmentId,
            assessmentName,
            occurring,
            totalMark,
            courses,
        } = body;
        const dbUpdate = {
            $set: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning.$[l]': {
                    _id: convertToMongoObjectId(assessmentId),
                    name: assessmentName,
                    occurring,
                    totalMark,
                    courses,
                    isActive: true,
                },
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [
                { 'i._id': convertToMongoObjectId(typeId) },
                { 'j._id': convertToMongoObjectId(subTypeId) },
                { 'k._id': convertToMongoObjectId(assessmentTypeId) },
                { 'l._id': convertToMongoObjectId(assessmentId) },
            ],
        };
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT UPDATED' };
        // Adding Details with Planning & Course Program Area
        const assessmentSettingData = await assessmentPlanningSchema.findOne(
            { _id: convertToMongoObjectId(_id) },
            { types: 1, _program_id: 1, _institution_calendar_id: 1, term: 1 },
        );
        let assessmentPushObject;
        let assessmentPushQuery;
        for (typeElement of assessmentSettingData.types) {
            for (subTypesElement of typeElement.subTypes) {
                for (assessmentTypesElement of subTypesElement.assessmentTypes) {
                    const assessmentData = assessmentTypesElement.planning.find(
                        (assTypeElement) => assTypeElement.name === assessmentName,
                    );
                    if (assessmentData) {
                        assessmentPushObject = {
                            $set: {
                                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning.$[l]': {
                                    _id: convertToMongoObjectId(assessmentId),
                                    name: assessmentName,
                                    occurring,
                                    totalMark,
                                    isActive: true,
                                },
                            },
                        };
                        assessmentPushQuery =
                            occurring === 'course'
                                ? {
                                      _institution_id: convertToMongoObjectId(_institution_id),
                                      _institution_calendar_id: convertToMongoObjectId(
                                          assessmentSettingData._institution_calendar_id,
                                      ),
                                      _program_id: convertToMongoObjectId(
                                          assessmentSettingData._program_id,
                                      ),
                                      term: assessmentSettingData.term,
                                      _course_id: {
                                          $in: courses.map((courseId) =>
                                              convertToMongoObjectId(courseId.courseId),
                                          ),
                                      },
                                      level: {
                                          $in: courses.map((courseId) => courseId.level),
                                      },
                                  }
                                : {
                                      _institution_id: convertToMongoObjectId(_institution_id),
                                      _institution_calendar_id: convertToMongoObjectId(
                                          assessmentSettingData._institution_calendar_id,
                                      ),
                                      _program_id: convertToMongoObjectId(
                                          assessmentSettingData._program_id,
                                      ),
                                      term: 'regular',
                                  };
                    }
                }
            }
        }
        if (assessmentPushObject && assessmentPushQuery) {
            const courseProgramAssessment = await assessmentCourseProgramSchema.find(
                assessmentPushQuery,
                { types: 1 },
            );
            const courseProgramBulkRight = [];
            for (courseProgramAssessmentElement of courseProgramAssessment) {
                for (typeElement of courseProgramAssessmentElement.types) {
                    if (typeElement._id.toString() === typeId)
                        for (subTypesElement of typeElement.subTypes) {
                            if (subTypesElement._id.toString() === subTypeId)
                                for (assessmentTypesElement of subTypesElement.assessmentTypes) {
                                    if (
                                        assessmentTypesElement._id.toString() === assessmentTypeId
                                    ) {
                                        const assessmentData = assessmentTypesElement.planning.find(
                                            (assTypeElement) =>
                                                assTypeElement._id.toString() ===
                                                assessmentId.toString(),
                                        );
                                        if (assessmentData) {
                                            courseProgramBulkRight.push({
                                                updateOne: {
                                                    filter: {
                                                        _id: convertToMongoObjectId(
                                                            courseProgramAssessmentElement._id,
                                                        ),
                                                    },
                                                    update: assessmentPushObject,
                                                    arrayFilters: dbUpdateFilter.arrayFilters,
                                                },
                                            });
                                        } else {
                                            courseProgramBulkRight.push({
                                                updateOne: {
                                                    filter: {
                                                        _id: convertToMongoObjectId(
                                                            courseProgramAssessmentElement._id,
                                                        ),
                                                    },
                                                    update: {
                                                        $push: {
                                                            'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning':
                                                                {
                                                                    _id: convertToMongoObjectId(
                                                                        assessmentId,
                                                                    ),
                                                                    name: assessmentName,
                                                                    occurring,
                                                                    totalMark,
                                                                    isActive: true,
                                                                },
                                                        },
                                                    },
                                                    arrayFilters: [
                                                        { 'i._id': convertToMongoObjectId(typeId) },
                                                        {
                                                            'j._id':
                                                                convertToMongoObjectId(subTypeId),
                                                        },
                                                        {
                                                            'k._id':
                                                                convertToMongoObjectId(
                                                                    assessmentTypeId,
                                                                ),
                                                        },
                                                    ],
                                                },
                                            });
                                        }
                                    }
                                }
                        }
                }
            }
            await assessmentCourseProgramSchema.bulkWrite(courseProgramBulkRight);
            if (assessmentSettingData && assessmentSettingData._institution_calendar_id)
                await assessmentLibrarySchema.updateMany(
                    {
                        _institution_id: convertToMongoObjectId(_institution_id),
                        _institution_calendar_id: convertToMongoObjectId(
                            assessmentSettingData._institution_calendar_id,
                        ),
                        _assessment_id: convertToMongoObjectId(assessmentId),
                    },
                    {
                        $set: {
                            assessmentMark: totalMark,
                            assessmentName,
                        },
                    },
                );
        }
        return { statusCode: 200, message: 'ASSESSMENT UPDATED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeAssessment = async ({ query = {}, body = {}, headers = {} }) => {
    try {
        const { _institution_id } = headers;
        const { _id, typeId, subTypeId, assessmentTypeId, assessmentId } = body;
        // Check Details with Planning & Course Program Area
        const courseProgramData = await assessmentCourseProgramSchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                'types.subTypes.assessmentTypes._id': convertToMongoObjectId(assessmentId),
                'types.subTypes.assessmentTypes.planning': { $exists: true, $ne: [] },
            },
            { _id: 1 },
        );
        const libraryData = await assessmentLibrarySchema.findOne(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _assessment_id: convertToMongoObjectId(assessmentId),
                questionMarks: { $exists: true, $ne: [] },
            },
            { _id: 1 },
        );
        if (courseProgramData || libraryData)
            return {
                statusCode: 410,
                message: 'Assessment Has Planned in Program/Course Level & Library',
                data: { courseProgramData, libraryData },
            };

        const dbUpdate = {
            $pull: {
                'types.$[i].subTypes.$[j].assessmentTypes.$[k].planning': {
                    _id: convertToMongoObjectId(assessmentId),
                },
            },
        };
        const dbUpdateFilter = {
            arrayFilters: [
                { 'i._id': convertToMongoObjectId(typeId) },
                { 'j._id': convertToMongoObjectId(subTypeId) },
                { 'k._id': convertToMongoObjectId(assessmentTypeId) },
            ],
        };
        const assessmentSetting = await assessmentPlanningSchema.updateOne(
            { _id: convertToMongoObjectId(_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        if (!assessmentSetting) return { statusCode: 410, message: 'ASSESSMENT NOT REMOVED' };
        // Updating Details with Planning & Course Program Area
        await assessmentCourseProgramSchema.updateMany(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            dbUpdate,
            dbUpdateFilter,
        );
        return { statusCode: 200, message: 'ASSESSMENT REMOVED' };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Getting Course List along with Credit hours in Program Calendar with Year & Level Datas
const courseList = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { programId, institutionCalendarId, term } = query;
        const userProgramCourses = await getUserCourseBasedProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            programId,
            term,
        });
        let courseList = await getCourseList({
            _institution_id,
            programId,
            institutionCalendarId,
        });
        if (userProgramCourses.isAdmin === false && userProgramCourses.courseIds.length)
            courseList = courseList.filter((courseElement) =>
                userProgramCourses.courseIds.find(
                    (courseIdElement) =>
                        courseIdElement.toString() === courseElement._course_id.toString(),
                ),
            );
        if (!courseList.length) return { statusCode: 404, message: 'Course Not Found' };
        const termList = term
            ? [term]
            : [...new Set(courseList.map((courseListElement) => courseListElement.term))];
        const termWiseCourse = [];
        for (termElement of termList) {
            const yearList = [
                ...new Set(
                    courseList
                        .filter((courseListElement) => courseListElement.term === termElement)
                        .map((courseListElement) => courseListElement.year),
                ),
            ];
            const yearLevelCourse = [];
            for (yearElement of yearList) {
                const levelCourse = [];
                const levelList = [
                    ...new Set(
                        courseList
                            .filter(
                                (courseListElement) =>
                                    courseListElement.term === termElement &&
                                    courseListElement.year === yearElement,
                            )
                            .map((courseListLevelElement) => courseListLevelElement.level),
                    ),
                ];
                for (levelElement of levelList) {
                    levelCourse.push({
                        level: levelElement,
                        courses: courseList
                            .filter(
                                (courseElement) =>
                                    courseElement.term === termElement &&
                                    courseElement.year === yearElement &&
                                    courseElement.level === levelElement,
                            )
                            .map((courseElement) => {
                                return {
                                    _course_id: courseElement._course_id,
                                    courses_name: courseElement.courses_name,
                                    courses_number: courseElement.courses_number,
                                    credit_hours: courseElement.credit_hours,
                                    rotation: courseElement.rotation,
                                    rotation_count: courseElement.rotation_count,
                                };
                            }),
                    });
                }
                yearLevelCourse.push({
                    year: yearElement,
                    levels: levelCourse,
                });
            }
            termWiseCourse.push({
                term: termElement,
                year: yearLevelCourse,
            });
        }
        return { statusCode: 200, message: 'DATA_RETRIEVED', data: termWiseCourse };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAssessmentPlanningProgramView = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { programId, institutionCalendarId, term } = query;
        const userProgramCourses = await getUserCourseBasedProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            programId,
            term,
        });
        let courseList = await getCourseList({
            _institution_id,
            programId,
            institutionCalendarId,
        });
        if (!courseList) return { statusCode: 404, message: 'Course Not Found' };
        courseList = clone(courseList.filter((courseElement) => courseElement.term === term));
        if (userProgramCourses.isAdmin === false && userProgramCourses.courseIds.length)
            courseList = courseList.filter((courseElement) =>
                userProgramCourses.courseIds.find(
                    (courseIdElement) =>
                        courseIdElement.toString() === courseElement._course_id.toString(),
                ),
            );
        if (!courseList.length) return { statusCode: 404, message: 'Course Not Found' };
        const assessmentPlanning = await assessmentPlanningSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                },
                {
                    types: 1,
                },
            )
            .lean();

        // Check Details with Planning & Course Program Area
        let courseProgramData = await assessmentCourseProgramSchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                term,
            },
            {
                types: 1,
                level: 1,
                _course_id: 1,
                type: 1,
            },
        );
        if (!courseProgramData) courseProgramData = [];
        if (!assessmentPlanning)
            return { statusCode: 404, message: 'Assessment Planning Not Found' };

        // Check is Assessment Mark is Feed
        const libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                term,
                questionMarks: { $exists: true, $ne: [] },
            },
            { _id: 1, _assessment_id: 1, type: 1, _course_id: 1 },
        );

        const assessmentMode = assessmentPlanning.types.map((assessmentPlanningElement) => {
            return {
                typeName: assessmentPlanningElement.typeName,
                assessmentCount: 0,
                assessmentAssignedCount: 0,
            };
        });
        const yearLevelDetails = (
            await getProgramYearLevelList({
                _institution_id,
                programId,
                institutionCalendarId,
            })
        )
            .filter((yearLevelElement) => yearLevelElement.term === term)
            .map((yearLevelElement) => {
                return {
                    year: yearLevelElement.year,
                    level: yearLevelElement.level,
                    term: yearLevelElement.term,
                    courseIds: [],
                    courseStatus: [],
                    assessmentMode: clone(assessmentMode),
                };
            });
        for (courseElement of courseList) {
            const levelIndex = yearLevelDetails.findIndex(
                (levelElement) => levelElement.level.toString() === courseElement.level,
            );
            if (levelIndex === -1)
                yearLevelDetails.push({
                    year: courseElement.year,
                    level: courseElement.level,
                    rotation: courseElement.rotation,
                    courseIds: [courseElement._course_id.toString()],
                    courseStatus: [
                        {
                            courseId: courseElement._course_id.toString(),
                            status: false,
                        },
                    ],
                    assessmentMode: clone(assessmentMode),
                });
            else {
                yearLevelDetails[levelIndex].courseIds.push(courseElement._course_id.toString());
                yearLevelDetails[levelIndex].courseStatus.push({
                    courseId: courseElement._course_id.toString(),
                    status: false,
                });
            }
        }
        const programAssessment = { assessmentMode: clone(assessmentMode), status: false };
        const individualProgramAssessment = courseProgramData.find(
            (courseProgramElement) => courseProgramElement.type === 'program',
        );
        if (individualProgramAssessment) {
            const typeAssessments = libraryData
                ? libraryData.filter((libraryDataElement) => libraryDataElement.type === 'program')
                : [];
            for (typeElement of individualProgramAssessment.types) {
                const assessmentTypeIndex = programAssessment.assessmentMode.findIndex(
                    (programAssessElement) =>
                        programAssessElement.typeName === typeElement.typeName,
                );
                for (subTypeElement of typeElement.subTypes) {
                    if (subTypeElement.isActive)
                        for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                            if (assessmentTypeElement.isActive)
                                for (planningElement of assessmentTypeElement.planning) {
                                    if (planningElement.isActive) {
                                        if (assessmentTypeIndex !== -1) {
                                            programAssessment.assessmentMode[assessmentTypeIndex]
                                                .assessmentCount++;
                                            programAssessment.assessmentMode[
                                                assessmentTypeIndex
                                            ].status = true;
                                            programAssessment.assessmentMode[
                                                assessmentTypeIndex
                                            ].assessmentAssignedCount += typeAssessments.find(
                                                (libraryDataElement) =>
                                                    libraryDataElement._assessment_id.toString() ===
                                                    planningElement._id.toString(),
                                            )
                                                ? 1
                                                : 0;
                                        }
                                    }
                                }
                        }
                }
            }
        }
        const individualProgramCourseAssessment = courseProgramData.filter(
            (courseProgramElement) => courseProgramElement.type === 'course',
        );
        if (individualProgramCourseAssessment.length) {
            const typeAssessments = libraryData
                ? libraryData.filter((libraryDataElement) => libraryDataElement.type === 'course')
                : [];
            for (courseAssessmentElement of individualProgramCourseAssessment)
                for (typeElement of courseAssessmentElement.types) {
                    for (subTypeElement of typeElement.subTypes) {
                        if (subTypeElement.isActive)
                            for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                                if (assessmentTypeElement.isActive)
                                    for (planningElement of assessmentTypeElement.planning) {
                                        if (planningElement.isActive) {
                                            const levelCourseIndex = yearLevelDetails.findIndex(
                                                (levelElement) =>
                                                    levelElement.level.toString() ===
                                                        courseAssessmentElement.level.toString() &&
                                                    levelElement.courseIds.find(
                                                        (levelCourseIdElement) =>
                                                            levelCourseIdElement.toString() ===
                                                            courseAssessmentElement._course_id.toString(),
                                                    ),
                                            );
                                            if (levelCourseIndex !== -1) {
                                                const courseAssessmentTypeIndex = yearLevelDetails[
                                                    levelCourseIndex
                                                ].assessmentMode.findIndex(
                                                    (levelAssessElement) =>
                                                        levelAssessElement.typeName ===
                                                        typeElement.typeName,
                                                );
                                                if (courseAssessmentTypeIndex !== -1) {
                                                    yearLevelDetails[levelCourseIndex]
                                                        .assessmentMode[courseAssessmentTypeIndex]
                                                        .assessmentCount++;
                                                    yearLevelDetails[
                                                        levelCourseIndex
                                                    ].assessmentMode[
                                                        courseAssessmentTypeIndex
                                                    ].assessmentAssignedCount += typeAssessments.find(
                                                        (libraryDataElement) =>
                                                            libraryDataElement._assessment_id.toString() ===
                                                            planningElement._id.toString(),
                                                    )
                                                        ? 1
                                                        : 0;
                                                    const courseIdIndex = yearLevelDetails[
                                                        levelCourseIndex
                                                    ].courseStatus.findIndex(
                                                        (courseStatusElement) =>
                                                            courseStatusElement.courseId.toString() ===
                                                            courseAssessmentElement._course_id.toString(),
                                                    );
                                                    if (courseIdIndex !== -1)
                                                        yearLevelDetails[
                                                            levelCourseIndex
                                                        ].courseStatus[courseIdIndex].status = true;
                                                }
                                            }
                                        }
                                    }
                            }
                    }
                }
        }

        for (typeElement of assessmentPlanning.types) {
            for (subTypeElement of typeElement.subTypes) {
                if (subTypeElement.isActive)
                    for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                        if (assessmentTypeElement.isActive)
                            for (planningElement of assessmentTypeElement.planning) {
                                if (planningElement.isActive)
                                    if (planningElement.occurring === 'program') {
                                        const assessmentTypeIndex =
                                            programAssessment.assessmentMode.findIndex(
                                                (programAssessElement) =>
                                                    programAssessElement.typeName ===
                                                        typeElement.typeName &&
                                                    programAssessElement.status === false,
                                            );
                                        if (assessmentTypeIndex !== -1)
                                            programAssessment.assessmentMode[assessmentTypeIndex]
                                                .assessmentCount++;
                                    } else {
                                        for (courseElement of planningElement.courses) {
                                            const levelCourseIndex = yearLevelDetails.findIndex(
                                                (levelElement) =>
                                                    levelElement.level.toString() ===
                                                        courseElement.level.toString() &&
                                                    levelElement.courseStatus.find(
                                                        (levelCourseIdElement) =>
                                                            levelCourseIdElement.courseId.toString() ===
                                                                courseElement.courseId.toString() &&
                                                            levelCourseIdElement.status === false,
                                                    ),
                                            );
                                            if (levelCourseIndex !== -1) {
                                                const courseAssessmentTypeIndex = yearLevelDetails[
                                                    levelCourseIndex
                                                ].assessmentMode.findIndex(
                                                    (levelAssessElement) =>
                                                        levelAssessElement.typeName ===
                                                        typeElement.typeName,
                                                );
                                                if (courseAssessmentTypeIndex !== -1)
                                                    yearLevelDetails[levelCourseIndex]
                                                        .assessmentMode[courseAssessmentTypeIndex]
                                                        .assessmentCount++;
                                            }
                                        }
                                    }
                            }
                    }
            }
        }
        return { statusCode: 200, data: { programAssessment, yearLevelDetails } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getAssessmentPlanningLevelView = async ({ headers = {}, query = {} }) => {
    try {
        const { _institution_id, user_id, role_id } = headers;
        const { programId, institutionCalendarId, term, level } = query;
        const userProgramCourses = await getUserCourseBasedProgramList({
            _institution_id,
            user_id,
            role_id,
            institutionCalendarId,
            programId,
            term,
        });
        let courseList = await getCourseList({
            _institution_id,
            programId,
            institutionCalendarId,
        });
        if (!courseList) return { statusCode: 404, message: 'Course Not Found' };
        const assessmentPlanning = await assessmentPlanningSchema
            .findOne(
                {
                    _institution_id: convertToMongoObjectId(_institution_id),
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    _program_id: convertToMongoObjectId(programId),
                    term,
                },
                {
                    types: 1,
                },
            )
            .lean();
        if (!assessmentPlanning)
            return { statusCode: 404, message: 'Assessment Planning Not Found' };
        // Check Details with Planning & Course Program Area
        let courseProgramData = await assessmentCourseProgramSchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                level,
                term,
                type: 'course',
            },
            {
                types: 1,
                level: 1,
                _course_id: 1,
                type: 1,
            },
        );
        if (!courseProgramData) courseProgramData = [];

        // Check is Assessment Mark is Feed
        const libraryData = await assessmentLibrarySchema.find(
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _program_id: convertToMongoObjectId(programId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                term,
                level,
                type: 'course',
                questionMarks: { $exists: true, $ne: [] },
            },
            { _id: 1, _assessment_id: 1, _course_id: 1 },
        );

        const assessmentMode = assessmentPlanning.types.map((assessmentPlanningElement) => {
            return {
                typeName: assessmentPlanningElement.typeName,
                assessmentCount: 0,
                assessmentAssignedCount: 0,
            };
        });
        courseList = clone(
            courseList
                .filter(
                    (courseElement) => courseElement.term === term && courseElement.level === level,
                )
                .map((courseDataElement) => {
                    return {
                        _course_id: courseDataElement._course_id,
                        courses_name: courseDataElement.courses_name,
                        level: courseDataElement.level,
                        rotation: courseDataElement.rotation,
                        rotationCount: courseDataElement.rotation_count,
                        assessmentMode: clone(assessmentMode),
                        status: false,
                    };
                }),
        );
        if (!courseList.length) return { statusCode: 404, message: 'Course Not Found' };
        if (userProgramCourses.isAdmin === false && userProgramCourses.courseIds.length)
            courseList = courseList.filter((courseElement) =>
                userProgramCourses.courseIds.find(
                    (courseIdElement) =>
                        courseIdElement.toString() === courseElement._course_id.toString(),
                ),
            );
        if (courseProgramData.length) {
            for (courseAssessmentElement of courseProgramData)
                for (typeElement of courseAssessmentElement.types) {
                    for (subTypeElement of typeElement.subTypes) {
                        if (subTypeElement.isActive)
                            for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                                if (assessmentTypeElement.isActive)
                                    for (planningElement of assessmentTypeElement.planning) {
                                        if (planningElement.isActive) {
                                            const levelCourseIndex = courseList.findIndex(
                                                (levelElement) =>
                                                    levelElement.level.toString() ===
                                                        courseAssessmentElement.level.toString() &&
                                                    levelElement._course_id ===
                                                        courseAssessmentElement._course_id.toString(),
                                            );
                                            if (levelCourseIndex !== -1) {
                                                const courseAssessmentTypeIndex = courseList[
                                                    levelCourseIndex
                                                ].assessmentMode.findIndex(
                                                    (levelAssessElement) =>
                                                        levelAssessElement.typeName ===
                                                        typeElement.typeName,
                                                );
                                                if (courseAssessmentTypeIndex !== -1) {
                                                    courseList[levelCourseIndex].assessmentMode[
                                                        courseAssessmentTypeIndex
                                                    ].assessmentCount++;
                                                    courseList[levelCourseIndex].status = true;

                                                    courseList[levelCourseIndex].assessmentMode[
                                                        courseAssessmentTypeIndex
                                                    ].assessmentAssignedCount += libraryData
                                                        ? libraryData.find(
                                                              (libraryDataElement) =>
                                                                  courseAssessmentElement._course_id.toString() ===
                                                                      libraryDataElement._course_id.toString() &&
                                                                  libraryDataElement._assessment_id.toString() ===
                                                                      planningElement._id.toString(),
                                                          )
                                                            ? 1
                                                            : 0
                                                        : 0;
                                                }
                                            }
                                        }
                                    }
                            }
                    }
                }
        }

        for (typeElement of assessmentPlanning.types) {
            for (subTypeElement of typeElement.subTypes) {
                if (subTypeElement.isActive)
                    for (assessmentTypeElement of subTypeElement.assessmentTypes) {
                        if (assessmentTypeElement.isActive)
                            for (planningElement of assessmentTypeElement.planning) {
                                if (
                                    assessmentTypeElement.isActive &&
                                    planningElement.occurring === 'course'
                                ) {
                                    for (courseElement of planningElement.courses) {
                                        const levelCourseIndex = courseList.findIndex(
                                            (levelElement) =>
                                                levelElement.level.toString() ===
                                                    courseElement.level.toString() &&
                                                levelElement._course_id ===
                                                    courseElement.courseId.toString() &&
                                                levelElement.status === false,
                                        );
                                        if (levelCourseIndex !== -1) {
                                            const courseAssessmentTypeIndex = courseList[
                                                levelCourseIndex
                                            ].assessmentMode.findIndex(
                                                (levelAssessElement) =>
                                                    levelAssessElement.typeName ===
                                                    typeElement.typeName,
                                            );
                                            if (courseAssessmentTypeIndex !== -1)
                                                courseList[levelCourseIndex].assessmentMode[
                                                    courseAssessmentTypeIndex
                                                ].assessmentCount++;
                                        }
                                    }
                                }
                            }
                    }
            }
        }
        return { statusCode: 200, data: { courseList } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getAssessmentPlanningDashboard,
    getAssessmentPlanning,
    toggleAssessmentPlanningTypes,
    addAssessment,
    updateAssessment,
    removeAssessment,
    courseList,
    getAssessmentPlanningProgramView,
    getAssessmentPlanningLevelView,
};
