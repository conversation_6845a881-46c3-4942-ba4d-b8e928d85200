const {
    response_function,
    convertToMongoObjectId,
    clone,
    responseFunctionWithRequest,
} = require('../utility/common');
const {
    get_list,
    get,
    get_list_populate,
    dsGetAllWithSortAsJSON,
} = require('../base/base_controller');
const {
    INSTITUTION,
    // PROGRAM_CALENDAR,
    // DIGI_COURSE,
    // USER,
    // ROLE,
    ROLE_ASSIGN,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    USER,
    PART_TIME,
    FULL_TIME,
    PROGRAM_CALENDAR,
    STUDENT_GROUP,
    DIGI_SESSION_ORDER,
    DIGI_SESSION_DELIVERY_TYPES,
    <PERSON><PERSON><PERSON>_DEPARTMENT_SUBJECT,
    <PERSON><PERSON>I_COURSE,
    ACADEMIC,
    ADMINISTRATION,
    BOTH,
    COURSE_SCHEDULE,
    PRIMARY,
    STUDENT_GROUP_MODE: { FYD, COURSE, ROTATION },
    GENDER: { MALE, FEMALE },
    EVENT_WHOM: { STUDENT, STAFF },
    PENDING,
    ONGOING,
    COMPLETED,
    ABSENT,
    LEAVE,
    PRESENT,
    LEAVE_TYPE: { ONDUTY, PERMISSION },
    LMS,
    MISSED,
    SCHEDULE_TYPES: { REGULAR, SUPPORT_SESSION },
    STUDENT_CRITERIA_MANIPULATION,
    V1_PROGRAM_LEVEL_STAFFS,
    DC_STAFF,
    SCHEDULE_ATTENDANCE,
    COURSE_WISE,
    EXCLUDE,
    DISCIPLINARY_REMARKS_TYPE: { SCHEDULE_LEVEL },
} = require('../utility/constants');
const {
    lmsNewSetting,
    getLateConfigAndStudentLateAbsent,
    getLateAutoAndManualRange,
    getLateLabelForSchedule,
    checkExclude,
    checkLateExcludeConfigurationForCourseOrStudent,
    calculateAttendancePercentages,
    filterAbsentSchedules,
} = require('../utility/utility.service');
const {
    staffSplit,
    scheduleDateFormateChange,
    courseSessionOrderFilterBasedSchedule,
} = require('../utility/common_functions');
const { SCHEDULE_SESSION_BASED_REPORT } = require('../utility/util_keys');
const studentCriteriaCollection = require('mongoose').model(STUDENT_CRITERIA_MANIPULATION);
const institution = require('mongoose').model(INSTITUTION);
const program_calendar = require('mongoose').model(PROGRAM_CALENDAR);
// const course = require('mongoose').model(DIGI_COURSE);
const user = require('mongoose').model(USER);
const role_assign = require('mongoose').model(ROLE_ASSIGN);
const digi_curriculum = require('mongoose').model(DIGI_CURRICULUM);
const program = require('mongoose').model(DIGI_PROGRAM);
const student_group = require('mongoose').model(STUDENT_GROUP);
const session_order = require('mongoose').model(DIGI_SESSION_ORDER);
const session_delivery_type = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const department_subject = require('mongoose').model(DIGI_DEPARTMENT_SUBJECT);
const course = require('mongoose').model(DIGI_COURSE);
const course_schedule = require('mongoose').model(COURSE_SCHEDULE);
const scheduleAttendanceSchema = require('mongoose').model(SCHEDULE_ATTENDANCE);
const lms = require('mongoose').model(LMS);
const lmsDenialSchema = require('../lms_denial/lms_denial_model');
const disciplinaryRemarksSchema = require('../disciplinary_remarks/disciplinaryRemarks.model');
const {
    allDepartmentSubjectList,
    allSessionDeliveryTypesDatas,
    allCourseList,
    allSessionOrderDatas,
    allProgramCalendarDatas,
    allStudentGroupYesterday,
} = require('../../service/cache.service');
const { redisClient } = require('../../config/redis-connection');
const { simplifyDeliveryGroupName } = require('./reports_analytics_services');

const removeDuplicatesFromArray = function (arr) {
    const new_arr = [];
    arr.forEach((eleArr) => {
        const ind = new_arr.findIndex((ele) => ele.toString() === eleArr.toString());
        if (ind == -1) new_arr.push(eleArr);
    });
    return new_arr;
};
const lmsStudentSettingData = async (_institution_id) => {
    // Leave Setting Data
    let warningAbsenceData = [
        { warning: 'Denial', absence_percentage: 101, warning_message: 'Denial' },
    ];
    const leave_category = await get(
        lms,
        {
            isDeleted: false,
            _institution_id: convertToMongoObjectId(_institution_id),
        },
        { _id: 1, category: 1, student_warning_absence_calculation: 1 },
    );
    if (
        leave_category.status &&
        leave_category.data &&
        leave_category.data.student_warning_absence_calculation &&
        leave_category.data.student_warning_absence_calculation.length !== 0 &&
        leave_category.data.student_warning_absence_calculation.filter(
            (ele) => ele.isDeleted === false,
        ).length !== 0
    )
        warningAbsenceData = clone(
            leave_category.data.student_warning_absence_calculation.filter(
                (ele) => ele.isDeleted === false,
            ),
        );
    warningAbsenceData = clone(
        warningAbsenceData.sort((a, b) => {
            let comparison = 0;
            if (parseInt(a.absence_percentage) > parseInt(b.absence_percentage)) {
                comparison = -1;
            } else if (parseInt(a.absence_percentage) < parseInt(b.absence_percentage)) {
                comparison = 1;
            }
            return comparison;
        }),
    );

    const finaleWarning =
        warningAbsenceData.length !== 1
            ? warningAbsenceData[1] && warningAbsenceData[1].warning
                ? warningAbsenceData[1].warning
                : undefined
            : undefined;
    const denialWarning = warningAbsenceData[0].warning;
    return {
        warningAbsenceData,
        finaleWarning,
        denialWarning,
    };
};

const studentAttendanceReport = (
    courseStudentIds,
    courseScheduled,
    lmsTemp,
    studentCriteriaData,
    lateDurationRange,
    manualLateRange,
    manualLateData,
    lateExclude,
    lateExcludeManagement,
) => {
    const studentData = [];
    for (studentElement of courseStudentIds) {
        const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: courseScheduled[0]._institution_calendar_id,
            courseId: courseScheduled[0]._course_id,
            programId: courseScheduled[0]._program_id,
            levelNo: courseScheduled[0].level_no,
            term: courseScheduled[0].term,
            rotationCount: courseScheduled[0].rotation_count,
            lateExcludeManagement,
            studentId: studentElement,
        }).lateExclude;
        const studentSchedule = courseScheduled.filter(
            (courseScheduleElement) =>
                courseScheduleElement &&
                courseScheduleElement.students.find(
                    (scheduledStudentElement) =>
                        scheduledStudentElement._id.toString() === studentElement.toString() &&
                        scheduledStudentElement.status !== EXCLUDE,
                ),
        );
        const completedSchedule = studentSchedule.filter(
            (scheduleElem) =>
                scheduleElem.status === COMPLETED &&
                scheduleElem.students.find(
                    (studentScheduleElem) =>
                        studentScheduleElem._id.toString() === studentElement.toString(),
                ),
        );
        // Use common utility function to filter absent schedules
        const absentSchedules = filterAbsentSchedules(
            studentSchedule.filter((ele) => ele.status === COMPLETED),
            studentElement,
            [ABSENT, LEAVE],
        );

        // Calculate leave schedules separately
        const leaveSchedules = studentSchedule.filter(
            (ele) =>
                ele.status === COMPLETED &&
                ele.students.find(
                    (ele2) =>
                        ele2._id.toString() === studentElement.toString() && ele2.status === LEAVE,
                ),
        );

        let studentLateAbsent = 0;
        if (!lateExclude && !lateExcludeForStudent) {
            const { studentLateAbsent: updateStudentLateAbsent } =
                getLateConfigAndStudentLateAbsent({
                    lateDurationRange,
                    manualLateRange,
                    manualLateData,
                    scheduleData: courseScheduled,
                    studentElement: { _student_id: studentElement },
                    lateExcludeManagement,
                });
            studentLateAbsent = updateStudentLateAbsent;
        }
        const completedScheduleLength = completedSchedule.length;
        const absentSchedulesLength = absentSchedules.length;
        const leaveSchedulesLength = leaveSchedules.length;

        // Use common utility function to calculate warning percentage
        const { warningPercentage } = calculateAttendancePercentages({
            totalPresent: completedScheduleLength - absentSchedulesLength,
            totalAbsent: absentSchedulesLength,
            totalLeave: leaveSchedulesLength,
            totalPermission: 0,
            totalOnDuty: 0,
            totalCompletedSchedule: completedScheduleLength,
            totalSchedules: studentSchedule.length,
            studentLateAbsent,
        });

        const denialPercentage = warningPercentage;
        // studentElement.absence = denialPercentage.toFixed(2);
        const studentWarningAbsence = clone(lmsTemp.warningAbsenceData);
        studentWarningAbsence[0].manipulationStatus = false;
        if (studentCriteriaData.length && studentSchedule.length) {
            const studentManipulation =
                studentCriteriaData.length &&
                studentCriteriaData.find(
                    (denialStudentElement) =>
                        ((studentSchedule[0].rotation_count && studentSchedule[0].rotation_count > 0
                            ? parseInt(studentSchedule[0].rotation_count) ===
                              denialStudentElement.rotationCount
                            : true) &&
                            denialStudentElement.term === studentSchedule[0].term &&
                            denialStudentElement.courseId.toString() ===
                                studentSchedule[0]._course_id.toString() &&
                            denialStudentElement.typeWiseUpdate === COURSE_WISE) ||
                        (denialStudentElement.term === studentSchedule[0].term &&
                            denialStudentElement.studentId &&
                            denialStudentElement.courseId.toString() ===
                                studentSchedule[0]._course_id.toString() &&
                            denialStudentElement.studentId.toString() ===
                                studentElement.toString()),
                );
            if (
                studentManipulation &&
                studentManipulation.absencePercentage &&
                lmsTemp.warningAbsenceData[0] &&
                lmsTemp.warningAbsenceData[0].percentage &&
                lmsTemp.warningAbsenceData[0].percentage < studentManipulation.absencePercentage
            ) {
                studentWarningAbsence[0].percentage = studentManipulation.absencePercentage;
                studentWarningAbsence[0].manipulationStatus = true;
            }
        }
        const warningData =
            studentWarningAbsence[0].labelName === lmsTemp.denialLabel &&
            studentWarningAbsence[0].manipulationStatus
                ? studentWarningAbsence.find(
                      (manipulatedElement) =>
                          manipulatedElement.percentage &&
                          parseFloat(denialPercentage) > parseFloat(manipulatedElement.percentage),
                  )
                : lmsTemp.warningAbsenceData.find(
                      (warningElement) =>
                          warningElement.percentage &&
                          parseFloat(denialPercentage) > parseFloat(warningElement.percentage),
                  );
        studentData.push({
            student_id: studentElement.toString(),
            warning: warningData ? warningData.labelName : '',
        });
    }
    return studentData;
};

const compare = function (a, b) {
    if (a.s_no < b.s_no) {
        return -1;
    }
    if (a.s_no > b.s_no) {
        return 1;
    }
    return 0;
};
const removeDuplicatesID = function (ids) {
    const new_arr = [];
    ids.forEach((ele) => {
        const ind = new_arr.findIndex((eleNA) => eleNA && eleNA.toString() === ele.toString());
        if (ind == -1) new_arr.push(ele);
    });
    return new_arr;
};

exports.department_subject_list = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const ds_q = {
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
        };
        // const department_subject_data = await dsGetAllWithSortAsJSON(department_subject, ds_q, {});
        const department_subject_data = await allDepartmentSubjectList();
        if (department_subject_data.length == 0)
            return res
                .status(200)
                .send(responseFunctionWithRequest(req, 200, true, req.t('PROGRAM_LIST'), []));

        const department_subject_list = department_subject_data.filter(
            (ele) => ele.program_id.toString() === programId.toString(),
        );

        const other_department_subject_list = department_subject_data.filter(
            (ele) => ele.program_id.toString() !== programId.toString(),
        );
        //return res.send(other_department_subject_list);

        //Filtering Not deleted subjects
        department_subject_list.forEach((eleDS, indexDS) => {
            const subjects = eleDS.subject.filter((ele) => ele.isDeleted === false);
            department_subject_list[indexDS].subject = subjects;
        });

        //Admin Department Counts
        const adminDepartmentsCount = department_subject_list.length;
        let adminSubjects = [];

        //Admin Subject count
        department_subject_list.forEach((eleDS, indexDS) => {
            aSubjects = eleDS.subject.filter((ele) => ele.isDeleted === false);
            adminSubjects = [...adminSubjects, ...aSubjects];
        });
        const adminSubjectsCount = adminSubjects.length;

        //Shared Subject count
        let sharedSubjectsCounts = 0;
        //Shared subject from other program
        department_subject_list.forEach((eleDS, indexDS) => {
            other_department_subject_list.forEach((eleODS) => {
                eleODS.subject.forEach((eleS, indexS) => {
                    if (eleS.isDeleted === false) {
                        const ind = eleS.shared_with.findIndex(
                            (eleSW) =>
                                eleSW.department_id.toString() == eleDS._id.toString() &&
                                eleSW.program_id.toString() == programId.toString(),
                        );
                        if (ind != -1) {
                            sharedSubjectsCounts++;
                            department_subject_list[indexDS].subject.push({
                                isActive: eleS.isActive,
                                isDeleted: eleS.isDeleted,
                                _id: eleS._id,
                                subject_name: eleS.subject_name,
                                shared_with: [],
                                shared_from: [
                                    {
                                        program_id: eleODS.program_id,
                                        program_name: eleODS.program_name,
                                        department_id: eleODS._id,
                                        department_name: eleODS.department_name,
                                    },
                                ],
                            });
                        }
                    }
                });
            });
        });
        //Shared Department count
        let sharedDepartmentCounts = 0;
        //Shared department from other program
        other_department_subject_list.forEach((eleODS) => {
            const ind = eleODS.shared_with.findIndex(
                (ele) => ele.program_id.toString() === programId.toString(),
            );
            if (ind != -1) {
                sharedDepartmentCounts++;
                sharedSubjectsCounts += eleODS.subject.length;

                //If dept shared from other program, We no need to show subject shared details
                const ODSubject = [];
                eleODS.subject.forEach((eleS) => {
                    ODSubject.push({
                        isActive: eleS.isActive,
                        isDeleted: eleS.isDeleted,
                        _id: eleS._id,
                        subject_name: eleS.subject_name,
                        shared_with: [],
                        shared_from: eleS.shared_from,
                    });
                });

                department_subject_list.push({
                    isActive: eleODS.isActive,
                    isDeleted: eleODS.isDeleted,
                    _id: eleODS._id,
                    _institution_id: eleODS._institution_id,
                    program_id: eleODS.program_id,
                    program_name: eleODS.program_name,
                    department_name: eleODS.department_name,
                    subject: ODSubject,
                    shared_with: [],
                    shared_from: [
                        {
                            program_id: eleODS.program_id,
                            program_name: eleODS.program_name,
                        },
                    ],
                    createdAt: eleODS.createdAt,
                    updatedAt: eleODS.updatedAt,
                });
            }
        });
        //adding key shared_from
        department_subject_list.forEach((eleDS, indexDS) => {
            if (!eleDS.shared_from) department_subject_list[indexDS].shared_from = [];
            eleDS.subject.forEach((ele, index) => {
                if (ele._id && !ele.shared_from)
                    department_subject_list[indexDS].subject[index].shared_from = [];
            });
        });
        // Department Subjects
        // const departmentSubject = await get_list(
        //     department_subject,
        //     {
        //         program_id: convertToMongoObjectId(programId),
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         isActive: true,
        //         isDeleted: false,
        //     },
        //     {},
        // );
        const departmentSubject = department_subject_data.filter(
            (departmentElement) => departmentElement.program_id.toString() === programId.toString(),
        );
        console.time('StaffList');
        //Staff Gets
        const staffData = await get_list(
            user,
            {
                status: 'completed',
                user_type: 'staff',
                'academic_allocation.allocation_type': 'primary',
                'academic_allocation._program_id': convertToMongoObjectId(programId),
                isActive: true,
                isDeleted: false,
            },
            { gender: 1, employment: 1, academic_allocation: 1, staff_employment_type: 1 },
        );
        staffData.data = staffData.status ? staffData.data : [];
        console.timeEnd('StaffList');

        // Staff Academic Calculation
        const academicStaffs = staffData.data.filter(
            (ele) => ele.staff_employment_type === ACADEMIC,
        );
        const adminStaffs = staffData.data.filter(
            (ele) => ele.staff_employment_type === ADMINISTRATION,
        );
        const bothStaffs = staffData.data.filter((ele) => ele.staff_employment_type === BOTH);
        const programStaff = {
            total: staffData.data.length,
            academic: await staffSplit(academicStaffs),
            admin: await staffSplit(adminStaffs),
            both: await staffSplit(bothStaffs),
        };
        // Staff Department Calculation
        const programDepartmentStaff = [];
        let departmentStaffData = [];
        for (departmentElement of departmentSubject) {
            const departmentStaffs = [];
            for (staffElement of staffData.data) {
                if (
                    !departmentStaffs.find(
                        (ele) => ele && ele._id.toString() === staffElement._id.toString(),
                    ) &&
                    staffElement.academic_allocation.find(
                        (allocationElement) =>
                            allocationElement._department_id.toString() ===
                            departmentElement._id.toString(),
                    )
                ) {
                    departmentStaffs.push(staffElement);
                }
            }
            programDepartmentStaff.push({
                _id: departmentElement._id,
                department_name: departmentElement.department_name,
                staffs: await staffSplit(departmentStaffs),
            });
            departmentStaffData = [...departmentStaffData, ...departmentStaffs];
        }
        departmentStaffData = departmentStaffData.filter(
            (ele, index) =>
                departmentStaffData.findIndex(
                    (ele2) => ele2._id.toString() === ele._id.toString(),
                ) === index,
        );
        const response = {
            program_name: department_subject_list[0].program_name,
            department_subject: {
                department_subject_list,
                adminDepartmentsCount,
                adminSubjectsCount,
                sharedDepartmentCounts,
                sharedSubjectsCounts,
            },
            program_staffs: programStaff,
            department_staff: programDepartmentStaff,
            department_staff_split: await staffSplit(departmentStaffData),
        };
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_SUBJECT_LIST'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

// Get Department & Subject Overview
exports.program_staff_list = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId },
        } = req;

        // Department Subjects
        // const departmentSubject = await get_list(
        //     department_subject,
        //     {
        //         // program_id: convertToMongoObjectId(programId),
        //         _institution_id: convertToMongoObjectId(_institution_id),
        //         isActive: true,
        //         isDeleted: false,
        //     },
        //     {},
        // );

        console.time('v1ProgramLevelStaffs');
        const v1ProgramLevelStaffs = await redisClient.Client.get(
            `${V1_PROGRAM_LEVEL_STAFFS}-${institutionCalendarId}`,
        );
        console.timeEnd('v1ProgramLevelStaffs');
        if (v1ProgramLevelStaffs) {
            return res.status(200).send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_SUBJECT_COURSE_LIST'),
                    JSON.parse(v1ProgramLevelStaffs).find(
                        (staffElement) =>
                            staffElement.program_id.toString() === programId.toString(),
                    ),
                ),
            );
        }

        const departmentSubject = await allDepartmentSubjectList();
        console.time('staffData');
        //Staff Gets
        const staffData = await get_list(
            user,
            {
                status: 'completed',
                user_type: 'staff',
                'academic_allocation.allocation_type': 'primary',
                'academic_allocation._program_id': convertToMongoObjectId(programId),
                isActive: true,
                isDeleted: false,
            },
            {
                name: 1,
                user_id: 1,
                gender: 1,
                employment: 1,
                academic_allocation: 1,
                staff_employment_type: 1,
            },
        );
        staffData.data = staffData.status ? staffData.data : [];
        console.timeEnd('staffData');

        // Staff Academic Calculation
        const academicStaffs = staffData.data.filter(
            (ele) => ele.staff_employment_type === ACADEMIC,
        );
        const adminStaffs = staffData.data.filter(
            (ele) => ele.staff_employment_type === ADMINISTRATION,
        );
        const bothStaffs = staffData.data.filter((ele) => ele.staff_employment_type === BOTH);
        const programStaff = {
            total: staffData.data.length,
            academic: await staffSplit(academicStaffs),
            admin: await staffSplit(adminStaffs),
            both: await staffSplit(bothStaffs),
        };

        // Staff Department Calculation
        const programDepartmentStaff = [];
        const programDepartmentSubject = departmentSubject.filter(
            (ele) => ele.program_id.toString() === programId.toString(),
        );
        for (departmentElement of programDepartmentSubject) {
            const subjectDetails = [];
            for (subjectElement of departmentElement.subject) {
                const subjectStaffs = [];
                for (staffElement of staffData.data) {
                    if (
                        !subjectStaffs.find(
                            (ele) => ele && ele._id.toString() === staffElement._id.toString(),
                        ) &&
                        staffElement.academic_allocation.find(
                            (allocationElement) =>
                                allocationElement._department_id.toString() ===
                                    departmentElement._id.toString() &&
                                allocationElement._department_subject_id.find(
                                    (ele) => ele.toString() === subjectElement._id.toString(),
                                ),
                        )
                    ) {
                        subjectStaffs.push(staffElement._id);
                    }
                }
                subjectDetails.push({
                    _id: subjectElement._id,
                    subject_name: subjectElement.subject_name,
                    staffs: subjectStaffs,
                });
            }
            programDepartmentStaff.push({
                _id: departmentElement._id,
                department_name: departmentElement.department_name,
                subjects: subjectDetails,
                // staffs: await staffSplit(departmentStaffs),
            });
        }
        console.time('courseSchedule');
        // Course Schedule for Course list
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                // _program_id: convertToMongoObjectId(programId),
                'staffs._staff_id': { $in: staffData.data.map((ele) => ele._id) },
                type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _course_id: 1,
                staffs: 1,
                rotation: 1,
                rotation_count: 1,
                term: 1,
                level_no: 1,
                program_name: 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];
        console.timeEnd('courseSchedule');

        const subjectList = departmentSubject
            .map((ele) =>
                ele.subject
                    .map((ele2) => {
                        return { subject_id: ele2._id.toString(), subject_name: ele2.subject_name };
                    })
                    .flat(),
            )
            .flat();
        const staffList = [];
        for (const staffElement of staffData.data) {
            const staffScheduleList = scheduleData.data.filter((ele) =>
                ele.staffs.find(
                    (ele2) => ele2._staff_id.toString() === staffElement._id.toString(),
                ),
            );
            const courseDatas = [];
            for (staffScheduleElement of staffScheduleList) {
                if (
                    courseDatas.findIndex(
                        (ele) =>
                            ele &&
                            ((staffScheduleElement.rotation === 'yes' &&
                                ele.rotation_count &&
                                ele.rotation_count.toString() ===
                                    staffScheduleElement.rotation_count.toString() &&
                                ele._course_id.toString() ===
                                    staffScheduleElement._course_id.toString() &&
                                ele.term === staffScheduleElement.term &&
                                ele.level_no === staffScheduleElement.level_no) ||
                                (staffScheduleElement.rotation === 'no' &&
                                    ele._course_id.toString() ===
                                        staffScheduleElement._course_id.toString() &&
                                    ele.term === staffScheduleElement.term &&
                                    ele.level_no === staffScheduleElement.level_no)),
                    ) === -1
                ) {
                    courseDatas.push({
                        rotation: staffScheduleElement.rotation,
                        rotation_count: staffScheduleElement.rotation_count,
                        _course_id: staffScheduleElement._course_id,
                        term: staffScheduleElement.term,
                        level_no: staffScheduleElement.level_no,
                    });
                }
            }

            let dSubject = departmentSubject.find(
                (ele) =>
                    ele.program_id.toString() ===
                    staffElement.academic_allocation
                        .find((ele2) => ele2.allocation_type === PRIMARY)
                        ._program_id.toString(),
            );
            const primary_program = dSubject ? dSubject.program_name : null;
            dSubject = departmentSubject.find(
                (ele) =>
                    ele._id.toString() ===
                    staffElement.academic_allocation
                        .find((ele2) => ele2.allocation_type === PRIMARY)
                        ._department_id.toString(),
            );
            const primary_department = dSubject ? dSubject.department_name : null;
            const primary_subjects = subjectList
                .filter((ele) =>
                    staffElement.academic_allocation
                        .find((ele2) => ele2.allocation_type === PRIMARY)
                        ._department_subject_id.find(
                            (ele3) => ele3.toString() === ele.subject_id.toString(),
                        ),
                )
                .map((ele4) => ele4.subject_name);
            staffList.push({
                _id: staffElement._id,
                name: staffElement.name,
                user_id: staffElement.user_id,
                gender: staffElement.gender,
                no_courses: courseDatas.length,
                staff_employment_type: staffElement.staff_employment_type,
                employment_type: staffElement.employment.user_employment_type,
                primary_program,
                primary_department,
                primary_subjects,
                department_ids: staffElement.academic_allocation.map((ele) => ele._department_id),
                subject_ids: staffElement.academic_allocation
                    .map((ele) => ele._department_subject_id)
                    .flat(),
            });
        }
        const response = {
            program_name: departmentSubject.find(
                (ele) => ele.program_id.toString() === programId.toString(),
            ).program_name,
            program_staffs: programStaff,
            department_subject_staff: programDepartmentStaff,
            staff_list: staffList,
        };
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('DEPARTMENT_SUBJECT_COURSE_LIST'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

const courseCreditContactHours = async (
    sessionDeliveryTypes,
    _program_id,
    courseScheduled,
    sessionOrderData,
    credit_hours,
) => {
    const credit_hoursData = [];
    const sessionDeliveryTypesData = sessionDeliveryTypes.filter(
        (ele) => ele._program_id.toString() === _program_id.toString(),
    );
    for (const sessionTypeElement of sessionDeliveryTypesData) {
        const deliveryCredit = credit_hours.find(
            (ele) =>
                ele.type_name === sessionTypeElement.session_name &&
                ele.type_symbol === sessionTypeElement.session_symbol,
        );
        let deliveryTypeData = [];
        let sessionContactHours = 0;
        for (const deliveryTypeElement of sessionTypeElement.delivery_types) {
            deliveryTypeData = [
                ...deliveryTypeData,
                ...courseScheduled
                    .filter(
                        (ele) =>
                            ele &&
                            ele.session &&
                            ele.session.session_type &&
                            ele.session.session_type.toLowerCase() ===
                                deliveryTypeElement.delivery_name.toLowerCase(),
                    )
                    .map((ele2) => {
                        return {
                            _session_id: ele2.session._session_id,
                            status: ele2.status,
                        };
                    }),
            ];
            for (scheduleElement of courseScheduled) {
                for (sessionOrderElement of sessionOrderData) {
                    if (
                        scheduleElement.session.session_type.toLowerCase() ===
                            deliveryTypeElement.delivery_name.toLowerCase() &&
                        scheduleElement.session._session_id.toString() ===
                            sessionOrderElement._id.toString()
                    )
                        sessionContactHours += sessionOrderElement.duration;
                }
            }
        }
        let sessionCompletedHours = 0;
        let endSchedule = [];
        if (deliveryTypeData.length) {
            endSchedule = deliveryTypeData.filter((ele) => ele.status === COMPLETED);
            for (scheduleElement of endSchedule) {
                const sessionOrderElement = sessionOrderData.find(
                    (ele) => ele._id.toString() === scheduleElement._session_id.toString(),
                );
                if (sessionOrderElement) sessionCompletedHours += sessionOrderElement.duration;
            }
        }
        const courseDeliveryWiseDurationPerContactHours =
            deliveryCredit &&
            deliveryCredit.duration_per_contact_hour &&
            parseInt(deliveryCredit.duration_per_contact_hour)
                ? parseInt(deliveryCredit.duration_per_contact_hour)
                : 60;
        const sessionCompletedCredit =
            sessionCompletedHours !== 0
                ? sessionCompletedHours / courseDeliveryWiseDurationPerContactHours
                : 0;

        // const sessionCompletedCredit = sessionCompletedHours !== 0 ? sessionCompletedHours / 60 : 0;
        // const sessionContact = sessionContactHours !== 0 ? sessionContactHours / 60 : 0;
        const sessionContact =
            sessionContactHours !== 0
                ? sessionContactHours / courseDeliveryWiseDurationPerContactHours
                : 0;
        // parseInt(
        //     deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
        // ) *
        // (deliveryCredit && deliveryCredit.contact_hours
        //     ? parseInt(deliveryCredit.contact_hours)
        //     : parseInt(sessionTypeElement.contact_hour_per_credit_hour));
        credit_hoursData.push({
            type_symbol: sessionTypeElement.session_symbol,
            type_name: sessionTypeElement.session_name,
            credit_hours:
                sessionContact / parseInt(sessionTypeElement.contact_hour_per_credit_hour),
            // deliveryCredit && deliveryCredit.credit_hours ? deliveryCredit.credit_hours : 0,
            // sessionContact / parseInt(sessionTypeElement.contact_hour_per_credit_hour),
            completed_credit_hours:
                sessionCompletedCredit /
                    parseInt(sessionTypeElement.contact_hour_per_credit_hour) || 0,
            // sessionCompletedCredit /
            //     (deliveryCredit && deliveryCredit.contact_hours
            //         ? parseInt(deliveryCredit.contact_hours)
            //         : parseInt(sessionTypeElement.contact_hour_per_credit_hour)) || 0,
            // sessionCompletedCredit /
            //     parseInt(sessionTypeElement.contact_hour_per_credit_hour) || 0,
            completed_contact_hours: sessionCompletedCredit || 0,
            contact_hours: sessionContact,
            no_of_sessions: deliveryTypeData.length,
            completed_sessions: endSchedule.length,
        });
    }
    return credit_hoursData;
};

// Staff Overview
exports.programStaffDetails = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, staffId },
        } = req;

        const departmentSubject = await allDepartmentSubjectList();
        //Staff Gets
        const populate = { path: 'address._nationality_id', select: { _id: 1, name: 1 } };
        const { status: userStatus, data: userDatas } = await get_list_populate(
            user,
            {
                _id: convertToMongoObjectId(staffId),
                isActive: true,
                isDeleted: false,
            },
            {
                name: 1,
                user_id: 1,
                email: 1,
                batch: 1,
                gender: 1,
                mobile: 1,
                dob: 1,
                'address.nationality_id': 1,
                'address._nationality_id': 1,
                'address.passport_no': 1,
                'program.program_no': 1,
                'program._program_id': 1,
                office: 1,
                employment: 1,
                academic_allocation: 1,
                staff_employment_type: 1,
                createdAt: 1,
            },
            populate,
        );
        if (!userStatus)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STAFF_NOT_FOUND'),
                        req.t('STAFF_NOT_FOUND'),
                    ),
                );
        const userData = userDatas[0];
        const response = {
            program_name: departmentSubject.find((ele) => ele.program_id.toString() === programId)
                .program_name,
            staff_detail: {
                _id: userData._id,
                name: userData.name,
                email: userData.email,
                user_id: userData.user_id,
                gender: userData.gender,
                dob: userData.dob,
                mobile_no: userData.mobile || '',
                createdAt: userData.createdAt,
                office: userData.office,
                passport_no: userData.address.passport_no,
                nationality_id: userData.address.nationality_id,
                nationality:
                    (userData.address._nationality_id && userData.address._nationality_id.name) ||
                    '',
                staff_employment_type: userData.staff_employment_type,
                employment_type: userData.employment.user_employment_type,
            },
        };
        if (userData.staff_employment_type === ADMINISTRATION)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STAFF_DETAILS_AND_COURSE_LIST'),
                        response,
                    ),
                );
        const subjectDatas = departmentSubject
            .map((ele) =>
                ele.subject.map((ele2) => {
                    return { _id: ele2._id, subject_name: ele2.subject_name };
                }),
            )
            .flat();

        const academicAllocation = [];
        const staffSubjects = [];
        for (academicElement of userData.academic_allocation) {
            const subjectNames = [];
            for (subjectElement of academicElement._department_subject_id) {
                const sub = subjectDatas.find(
                    (ele) => ele._id.toString() === subjectElement._id.toString(),
                );
                subjectNames.push({
                    _subject_id: sub._id,
                    subject_name: sub.subject_name,
                });
                staffSubjects.push(sub._id.toString());
            }
            academicAllocation.push({
                allocation_type: academicElement.allocation_type,
                _program_id: academicElement._program_id,
                program_name:
                    departmentSubject.find(
                        (ele) =>
                            ele.program_id.toString() === academicElement._program_id.toString(),
                    ).program_name || '',
                _department_id: academicElement._department_id,
                department_name: departmentSubject.find(
                    (ele) => ele._id.toString() === academicElement._department_id.toString(),
                ).department_name,
                subjects: subjectNames,
            });
        }

        const sessionDeliveryTypes = await allSessionDeliveryTypesDatas();
        console.time('courseSchedule');
        // Course Schedule for Course list
        const scheduleData = await get_list(
            course_schedule,
            {
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'staffs._staff_id': convertToMongoObjectId(staffId),
                type: 'regular',
                isActive: true,
                isDeleted: false,
            },
            {
                _course_id: 1,
                session: 1,
                subjects: 1,
                staffs: 1,
                term: 1,
                level_no: 1,
                status: 1,
                rotation: 1,
                rotation_count: 1,
            },
        );
        scheduleData.data = scheduleData.status ? scheduleData.data : [];
        console.timeEnd('courseSchedule');
        let courseIds = scheduleData.data.map((ele) => ele._course_id.toString());
        courseIds = [...new Set(courseIds)];
        if (courseIds.length === 0)
            return res
                .status(200)
                .send(
                    responseFunctionWithRequest(
                        req,
                        200,
                        true,
                        req.t('STAFF_DETAILS_AND_COURSE_LIST'),
                        response,
                    ),
                );

        const courseList = (await allCourseList()).filter((courseElement) =>
            courseIds.find(
                (courseIdElement) => courseIdElement.toString() === courseElement._id.toString(),
            ),
        );
        const sessionFlowData = (await allSessionOrderDatas()).filter((sessionOrderElement) =>
            courseIds.find(
                (courseIdElement) =>
                    courseIdElement.toString() === sessionOrderElement._course_id.toString(),
            ),
        );
        const pcData = (await allProgramCalendarDatas()).filter(
            (programElement) =>
                programElement._institution_calendar_id.toString() === institutionCalendarId &&
                programElement.level.some(
                    (levelElement) =>
                        levelElement.course.some((courseElement) =>
                            courseIds.find(
                                (courseIdElement) =>
                                    courseIdElement.toString() ===
                                    courseElement._course_id.toString(),
                            ),
                        ) ||
                        levelElement.rotation_course.some((rotationElement) =>
                            rotationElement.course.some((courseElement) =>
                                courseIds.find(
                                    (courseIdElement) =>
                                        courseIdElement.toString() ===
                                        courseElement._course_id.toString(),
                                ),
                            ),
                        ),
                ),
        );
        const coursesList = [];
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (pcLevel.rotation === 'no')
                    for (levelCourse of pcLevel.course) {
                        if (
                            courseIds.find(
                                (ele) => ele.toString() === levelCourse._course_id.toString(),
                            )
                        ) {
                            const courseSessionOrderData =
                                sessionFlowData.find(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                        levelCourse._course_id.toString(),
                                ).session_flow_data || [];

                            const courseScheduled = clone(
                                scheduleData.data.filter(
                                    (ele) =>
                                        ele._course_id.toString() ===
                                            levelCourse._course_id.toString() &&
                                        ele.term === pcLevel.term &&
                                        ele.level_no === pcLevel.level_no,
                                ),
                            );
                            const sCourse = clone(
                                courseList.find(
                                    (uCourse) =>
                                        uCourse._id.toString() ===
                                        levelCourse._course_id.toString(),
                                ),
                            );

                            // * Session Schedule Based Report Course Session Order filter based on Schedule
                            let sessionOrderData = courseSessionOrderData
                                ? clone(courseSessionOrderData)
                                : { session_flow_data: [] };
                            if (
                                SCHEDULE_SESSION_BASED_REPORT &&
                                SCHEDULE_SESSION_BASED_REPORT === 'true'
                            ) {
                                sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                    courseSessionFlow: courseSessionOrderData,
                                    courseSchedule: courseScheduled,
                                }).session_flow_data;
                            }

                            const sessionTypeDatas = await courseCreditContactHours(
                                sessionDeliveryTypes,
                                pcList._program_id,
                                courseScheduled,
                                sessionOrderData,
                                sCourse.credit_hours,
                            );
                            if (
                                sessionTypeDatas
                                    .map((ele) => ele.contact_hours)
                                    .reduce((accumulator, current) => accumulator + current) !== 0
                            ) {
                                const crsData = clone(levelCourse);
                                crsData.course_name = crsData.courses_name;
                                crsData.course_no = crsData.courses_number;
                                crsData.course_type = crsData.model;
                                crsData.credit_session_details = sessionTypeDatas;
                                crsData.no_session = sessionTypeDatas
                                    .map((ele) => ele.no_of_sessions)
                                    .reduce((accumulator, current) => accumulator + current);
                                crsData.completed_session = sessionTypeDatas
                                    .map((ele) => ele.completed_sessions)
                                    .reduce((accumulator, current) => accumulator + current);
                                crsData.term = pcLevel.term;
                                crsData.program_name = departmentSubject.find(
                                    (ele) =>
                                        ele.program_id.toString() ===
                                        pcLevel._program_id.toString(),
                                ).program_name;
                                crsData._program_id = pcLevel._program_id;
                                crsData.year = pcLevel.year;
                                crsData.level = pcLevel.level_no;
                                crsData.curriculum = pcLevel.curriculum;
                                crsData.rotation = pcLevel.rotation;
                                //version keys added
                                crsData.versionNo = sCourse.versionNo || 1;
                                crsData.versioned = sCourse.versioned || false;
                                crsData.versionName = sCourse.versionName || '';
                                crsData.versionedFrom = sCourse.versionedFrom || null;
                                crsData.versionedCourseIds = sCourse.versionedCourseIds || [];
                                // Shared Details Adding
                                crsData.course_shared =
                                    sCourse._program_id.toString() !==
                                    pcLevel._program_id.toString();
                                crsData.course_shared_program = crsData.course_shared
                                    ? sCourse.course_assigned_details.find(
                                          (cad) =>
                                              cad._program_id.toString() ===
                                              sCourse._program_id.toString(),
                                      ).program_name
                                    : departmentSubject.find(
                                          (ele) =>
                                              ele.program_id.toString() ===
                                              pcLevel._program_id.toString(),
                                      ).program_name;
                                if (
                                    !crsData.course_shared &&
                                    !coursesList.find(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                                crsData._course_id.toString() &&
                                            ele.term === crsData.term,
                                    )
                                )
                                    coursesList.push(crsData);
                            }
                        }
                    }
                else
                    for (rotations of pcLevel.rotation_course) {
                        for (rotationCourse of rotations.course) {
                            if (
                                courseIds.find(
                                    (ele) =>
                                        ele.toString() === rotationCourse._course_id.toString(),
                                )
                            ) {
                                const courseSessionOrderData =
                                    sessionFlowData.find(
                                        (ele) =>
                                            ele._course_id.toString() ===
                                            rotationCourse._course_id.toString(),
                                    ).session_flow_data || [];
                                const courseScheduled = clone(
                                    scheduleData.data.filter(
                                        (ele) =>
                                            ele.rotation &&
                                            ele.rotation === 'yes' &&
                                            ele.rotation_count === rotations.rotation_count &&
                                            ele._course_id.toString() ===
                                                rotationCourse._course_id.toString() &&
                                            ele.term === pcLevel.term &&
                                            ele.level_no === pcLevel.level_no,
                                    ),
                                );
                                const sCourse = clone(
                                    courseList.find(
                                        (uCourse) =>
                                            uCourse._id.toString() ===
                                            rotationCourse._course_id.toString(),
                                    ),
                                );

                                // * Session Schedule Based Report Course Session Order filter based on Schedule
                                let sessionOrderData = courseSessionOrderData
                                    ? clone(courseSessionOrderData)
                                    : { session_flow_data: [] };
                                if (
                                    SCHEDULE_SESSION_BASED_REPORT &&
                                    SCHEDULE_SESSION_BASED_REPORT === 'true'
                                ) {
                                    sessionOrderData = courseSessionOrderFilterBasedSchedule({
                                        courseSessionFlow: courseSessionOrderData,
                                        courseSchedule: courseScheduled,
                                    }).session_flow_data;
                                }

                                const sessionTypeDatas = await courseCreditContactHours(
                                    sessionDeliveryTypes,
                                    pcList._program_id,
                                    courseScheduled,
                                    sessionOrderData,
                                    sCourse.credit_hours,
                                );
                                if (
                                    sessionTypeDatas
                                        .map((ele) => ele.contact_hours)
                                        .reduce((accumulator, current) => accumulator + current) !==
                                    0
                                ) {
                                    const crsData = clone(rotationCourse);
                                    crsData.course_name = crsData.courses_name;
                                    crsData.course_no = crsData.courses_number;
                                    crsData.course_type = crsData.model;
                                    crsData.credit_session_details = sessionTypeDatas;
                                    crsData.no_session = sessionTypeDatas
                                        .map((ele) => ele.no_of_sessions)
                                        .reduce((accumulator, current) => accumulator + current);
                                    crsData.completed_session = sessionTypeDatas
                                        .map((ele) => ele.completed_sessions)
                                        .reduce((accumulator, current) => accumulator + current);
                                    crsData.term = pcLevel.term;
                                    crsData.program_name = departmentSubject.find(
                                        (ele) =>
                                            ele.program_id.toString() ===
                                            pcLevel._program_id.toString(),
                                    ).program_name;
                                    crsData._program_id = pcLevel._program_id;
                                    crsData.year = pcLevel.year;
                                    crsData.level = pcLevel.level_no;
                                    crsData.curriculum = pcLevel.curriculum;
                                    crsData.rotation = pcLevel.rotation;
                                    crsData.rotation_count = rotations.rotation_count;
                                    crsData.versionNo = sCourse.versionNo || 1;
                                    crsData.versioned = sCourse.versioned || false;
                                    crsData.versionName = sCourse.versionName || '';
                                    crsData.versionedFrom = sCourse.versionedFrom || null;
                                    crsData.versionedCourseIds = sCourse.versionedCourseIds || [];
                                    // Shared Details Adding
                                    crsData.course_shared =
                                        sCourse._program_id.toString() !==
                                        pcLevel._program_id.toString();
                                    crsData.course_shared_program = crsData.course_shared
                                        ? sCourse.course_assigned_details.find(
                                              (cad) =>
                                                  cad._program_id.toString() ===
                                                  sCourse._program_id.toString(),
                                          ).program_name
                                        : departmentSubject.find(
                                              (ele) =>
                                                  ele.program_id.toString() ===
                                                  pcLevel._program_id.toString(),
                                          ).program_name;
                                    coursesList.push(crsData);
                                }
                            }
                        }
                    }
            }
        }
        response.staff_detail.academic_allocation = academicAllocation;
        response.course_list = coursesList;
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STAFF_DETAILS_AND_COURSE_LIST'),
                    response,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

const getConcatSessionGroup = function (scheduleData) {
    let session = scheduleData.session.delivery_symbol + scheduleData.session.delivery_no;
    let i = 0;
    scheduleData.student_groups.forEach((eleStudG) => {
        eleStudG.session_group.forEach((eleSG, indexSG) => {
            if (i == 0) {
                if (eleStudG.session_group.length == 1) session = session + '-G' + eleSG.group_no;
                else session = session + '-G' + eleSG.group_no + ',';
            } else {
                if (eleStudG.session_group.length - 1 != indexSG)
                    session = session + 'G' + eleSG.group_no + ',';
                else session = session + 'G' + eleSG.group_no;
            }
            i++;
        });
    });
    return session;
};
const getMergeSessionConcat = function (mergeSession, group_mode = '') {
    let session = '';
    mergeSession.forEach((eleMS) => {
        //session += ',' + eleMS.session.delivery_symbol + eleMS.session.delivery_no;
        let i = 0;
        eleMS.student_groups.forEach((eleStudG) => {
            if (eleStudG.gender == MALE) {
                const grp = group_mode === ROTATION ? 'RMG' : 'MG';
                session +=
                    grp +
                    eleStudG.group_no.toString() +
                    '-' +
                    eleMS.session.delivery_symbol +
                    eleMS.session.delivery_no;
            } else {
                const grp = group_mode === ROTATION ? 'RFG' : 'FG';
                session +=
                    grp +
                    eleStudG.group_no.toString() +
                    '-' +
                    eleMS.session.delivery_symbol +
                    eleMS.session.delivery_no;
            }

            eleStudG.session_group.forEach((eleSG, indexSG) => {
                if (i == 0) {
                    if (eleStudG.session_group.length == 1)
                        session = session + '-G' + eleSG.group_no;
                    else session = session + '-G' + eleSG.group_no + ',';
                } else {
                    if (eleStudG.session_group.length - 1 != indexSG)
                        session = session + 'G' + eleSG.group_no + ',';
                    else session = session + 'G' + eleSG.group_no;
                }
                i++;
            });
        });
    });
    return session;
};

const filterDeliveryGroups = ({ isDeliveryChange, groupIdsArray, eleCourseSetting }) => {
    if (!isDeliveryChange) return true;

    if (groupIdsArray.length === 0) return true;

    return groupIdsArray
        .map((groupElement) => groupElement.toString())
        .includes(eleCourseSetting._id.toString());
};

exports.course_sessions = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, type, level },
            query: { rotation_count, groupIds, sessionGroupIds, isDeliveryGroup, hasSupportEvents },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const isDeliveryChange = isDeliveryGroup === 'true';
        const includedSupportEvents = hasSupportEvents === 'true';
        let groupIdsArray = [];
        if (groupIds && isDeliveryChange) {
            try {
                if (Array.isArray(groupIds)) {
                    groupIdsArray = groupIds;
                } else {
                    groupIdsArray = JSON.parse(groupIds);
                }
            } catch (error) {
                groupIdsArray = [groupIds];
            }
        }

        let sessionGroupIdsArray = [];
        if (sessionGroupIds && isDeliveryChange) {
            try {
                if (Array.isArray(sessionGroupIds)) {
                    sessionGroupIdsArray = sessionGroupIds;
                } else {
                    sessionGroupIdsArray = JSON.parse(sessionGroupIds);
                }
            } catch (error) {
                sessionGroupIdsArray = [sessionGroupIds];
            }
        }

        if (isDeliveryChange && groupIdsArray.length === 0 && !includedSupportEvents) {
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, 'Please Select Groups', {
                    course_details: [],
                }),
            );
        }

        const cData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId.toString(),
        );

        const pcData = (await allProgramCalendarDatas()).filter(
            (programElement) =>
                programElement._institution_calendar_id.toString() === institutionCalendarId &&
                programElement.level.some(
                    (levelElement) =>
                        levelElement.level_no.toString() === level.toString() &&
                        levelElement.term.toString() === term.toString() &&
                        (levelElement.course.find(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ) ||
                            levelElement.rotation_course.some((rotationElement) =>
                                rotationElement.course.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() === courseId.toString(),
                                ),
                            )),
                ),
        );
        const programData = cData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
            versionNo: cData.versionNo || 1,
            versioned: cData.versioned || false,
            versionName: cData.versionName || '',
            versionedFrom: cData.versionedFrom || null,
            versionedCourseIds: cData.versionedCourseIds || [],
        };
        //return res.send(pcData);
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (
                    pcLevel.rotation === 'no' &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const crsData = pcLevel.course.find(
                        (ele) => ele._course_id.toString() === courseId.toString(),
                    );
                    if (crsData) {
                        courseResponse.course_name = crsData.courses_name;
                        courseResponse.course_no = crsData.courses_number;
                        courseResponse.course_type = crsData.model;
                        courseResponse.start_date = crsData.start_date;
                        courseResponse.end_date = crsData.end_date;
                        courseResponse.credit_hours = crsData.credit_hours;
                        courseResponse.term = pcLevel.term;
                        // courseResponse.program_name = shared_with.program_name;
                        courseResponse._program_id = pcLevel._program_id;
                        courseResponse.year = pcLevel.year;
                        courseResponse.level = pcLevel.level_no;
                        courseResponse.curriculum = pcLevel.curriculum;
                        courseResponse.rotation = pcLevel.rotation;
                    }
                } else if (
                    rotation_count &&
                    rotation_count !== 0 &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const rotationData = pcLevel.rotation_course.find(
                        (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                    );

                    if (rotationData) {
                        const crsData = rotationData.course.find(
                            (ele) =>
                                ele._course_id && ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            // courseResponse.program_name = shared_with.program_name;
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                            courseResponse.rotation_count = rotationData.rotation_count;
                        }
                    }
                }
            }
        }
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === level &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );

        const groupInd = studentGroupData.groups.findIndex(
            (ele) => ele.term === term && ele.level === level,
        );
        if (groupInd == -1)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('LEVEL_AND_TERM_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        const groups = [];
        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            studentGroupData.groups[groupInd].group_setting.forEach((ele) => {
                ele.groups.forEach((eleMG) => {
                    groups.push({
                        group_id: eleMG._id,
                        group_no: eleMG.group_no,
                        group_name: simplifyDeliveryGroupName(eleMG.group_name, '-'),
                    });
                });
            });
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            const courseInd = studentGroupData.groups[groupInd].courses.findIndex(
                (eleC) => eleC._course_id.toString() === courseId.toString(),
            );
            if (courseInd == -1)
                return res.status(200).send(
                    responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                        course_details: courseResponse,
                    }),
                );

            studentGroupData.groups[groupInd].courses[courseInd].setting
                .filter((eleCourseSetting) =>
                    filterDeliveryGroups({
                        isDeliveryChange,
                        groupIdsArray,
                        eleCourseSetting,
                    }),
                )
                .forEach((eleCourseSetting) => {
                    if (eleCourseSetting.gender == 'male') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'MG-1',
                        });
                    } else if (eleCourseSetting.gender == 'female') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'FG-1',
                        });
                    } else if (eleCourseSetting.gender == BOTH) {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'SG1',
                        });
                    }
                });
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            studentGroupData.groups[groupInd].rotation_group_setting.forEach((ele) => {
                groups.push({
                    group_id: ele._id,
                    group_no: ele.group_no,
                    group_name: simplifyDeliveryGroupName(ele.group_name, '-'),
                });
            });
        }

        console.time('courseSchedule');
        const course_schedule_data = await get_list(
            course_schedule,
            {
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _institution_id: convertToMongoObjectId(_institution_id),
                type: { $in: [REGULAR, SUPPORT_SESSION] },
                term,
                isDeleted: false,
            },
            {
                session: 1,
                start: 1,
                end: 1,
                merge_status: 1,
                status: 1,
                isDeleted: 1,
                isActive: 1,
                _id: 1,
                topic: 1,
                student_groups: 1,
                mode: 1,
                subjects: 1,
                staffs: 1,
                infra_name: 1,
                'sessionDetail.start_time': 1,
                'sessionDetail.stop_time': 1,
                merge_with: 1,
                title: 1,
                type: 1,
                sub_type: 1,
                scheduleStartDateAndTime: 1,
                scheduleEndDateAndTime: 1,
                scheduleStartFrom: 1,
                schedule_date: 1,
                classModeType: 1,
            },
        );
        console.timeEnd('courseSchedule');
        if (!course_schedule_data.status)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        //Groupwise schedule data
        if (groups.length > 0) {
            groups.forEach((eleG, indexG) => {
                if (!groups[indexG].schedule_data) groups[indexG].schedule_data = [];
                if (!groups[indexG].schedule_support_data)
                    groups[indexG].schedule_support_data = [];

                const filteredData = course_schedule_data.data.filter((eleCS) => {
                    const isSupportType = ['support_session', 'event'].includes(eleCS.type);

                    const isGroupMatch = eleCS.student_groups.some((studGroup) =>
                        groupIdsArray
                            .map((id) => id.toString())
                            .includes(studGroup.group_id.toString()),
                    );

                    const isSessionGroupMatch = eleCS.student_groups.some((studGroup) =>
                        studGroup.session_group?.some((sessionG) =>
                            sessionGroupIdsArray
                                .map((sid) => sid.toString())
                                .includes(sessionG.session_group_id.toString()),
                        ),
                    );

                    if (isDeliveryChange && !includedSupportEvents) {
                        return eleCS.student_groups.some((studGroup) => {
                            const groupMatch = groupIdsArray
                                .map((groupElementId) => groupElementId.toString())
                                .includes(studGroup.group_id.toString());

                            const sessionGroupMatch =
                                sessionGroupIdsArray.length > 0
                                    ? studGroup.session_group?.some((sessionG) =>
                                          sessionGroupIdsArray
                                              .map((sessionGroupElementId) =>
                                                  sessionGroupElementId.toString(),
                                              )
                                              .includes(sessionG.session_group_id.toString()),
                                      )
                                    : true;

                            return groupMatch && sessionGroupMatch;
                        });
                    }

                    if (includedSupportEvents && groupIdsArray.length === 0) {
                        return isSupportType;
                    }

                    if (includedSupportEvents && groupIdsArray.length > 0) {
                        return (
                            isSupportType ||
                            eleCS.student_groups.some((studGroup) => {
                                const groupMatch = groupIdsArray
                                    .map((id) => id.toString())
                                    .includes(studGroup.group_id.toString());

                                const sessionGroupMatch =
                                    sessionGroupIdsArray.length > 0
                                        ? studGroup.session_group?.some((sessionG) =>
                                              sessionGroupIdsArray
                                                  .map((sid) => sid.toString())
                                                  .includes(sessionG.session_group_id.toString()),
                                          )
                                        : true;

                                return groupMatch && sessionGroupMatch;
                            })
                        );
                    }
                    return true;
                });

                filteredData.forEach((eleCS) => {
                    const CSStudGrpInd = eleCS.student_groups.findIndex(
                        (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                    );
                    if (CSStudGrpInd != -1) {
                        const objCS = {
                            s_no: eleCS.session.s_no,
                            session: eleCS.session,
                            start: eleCS.start,
                            end: eleCS.end,
                            sessionDetail: eleCS.sessionDetail,
                            merge_status: eleCS.merge_status,
                            status: eleCS.status,
                            isDeleted: eleCS.isDeleted,
                            isActive: eleCS.isActive,
                            _id: eleCS._id,
                            student_groups: eleCS.student_groups.filter((eleCourseSetting) =>
                                isDeliveryChange
                                    ? groupIdsArray
                                          .map((groupElement) => groupElement.toString())
                                          .includes(eleCourseSetting.group_id.toString())
                                    : true,
                            ),
                            mode: eleCS.mode,
                            subjects: eleCS.subjects,
                            staffs: eleCS.staffs,
                            infra_name: eleCS.infra_name,
                            merge_with: eleCS.merge_with,
                            title: eleCS.title,
                            type: eleCS.type,
                            sub_type: eleCS.sub_type,
                            scheduleStartDateAndTime: eleCS.scheduleStartDateAndTime,
                            scheduleEndDateAndTime: eleCS.scheduleEndDateAndTime,
                            scheduleStartFrom: eleCS.scheduleStartFrom,
                            schedule_date: eleCS.schedule_date,
                            classModeType: eleCS.classModeType,
                        };
                        if (eleCS.type === REGULAR) groups[indexG].schedule_data.push(objCS);
                        else if (eleCS.type === SUPPORT_SESSION)
                            groups[indexG].schedule_support_data.push(objCS);
                    }
                });
            });
        }

        //Merge Schedules
        if (groups.length > 0) {
            groups.forEach((eleG, indexG) => {
                if (eleG && eleG.schedule_data) {
                    //Merge Schedule handle
                    const mergedSchedules = clone(
                        eleG.schedule_data.filter((ele) => ele.merge_status === true),
                    );

                    mergedSchedules.forEach((eleMS, indexMS) => {
                        eleMS.merge_with.forEach((eleMW, indexMW) => {
                            const mergedLoc = mergedSchedules.findIndex(
                                (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                            );
                            if (mergedLoc !== -1) {
                                if (!mergedSchedules[indexMS].merge_sessions)
                                    mergedSchedules[indexMS].merge_sessions = [];
                                mergedSchedules[indexMS].merge_sessions.push({
                                    session: mergedSchedules[mergedLoc].session,
                                    student_groups: mergedSchedules[mergedLoc].student_groups,
                                });
                                mergedSchedules.splice(mergedLoc, 1);
                            } else {
                                const csInd = course_schedule_data.data.findIndex(
                                    (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                                );
                                if (csInd !== -1) {
                                    if (!mergedSchedules[indexMS].merge_sessions)
                                        mergedSchedules[indexMS].merge_sessions = [];
                                    mergedSchedules[indexMS].merge_sessions.push({
                                        session: course_schedule_data.data[csInd].session,
                                        student_groups:
                                            course_schedule_data.data[csInd].student_groups,
                                    });
                                }
                            }
                        });
                    });
                    const normalSchedules = clone([
                        ...clone(eleG.schedule_data.filter((ele) => ele.merge_status === false)),
                        ...mergedSchedules,
                    ]);
                    const sortedNormalSchedules = normalSchedules.sort(compare);
                    groups[indexG].normalSchedules = sortedNormalSchedules;
                    groups[indexG].merged = eleG.schedule_data.filter(
                        (ele) => ele.isActive === true && ele.merge_status === true,
                    ).length;
                    groups[indexG].schedule_dataFromDB = groups[indexG].schedule_data;
                    delete groups[indexG].schedule_data;
                }
            });
        }

        // return res.send(groups);
        if (groups.length > 0) {
            groups.forEach((eleG, indexG) => {
                if (eleG.normalSchedules) {
                    eleG.normalSchedules.forEach((eleCS) => {
                        let grp = '';
                        if (eleG.gender === MALE) grp = 'MG' + eleG.group_no;
                        else grp = 'FG' + eleG.group_no;
                        if (!groups[indexG].schedule_data) {
                            groups[indexG].schedule_data = [];
                            if (eleCS.merge_status == true) {
                                let mergeSessionConcat = '';
                                eleCS.merge_with.forEach((eleMW) => {
                                    const csInd = course_schedule_data.data.findIndex(
                                        (eleCourseS) =>
                                            eleCourseS._id.toString() ===
                                            eleMW.schedule_id.toString(),
                                    );
                                    if (csInd != -1) {
                                        mergeSessionConcat =
                                            course_schedule_data.data[csInd].session
                                                .delivery_symbol +
                                            course_schedule_data.data[csInd].session.delivery_no +
                                            ',';
                                    }
                                });
                                groups[indexG].schedule_data.push({
                                    ...eleCS,
                                    delivery_type:
                                        eleCS.session.delivery_symbol +
                                        eleCS.session.delivery_no +
                                        ',' +
                                        mergeSessionConcat,
                                });
                            } else {
                                groups[indexG].schedule_data.push({
                                    ...eleCS,
                                    delivery_type:
                                        eleCS.session.delivery_symbol +
                                        eleCS.session.delivery_no +
                                        ' ' +
                                        eleCS.session.session_topic,
                                });
                            }
                        } else {
                            if (eleCS.merge_status == true) {
                                let mergeSessionConcat = '';
                                eleCS.merge_with.forEach((eleMW) => {
                                    const csInd = course_schedule_data.data.findIndex(
                                        (eleCourseS) =>
                                            eleCourseS._id.toString() ===
                                            eleMW.schedule_id.toString(),
                                    );
                                    if (csInd != -1) {
                                        mergeSessionConcat =
                                            course_schedule_data.data[csInd].session
                                                .delivery_symbol +
                                            course_schedule_data.data[csInd].session.delivery_no +
                                            ',';
                                    }
                                });
                                groups[indexG].schedule_data.push({
                                    ...eleCS,
                                    delivery_type:
                                        eleCS.session.delivery_symbol +
                                        eleCS.session.delivery_no +
                                        ',' +
                                        mergeSessionConcat,
                                });
                            } else {
                                groups[indexG].schedule_data.push({
                                    ...eleCS,
                                    delivery_type:
                                        eleCS.session.delivery_symbol +
                                        eleCS.session.delivery_no +
                                        ' ' +
                                        eleCS.session.session_topic,
                                });
                            }
                        }
                    });
                }
                delete groups[indexG].normalSchedules;
            });
        }

        groups.forEach((eleG, indexG) => {
            //Counts
            if (eleG && eleG.schedule_data) {
                groups[indexG].no_of_sessions = eleG.schedule_dataFromDB
                    ? eleG.schedule_dataFromDB.length
                    : 0;
                groups[indexG].pending = eleG.schedule_dataFromDB.filter(
                    (eleCS) => eleCS.status === PENDING && eleCS.isActive === true,
                ).length;
                groups[indexG].completed = eleG.schedule_dataFromDB.filter(
                    (eleCS) => eleCS.status === COMPLETED && eleCS.isActive === true,
                ).length;
                groups[indexG].missed = eleG.schedule_dataFromDB.filter(
                    (eleCS) => eleCS.status === MISSED && eleCS.isActive === true,
                ).length;
                // groups[indexG].merged = eleG.schedule_data.filter(
                //     (eleCS) => eleCS.merge_status === true && eleCS.isActive === true,
                // ).length;
                groups[indexG].cancelled = eleG.schedule_dataFromDB.filter(
                    (eleCS) => eleCS.isActive === false,
                ).length;

                //Sessions Count
                groups[indexG].sessions = [];
                eleG.schedule_dataFromDB.forEach((eleSD) => {
                    if (eleSD.isActive === true) {
                        const sessionInd = groups[indexG].sessions.findIndex(
                            (eleS) =>
                                eleS.session_type &&
                                eleS.session_type === eleSD.session.session_type,
                        );
                        if (sessionInd != -1) {
                            if (eleSD.status == COMPLETED)
                                groups[indexG].sessions[sessionInd].completed_session += 1;
                            groups[indexG].sessions[sessionInd].total_session += 1;
                        } else {
                            let completed_session = 0;
                            if (eleSD.status == COMPLETED) completed_session = 1;
                            groups[indexG].sessions.push({
                                session_type: eleSD.session.session_type,
                                total_session: 1,
                                completed_session,
                            });
                        }
                    }
                });
            }
            delete eleG.schedule_dataFromDB;
            eleG.schedule_support_data.forEach((eleSD) => {
                if (eleSD.isActive === true && groups[indexG].sessions) {
                    const sessionInd = groups[indexG].sessions.findIndex(
                        (eleS) =>
                            eleS.session_type && eleS.session_type === eleSD.session.session_type,
                    );
                    if (sessionInd != -1) {
                        if (eleSD.status == COMPLETED)
                            groups[indexG].sessions[sessionInd].completed_session += 1;
                        groups[indexG].sessions[sessionInd].total_session += 1;
                    } else {
                        let completed_session = 0;
                        if (eleSD.status == COMPLETED) completed_session = 1;
                        groups[indexG].sessions.push({
                            session_type: eleSD.type,
                            total_session: 1,
                            completed_session,
                        });
                    }
                }
            });
            // Adding Support Session with regular schedule
            if (eleG.schedule_data)
                eleG.schedule_data = [...eleG.schedule_data, ...eleG.schedule_support_data];
            else eleG.schedule_data = eleG.schedule_support_data;
            delete eleG.schedule_support_data;
        });

        return res.status(200).send(
            responseFunctionWithRequest(req, 200, true, req.t('SESSION_STATUS'), {
                course_details: courseResponse,
                session_status: groups,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
exports.attendance_log = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, type, level },
            query: { rotation_count, groupName },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const cData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId.toString(),
        );
        const pcData = (await allProgramCalendarDatas()).filter(
            (programElement) =>
                programElement._institution_calendar_id.toString() === institutionCalendarId &&
                programElement.level.some(
                    (levelElement) =>
                        levelElement.level_no.toString() === level.toString() &&
                        levelElement.term.toString() === term.toString() &&
                        (levelElement.course.find(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ) ||
                            levelElement.rotation_course.some((rotationElement) =>
                                rotationElement.course.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() === courseId.toString(),
                                ),
                            )),
                ),
        );
        const programData = cData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
        };
        //return res.send(pcData);
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (pcLevel.rotation === 'no') {
                    if (pcLevel.term === term && pcLevel.level_no === level) {
                        const crsData = pcLevel.course.find(
                            (ele) => ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            // courseResponse.program_name = shared_with.program_name;
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                        }
                    }
                } else if (rotation_count && rotation_count !== 0) {
                    if (pcLevel.term === term && pcLevel.level_no === level) {
                        const rotationData = pcLevel.rotation_course.find(
                            (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                        );

                        if (rotationData) {
                            const crsData = rotationData.course.find(
                                (ele) =>
                                    ele._course_id &&
                                    ele._course_id.toString() === courseId.toString(),
                            );
                            if (crsData) {
                                courseResponse.course_name = crsData.courses_name;
                                courseResponse.course_no = crsData.courses_number;
                                courseResponse.course_type = crsData.model;
                                courseResponse.start_date = crsData.start_date;
                                courseResponse.end_date = crsData.end_date;
                                courseResponse.credit_hours = crsData.credit_hours;
                                courseResponse.term = pcLevel.term;
                                // courseResponse.program_name = shared_with.program_name;
                                courseResponse._program_id = pcLevel._program_id;
                                courseResponse.year = pcLevel.year;
                                courseResponse.level = pcLevel.level_no;
                                courseResponse.curriculum = pcLevel.curriculum;
                                courseResponse.rotation = pcLevel.rotation;
                                courseResponse.rotation_count = rotationData.rotation_count;
                            }
                        }
                    }
                }
            }
        }

        //return res.send(courseResponse);

        // const studentGroupData = await get(
        //     student_group,
        //     {
        //         isDeleted: false,
        //         //_id: convertToMongoObjectId(_student_group_id),
        //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //         'master._program_id': convertToMongoObjectId(programId),
        //         'groups.term': term,
        //         'groups.level': level,
        //         'groups.courses._course_id': convertToMongoObjectId(courseId),
        //     },
        //     {},
        // );

        // if (!studentGroupData.status)
        //     return res.status(200).send(
        //         response_function(res, 200, true, 'No data', {
        //             course_details: courseResponse,
        //         }),
        //     );
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === level &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const groupInd = studentGroupData.groups.findIndex(
            (ele) => ele.term === term && ele.level === level,
        );
        if (groupInd == -1)
            return res.status(200).send(
                response_function(res, 200, true, req.t('NO_DATA_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        let groups = [];
        const group_mode = studentGroupData.groups[groupInd].group_mode;
        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            studentGroupData.groups[groupInd].group_setting.forEach((ele) => {
                if (ele._id && ele.gender == 'male') {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-MG' + eleMG.group_no,
                            gender: ele.gender,
                        });
                    });
                }
                if (ele._id && ele.gender == 'female') {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-FG' + eleMG.group_no,
                            gender: ele.gender,
                        });
                    });
                }
                if (ele.gender === BOTH) {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-SG' + eleMG.group_no,
                            gender: ele.gender,
                        });
                    });
                }
            });
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            const courseInd = studentGroupData.groups[groupInd].courses.findIndex(
                (eleC) => eleC._course_id.toString() === courseId.toString(),
            );
            if (courseInd == -1)
                return res.status(200).send(
                    response_function(res, 200, true, req.t('COURSE_NOT_FOUND'), {
                        course_details: courseResponse,
                    }),
                );
            studentGroupData.groups[groupInd].courses[courseInd].setting.forEach(
                (eleCourseSetting) => {
                    if (eleCourseSetting.gender == 'male') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'MG-1',
                            gender: eleCourseSetting.gender,
                        });
                    } else if (eleCourseSetting.gender == 'female') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'FG-1',
                            gender: eleCourseSetting.gender,
                        });
                    } else if (eleCourseSetting.gender === BOTH) {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'SG1',
                            gender: eleCourseSetting.gender,
                        });
                    }
                },
            );
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            studentGroupData.groups[groupInd].rotation_group_setting.forEach((ele) => {
                if (ele._id && ele.gender == 'male' && ele.group_no == rotation_count) {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RMG' + ele.group_no,
                        gender: ele.gender,
                    });
                }
                if (ele._id && ele.gender == 'female' && ele.group_no == rotation_count) {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RFG' + ele.group_no,
                        gender: ele.gender,
                    });
                }
                if (ele._id && ele.gender === BOTH && ele.group_no == rotation_count) {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RSG' + ele.group_no,
                        gender: ele.gender,
                    });
                }
            });
        }
        const scheduleQuery = {
            isDeleted: false,
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            type,
            term,
        };
        courseResponse.sgGroups = groups.map((sgElement) => sgElement.group_name);
        if (groupName) {
            groups = groups.filter(
                (groupElement) => groupElement.group_name.toString() === groupName.toString(),
            );
            scheduleQuery['student_groups.group_id'] = convertToMongoObjectId(groups[0].group_id);
        } else if (groups.length > 0) {
            groups = [groups[0]];
            scheduleQuery['student_groups.group_id'] = convertToMongoObjectId(groups[0].group_id);
        }
        console.time('courseSchedule');
        const course_schedule_data = await get_list(course_schedule, scheduleQuery, {
            session: 1,
            _id: 1,
            student_groups: 1,
            students: 1,
            staffs: 1,
            status: 1,
            isActive: 1,
            schedule_date: 1,
            start: 1,
            end: 1,
            merge_status: 1,
            merge_with: 1,
            scheduleStartDateAndTime: 1,
            'sessionDetail.start_time': 1,
        });
        console.timeEnd('courseSchedule');
        if (!course_schedule_data.status)
            return res.status(200).send(
                response_function(res, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        }
        //

        const user_data = await get_list(
            user,
            {
                isDeleted: false,
            },
            { _id: 1, gender: 1, user_id: 1, name: 1 },
        );

        if (!user_data.status)
            return res.status(200).send(
                response_function(res, 200, true, req.t('USER_DATA_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        if (groups.length > 0) {
            groups.forEach((eleG, indexG) => {
                if (eleG.schedule_data) {
                    //Merge Schedule handle
                    const mergedSchedules = clone(
                        eleG.schedule_data.filter((ele) => ele.merge_status === true),
                    );

                    mergedSchedules.forEach((eleMS, indexMS) => {
                        eleMS.merge_with.forEach((eleMW, indexMW) => {
                            const mergedLoc = mergedSchedules.findIndex(
                                (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                            );
                            if (mergedLoc !== -1) {
                                if (!mergedSchedules[indexMS].merge_sessions)
                                    mergedSchedules[indexMS].merge_sessions = [];
                                mergedSchedules[indexMS].merge_sessions.push({
                                    session: mergedSchedules[mergedLoc].session,
                                    student_groups: mergedSchedules[mergedLoc].student_groups,
                                });
                                mergedSchedules.splice(mergedLoc, 1);
                            } else {
                                const csInd = course_schedule_data.data.findIndex(
                                    (ele) => ele._id.toString() === eleMW.schedule_id.toString(),
                                );
                                if (csInd !== -1) {
                                    if (!mergedSchedules[indexMS].merge_sessions)
                                        mergedSchedules[indexMS].merge_sessions = [];
                                    mergedSchedules[indexMS].merge_sessions.push({
                                        session: course_schedule_data.data[csInd].session,
                                        student_groups:
                                            course_schedule_data.data[csInd].student_groups,
                                    });
                                }
                            }
                        });
                    });
                    const normalSchedules = clone([
                        ...clone(eleG.schedule_data.filter((ele) => ele.merge_status === false)),
                        ...mergedSchedules,
                    ]);
                    groups[indexG].schedule_data = clone(normalSchedules);
                }
            });
        }
        //return res.send(groups);
        //Group wise students
        if (groups.length > 0) {
            for (let i = 0; i < groups.length; i++) {
                if (groups[i].schedule_data) {
                    const staff = [];
                    groups[i].schedule_data.forEach((eleSD) => {
                        //Staffs for this Group
                        eleSD.staffs.forEach((eleST) => {
                            const staffInd = staff.findIndex(
                                (eleSTAFF) =>
                                    eleSTAFF._staff_id.toString() === eleST._staff_id.toString(),
                            );
                            if (staffInd == -1)
                                staff.push({
                                    _staff_id: eleST._staff_id,
                                    staff_name: eleST.staff_name,
                                    user_id: eleST.user_id,
                                    status: eleST.status,
                                    mode: eleST.mode,
                                });
                        });
                    });
                    groups[i].staffs = staff;

                    //Students
                    //Student For this group
                    const courseInd = studentGroupData.groups[groupInd].courses.findIndex(
                        (eleC) => eleC._course_id.toString() === courseId.toString(),
                    );
                    if (courseInd == -1)
                        return res.status(200).send(
                            response_function(res, 200, true, req.t('COURSE_NOT_FOUND'), {
                                course_details: courseResponse,
                            }),
                        );

                    let courseSettingGInd = -1;
                    if (studentGroupData.groups[groupInd].group_mode == FYD) {
                        courseSettingGInd = studentGroupData.groups[groupInd].courses[
                            courseInd
                        ].setting.findIndex(
                            (eleSettingG) =>
                                eleSettingG._group_no.toString() ===
                                    groups[i].group_no.toString() &&
                                eleSettingG.gender === groups[i].gender,
                        );
                    } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
                        courseSettingGInd = studentGroupData.groups[groupInd].courses[
                            courseInd
                        ].setting.findIndex(
                            (eleSettingG) =>
                                eleSettingG._group_no === groups[i].group_no &&
                                eleSettingG.gender === groups[i].gender,
                        );
                    } else {
                        courseSettingGInd = studentGroupData.groups[groupInd].courses[
                            courseInd
                        ].setting.findIndex(
                            (eleSettingG) =>
                                eleSettingG._id.toString() === groups[i].group_id.toString(),
                        );
                    }

                    if (courseSettingGInd === -1)
                        return res.status(200).send(
                            response_function(res, 200, true, 'Group not found', {
                                course_details: courseResponse,
                            }),
                        );

                    const session_setting =
                        studentGroupData.groups[groupInd].courses[courseInd].setting[
                            courseSettingGInd
                        ].session_setting;

                    let studentsID = [];
                    session_setting.forEach((eleSS) => {
                        eleSS.groups.forEach((eleSSG) => {
                            studentsID = [...studentsID, ...eleSSG._student_ids];
                        });
                    });
                    //const uniq_ids = [...new Set(studentsID)];
                    let uniq_ids = [];
                    if (studentsID.length > 0) uniq_ids = removeDuplicatesID(studentsID);
                    groups[i].students = uniq_ids;
                    if (groups[i].students.length > 0) {
                        const students_arr = [];
                        groups[i].students.forEach((eleSIDS) => {
                            const userInd = user_data.data.findIndex(
                                (eleUD) => eleUD._id.toString() === eleSIDS.toString(),
                            );
                            if (userInd != -1) {
                                students_arr.push({
                                    name: user_data.data[userInd].name,
                                    _id: user_data.data[userInd]._id,
                                });
                            }
                        });
                        groups[i].students = clone(students_arr);
                    }
                }
            }
        }

        const clone_groups = clone(groups);
        // adding gender
        if (clone_groups.length > 0) {
            clone_groups.forEach((eleG, indexG) => {
                if (eleG.students) {
                    eleG.students.forEach((eleS, indexS) => {
                        const userInd = user_data.data.findIndex(
                            (eleUD) => eleUD._id.toString() === eleS._id.toString(),
                        );
                        if (userInd != -1) {
                            clone_groups[indexG].students[indexS].gender =
                                user_data.data[userInd].gender;
                            clone_groups[indexG].students[indexS].academic_no =
                                user_data.data[userInd].user_id;
                            clone_groups[indexG].students[indexS].name =
                                user_data.data[userInd].name;
                        } else {
                            clone_groups[indexG].students[indexS].gender = '';
                            clone_groups[indexG].students[indexS].academic_no = '';
                        }
                    });
                }
                if (eleG.staffs) {
                    eleG.staffs.forEach((eleS, indexS) => {
                        const userInd = user_data.data.findIndex(
                            (eleUD) => eleUD._id.toString() === eleS._staff_id.toString(),
                        );
                        if (userInd != -1) {
                            clone_groups[indexG].staffs[indexS].gender =
                                user_data.data[userInd].gender;
                            clone_groups[indexG].staffs[indexS].academic_no =
                                user_data.data[userInd].user_id;
                            clone_groups[indexG].staffs[indexS].name = user_data.data[userInd].name;
                            clone_groups[indexG].staffs[indexS].staff_name =
                                user_data.data[userInd].name;
                        } else {
                            clone_groups[indexG].staffs[indexS].gender = '';
                            clone_groups[indexG].staffs[indexS].academic_no = '';
                        }
                    });
                }
            });
        }
        //return res.send(clone_groups);
        //Attendance log
        //Student
        if (clone_groups.length > 0) {
            clone_groups.forEach((eleG, indexG) => {
                if (eleG.students) {
                    eleG.students.forEach((eleS, indexS) => {
                        if (eleG.schedule_data) {
                            eleG.schedule_data.forEach((eleSD) => {
                                const studInd = eleSD.students.findIndex(
                                    (eleSDS) => eleSDS._id.toString() === eleS._id.toString(),
                                );
                                const sessionGroupConCat = getConcatSessionGroup(eleSD);
                                if (!clone_groups[indexG].students[indexS].session_details)
                                    clone_groups[indexG].students[indexS].session_details = [];

                                if (studInd != -1) {
                                    let attendance = PENDING;
                                    if (
                                        eleSD.students[studInd].status == PRESENT ||
                                        eleSD.students[studInd].status == ONDUTY
                                    )
                                        attendance = eleSD.students[studInd].status;
                                    if (eleSD.students[studInd].status == ABSENT)
                                        attendance = eleSD.students[studInd].status;
                                    if (
                                        eleSD.students[studInd].status == LEAVE ||
                                        eleSD.students[studInd].status == PERMISSION
                                    )
                                        attendance = eleSD.students[studInd].status;
                                    clone_groups[indexG].students[indexS].session_details.push({
                                        session: sessionGroupConCat,
                                        s_no: eleSD.session.s_no,
                                        session_no:
                                            eleSD.session.delivery_symbol +
                                            eleSD.session.delivery_no,
                                        schedule_id: eleSD._id,
                                        schedule_status: eleSD.status,
                                        schedule_date: eleSD.schedule_date,
                                        group_status: true,
                                        attendance,
                                        isActive: eleSD.isActive,
                                        students: eleSD.students[studInd],
                                        scheduleStartDateAndTime: eleSD.scheduleStartDateAndTime,
                                    });
                                } else {
                                    clone_groups[indexG].students[indexS].session_details.push({
                                        session: sessionGroupConCat,
                                        s_no: eleSD.session.s_no,
                                        session_no:
                                            eleSD.session.delivery_symbol +
                                            eleSD.session.delivery_no,
                                        schedule_id: eleSD._id,
                                        schedule_status: eleSD.status,
                                        schedule_date: eleSD.schedule_date,
                                        group_status: false,
                                        isActive: eleSD.isActive,
                                        students: eleSD.students[studInd],
                                        scheduleStartDateAndTime: eleSD.scheduleStartDateAndTime,
                                    });
                                }
                            });
                            const session_D = clone_groups[indexG].students[indexS].session_details;
                            clone_groups[indexG].students[indexS].session_details = clone(
                                session_D.sort(compare),
                            );
                        }
                    });
                }
            });
        }
        //return res.send(clone_groups);
        //Staff Attendance log
        if (clone_groups.length > 0) {
            clone_groups.forEach((eleG, indexG) => {
                if (eleG.staffs) {
                    eleG.staffs.forEach((eleS, indexS) => {
                        if (eleG.schedule_data) {
                            eleG.schedule_data.forEach((eleSD) => {
                                const studInd = eleSD.staffs.findIndex(
                                    (eleSDS) =>
                                        eleSDS._staff_id.toString() === eleS._staff_id.toString(),
                                );
                                const sessionGroupConCat = getConcatSessionGroup(eleSD);
                                if (!clone_groups[indexG].staffs[indexS].session_details)
                                    clone_groups[indexG].staffs[indexS].session_details = [];

                                if (studInd != -1) {
                                    let attendance = PENDING;
                                    if (
                                        eleSD.staffs[studInd].status == PRESENT ||
                                        eleSD.staffs[studInd].status == ONDUTY
                                    )
                                        attendance = eleSD.staffs[studInd].status;
                                    if (eleSD.staffs[studInd].status == ABSENT)
                                        attendance = eleSD.staffs[studInd].status;
                                    if (
                                        eleSD.staffs[studInd].status == LEAVE ||
                                        eleSD.staffs[studInd].status == PERMISSION
                                    )
                                        attendance = eleSD.staffs[studInd].status;
                                    clone_groups[indexG].staffs[indexS].session_details.push({
                                        session: sessionGroupConCat,
                                        s_no: eleSD.session.s_no,
                                        session_no:
                                            eleSD.session.delivery_symbol +
                                            eleSD.session.delivery_no,
                                        schedule_id: eleSD._id,
                                        schedule_status: eleSD.status,
                                        schedule_date: eleSD.schedule_date,
                                        group_status: true,
                                        attendance,
                                        isActive: eleSD.isActive,
                                    });
                                } else {
                                    clone_groups[indexG].staffs[indexS].session_details.push({
                                        session: sessionGroupConCat,
                                        s_no: eleSD.session.s_no,
                                        session_no:
                                            eleSD.session.delivery_symbol +
                                            eleSD.session.delivery_no,
                                        schedule_id: eleSD._id,
                                        schedule_status: eleSD.status,
                                        schedule_date: eleSD.schedule_date,
                                        group_status: false,
                                        isActive: eleSD.isActive,
                                    });
                                }
                            });
                            const session_D = clone_groups[indexG].staffs[indexS].session_details;
                            clone_groups[indexG].staffs[indexS].session_details = clone(
                                session_D.sort(compare),
                            );
                        }
                    });
                }
            });
        }
        if (clone_groups.length > 0) {
            clone_groups.forEach((eleG, indexG) => {
                const header = [];
                if (eleG.schedule_data) {
                    eleG.schedule_data.forEach((eleSD) => {
                        let sessionGroupConCat = '';
                        let merge_session_concat = '';
                        if (!eleSD.merge_sessions)
                            sessionGroupConCat = getConcatSessionGroup(eleSD);
                        else {
                            sessionGroupConCat =
                                eleG.group_name + '-' + getConcatSessionGroup(eleSD);
                            merge_session_concat = getMergeSessionConcat(
                                eleSD.merge_sessions,
                                group_mode,
                            );
                        }
                        let delivery_type =
                            eleSD.session.delivery_symbol + eleSD.session.delivery_no;
                        if (merge_session_concat != '') {
                            sessionGroupConCat += ',' + merge_session_concat;

                            eleSD.merge_sessions.forEach((eleMS) => {
                                delivery_type +=
                                    ',' + eleMS.session.delivery_symbol + eleMS.session.delivery_no;
                            });
                        }
                        header.push({
                            session: sessionGroupConCat,
                            s_no: eleSD.session.s_no,
                            session_no: eleSD.session.delivery_no,
                            delivery_symbol: eleSD.session.delivery_symbol,
                            schedule_id: eleSD._id,
                            delivery_type,

                            session_topic: eleSD.session.session_topic,
                            schedule_status: eleSD.status,
                            schedule_date: eleSD.schedule_date,
                            start: eleSD.start,
                            end: eleSD.end,
                            staffs: eleSD.staffs,
                            isActive: eleSD.isActive,
                        });
                    });
                    const sortedHeader = header.sort(compare);
                    clone_groups[indexG].staff_header = sortedHeader;
                    clone_groups[indexG].student_header = sortedHeader;
                }
            });
        }
        //return res.send(clone_groups);
        if (clone_groups.length > 0) {
            //Staff
            clone_groups.forEach((eleG, indexG) => {
                if (eleG.staff_header) {
                    eleG.staff_header.forEach((eleSH, indexSH) => {
                        let present = 0;
                        let absent = 0;
                        let leave = 0;
                        eleG.staffs.forEach((eleSTAFF) => {
                            const sessInd = eleSTAFF.session_details.findIndex(
                                (eleSessD) =>
                                    eleSessD.schedule_id.toString() ===
                                    eleSH.schedule_id.toString(),
                            );
                            if (sessInd != -1) {
                                if (eleSTAFF.session_details[sessInd].group_status == true) {
                                    if (
                                        eleSTAFF.session_details[sessInd].attendance == LEAVE ||
                                        eleSTAFF.session_details[sessInd].attendance == PERMISSION
                                    )
                                        leave++;
                                    if (eleSTAFF.session_details[sessInd].attendance == ABSENT)
                                        absent++;
                                    if (
                                        eleSTAFF.session_details[sessInd].attendance == PRESENT ||
                                        eleSTAFF.session_details[sessInd].attendance == ONDUTY
                                    )
                                        present++;
                                }
                            }
                        });
                        const percentage = (present * 100) / eleG.staffs.length;
                        clone_groups[indexG].staff_header[indexSH].summary = {
                            present,
                            absent,
                            leave,
                            percentage,
                        };
                    });
                }
            });

            //Students
            clone_groups.forEach((eleG, indexG) => {
                if (eleG.schedule_data) delete eleG.schedule_data;

                if (eleG.student_header) {
                    eleG.student_header.forEach((eleSH, indexSH) => {
                        let present = 0;
                        let absent = 0;
                        let leave = 0;
                        let total_student = 0;
                        eleG.students.forEach((eleSTUD) => {
                            const sessInd = eleSTUD.session_details.findIndex(
                                (eleSessD) =>
                                    eleSessD.schedule_id.toString() ===
                                    eleSH.schedule_id.toString(),
                            );
                            if (sessInd != -1) {
                                if (eleSTUD.session_details[sessInd].group_status == true) {
                                    total_student++;
                                    if (
                                        eleSTUD.session_details[sessInd].attendance == LEAVE ||
                                        eleSTUD.session_details[sessInd].attendance == PERMISSION
                                    )
                                        leave++;
                                    if (eleSTUD.session_details[sessInd].attendance == ABSENT)
                                        absent++;
                                    if (
                                        eleSTUD.session_details[sessInd].attendance == PRESENT ||
                                        eleSTUD.session_details[sessInd].attendance == ONDUTY
                                    )
                                        present++;
                                }
                            }
                        });

                        // Use common utility function for attendance percentage calculation
                        const { presentPercentage } = calculateAttendancePercentages({
                            totalPresent: present,
                            totalAbsent: absent,
                            totalLeave: leave,
                            totalPermission: 0,
                            totalOnDuty: 0,
                            totalCompletedSchedule: total_student,
                            totalSchedules: total_student,
                            studentLateAbsent: 0,
                        });

                        const percentage = presentPercentage;

                        clone_groups[indexG].student_header[indexSH].summary = {
                            present,
                            absent,
                            leave,
                            percentage,
                            total_student,
                        };
                    });
                }
            });
        }

        return res.status(200).send(
            response_function(res, 200, true, req.t('ATTENDANCE_LOG'), {
                course_details: courseResponse,
                attendance_log: clone_groups,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(response_function(res, 500, false, req.t('ERROR_CATCH'), error.toString()));
    }
};
exports.student_details = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, type, level },
            query: { rotation_count, groupIds, sessionGroupIds, isDeliveryGroup },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        const isDeliveryChange = isDeliveryGroup === 'true';
        let groupIdsArray = [];
        if (groupIds && isDeliveryChange) {
            try {
                if (Array.isArray(groupIds)) {
                    groupIdsArray = groupIds;
                } else {
                    groupIdsArray = JSON.parse(groupIds);
                }
            } catch (error) {
                groupIdsArray = [groupIds];
            }
        }

        let sessionGroupIdsArray = [];
        if (sessionGroupIds && isDeliveryChange) {
            try {
                if (Array.isArray(sessionGroupIds)) {
                    sessionGroupIdsArray = sessionGroupIds;
                } else {
                    sessionGroupIdsArray = JSON.parse(sessionGroupIds);
                }
            } catch (error) {
                sessionGroupIdsArray = [sessionGroupIds];
            }
        }

        if (isDeliveryChange && groupIdsArray.length === 0)
            return res.status(404).send(
                responseFunctionWithRequest(req, 404, true, 'Please Select Groups', {
                    course_details: [],
                }),
            );

        const cData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId.toString(),
        );
        const pcData = (await allProgramCalendarDatas()).filter(
            (programElement) =>
                programElement._institution_calendar_id.toString() === institutionCalendarId &&
                programElement.level.some(
                    (levelElement) =>
                        levelElement.level_no.toString() === level.toString() &&
                        levelElement.term.toString() === term.toString() &&
                        (levelElement.course.find(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ) ||
                            levelElement.rotation_course.some((rotationElement) =>
                                rotationElement.course.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() === courseId.toString(),
                                ),
                            )),
                ),
        );
        const programData = cData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
            versionNo: cData.versionNo || 1,
            versioned: cData.versioned || false,
            versionName: cData.versionName || '',
            versionedFrom: cData.versionedFrom || null,
            versionedCourseIds: cData.versionedCourseIds || [],
        };
        //return res.send(pcData);
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (
                    pcLevel.rotation === 'no' &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const crsData = pcLevel.course.find(
                        (ele) => ele._course_id.toString() === courseId.toString(),
                    );
                    if (crsData) {
                        courseResponse.course_name = crsData.courses_name;
                        courseResponse.course_no = crsData.courses_number;
                        courseResponse.course_type = crsData.model;
                        courseResponse.start_date = crsData.start_date;
                        courseResponse.end_date = crsData.end_date;
                        courseResponse.credit_hours = crsData.credit_hours;
                        courseResponse.term = pcLevel.term;
                        // courseResponse.program_name = shared_with.program_name;
                        courseResponse._program_id = pcLevel._program_id;
                        courseResponse.year = pcLevel.year;
                        courseResponse.level = pcLevel.level_no;
                        courseResponse.curriculum = pcLevel.curriculum;
                        courseResponse.rotation = pcLevel.rotation;
                    }
                } else if (
                    rotation_count &&
                    rotation_count !== 0 &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const rotationData = pcLevel.rotation_course.find(
                        (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                    );

                    if (rotationData) {
                        const crsData = rotationData.course.find(
                            (ele) =>
                                ele._course_id && ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            // courseResponse.program_name = shared_with.program_name;
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                            courseResponse.rotation_count = rotationData.rotation_count;
                        }
                    }
                }
            }
        }

        // LMS Setting Data
        const lmsData = await lmsNewSetting({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
        });
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo: level,
            term,
            rotationCount: rotation_count,
        });
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo: level,
            term,
            rotationCount: rotation_count,
            lateExcludeManagement,
        });
        const studentCriteriaData = await lmsDenialSchema
            .find({
                programId,
                _institution_calendar_id: institutionCalendarId,
                isActive: true,
                isDeleted: false,
            })
            .sort({ updatedAt: -1 })
            .lean();
        const lmsClonedData = clone(lmsData);
        let courseAbsencePercentage = 0;
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === level &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );

        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        // console.log(studentGroupData.data._id, 'SG');
        const groupInd = studentGroupData.groups.findIndex(
            (ele) => ele.term === term && ele.level === level,
        );
        if (groupInd == -1)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('TERM_LEVEL_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        const courseInd = studentGroupData.groups[groupInd].courses.findIndex(
            (eleC) => eleC._course_id.toString() === courseId.toString(),
        );
        if (courseInd == -1)
            return res.status(200).send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('STUDENT_GROUP_COURSE_NOT_FOUND'),
                    {
                        course_details: courseResponse,
                    },
                ),
            );
        courseAbsencePercentage =
            studentGroupData.groups[groupInd].courses[courseInd].student_absence_percentage;
        const groups = [];
        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            studentGroupData.groups[groupInd].group_setting
                .map((settingElement) => {
                    const filteredGroups = settingElement.groups.filter((studentGroupElement) =>
                        isDeliveryChange && groupIdsArray.length > 0
                            ? groupIdsArray
                                  .map((groupElementId) => groupElementId.toString())
                                  .includes(studentGroupElement._id.toString())
                            : true,
                    );

                    return {
                        ...settingElement,
                        groups: filteredGroups,
                    };
                })
                .forEach((ele) => {
                    if (ele._id && ele.gender == 'male') {
                        ele.groups.forEach((eleMG) => {
                            groups.push({
                                group_id: eleMG._id,
                                group_no: eleMG.group_no,
                                group_name: 'F-MG' + eleMG.group_no,
                                gender: ele.gender,
                            });
                        });
                    }
                    if (ele._id && ele.gender == 'female') {
                        ele.groups.forEach((eleMG) => {
                            groups.push({
                                group_id: eleMG._id,
                                group_no: eleMG.group_no,
                                group_name: 'F-FG' + eleMG.group_no,
                                gender: ele.gender,
                            });
                        });
                    }
                    if (ele._id && ele.gender == BOTH) {
                        ele.groups.forEach((eleMG) => {
                            groups.push({
                                group_id: eleMG._id,
                                group_no: eleMG.group_no,
                                group_name: 'F-SG' + eleMG.group_no,
                                gender: ele.gender,
                            });
                        });
                    }
                });
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            studentGroupData.groups[groupInd].courses[courseInd].setting
                .filter((settingElement) =>
                    isDeliveryChange && groupIdsArray.length > 0
                        ? groupIdsArray
                              .map((groupElementId) => groupElementId.toString())
                              .includes(settingElement._id.toString())
                        : true,
                )
                .forEach((eleCourseSetting) => {
                    if (eleCourseSetting.gender == 'male') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'MG-1',
                            gender: eleCourseSetting.gender,
                        });
                    } else if (eleCourseSetting.gender == 'female') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'FG-1',
                            gender: eleCourseSetting.gender,
                        });
                    } else if (eleCourseSetting.gender === BOTH) {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'SG1',
                            gender: eleCourseSetting.gender,
                        });
                    }
                });
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            studentGroupData.groups[groupInd].rotation_group_setting
                .filter((settingElement) =>
                    isDeliveryChange && groupIdsArray.length > 0
                        ? groupIdsArray
                              .map((groupElementId) => groupElementId.toString())
                              .includes(settingElement._id.toString())
                        : true,
                )
                .forEach((ele) => {
                    if (ele._id && ele.gender == 'male' && ele.group_no == rotation_count) {
                        groups.push({
                            group_id: ele._id,
                            group_no: ele.group_no,
                            group_name: 'RMG' + ele.group_no,
                            gender: ele.gender,
                        });
                    }
                    if (ele._id && ele.gender == 'female' && ele.group_no == rotation_count) {
                        groups.push({
                            group_id: ele._id,
                            group_no: ele.group_no,
                            group_name: 'RFG' + ele.group_no,
                            gender: ele.gender,
                        });
                    }
                    if (ele._id && ele.gender === BOTH && ele.group_no == rotation_count) {
                        groups.push({
                            group_id: ele._id,
                            group_no: ele.group_no,
                            group_name: 'RSG' + ele.group_no,
                            gender: ele.gender,
                        });
                    }
                });
        }
        console.time('courseSchedule');
        const course_schedule_data = await get_list(
            course_schedule,
            {
                isDeleted: false,
                isActive: true,
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                type,
                term,
            },
            {
                session: 1,
                _id: 1,
                student_groups: 1,
                students: 1,
                staffs: 1,
                status: 1,
                _course_id: 1,
                rotation_count: 1,
                term: 1,
                scheduleStartDateAndTime: 1,
                'sessionDetail.start_time': 1,
                _institution_calendar_id: 1,
                _program_id: 1,
                year_no: 1,
                level_no: 1,
            },
        );
        console.timeEnd('courseSchedule');
        if (!course_schedule_data.status)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        //User data
        const user_data = await get_list(
            user,
            {
                isDeleted: false,
                user_type: STUDENT,
            },
            { _id: 1, user_id: 1, gender: 1, name: 1 },
        );
        if (!user_data.status)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('USER_DATA_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    const filteredData = course_schedule_data.data.filter((eleCS) =>
                        isDeliveryChange
                            ? eleCS.student_groups.filter((studGroup) => {
                                  const isGroupMatch = groupIdsArray
                                      .map((groupElementId) => groupElementId.toString())
                                      .includes(studGroup.group_id.toString());

                                  const isSessionGroupMatch = studGroup.session_group?.filter(
                                      (sessionG) =>
                                          sessionGroupIdsArray
                                              .map((sessionGroupElementId) =>
                                                  sessionGroupElementId.toString(),
                                              )
                                              .includes(sessionG.session_group_id.toString()),
                                  );

                                  return (
                                      isGroupMatch &&
                                      (sessionGroupIdsArray.length > 0 ? isSessionGroupMatch : true)
                                  );
                              })
                            : true,
                    );
                    filteredData.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    const filteredData = course_schedule_data.data.filter((eleCS) =>
                        isDeliveryChange
                            ? eleCS.student_groups.filter((studGroup) => {
                                  const isGroupMatch = groupIdsArray
                                      .map((groupElementId) => groupElementId.toString())
                                      .includes(studGroup.group_id.toString());

                                  const isSessionGroupMatch = studGroup.session_group?.filter(
                                      (sessionG) =>
                                          sessionGroupIdsArray
                                              .map((sessionGroupElementId) =>
                                                  sessionGroupElementId.toString(),
                                              )
                                              .includes(sessionG.session_group_id.toString()),
                                  );

                                  return (
                                      isGroupMatch &&
                                      (sessionGroupIdsArray.length > 0 ? isSessionGroupMatch : true)
                                  );
                              })
                            : true,
                    );

                    filteredData.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );

                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    const filteredData = course_schedule_data.data.filter((eleCS) =>
                        isDeliveryChange
                            ? eleCS.student_groups.filter((studGroup) => {
                                  const isGroupMatch = groupIdsArray
                                      .map((groupElementId) => groupElementId.toString())
                                      .includes(studGroup.group_id.toString());

                                  const isSessionGroupMatch = studGroup.session_group?.filter(
                                      (sessionG) =>
                                          sessionGroupIdsArray
                                              .map((sessionGroupElementId) =>
                                                  sessionGroupElementId.toString(),
                                              )
                                              .includes(sessionG.session_group_id.toString()),
                                  );

                                  return (
                                      isGroupMatch &&
                                      (sessionGroupIdsArray.length > 0 ? isSessionGroupMatch : true)
                                  );
                              })
                            : true,
                    );
                    filteredData.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        }
        //return res.send(groups);
        //Group wise students
        if (groups.length > 0) {
            for (let i = 0; i < groups.length; i++) {
                if (!groups[i].students) groups[i].students = [];
                const courseInd = studentGroupData.groups[groupInd].courses.findIndex(
                    (eleC) => eleC._course_id.toString() === courseId.toString(),
                );
                if (courseInd == -1)
                    return res.status(200).send(
                        responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                            course_details: courseResponse,
                        }),
                    );
                //Match students with student groups group and push to particular group
                let studentsID = [];
                let courseSettingGInd = -1;
                if (studentGroupData.groups[groupInd].group_mode == FYD) {
                    courseSettingGInd = studentGroupData.groups[groupInd].courses[
                        courseInd
                    ].setting.findIndex(
                        (eleSettingG) =>
                            eleSettingG._group_no.toString() === groups[i].group_no.toString() &&
                            eleSettingG.gender === groups[i].gender,
                    );
                } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
                    courseSettingGInd = studentGroupData.groups[groupInd].courses[
                        courseInd
                    ].setting.findIndex(
                        (eleSettingG) =>
                            eleSettingG._id.toString() === groups[i].group_id.toString(),
                    );
                } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
                    courseSettingGInd = studentGroupData.groups[groupInd].courses[
                        courseInd
                    ].setting.findIndex(
                        (eleSettingG) =>
                            eleSettingG._group_no === groups[i].group_no &&
                            eleSettingG.gender === groups[i].gender,
                    );
                }
                if (courseSettingGInd == -1)
                    return res.status(200).send(
                        responseFunctionWithRequest(
                            req,
                            200,
                            true,
                            req.t('COURSE_SETTINGS_NOT_FOUND'),
                            {
                                course_details: courseResponse,
                            },
                        ),
                    );
                const session_setting =
                    studentGroupData.groups[groupInd].courses[courseInd].setting[courseSettingGInd]
                        .session_setting;

                session_setting.forEach((sessionSettingElement) => {
                    sessionSettingElement.groups
                        .filter((groupElement) =>
                            isDeliveryChange && sessionGroupIdsArray.length > 0
                                ? sessionGroupIdsArray
                                      .map((sessionGroupElementId) =>
                                          sessionGroupElementId.toString(),
                                      )
                                      .includes(groupElement._id.toString())
                                : true,
                        )
                        .forEach((groupElement) => {
                            if (groupElement._student_ids.length > 0)
                                studentsID = [...studentsID, ...groupElement._student_ids];
                        });
                });
                groups[i].students = studentsID;
                //Students
                if (groups[i].students.length > 0) {
                    const students_arr = [];
                    const newArr = removeDuplicatesFromArray(groups[i].students);
                    newArr.forEach((eleNA) => {
                        const userInd = user_data.data.findIndex(
                            (eleUD) => eleUD._id.toString() === eleNA.toString(),
                        );

                        if (userInd != -1) {
                            students_arr.push({
                                _id: user_data.data[userInd]._id,
                                name: user_data.data[userInd].name,
                                gender: user_data.data[userInd].gender,
                                academic_no: user_data.data[userInd].user_id,
                            });
                        }
                    });
                    groups[i].students = students_arr;
                }
            }
        }

        const clonedGroupsArray = JSON.parse(JSON.stringify(groups));
        if (clonedGroupsArray.length > 0) {
            clonedGroupsArray.forEach((eleG, indexG) => {
                if (eleG.students) {
                    //Warning Calculations
                    let lmsTemp = lmsData;
                    const courseStudentIds = eleG.students.map((ele) => ele._id).flat();
                    if (courseAbsencePercentage && courseAbsencePercentage !== 0) {
                        lmsTemp.warningAbsenceData[0].percentage = courseAbsencePercentage;
                    } else {
                        lmsTemp = lmsClonedData;
                    }
                    let studentReport = [];
                    if (eleG.schedule_data) {
                        studentReport = studentAttendanceReport(
                            courseStudentIds,
                            eleG.schedule_data,
                            lmsTemp,
                            studentCriteriaData,
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            lateExclude,
                            lateExcludeManagement,
                        );
                    }

                    eleG.students.forEach((eleStud, indexStud) => {
                        let total_session = 0;
                        let completed_session = 0;
                        let warningName = '';
                        if (studentReport.length > 0) {
                            warningName =
                                studentReport.find(
                                    (ele) => ele.student_id.toString() === eleStud._id.toString(),
                                )?.warning || '';
                        }
                        let attendance_percentage = 0;
                        let present = 0;
                        let absentCount = 0;
                        let permissionCount = 0;
                        let leaveCount = 0;
                        let ondutyCount = 0;
                        let studentLateAbsent = 0;
                        if (eleG.schedule_data) {
                            eleG.schedule_data.forEach((eleSD) => {
                                const studInd = eleSD.students.findIndex(
                                    (studentELement) =>
                                        studentELement._id.toString() === eleStud._id.toString() &&
                                        studentELement.status !== EXCLUDE,
                                );
                                if (studInd != -1) {
                                    total_session++;
                                    //console.log(eleSD.status, eleSD._id);
                                    if (eleSD.status == COMPLETED) {
                                        completed_session++;
                                        // if (
                                        //     eleSD.students[studInd].status == PRESENT ||
                                        //     eleSD.students[studInd].status == ONDUTY
                                        // )
                                        //     present++;
                                        switch (eleSD.students[studInd].status) {
                                            case PRESENT:
                                                present++;
                                                break;
                                            case ONDUTY:
                                                ondutyCount++;
                                                break;
                                            case PERMISSION:
                                                permissionCount++;
                                                break;
                                            case LEAVE:
                                                leaveCount++;
                                                break;
                                            case ABSENT:
                                                absentCount++;
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                }
                            });
                            const lateExcludeForStudent =
                                checkLateExcludeConfigurationForCourseOrStudent({
                                    _institution_calendar_id: institutionCalendarId,
                                    courseId,
                                    programId,
                                    levelNo: level,
                                    term,
                                    rotationCount: rotation_count,
                                    lateExcludeManagement,
                                    studentId: eleStud._id,
                                }).lateExclude;
                            if (!lateExclude && !lateExcludeForStudent) {
                                const { studentLateAbsent: updatedStudentLateAbsent } =
                                    getLateConfigAndStudentLateAbsent({
                                        lateDurationRange,
                                        manualLateRange,
                                        manualLateData,
                                        scheduleData: eleG.schedule_data,
                                        studentElement: { _student_id: eleStud._id },
                                        lateExcludeManagement,
                                    });
                                studentLateAbsent = updatedStudentLateAbsent;
                            }
                        }

                        //console.log(groups[indexG].students[indexStud].status);
                        clonedGroupsArray[indexG].students[indexStud].total_session = total_session;
                        clonedGroupsArray[indexG].students[indexStud].completed_session =
                            completed_session;
                        clonedGroupsArray[indexG].students[indexStud].no_of_session_attended =
                            present;
                        clonedGroupsArray[indexG].students[indexStud].absent_count = absentCount;
                        clonedGroupsArray[indexG].students[indexStud].permission_count =
                            permissionCount;
                        clonedGroupsArray[indexG].students[indexStud].onduty_count = ondutyCount;
                        clonedGroupsArray[indexG].students[indexStud].leave_count = leaveCount;
                        clonedGroupsArray[indexG].students[indexStud].no_of_warning = warningName;

                        // Use the common utility function for attendance percentage calculation
                        const { presentPercentage } = calculateAttendancePercentages({
                            totalPresent: present,
                            totalAbsent: absentCount,
                            totalLeave: leaveCount,
                            totalPermission: permissionCount,
                            totalOnDuty: ondutyCount,
                            totalCompletedSchedule: completed_session,
                            totalSchedules: total_session,
                            studentLateAbsent,
                        });

                        attendance_percentage = presentPercentage;
                        clonedGroupsArray[indexG].students[indexStud].attendance_percentage =
                            attendance_percentage;
                        clonedGroupsArray[indexG].students[indexStud].studentLateAbsent =
                            studentLateAbsent;
                    });
                }
            });
        }

        //Remove schedule data
        if (clonedGroupsArray.length > 0) {
            clonedGroupsArray.forEach((eleG, indexG) => {
                if (eleG.schedule_data) delete clonedGroupsArray[indexG].schedule_data;
                if (eleG.students) {
                    eleG.students.forEach((eleStud, indexStud) => {
                        const userInd = user_data.data.findIndex(
                            (eleUD) => eleUD._id.toString() === eleStud._id.toString(),
                        );
                        if (userInd != -1) {
                            clonedGroupsArray[indexG].students[indexStud].academic_no =
                                user_data.data[userInd].user_id;
                            clonedGroupsArray[indexG].students[indexStud].gender =
                                user_data.data[userInd].gender;
                        } else {
                            clonedGroupsArray[indexG].students[indexStud].gender =
                                user_data.data[userInd].gender;
                        }
                    });
                }
            });
        }

        return res.status(200).send(
            responseFunctionWithRequest(req, 200, true, req.t('STUDENT_DETAILS'), {
                course_details: courseResponse,
                student_details: clonedGroupsArray,
                denialLabel: lmsData.denialLabel,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
const removeDuplicatesStaffID = function (staffArr) {
    const new_arr = [];
    staffArr.forEach((eleS) => {
        const ind = new_arr.findIndex(
            (eleNA) => eleNA._staff_id && eleNA._staff_id.toString() === eleS._staff_id.toString(),
        );
        if (ind == -1) new_arr.push(eleS);
    });
    return new_arr;
};
exports.staff_details = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, type, level },
            query: { rotation_count },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const cData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId.toString(),
        );
        const pcData = (await allProgramCalendarDatas()).filter(
            (programElement) =>
                programElement._institution_calendar_id.toString() === institutionCalendarId &&
                programElement.level.some(
                    (levelElement) =>
                        levelElement.level_no.toString() === level.toString() &&
                        levelElement.term.toString() === term.toString() &&
                        (levelElement.course.find(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ) ||
                            levelElement.rotation_course.some((rotationElement) =>
                                rotationElement.course.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() === courseId.toString(),
                                ),
                            )),
                ),
        );
        const programData = cData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
            versionNo: cData.versionNo || 1,
            versioned: cData.versioned || false,
            versionName: cData.versionName || '',
            versionedFrom: cData.versionedFrom || null,
            versionedCourseIds: cData.versionedCourseIds || [],
        };
        //return res.send(pcData);
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (
                    pcLevel.rotation === 'no' &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const crsData = pcLevel.course.find(
                        (ele) => ele._course_id.toString() === courseId.toString(),
                    );
                    if (crsData) {
                        courseResponse.course_name = crsData.courses_name;
                        courseResponse.course_no = crsData.courses_number;
                        courseResponse.course_type = crsData.model;
                        courseResponse.start_date = crsData.start_date;
                        courseResponse.end_date = crsData.end_date;
                        courseResponse.credit_hours = crsData.credit_hours;
                        courseResponse.term = pcLevel.term;
                        // courseResponse.program_name = shared_with.program_name;
                        courseResponse._program_id = pcLevel._program_id;
                        courseResponse.year = pcLevel.year;
                        courseResponse.level = pcLevel.level_no;
                        courseResponse.curriculum = pcLevel.curriculum;
                        courseResponse.rotation = pcLevel.rotation;
                    }
                } else if (
                    rotation_count &&
                    rotation_count !== 0 &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const rotationData = pcLevel.rotation_course.find(
                        (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                    );

                    if (rotationData) {
                        const crsData = rotationData.course.find(
                            (ele) =>
                                ele._course_id && ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            // courseResponse.program_name = shared_with.program_name;
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                            courseResponse.rotation_count = rotationData.rotation_count;
                        }
                    }
                }
            }
        }
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === level &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const groupInd = studentGroupData.groups.findIndex(
            (ele) => ele.term === term && ele.level === level,
        );
        if (groupInd == -1)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('GROUP_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        const groups = [];
        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            studentGroupData.groups[groupInd].group_setting.forEach((ele) => {
                if (ele._id && ele.gender == 'male') {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-MG' + eleMG.group_no,
                            gender: ele.gender,
                        });
                    });
                }
                if (ele._id && ele.gender == 'female') {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-FG' + eleMG.group_no,
                            gender: ele.gender,
                        });
                    });
                }
                if (ele._id && ele.gender === BOTH) {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-SG' + eleMG.group_no,
                            gender: ele.gender,
                        });
                    });
                }
            });
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            const courseInd = studentGroupData.groups[groupInd].courses.findIndex(
                (eleC) => eleC._course_id.toString() === courseId.toString(),
            );
            if (courseInd == -1)
                return res.status(200).send(
                    responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                        course_details: courseResponse,
                    }),
                );
            studentGroupData.groups[groupInd].courses[courseInd].setting.forEach(
                (eleCourseSetting) => {
                    if (eleCourseSetting.gender == 'male') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'MG-1',
                            gender: eleCourseSetting.gender,
                        });
                    } else if (eleCourseSetting.gender == 'female') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'FG-1',
                            gender: eleCourseSetting.gender,
                        });
                    } else if (eleCourseSetting.gender === BOTH) {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'SG1',
                            gender: eleCourseSetting.gender,
                        });
                    }
                },
            );
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            studentGroupData.groups[groupInd].rotation_group_setting.forEach((ele) => {
                if (ele._id && ele.gender == 'male' && ele.group_no == rotation_count) {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RMG' + ele.group_no,
                        gender: ele.gender,
                    });
                }
                if (ele._id && ele.gender == 'female' && ele.group_no == rotation_count) {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RFG' + ele.group_no,
                        gender: ele.gender,
                    });
                }
                if (ele._id && ele.gender == BOTH && ele.group_no == rotation_count) {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RSG' + ele.group_no,
                        gender: ele.gender,
                    });
                }
            });
        }
        if (groups.length == 0)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('GROUP_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        console.time('courseSchedule');
        const course_schedule_data = await get_list(
            course_schedule,
            {
                isDeleted: false,
                isActive: true,
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                type,
                term,
                rotation_count,
                //status: COMPLETED,
            },
            {
                session: 1,
                _id: 1,
                student_groups: 1,
                // students: 1,
                staffs: 1,
                status: 1,
            },
        );
        if (!course_schedule_data.status)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        console.timeEnd('courseSchedule');
        if (studentGroupData.groups[groupInd].group_mode == FYD) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.groups[groupInd].group_mode == COURSE) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.groups[groupInd].group_mode == ROTATION) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        }

        //Staffs
        let staff_details = [];
        groups.forEach((eleG, indexG) => {
            if (eleG.schedule_data) {
                eleG.schedule_data.forEach((eleSD) => {
                    staff_details = [...staff_details, ...eleSD.staffs];
                });
            }
        });
        const uniq_staff_details = await removeDuplicatesStaffID(staff_details);
        const cloned_uniq_staff_details = JSON.parse(JSON.stringify(uniq_staff_details));
        //return res.send(cloned_uniq_staff_details);

        //Extract staff ID
        const staff_ids = cloned_uniq_staff_details.map((ele) => ele._staff_id);

        //User Data
        const user_data = await get_list(
            user,
            {
                _id: { $in: staff_ids },
                isDeleted: false,
            },
            {
                _id: 1,
                gender: 1,
                user_id: 1,
            },
        );

        if (!user_data.status)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('USER_DATA_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        cloned_uniq_staff_details.forEach((eleUSD, indexUSD) => {
            const grp = [];
            let total_session = 0;
            let completed_session = 0;
            let no_of_present = 0;
            let no_of_absent = 0;
            let no_of_leave = 0;
            groups.forEach((eleG, indexG) => {
                if (eleG.schedule_data) {
                    eleG.schedule_data.forEach((eleSD) => {
                        const staffInd = eleSD.staffs.findIndex(
                            (eleS) => eleS._staff_id.toString() === eleUSD._staff_id.toString(),
                        );
                        if (staffInd != -1) {
                            total_session++;
                            if (eleSD.status === COMPLETED) {
                                completed_session++;
                                if (
                                    eleSD.staffs[staffInd].status === PRESENT ||
                                    eleSD.staffs[staffInd].status === ONDUTY
                                )
                                    no_of_present++;
                                if (eleSD.staffs[staffInd].status === ABSENT) no_of_absent++;
                                if (
                                    eleSD.staffs[staffInd].status === LEAVE ||
                                    eleSD.staffs[staffInd].status === PERMISSION
                                )
                                    no_of_leave++;
                            }
                            eleSD.student_groups.forEach((eleSG) => {
                                const grpInd = groups.findIndex(
                                    (eleG) =>
                                        eleG.group_id.toString() === eleSG.group_id.toString(),
                                );
                                if (grpInd != -1) grp.push(groups[grpInd].group_name);
                            });
                        }
                    });
                }
            });
            //console.log(grp);
            cloned_uniq_staff_details[indexUSD].group = [...new Set(grp)];
            // cloned_uniq_staff_details[indexUSD].total_session = total_session;
            // cloned_uniq_staff_details[indexUSD].completed_session = completed_session;
            // cloned_uniq_staff_details[indexUSD].no_of_present = no_of_present;
            // cloned_uniq_staff_details[indexUSD].no_of_absent = no_of_absent;
            // cloned_uniq_staff_details[indexUSD].leave = no_of_leave;
            // cloned_uniq_staff_details[indexUSD].attendance_percentage =
            //     no_of_present == 0 ? no_of_present : (no_of_present / completed_session) * 100;

            // Recreating Staff Work Load
            const staffSchedule = course_schedule_data.data.filter((scheduleElement) =>
                scheduleElement.staffs.find(
                    (staffElement) =>
                        staffElement._staff_id.toString() === eleUSD._staff_id.toString(),
                ),
            );
            const completedSchedule = staffSchedule.filter(
                (scheduleElement) => scheduleElement.status === COMPLETED,
            );
            const noOfPresentSchedule = staffSchedule.filter(
                (scheduleElement) =>
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.staffs.find(
                        (staffElement) =>
                            staffElement._staff_id.toString() === eleUSD._staff_id.toString() &&
                            (staffElement.status === PRESENT || staffElement.status === ONDUTY),
                    ),
            );
            const noOfAbsentSchedule = staffSchedule.filter(
                (scheduleElement) =>
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.staffs.find(
                        (staffElement) =>
                            staffElement._staff_id.toString() === eleUSD._staff_id.toString() &&
                            staffElement.status === LEAVE,
                    ),
            );
            const noOfLeaveSchedule = staffSchedule.filter(
                (scheduleElement) =>
                    scheduleElement.status === COMPLETED &&
                    scheduleElement.staffs.find(
                        (staffElement) =>
                            staffElement._staff_id.toString() === eleUSD._staff_id.toString() &&
                            (staffElement.status === ABSENT || staffElement.status === PERMISSION),
                    ),
            );
            cloned_uniq_staff_details[indexUSD].total_session = staffSchedule.length;
            cloned_uniq_staff_details[indexUSD].completed_session = completedSchedule.length;
            cloned_uniq_staff_details[indexUSD].no_of_present = noOfPresentSchedule.length;
            cloned_uniq_staff_details[indexUSD].no_of_absent = noOfAbsentSchedule.length;
            cloned_uniq_staff_details[indexUSD].leave = noOfLeaveSchedule.length;
            cloned_uniq_staff_details[indexUSD].attendance_percentage =
                noOfPresentSchedule.length == 0
                    ? noOfPresentSchedule.length
                    : (noOfPresentSchedule.length / completedSchedule.length) * 100;

            //Adding Gender and academic no
            const userInd = user_data.data.findIndex(
                (eleU) => eleU._id.toString() === eleUSD._staff_id.toString(),
            );
            if (userInd != -1) {
                cloned_uniq_staff_details[indexUSD].gender = user_data.data[userInd].gender;
                cloned_uniq_staff_details[indexUSD].academic_no = user_data.data[userInd].user_id;
            }
        });

        return res.status(200).send(
            responseFunctionWithRequest(req, 200, true, req.t('STAFF_DETAILS'), {
                course_details: courseResponse,
                staff_details: cloned_uniq_staff_details,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
exports.student_attendance_report = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, type, level, studentId },
            query: { rotation_count },
        } = req;
        const { status: ic_status } = await get(
            institution,
            { _id: convertToMongoObjectId(_institution_id) },
            { _id: 1 },
        );
        if (!ic_status)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const { data: cData } = await get(
            course,
            { _id: convertToMongoObjectId(courseId) },
            {
                _program_id: 1,
                'course_assigned_details._program_id': 1,
                'course_assigned_details.program_name': 1,
            },
        );

        //Program Calendar
        const pc_query = {
            isDeleted: false,
            isActive: true,
            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
            _program_id: convertToMongoObjectId(programId),
            'level.level_no': level,
            'level.term': term,
            $or: [
                {
                    'level.course._course_id': convertToMongoObjectId(courseId),
                },
                {
                    'level.rotation_course.course._course_id': convertToMongoObjectId(courseId),
                },
            ],
        };
        pc_project = {
            _id: 1,
            _program_id: 1,
            isActive: 1,
            'level.start_date': 1,
            'level.end_date': 1,
            'level.course._course_id': 1,
            'level.course.courses_name': 1,
            'level.course.start_date': 1,
            'level.course.end_date': 1,
            'level.course.courses_number': 1,
            'level.course.model': 1,
            'level.course.credit_hours': 1,
            'level._id': 1,
            'level.curriculum': 1,
            'level.term': 1,
            'level.year': 1,
            'level.level_no': 1,
            'level._program_id': 1,
            'level.rotation_course.rotation_count': 1,
            'level.rotation_course.course._course_id': 1,
            'level.rotation_course.course.courses_name': 1,
            'level.rotation_course.course.start_date': 1,
            'level.rotation_course.course.end_date': 1,
            'level.rotation_course.course.courses_number': 1,
            'level.rotation_course.course.model': 1,
            'level.rotation_course.course.credit_hours': 1,
            'level.rotation': 1,
        };
        const { status: pcStatus, data: pcData } = await get_list(
            program_calendar,
            pc_query,
            pc_project,
        );
        if (!pcStatus)
            return res
                .status(404)
                .send(
                    response_function(
                        res,
                        404,
                        false,
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                        req.t('PROGRAM_CALENDAR_NOT_FOUND'),
                    ),
                );
        const programData = cData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
        };
        //return res.send(pcData);
        for (const pcList of pcData) {
            for (const pcLevel of pcList.level) {
                if (
                    pcLevel.rotation === 'no' &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const crsData = pcLevel.course.find(
                        (ele) => ele._course_id.toString() === courseId.toString(),
                    );
                    if (crsData) {
                        courseResponse.course_name = crsData.courses_name;
                        courseResponse.course_no = crsData.courses_number;
                        courseResponse.course_type = crsData.model;
                        courseResponse.start_date = crsData.start_date;
                        courseResponse.end_date = crsData.end_date;
                        courseResponse.credit_hours = crsData.credit_hours;
                        courseResponse.term = pcLevel.term;
                        // courseResponse.program_name = shared_with.program_name;
                        courseResponse._program_id = pcLevel._program_id;
                        courseResponse.year = pcLevel.year;
                        courseResponse.level = pcLevel.level_no;
                        courseResponse.curriculum = pcLevel.curriculum;
                        courseResponse.rotation = pcLevel.rotation;
                    }
                } else if (
                    rotation_count &&
                    rotation_count !== 0 &&
                    pcLevel.term === term &&
                    pcLevel.level_no === level
                ) {
                    const rotationData = pcLevel.rotation_course.find(
                        (ele) => ele.rotation_count.toString() === rotation_count.toString(),
                    );

                    if (rotationData) {
                        const crsData = rotationData.course.find(
                            (ele) =>
                                ele._course_id && ele._course_id.toString() === courseId.toString(),
                        );
                        if (crsData) {
                            courseResponse.course_name = crsData.courses_name;
                            courseResponse.course_no = crsData.courses_number;
                            courseResponse.course_type = crsData.model;
                            courseResponse.start_date = crsData.start_date;
                            courseResponse.end_date = crsData.end_date;
                            courseResponse.credit_hours = crsData.credit_hours;
                            courseResponse.term = pcLevel.term;
                            // courseResponse.program_name = shared_with.program_name;
                            courseResponse._program_id = pcLevel._program_id;
                            courseResponse.year = pcLevel.year;
                            courseResponse.level = pcLevel.level_no;
                            courseResponse.curriculum = pcLevel.curriculum;
                            courseResponse.rotation = pcLevel.rotation;
                            courseResponse.rotation_count = rotationData.rotation_count;
                        }
                    }
                }
            }
        }

        //User Data
        const user_data = await get(
            user,
            {
                isDeleted: false,
                _id: convertToMongoObjectId(studentId),
            },
            { _id: 1, user_id: 1, gender: 1 },
        );

        if (!user_data.status)
            return res.status(200).send(
                response_function(res, 200, true, req.t('USER_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        const studentGroupData = await get(
            student_group,
            {
                isDeleted: false,
                //_id: convertToMongoObjectId(_student_group_id),
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                'groups.term': term,
                'groups.level': level,
                'groups.courses._course_id': convertToMongoObjectId(courseId),
            },
            {},
        );
        //return res.send(studentGroupData);
        if (!studentGroupData.status)
            return res.status(200).send(
                response_function(res, 200, true, req.t('STUDENT_GROUP_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        const groupInd = studentGroupData.data.groups.findIndex(
            (ele) => ele.term === term && ele.level === level,
        );
        if (groupInd == -1)
            return res.status(200).send(
                response_function(res, 200, true, req.t('GROUP_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        const groups = [];
        if (studentGroupData.data.groups[groupInd].group_mode == FYD) {
            studentGroupData.data.groups[groupInd].group_setting.forEach((ele) => {
                if (ele._id && ele.gender == 'male') {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-MG' + eleMG.group_no,
                        });
                    });
                }
                if (ele._id && ele.gender == 'female') {
                    ele.groups.forEach((eleMG) => {
                        groups.push({
                            group_id: eleMG._id,
                            group_no: eleMG.group_no,
                            group_name: 'F-FG' + eleMG.group_no,
                        });
                    });
                }
            });
        } else if (studentGroupData.data.groups[groupInd].group_mode == COURSE) {
            const courseInd = studentGroupData.data.groups[groupInd].courses.findIndex(
                (eleC) => eleC._course_id.toString() === courseId.toString(),
            );
            if (courseInd == -1)
                return res.status(200).send(
                    response_function(res, 200, true, req.t('COURSE_NOT_FOUND'), {
                        course_details: courseResponse,
                    }),
                );
            studentGroupData.data.groups[groupInd].courses[courseInd].setting.forEach(
                (eleCourseSetting) => {
                    if (eleCourseSetting.gender == 'male') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'MG-1',
                        });
                    } else if (eleCourseSetting.gender == 'female') {
                        groups.push({
                            group_id: eleCourseSetting._id,
                            group_name: 'FG-1',
                        });
                    }
                },
            );
        } else if (studentGroupData.data.groups[groupInd].group_mode == ROTATION) {
            studentGroupData.data.groups[groupInd].rotation_group_setting.forEach((ele) => {
                if (ele._id && ele.gender == 'male') {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RMG' + ele.group_no,
                    });
                }
                if (ele._id && ele.gender == 'female') {
                    groups.push({
                        group_id: ele._id,
                        group_no: ele.group_no,
                        group_name: 'RFG' + ele.group_no,
                    });
                }
            });
        }

        const course_schedule_data = await get_list(
            course_schedule,
            {
                isDeleted: false,
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                type,
                term,
            },
            {
                session: 1,
                _id: 1,
                student_groups: 1,
                students: 1,
            },
        );
        if (!course_schedule_data.status)
            return res.status(200).send(
                response_function(res, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        if (studentGroupData.data.groups[groupInd].group_mode == FYD) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.data.groups[groupInd].group_mode == COURSE) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        } else if (studentGroupData.data.groups[groupInd].group_mode == ROTATION) {
            if (groups.length > 0) {
                groups.forEach((eleG, indexG) => {
                    course_schedule_data.data.forEach((eleCS) => {
                        const CSStudGrpInd = eleCS.student_groups.findIndex(
                            (eleStudG) => eleStudG.group_id.toString() === eleG.group_id.toString(),
                        );
                        if (CSStudGrpInd != -1) {
                            if (!groups[indexG].schedule_data) {
                                groups[indexG].schedule_data = [];
                                groups[indexG].schedule_data.push(eleCS);
                            } else groups[indexG].schedule_data.push(eleCS);
                        }
                    });
                });
            }
        }
        //return res.send(groups);
        const new_arr = [];
        if (groups.length > 0) {
            let indexG = 0;
            let flag = 0;
            for (const eleG of groups) {
                if (eleG.schedule_data) {
                    for (const eleSD of eleG.schedule_data) {
                        for (const eleStudG of eleSD.student_groups) {
                            for (const eleSG of eleStudG.session_group) {
                                if (!groups[indexG].students) groups[indexG].students = [];
                                //session group id

                                const courseInd = studentGroupData.data.groups[
                                    groupInd
                                ].courses.findIndex(
                                    (eleC) => eleC._course_id.toString() === courseId.toString(),
                                );
                                if (courseInd == -1)
                                    return res.status(200).send(
                                        response_function(
                                            res,
                                            200,
                                            true,
                                            req.t('COURSE_NOT_FOUND'),
                                            {
                                                course_details: courseResponse,
                                            },
                                        ),
                                    );
                                //Match students with student groups group and push to particular group
                                let students = {};
                                for (const eleCourseSetting of studentGroupData.data.groups[
                                    groupInd
                                ].courses[courseInd].setting) {
                                    for (const eleSessSettings of eleCourseSetting.session_setting) {
                                        const sessGroupInd = eleSessSettings.groups.findIndex(
                                            (eleGroups) =>
                                                eleGroups._id.toString() ===
                                                eleSG.session_group_id.toString(),
                                        );
                                        if (sessGroupInd != -1) {
                                            const student_ids =
                                                eleSessSettings.groups[sessGroupInd]._student_ids;
                                            /* students = eleSD.students.filter((eleS) =>
                                                student_ids.includes(eleS._id),
                                            ); */
                                            const studIdInd = student_ids.findIndex(
                                                (eleStudId) =>
                                                    eleStudId.toString() === studentId.toString(),
                                            );
                                            if (studIdInd != -1) {
                                                const studentInd = eleSD.students.findIndex(
                                                    (eleStud) =>
                                                        eleStud._id.toString() ===
                                                        studentId.toString(),
                                                );
                                                students = eleSD.students[studentInd];
                                                new_arr[0] = groups[indexG];
                                                new_arr[0].students = students;
                                                groups[indexG].students = students;
                                                flag = 1;
                                            }
                                        }
                                        if (flag == 1) break;
                                    }
                                    if (flag == 1) break;
                                }
                                if (flag == 1) break;
                            }
                            if (flag == 1) break;
                        }
                        if (flag == 1) break;
                    }
                }
                indexG++;
                if (flag == 1) break;
            }
        }
        //return res.send(new_arr);

        //Student wise sessions
        const group_array = clone(new_arr);
        if (group_array.length > 0) {
            group_array.forEach((eleG, indexG) => {
                if (eleG.students) {
                    const student_arr = [];

                    if (eleG.schedule_data) {
                        eleG.schedule_data.forEach((eleSD) => {
                            const studInd = eleSD.students.findIndex(
                                (eleSDS) => eleSDS._id.toString() === eleG.students._id.toString(),
                            );
                            if (studInd != -1) {
                                group_array[indexG].students.session_details =
                                    eleSD.session.delivery_symbol + ' ' + eleSD.session.delivery_no;
                                group_array[indexG].students.academic_no = user_data.data.user_id;

                                if (!group_array[indexG].students.session) {
                                    group_array[indexG].students.session = [
                                        {
                                            attendance: eleG.students.status,
                                            session_details:
                                                eleSD.session.delivery_symbol +
                                                ' ' +
                                                eleSD.session.delivery_no +
                                                ' ' +
                                                eleSD.session.session_topic,
                                        },
                                    ];
                                } else {
                                    group_array[indexG].students.session.push({
                                        attendance: eleG.students.status,
                                        session_details:
                                            eleSD.session.delivery_symbol +
                                            ' ' +
                                            eleSD.session.delivery_no +
                                            ' ' +
                                            eleSD.session.session_topic,
                                    });
                                }
                            }
                        });
                    }
                }
            });
        }
        delete group_array[0].schedule_data;

        return res.status(200).send(
            response_function(res, 200, true, req.t('STUDENT_DETAILS_ATTENDANCE'), {
                course_details: courseResponse,
                student_attendance_report: group_array,
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                response_function(
                    res,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.courseAttendanceLog = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, scheduleType, level },
            query: {
                rotation_count,
                groupName,
                type,
                firstDate,
                lastDate,
                gender,
                groupIds,
                sessionGroupIds,
                isDeliveryGroup,
            },
        } = req;
        let {
            query: { tableFilter },
        } = req;
        if (!Array.isArray(tableFilter)) tableFilter = [tableFilter];
        const studentGroupFilter = groupName
            ? Array.isArray(groupName)
                ? groupName
                : [groupName]
            : [];
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId.toString(),
        );

        const isDeliveryChange = isDeliveryGroup === 'true';
        let groupIdsArray = [];
        if (groupIds && isDeliveryChange) {
            try {
                if (Array.isArray(groupIds)) {
                    groupIdsArray = groupIds;
                } else {
                    groupIdsArray = JSON.parse(groupIds);
                }
            } catch (error) {
                groupIdsArray = [groupIds];
            }
        }

        if (isDeliveryChange && groupIdsArray.length === 0) {
            return res.status(404).send(
                responseFunctionWithRequest(req, 404, true, 'Please Select Groups', {
                    course_details: [],
                    attendance_log: [],
                }),
            );
        }

        let sessionGroupIdsArray = [];
        if (sessionGroupIds && isDeliveryChange) {
            try {
                if (Array.isArray(sessionGroupIds)) {
                    sessionGroupIdsArray = sessionGroupIds;
                } else {
                    sessionGroupIdsArray = JSON.parse(sessionGroupIds);
                }
            } catch (error) {
                sessionGroupIdsArray = [sessionGroupIds];
            }
        }

        const pcData = (await allProgramCalendarDatas()).find(
            (programElement) =>
                programElement._institution_calendar_id.toString() === institutionCalendarId &&
                programElement.level.find(
                    (levelElement) =>
                        levelElement.level_no.toString() === level.toString() &&
                        levelElement.term.toString() === term.toString() &&
                        (levelElement.course.find(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ) ||
                            levelElement.rotation_course.some((rotationElement) =>
                                rotationElement.course.find(
                                    (courseElement) =>
                                        courseElement._course_id.toString() === courseId.toString(),
                                ),
                            )),
                ),
        );
        const programData = courseData.course_assigned_details.find(
            (ele) => ele._program_id.toString() === programId.toString(),
        );
        const courseResponse = {
            _program_id: programData ? programData._program_id : '',
            program_name: programData ? programData.program_name : '',
            versionNo: courseData.versionNo || 1,
            versioned: courseData.versioned || false,
            versionName: courseData.versionName || '',
            versionedFrom: courseData.versionedFrom || null,
            versionedCourseIds: courseData.versionedCourseIds || [],
        };
        const calendarLevelData = pcData.level.find(
            (pcLevelElement) => pcLevelElement.term === term && pcLevelElement.level_no === level,
        );
        const programCalendarCourseData =
            rotation_count && rotation_count !== 0
                ? calendarLevelData.rotation_course
                      .find((ele) => ele.rotation_count.toString() === rotation_count.toString())
                      .course.find((ele) => ele._course_id.toString() === courseId.toString())
                : calendarLevelData.course.find(
                      (ele) => ele._course_id.toString() === courseId.toString(),
                  );
        if (programCalendarCourseData) {
            courseResponse.course_name = programCalendarCourseData.courses_name;
            courseResponse.course_no = programCalendarCourseData.courses_number;
            courseResponse.course_type = programCalendarCourseData.model;
            courseResponse.start_date = programCalendarCourseData.start_date;
            courseResponse.end_date = programCalendarCourseData.end_date;
            courseResponse.term = term;
            courseResponse._program_id = calendarLevelData._program_id;
            courseResponse.year = calendarLevelData.year;
            courseResponse.level = calendarLevelData.level_no;
            courseResponse.rotation = calendarLevelData.rotation;
            courseResponse.rotation_count = rotation_count;
        }
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === level &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );
        const groupLevelElement = studentGroupData.groups.find(
            (levelElement) => levelElement.term === term && levelElement.level === level,
        );
        if (!groupLevelElement)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('NO_DATA_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        let groups = [];
        const groupNaming = (gender) => {
            let genderName = '';
            switch (gender) {
                case MALE:
                    genderName = 'MG';
                    break;
                case FEMALE:
                    genderName = 'FG';
                    break;
                case BOTH:
                    genderName = 'SG';
                    break;
                default:
                    break;
            }
            return genderName;
        };

        const filterCourseData = groupLevelElement.courses.find(
            (courseElement) => courseElement._course_id.toString() === courseId.toString(),
        );
        if (!filterCourseData)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        switch (groupLevelElement.group_mode) {
            case FYD:
                groupLevelElement.group_setting.forEach((groupSettingElement) => {
                    groupSettingElement.groups.forEach((groupElement) => {
                        const filterSetting = filterCourseData.setting
                            .filter(
                                (settingElement) =>
                                    settingElement._group_no.toString() ===
                                    groupElement.group_no.toString(),
                            )
                            .flatMap((settingElement) => settingElement.session_setting);
                        groups.push({
                            group_id: groupElement._id,
                            group_no: groupElement.group_no,
                            group_name: groupElement.group_name.substring(
                                groupElement.group_name.length - 4,
                            ),
                            // group_name: `F-${groupNaming(groupElement.gender)}${
                            //     groupElement.group_no
                            // }`,
                            gender: groupElement.gender,
                            schedule_data: [],
                            session_setting: filterSetting,
                        });
                    });
                });
                break;
            case COURSE:
                filterCourseData.setting
                    .filter((settingElement) =>
                        isDeliveryChange && groupIdsArray.length > 0
                            ? groupIdsArray
                                  .map((groupElement) => groupElement.toString())
                                  .includes(settingElement._id.toString())
                            : true,
                    )
                    .forEach((groupElement) => {
                        groups.push({
                            group_id: groupElement._id,
                            group_name:
                                groupNaming(groupElement.gender) === 'SG'
                                    ? `${groupNaming(groupElement.gender)}1`
                                    : `${groupNaming(groupElement.gender)}-1`,
                            gender: groupElement.gender,
                            schedule_data: [],
                            session_setting: groupElement.session_setting,
                        });
                    });
                break;
            case ROTATION:
                groupLevelElement.rotation_group_setting.forEach((groupSettingElement) => {
                    if (
                        groupSettingElement.group_no &&
                        groupSettingElement.group_no.toString() === rotation_count
                    ) {
                        const filterSetting = filterCourseData.setting
                            .filter(
                                (settingElement) =>
                                    settingElement._group_no.toString() === rotation_count,
                            )
                            .flatMap((settingElement) => settingElement.session_setting);
                        groups.push({
                            group_id: groupSettingElement._id,
                            group_no: groupSettingElement.group_no,
                            group_name: groupSettingElement.group_name.substring(
                                groupSettingElement.group_name.length - 5,
                            ),
                            // group_name: `R-${groupNaming(groupSettingElement.gender)}${
                            //     groupSettingElement.group_no
                            // }`,
                            gender: groupSettingElement.gender,
                            schedule_data: [],
                            session_setting: filterSetting,
                        });
                    }
                });
                break;
            default:
                break;
        }
        if (!groups.length)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, 'Student Groups Not Found', {
                    course_details: courseResponse,
                }),
            );
        const courseGroup = groupLevelElement.courses.find(
            (levelElement) => levelElement._course_id.toString() === courseId.toString(),
        );
        if (!courseGroup)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        const scheduleQuery = {
            isDeleted: false,
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            type: scheduleType,
            term,
        };
        courseResponse.sgGroups = groups.map(
            (studentGroupElement) => studentGroupElement.group_name,
        );

        if (isDeliveryChange && groupIdsArray.length > 0) {
            scheduleQuery['student_groups.group_id'] = {
                $in: groupIdsArray.map((groupElement) =>
                    convertToMongoObjectId(groupElement.toString()),
                ),
            };
            if (sessionGroupIdsArray.length > 0)
                scheduleQuery['student_groups.session_group.session_group_id'] = {
                    $in: sessionGroupIdsArray.map((groupElement) =>
                        convertToMongoObjectId(groupElement.toString()),
                    ),
                };
        } else if (studentGroupFilter.length) {
            groups = groups.filter((groupElement) =>
                studentGroupFilter.find(
                    (studentGroupElement) =>
                        studentGroupElement.toString() === groupElement.group_name.toString(),
                ),
            );
            scheduleQuery['student_groups.group_id'] = {
                $in: groups.map((groupElement) => convertToMongoObjectId(groupElement.group_id)),
            };
        } else if (groups.length > 0) {
            groups = [groups[0]];
            scheduleQuery['student_groups.group_id'] = convertToMongoObjectId(groups[0].group_id);
        }
        if (firstDate && lastDate) {
            scheduleQuery.schedule_date = {
                $gte: new Date(firstDate),
                $lte: new Date(lastDate),
            };
        } else {
            // Course End Date based Flow
            const courseStartDate = new Date(courseResponse.start_date);
            scheduleQuery.schedule_date = {
                $gte: new Date(courseStartDate.getFullYear(), courseStartDate.getMonth(), 1),
                $lte: new Date(courseResponse.end_date),
            };
        }
        let courseScheduleProject = {
            _id: 1,
            'session._session_id': 1,
            'session.s_no': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'session.session_topic': 1,
            'student_groups.group_id': 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_no': 1,
            'student_groups.session_group.session_group_id': 1,
            'students._id': 1,
            'students.status': 1,
            'students.tardisId': 1,
            'students.reasonForLate': 1,
            'students.primaryTime': 1,
            'students.lateExclude': 1,
            scheduleStartDateAndTime: 1,
            'sessionDetail.start_time': 1,
            isActive: 1,
            status: 1,
            merge_status: 1,
            merge_with: 1,
            // ...(type === DC_STAFF && { 'staffs._staff_id': 1, 'staffs.status': 1 }),
            'staffs._staff_id': 1,
            'staffs.status': 1,
            _institution_id: 1,
            _institution_calendar_id: 1,
            _program_id: 1,
            _course_id: 1,
            term: 1,
            level_no: 1,
            rotation_count: 1,
            classModeType: 1,
        };
        if (
            tableFilter &&
            tableFilter.find((tableFilterElement) => tableFilterElement === 'schedule_date')
        ) {
            courseScheduleProject = {
                ...courseScheduleProject,
                ...{ schedule_date: 1, start: 1, end: 1 },
            };
        }
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const { lateExcludeManagement } = await checkExclude({
            _institution_id,
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo: level,
            term,
            rotationCount: rotation_count,
        });
        const { lateExclude } = checkLateExcludeConfigurationForCourseOrStudent({
            _institution_calendar_id: institutionCalendarId,
            courseId,
            programId,
            levelNo: level,
            term,
            rotationCount: rotation_count,
            lateExcludeManagement,
        });
        // OverAll Student Attendance
        let overAllCourseScheduleData = [];
        const overAllStudentDetails = [];
        const overAllStaffDetails = [];
        if (
            tableFilter &&
            tableFilter.find((tableFilterElement) => tableFilterElement === 'overall')
        ) {
            // scheduleQuery.schedule_date = {
            //     $gte: new Date(courseResponse.start_date),
            //     $lte: new Date(courseResponse.end_date),
            // };
            const overAllScheduleQuery = {
                ...scheduleQuery,
                ...{
                    schedule_date: {
                        $gte: new Date(courseResponse.start_date),
                        $lte: new Date(courseResponse.end_date),
                    },
                },
                status: COMPLETED,
            };
            console.time('overAllCourseScheduleDatas');
            overAllCourseScheduleData = await course_schedule
                .find(overAllScheduleQuery, {
                    'students._id': 1,
                    'students.status': 1,
                    'students.reasonForLate': 1,
                    'students.tardisId': 1,
                    'students.primaryTime': 1,
                    'students.lateExclude': 1,
                    scheduleStartDateAndTime: 1,
                    'sessionDetail.start_time': 1,
                    status: 1,
                    merge_status: 1,
                    'merge_with.schedule_id': 1,
                    ...(type === DC_STAFF && { 'staffs._staff_id': 1, 'staffs.status': 1 }),
                    _course_id: 1,
                    _program_id: 1,
                    _institution_calendar_id: 1,
                    term: 1,
                    level_no: 1,
                    rotation_count: 1,
                    'session._session_id': 1,
                })
                .sort({
                    'session.s_no': 1,
                })
                .lean();
            console.timeEnd('overAllCourseScheduleDatas');
            let overAllMergedScheduleId = [];
            overAllCourseScheduleData.forEach((overAllCourseScheduleElement) => {
                overAllCourseScheduleElement.students.forEach((studentElement) => {
                    const studentIndex = overAllStudentDetails.findIndex(
                        (overAllStudentDetailElement) =>
                            studentElement._id.toString() ===
                            overAllStudentDetailElement._id.toString(),
                    );
                    const increment = 1;
                    if (studentIndex === -1 && studentElement.status !== EXCLUDE) {
                        overAllStudentDetails.push({
                            _id: studentElement._id.toString(),
                            schedules: [overAllCourseScheduleElement],
                            sessionCount: increment,
                            attendedCount:
                                studentElement.status &&
                                (studentElement.status === PRESENT ||
                                    studentElement.status === ONDUTY ||
                                    studentElement.status === PERMISSION)
                                    ? increment
                                    : 0,
                            presentCount:
                                studentElement.status && studentElement.status === PRESENT
                                    ? increment
                                    : 0,
                            absentCount:
                                studentElement.status && studentElement.status === ABSENT
                                    ? increment
                                    : 0,
                            permissionCount:
                                studentElement.status && studentElement.status === PERMISSION
                                    ? increment
                                    : 0,
                            onDutyCount:
                                studentElement.status && studentElement.status === ONDUTY
                                    ? increment
                                    : 0,
                            leaveCount:
                                studentElement.status && studentElement.status === LEAVE
                                    ? increment
                                    : 0,
                            percentage: 0,
                            studentLateAbsent: 0,
                        });
                    } else if (studentElement.status !== EXCLUDE) {
                        overAllStudentDetails[studentIndex].sessionCount += increment;
                        overAllStudentDetails[studentIndex].attendedCount +=
                            studentElement.status &&
                            (studentElement.status === PRESENT ||
                                studentElement.status === ONDUTY ||
                                studentElement.status === PERMISSION)
                                ? increment
                                : 0;
                        overAllStudentDetails[studentIndex].presentCount +=
                            studentElement.status && studentElement.status === PRESENT
                                ? increment
                                : 0;
                        overAllStudentDetails[studentIndex].absentCount +=
                            studentElement.status && studentElement.status === ABSENT
                                ? increment
                                : 0;
                        overAllStudentDetails[studentIndex].permissionCount +=
                            studentElement.status && studentElement.status === PERMISSION
                                ? increment
                                : 0;
                        overAllStudentDetails[studentIndex].onDutyCount +=
                            studentElement.status && studentElement.status === ONDUTY
                                ? increment
                                : 0;
                        overAllStudentDetails[studentIndex].leaveCount +=
                            studentElement.status && studentElement.status === LEAVE
                                ? increment
                                : 0;
                        if (
                            overAllStudentDetails[studentIndex].schedules.findIndex(
                                (scheduleElement) =>
                                    scheduleElement._id.toString() ===
                                    overAllCourseScheduleElement._id.toString(),
                            ) == -1
                        ) {
                            overAllStudentDetails[studentIndex].schedules.push(
                                overAllCourseScheduleElement,
                            );
                        }
                    }
                });
                if (type === DC_STAFF) {
                    overAllCourseScheduleElement.staffs.forEach((staffElement) => {
                        const staffIndex = overAllStaffDetails.findIndex(
                            (overAllStudentDetailElement) =>
                                staffElement._staff_id.toString() ===
                                overAllStudentDetailElement._staff_id.toString(),
                        );
                        if (staffIndex === -1) {
                            overAllStaffDetails.push({
                                _staff_id: staffElement._staff_id.toString(),
                                sessionCount: 1,
                                attendedCount:
                                    staffElement.status &&
                                    (staffElement.status === PRESENT ||
                                        staffElement.status === ONDUTY)
                                        ? 1
                                        : 0,
                                percentage: 0,
                            });
                        } else {
                            overAllStaffDetails[staffIndex].sessionCount++;
                            overAllStaffDetails[staffIndex].attendedCount +=
                                staffElement.status &&
                                (staffElement.status === PRESENT || staffElement.status === ONDUTY)
                                    ? 1
                                    : 0;
                        }
                    });
                }

                if (overAllCourseScheduleElement.merge_status) {
                    overAllMergedScheduleId = [
                        ...overAllMergedScheduleId,
                        ...overAllCourseScheduleElement._id.toString(),
                        ...overAllCourseScheduleElement.merge_with.map((mergeScheduleElement) =>
                            mergeScheduleElement.schedule_id._id.toString(),
                        ),
                    ];
                }
            });
        }
        overAllStudentDetails.forEach((overAllStudentDetailElement, scheduleIndex) => {
            const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                _institution_calendar_id: institutionCalendarId,
                courseId,
                programId,
                levelNo: level,
                term,
                rotationCount: rotation_count,
                lateExcludeManagement,
                studentId: overAllStudentDetailElement._id,
            }).lateExclude;
            let studentLateAbsent = 0;
            if (!lateExclude && !lateExcludeForStudent) {
                const { studentLateAbsent: updatedStudentLateAbsent } =
                    getLateConfigAndStudentLateAbsent({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        scheduleData: overAllStudentDetailElement.schedules,
                        studentElement: { _student_id: overAllStudentDetailElement._id },
                        lateExcludeManagement,
                    });
                studentLateAbsent = updatedStudentLateAbsent;
            }
            if (studentLateAbsent) {
                overAllStudentDetails[scheduleIndex].studentLateAbsent = studentLateAbsent;
            } else {
                overAllStudentDetails[scheduleIndex].studentLateAbsent = 0;
            }
            // Use common utility function for overall attendance percentage calculation
            const { presentPercentage } = calculateAttendancePercentages({
                totalPresent: overAllStudentDetailElement.presentCount,
                totalAbsent: overAllStudentDetailElement.absentCount,
                totalLeave: overAllStudentDetailElement.leaveCount,
                totalPermission: overAllStudentDetailElement.permissionCount,
                totalOnDuty: overAllStudentDetailElement.onDutyCount,
                totalCompletedSchedule: overAllStudentDetailElement.sessionCount,
                totalSchedules: overAllStudentDetailElement.sessionCount,
                studentLateAbsent,
            });

            overAllStudentDetailElement.percentage = presentPercentage;
        });
        overAllStaffDetails.forEach((overAllStaffDetailElement) => {
            overAllStaffDetailElement.percentage =
                (overAllStaffDetailElement.attendedCount / overAllStaffDetailElement.sessionCount) *
                100;
        });
        console.time('courseSchedule');
        let courseScheduleDatas = await course_schedule
            .find(scheduleQuery, courseScheduleProject)
            .populate({
                path: 'merge_with.schedule_id',
                select: {
                    // 'session.s_no': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'student_groups.gender': 1,
                    'student_groups.group_no': 1,
                    'student_groups.session_group.group_no': 1,
                },
            })
            .populate({ path: 'students.tardisId', select: { name: 1, short_code: 1 } })
            .sort({
                scheduleStartDateAndTime: 1,
            })
            .lean();
        console.timeEnd('courseSchedule');
        if (!courseScheduleDatas)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        let mergedScheduleId = [];
        let scheduleStaffIds = [];
        let scheduleStudentIds = [];
        courseScheduleDatas = courseScheduleDatas.filter((scheduleElement) => {
            // if (type === DC_STAFF)
            scheduleStaffIds = [
                ...scheduleStaffIds,
                ...scheduleElement.staffs.map((staffElement) => staffElement._staff_id.toString()),
            ];
            scheduleStudentIds = [
                ...scheduleStudentIds,
                ...scheduleElement.students.map((studentElement) => studentElement._id.toString()),
            ];
            if (scheduleElement.merge_status) {
                const mergeScheduleId = scheduleElement.merge_with
                    .filter((mergeScheduleElement) => {
                        return (
                            mergeScheduleElement &&
                            mergeScheduleElement.schedule_id &&
                            mergeScheduleElement.schedule_id._id
                        );
                    })
                    .map((mergeScheduleElement) => mergeScheduleElement.schedule_id._id.toString());
                mergeScheduleId.push(scheduleElement._id.toString());
                if (
                    !mergeScheduleId.find((mergeScheduleIdElement) =>
                        mergedScheduleId.find(
                            (mergedScheduleIdElement) =>
                                mergedScheduleIdElement.toString() ===
                                mergeScheduleIdElement.toString(),
                        ),
                    )
                ) {
                    mergedScheduleId = [...mergedScheduleId, ...mergeScheduleId];
                    return true;
                }
                return false;
            }
            return true;
        });
        scheduleStaffIds = [...new Set(scheduleStaffIds)];
        scheduleStudentIds = [...new Set(scheduleStudentIds)];
        const userProject = {
            _id: 1,
            name: 1,
            ...(tableFilter &&
                tableFilter.find((tableFilterElement) => tableFilterElement === 'gender') && {
                    gender: 1,
                }),
            ...(tableFilter &&
                tableFilter.find((tableFilterElement) => tableFilterElement === 'user_id') && {
                    user_id: 1,
                }),
        };
        console.time('userDetails');
        const userDetails = await user
            .find(
                {
                    isDeleted: false,
                    _id: { $in: [...scheduleStaffIds, ...scheduleStudentIds] },
                    // ...(gender && { gender }),
                },
                userProject,
            )
            .sort({ user_id: 1 })
            .lean();
        console.timeEnd('userDetails');
        if (!userDetails)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('USER_DATA_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        const groupMerge = { group_id: [], group_name: '', schedule_data: [], session_setting: [] };
        for (groupElement of groups) {
            groupMerge.group_id.push(groupElement.group_id);
            groupMerge.group_name =
                groupMerge.group_name === ''
                    ? groupElement.group_name
                    : groupMerge.group_name + ',' + groupElement.group_name;
            groupMerge.session_setting.push(...groupElement.session_setting);
        }
        const disciplinaryRemarksData = await disciplinaryRemarksSchema
            .find(
                {
                    institutionCalendarId: convertToMongoObjectId(institutionCalendarId),
                    scheduleId: courseScheduleDatas.map(({ _id }) => convertToMongoObjectId(_id)),
                    type: SCHEDULE_LEVEL,
                    isDeleted: false,
                    isActive: true,
                },
                { scheduleId: 1, tardisId: 1, studentId: 1 },
            )
            .populate({ path: 'tardisId', select: { name: 1, short_code: 1 } })
            .lean();
        //Group wise students
        for (groupElement of [groupMerge]) {
            console.time('groupElement');
            let groupStaffIds = [];
            let groupStudentIds = [];
            const sessionSettingGroups = isDeliveryChange
                ? groupElement.session_setting.flatMap((settingElement) => settingElement.groups)
                : [];
            courseScheduleDatas.forEach((courseScheduleElement) => {
                const scheduleGroupFind = courseScheduleElement.student_groups.find(
                    (scheduleSGElement) =>
                        isDeliveryChange
                            ? groupIdsArray
                                  .map((groupElement) => groupElement.toString())
                                  .includes(scheduleSGElement.group_id.toString()) &&
                              (sessionGroupIdsArray.length > 0
                                  ? scheduleSGElement.session_group?.some((groupSettingElement) =>
                                        sessionGroupIdsArray
                                            .map((groupElement) => groupElement.toString())
                                            .includes(
                                                groupSettingElement.session_group_id.toString(),
                                            ),
                                    )
                                  : true)
                            : groupElement.group_id.some(
                                  (groupElementIds) =>
                                      groupElementIds.toString() ===
                                      scheduleSGElement.group_id.toString(),
                              ),
                );
                if (scheduleGroupFind) {
                    groupElement.schedule_data.push(courseScheduleElement);
                    if (type === DC_STAFF)
                        groupStaffIds = [
                            ...groupStaffIds,
                            ...courseScheduleElement.staffs.map((staffElement) =>
                                staffElement._staff_id.toString(),
                            ),
                        ];
                    if (isDeliveryChange) {
                        const filteredStudent = sessionSettingGroups
                            .filter((settingElement) =>
                                sessionGroupIdsArray
                                    .map((groupElement) => groupElement.toString())
                                    .includes(settingElement._id.toString()),
                            )
                            .flatMap((studentElement) => studentElement._student_ids);

                        groupStudentIds = [...groupStudentIds, ...filteredStudent];
                    } else {
                        groupStudentIds = [
                            ...groupStudentIds,
                            ...courseScheduleElement.students.map((studentElement) =>
                                studentElement._id.toString(),
                            ),
                        ];
                    }
                }
            });
            const staffIdsSet = new Set(groupStaffIds.map(String));
            const studentIdsSet = new Set(groupStudentIds.map(String));
            // Staff Listing
            if (type === DC_STAFF) {
                groupElement.staffs = userDetails
                    .filter(
                        (userElement) => staffIdsSet.has(userElement._id.toString()),
                        // scheduleStaffIds.find(
                        //     (staffElement) =>
                        //         staffElement.toString() === userElement._id.toString(),
                        // ),
                    )
                    .map((userElement) => {
                        let overAllStatus;
                        if (
                            tableFilter &&
                            tableFilter.find(
                                (tableFilterElement) => tableFilterElement === 'overall',
                            )
                        ) {
                            overAllStatus = overAllStaffDetails.find(
                                (staffElement) =>
                                    staffElement._staff_id.toString() ===
                                    userElement._id.toString(),
                            );
                        }
                        return {
                            _staff_id: userElement._id,
                            staff_name: userElement.name,
                            gender: userElement.gender,
                            academic_no: userElement.user_id,
                            session_details: [],
                            ...(tableFilter &&
                                tableFilter.find(
                                    (tableFilterElement) => tableFilterElement === 'monthly',
                                ) && {
                                    currentMonth: {
                                        sessionCount: 0,
                                        attendedCount: 0,
                                        percentage: 0,
                                    },
                                }),
                            ...(tableFilter &&
                                tableFilter.find(
                                    (tableFilterElement) => tableFilterElement === 'overall',
                                ) && {
                                    overall: {
                                        sessionCount: overAllStatus
                                            ? overAllStatus.sessionCount
                                            : 0,
                                        attendedCount: overAllStatus
                                            ? overAllStatus.attendedCount
                                            : 0,
                                        percentage: overAllStatus ? overAllStatus.percentage : 0,
                                    },
                                }),
                        };
                    });
            }
            groupElement.students = userDetails
                .filter(
                    (userElement) => studentIdsSet.has(userElement._id.toString()),
                    // scheduleStudentIds.find(
                    //     (studentElement) =>
                    //         studentElement.toString() === userElement._id.toString(),
                    // ),
                )
                .map((userElement) => {
                    let overAllStatus;
                    if (
                        tableFilter &&
                        tableFilter.find((tableFilterElement) => tableFilterElement === 'overall')
                    ) {
                        overAllStatus = overAllStudentDetails.find(
                            (studentElement) =>
                                studentElement._id.toString() === userElement._id.toString(),
                        );
                    }
                    return {
                        _id: userElement._id,
                        name: userElement.name,
                        gender: userElement.gender,
                        academic_no: userElement.user_id,
                        session_details: [],
                        ...(tableFilter &&
                            tableFilter.find(
                                (tableFilterElement) => tableFilterElement === 'monthly',
                            ) && {
                                currentMonth: {
                                    sessionCount: 0,
                                    attendedCount: 0,
                                    presentCount: 0,
                                    absentCount: 0,
                                    permissionCount: 0,
                                    onDutyCount: 0,
                                    leaveCount: 0,
                                    percentage: 0,
                                    studentLateAbsent: 0,
                                },
                            }),
                        ...(tableFilter &&
                            tableFilter.find(
                                (tableFilterElement) => tableFilterElement === 'overall',
                            ) && {
                                overall: {
                                    sessionCount: overAllStatus ? overAllStatus.sessionCount : 0,
                                    attendedCount: overAllStatus ? overAllStatus.attendedCount : 0,
                                    presentCount: overAllStatus ? overAllStatus.presentCount : 0,
                                    absentCount: overAllStatus ? overAllStatus.absentCount : 0,
                                    permissionCount: overAllStatus
                                        ? overAllStatus.permissionCount
                                        : 0,
                                    leaveCount: overAllStatus ? overAllStatus.leaveCount : 0,
                                    onDutyCount: overAllStatus ? overAllStatus.onDutyCount : 0,
                                    percentage: overAllStatus ? overAllStatus.percentage : 0,
                                    studentLateAbsent: overAllStatus
                                        ? overAllStatus.studentLateAbsent
                                        : 0,
                                },
                            }),
                    };
                });

            const studentMap = new Map(
                groupElement.students.map((studentElement) => [
                    studentElement._id.toString(),
                    studentElement,
                ]),
            );
            const userHeaders = [];
            groupElement.schedule_data.forEach((scheduleElement) => {
                const sessionWithGroupLabel = getConcatSessionGroup(scheduleElement);
                let studentLateAbsent = 0;
                studentMap.forEach((studentElement) => {
                    const lateExcludeForStudent = checkLateExcludeConfigurationForCourseOrStudent({
                        _institution_calendar_id: institutionCalendarId,
                        courseId,
                        programId,
                        levelNo: level,
                        term,
                        rotationCount: rotation_count,
                        lateExcludeManagement,
                        studentId: studentElement._id,
                    }).lateExclude;
                    if (!lateExclude && !lateExcludeForStudent) {
                        const { studentLateAbsent: updatedStudentLateAbsent } =
                            getLateConfigAndStudentLateAbsent({
                                lateDurationRange,
                                manualLateRange,
                                manualLateData,
                                scheduleData: [scheduleElement],
                                studentElement: { _student_id: studentElement._id },
                                lateExcludeManagement,
                            });
                        studentLateAbsent = updatedStudentLateAbsent;
                    }
                    const studentSchedule = scheduleElement.students.find(
                        (studentScheduleElement) =>
                            studentScheduleElement._id.toString() === studentElement._id.toString(),
                    );
                    let lateLabel = null;
                    if (!lateExclude && !lateExcludeForStudent) {
                        const { lateLabel: updatedLateLabel } = getLateLabelForSchedule({
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            schedule: {
                                status: scheduleElement.status,
                                scheduleStartDateAndTime: scheduleElement.sessionDetail.start_time,
                                session: scheduleElement.session,
                            },
                            student_data: {
                                _id: studentSchedule?._id,
                                status: studentSchedule?.status,
                                primaryTime: studentSchedule?.primaryTime,
                                lateExclude: studentSchedule?.lateExclude,
                                tardisId: studentSchedule?.tardisId,
                            },
                            _institution_calendar_id: institutionCalendarId,
                            courseId,
                            programId,
                            levelNo: level,
                            term,
                            rotationCount: rotation_count,
                            lateExcludeManagement,
                        });
                        lateLabel = updatedLateLabel;
                    }
                    const studentTardisData = disciplinaryRemarksData.find(
                        (remarkElement) =>
                            remarkElement.scheduleId.toString() ===
                                scheduleElement._id?.toString() &&
                            remarkElement.studentId.toString() === studentElement._id?.toString(),
                    );
                    studentElement.session_details.push({
                        session: sessionWithGroupLabel,
                        s_no: scheduleElement.session.s_no,
                        session_no:
                            scheduleElement.session.delivery_symbol +
                            scheduleElement.session.delivery_no,
                        schedule_id: scheduleElement._id,
                        schedule_status: scheduleElement.status,
                        schedule_date: scheduleElement.schedule_date,
                        group_status: Boolean(studentSchedule),
                        attendance: studentSchedule ? studentSchedule.status : undefined,
                        tardisId: studentTardisData ? studentTardisData.tardisId : undefined,
                        primaryTime: studentSchedule ? studentSchedule.primaryTime : undefined,
                        lateExclude: studentSchedule ? studentSchedule.lateExclude : undefined,
                        scheduleStartDateAndTime: scheduleElement
                            ? scheduleElement.scheduleStartDateAndTime
                            : undefined,
                        sessionDetail: scheduleElement ? scheduleElement.sessionDetail : undefined,
                        sessionId: scheduleElement
                            ? scheduleElement.session._session_id
                            : undefined,
                        reasonForLate:
                            studentSchedule && studentSchedule.reasonForLate
                                ? studentSchedule.reasonForLate
                                : undefined,
                        isActive: scheduleElement.isActive,
                        lateLabel: lateLabel ? lateLabel.lateLabel : null,
                        classModeType: scheduleElement.classModeType,
                    });
                    if (
                        studentSchedule &&
                        tableFilter &&
                        tableFilter.find(
                            (tableFilterElement) => tableFilterElement === 'monthly',
                        ) &&
                        scheduleElement.status === COMPLETED &&
                        studentSchedule.status !== EXCLUDE
                    ) {
                        const increment = scheduleElement.merge_status
                            ? 1 + scheduleElement.merge_with.length
                            : 1;
                        if (studentSchedule.status === PRESENT) {
                            studentElement.currentMonth.presentCount += increment;
                        } else if (studentSchedule.status === ABSENT) {
                            studentElement.currentMonth.absentCount += increment;
                        } else if (studentSchedule.status === PERMISSION) {
                            studentElement.currentMonth.permissionCount += increment;
                        } else if (studentSchedule.status === ONDUTY) {
                            studentElement.currentMonth.onDutyCount += increment;
                        } else if (studentSchedule.status === LEAVE) {
                            studentElement.currentMonth.leaveCount += increment;
                        }
                        studentElement.currentMonth.sessionCount += increment;
                        studentElement.currentMonth.attendedCount +=
                            studentSchedule.status === PRESENT ||
                            studentSchedule.status === PERMISSION ||
                            studentSchedule.status === ONDUTY
                                ? increment
                                : 0;
                        if (studentLateAbsent) {
                            studentElement.currentMonth.studentLateAbsent += studentLateAbsent;
                        }
                    }
                });
                // scheduleElement.students.forEach((studentScheduleElement) => {
                //     const studentIdString = studentScheduleElement._id.toString();
                //     const studentElement = studentMap.get(studentIdString);
                //     studentElement.session_details.push({
                //         session: sessionWithGroupLabel,
                //         s_no: scheduleElement.session.s_no,
                //         session_no:
                //             scheduleElement.session.delivery_symbol +
                //             scheduleElement.session.delivery_no,
                //         schedule_id: scheduleElement._id,
                //         schedule_status: scheduleElement.status,
                //         schedule_date: scheduleElement.schedule_date,
                //         group_status: Boolean(studentScheduleElement),
                //         attendance: studentScheduleElement
                //             ? studentScheduleElement.status
                //             : undefined,
                //         isActive: scheduleElement.isActive,
                //     });
                //     if (
                //         studentScheduleElement &&
                //         tableFilter &&
                //         tableFilter.find((tableFilterElement) => tableFilterElement === 'monthly')
                //     ) {
                //         studentElement.currentMonth.sessionCount++;
                //         studentElement.currentMonth.attendedCount +=
                //             studentScheduleElement &&
                //             studentScheduleElement.status &&
                //             (studentScheduleElement.status === PRESENT ||
                //                 studentScheduleElement.status === ONDUTY)
                //                 ? 1
                //                 : 0;
                //     }
                // });

                // Headers Data Adding
                let sessionName = sessionWithGroupLabel.toString();
                let deliveryTypeName =
                    scheduleElement.session.delivery_symbol + scheduleElement.session.delivery_no;
                if (scheduleElement.merge_status) {
                    sessionName = `${groupElement.group_name}-${sessionWithGroupLabel.toString()}`;
                    // sessionName = `${
                    //     scheduleElement.student_groups[0].group_name
                    // }-${sessionWithGroupLabel.toString()}`;
                    scheduleElement.merge_with.forEach((mergedScheduleElement) => {
                        sessionName += ',';
                        let i = 0;
                        if (
                            mergedScheduleElement &&
                            mergedScheduleElement.schedule_id &&
                            mergedScheduleElement.schedule_id.student_groups
                        ) {
                            mergedScheduleElement.schedule_id.student_groups.forEach(
                                (studentGroupElement) => {
                                    const groupName =
                                        groupLevelElement.group_mode === ROTATION
                                            ? studentGroupElement.gender === MALE
                                                ? 'RMG'
                                                : studentGroupElement.gender === FEMALE
                                                ? 'RFG'
                                                : 'RSG'
                                            : studentGroupElement.gender === MALE
                                            ? 'MG'
                                            : studentGroupElement.gender === FEMALE
                                            ? 'FG'
                                            : 'SG';
                                    if (studentGroupElement.gender == MALE) {
                                        sessionName +=
                                            groupName +
                                            studentGroupElement.group_no.toString() +
                                            '-' +
                                            mergedScheduleElement.schedule_id.session
                                                .delivery_symbol +
                                            mergedScheduleElement.schedule_id.session.delivery_no;
                                    } else {
                                        sessionName +=
                                            groupName +
                                            studentGroupElement.group_no.toString() +
                                            '-' +
                                            mergedScheduleElement.schedule_id.session
                                                .delivery_symbol +
                                            mergedScheduleElement.schedule_id.session.delivery_no;
                                    }
                                    studentGroupElement.session_group.forEach((eleSG, indexSG) => {
                                        if (i == 0) {
                                            if (studentGroupElement.session_group.length == 1)
                                                sessionName += '-G' + eleSG.group_no;
                                            else sessionName += '-G' + eleSG.group_no + ',';
                                        } else {
                                            if (
                                                studentGroupElement.session_group.length - 1 !=
                                                indexSG
                                            )
                                                sessionName += 'G' + eleSG.group_no + ',';
                                            else sessionName += 'G' + eleSG.group_no;
                                        }
                                        i++;
                                    });
                                },
                            );
                            deliveryTypeName +=
                                ',' +
                                mergedScheduleElement.schedule_id.session.delivery_symbol +
                                mergedScheduleElement.schedule_id.session.delivery_no;
                        }
                    });
                }
                userHeaders.push({
                    session: sessionName,
                    s_no: scheduleElement.session.s_no,
                    session_no: scheduleElement.session.delivery_no,
                    delivery_symbol: scheduleElement.session.delivery_symbol,
                    schedule_id: scheduleElement._id,
                    delivery_type: deliveryTypeName,
                    session_topic: scheduleElement.session.session_topic,
                    schedule_status: scheduleElement.status,
                    schedule_date: scheduleElement.schedule_date,
                    start: scheduleElement.start,
                    end: scheduleElement.end,
                    staffs: scheduleElement.staffs.map((staffElement) => {
                        const staffData = userDetails.find(
                            (userDetailElement) =>
                                userDetailElement._id.toString() ===
                                staffElement._staff_id.toString(),
                        );
                        return {
                            status: staffElement.status,
                            _staff_id: staffElement._staff_id,
                            staff_name: staffData.name,
                        };
                    }),
                    isActive: scheduleElement.isActive,
                });
            });
            groupElement.staff_header = clone(userHeaders);
            groupElement.student_header = clone(userHeaders);
            if (
                tableFilter &&
                tableFilter.find((tableFilterElement) => tableFilterElement === 'monthly')
            )
                studentMap.forEach((studentElement) => {
                    const { presentPercentage } = calculateAttendancePercentages({
                        totalPresent: studentElement.currentMonth.presentCount,
                        totalAbsent: studentElement.currentMonth.absentCount,
                        totalLeave: studentElement.currentMonth.leaveCount,
                        totalPermission: studentElement.currentMonth.permissionCount,
                        totalOnDuty: studentElement.currentMonth.onDutyCount,
                        totalCompletedSchedule: studentElement.currentMonth.sessionCount,
                        totalSchedules: studentElement.currentMonth.sessionCount,
                        studentLateAbsent: studentElement.currentMonth.studentLateAbsent,
                    });
                    studentElement.currentMonth.percentage = presentPercentage;
                });
            groupElement.students = Array.from(studentMap.values());
            // Student Details
            // groupElement.students.forEach((studentElement) => {
            //     groupElement.schedule_data.forEach((scheduleElement) => {
            //         const studentScheduleData = scheduleElement.students.find(
            //             (studentScheduleElement) =>
            //                 studentScheduleElement._id.toString() === studentElement._id.toString(),
            //         );
            //         const sessionWithGroupLabel = getConcatSessionGroup(scheduleElement);
            //         if (studentScheduleData) {
            //             studentElement.session_details.push({
            //                 session: sessionWithGroupLabel,
            //                 s_no: scheduleElement.session.s_no,
            //                 session_no:
            //                     scheduleElement.session.delivery_symbol +
            //                     scheduleElement.session.delivery_no,
            //                 schedule_id: scheduleElement._id,
            //                 schedule_status: scheduleElement.status,
            //                 schedule_date: scheduleElement.schedule_date,
            //                 group_status: true,
            //                 attendance: studentScheduleData.status,
            //                 isActive: scheduleElement.isActive,
            //             });
            //         } else {
            //             studentElement.session_details.push({
            //                 session: sessionWithGroupLabel,
            //                 s_no: scheduleElement.session.s_no,
            //                 session_no:
            //                     scheduleElement.session.delivery_symbol +
            //                     scheduleElement.session.delivery_no,
            //                 schedule_id: scheduleElement._id,
            //                 schedule_status: scheduleElement.status,
            //                 schedule_date: scheduleElement.schedule_date,
            //                 group_status: false,
            //                 isActive: scheduleElement.isActive,
            //             });
            //         }
            //     });
            // });

            // Staff Details
            if (type === DC_STAFF) {
                groupElement.staffs.forEach((staffElement) => {
                    groupElement.schedule_data.forEach((scheduleElement) => {
                        const staffScheduleData = scheduleElement.staffs.find(
                            (staffScheduleElement) =>
                                staffScheduleElement._staff_id.toString() ===
                                staffElement._staff_id.toString(),
                        );
                        const sessionWithGroupLabel = getConcatSessionGroup(scheduleElement);
                        if (staffScheduleData) {
                            staffElement.session_details.push({
                                session: sessionWithGroupLabel,
                                s_no: scheduleElement.session.s_no,
                                session_no:
                                    scheduleElement.session.delivery_symbol +
                                    scheduleElement.session.delivery_no,
                                schedule_id: scheduleElement._id,
                                schedule_status: scheduleElement.status,
                                schedule_date: scheduleElement.schedule_date,
                                group_status: true,
                                attendance: staffScheduleData.status,
                                isActive: scheduleElement.isActive,
                                classModeType: scheduleElement.classModeType,
                            });
                            if (
                                staffScheduleData.status &&
                                tableFilter &&
                                tableFilter.find(
                                    (tableFilterElement) => tableFilterElement === 'monthly',
                                ) &&
                                scheduleElement.status === COMPLETED
                            ) {
                                staffElement.currentMonth.sessionCount++;
                                staffElement.currentMonth.attendedCount +=
                                    staffScheduleData &&
                                    staffScheduleData.status &&
                                    (staffScheduleData.status === PRESENT ||
                                        staffScheduleData.status === ONDUTY ||
                                        staffScheduleData.status === PERMISSION)
                                        ? 1
                                        : 0;
                            }
                        } else {
                            staffElement.session_details.push({
                                session: sessionWithGroupLabel,
                                s_no: scheduleElement.session.s_no,
                                session_no:
                                    scheduleElement.session.delivery_symbol +
                                    scheduleElement.session.delivery_no,
                                schedule_id: scheduleElement._id,
                                schedule_status: scheduleElement.status,
                                schedule_date: scheduleElement.schedule_date,
                                group_status: false,
                                isActive: scheduleElement.isActive,
                                classModeType: scheduleElement.classModeType,
                            });
                        }
                    });
                    if (
                        tableFilter &&
                        tableFilter.find((tableFilterElement) => tableFilterElement === 'monthly')
                    )
                        staffElement.currentMonth.percentage =
                            (staffElement.currentMonth.attendedCount /
                                staffElement.currentMonth.sessionCount) *
                            100;
                });
                // Header Loop
                groupElement.staff_header.forEach((staffHeaderElement) => {
                    const staffSummary = {
                        present: 0,
                        absent: 0,
                        leave: 0,
                        onduty: 0,
                        permission: 0,
                    };
                    groupElement.staffs.forEach((staffElement) => {
                        const sessionDetailIndex = staffElement.session_details.findIndex(
                            (sessionDetailElement) =>
                                sessionDetailElement.schedule_id.toString() ===
                                    staffHeaderElement.schedule_id.toString() &&
                                sessionDetailElement.group_status === true,
                        );
                        if (sessionDetailIndex != -1) {
                            switch (staffElement.session_details[sessionDetailIndex].attendance) {
                                case LEAVE:
                                    staffSummary.leave++;
                                    break;
                                case PERMISSION:
                                    staffSummary.permission++;
                                    break;
                                case ABSENT:
                                    staffSummary.absent++;
                                    break;
                                case PRESENT:
                                    staffSummary.present++;
                                    break;
                                case ONDUTY:
                                    staffSummary.onduty++;
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
                    staffSummary.percentage =
                        ((staffSummary.present + staffSummary.onduty + staffSummary.permission) *
                            100) /
                        groupElement.staffs.length;
                    staffHeaderElement.summary = staffSummary;
                });
            }
            delete groupElement.schedule_data;
            // Student Headers
            const studentDetails = clone(groupElement.students);
            groupElement.student_header.forEach((studentHeaderElement) => {
                const studentSummary = {
                    present: 0,
                    absent: 0,
                    leave: 0,
                    onduty: 0,
                    permission: 0,
                    percentage: 0,
                    total_student: 0,
                    studentLateAbsent: 0,
                };
                studentDetails.forEach((studentElement, studentIndex) => {
                    const sessionDetailIndex = studentElement.session_details.findIndex(
                        (sessionDetailElement) =>
                            sessionDetailElement &&
                            sessionDetailElement.schedule_id &&
                            studentHeaderElement.schedule_id &&
                            sessionDetailElement.schedule_id.toString() ===
                                studentHeaderElement.schedule_id.toString() &&
                            sessionDetailElement.group_status === true,
                    );
                    if (
                        sessionDetailIndex != -1 &&
                        studentElement.session_details[sessionDetailIndex].attendance !== EXCLUDE
                    ) {
                        if (sessionDetailIndex != -1) {
                            const lateExcludeForStudent =
                                checkLateExcludeConfigurationForCourseOrStudent({
                                    _institution_calendar_id: institutionCalendarId,
                                    courseId,
                                    programId,
                                    levelNo: level,
                                    term,
                                    rotationCount: rotation_count,
                                    lateExcludeManagement,
                                    studentId: studentElement._id,
                                }).lateExclude;
                            let studentLateAbsent = 0;
                            if (!lateExclude && !lateExcludeForStudent) {
                                const { studentLateAbsent: updateStudentLateAbsent } =
                                    getLateConfigAndStudentLateAbsent({
                                        lateDurationRange,
                                        manualLateRange,
                                        manualLateData,
                                        scheduleData: [
                                            {
                                                _institution_calendar_id: institutionCalendarId,
                                                _course_id: courseId,
                                                _program_id: programId,
                                                levelNo: level,
                                                term,
                                                rotationCount: rotation_count,
                                                status: studentElement.session_details[
                                                    sessionDetailIndex
                                                ].schedule_status,
                                                scheduleStartDateAndTime:
                                                    studentElement.session_details[
                                                        sessionDetailIndex
                                                    ].scheduleStartDateAndTime,
                                                classModeType:
                                                    studentElement.session_details[
                                                        sessionDetailIndex
                                                    ].classModeType,
                                                session: {
                                                    _session_id:
                                                        studentElement.session_details[
                                                            sessionDetailIndex
                                                        ].sessionId,
                                                },
                                                students: [
                                                    {
                                                        _id: studentElement._id,
                                                        status: studentElement.session_details[
                                                            sessionDetailIndex
                                                        ].attendance,
                                                        primaryTime:
                                                            studentElement.session_details[
                                                                sessionDetailIndex
                                                            ].primaryTime,
                                                        lateExclude:
                                                            studentElement.session_details[
                                                                sessionDetailIndex
                                                            ].lateExclude,
                                                        tardisId:
                                                            studentElement.session_details[
                                                                sessionDetailIndex
                                                            ].tardisId,
                                                    },
                                                ],
                                            },
                                        ],
                                        studentElement: { _student_id: studentElement._id },
                                        lateExcludeManagement,
                                    });
                                studentLateAbsent = updateStudentLateAbsent;
                            }
                            studentSummary.studentLateAbsent += studentLateAbsent;
                            studentSummary.total_student++;
                            switch (studentElement.session_details[sessionDetailIndex].attendance) {
                                case LEAVE:
                                    studentSummary.leave++;
                                    break;
                                case PERMISSION:
                                    studentSummary.permission++;
                                    break;
                                case ABSENT:
                                    studentSummary.absent++;
                                    break;
                                case PRESENT:
                                    studentSummary.present++;
                                    break;
                                case ONDUTY:
                                    studentSummary.onduty++;
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                });
                studentSummary.percentage =
                    ((studentSummary.present + studentSummary.onduty + studentSummary.permission) *
                        100) /
                    studentSummary.total_student;
                studentHeaderElement.summary = studentSummary;
            });
            console.timeEnd('groupElement');
        }
        return res.status(200).send(
            responseFunctionWithRequest(req, 200, true, req.t('ATTENDANCE_LOG'), {
                course_details: courseResponse,
                attendance_log: [groupMerge],
            }),
        );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};

exports.courseAttendanceLogExport = async (req, res) => {
    try {
        const {
            headers: { _institution_id },
            params: { institutionCalendarId, programId, courseId, term, scheduleType, level },
            query: {
                rotation_count,
                groupName,
                type,
                firstDate,
                lastDate,
                groupIds,
                sessionGroupIds,
                isDeliveryGroup,
            },
        } = req;
        let {
            query: { tableFilter },
        } = req;
        if (!Array.isArray(tableFilter)) tableFilter = [tableFilter];
        const studentGroupFilter = groupName
            ? Array.isArray(groupName)
                ? groupName
                : [groupName]
            : [];
        const courseResponse = {};
        const studentGroupData = (
            await allStudentGroupYesterday(
                institutionCalendarId,
                scheduleDateFormateChange(new Date()),
            )
        ).find(
            (sgElement) =>
                sgElement.master._program_id.toString() === programId.toString() &&
                sgElement._institution_calendar_id.toString() ===
                    institutionCalendarId.toString() &&
                sgElement.groups.find(
                    (groupElement) =>
                        groupElement.term === term &&
                        groupElement.level === level &&
                        groupElement.courses.some(
                            (courseElement) =>
                                courseElement._course_id.toString() === courseId.toString(),
                        ),
                ),
        );
        if (!studentGroupData)
            return res
                .status(404)
                .send(
                    responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                        req.t('STUDENT_GROUP_NOT_FOUND'),
                    ),
                );

        const isDeliveryChange = isDeliveryGroup === 'true';
        let groupIdsArray = [];
        if (groupIds && isDeliveryChange) {
            try {
                if (Array.isArray(groupIds)) {
                    groupIdsArray = groupIds;
                } else {
                    groupIdsArray = JSON.parse(groupIds);
                }
            } catch (error) {
                groupIdsArray = [groupIds];
            }
        }

        let sessionGroupIdsArray = [];
        if (sessionGroupIds && isDeliveryChange) {
            try {
                if (Array.isArray(sessionGroupIds)) {
                    sessionGroupIdsArray = sessionGroupIds;
                } else {
                    sessionGroupIdsArray = JSON.parse(sessionGroupIds);
                }
            } catch (error) {
                sessionGroupIdsArray = [sessionGroupIds];
            }
        }

        if (isDeliveryChange && groupIdsArray.length === 0) {
            return res.status(404).send(
                responseFunctionWithRequest(req, 404, true, 'Please Select Groups', {
                    course_details: [],
                }),
            );
        }

        const groupLevelElement = studentGroupData.groups.find(
            (levelElement) => levelElement.term === term && levelElement.level === level,
        );
        if (!groupLevelElement)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('NO_DATA_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        let groups = [];
        const groupNaming = (gender) => {
            let genderName = '';
            switch (gender) {
                case MALE:
                    genderName = 'MG';
                    break;
                case FEMALE:
                    genderName = 'FG';
                    break;
                case BOTH:
                    genderName = 'SG';
                    break;
                default:
                    break;
            }
            return genderName;
        };

        const filterCourseData = groupLevelElement.courses.find(
            (courseElement) => courseElement._course_id.toString() === courseId.toString(),
        );
        if (!filterCourseData)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );

        switch (groupLevelElement.group_mode) {
            case FYD:
                groupLevelElement.group_setting.forEach((groupSettingElement) => {
                    groupSettingElement.groups.forEach((groupElement) => {
                        const filterSetting = filterCourseData.setting
                            .filter(
                                (settingElement) =>
                                    settingElement._group_no.toString() ===
                                    groupElement.group_no.toString(),
                            )
                            .flatMap((settingElement) => settingElement.session_setting);
                        groups.push({
                            group_id: groupElement._id,
                            group_no: groupElement.group_no,
                            group_name: groupElement.group_name.substring(
                                groupElement.group_name.length - 4,
                            ),
                            // group_name: `F-${groupNaming(groupElement.gender)}${
                            //     groupElement.group_no
                            // }`,
                            gender: groupElement.gender,
                            schedule_data: [],
                            session_setting: filterSetting,
                        });
                    });
                });
                break;
            case COURSE:
                filterCourseData.setting
                    .filter((settingElement) =>
                        isDeliveryChange && groupIdsArray.length > 0
                            ? groupIdsArray
                                  .map((groupElement) => groupElement.toString())
                                  .includes(settingElement._id.toString())
                            : true,
                    )
                    .forEach((groupElement) => {
                        groups.push({
                            group_id: groupElement._id,
                            group_name:
                                groupNaming(groupElement.gender) === 'SG'
                                    ? `${groupNaming(groupElement.gender)}1`
                                    : `${groupNaming(groupElement.gender)}-1`,
                            gender: groupElement.gender,
                            schedule_data: [],
                            session_setting: groupElement.session_setting,
                        });
                    });

                break;
            case ROTATION:
                groupLevelElement.rotation_group_setting.forEach((groupSettingElement) => {
                    const filterSetting = filterCourseData.setting
                        .filter(
                            (settingElement) =>
                                settingElement._group_no.toString() === rotation_count,
                        )
                        .flatMap((settingElement) => settingElement.session_setting);
                    groups.push({
                        group_id: groupSettingElement._id,
                        group_no: groupSettingElement.group_no,
                        group_name: groupSettingElement.group_name.substring(
                            groupSettingElement.group_name.length - 5,
                        ),
                        // group_name: `R-${groupNaming(groupSettingElement.gender)}${
                        //     groupSettingElement.group_no
                        // }`,
                        gender: groupSettingElement.gender,
                        schedule_data: [],
                        session_setting: filterSetting,
                    });
                });
                break;
            default:
                break;
        }
        if (!groups.length)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, 'Student Groups Not Found', {
                    course_details: courseResponse,
                }),
            );
        const courseGroup = groupLevelElement.courses.find(
            (levelElement) => levelElement._course_id.toString() === courseId.toString(),
        );
        if (!courseGroup)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        const scheduleQuery = {
            isDeleted: false,
            _program_id: convertToMongoObjectId(programId),
            _course_id: convertToMongoObjectId(courseId),
            type: scheduleType,
            term,
        };
        courseResponse.sgGroups = groups.map(
            (studentGroupElement) => studentGroupElement.group_name,
        );

        if (isDeliveryChange && groupIdsArray.length > 0) {
            scheduleQuery['student_groups.group_id'] = {
                $in: groupIdsArray.map((groupElement) =>
                    convertToMongoObjectId(groupElement.toString()),
                ),
            };
            if (sessionGroupIdsArray.length > 0)
                scheduleQuery['student_groups.session_group.session_group_id'] = {
                    $in: sessionGroupIdsArray.map((groupElement) =>
                        convertToMongoObjectId(groupElement.toString()),
                    ),
                };
        } else if (studentGroupFilter.length) {
            groups = groups.filter((groupElement) =>
                studentGroupFilter.find(
                    (studentGroupElement) =>
                        studentGroupElement.toString() === groupElement.group_name.toString(),
                ),
            );
            scheduleQuery['student_groups.group_id'] = {
                $in: groups.map((groupElement) => convertToMongoObjectId(groupElement.group_id)),
            };
        } else if (groups.length > 0) {
            groups = [groups[0]];
            scheduleQuery['student_groups.group_id'] = convertToMongoObjectId(groups[0].group_id);
        }
        if (firstDate && lastDate) {
            scheduleQuery.schedule_date = {
                $gte: new Date(firstDate),
                $lte: new Date(lastDate),
            };
        } else {
            // Course End Date based Flow
            const courseStartDate = new Date(courseResponse.start_date);
            scheduleQuery.schedule_date = {
                $gte: new Date(courseStartDate.getFullYear(), courseStartDate.getMonth(), 1),
                $lte: new Date(courseResponse.end_date),
            };
        }
        const { lateDurationRange, manualLateRange, manualLateData } =
            await getLateAutoAndManualRange({
                _institution_id,
            });
        const courseScheduleProject = {
            _id: 1,
            'sessionDetail.start_time': 1,
            scheduleStartDateAndTime: 1,
            'session.s_no': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'student_groups.group_id': 1,
            'student_groups.group_name': 1,
            'student_groups.session_group.group_no': 1,
            'students._id': 1,
            'students.status': 1,
            'students.tardisId': 1,
            'students.time': 1,
            'students.primaryTime': 1,
            'students.lateExclude': 1,
            'students.primaryStatus': 1,
            isActive: 1,
            status: 1,
            merge_status: 1,
            merge_with: 1,
            schedule_date: 1,
        };
        console.time('courseSchedule');
        let courseScheduleDatas = await course_schedule
            .find(scheduleQuery, courseScheduleProject)
            .populate({
                path: 'merge_with.schedule_id',
                select: {
                    // 'session.s_no': 1,
                    'session.delivery_symbol': 1,
                    'session.delivery_no': 1,
                    'student_groups.gender': 1,
                    'student_groups.group_no': 1,
                    'student_groups.session_group.group_no': 1,
                },
            })
            .populate({ path: 'students.tardisId', select: { name: 1, short_code: 1 } })
            .sort({
                scheduleStartDateAndTime: 1,
            })
            .lean();
        console.timeEnd('courseSchedule');
        const courseScheduleDocs = courseScheduleDatas;
        if (!courseScheduleDatas)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('COURSE_SCHEDULE_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        let mergedScheduleId = [];
        let scheduleStudentIds = [];
        const scheduleIds = [];
        courseScheduleDatas = courseScheduleDatas.filter((scheduleElement) => {
            scheduleIds.push(scheduleElement._id);
            scheduleStudentIds = [
                ...scheduleStudentIds,
                ...scheduleElement.students.map((studentElement) => studentElement._id.toString()),
            ];
            if (scheduleElement.merge_status) {
                const mergeScheduleId = scheduleElement.merge_with.map((mergeScheduleElement) =>
                    mergeScheduleElement.schedule_id._id.toString(),
                );
                mergeScheduleId.push(scheduleElement._id.toString());
                if (
                    !mergeScheduleId.find((mergeScheduleIdElement) =>
                        mergedScheduleId.find(
                            (mergedScheduleIdElement) =>
                                mergedScheduleIdElement.toString() ===
                                mergeScheduleIdElement.toString(),
                        ),
                    )
                ) {
                    mergedScheduleId = [...mergedScheduleId, ...mergeScheduleId];
                    return true;
                }
                return false;
            }
            return true;
        });
        console.time('scheduleAttendanceSchema');
        let scheduleMultiAttendance = await scheduleAttendanceSchema
            .find(
                {
                    scheduleId: { $in: scheduleIds },
                    students: { $not: { $size: 0 } },
                },
                { scheduleId: 1, modeBy: 1, students: 1, createdAt: 1 },
            )
            .lean();
        console.timeEnd('scheduleAttendanceSchema');
        if (!scheduleMultiAttendance) scheduleMultiAttendance = [];
        scheduleStudentIds = [...new Set(scheduleStudentIds)];
        const userProject = {
            _id: 1,
            name: 1,
        };
        console.time('userDetails');
        const userDetails = await user
            .find(
                {
                    isDeleted: false,
                    _id: { $in: scheduleStudentIds },
                },
                userProject,
            )
            .sort({ user_id: 1 })
            .lean();
        console.timeEnd('userDetails');
        if (!userDetails)
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('USER_DATA_NOT_FOUND'), {
                    course_details: courseResponse,
                }),
            );
        const groupMerge = { group_id: [], group_name: '', schedule_data: [], session_setting: [] };
        for (groupElement of groups) {
            groupMerge.group_id.push(groupElement.group_id);
            groupMerge.group_name =
                groupMerge.group_name === ''
                    ? groupElement.group_name
                    : groupMerge.group_name + ',' + groupElement.group_name;
            groupMerge.session_setting.push(...groupElement.session_setting);
        }
        //Group wise students
        // eslint-disable-next-line no-unreachable-loop
        for (const groupElement of [groupMerge]) {
            console.time('groupElement');
            let groupStudentIds = [];
            const sessionSettingGroups = isDeliveryChange
                ? groupElement.session_setting.flatMap((settingElement) => settingElement.groups)
                : [];

            courseScheduleDatas.forEach((courseScheduleElement) => {
                const scheduleGroupFind = courseScheduleElement.student_groups.find(
                    (scheduleSGElement) =>
                        isDeliveryChange
                            ? groupIdsArray
                                  .map((groupElement) => groupElement.toString())
                                  .includes(scheduleSGElement.group_id.toString()) &&
                              (sessionGroupIdsArray.length > 0
                                  ? scheduleSGElement.session_group?.some((groupSettingElement) =>
                                        sessionGroupIdsArray
                                            .map((groupElement) => groupElement.toString())
                                            .includes(
                                                groupSettingElement.session_group_id.toString(),
                                            ),
                                    )
                                  : true)
                            : groupElement.group_id.some(
                                  (groupElementIds) =>
                                      groupElementIds.toString() ===
                                      scheduleSGElement.group_id.toString(),
                              ),
                );
                if (scheduleGroupFind) {
                    groupElement.schedule_data.push(courseScheduleElement);
                    if (isDeliveryChange) {
                        const filteredStudent = sessionSettingGroups
                            .filter((settingElement) =>
                                sessionGroupIdsArray
                                    .map((groupElement) => groupElement.toString())
                                    .includes(settingElement._id.toString()),
                            )
                            .flatMap((studentElement) => studentElement._student_ids);

                        groupStudentIds = [...groupStudentIds, ...filteredStudent];
                    } else {
                        groupStudentIds = [
                            ...groupStudentIds,
                            ...courseScheduleElement.students.map((studentElement) =>
                                studentElement._id.toString(),
                            ),
                        ];
                    }
                }
            });
            const studentIdsSet = new Set(groupStudentIds.map(String));
            groupElement.students = userDetails
                .filter((userElement) => studentIdsSet.has(userElement._id.toString()))
                .map((userElement) => {
                    const { studentLateAbsent } = getLateConfigAndStudentLateAbsent({
                        lateDurationRange,
                        manualLateRange,
                        manualLateData,
                        scheduleData: courseScheduleDocs,
                        studentElement: { _student_id: userElement._id },
                    });
                    return {
                        _id: userElement._id,
                        name: userElement.name,
                        // gender: userElement.gender,
                        // academic_no: userElement.user_id,
                        session_details: [],
                        studentLateAbsent,
                    };
                });

            const studentMap = new Map(
                groupElement.students.map((studentElement) => [
                    studentElement._id.toString(),
                    studentElement,
                ]),
            );
            const sessionList = [];
            groupElement.schedule_data = clone(groupElement.schedule_data);
            groupElement.schedule_data.forEach((scheduleElement) => {
                const sessionWithGroupLabel = getConcatSessionGroup(scheduleElement);
                const multiAttendance = scheduleMultiAttendance.filter((attendanceElement) =>
                    attendanceElement.scheduleId.find(
                        (scheduleIdElement) =>
                            scheduleIdElement.toString() === scheduleElement._id.toString(),
                    ),
                );
                const modeByIds = {
                    primary: convertToMongoObjectId(),
                    final: convertToMongoObjectId(),
                };
                studentMap.forEach((studentElement) => {
                    const studentSchedule = scheduleElement.students.find(
                        (studentScheduleElement) =>
                            studentScheduleElement._id.toString() === studentElement._id.toString(),
                    );
                    const studentScheduleStatus = Boolean(studentSchedule);
                    const sessionObject = {
                        schedule_id: scheduleElement._id,
                        group_status: studentScheduleStatus,
                        attendance: [],
                        tardisId: studentSchedule ? studentSchedule.tardisId : undefined,
                        lateLabel: null,
                    };
                    if (studentScheduleStatus && studentSchedule) {
                        const { lateLabel } = getLateLabelForSchedule({
                            lateDurationRange,
                            manualLateRange,
                            manualLateData,
                            schedule: scheduleElement,
                            student_data: studentSchedule,
                        });
                        sessionObject.lateLabel = lateLabel ? lateLabel.lateLabel : null;
                        sessionObject.attendance.push({
                            _id: modeByIds.primary,
                            mode: 'primary',
                            time: studentSchedule.primaryTime
                                ? studentSchedule.primaryTime
                                : studentSchedule.time,
                            status: studentSchedule.primaryStatus
                                ? studentSchedule.primaryStatus
                                : studentSchedule.status,
                        });

                        for (const multiAttendanceElement of multiAttendance) {
                            const studentMultiAttendance = multiAttendanceElement.students.find(
                                (studentScheduleElement) =>
                                    studentScheduleElement._student_id.toString() ===
                                    studentElement._id.toString(),
                            );
                            if (studentMultiAttendance)
                                sessionObject.attendance.push({
                                    _id: multiAttendanceElement._id,
                                    mode: multiAttendanceElement.modeBy,
                                    time: studentMultiAttendance.time,
                                    status: studentMultiAttendance.status,
                                });
                        }
                        sessionObject.attendance.push({
                            _id: modeByIds.final,
                            mode: 'final',
                            time: studentSchedule.time,
                            status: studentSchedule.status,
                        });
                    }
                    studentElement.session_details.push(sessionObject);
                });
                let sessionName = sessionWithGroupLabel.toString();
                if (scheduleElement.merge_status) {
                    sessionName = `${
                        scheduleElement.student_groups[0].group_name
                    }-${sessionWithGroupLabel.toString()}`;
                    scheduleElement.merge_with.forEach((mergedScheduleElement) => {
                        sessionName += ',';
                        let i = 0;
                        mergedScheduleElement.schedule_id.student_groups.forEach(
                            (studentGroupElement) => {
                                const groupName =
                                    groupLevelElement.group_mode === ROTATION
                                        ? studentGroupElement.gender === MALE
                                            ? 'RMG'
                                            : studentGroupElement.gender === FEMALE
                                            ? 'RFG'
                                            : 'RSG'
                                        : studentGroupElement.gender === MALE
                                        ? 'MG'
                                        : studentGroupElement.gender === FEMALE
                                        ? 'FG'
                                        : 'SG';
                                if (studentGroupElement.gender == MALE) {
                                    sessionName +=
                                        groupName +
                                        studentGroupElement.group_no.toString() +
                                        '-' +
                                        mergedScheduleElement.schedule_id.session.delivery_symbol +
                                        mergedScheduleElement.schedule_id.session.delivery_no;
                                } else {
                                    sessionName +=
                                        groupName +
                                        studentGroupElement.group_no.toString() +
                                        '-' +
                                        mergedScheduleElement.schedule_id.session.delivery_symbol +
                                        mergedScheduleElement.schedule_id.session.delivery_no;
                                }
                                studentGroupElement.session_group.forEach((eleSG, indexSG) => {
                                    if (i == 0) {
                                        if (studentGroupElement.session_group.length == 1)
                                            sessionName += '-G' + eleSG.group_no;
                                        else sessionName += '-G' + eleSG.group_no + ',';
                                    } else {
                                        if (studentGroupElement.session_group.length - 1 != indexSG)
                                            sessionName += 'G' + eleSG.group_no + ',';
                                        else sessionName += 'G' + eleSG.group_no;
                                    }
                                    i++;
                                });
                            },
                        );
                    });
                }
                sessionList.push({
                    session: sessionName,
                    schedule_id: scheduleElement._id,
                    schedule_status: scheduleElement.status,
                    isActive: scheduleElement.isActive,
                    schedule_date: scheduleElement.schedule_date,
                    mode: [
                        {
                            _id: modeByIds.primary,
                            modeBy: 'primary',
                            startTime:
                                scheduleElement.sessionDetail &&
                                scheduleElement.sessionDetail.start_time
                                    ? scheduleElement.sessionDetail.start_time
                                    : '',
                        },
                        ...multiAttendance.map((multiAttendanceElement) => {
                            return {
                                _id: multiAttendanceElement._id,
                                modeBy: multiAttendanceElement.modeBy,
                                startTime: multiAttendanceElement.createdAt,
                            };
                        }),
                        {
                            _id: modeByIds.final,
                            modeBy: 'final',
                        },
                    ],
                });
            });
            console.timeEnd('groupElement');
            return res.status(200).send(
                responseFunctionWithRequest(req, 200, true, req.t('ATTENDANCE_LOG'), {
                    sessions: sessionList,
                    students: Array.from(studentMap.values()),
                }),
            );
        }
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('ERROR_CATCH'),
                    error.toString(),
                ),
            );
    }
};
