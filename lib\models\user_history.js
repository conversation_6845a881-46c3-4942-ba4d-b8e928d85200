let mongoose = require('mongoose');
let Schemas = mongoose.Schema;
let constant = require('../utility/constants');

let userSchemas = new Schemas({
    _user_id: {
        type: Schemas.Types.ObjectId,
        ref: constant.USER,
        required: true,
        sparse: true
    },
    user_type: {
        type: String,
        required: true,
        enum: ['student', 'staff', 'administration']
    },
    section: {
        type: String,
        required: true
    },
    title: {
        type: String,
        required: true
    },
    before_update: {
        type: String,
        required: true
    },
    after_update: {
        type: String,
        required: true
    },
    _editor_id: {
        type: Schemas.Types.ObjectId,
        ref: constant.USER,
        required: true
    },
    reason: {
        type: String,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isDeleted: {
        type: Boolean,
        default: false
    }
},
    { timestamps: true });
module.exports = mongoose.model(constant.USER_HISTORY, userSchemas);