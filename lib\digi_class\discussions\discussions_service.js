const { convertToMongoObjectId } = require('../../utility/common');
const {
    STUDENT_GROUP,
    DIGI_SESSION_DELIVERY_TYPES,
    GENDER: { BOTH, MALE },
    STUDENT_GROUP_MODE: { FYD, COURSES, ROTATION },
} = require('../../../commonService/utility/constants');
const studentGroupSchema = require('mongoose').model(STUDENT_GROUP);
const sessionDeliveryTypeSchema = require('mongoose').model(DIGI_SESSION_DELIVERY_TYPES);
const { discussionTopicsSchema } = require('./discussions_topics_model');

const replaceSpaces = (name, hyphen = false) => {
    if (name === '' || name === undefined) return '';
    return name.replace(/ /g, hyphen ? '' : '_').toLowerCase();
};

const getAllCourseGroup = ({
    master_group,
    _course_id,
    _institution_calendar_id,
    yearNo,
    levelNo,
    term,
    rotationName,
    _user_id,
}) => {
    const studentGroupData = master_group;
    const uniqueGroupNames = new Set();
    const uniqueStudents = new Set();
    studentGroupData.forEach((groupElement) => {
        uniqueGroupNames.add(groupElement.group_name);
        if (groupElement.session_group) {
            groupElement.session_group.forEach((sessionGroupElement) => {
                if (sessionGroupElement.students) {
                    sessionGroupElement.students.forEach((studentElement) => {
                        uniqueStudents.add(JSON.stringify(studentElement));
                    });
                }
            });
        }
    });
    const groupNamesArray = Array.from(uniqueGroupNames);
    const studentsArray = Array.from(uniqueStudents).map((student) => JSON.parse(student));
    const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
        levelNo,
        true,
    )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(term)}${rotationName}`;
    switch (_user_id) {
        case null: {
            return [
                {
                    channelId,
                    groupName: groupNamesArray.join(', '),
                    students: studentsArray,
                },
            ];
        }
        default: {
            const findUser = studentsArray.some(
                (studentElement) => studentElement._student_id.toString() === _user_id.toString(),
            );
            return findUser
                ? [
                      {
                          channelId,
                          groupName: groupNamesArray.join(', '),
                      },
                  ]
                : [];
        }
    }
};

const getIndividualCourseGroup = ({
    master_group,
    _course_id,
    _institution_calendar_id,
    yearNo,
    levelNo,
    term,
    rotationName,
    _user_id,
}) => {
    const studentGroupData = master_group;
    const groupedData = {};
    studentGroupData.forEach((studentGroupElement) => {
        const groupKey = studentGroupElement.group_name;
        if (!groupedData[groupKey]) {
            groupedData[groupKey] = {
                groupName: studentGroupElement.group_name,
                students: new Set(),
            };
        }
        if (studentGroupElement.session_group) {
            studentGroupElement.session_group.forEach((sessionGroupElement) => {
                if (sessionGroupElement.students) {
                    sessionGroupElement.students.forEach((studentElement) => {
                        groupedData[groupKey].students.add(JSON.stringify(studentElement));
                    });
                }
            });
        }
    });
    switch (_user_id) {
        case null: {
            const resultArray = Object.values(groupedData).map((groupElement) => {
                const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
                    levelNo,
                    true,
                )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(
                    term,
                )}${rotationName}_${replaceSpaces(groupElement.groupName)}`;
                return {
                    channelId,
                    groupName: groupElement.groupName,
                    students: Array.from(groupElement.students).map((student) =>
                        JSON.parse(student),
                    ),
                };
            });
            return resultArray;
        }
        default: {
            const resultArray = Object.values(groupedData).map((groupElement) => {
                const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
                    levelNo,
                    true,
                )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(
                    term,
                )}${rotationName}_${replaceSpaces(groupElement.groupName)}`;
                const studentsArray = Array.from(groupElement.students).map((student) =>
                    JSON.parse(student),
                );
                const findUser = studentsArray.some(
                    (studentElement) =>
                        studentElement._student_id.toString() === _user_id.toString(),
                );
                return findUser
                    ? {
                          channelId,
                          groupName: groupElement.groupName,
                      }
                    : {};
            });
            return resultArray;
        }
    }
};

const getAllDeliveryGroup = ({
    master_group,
    _course_id,
    _institution_calendar_id,
    yearNo,
    levelNo,
    term,
    rotationName,
    _user_id,
    mode,
}) => {
    const studentGroupData = master_group;
    const groupedData = {};
    switch (_user_id) {
        case null: {
            studentGroupData.map((studentGroupElement) => {
                const deliveryKey = studentGroupElement.delivery_type;
                if (!groupedData[deliveryKey]) {
                    groupedData[deliveryKey] = {
                        delivery_type: studentGroupElement.delivery_type,
                        groups: [],
                    };
                }
                const sessionSubGroups = [];
                if (studentGroupElement.session_group) {
                    studentGroupElement.session_group.forEach((sessionGroupElement) => {
                        if (sessionGroupElement.groups) {
                            sessionGroupElement.groups.forEach((groupElement) => {
                                const extractGroupName = groupElement.group_name.split('-');
                                const splitElement = mode === 'foundation' ? 3 : 2;
                                const groupName = extractGroupName
                                    .slice(
                                        extractGroupName.length - splitElement,
                                        extractGroupName.length,
                                    )
                                    .join('-');
                                const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
                                    levelNo,
                                    true,
                                )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(
                                    term,
                                )}${rotationName}_${replaceSpaces(
                                    studentGroupElement.group_name,
                                )}_${replaceSpaces(
                                    studentGroupElement.delivery_type,
                                )}_${replaceSpaces(groupName)}`;
                                sessionSubGroups.push({
                                    channelId,
                                    groupName: `${studentGroupElement.group_name} - ${groupName}`,
                                    students: groupElement.students,
                                });
                            });
                        } else {
                            const extractGroupName = sessionGroupElement.group_name.split('-');
                            const groupName = extractGroupName
                                .slice(extractGroupName.length - 2, extractGroupName.length)
                                .join('-');
                            const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
                                levelNo,
                                true,
                            )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(
                                term,
                            )}${rotationName}_${replaceSpaces(
                                studentGroupElement.group_name,
                            )}_${replaceSpaces(studentGroupElement.delivery_type)}_${replaceSpaces(
                                groupName,
                            )}`;
                            sessionSubGroups.push({
                                channelId,
                                groupName: `${studentGroupElement.group_name} - ${groupName}`,
                                students: sessionGroupElement.students,
                            });
                        }
                    });
                }
                groupedData[deliveryKey].groups.push(...sessionSubGroups);
            });

            return Object.values(groupedData)
                .filter((deliveryElement) => deliveryElement.delivery_type !== undefined)
                .map((deliveryElement) => ({
                    delivery_type: deliveryElement.delivery_type,
                    delivery_symbol: deliveryElement.delivery_symbol,
                    delivery_name: deliveryElement.delivery_name,
                    groups: deliveryElement.groups,
                }));
        }
        default: {
            studentGroupData.map((studentGroupElement) => {
                const deliveryKey = studentGroupElement.delivery_type;
                if (!groupedData[deliveryKey]) {
                    groupedData[deliveryKey] = {
                        delivery_type: studentGroupElement.delivery_type,
                        groups: [],
                    };
                }
                const groupData = [];
                if (studentGroupElement.session_group) {
                    studentGroupElement.session_group.forEach((sessionGroupElement) => {
                        if (sessionGroupElement.groups) {
                            sessionGroupElement.groups.forEach((groupElement) => {
                                const extractGroupName = groupElement.group_name.split('-');
                                const splitElement = mode === 'foundation' ? 3 : 2;
                                const groupName = extractGroupName
                                    .slice(
                                        extractGroupName.length - splitElement,
                                        extractGroupName.length,
                                    )
                                    .join('-');
                                const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
                                    levelNo,
                                    true,
                                )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(
                                    term,
                                )}${rotationName}_${replaceSpaces(
                                    studentGroupElement.group_name,
                                )}_${replaceSpaces(
                                    studentGroupElement.delivery_type,
                                )}_${replaceSpaces(groupName)}`;

                                const findUser = groupElement.students.some(
                                    (studentElement) =>
                                        studentElement._student_id.toString() ===
                                        _user_id.toString(),
                                );
                                groupData.push(findUser ? { channelId } : {});
                            });
                        } else {
                            const extractGroupName = sessionGroupElement.group_name.split('-');
                            const groupName = extractGroupName
                                .slice(extractGroupName.length - 2, extractGroupName.length)
                                .join('-');
                            const channelId = `${replaceSpaces(yearNo)}_${replaceSpaces(
                                levelNo,
                                true,
                            )}_${_course_id}_${_institution_calendar_id}_${replaceSpaces(
                                term,
                            )}${rotationName}_${replaceSpaces(
                                studentGroupElement.group_name,
                            )}_${replaceSpaces(studentGroupElement.delivery_type)}_${replaceSpaces(
                                groupName,
                            )}`;
                            const findUser = sessionGroupElement.students.some(
                                (studentElement) =>
                                    studentElement._student_id.toString() === _user_id.toString(),
                            );
                            groupData.push(findUser ? { channelId } : {});
                        }
                    });
                }
                groupedData[deliveryKey].groups.push(...groupData);
            });
            const groupDelivery = Object.values(groupedData)
                .filter((deliveryElement) => deliveryElement.delivery_type !== undefined)
                .map((deliveryElement) => ({
                    delivery_type: deliveryElement.delivery_type,
                    groups: deliveryElement.groups,
                }));

            return groupDelivery;
        }
    }
};

const getDeliveryTypes = (sessionDeliveryType) => {
    const delivery = [];
    for (const sessionDeliveryElement of sessionDeliveryType) {
        for (const deliveryTypeElement of sessionDeliveryElement.delivery_types) {
            delivery.push(deliveryTypeElement);
        }
    }
    return delivery;
};

const extractChannelIds = (data) => {
    const channelIds = [];
    data.forEach((groupElement) => {
        groupElement.groups.forEach((subGroupElement) => {
            if (subGroupElement.hasOwnProperty('channelId')) {
                channelIds.push(subGroupElement.channelId);
            } else if (subGroupElement.hasOwnProperty('groups')) {
                subGroupElement.groups.forEach((innerGroupElement) => {
                    if (innerGroupElement.hasOwnProperty('channelId')) {
                        channelIds.push(innerGroupElement.channelId);
                    }
                });
            }
        });
    });
    return channelIds;
};

const getStudentGroupListsFormatting = async ({
    studentGroupData,
    sessionDeliveryType,
    _course_id,
    _institution_calendar_id,
    yearNo,
    levelNo,
    term,
    rotation,
    rotationCount,
    _user_id,
    group_name,
}) => {
    try {
        let sgStudentList = [];
        let master_group = [];
        if (!studentGroupData) return [];
        const filteredByTermLevel = studentGroupData.groups.filter(
            (groupElement) => groupElement.term === term && groupElement.level === levelNo,
        );
        for (groupElement of filteredByTermLevel) {
            const filteredCourse = groupElement.courses.find(
                (courseIdElement) =>
                    courseIdElement._course_id.toString() === _course_id.toString(),
            );
            if (filteredCourse) {
                sgStudentList = groupElement.students
                    .filter((studentElement) => {
                        return studentElement.course_group_status.some(
                            (courseGroupElement) =>
                                _course_id.toString() === courseGroupElement._course_id.toString(),
                            // &&
                            // courseGroupElement.status === 'published',
                        );
                    })
                    .map(({ _student_id, academic_no, name, gender }) => ({
                        _student_id,
                        academic_no,
                        name,
                        gender,
                    }));

                switch (groupElement.group_mode.toString()) {
                    case FYD.toString():
                        {
                            const deliveryTypes = getDeliveryTypes(sessionDeliveryType);
                            let foundation_master_group = [];
                            for (foundationGroupSettingElement of groupElement.group_setting) {
                                for (const foundationGroup of foundationGroupSettingElement.groups) {
                                    const extractGroupName = foundationGroup.group_name.split('-');
                                    const groupName = extractGroupName
                                        .slice(extractGroupName.length - 2, extractGroupName.length)
                                        .join('-');
                                    const filteredGroupElementSetting =
                                        filteredCourse.setting.filter(
                                            (groupSettingElement) =>
                                                foundationGroupSettingElement.gender ===
                                                groupSettingElement.gender,
                                        );

                                    const groupDelivery = [];
                                    for (const settingElement of filteredGroupElementSetting) {
                                        for (setting_element of settingElement.session_setting) {
                                            groupDelivery.push({
                                                ...setting_element,
                                                gender: settingElement.gender,
                                                group_no: settingElement._group_no,
                                            });
                                        }
                                    }
                                    const groupedData = groupDelivery.reduce(
                                        (acc, deliveryElement) => {
                                            const {
                                                delivery_type,
                                                session_type,
                                                _id,
                                                group_name,
                                                groups,
                                                gender,
                                                group_no,
                                            } = deliveryElement;

                                            let deliveryType = acc.find(
                                                (deliveryElement) =>
                                                    deliveryElement.delivery_type === delivery_type,
                                            );

                                            if (!deliveryType) {
                                                deliveryType = {
                                                    group_name: groupName,
                                                    delivery_type,
                                                    gender,
                                                    group_no,
                                                    session_group: [],
                                                };
                                                acc.push(deliveryType);
                                            }

                                            let sessionGroup = deliveryType.session_group.find(
                                                (session) => session.session_type === session_type,
                                            );

                                            if (!sessionGroup) {
                                                const findDeliveryType = deliveryTypes.find(
                                                    (deliveryElement) =>
                                                        deliveryElement.delivery_symbol ===
                                                        session_type,
                                                );
                                                sessionGroup = {
                                                    _id,
                                                    group_name,
                                                    delivery_symbol: session_type,
                                                    delivery_name:
                                                        findDeliveryType.delivery_name !== undefined
                                                            ? findDeliveryType.delivery_name
                                                            : '',
                                                    groups: groups || [],
                                                };
                                                deliveryType.session_group.push(sessionGroup);
                                            } else {
                                                sessionGroup.groups.push(groups);
                                            }

                                            return acc;
                                        },
                                        [],
                                    );
                                    foundation_master_group = [
                                        ...foundation_master_group,
                                        ...groupedData,
                                    ];
                                }
                            }
                            master_group = [...foundation_master_group];
                        }
                        break;
                    case ROTATION.toString(): {
                        const deliveryTypes = getDeliveryTypes(sessionDeliveryType);
                        const filteredRotation = groupElement.rotation_group_setting.filter(
                            (groupSettingElement) =>
                                groupSettingElement.group_no.toString() ===
                                rotationCount.toString(),
                        );
                        let rotation_master_group = [];
                        for (rotationGroupSettingElement of filteredRotation) {
                            const extractGroupName =
                                rotationGroupSettingElement.group_name.split('-');
                            const groupName = extractGroupName
                                .slice(extractGroupName.length - 2, extractGroupName.length)
                                .join('-');
                            const filteredGroupElementSetting = filteredCourse.setting.filter(
                                (groupSettingElement) =>
                                    groupSettingElement._group_no.toString() ===
                                        rotationCount.toString() &&
                                    rotationGroupSettingElement.gender ===
                                        groupSettingElement.gender,
                            );
                            const groupDelivery = [];
                            for (const settingElement of filteredGroupElementSetting) {
                                for (setting_element of settingElement.session_setting) {
                                    groupDelivery.push({
                                        ...setting_element,
                                        gender: settingElement.gender,
                                        group_no: settingElement._group_no,
                                    });
                                }
                            }
                            const groupedData = groupDelivery.reduce((acc, deliveryElement) => {
                                const {
                                    delivery_type,
                                    session_type,
                                    _id,
                                    group_name,
                                    groups,
                                    gender,
                                    group_no,
                                } = deliveryElement;

                                let deliveryType = acc.find(
                                    (deliveryElement) =>
                                        deliveryElement.delivery_type === delivery_type,
                                );

                                if (!deliveryType) {
                                    deliveryType = {
                                        group_name: groupName,
                                        delivery_type,
                                        gender,
                                        group_no,
                                        session_group: [],
                                    };
                                    acc.push(deliveryType);
                                }

                                let sessionGroup = deliveryType.session_group.find(
                                    (session) => session.session_type === session_type,
                                );

                                if (!sessionGroup) {
                                    const findDeliveryType = deliveryTypes.find(
                                        (deliveryElement) =>
                                            deliveryElement.delivery_symbol === session_type,
                                    );
                                    sessionGroup = {
                                        _id,
                                        group_name,
                                        delivery_symbol: session_type,
                                        delivery_name:
                                            findDeliveryType.delivery_name !== undefined
                                                ? findDeliveryType.delivery_name
                                                : '',
                                        groups: groups || [],
                                    };
                                    deliveryType.session_group.push(sessionGroup);
                                } else {
                                    sessionGroup.groups.push(groups);
                                }

                                return acc;
                            }, []);
                            rotation_master_group = [...rotation_master_group, ...groupedData];
                        }
                        master_group = [...rotation_master_group];
                        break;
                    }
                    case COURSES.toString(): {
                        for (const settingElement of filteredCourse.setting) {
                            for (setting_element of settingElement.session_setting) {
                                if (setting_element.delivery_type) {
                                    let delivery;
                                    for (const sessionDeliveryElement of sessionDeliveryType) {
                                        for (const deliveryTypeElement of sessionDeliveryElement.delivery_types) {
                                            if (
                                                deliveryTypeElement.delivery_symbol ===
                                                setting_element.session_type
                                            )
                                                delivery = deliveryTypeElement;
                                        }
                                    }
                                    const group_name =
                                        settingElement.gender === BOTH
                                            ? 'SG-1'
                                            : settingElement.gender === MALE
                                            ? 'MG-1'
                                            : 'FG-1';
                                    master_group.push({
                                        group_no: 1,
                                        group_name,
                                        delivery_type: setting_element.delivery_type,
                                        delivery_symbol: setting_element.session_type,
                                        delivery_name: delivery ? delivery.delivery_name : null,
                                        session_group: (
                                            settingElement.gender === BOTH
                                                ? sgStudentList.length
                                                : sgStudentList.filter(
                                                      (studentElement) =>
                                                          studentElement.gender ===
                                                          settingElement.gender,
                                                  ).length !== 0
                                        )
                                            ? setting_element.groups.map((groupElement) => {
                                                  return {
                                                      group_name: groupElement.group_name,
                                                      _student_ids: groupElement._student_ids,
                                                  };
                                              })
                                            : [],
                                    });
                                }
                            }
                        }
                        break;
                    }
                    default:
                        break;
                }
            }
        }

        master_group.forEach((masterGroupElement, index) => {
            if (masterGroupElement && masterGroupElement.session_group)
                masterGroupElement.session_group.forEach((sess_group_ele, indSessG) => {
                    const studentArr = [];
                    if (sess_group_ele._student_ids) {
                        sess_group_ele._student_ids.forEach((student_id) => {
                            const userInd = sgStudentList.findIndex(
                                (studentElement) =>
                                    studentElement._student_id.toString() === student_id.toString(),
                            );
                            if (userInd != -1) studentArr.push(sgStudentList[userInd]);
                        });
                    } else {
                        sess_group_ele.groups.forEach((groupElement, groupIndex) => {
                            const groupStudentArr = [];
                            groupElement._student_ids.forEach((student_id) => {
                                const userInd = sgStudentList.findIndex(
                                    (studentElement) =>
                                        studentElement._student_id.toString() ===
                                        student_id.toString(),
                                );
                                if (userInd != -1) {
                                    studentArr.push(sgStudentList[userInd]);
                                    groupStudentArr.push(sgStudentList[userInd]);
                                }
                            });
                            master_group[index].session_group[indSessG].groups[
                                groupIndex
                            ].students = groupStudentArr;
                        });
                    }
                    master_group[index].session_group[indSessG].students = studentArr;
                });
        });
        const mode = groupElement.group_mode.toString();
        const rotationName = `${rotation === 'yes' ? `_r${rotationCount}` : ``}`;
        let formatGroups = [];
        if (group_name === null) {
            const allCourseGroup =
                master_group.length > 0
                    ? getAllCourseGroup({
                          master_group,
                          _course_id,
                          _institution_calendar_id,
                          yearNo,
                          levelNo,
                          term,
                          rotationName,
                          _user_id,
                      })
                    : [];
            const individualCourseGroup =
                master_group.length > 0
                    ? getIndividualCourseGroup({
                          master_group,
                          _course_id,
                          _institution_calendar_id,
                          yearNo,
                          levelNo,
                          term,
                          rotationName,
                          _user_id,
                      })
                    : [];
            const checkBothGroupSame =
                allCourseGroup[0] &&
                individualCourseGroup[0] &&
                allCourseGroup[0].groupName === individualCourseGroup[0].groupName;
            const deliveryCourseGroup =
                master_group.length > 0
                    ? getAllDeliveryGroup({
                          master_group,
                          _course_id,
                          _institution_calendar_id,
                          yearNo,
                          levelNo,
                          term,
                          rotationName,
                          _user_id,
                          mode,
                      })
                    : [];
            formatGroups = [
                {
                    groupName: 'ALL COURSE GROUP',
                    groups: allCourseGroup,
                },
                {
                    groupName: 'DELIVERY TYPE',
                    groups: deliveryCourseGroup,
                },
            ];
            if (!checkBothGroupSame) {
                formatGroups.splice(1, 0, {
                    groupName: 'COURSE GROUP',
                    groups: individualCourseGroup,
                });
            }
        } else {
            formatGroups = [
                {
                    groupName: group_name,
                    groups:
                        group_name === 'ALL COURSE GROUP'
                            ? master_group.length > 0
                                ? getAllCourseGroup({
                                      master_group,
                                      _course_id,
                                      _institution_calendar_id,
                                      yearNo,
                                      levelNo,
                                      term,
                                      rotationName,
                                      _user_id,
                                  })
                                : []
                            : group_name === 'COURSE GROUP'
                            ? master_group.length > 0
                                ? getIndividualCourseGroup({
                                      master_group,
                                      _course_id,
                                      _institution_calendar_id,
                                      yearNo,
                                      levelNo,
                                      term,
                                      rotationName,
                                      _user_id,
                                  })
                                : []
                            : group_name === 'DELIVERY TYPE'
                            ? master_group.length > 0
                                ? getAllDeliveryGroup({
                                      master_group,
                                      _course_id,
                                      _institution_calendar_id,
                                      yearNo,
                                      levelNo,
                                      term,
                                      rotationName,
                                      _user_id,
                                      mode,
                                  })
                                : []
                            : [],
                },
            ];
        }
        let topicCounts = [];
        if (_user_id === null && group_name === null) {
            const channelIdArray = extractChannelIds(formatGroups);
            if (channelIdArray) {
                const discussionTopicsCount = await discussionTopicsSchema.aggregate([
                    {
                        $match: {
                            isDeleted: false,
                            channel_id: {
                                $in: channelIdArray,
                            },
                        },
                    },
                    {
                        $group: {
                            _id: '$channel_id',
                            count: { $sum: 1 },
                        },
                    },
                ]);
                topicCounts = discussionTopicsCount;
            }
        }
        return { studentGroups: formatGroups, topicCounts };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getStudentGroupLists = async ({
    _institution_id,
    _institution_calendar_id,
    _program_id,
    _course_id,
    yearNo,
    levelNo,
    term,
    rotation,
    rotationCount,
    _user_id,
    group_name,
}) => {
    try {
        // Fetch session delivery types
        const sessionDeliveryType = await sessionDeliveryTypeSchema
            .find(
                {
                    isDeleted: false,
                    _program_id: convertToMongoObjectId(_program_id),
                },
                { delivery_types: 1 },
            )
            .lean();

        // Fetch student group data
        const studentGroupData = await studentGroupSchema
            .findOne({
                isDeleted: false,
                'master._program_id': convertToMongoObjectId(_program_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                _institution_calendar_id: convertToMongoObjectId(_institution_calendar_id),
                'groups.courses._course_id': convertToMongoObjectId(_course_id),
            })
            .lean();

        // Process and format the data
        const formattedData = getStudentGroupListsFormatting({
            studentGroupData,
            sessionDeliveryType,
            _course_id,
            _institution_calendar_id,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            _user_id,
            group_name,
        });
        return formattedData;
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getStudentGroupLists,
};
