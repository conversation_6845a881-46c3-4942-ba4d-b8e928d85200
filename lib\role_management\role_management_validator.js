// const middleware = exports;
const Joi = require('joi');
const common_files = require('../utility/common');
const constant = require('../utility/constants');

exports.program = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _calendar_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                level_no: Joi.number()
                    .min(1)
                    .max(20)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                rotation_count: Joi.number()
                    .min(0)
                    .max(9)
                    .error((error) => {
                        return error;
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                start_date: Joi.date()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                end_date: Joi.date()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                color_code: Joi.string()
                    .required()
                    .error((error) => {
                        return error;
                    }),
                _event_id: Joi.array()
                    .items(Joi.string().alphanum().length(24))
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_update = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _calendar_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                level_no: Joi.number()
                    .min(1)
                    .max(20)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                start_date: Joi.date().error((error) => {
                    return error;
                }),
                end_date: Joi.date().error((error) => {
                    return error;
                }),
                color_code: Joi.string().error((error) => {
                    return error;
                }),
                _event_id: Joi.array()
                    .items(Joi.string().alphanum().length(24))
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_delete = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _calendar_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                level_no: Joi.number()
                    .min(1)
                    .max(20)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                rotation_no: Joi.number()
                    .min(1)
                    .max(20)
                    .error((error) => {
                        return error;
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_delete_event = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            body: Joi.object().keys({
                _calendar_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                level_no: Joi.number()
                    .min(1)
                    .max(20)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                _course_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                rotation_no: Joi.number()
                    .min(1)
                    .max(20)
                    .error((error) => {
                        return error;
                    }),
                _event_id: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.program_id = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object()
                .keys({
                    id: Joi.string()
                        .alphanum()
                        .length(24)
                        .required()
                        .error((error) => {
                            return error;
                        }),
                })
                .unknown(true),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.rotation_course_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                rotation_no: Joi.number()
                    .min(1)
                    .max(9)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                course: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_list_year_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                year_no: Joi.number()
                    .min(1)
                    .max(9)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_list_level_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                level_no: Joi.number()
                    .min(1)
                    .max(25)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};

exports.course_get = (req, res, next) => {
    const schema = Joi.object()
        .keys({
            params: Joi.object().keys({
                calendar: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                term: Joi.string()
                    .valid(constant.BATCH.REGULAR, constant.BATCH.INTERIM)
                    .required()
                    .error((error) => {
                        return error;
                    }),
                course: Joi.string()
                    .alphanum()
                    .length(24)
                    .required()
                    .error((error) => {
                        return error;
                    }),
            }),
        })
        .unknown(true);
    const { error } = schema.validate(req, { abortEarly: false });
    if (error) {
        const errorMessage = error.details.map((detail) => detail.message).join(', ');
        return common_files.com_response(res, 422, false, req.t('VALIDATION_ERROR'), errorMessage);
    }
    next();
};
