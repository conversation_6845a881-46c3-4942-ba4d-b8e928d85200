const {
    model,
    Schema,
    Types: { ObjectId },
} = require('mongoose');
const {
    INSTITUTION,
    INSTITUTION_CALENDAR,
    USER,
    DIGI_COURSE,
    DIGI_PROGRAM,
    COURSE_SCHEDULE,
    SESSION_RATING,
} = require('../utility/constants');

const sessionRatings = new Schema(
    {
        institutionId: {
            type: ObjectId,
            ref: INSTITUTION,
            required: true,
        },
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
            required: true,
        },
        programId: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
            required: true,
        },
        courseId: {
            type: ObjectId,
            ref: DIGI_COURSE,
            required: true,
        },
        year: {
            type: String,
            required: true,
        },
        level: {
            type: String,
            required: true,
        },
        term: {
            type: String,
            required: true,
        },
        rotationCount: {
            type: Number,
            default: 0,
        },
        scheduleId: {
            type: ObjectId,
            ref: COURSE_SCHEDULE,
            required: true,
        },
        feedBack: {
            type: String,
            trim: true,
            maxlength: 500,
        },
        rating: {
            type: Number,
            min: 1,
            max: 5,
            required: true,
        },
        isDeleted: {
            type: Boolean,
            default: false,
        },
        studentId: {
            type: ObjectId,
            ref: USER,
            required: true,
        },
        staffId: [
            {
                type: ObjectId,
                ref: USER,
                required: true,
            },
        ],
    },
    { timestamps: true },
);

module.exports = model(SESSION_RATING, sessionRatings);
