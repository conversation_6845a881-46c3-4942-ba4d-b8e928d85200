const axios = require('axios');
const keys = require('../utility/util_keys');

/**
 *
 * @param {Its is the redirection url eg. https://google.com} frontEndUrl
 * @param {Its the text that need to passed to Deeplink url as param eg. <deeplink-url>?encodedText} encodedText
 * @returns DeepLinkUrl
 *
 * on failure it returns empty string
 */

const makeDeepLinkUrl = async (frontEndUrl, encodedText) => {
    try {
        const param = {
            dynamicLinkInfo: {
                domainUriPrefix: keys.DEEP_LINK_BASE_URL,
                link: frontEndUrl,
                androidInfo: {
                    androidPackageName: keys.DEEP_LINK_DC_ANDROID_BUNDLE_ID,
                },
                iosInfo: {
                    iosBundleId: keys.DEEP_LINK_DC_IOS_BUNDLE_ID,
                    iosIpadBundleId: keys.DEEP_LINK_DC_IOS_BUNDLE_ID,
                    iosAppStoreId: keys.DEEP_LINK_DC_APP_STORE_ID,
                },
                socialMetaTagInfo: {
                    socialTitle: keys.DEEP_LINK_SOCIAL_TITLE,
                    socialDescription: keys.DEEP_LINK_SOCIAL_DESCRIPTION,
                    socialImageLink: keys.DEEP_LINK_SOCIAL_IMAGE,
                },
            },
            suffix: {
                option: 'UNGUESSABLE',
            },
        };
        const response = await axios.post(keys.DEEP_LINK_FIREBASE_URL, param, {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        return `${response.data.shortLink}?${encodedText}`;
    } catch (error) {
        return '';
    }
};

module.exports = { makeDeepLinkUrl };
