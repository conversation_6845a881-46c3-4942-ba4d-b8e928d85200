const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const constant = require('../../../utility/constants');

const assignmentPromptSchema = new Schema(
    {
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isReset: {
            type: Boolean,
            default: false,
        },
        isDuplicate: {
            type: Boolean,
            default: false,
        },
        _institution_id: {
            type: Schema.Types.ObjectId,
            ref: constant.INSTITUTION,
        },
        subjectId: {
            type: Schema.Types.ObjectId,
        },
        assignmentId: {
            type: Schema.Types.ObjectId,
        },
        rubricsId: { type: Schema.Types.ObjectId, ref: constant.ASSIGNMENT_RUBRICS },
        learningOutcome: {
            type: { type: String, default: '' },
            outcomeId: [{ type: Schema.Types.ObjectId, default: [] }],
        },
        parentPromptId: {
            type: Schema.Types.ObjectId,
        },
        category: {
            type: String,
        },
        childPromptIds: [
            {
                type: Schema.Types.ObjectId,
            },
        ],
        showTaxonomy: {
            type: Boolean,
            default: false,
        },
        taxonomyIds: [{ type: Schema.Types.ObjectId, ref: constant.TAXONOMY }],
        score: {
            value: {
                type: Number,
            },
            unit: {
                type: String,
            },
            type: {
                type: String,
            },
            mandatoryToAttemptAtleast: {
                type: Number,
                default: 0,
            },
            questionText: {
                type: String,
                trim: true,
            },
            isMandatory: {
                type: Boolean,
                default: false,
            },
            instructionAttachment: {
                value: {
                    type: String,
                },
                attachments: [
                    {
                        url: String,
                        signedUrl: String,
                        sizeInKb: Number,
                        name: String,
                    },
                ],
            },
            hint: {
                value: {
                    type: String,
                },
                attachments: [
                    {
                        url: String,
                        signedUrl: String,
                        sizeInKb: Number,
                        name: String,
                    },
                ],
            },
            choiceType: {
                isSingleChoice: {
                    type: Boolean,
                    default: false,
                },
                choices: [
                    {
                        isCorrect: {
                            type: Boolean,
                            default: false,
                        },
                        value: {
                            type: String,
                            trim: true,
                        },
                        attachments: [
                            {
                                url: String,
                                signedUrl: String,
                                sizeInKb: Number,
                                name: String,
                            },
                        ],
                    },
                ],
                randomizeChoice: {
                    type: Boolean,
                    default: false,
                },
            },
            singleAnswer: {
                maxCharLimit: {
                    type: Number,
                },
                answers: [
                    {
                        answerText: { type: String, trim: true },
                        attachments: [
                            {
                                url: String,
                                signedUrl: String,
                                sizeInKb: Number,
                                name: String,
                            },
                        ],
                    },
                ],
            },
            matchTheFollowing: {
                leftValues: [
                    {
                        value: {
                            type: String,
                            trim: true,
                        },
                        attachments: [
                            {
                                url: String,
                                signedUrl: String,
                                sizeInKb: Number,
                                name: String,
                            },
                        ],
                    },
                ],
                rightValues: [
                    {
                        value: {
                            type: String,
                            trim: true,
                        },
                        attachments: [
                            {
                                url: String,
                                signedUrl: String,
                                sizeInKb: Number,
                                name: String,
                            },
                        ],
                    },
                ],
                randomizeChoice: {
                    type: Boolean,
                    default: false,
                },
            },
            fillInTheBlanks: {
                isCaseSensitive: {
                    type: Boolean,
                    default: false,
                },
                showOptionsToSelect: {
                    type: Boolean,
                    default: false,
                },
                options: [[{ type: String, trim: true }]],
                answers: [[{ type: String, trim: true }]],
            },
            inCorrectScore: {
                isActive: {
                    type: Boolean,
                },
                value: {
                    type: Number,
                },
            },
        },
    },
    { timestamps: true },
);
module.exports = mongoose.model(constant.ASSIGNMENT_PROMPT, assignmentPromptSchema);
