const { ObjectStorageClient } = require('oci-objectstorage');
const { Region, SimpleAuthenticationDetailsProvider } = require('oci-common');
const {
    AWS_BUCKET_NAME,
    OCI_NAMESPACE,
    OCI_TENANCY,
    OCI_USER,
    OCI_FINGERPRINT,
    OCI_KEY,
    OCI_REGION,
    OCI_PAR_EXPIRE,
} = require('../lib/utility/util_keys');
const ociProvider = new SimpleAuthenticationDetailsProvider(
    OCI_TENANCY,
    OCI_USER,
    OCI_FINGERPRINT,
    Buffer.from(OCI_KEY, 'base64').toString('ascii'),
    null,
    Region.fromRegionId(OCI_REGION),
);
const ociClient = new ObjectStorageClient({
    authenticationDetailsProvider: ociProvider,
});

const getFilePathFromOciUrl = (url) => {
    const bucketNameWithObjectFolder = AWS_BUCKET_NAME;
    const index = url.indexOf(bucketNameWithObjectFolder) + (bucketNameWithObjectFolder.length + 1);
    return url.substring(index);
};

const makeOCIUrl = (accessUri) => {
    return `https://objectstorage.${OCI_REGION}.oraclecloud.com${accessUri}`;
};

const getOciSignedUrl = async (url) => {
    try {
        const urlObjectName = getFilePathFromOciUrl(url);
        const createPreauthenticatedRequestDetails = {
            name: urlObjectName,
            bucketListingAction: 'Deny',
            objectName: urlObjectName,
            accessType: 'ObjectRead',
            timeExpires: new Date(new Date().getTime() + parseInt(OCI_PAR_EXPIRE)),
        };
        const createPreauthenticatedRequestRequest = {
            namespaceName: OCI_NAMESPACE,
            bucketName: AWS_BUCKET_NAME,
            createPreauthenticatedRequestDetails,
        };
        const { preauthenticatedRequest } = await ociClient.createPreauthenticatedRequest(
            createPreauthenticatedRequestRequest,
        );
        const objectURL =
            preauthenticatedRequest && preauthenticatedRequest.accessUri
                ? makeOCIUrl(preauthenticatedRequest.accessUri)
                : url;
        return objectURL;
    } catch (error) {
        throw new Error(error);
    }
};

module.exports = { getOciSignedUrl };
