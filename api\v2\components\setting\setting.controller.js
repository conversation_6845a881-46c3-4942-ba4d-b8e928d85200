const settingSchema = require('./setting.model');
const programSchema = require('../program-input/program-input.model');
const curriculumSchema = require('../curriculum/curriculum.model');
const userSchema = require('../user-management/user.model');
const {
    DS_DATA_RETRIEVED,
    DS_UPDATED,
    DS_ADDED,
    DS_DELETED,
    DS_UPDATE_FAILED,
    DS_ADD_FAILED,
    SETTINGS,
    DS_TERM_KEY,
    DS_PROGRAM_KEY,
    PROGRAM,
    CURRICULUM,
    DS_GET_FAILED,
    SESSION_DELIVERY_TYPES,
    DC_STAFF,
    DC_STUDENT,
    USER,
    COMPLETED,
    VALID,
    DS_YEAR_KEY,
    DS_PHASE_KEY,
    DS_CREATE,
    DS_UPDATE,
    DS_SAVED,
    DS_NOT_FOUND,
} = require('../../utility/constants');
const bcrypt = require('bcrypt');
const { formatGlobalConfiguration, formatProgramInput } = require('./setting.format');
const {
    mockGlobalConfigurationData,
    mockProgramInputConfigurationData,
    getMockGenerateLabel,
    mockStudentLabelFieldConfigurationData,
    mockLabelFieldConfigurationData,
    privacySettings,
    mockMailConfigurationData,
} = require('./setting.util');
const { isIDEquals, convertToMongoObjectId, getModel } = require('../../utility/common');
const { ENGLISH } = require('../../utility/enums');
const { sendEmail, sendEmailCommon, sendStaffEmail } = require('../../services/email.service');
const sessionDeliverySchema = require('../session-delivery-types/session-delivery-types.model');

// Send Email if any document made as a mandatory
const DocumentMissingCheck = async (setting, document, tenantURL, type) => {
    const usersModel = getModel(tenantURL, USER, userSchema);
    const userProject = {
        _id: 1,
        email: 1,
        _institution_id: 1,
        status: 1,
        name: 1,
        uploadedDocuments: 1,
    };
    const userQuery = {
        isDeleted: false,
        isActive: true,
        $or: [{ status: VALID }, { status: COMPLETED }],
        user_type: type === DC_STAFF ? DC_STAFF : DC_STUDENT,
        _institution_id: convertToMongoObjectId(setting._institution_id),
    };
    const userLists = await usersModel.find(userQuery, userProject).lean();
    if (userLists) {
        const dbUpdateQuery = [];
        for (userData of userLists) {
            let categoryCheck = false;
            let documentCheck = false;

            categoryFound = userData.uploadedDocuments.find(
                (element) => element._category_id.toString() === document._category_id.toString(),
            );
            if (!categoryFound) {
                categoryCheck = true;
                const categoryUpdate = await usersModel.updateOne(
                    { _id: userData._id },
                    { $push: { uploadedDocuments: { _category_id: document._category_id } } },
                );
            }
            if (categoryFound || categoryCheck) {
                let documentFound = false;
                if (categoryFound) {
                    documentFound = categoryFound.document.find(
                        (element) =>
                            element._document_id &&
                            element._document_id.toString() === document._id.toString(),
                    );
                }

                if (!documentFound || categoryCheck) {
                    documentCheck = true;
                    const documentUpdate = await usersModel.findOneAndUpdate(
                        { _id: userData._id },
                        {
                            $push: {
                                'uploadedDocuments.$[categoryId].document': {
                                    _document_id: document._id,
                                    globalMandatory: document.isMandatory,
                                },
                            },
                        },
                        {
                            arrayFilters: [
                                {
                                    'categoryId._category_id': convertToMongoObjectId(
                                        document._category_id,
                                    ),
                                },
                            ],
                        },
                    );
                }
            }

            const documentLists = [];

            for (uploadedDocumentLists of userData.uploadedDocuments) {
                for (documents of uploadedDocumentLists.document) {
                    documentLists.push({
                        _document_id: documents._document_id,
                        url: documents.url,
                    });
                }
            }

            const checkingDocuments = documentLists.find(
                (element) =>
                    element._document_id &&
                    element._document_id.toString() === document._id.toString(),
            );
            if (!categoryCheck && !documentCheck) {
                dbUpdateQuery.push({
                    updateMany: {
                        filter: {
                            _id: convertToMongoObjectId(userData._id),
                            uploadedDocuments: { $ne: null },
                        },
                        update: {
                            $set: {
                                'uploadedDocuments.$[categoryId].document.$[documentId].globalMandatory':
                                    document.isMandatory,
                            },
                        },
                        arrayFilters: [
                            {
                                'categoryId._category_id': convertToMongoObjectId(
                                    document._category_id,
                                ),
                            },
                            {
                                'documentId._document_id': convertToMongoObjectId(document._id),
                            },
                        ],
                    },
                });
            }

            if (
                (!checkingDocuments ||
                    !checkingDocuments.url ||
                    !checkingDocuments.url.length ||
                    documentCheck) &&
                document.isMandatory
            ) {
                const subject = `Missing Documents to upload`;
                let mailBody =
                    '<h3>DigiScheduler</h3>;<p><b>Dear ' +
                    (userData.name.first.value ? userData.name.first.value : '') +
                    ' ' +
                    (userData.name.middle.value ? userData.name.middle.value : '') +
                    ' ' +
                    (userData.name.last.value ? userData.name.last.value : '') +
                    '</b>, <b>Below Document made as mandatory. please upload it.</b></p>;';
                mailBody += '<p>Document Name:<b>' + document.labelName + '</b></p>';
                const mailSendStatus = sendStaffEmail({
                    displayName:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.displayName,
                    emailId: setting.globalConfiguration.basicDetails.emailIdConfiguration.email,
                    userName:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.userName,
                    password:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.password,
                    smtpClient:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.smtpClient,
                    portNumber:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.portNumber,
                    toEmail: userData.email,
                    subject,
                    text: mailBody,
                });
            }
        }
        if (dbUpdateQuery.length > 0) {
            await usersModel.bulkWrite(dbUpdateQuery).catch((error) => {
                throw new Error(error);
            });
        }
    }
};

// Send Email if any label field made as a mandatory
const LabelFieldCheckEmailSent = async (setting, labelField, tenantURL) => {
    const usersModel = getModel(tenantURL, USER, userSchema);

    const userProject = {
        _id: 1,
        email: 1,
        _institution_id: 1,
        status: 1,
        name: 1,
        uploadedDocuments: 1,
        profileDetails: 1,
        gender: 1,
        mobile: 1,
        user_id: 1,
        user_type: 1,
        enrollmentYear: 1,
    };
    let dbUpdateQuery = {};
    const userQuery = {
        isDeleted: false,
        isActive: true,
        $or: [{ status: VALID }, { status: COMPLETED }],
        user_type: labelField.type,
        _institution_id: convertToMongoObjectId(setting._institution_id),
    };
    const userLists = await usersModel.find(userQuery, userProject).lean();
    let labelConfiguration;
    if (labelField.type === DC_STAFF) {
        labelConfiguration =
            setting.globalConfiguration.staffUserManagement.labelFieldConfiguration;
    } else {
        labelConfiguration =
            setting.globalConfiguration.studentUserManagement.labelFieldConfiguration;
    }

    labelConfiguration = labelConfiguration.find(
        (element) => element.language === labelField.language,
    );
    labelConfiguration = labelConfiguration[labelField.labelType].find(
        (element) => element._id.toString() === labelField.labelId.toString(),
    );
    const mappingKey = labelConfiguration.mappingKey;
    const userId = [];
    for (userData of userLists) {
        let checkingDocuments = false;
        if (labelField.labelType === 'basicDetails') {
            if (mappingKey === 'familyName' && !userData.name.family.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'name.family.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'middleName' && !userData.name.middle.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'name.middle.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'lastName' && !userData.name.last.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'name.last.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'firstName' && !userData.name.first.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'name.first.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'gender' && !userData.gender.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'gender.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'no' && !userData.mobile.code) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'mobile.globalMandatory': labelField.isMandatory,
                    },
                };
            }

            if (mappingKey === 'user_id' && !userData.user_id.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'user_id.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'email' && !userData.email) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'email.globalMandatory': labelField.isMandatory,
                    },
                };
            }
        }
        if (labelField.labelType === 'profileDetails') {
            if (mappingKey === 'passportNo' && !userData.profileDetails.passportNo.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.passportNo.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'dob' && !userData.profileDetails.dob.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.dob.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'nationalityId' && !userData.profileDetails.nationalityId.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.nationalityId.globalMandatory': labelField.isMandatory,
                    },
                };
            }
            if (
                mappingKey === '_nationality_id' &&
                !userData.profileDetails._nationality_id.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails._nationality_id.globalMandatory': labelField.isMandatory,
                    },
                };
            }
        }
        if (labelField.labelType === 'addressDetails') {
            if (
                mappingKey === 'buildingStreetName' &&
                !userData.profileDetails.addressDetails.buildingStreetName.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.addressDetails.buildingStreetName.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'floorNo' && !userData.profileDetails.addressDetails.floorNo.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.addressDetails.floorNo.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'country' && !userData.profileDetails.addressDetails.country.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.addressDetails.country.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (
                mappingKey === 'district' &&
                !userData.profileDetails.addressDetails.district.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.addressDetails.district.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'city' && !userData.profileDetails.addressDetails.city.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.addressDetails.city.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (mappingKey === 'zipCode' && !userData.profileDetails.addressDetails.zipCode.value) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.addressDetails.zipCode.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
        }
        if (labelField.labelType === 'otherContactDetails') {
            if (
                mappingKey === 'parentGuardianPhoneNo' &&
                !userData.profileDetails.otherContactDetails.parentGuardianPhoneNo.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.otherContactDetails.parentGuardianPhoneNo.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (
                mappingKey === 'parentGuardianEmailId' &&
                !userData.profileDetails.otherContactDetails.parentGuardianEmailId.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.otherContactDetails.parentGuardianEmailId.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (
                mappingKey === 'spouseGuardianPhoneNo' &&
                !userData.profileDetails.otherContactDetails.spouseGuardianPhoneNo.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.otherContactDetails.spouseGuardianPhoneNo.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
        }
        if (labelField.labelType === 'contactDetails') {
            if (
                mappingKey === 'officeExtension' &&
                !userData.profileDetails.office.officeExtension.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.office.officeExtension.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
            if (
                mappingKey === 'officeRoomNo' &&
                !userData.profileDetails.office.officeRoomNo.value
            ) {
                checkingDocuments = true;
                dbUpdateQuery = {
                    $set: {
                        'profileDetails.office.officeRoomNo.globalMandatory':
                            labelField.isMandatory,
                    },
                };
            }
        }

        if (mappingKey === 'enrollment_year' && !userData.enrollmentYear.value) {
            checkingDocuments = true;
            dbUpdateQuery = {
                $set: {
                    'enrollmentYear.globalMandatory': labelField.isMandatory,
                },
            };
        }
        if (checkingDocuments) {
            userId.push(userData._id);
            if (labelField.isMandatory) {
                const subject = `Label Field Updation`;
                let mailBody =
                    '<h3>DigiScheduler</h3>;<p><b>Dear ' +
                    (userData.name.first.value ? userData.name.first.value : '') +
                    ' ' +
                    (userData.name.middle.value ? userData.name.middle.value : '') +
                    ' ' +
                    (userData.name.last.value ? userData.name.last.value : '') +
                    '</b>, <b>Below Label field made as mandatory. please update it.</b></p>;';
                mailBody +=
                    '<p>LabelName: <b>' +
                    (labelConfiguration.translatedInput
                        ? labelConfiguration.translatedInput
                        : labelConfiguration.name) +
                    '</b></p>';
                const mailSendStatus = sendStaffEmail({
                    displayName:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.displayName,
                    emailId: setting.globalConfiguration.basicDetails.emailIdConfiguration.email,
                    userName:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.userName,
                    password:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.password,
                    smtpClient:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.smtpClient,
                    portNumber:
                        setting.globalConfiguration.basicDetails.emailIdConfiguration.portNumber,
                    toEmail: userData.email,
                    subject,
                    text: mailBody,
                });
            }
        }
    }
    await usersModel.updateMany({ _id: { $in: userId } }, dbUpdateQuery);
};
// Global Settings --> Basic  Details
const getGlobalConfiguration = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { _institution_id, _parent_id } = headers;
        const globalSettingQuery = {
            ...(_institution_id && {
                _institution_id: convertToMongoObjectId(_institution_id),
            }),
            ...(_parent_id && { _parent_id: convertToMongoObjectId(_parent_id) }),
        };
        let globalSetting = await settingsModel
            .findOne(globalSettingQuery, {
                globalConfiguration: 1,
            })
            .lean();
        if (!globalSetting) {
            await settingsModel.create(mockGlobalConfigurationData(_institution_id, _parent_id));
            globalSetting = await settingsModel
                .findOne(globalSettingQuery, {
                    globalConfiguration: 1,
                })
                .lean();
        }
        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: formatGlobalConfiguration(globalSetting),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateGlobalConfiguration = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { timeZone, isIndependentHours, session, isGenderSegregation } = body;
        const { id } = params;
        const setting = await settingsModel.findOne({ _id: id }, { globalConfiguration: 1 }).lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        const {
            basicDetails: { workingDays = [] },
        } = setting.globalConfiguration;

        const settingUpdate = await settingsModel.updateOne(
            { _id: id },
            {
                ...(timeZone && { 'globalConfiguration.basicDetails.timeZone': timeZone }),
                'globalConfiguration.basicDetails.isIndependentHours': isIndependentHours,
                'globalConfiguration.basicDetails.isGenderSegregation': isGenderSegregation,
                ...(session && {
                    'globalConfiguration.basicDetails.session.start': session.start,
                    'globalConfiguration.basicDetails.session.end': session.end,
                }),
                ...(session &&
                    !isIndependentHours && {
                        'globalConfiguration.basicDetails.workingDays': workingDays.map(
                            (workingDay) => {
                                return {
                                    session: workingDay.isActive ? workingDay.session : session,
                                    isActive: workingDay.isActive,
                                    _id: workingDay._id,
                                    name: workingDay.name,
                                };
                            },
                        ),
                    }),
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateWorkingDays = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, isIndependentHours, session, isAddWorkingDay } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne({ _id: settingId }, { globalConfiguration: 1 })
            .lean();
        const {
            basicDetails: { session: globalSession },
        } = setting.globalConfiguration;

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                ...(isIndependentHours &&
                    isAddWorkingDay && {
                        'globalConfiguration.basicDetails.workingDays.$[i].isActive': true,
                        'globalConfiguration.basicDetails.workingDays.$[i].session': session,
                    }),
                ...(!isIndependentHours &&
                    isAddWorkingDay && {
                        'globalConfiguration.basicDetails.workingDays.$[i].isActive': true,
                        'globalConfiguration.basicDetails.workingDays.$[i].session': globalSession,
                    }),
                ...(!isAddWorkingDay && {
                    'globalConfiguration.basicDetails.workingDays.$[i].isActive': false,
                }),
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const activateOrDeactivateWorkingDays = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, isActive } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne({ _id: settingId }, { globalConfiguration: 1 })
            .lean();

        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                'globalConfiguration.basicDetails.workingDays.$[i].isActive': isActive,
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addBreaks = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, breaks, session } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails.breaks': 1 },
            )
            .lean();

        if (
            setting &&
            setting.globalConfiguration &&
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.breaks
        ) {
            let breakNameExists = false;
            setting.globalConfiguration.basicDetails.breaks.forEach((breakEntry) => {
                if (breakEntry.name.toLowerCase() === breaks.name.toLowerCase()) {
                    breakNameExists = true;
                }
            });

            if (breakNameExists) return { statusCode: 410, message: 'Break Name already exists' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $push: {
                    'globalConfiguration.basicDetails.breaks': {
                        name: breaks.name,
                        session,
                        workingDays: breaks.days,
                    },
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getEmailIdConfiguration = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = params;

        const setting = await settingsModel
            .findById(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                { 'globalConfiguration.basicDetails.emailIdConfiguration': 1 },
            )
            .lean();
        return { statusCode: 200, message: DS_DATA_RETRIEVED, data: setting };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addEmailIdConfiguration = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, emailIdConfig } = body;
        if (
            emailIdConfig.password.toString().toLowerCase() !==
            emailIdConfig.reEnterPassword.toString().toLowerCase()
        ) {
            return { statusCode: 410, message: 'PASSWORD_MISMATCH' };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails.emailIdConfiguration': 1 },
            )
            .lean();

        const settingUpdate = await settingsModel.findByIdAndUpdate(
            { _id: settingId },
            {
                $set: {
                    'globalConfiguration.basicDetails.emailIdConfiguration': emailIdConfig,
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const sendTestMail = async ({ body = {} }) => {
    try {
        const { settingId, emailIdConfig } = body;
        if (
            emailIdConfig.password.toString().toLowerCase() !==
            emailIdConfig.reEnterPassword.toString().toLowerCase()
        ) {
            return { statusCode: 410, message: 'PASSWORD_MISMATCH' };
        }
        const mailSendStatus = await sendEmailCommon({
            displayName: emailIdConfig.displayName,
            fromEmailId: emailIdConfig.fromEmail,
            userName: emailIdConfig.userName ? emailIdConfig.userName : emailIdConfig.fromEmail,
            password: emailIdConfig.password,
            smtpClient: emailIdConfig.smtpClient,
            portNumber: emailIdConfig.portNumber,
            toEmailId: emailIdConfig.toEmail,
            ttl_ssl: emailIdConfig.ttl_ssl,
            subject: 'Test Email from DS',
            text: `It's An Test Email to verify mail Service`,
        });
        if (!mailSendStatus) {
            return { statusCode: 410, message: 'CHECK_CREDENTIALS' };
        }

        return { statusCode: 200, message: 'MAIL_SEND' };
    } catch (error) {
        console.log(error);
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateEmailIdConfiguration = async ({ body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, emailIdConfig } = body;
        const setting = await settingsModel
            .findOne(
                { _id: settingId },
                { _id: 1, 'globalConfiguration.basicDetails.emailIdConfiguration': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'EMAIL_CONFIGURATION_NOT_FOUND' };
        }
        if (emailIdConfig.password.toLowerCase() !== emailIdConfig.reEnterPassword.toLowerCase()) {
            return { statusCode: 410, message: 'PASSWORD_MISMATCH' };
        }
        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                'globalConfiguration.basicDetails.emailIdConfiguration': emailIdConfig,
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeEmailIdConfiguration = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = body;
        const setting = await settingsModel.findOne({ _id: settingId }, { _id: 1 }).lean();
        if (!setting) {
            return { statusCode: 410, message: 'EMAIL_CONFIGURATION_NOT_FOUND' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $unset: { 'globalConfiguration.basicDetails.emailIdConfiguration': '' },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateBreaks = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, breaks, session } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                { _id: settingId, 'globalConfiguration.basicDetails.breaks._id': id },
                { _id: 1, 'globalConfiguration.basicDetails.breaks': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Breaks not found' };
        }

        if (
            setting &&
            setting.globalConfiguration &&
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.breaks
        ) {
            let breakNameExists = false;
            setting.globalConfiguration.basicDetails.breaks.forEach((breakEntry) => {
                if (
                    breakEntry.name.toLowerCase() === breaks.name.toLowerCase() &&
                    breakEntry._id.toString() !== id
                ) {
                    breakNameExists = true;
                }
            });

            if (breakNameExists) return { statusCode: 410, message: 'Break Name already exists' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                'globalConfiguration.basicDetails.breaks.$[i].name': breaks.name,
                ...(session && { 'globalConfiguration.basicDetails.breaks.$[i].session': session }),
                ...(breaks.days && {
                    'globalConfiguration.basicDetails.breaks.$[i].workingDays': breaks.days,
                }),
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeBreaks = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                { _id: settingId, 'globalConfiguration.basicDetails.breaks._id': id },
                { _id: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Breaks not found' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $pull: { 'globalConfiguration.basicDetails.breaks': { _id: id } },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addEventType = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, eventType } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails.eventType': 1 },
            )
            .lean();

        if (
            setting &&
            setting.globalConfiguration &&
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.eventType
        ) {
            let eventTypeExists = false;
            setting.globalConfiguration.basicDetails.eventType.forEach((eventTypeEntry) => {
                if (eventTypeEntry.name.toLowerCase() === eventType.name.toLowerCase()) {
                    eventTypeExists = true;
                }
            });

            if (eventTypeExists) return { statusCode: 410, message: 'Event type already exists' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $push: {
                    'globalConfiguration.basicDetails.eventType': {
                        name: eventType.name,
                        isLeave: eventType.isLeave,
                    },
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateEventType = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, eventType, isRemove } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                { _id: settingId, 'globalConfiguration.basicDetails.eventType._id': id },
                { 'globalConfiguration.basicDetails.eventType': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Event type not found' };
        }

        if (
            setting &&
            setting.globalConfiguration &&
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.eventType
        ) {
            let eventTypeExists = false;
            setting.globalConfiguration.basicDetails.eventType.forEach((eventTypeEntry) => {
                if (
                    eventTypeEntry.name.toLowerCase() === eventType.name.toLowerCase() &&
                    eventTypeEntry._id.toString() !== id
                ) {
                    eventTypeExists = true;
                }
            });

            if (eventTypeExists) return { statusCode: 410, message: 'Event type already exists' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                ...(isRemove && {
                    $pull: {
                        'globalConfiguration.basicDetails.eventType': {
                            _id: id,
                        },
                    },
                }),
                ...(!isRemove && {
                    'globalConfiguration.basicDetails.eventType.$[i].name': eventType.name,
                    'globalConfiguration.basicDetails.eventType.$[i].isLeave': eventType.isLeave,
                }),
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: isRemove ? DS_DELETED : DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addLanguage = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, language } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const {
            basicDetails: { language: languageSetting = [] },
            programInput: { labelConfiguration = [] },
        } = setting.globalConfiguration;

        if (languageSetting.length) {
            if (
                languageSetting.find(
                    (languageEntry) =>
                        languageEntry.name === language.name &&
                        languageEntry.code === language.code,
                )
            ) {
                return { statusCode: 410, message: 'Language already exists' };
            }
        }

        languageSetting.push({ name: language.name, code: language.code, isDefault: false });

        const languageUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                'globalConfiguration.basicDetails.language': languageSetting,
                ...(!labelConfiguration.find(
                    (labelEntry) => labelEntry.language === language.code,
                ) && { ...mockProgramInputConfigurationData(language.code) }),
            },
        );

        if (!languageUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDefaultInLanguageSetting = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { id } = params;
        const { settingId } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.basicDetails.language._id': id,
                },
                { globalConfiguration: 1 },
            )
            .lean();

        if (!setting) {
            return { statusCode: 410, message: 'Language not found' };
        }

        const {
            basicDetails: { language },
        } = setting.globalConfiguration;

        language.forEach((languageEntry) => {
            if (isIDEquals(languageEntry._id, id)) {
                languageEntry.isDefault = true;
            } else {
                languageEntry.isDefault = false;
            }
        });

        const languageUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                'globalConfiguration.basicDetails.language': language,
            },
        );

        if (!languageUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateLanguage = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, language, isRemove } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.basicDetails.language._id': id,
                },
                { _id: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Language not found' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                ...(isRemove && {
                    $pull: {
                        'globalConfiguration.basicDetails.language': {
                            _id: id,
                        },
                    },
                }),
                ...(!isRemove && {
                    'globalConfiguration.basicDetails.language.$[i].name': language.name,
                    'globalConfiguration.basicDetails.language.$[i].code': language.code,
                }),
            },
            { arrayFilters: [{ 'i._id': id }] },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: isRemove ? DS_DELETED : DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

// Global Settings --> Program Inputs
const getProgramInputs = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { _institution_id, _parent_id } = headers;
        const setting = await settingsModel
            .findOne(
                {
                    ...(_institution_id && {
                        _institution_id: convertToMongoObjectId(_institution_id),
                    }),
                    ...(_parent_id && { _parent_id: convertToMongoObjectId(_parent_id) }),
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const { programInput } = setting.globalConfiguration;
        if (!programInput || !programInput.labelConfiguration.length) {
            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: [],
            };
        }

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: formatProgramInput(setting),
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const resetLabelConfiguration = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { id } = params;
        const setting = await settingsModel.findOne({ _id: id }, { globalConfiguration: 1 }).lean();
        const {
            basicDetails: { language = [] },
        } = setting.globalConfiguration;

        const languages = [];
        if (language.length) {
            language.forEach((languageEntry) => {
                languages.push(mockProgramInputConfigurationData(languageEntry.code, true));
            });

            const settingUpdate = await settingsModel.updateOne(
                { _id: id },
                {
                    'globalConfiguration.programInput.labelConfiguration': languages,
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
        }

        return {
            statusCode: 200,
            message: 'Label configuration reset successfully',
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateLabelConfiguration = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { id } = params;
        const { labelName, translatedLabels } = body;
        const setting = await settingsModel.findOne({ _id: id }, { globalConfiguration: 1 }).lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const {
            programInput: { labelConfiguration = [] },
        } = setting.globalConfiguration;

        const bulkWrites = [];
        const bulkUpdate = [];
        if (translatedLabels.length && labelConfiguration.length) {
            translatedLabels.forEach((translatedLabel) => {
                const labelData = labelConfiguration.find(
                    (labelEntry) => labelEntry.language === translatedLabel.language,
                );
                if (labelData) {
                    const label = labelData.labels.find(
                        (labelEntry) => labelEntry.defaultInput === labelName,
                    );
                    if (label) {
                        bulkWrites.push({
                            updateOne: {
                                filter: { _id: id },
                                update: {
                                    'globalConfiguration.programInput.labelConfiguration.$[i].labels.$[j].translatedInput':
                                        translatedLabel.label,
                                },
                                arrayFilters: [{ 'i._id': labelData._id }, { 'j._id': label._id }],
                            },
                        });
                    }
                    if (label.defaultInput === DS_TERM_KEY) {
                        bulkWrites.push({
                            updateOne: {
                                filter: { _id: convertToMongoObjectId(id) },
                                update: {
                                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[i].basicDetails.$[j].name':
                                        translatedLabel.label,
                                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[i].basicDetails.$[j].translatedInput':
                                        translatedLabel.label,
                                },
                                arrayFilters: [
                                    { 'i.language': translatedLabel.language },
                                    { 'j.mappingKey': 'batch' },
                                ],
                            },
                        });
                    }
                    if (label.defaultInput === DS_PROGRAM_KEY) {
                        bulkWrites.push({
                            updateOne: {
                                filter: { _id: convertToMongoObjectId(id) },
                                update: {
                                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[i].basicDetails.$[j].name':
                                        translatedLabel.label,
                                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[i].basicDetails.$[j].translatedInput':
                                        translatedLabel.label,
                                },
                                arrayFilters: [
                                    { 'i.language': translatedLabel.language },
                                    { 'j.mappingKey': 'program_no' },
                                ],
                            },
                        });
                    }
                    if (label.defaultInput === DS_PHASE_KEY) {
                        bulkUpdate.push({
                            updateMany: {
                                filter: {
                                    _institution_id: convertToMongoObjectId(_institution_id),
                                    isDeleted: false,
                                },
                                update: {
                                    'settings.programInput.programDurationFormat.$[i].format':
                                        translatedLabel.label,
                                },
                                arrayFilters: [{ 'i.defaultInput': label.defaultInput }],
                            },
                        });
                        bulkWrites.push({
                            updateOne: {
                                filter: { _id: convertToMongoObjectId(id) },
                                update: {
                                    'globalConfiguration.programInput.programDurationFormat.$[i].format':
                                        translatedLabel.label,
                                },
                                arrayFilters: [{ 'i.defaultInput': label.defaultInput }],
                            },
                        });
                    }
                    if (label.defaultInput === DS_YEAR_KEY) {
                        bulkUpdate.push({
                            updateMany: {
                                filter: {
                                    _institution_id: convertToMongoObjectId(_institution_id),
                                    isDeleted: false,
                                },
                                update: {
                                    'settings.programInput.programDurationFormat.$[i].format':
                                        translatedLabel.label,
                                },
                                arrayFilters: [{ 'i.defaultInput': label.defaultInput }],
                            },
                        });
                        bulkWrites.push({
                            updateOne: {
                                filter: { _id: convertToMongoObjectId(id) },
                                update: {
                                    'globalConfiguration.programInput.programDurationFormat.$[i].format':
                                        translatedLabel.label,
                                },
                                arrayFilters: [{ 'i.defaultInput': label.defaultInput }],
                            },
                        });
                    }
                }
            });
        }

        if (bulkWrites.length) {
            await settingsModel.bulkWrite(bulkWrites).catch((error) => {
                throw new Error(error);
            });
        }
        if (bulkUpdate.length) {
            await programModel.bulkWrite(bulkUpdate).catch((error) => {
                throw new Error(error);
            });
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const generateLabelConfiguration = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { id } = params;
        const setting = await settingsModel.findOne({ _id: id }, { globalConfiguration: 1 }).lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const {
            programInput: { labelConfiguration = [] },
        } = setting.globalConfiguration;

        const bulkWrites = [];
        const labels = getMockGenerateLabel();
        labels.forEach((label) => {
            labelConfiguration.forEach((configuration) => {
                bulkWrites.push({
                    updateOne: {
                        filter: { _id: id },
                        update: {
                            $push: {
                                'globalConfiguration.programInput.labelConfiguration.$[i].labels': {
                                    name: label.label,
                                    defaultInput: label.labelEnum,
                                    translatedInput: '',
                                },
                            },
                        },
                        arrayFilters: [{ 'i._id': configuration._id }],
                    },
                });
            });
        });

        if (bulkWrites.length) {
            await settingsModel.bulkWrite(bulkWrites).catch((error) => {
                throw new Error(error);
            });
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const createProgramType = async ({ body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { name, settingId, code } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const {
            programInput: { programType = [] },
        } = setting.globalConfiguration;

        if (programType.length) {
            if (
                programType.find(
                    (programTypeEntry) =>
                        programTypeEntry.name.toLowerCase() === name.toLowerCase(),
                )
            ) {
                return { statusCode: 410, message: 'Program type name already exists' };
            }
            if (
                programType.find(
                    (programTypeEntry) =>
                        programTypeEntry.code.toLowerCase() === code.toLowerCase(),
                )
            ) {
                return { statusCode: 410, message: 'Program type code already exists' };
            }
        }

        const programTypeUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $push: { 'globalConfiguration.programInput.programType': { name, code } },
            },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateProgramType = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { name, settingId, code } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.programInput.programType._id': id,
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Program type not found' };
        }

        const {
            programInput: { programType = [] },
        } = setting.globalConfiguration;

        if (programType.length) {
            if (
                programType.find(
                    (programTypeEntry) =>
                        programTypeEntry.name.toLowerCase() === name.toLowerCase() &&
                        programTypeEntry._id.toString() !== id,
                )
            ) {
                return { statusCode: 410, message: 'Program type name already exists' };
            }
            if (
                programType.find(
                    (programTypeEntry) =>
                        programTypeEntry.code.toLowerCase() === code.toLowerCase() &&
                        programTypeEntry._id.toString() !== id,
                )
            ) {
                return { statusCode: 410, message: 'Program type code already exists' };
            }
        }

        const programTypeUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                'globalConfiguration.programInput.programType.$[i].name': name,
                'globalConfiguration.programInput.programType.$[i].code': code,
            },
            { arrayFilters: [{ 'i._id': convertToMongoObjectId(id) }] },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        const programTypeUpdateInProgramInput = await programModel.updateMany(
            { _program_type_id: id },
            {
                programType: name,
            },
        );

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const removeProgramType = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const programModel = getModel(tenantURL, PROGRAM, programSchema);
        const { settingId } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.programInput.programType._id': id,
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Program type not found' };
        }

        const programInput = await programModel.findOne({ _program_type_id: id, isDeleted: false });
        if (programInput) {
            return { statusCode: 410, message: 'Program type already mapped' };
        }

        const programTypeUpdate = await settingsModel.updateOne(
            { _id: settingId },
            { $pull: { 'globalConfiguration.programInput.programType': { _id: id } } },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCurriculumNaming = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const curriculumModel = getModel(tenantURL, CURRICULUM, curriculumSchema);
        const { settingId } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.programInput.curriculumNaming._id': id,
                },
                { globalConfiguration: 1, _institution_id: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Settings not found' };
        }

        const {
            programInput: { curriculumNaming = [] },
        } = setting.globalConfiguration;

        let curriculumMode;
        if (curriculumNaming.length) {
            curriculumNaming.forEach((curriculumNamingEntry) => {
                curriculumNamingEntry.isDefault = false;

                if (isIDEquals(curriculumNamingEntry._id, id)) {
                    curriculumNamingEntry.isDefault = true;
                    curriculumMode = curriculumNamingEntry.mode;
                }
            });
        }
        const programTypeUpdate = await settingsModel.findByIdAndUpdate(
            { _id: settingId },
            { 'globalConfiguration.programInput.curriculumNaming': curriculumNaming },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        if (curriculumMode !== 3) {
            const curriculumDocs = await curriculumModel.aggregate([
                {
                    $match: {
                        _institution_id: setting._institution_id,
                        isDeleted: false,
                        isActive: true,
                    },
                },
                {
                    $group: {
                        _id: '$_program_id',
                        curriculum: {
                            $push: {
                                _id: '$_id',
                                curriculumName: '$curriculumName',
                                createdAt: '$createdAt',
                            },
                        },
                    },
                },
                { $sort: { createdAt: 1 } },
            ]);
            if (curriculumMode === 1) {
                const bulkWrites = [];
                curriculumDocs.forEach(async (curriculumDoc) => {
                    curriculumDoc.curriculum.forEach(async (curriculum, index) => {
                        bulkWrites.push({
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(curriculum._id),
                                },
                                update: { curriculumName: `version ${index + 1}.0` },
                            },
                        });
                    });
                });
                if (bulkWrites.length) {
                    const docs = await curriculumModel.bulkWrite(bulkWrites);
                }
            } else if (curriculumMode === 2) {
                const bulkWrites = [];
                curriculumDocs.forEach(async (curriculumDoc) => {
                    curriculumDoc.curriculum.forEach(async (curriculum, index) => {
                        bulkWrites.push({
                            updateOne: {
                                filter: {
                                    _id: convertToMongoObjectId(curriculum._id),
                                },
                                update: { curriculumName: `version ${index + 1}` },
                            },
                        });
                    });
                });
                if (bulkWrites.length) {
                    const docs = await curriculumModel.bulkWrite(bulkWrites);
                }
            }
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateCreditHours = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.programInput.creditHours._id': id,
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Settings not found' };
        }

        const {
            programInput: { creditHours = [] },
        } = setting.globalConfiguration;

        if (creditHours.length) {
            creditHours.forEach((creditHoursEntry) => {
                creditHoursEntry.isDefault = false;

                if (isIDEquals(creditHoursEntry._id, id)) {
                    creditHoursEntry.isDefault = true;
                }
            });
        }

        const programTypeUpdate = await settingsModel.updateOne(
            { _id: settingId },
            { 'globalConfiguration.programInput.creditHours': creditHours },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateProgramDurationFormat = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, withoutLevel } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                    'globalConfiguration.programInput.programDurationFormat._id':
                        convertToMongoObjectId(id),
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Settings not found' };
        }

        const {
            programInput: { programDurationFormat = [] },
        } = setting.globalConfiguration;

        if (programDurationFormat.length) {
            programDurationFormat.forEach((programDurationFormatEntry) => {
                programDurationFormatEntry.isDefault = false;
                if (isIDEquals(programDurationFormatEntry._id, id)) {
                    programDurationFormatEntry.isDefault = true;
                    programDurationFormatEntry.withoutLevel = withoutLevel;
                }
            });
        }

        const programTypeUpdate = await settingsModel.updateOne(
            { _id: settingId },
            { 'globalConfiguration.programInput.programDurationFormat': programDurationFormat },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateIndependentCourseCreditHours = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = body;
        const { id } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.independentCourseInput.creditHours._id': id,
                },
                { globalConfiguration: 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Settings not found' };
        }

        const {
            independentCourseInput: { creditHours = [] },
        } = setting.globalConfiguration;

        if (creditHours.length) {
            creditHours.forEach((creditHoursEntry) => {
                creditHoursEntry.isDefault = false;

                if (isIDEquals(creditHoursEntry._id, id)) {
                    creditHoursEntry.isDefault = true;
                }
            });
        }

        const programTypeUpdate = await settingsModel.updateOne(
            { _id: settingId },
            { 'globalConfiguration.independentCourseInput.creditHours': creditHours },
        );

        if (!programTypeUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const staffManagementDashboard = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL, _institution_id, _parent_id } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        let globalSetting = await settingsModel
            .findById(settingId, {
                globalConfiguration: 1,
            })
            .lean();
        if (!globalSetting) {
            const setting = await settingsModel.create(
                mockGlobalConfigurationData(_institution_id, _parent_id),
            );
            globalSetting = await settingsModel
                .findById(setting._id, {
                    globalConfiguration: 1,
                })
                .lean();
        }
        let biometricLabel;
        let staffSettingCount;
        if (type === DC_STAFF) {
            globalSetting.globalConfiguration.staffUserManagement.biometricConfiguration.forEach(
                (config) => {
                    if (config.isActive === true) {
                        biometricLabel = config.labelName;
                    }
                },
            );
            staffSettingCount = {
                designationConfiguration: globalSetting.globalConfiguration.staffUserManagement
                    .designationConfiguration
                    ? globalSetting.globalConfiguration.staffUserManagement.designationConfiguration
                          .designations.length
                    : 0,
                vaccinationsConfiguration: globalSetting.globalConfiguration.basicDetails
                    .vaccineConfiguration
                    ? globalSetting.globalConfiguration.basicDetails.vaccineConfiguration.length
                    : 0,
                mailContentConfiguration: globalSetting.globalConfiguration.staffUserManagement
                    .mailConfiguration
                    ? globalSetting.globalConfiguration.staffUserManagement.mailConfiguration.length
                    : 0,
                biometricConfiguration: biometricLabel,
                mailSettingsConfiguration: globalSetting.globalConfiguration.staffUserManagement
                    .mailSettingsConfiguration
                    ? globalSetting.globalConfiguration.staffUserManagement
                          .mailSettingsConfiguration.length
                    : 0,
                documentConfiguration: globalSetting.globalConfiguration.staffUserManagement
                    .documentConfiguration
                    ? globalSetting.globalConfiguration.staffUserManagement.documentConfiguration
                          .chooseDocuments.length
                    : 0,
                labelFieldConfiguration: globalSetting.globalConfiguration.staffUserManagement
                    .labelFieldConfiguration
                    ? globalSetting.globalConfiguration.staffUserManagement
                          .labelFieldConfiguration[0].basicDetails.length +
                      globalSetting.globalConfiguration.staffUserManagement
                          .labelFieldConfiguration[0].profileDetails.length +
                      globalSetting.globalConfiguration.staffUserManagement
                          .labelFieldConfiguration[0].addressDetails.length +
                      globalSetting.globalConfiguration.staffUserManagement
                          .labelFieldConfiguration[0].contactDetails.length
                    : 0,
            };
        } else {
            globalSetting.globalConfiguration.studentUserManagement.biometricConfiguration.forEach(
                (config) => {
                    if (config.isActive === true) {
                        biometricLabel = config.labelName;
                    }
                },
            );
            staffSettingCount = {
                designationConfiguration: globalSetting.globalConfiguration.studentUserManagement
                    .designationConfiguration
                    ? globalSetting.globalConfiguration.studentUserManagement
                          .designationConfiguration.designations.length
                    : 0,
                vaccinationsConfiguration: globalSetting.globalConfiguration.studentUserManagement
                    .vaccineConfiguration
                    ? globalSetting.globalConfiguration.studentUserManagement.vaccineConfiguration
                          .length
                    : 0,
                mailContentConfiguration: globalSetting.globalConfiguration.studentUserManagement
                    .mailConfiguration
                    ? globalSetting.globalConfiguration.studentUserManagement.mailConfiguration
                          .length
                    : 0,
                biometricConfiguration: biometricLabel,
                mailSettingsConfiguration: globalSetting.globalConfiguration.studentUserManagement
                    .mailSettingsConfiguration
                    ? globalSetting.globalConfiguration.studentUserManagement
                          .mailSettingsConfiguration.length
                    : 0,
                documentConfiguration: globalSetting.globalConfiguration.studentUserManagement
                    .documentConfiguration
                    ? globalSetting.globalConfiguration.studentUserManagement.documentConfiguration
                          .chooseDocuments.length
                    : 0,
                labelFieldConfiguration: globalSetting.globalConfiguration.studentUserManagement
                    .labelFieldConfiguration
                    ? globalSetting.globalConfiguration.studentUserManagement
                          .labelFieldConfiguration[0].basicDetails.length +
                      globalSetting.globalConfiguration.studentUserManagement
                          .labelFieldConfiguration[0].profileDetails.length +
                      globalSetting.globalConfiguration.studentUserManagement
                          .labelFieldConfiguration[0].addressDetails.length +
                      globalSetting.globalConfiguration.studentUserManagement
                          .labelFieldConfiguration[0].contactDetails.length
                    : 0,
            };
        }
        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: staffSettingCount,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addDesignation = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { type } = params;
        const { settingId, designationName } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.designationConfiguration.designations': 1,
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations': 1,
                },
            )
            .lean();
        let settingUpdate;
        if (type === DC_STAFF) {
            if (
                setting &&
                setting.globalConfiguration &&
                setting.globalConfiguration.staffUserManagement &&
                setting.globalConfiguration.staffUserManagement.designationConfiguration &&
                setting.globalConfiguration.staffUserManagement.designationConfiguration
                    .designations
            ) {
                let designationNameExists = false;
                setting.globalConfiguration.staffUserManagement.designationConfiguration.designations.forEach(
                    (designationEntry) => {
                        if (
                            designationEntry.designationName.toLowerCase() ===
                            designationName.toLowerCase()
                        ) {
                            designationNameExists = true;
                        }
                    },
                );

                if (designationNameExists)
                    return { statusCode: 410, message: 'Designation Name already exists' };
            }
            settingUpdate = await settingsModel.updateOne(
                { _id: settingId },
                {
                    $push: {
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations':
                            {
                                designationName,
                            },
                    },
                },
            );
        } else {
            if (
                setting &&
                setting.globalConfiguration &&
                setting.globalConfiguration.studentUserManagement &&
                setting.globalConfiguration.studentUserManagement.designationConfiguration &&
                setting.globalConfiguration.studentUserManagement.designationConfiguration
                    .designations
            ) {
                let designationNameExists = false;
                setting.globalConfiguration.studentUserManagement.designationConfiguration.designations.forEach(
                    (designationEntry) => {
                        if (
                            designationEntry.designationName.toLowerCase() ===
                            designationName.toLowerCase()
                        ) {
                            designationNameExists = true;
                        }
                    },
                );

                if (designationNameExists)
                    return { statusCode: 410, message: 'Designation Name already exists' };
            }
            settingUpdate = await settingsModel.updateOne(
                { _id: settingId },
                {
                    $push: {
                        'globalConfiguration.studentUserManagement.designationConfiguration.designations':
                            {
                                designationName,
                            },
                    },
                },
            );
        }

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        return { statusCode: 201, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const designationCheck = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = body;
        const { designationId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations._id':
                            designationId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Designation not exists' };
            const checkState =
                setting.globalConfiguration.staffUserManagement.designationConfiguration.designations.find(
                    (designationEntry) =>
                        designationEntry._id.toString() === designationId.toString(),
                );
            const statusCheck = checkState.isActive !== true;
            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.designationConfiguration.designations.$[designationId].isActive':
                        statusCheck,
                },
                {
                    arrayFilters: [
                        {
                            'designationId._id': designationId,
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 204, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations._id':
                        designationId,
                },
                {
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Designation not exists' };
        const checkState =
            setting.globalConfiguration.studentUserManagement.designationConfiguration.designations.find(
                (designationEntry) => designationEntry._id.toString() === designationId.toString(),
            );
        const statusCheck = checkState.isActive !== true;
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.designationConfiguration.designations.$[designationId].isActive':
                    statusCheck,
            },
            {
                arrayFilters: [
                    {
                        'designationId._id': designationId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const designationUpdate = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, designationName } = body;
        const { designationId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations._id':
                            designationId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Designation not exists' };
            const checkState =
                setting.globalConfiguration.staffUserManagement.designationConfiguration.designations.find(
                    (designationEntry) =>
                        designationEntry._id.toString() !== designationId.toString() &&
                        designationEntry.designationName.toLowerCase() ===
                            designationName.toLowerCase(),
                );

            if (checkState) return { statusCode: 410, message: 'Designation Name already exists' };

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.designationConfiguration.designations.$[designationId].designationName':
                        designationName,
                },
                {
                    arrayFilters: [
                        {
                            'designationId._id': designationId,
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 204, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations._id':
                        designationId,
                },
                {
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Designation not exists' };
        const checkState =
            setting.globalConfiguration.studentUserManagement.designationConfiguration.designations.find(
                (designationEntry) =>
                    designationEntry._id.toString() !== designationId.toString() &&
                    designationEntry.designationName.toLowerCase() ===
                        designationName.toLowerCase(),
            );

        if (checkState) return { statusCode: 410, message: 'Designation Name already exists' };

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.designationConfiguration.designations.$[designationId].designationName':
                    designationName,
            },
            {
                arrayFilters: [
                    {
                        'designationId._id': designationId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const designationDelete = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, designationId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations._id':
                            designationId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Designation not exists' };

            const settingUpdate = await settingsModel.updateOne(
                { _id: settingId },
                {
                    $pull: {
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations':
                            { _id: designationId },
                    },
                },
            );
            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 204, message: DS_DELETED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations._id':
                        designationId,
                },
                {
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Designation not exists' };

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $pull: {
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations':
                        { _id: designationId },
                },
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDesignationLists = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.designationConfiguration.designations': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Designation not exists' };

            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: setting.globalConfiguration.staffUserManagement.designationConfiguration
                    .designations,
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.designationConfiguration.designations': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Designation not exists' };

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.studentUserManagement.designationConfiguration
                .designations,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getLabelFieldLists = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, language, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                        'globalConfiguration.basicDetails.vaccineConfiguration': 1,
                        'globalConfiguration.basicDetails.vaccineLabelDetails': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Label / Field not exists' };
            let settingsGet;
            if (
                setting.globalConfiguration.staffUserManagement.labelFieldConfiguration &&
                setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.length > 0
            ) {
                settingsGet =
                    setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.find(
                        (element) => element.language === language,
                    );
            }

            let vaccineDetails = [];
            if (
                setting.globalConfiguration.basicDetails.vaccineConfiguration &&
                setting.globalConfiguration.basicDetails.vaccineConfiguration.length > 0
            ) {
                vaccineDetails = setting.globalConfiguration.basicDetails.vaccineConfiguration.map(
                    (element) => {
                        return {
                            _id: element._id,
                            categoryName: element.categoryName,
                            isActive: element.isActive,
                            isMandatory: element.isMandatory,
                        };
                    },
                );
            }

            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: {
                    VaccineDetailsCheckBox:
                        setting.globalConfiguration.basicDetails.vaccineLabelDetails,
                    vaccineDetails,
                    labelDetails: language
                        ? settingsGet
                        : setting.globalConfiguration.staffUserManagement.labelFieldConfiguration,
                },
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.basicDetails.vaccineConfiguration': 1,
                    'globalConfiguration.basicDetails.vaccineLabelDetails': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Label / Field not exists' };
        let settingsGet;
        if (
            setting.globalConfiguration.studentUserManagement.labelFieldConfiguration &&
            setting.globalConfiguration.studentUserManagement.labelFieldConfiguration.length > 0
        ) {
            settingsGet =
                setting.globalConfiguration.studentUserManagement.labelFieldConfiguration.find(
                    (element) => element.language === language,
                );
        }

        let vaccineDetails = [];
        if (
            setting.globalConfiguration.basicDetails.vaccineConfiguration &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration.length > 0
        ) {
            vaccineDetails = setting.globalConfiguration.basicDetails.vaccineConfiguration.map(
                (element) => {
                    return {
                        _id: element._id,
                        categoryName: element.categoryName,
                        isActive: element.isActive,
                        isMandatory: element.isMandatory,
                    };
                },
            );
        }

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: {
                VaccineDetailsCheckBox:
                    setting.globalConfiguration.basicDetails.vaccineLabelDetails,
                vaccineDetails,
                labelDetails: language
                    ? settingsGet
                    : setting.globalConfiguration.studentUserManagement.labelFieldConfiguration,
            },
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const labelFieldUpdate = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelName, labelType, translatedLabels } = body;
        const { labelId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    { 'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1 },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Setting not exists' };

            const languagesReceived = translatedLabels.map((element) => element.language);
            const languagesAvailable =
                setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.map(
                    (element) => element.language,
                );
            const languagesToCreate = languagesReceived.filter(function (obj) {
                return languagesAvailable.indexOf(obj) === -1;
            });
            if (languagesToCreate.length > 0) {
                const bulkWrites = [];
                for (language of languagesToCreate) {
                    bulkWrites.push({
                        updateOne: {
                            filter: { _id: settingId },
                            update: {
                                $push: {
                                    'globalConfiguration.staffUserManagement.labelFieldConfiguration':
                                        mockLabelFieldConfigurationData(language),
                                },
                            },
                        },
                    });
                }
                if (bulkWrites.length) {
                    await settingsModel.bulkWrite(bulkWrites).catch((error) => {
                        throw new Error(error);
                    });
                }
            }
            const labelUpdates = [];
            for (translatedLabel of translatedLabels) {
                const setModifier = { update: {} };
                setModifier.update[
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[i].translatedInput'
                ] = translatedLabel.label;

                labelUpdates.push({
                    updateOne: {
                        filter: { _id: settingId },
                        update: setModifier.update,
                        arrayFilters: [
                            { 'i.name': labelName },
                            { 'j.language': translatedLabel.language },
                        ],
                    },
                });
            }

            const settingUpdate = await settingsModel.bulkWrite(labelUpdates).catch((error) => {
                throw new Error(error);
            });

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Setting not exists' };

        const languagesReceived = translatedLabels.map((element) => element.language);
        const languagesAvailable =
            setting.globalConfiguration.studentUserManagement.labelFieldConfiguration.map(
                (element) => element.language,
            );
        const languagesToCreate = languagesReceived.filter(function (obj) {
            return languagesAvailable.indexOf(obj) === -1;
        });
        if (languagesToCreate.length > 0) {
            const bulkWrites = [];
            for (language of languagesToCreate) {
                bulkWrites.push({
                    updateOne: {
                        filter: { _id: settingId },
                        update: {
                            $push: {
                                'globalConfiguration.studentUserManagement.labelFieldConfiguration':
                                    mockLabelFieldConfigurationData(language),
                            },
                        },
                    },
                });
            }
            if (bulkWrites.length) {
                await settingsModel.bulkWrite(bulkWrites).catch((error) => {
                    throw new Error(error);
                });
            }
        }
        const labelUpdates = [];
        for (translatedLabel of translatedLabels) {
            const setModifier = { update: {} };
            setModifier.update[
                'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                    labelType +
                    '.$[i].translatedInput'
            ] = translatedLabel.label;

            labelUpdates.push({
                updateOne: {
                    filter: { _id: settingId },
                    update: setModifier.update,
                    arrayFilters: [
                        { 'i.name': labelName },
                        { 'j.language': translatedLabel.language },
                    ],
                },
            });
        }

        const settingUpdate = await settingsModel.bulkWrite(labelUpdates).catch((error) => {
            throw new Error(error);
        });

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const labelFieldCheck = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelType, language, isActive, isMandatory } = body;
        const { labelId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration': 1,
                        'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                        _institution_id: 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Setting not exists' };

            const typeLists =
                setting.globalConfiguration.staffUserManagement.labelFieldConfiguration.find(
                    (entry) => entry.language === language,
                );

            const typeListExists = typeLists[labelType].find(
                (entry) => entry._id.toString() === labelId.toString(),
            );

            if (!typeListExists) return { statusCode: 404, message: 'Not found the records' };

            const setModifier = { $set: {} };

            const arrayFiltersToUpdate = [{ 'i._id': labelId }, { 'j.language': language }];
            if (
                typeListExists.mappingKey === 'country' ||
                typeListExists.mappingKey === 'district' ||
                typeListExists.mappingKey === 'city'
            ) {
                if (typeListExists.mappingKey !== 'country') {
                    setModifier.$set[
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                            labelType +
                            '.$[m].isActive'
                    ] = isActive;
                    setModifier.$set[
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                            labelType +
                            '.$[m].isMandatory'
                    ] = isMandatory;

                    const getCountryId = typeLists[labelType].find(
                        (entry) => entry.mappingKey === 'country',
                    );
                    arrayFiltersToUpdate.push({
                        'm._id': convertToMongoObjectId(getCountryId._id),
                    });
                }
                if (typeListExists.mappingKey !== 'district') {
                    setModifier.$set[
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                            labelType +
                            '.$[n].isActive'
                    ] = isActive;

                    setModifier.$set[
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                            labelType +
                            '.$[n].isMandatory'
                    ] = isMandatory;
                    const getStateId = typeLists[labelType].find(
                        (entry) => entry.mappingKey === 'district',
                    );
                    arrayFiltersToUpdate.push({ 'n._id': convertToMongoObjectId(getStateId._id) });
                }
                if (typeListExists.mappingKey !== 'city') {
                    setModifier.$set[
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                            labelType +
                            '.$[o].isActive'
                    ] = isActive;

                    setModifier.$set[
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                            labelType +
                            '.$[o].isMandatory'
                    ] = isMandatory;
                    const getCityId = typeLists[labelType].find(
                        (entry) => entry.mappingKey === 'city',
                    );
                    arrayFiltersToUpdate.push({ 'o._id': convertToMongoObjectId(getCityId._id) });
                }
            }

            setModifier.$set[
                'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                    labelType +
                    '.$[i].isActive'
            ] = isActive;

            setModifier.$set[
                'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[j].' +
                    labelType +
                    '.$[i].isMandatory'
            ] = isMandatory;

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                setModifier,
                { arrayFilters: arrayFiltersToUpdate },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            LabelFieldCheckEmailSent(
                setting,
                { isMandatory, labelId, labelType, language, type: DC_STAFF },
                tenantURL,
            );

            return { statusCode: 200, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': 1,
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    _institution_id: 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Setting not exists' };

        const typeLists =
            setting.globalConfiguration.studentUserManagement.labelFieldConfiguration.find(
                (entry) => entry.language === language,
            );

        const typeListExists = typeLists[labelType].find(
            (entry) => entry._id.toString() === labelId.toString(),
        );

        if (!typeListExists) return { statusCode: 404, message: 'Not found the records' };

        const setModifier = { $set: {} };

        const arrayFiltersToUpdate = [{ 'i._id': labelId }, { 'j.language': language }];
        if (
            typeListExists.mappingKey === 'country' ||
            typeListExists.mappingKey === 'district' ||
            typeListExists.mappingKey === 'city'
        ) {
            if (typeListExists.mappingKey !== 'country') {
                setModifier.$set[
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[m].isActive'
                ] = isActive;
                setModifier.$set[
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[m].isMandatory'
                ] = isMandatory;

                const getCountryId = typeLists[labelType].find(
                    (entry) => entry.mappingKey === 'country',
                );
                arrayFiltersToUpdate.push({
                    'm._id': convertToMongoObjectId(getCountryId._id),
                });
            }
            if (typeListExists.mappingKey !== 'district') {
                setModifier.$set[
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[n].isActive'
                ] = isActive;

                setModifier.$set[
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[n].isMandatory'
                ] = isMandatory;
                const getStateId = typeLists[labelType].find(
                    (entry) => entry.mappingKey === 'district',
                );
                arrayFiltersToUpdate.push({ 'n._id': convertToMongoObjectId(getStateId._id) });
            }
            if (typeListExists.mappingKey !== 'city') {
                setModifier.$set[
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[o].isActive'
                ] = isActive;

                setModifier.$set[
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                        labelType +
                        '.$[o].isMandatory'
                ] = isMandatory;
                const getCityId = typeLists[labelType].find((entry) => entry.mappingKey === 'city');
                arrayFiltersToUpdate.push({ 'o._id': convertToMongoObjectId(getCityId._id) });
            }
        }

        setModifier.$set[
            'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                labelType +
                '.$[i].isActive'
        ] = isActive;

        setModifier.$set[
            'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[j].' +
                labelType +
                '.$[i].isMandatory'
        ] = isMandatory;

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            setModifier,
            { arrayFilters: arrayFiltersToUpdate },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        LabelFieldCheckEmailSent(
            setting,
            { isMandatory, labelId, labelType, language, type: DC_STUDENT },
            tenantURL,
        );

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const existingLabelFieldUpdate = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const { type } = params;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        if (type === DC_STAFF) {
            const settingUpdate = await settingsModel.updateMany(
                {
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': {
                        $exists: false,
                    },
                },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration':
                            mockLabelFieldConfigurationData(ENGLISH),
                    },
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 204, message: DS_UPDATED };
        }
        const settingUpdate = await settingsModel.updateMany(
            {
                'globalConfiguration.studentUserManagement.labelFieldConfiguration': {
                    $exists: false,
                },
            },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration':
                        mockLabelFieldConfigurationData(ENGLISH),
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const biometricCheck = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = body;
        const { labelId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    { 'globalConfiguration.staffUserManagement.biometricConfiguration': 1 },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Setting not exists' };

            const typeLists =
                setting.globalConfiguration.staffUserManagement.biometricConfiguration;

            const typeListExists = typeLists.find(
                (entry) => entry._id.toString() === labelId.toString(),
            );

            if (!typeListExists) return { statusCode: 404, message: 'Not found the records' };

            const disableAll = await settingsModel.update(
                { _id: settingId },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.biometricConfiguration.$[labelId].isActive': false,
                    },
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': { $exists: true },
                        },
                    ],
                },
            );
            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.biometricConfiguration.$[labelId].isActive': true,
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': labelId,
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 204, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.studentUserManagement.biometricConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Setting not exists' };

        const typeLists = setting.globalConfiguration.studentUserManagement.biometricConfiguration;

        const typeListExists = typeLists.find(
            (entry) => entry._id.toString() === labelId.toString(),
        );

        if (!typeListExists) return { statusCode: 404, message: 'Not found the records' };

        const disableAll = await settingsModel.update(
            { _id: settingId },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.biometricConfiguration.$[labelId].isActive': false,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': { $exists: true },
                    },
                ],
            },
        );
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.biometricConfiguration.$[labelId].isActive': true,
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': labelId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getBiometricList = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        if (type == 'staff') {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    { 'globalConfiguration.staffUserManagement.biometricConfiguration': 1 },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Biometric not exists' };

            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: setting.globalConfiguration.staffUserManagement.biometricConfiguration,
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.studentUserManagement.biometricConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Biometric not exists' };

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.studentUserManagement.biometricConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateMailContent = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelBody } = body;
        const { labelId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    { 'globalConfiguration.staffUserManagement.mailConfiguration': 1 },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Setting not exists' };

            const typeLists = setting.globalConfiguration.staffUserManagement.mailConfiguration;

            const typeListExists = typeLists.find(
                (entry) => entry._id.toString() === labelId.toString(),
            );

            if (!typeListExists) return { statusCode: 404, message: 'Not found the records' };

            const disableAll = await settingsModel.update(
                { _id: settingId },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.mailConfiguration.$[labelId].labelBody':
                            labelBody,
                    },
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': labelId,
                        },
                    ],
                },
            );
            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.biometricConfiguration.$[labelId].isActive': true,
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': labelId,
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 204, message: DS_UPDATED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.studentUserManagement.mailConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Setting not exists' };

        const typeLists = setting.globalConfiguration.studentUserManagement.mailConfiguration;

        const typeListExists = typeLists.find(
            (entry) => entry._id.toString() === labelId.toString(),
        );

        if (!typeListExists) return { statusCode: 404, message: 'Not found the records' };

        const disableAll = await settingsModel.update(
            { _id: settingId },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.mailConfiguration.$[labelId].labelBody':
                        labelBody,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': labelId,
                    },
                ],
            },
        );
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.biometricConfiguration.$[labelId].isActive': true,
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': labelId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getMailContent = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.mailConfiguration': 1,
                    'globalConfiguration.studentUserManagement.mailConfiguration': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'mail configuration not exists' };

        if (type === DC_STAFF) {
            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: setting.globalConfiguration.staffUserManagement.mailConfiguration,
            };
        }
        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.studentUserManagement.mailConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDocumentFormatAndSize = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const { type } = params;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, documentFormat, documentSize, maximumSize } = body;
        if (type === DC_STAFF) {
            const settingUpdate = await settingsModel.findByIdAndUpdate(
                {
                    _id: settingId,
                },
                {
                    $set: {
                        ...(documentFormat && {
                            'globalConfiguration.staffUserManagement.documentConfiguration.documentFormat':
                                documentFormat,
                        }),
                        ...(documentSize && {
                            'globalConfiguration.staffUserManagement.documentConfiguration.documentSize':
                                documentSize,
                        }),
                        ...(maximumSize && {
                            'globalConfiguration.staffUserManagement.documentConfiguration.documentMaximumSize':
                                maximumSize,
                        }),
                    },
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            return { statusCode: 200, message: DS_UPDATED };
        }
        const settingUpdate = await settingsModel.findByIdAndUpdate(
            {
                _id: settingId,
            },
            {
                $set: {
                    ...(documentFormat && {
                        'globalConfiguration.studentUserManagement.documentConfiguration.documentFormat':
                            documentFormat,
                    }),
                    ...(documentSize && {
                        'globalConfiguration.studentUserManagement.documentConfiguration.documentSize':
                            documentSize,
                    }),
                    ...(maximumSize && {
                        'globalConfiguration.studentUserManagement.documentConfiguration.documentMaximumSize':
                            maximumSize,
                    }),
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addDocumentsCategory = async ({ body = {}, headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const { type } = params;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, documentCategory, documentCategoryDescription } = body;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                    },
                )
                .lean();

            if (!setting) return { statusCode: 404, message: 'Document configuration not exists' };

            if (
                setting.globalConfiguration &&
                setting.globalConfiguration.staffUserManagement &&
                setting.globalConfiguration.staffUserManagement.documentConfiguration &&
                setting.globalConfiguration.staffUserManagement.documentConfiguration
                    .chooseDocuments
            ) {
                const checkDuplicateCategory =
                    setting.globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.map(
                        (categoryEntry) => categoryEntry.documentCategory.toLowerCase(),
                    );
                if (checkDuplicateCategory.length > 0) {
                    const isExists = checkDuplicateCategory.includes(
                        documentCategory.toLowerCase(),
                    );
                    if (isExists)
                        return { statusCode: 410, message: 'Category name already exists' };
                }
            }
            const settingUpdate = await settingsModel.findByIdAndUpdate(
                { _id: settingId },
                {
                    $push: {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments':
                            {
                                documentCategory,
                                documentCategoryDescription,
                            },
                    },
                },
            );
            if (!settingUpdate) {
                return { statusCode: 410, message: DS_ADD_FAILED };
            }
            return { statusCode: 200, message: DS_ADDED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                },
            )
            .lean();

        if (!setting) return { statusCode: 404, message: 'Document configuration not exists' };

        if (
            setting.globalConfiguration &&
            setting.globalConfiguration.studentUserManagement &&
            setting.globalConfiguration.studentUserManagement.documentConfiguration &&
            setting.globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments
        ) {
            const checkDuplicateCategory =
                setting.globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.map(
                    (categoryEntry) => categoryEntry.documentCategory.toLowerCase(),
                );
            if (checkDuplicateCategory.length > 0) {
                const isExists = checkDuplicateCategory.includes(documentCategory.toLowerCase());
                if (isExists) return { statusCode: 410, message: 'Category name already exists' };
            }
        }
        const settingUpdate = await settingsModel.findByIdAndUpdate(
            { _id: settingId },
            {
                $push: {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments':
                        {
                            documentCategory,
                            documentCategoryDescription,
                        },
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }
        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDocumentsCategory = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { documentCategory, documentCategoryDescription } = body;
        const { settingId, documentCategoryId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: convertToMongoObjectId(settingId),
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments._id':
                            convertToMongoObjectId(documentCategoryId),
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Document not exists' };

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    ...(documentCategory && {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.$[labelId].documentCategory':
                            documentCategory,
                    }),
                    ...(documentCategoryDescription && {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.$[labelId].documentCategoryDescription':
                            documentCategoryDescription,
                    }),
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': documentCategoryId,
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments._id':
                        convertToMongoObjectId(documentCategoryId),
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Document not exists' };

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                ...(documentCategory && {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.$[labelId].documentCategory':
                        documentCategory,
                }),
                ...(documentCategoryDescription && {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.$[labelId].documentCategoryDescription':
                        documentCategoryDescription,
                }),
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': documentCategoryId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteDocumentsCategory = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, documentCategoryId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments._id':
                            documentCategoryId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                    },
                )
                .lean();

            if (!setting) return { statusCode: 404, message: 'Document not exists' };

            const settingUpdate = await settingsModel.updateOne(
                { _id: settingId },
                {
                    $pull: {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments':
                            { _id: convertToMongoObjectId(documentCategoryId) },
                    },
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_DELETED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments._id':
                        documentCategoryId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                },
            )
            .lean();

        if (!setting) return { statusCode: 404, message: 'Document not exists' };

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $pull: {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments':
                        { _id: convertToMongoObjectId(documentCategoryId) },
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addDocuments = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, documentCategoryId, document } = body;
        const { type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                    },
                )
                .lean();

            if (!setting) return { statusCode: 404, message: 'Document configuration not exists' };
            const settingUpdate = await settingsModel.findByIdAndUpdate(
                {
                    _id: settingId,
                    'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments._id':
                        convertToMongoObjectId(documentCategoryId),
                },
                {
                    $push: {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.$[documentCategory].document':
                            document,
                    },
                },
                {
                    arrayFilters: [
                        {
                            'documentCategory._id': convertToMongoObjectId(documentCategoryId),
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_ADD_FAILED };
            }

            const getSettingMailTrigger = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                        'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                        _institution_id: 1,
                    },
                )
                .lean();

            const categoryObject =
                getSettingMailTrigger.globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.find(
                    (element) => element._id.toString() === documentCategoryId.toString(),
                );
            const documentObject = categoryObject.document.find(
                (elementEntry) =>
                    elementEntry.labelName.toString() === document[0].labelName.toString(),
            );
            documentObject._category_id = documentCategoryId;

            return { statusCode: 200, message: DS_ADDED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                },
            )
            .lean();

        if (!setting) return { statusCode: 404, message: 'Document configuration not exists' };
        const settingUpdate = await settingsModel.findByIdAndUpdate(
            {
                _id: settingId,
                'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments._id':
                    convertToMongoObjectId(documentCategoryId),
            },
            {
                $push: {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.$[documentCategory].document':
                        document,
                },
            },
            {
                arrayFilters: [
                    {
                        'documentCategory._id': convertToMongoObjectId(documentCategoryId),
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_ADD_FAILED };
        }

        const getSettingMailTrigger = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    _institution_id: 1,
                },
            )
            .lean();

        const categoryObject =
            getSettingMailTrigger.globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.find(
                (element) => element._id.toString() === documentCategoryId.toString(),
            );
        const documentObject = categoryObject.document.find(
            (elementEntry) =>
                elementEntry.labelName.toString() === document[0].labelName.toString(),
        );
        documentObject._category_id = documentCategoryId;

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDocuments = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { document } = body;
        const { settingId, documentCategoryId, documentId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: convertToMongoObjectId(settingId),
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments._id':
                            convertToMongoObjectId(documentCategoryId),
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                        'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                        _institution_id: 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Document not exists' };

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.$[labelId].document.$[documentId]':
                        document,
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': documentCategoryId,
                        },
                        {
                            'documentId._id': documentId,
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            document._category_id = documentCategoryId;
            DocumentMissingCheck(setting, document, tenantURL, type);

            return { statusCode: 200, message: DS_UPDATED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments._id':
                        convertToMongoObjectId(documentCategoryId),
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                    'globalConfiguration.basicDetails.emailIdConfiguration': 1,
                    _institution_id: 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Document not exists' };

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.$[labelId].document.$[documentId]':
                    document,
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': documentCategoryId,
                    },
                    {
                        'documentId._id': documentId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        document._category_id = documentCategoryId;
        DocumentMissingCheck(setting, document, tenantURL, type);

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteDocuments = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, documentCategoryId, documentId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments._id':
                            documentCategoryId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                    },
                )
                .lean();

            if (!setting) return { statusCode: 404, message: 'Document not exists' };

            const settingUpdate = await settingsModel.updateOne(
                { _id: settingId },
                {
                    $pull: {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.$[documentCategory].document':
                            { _id: convertToMongoObjectId(documentId) },
                    },
                },
                {
                    arrayFilters: [
                        {
                            'documentCategory._id': convertToMongoObjectId(documentCategoryId),
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_DELETED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments._id':
                        documentCategoryId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                },
            )
            .lean();

        if (!setting) return { statusCode: 404, message: 'Document not exists' };

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $pull: {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.$[documentCategory].document':
                        { _id: convertToMongoObjectId(documentId) },
                },
            },
            {
                arrayFilters: [
                    {
                        'documentCategory._id': convertToMongoObjectId(documentCategoryId),
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateReminderMail = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const {
            isMandatoryDocuments,
            recurring,
            recurringInterval,
            time,
            notificationMailLabel,
            notificationMailTime,
            weeks,
            type,
        } = body;
        const { settingId } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Document not exists' };
            const labelWeeks = [];
            if (weeks) {
                weeks.forEach((val) => {
                    labelWeeks.push({ labelName: val, isActive: true });
                });
            }

            const settingUpdate = await settingsModel.findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.recurring':
                            recurring,
                        'globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.isMandatoryDocuments':
                            isMandatoryDocuments,
                        'globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.recurringInterval':
                            recurringInterval,
                        'globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.time':
                            time,
                        'globalConfiguration.staffUserManagement.documentConfiguration.notificationMail.label':
                            notificationMailLabel,
                        'globalConfiguration.staffUserManagement.documentConfiguration.notificationMail.time':
                            notificationMailTime,
                        'globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.weeks':
                            labelWeeks,
                    },
                },
                { new: true },
            );
            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            return { statusCode: 200, message: DS_UPDATED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Document not exists' };
        const labelWeeks = [];
        if (weeks) {
            weeks.forEach((val) => {
                labelWeeks.push({ labelName: val, isActive: true });
            });
        }

        const settingUpdate = await settingsModel.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.recurring':
                        recurring,
                    'globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.isMandatoryDocuments':
                        isMandatoryDocuments,
                    'globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.recurringInterval':
                        recurringInterval,
                    'globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.time':
                        time,
                    'globalConfiguration.studentUserManagement.documentConfiguration.notificationMail.label':
                        notificationMailLabel,
                    'globalConfiguration.studentUserManagement.documentConfiguration.notificationMail.time':
                        notificationMailTime,
                    'globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.weeks':
                        labelWeeks,
                },
            },
            { new: true },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateMailSettings = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { recurring, recurringInterval, isActive, time, weeks, type, mailExpiration } = body;
        const { settingId, userType } = params;
        if (userType === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Mail setting not exists' };

            const setModifierMailConfig = { $set: {} };
            setModifierMailConfig.$set[
                'globalConfiguration.staffUserManagement.mailSettingsConfiguration.' +
                    type +
                    '.recurring'
            ] = recurring;
            setModifierMailConfig.$set[
                'globalConfiguration.staffUserManagement.mailSettingsConfiguration.' +
                    type +
                    '.isActive'
            ] = isActive;
            setModifierMailConfig.$set[
                'globalConfiguration.staffUserManagement.mailSettingsConfiguration.' +
                    type +
                    '.recurringInterval'
            ] = recurringInterval;
            setModifierMailConfig.$set[
                'globalConfiguration.staffUserManagement.mailSettingsConfiguration.' +
                    type +
                    '.time'
            ] = time;

            const labelWeeks = [];
            if (weeks) {
                weeks.forEach((val) => {
                    labelWeeks.push({ labelName: val, isActive: true });
                });
                setModifierMailConfig.$set[
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration.' +
                        type +
                        '.weeks'
                ] = labelWeeks;
            }

            if (mailExpiration)
                setModifierMailConfig.$set[
                    'globalConfiguration.staffUserManagement.mailSettingsConfiguration.mailExpiration'
                ] = mailExpiration;

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                setModifierMailConfig,
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.mailSettingsConfiguration': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Mail setting not exists' };

        const setModifierMailConfig = { $set: {} };
        setModifierMailConfig.$set[
            'globalConfiguration.studentUserManagement.mailSettingsConfiguration.' +
                type +
                '.recurring'
        ] = recurring;
        setModifierMailConfig.$set[
            'globalConfiguration.studentUserManagement.mailSettingsConfiguration.' +
                type +
                '.isActive'
        ] = isActive;
        setModifierMailConfig.$set[
            'globalConfiguration.studentUserManagement.mailSettingsConfiguration.' +
                type +
                '.recurringInterval'
        ] = recurringInterval;
        setModifierMailConfig.$set[
            'globalConfiguration.studentUserManagement.mailSettingsConfiguration.' + type + '.time'
        ] = time;

        const labelWeeks = [];
        if (weeks) {
            weeks.forEach((val) => {
                labelWeeks.push({ labelName: val, isActive: true });
            });
            setModifierMailConfig.$set[
                'globalConfiguration.studentUserManagement.mailSettingsConfiguration.' +
                    type +
                    '.weeks'
            ] = labelWeeks;
        }

        if (mailExpiration)
            setModifierMailConfig.$set[
                'globalConfiguration.studentUserManagement.mailSettingsConfiguration.mailExpiration'
            ] = mailExpiration;

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            setModifierMailConfig,
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDocumentContent = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    { 'globalConfiguration.staffUserManagement.documentConfiguration': 1 },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'document configuration not exists' };
            const weeks = [];
            setting.globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.weeks.forEach(
                (week) => {
                    if (week.isActive === true) {
                        weeks.push(week.labelName);
                    }
                },
            );
            setting.globalConfiguration.staffUserManagement.documentConfiguration.allowRemainderMail.weeks =
                weeks;
            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: setting.globalConfiguration.staffUserManagement.documentConfiguration,
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.studentUserManagement.documentConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'document configuration not exists' };
        const weeks = [];
        setting.globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.weeks.forEach(
            (week) => {
                if (week.isActive === true) {
                    weeks.push(week.labelName);
                }
            },
        );
        setting.globalConfiguration.studentUserManagement.documentConfiguration.allowRemainderMail.weeks =
            weeks;
        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.studentUserManagement.documentConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getMailSettingsContent = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    { 'globalConfiguration.staffUserManagement.mailSettingsConfiguration': 1 },
                )
                .lean();
            if (!setting)
                return { statusCode: 404, message: 'mail settings configuration not exists' };

            return {
                statusCode: 200,
                message: DS_DATA_RETRIEVED,
                data: setting.globalConfiguration.staffUserManagement.mailSettingsConfiguration,
            };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.studentUserManagement.mailSettingsConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'mail settings configuration not exists' };

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.studentUserManagement.mailSettingsConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const labelFieldReset = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;

        if (type === DC_STAFF) {
            const settingLabelRemove = await settingsModel.updateOne(
                { _id: settingId },
                {
                    $pull: {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration': {
                            _id: { $exists: true },
                        },
                    },
                },
            );

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration': {
                        $exists: true,
                    },
                },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration':
                            mockLabelFieldConfigurationData(ENGLISH),
                    },
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const settingLabelRemove = await settingsModel.updateOne(
            { _id: settingId },
            {
                $pull: {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration': {
                        _id: { $exists: true },
                    },
                },
            },
        );

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
                'globalConfiguration.studentUserManagement.labelFieldConfiguration': {
                    $exists: true,
                },
            },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration':
                        mockStudentLabelFieldConfigurationData(ENGLISH),
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const mailContentReset = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;

        if (type === DC_STAFF) {
            const settingLabelRemove = await settingsModel.updateOne(
                { _id: convertToMongoObjectId(settingId) },
                {
                    $pull: {
                        'globalConfiguration.staffUserManagement.mailConfiguration': {
                            _id: { $exists: true },
                        },
                    },
                },
            );

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: convertToMongoObjectId(settingId),
                    'globalConfiguration.staffUserManagement.mailConfiguration': {
                        $exists: true,
                    },
                },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.mailConfiguration':
                            mockMailConfigurationData(),
                    },
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const settingLabelRemove = await settingsModel.updateOne(
            { _id: convertToMongoObjectId(settingId) },
            {
                $pull: {
                    'globalConfiguration.studentUserManagement.mailConfiguration': {
                        _id: { $exists: true },
                    },
                },
            },
        );

        const settingUpdate = await settingsModel.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.mailConfiguration':
                        mockMailConfigurationData(),
                },
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const particularMailContentReset = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        const { labelName } = body;
        const labelBody = mockMailConfigurationData(labelName);
        if (type === DC_STAFF) {
            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: convertToMongoObjectId(settingId),
                    'globalConfiguration.staffUserManagement.mailConfiguration': {
                        $exists: true,
                    },
                },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.mailConfiguration.$[label].labelBody':
                            labelBody,
                    },
                },
                {
                    arrayFilters: [{ 'label.labelName': labelName }],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: convertToMongoObjectId(settingId),
                'globalConfiguration.staffUserManagement.mailConfiguration': {
                    $exists: true,
                },
            },
            {
                $set: {
                    'globalConfiguration.staffUserManagement.mailConfiguration.$[label].labelBody':
                        labelBody,
                },
            },
            {
                arrayFilters: [{ 'label.labelName': labelName }],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const labelVaccineFieldCheck = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, type } = params;
        const { isActive } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.basicDetails.vaccineConfiguration': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Vaccine Configuration not exists' };

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.basicDetails.vaccineLabelDetails.isActive': isActive,
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const addVaccineCategory = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { categoryName } = body;
        const { settingId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        if (
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration.length > 0
        ) {
            const isExists = setting.globalConfiguration.basicDetails.vaccineConfiguration.find(
                (element) => element.categoryName.toLowerCase() === categoryName.toLowerCase(),
            );

            if (isExists) {
                return { statusCode: 410, message: 'Category name already exists' };
            }
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $push: {
                    'globalConfiguration.basicDetails.vaccineConfiguration': {
                        categoryName,
                    },
                },
            },
        );
        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editVaccineCategory = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { categoryName } = body;
        const { settingId, vaccineId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        if (
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration.length > 0
        ) {
            const isExists = setting.globalConfiguration.basicDetails.vaccineConfiguration.find(
                (element) =>
                    element.categoryName.toLowerCase() === categoryName.toLowerCase() &&
                    element._id.toString() !== vaccineId.toString(),
            );
            if (isExists) {
                return { statusCode: 410, message: 'Category name already exists' };
            }
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $set: {
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].categoryName':
                        categoryName,
                },
            },
            {
                arrayFilters: [
                    {
                        'i._id': vaccineId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteVaccineCategory = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, vaccineId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }

        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $pull: {
                    'globalConfiguration.basicDetails.vaccineConfiguration': {
                        _id: vaccineId,
                    },
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const addVaccineDetails = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { vaccineDetails } = body;
        const { settingId, vaccineId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        if (
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration.length > 0
        ) {
            const isExists = setting.globalConfiguration.basicDetails.vaccineConfiguration.find(
                (element) => element._id.toString() === vaccineId.toString(),
            );
            if (!isExists) return { statusCode: 410, message: 'Vaccine not found' };
            const checkVaccineNumber = isExists.vaccineDetails.find(
                (element) =>
                    element.vaccineNumber.toLowerCase() ===
                    vaccineDetails.vaccineNumber.toLowerCase(),
            );
            if (checkVaccineNumber)
                return {
                    statusCode: 410,
                    message: 'vaccineNumber already exists in this category',
                };
        }
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                $push: {
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails':
                        vaccineDetails,
                },
            },
            {
                arrayFilters: [{ 'i._id': vaccineId }],
            },
        );
        if (!settingUpdate) return { statusCode: 410, message: DS_NOT_UPDATED };

        return { statusCode: 200, message: DS_ADDED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const editVaccineDetails = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { vaccineDetails } = body;
        const { settingId, vaccineId, vaccineDetailsId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        if (
            setting.globalConfiguration.basicDetails &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration &&
            setting.globalConfiguration.basicDetails.vaccineConfiguration.length > 0
        ) {
            const isExists = setting.globalConfiguration.basicDetails.vaccineConfiguration.find(
                (element) => element._id.toString() === vaccineId.toString(),
            );
            if (!isExists) return { statusCode: 410, message: 'Vaccine not found' };

            const checkVaccineNumber = isExists.vaccineDetails.find(
                (element) =>
                    element.vaccineNumber.toLowerCase() ===
                        vaccineDetails.vaccineNumber.toLowerCase() &&
                    vaccineDetailsId.toString() !== element._id.toString(),
            );
            if (checkVaccineNumber)
                return {
                    statusCode: 410,
                    message: 'vaccineNumber already exists in this category',
                };
        }

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                $set: {
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].vaccineNumber':
                        vaccineDetails.vaccineNumber,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].vaccineName':
                        vaccineDetails.vaccineName,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].vaccineType':
                        vaccineDetails.vaccineType,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].antigenName':
                        vaccineDetails.antigenName,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].companyName':
                        vaccineDetails.companyName,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].brandName':
                        vaccineDetails.brandName,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].noOfDosage':
                        vaccineDetails.noOfDosage,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].noOfBooster':
                        vaccineDetails.noOfBooster,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].dosageDetails':
                        vaccineDetails.dosageDetails,
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails.$[j].boosterDetails':
                        vaccineDetails.boosterDetails,
                },
            },
            {
                arrayFilters: [{ 'i._id': vaccineId }, { 'j._id': vaccineDetailsId }],
            },
        );
        if (!settingUpdate) return { statusCode: 410, message: DS_NOT_UPDATED };

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const deleteVaccineDetails = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, vaccineId, vaccineDetailsId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },

                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                $pull: {
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].vaccineDetails': {
                        _id: vaccineDetailsId,
                    },
                },
            },
            {
                arrayFilters: [{ 'i._id': vaccineId }],
            },
        );
        if (!settingUpdate) return { statusCode: 410, message: DS_NOT_UPDATED };
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleAllowMixedVaccination = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, vaccineId, allow } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },

                { 'globalConfiguration.basicDetails': 1 },
            )
            .lean();
        if (!setting) {
            return { statusCode: 410, message: 'Setting not found' };
        }
        const settingUpdate = await settingsModel.updateOne(
            { _id: settingId },
            {
                $set: {
                    'globalConfiguration.basicDetails.vaccineConfiguration.$[i].allowMixedVaccine':
                        allow,
                },
            },
            {
                arrayFilters: [
                    {
                        'i._id': vaccineId,
                    },
                ],
            },
        );
        if (!settingUpdate) return { statusCode: 410, message: DS_NOT_UPDATED };
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getVaccination = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.basicDetails.vaccineConfiguration': 1 },
            )
            .lean();
        if (!setting)
            return { statusCode: 404, message: 'vaccine settings configuration not exists' };

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.basicDetails.vaccineConfiguration,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const vaccineDetailsStatusCheck = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, vaccineId } = params;
        const { isMandatory, isActive } = body;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.basicDetails.vaccineConfiguration._id': vaccineId,
                },
                { 'globalConfiguration.basicDetails.vaccineConfiguration': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Vaccine details not exists' };

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.basicDetails.vaccineConfiguration.$[vaccineId].isActive':
                    isActive,
                'globalConfiguration.basicDetails.vaccineConfiguration.$[vaccineId].isMandatory':
                    isMandatory,
            },
            {
                arrayFilters: [
                    {
                        'vaccineId._id': vaccineId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getIndependentCourseInput = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const sessionDeliveryModel = getModel(
            tenantURL,
            SESSION_DELIVERY_TYPES,
            sessionDeliverySchema,
        );
        const { id } = params;
        const independentCourseInput = await settingsModel
            .findOne({ _institution_id: id }, { 'globalConfiguration.independentCourseInput': 1 })
            .lean();
        if (!independentCourseInput) {
            return { statusCode: 500, message: DS_GET_FAILED };
        }
        const sessionDeliveryType = await sessionDeliveryModel
            .find({
                _institution_id: convertToMongoObjectId(id),
                _program_id: { $exists: false },
                isDeleted: false,
            })
            .lean();
        if (sessionDeliveryType && sessionDeliveryType.length) {
            independentCourseInput.isCreditHourEditable = false;
        } else {
            independentCourseInput.isCreditHourEditable = true;
        }
        return { statusCode: 200, data: { setting: independentCourseInput } };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const phoneFieldUpdate = async ({ params = {}, body = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelId, type } = params;
        const { defaultValue, allowToChange } = body;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.basicDetails._id':
                            labelId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.labelFieldConfiguration.basicDetails': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Label details not exists' };

            const settingUpdate = await settingsModel.updateOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[languageId].basicDetails.$[labelId].defaultValue':
                        defaultValue,
                    'globalConfiguration.staffUserManagement.labelFieldConfiguration.$[languageId].basicDetails.$[labelId].allowToChange':
                        allowToChange,
                },
                {
                    arrayFilters: [
                        {
                            'labelId._id': labelId,
                        },
                        {
                            'languageId._id': { $exists: true },
                        },
                    ],
                },
            );

            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }

            return { statusCode: 200, message: DS_UPDATED };
        }
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.basicDetails._id':
                        labelId,
                },
                {
                    'globalConfiguration.studentUserManagement.labelFieldConfiguration.basicDetails': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Label details not exists' };

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[languageId].basicDetails.$[labelId].defaultValue':
                    defaultValue,
                'globalConfiguration.studentUserManagement.labelFieldConfiguration.$[languageId].basicDetails.$[labelId].allowToChange':
                    allowToChange,
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': labelId,
                    },
                    {
                        'languageId._id': { $exists: true },
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const privacyUpdate = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { labelId, settingId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.basicDetails.privacySettings._id': labelId,
                },
                { 'globalConfiguration.basicDetails.privacySettings': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Privacy not exists' };
        const checkState = setting.globalConfiguration.basicDetails.privacySettings.find(
            (designationEntry) => designationEntry._id.toString() === labelId.toString(),
        );
        const statusCheck = checkState.isActive !== true;
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.basicDetails.privacySettings.$[labelId].isActive': statusCheck,
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': labelId,
                    },
                ],
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const allowMultipleUpdate = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { labelId, settingId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments._id':
                            labelId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Document not exists' };
            const checkState =
                setting.globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.find(
                    (categoryEntry) => categoryEntry._id.toString() === labelId.toString(),
                );
            const statusCheck = checkState.allowMultipleDocuments !== true;
            const settingUpdate = await settingsModel.findByIdAndUpdate(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.staffUserManagement.documentConfiguration.chooseDocuments.$[labelId].allowMultipleDocuments':
                        statusCheck,
                },
                {
                    new: true,
                    arrayFilters: [
                        {
                            'labelId._id': convertToMongoObjectId(labelId),
                        },
                    ],
                },
            );
            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            return { statusCode: 200, message: DS_UPDATED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments._id':
                        labelId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Document not exists' };
        const checkState =
            setting.globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.find(
                (categoryEntry) => categoryEntry._id.toString() === labelId.toString(),
            );
        const statusCheck = checkState.allowMultipleDocuments !== true;
        const settingUpdate = await settingsModel.findByIdAndUpdate(
            {
                _id: settingId,
            },
            {
                'globalConfiguration.studentUserManagement.documentConfiguration.chooseDocuments.$[labelId].allowMultipleDocuments':
                    statusCheck,
            },
            {
                new: true,
                arrayFilters: [
                    {
                        'labelId._id': convertToMongoObjectId(labelId),
                    },
                ],
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const existingPrivacyFieldUpdate = async ({ headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const settingUpdate = await settingsModel.updateMany(
            {
                'globalConfiguration.basicDetails.privacySettings': {
                    $exists: false,
                },
            },
            {
                $set: {
                    'globalConfiguration.basicDetails.privacySettings': privacySettings(),
                },
            },
        );

        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }

        return { statusCode: 204, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const updateNotificationMail = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { labelBody } = body;
        const { settingId, type } = params;
        if (type === DC_STAFF) {
            const setting = await settingsModel
                .findOne(
                    {
                        _id: settingId,
                    },
                    {
                        'globalConfiguration.staffUserManagement.documentConfiguration': 1,
                    },
                )
                .lean();
            if (!setting) return { statusCode: 404, message: 'Document not exists' };

            const settingUpdate = await settingsModel.findByIdAndUpdate(
                {
                    _id: convertToMongoObjectId(settingId),
                },
                {
                    $set: {
                        'globalConfiguration.staffUserManagement.documentConfiguration.notificationMail':
                            { labelBody },
                    },
                },
                { new: true },
            );
            if (!settingUpdate) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            return { statusCode: 200, message: DS_UPDATED };
        }

        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.studentUserManagement.documentConfiguration': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Document not exists' };

        const settingUpdate = await settingsModel.findByIdAndUpdate(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.studentUserManagement.documentConfiguration.notificationMail':
                        { labelBody },
                },
            },
            { new: true },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDepartmentSubjectMode = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.departmentSubject': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Department subject not exists' };

        const disableBeforeUpdate = await settingsModel.updateMany(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.departmentSubject.$[labelId].isActive': false,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': { $exists: true },
                    },
                ],
            },
        );

        const settingUpdate = await settingsModel.updateOne(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.departmentSubject.$[labelId].isActive': true,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': convertToMongoObjectId(labelId),
                    },
                ],
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const getDepartmentSubject = async ({ params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                { 'globalConfiguration.departmentSubject': 1 },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'departmentSubject not exists' };

        return {
            statusCode: 200,
            message: DS_DATA_RETRIEVED,
            data: setting.globalConfiguration.departmentSubject,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const updateDepartmentHierarchyMode = async ({ body = {}, params = {}, headers = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelId } = params;
        const setting = await settingsModel
            .findOne(
                {
                    _id: settingId,
                },
                {
                    'globalConfiguration.basicDetails.departmentHierarchyStructure': 1,
                },
            )
            .lean();
        if (!setting) return { statusCode: 404, message: 'Department hierarchy not exists' };

        const disableBeforeUpdate = await settingsModel.updateMany(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.basicDetails.departmentHierarchyStructure.$[labelId].isActive': false,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': { $exists: true },
                    },
                ],
            },
        );
        const settingUpdate = await settingsModel.updateOne(
            {
                _id: convertToMongoObjectId(settingId),
            },
            {
                $set: {
                    'globalConfiguration.basicDetails.departmentHierarchyStructure.$[labelId].isActive': true,
                },
            },
            {
                arrayFilters: [
                    {
                        'labelId._id': convertToMongoObjectId(labelId),
                    },
                ],
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const addDepartmentType = async ({ headers = {}, body = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { labelName, mode, _id } = body;
        const { settingId } = params;

        if (mode === DS_CREATE) {
            const chkSettings = await settingsModel.findOne({
                _id: convertToMongoObjectId(settingId),
                'globalConfiguration.departmentManagement.departmentTypes.labelName': {
                    $in: [labelName],
                },
            });

            if (chkSettings) {
                return { statusCode: 400, message: 'DEPARTMENT_TYPE_ALREADY_EXISTS' };
            }
            const addDocumentType = await settingsModel.updateOne(
                { _id: convertToMongoObjectId(settingId) },
                {
                    $push: {
                        'globalConfiguration.departmentManagement.departmentTypes': {
                            labelName,
                        },
                    },
                },
            );
            if (!addDocumentType) {
                return { statusCode: 410, message: DS_SAVE_FAILED };
            }
            return { statusCode: 200, message: DS_SAVED };
        }
        if (mode === DS_UPDATE) {
            const chkSettings = await settingsModel.findOne(
                {
                    _id: convertToMongoObjectId(settingId),
                    'globalConfiguration.departmentManagement.departmentTypes.labelName': labelName,
                },
                {
                    'globalConfiguration.departmentManagement.departmentTypes': 1,
                },
            );

            if (chkSettings) {
                const checkValues =
                    chkSettings.globalConfiguration.departmentManagement.departmentTypes.find(
                        (element) =>
                            element.labelName === labelName &&
                            element._id.toString() !== _id.toString(),
                    );

                if (checkValues)
                    return { statusCode: 400, message: 'DEPARTMENT_TYPE_ALREADY_EXISTS' };
            }
            const updateDepartmentType = await settingsModel.updateOne(
                { _id: convertToMongoObjectId(settingId) },
                {
                    'globalConfiguration.departmentManagement.departmentTypes.$[i].labelName':
                        labelName,
                },
                { arrayFilters: [{ 'i._id': convertToMongoObjectId(_id) }] },
            );
            if (!updateDepartmentType) {
                return { statusCode: 410, message: DS_UPDATE_FAILED };
            }
            return { statusCode: 200, message: DS_UPDATED };
        }
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const deleteDepartmentType = async ({ headers = {}, params = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, labelId } = params;
        const settingUpdate = await settingsModel.updateOne(
            { _id: convertToMongoObjectId(settingId) },
            {
                $pull: {
                    'globalConfiguration.departmentManagement.departmentTypes': {
                        _id: convertToMongoObjectId(labelId),
                    },
                },
            },
        );
        if (!settingUpdate) {
            return { statusCode: 410, message: DS_UPDATE_FAILED };
        }
        return { statusCode: 200, message: DS_DELETED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};
const getDepartmentType = async ({ headers = {} }) => {
    try {
        const { tenantURL, _institution_id } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const getSettings = await settingsModel.findOne(
            { _institution_id: convertToMongoObjectId(_institution_id) },
            {
                'globalConfiguration.departmentManagement.departmentTypes': 1,
            },
        );
        if (!getSettings.globalConfiguration.departmentManagement.departmentTypes) {
            return { statusCode: 404, message: DS_NOT_FOUND };
        }
        return {
            statusCode: 200,
            data: getSettings.globalConfiguration.departmentManagement.departmentTypes,
        };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

const toggleWithWithoutCredit = async ({ params = {}, headers = {}, body = {} }) => {
    try {
        const { tenantURL } = headers;
        const settingsModel = getModel(tenantURL, SETTINGS, settingSchema);
        const { settingId, value } = body;
        const { id } = params;
        const creditHours = await settingsModel.updateOne(
            { _id: settingId, 'globalConfiguration.programInput.creditHours._id': id },
            { $set: { 'globalConfiguration.programInput.creditHours.$.withoutCreditHour': value } },
        );
        if (!creditHours) return { statusCode: 410, message: DS_UPDATE_FAILED };
        return { statusCode: 200, message: DS_UPDATED };
    } catch (error) {
        if (error instanceof Error) {
            throw error;
        } else {
            throw new Error(error);
        }
    }
};

module.exports = {
    getGlobalConfiguration,
    updateGlobalConfiguration,
    updateWorkingDays,
    activateOrDeactivateWorkingDays,
    addBreaks,
    updateBreaks,
    removeBreaks,
    addEventType,
    updateEventType,
    addLanguage,
    updateLanguage,
    updateDefaultInLanguageSetting,
    getProgramInputs,
    resetLabelConfiguration,
    updateLabelConfiguration,
    generateLabelConfiguration,
    createProgramType,
    removeProgramType,
    updateProgramType,
    updateCurriculumNaming,
    updateCreditHours,
    updateIndependentCourseCreditHours,
    addEmailIdConfiguration,
    removeEmailIdConfiguration,
    updateEmailIdConfiguration,
    getEmailIdConfiguration,
    staffManagementDashboard,
    addDesignation,
    designationCheck,
    designationUpdate,
    designationDelete,
    getDesignationLists,
    getLabelFieldLists,
    labelFieldUpdate,
    labelFieldCheck,
    existingLabelFieldUpdate,
    biometricCheck,
    getBiometricList,
    updateMailContent,
    getMailContent,
    updateDocumentFormatAndSize,
    addDocumentsCategory,
    updateDocumentsCategory,
    deleteDocumentsCategory,
    updateDocuments,
    addDocuments,
    deleteDocuments,
    updateReminderMail,
    updateMailSettings,
    getDocumentContent,
    getMailSettingsContent,
    labelFieldReset,
    mailContentReset,
    particularMailContentReset,
    labelVaccineFieldCheck,
    addVaccineCategory,
    editVaccineCategory,
    deleteVaccineCategory,
    addVaccineDetails,
    editVaccineDetails,
    deleteVaccineDetails,
    getVaccination,
    vaccineDetailsStatusCheck,
    getIndependentCourseInput,
    toggleAllowMixedVaccination,
    phoneFieldUpdate,
    privacyUpdate,
    allowMultipleUpdate,
    sendTestMail,
    existingPrivacyFieldUpdate,
    updateNotificationMail,
    updateDepartmentSubjectMode,
    getDepartmentSubject,
    toggleWithWithoutCredit,
    updateProgramDurationFormat,
    updateDepartmentHierarchyMode,
    addDepartmentType,
    deleteDepartmentType,
    getDepartmentType,
};
