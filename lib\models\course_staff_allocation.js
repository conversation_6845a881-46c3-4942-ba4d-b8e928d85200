const mongoose = require("mongoose");
const { Schema } = mongoose;
let constant = require('../utility/constants');

const schema = new Schema(
  {
    _institution_id: {
      type: Schema.Types.ObjectId,
      ref: constant.INSTITUTION
    },
    program: {
      _program_id: {
        type: Schema.Types.ObjectId,
        ref: constant.PROGRAM
      },
      program_name: String
    },
    year: {
      type: String,
      required: true
    },
    level: {
      type: String,
      required: true
    },
    department: {
      _department_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_DIVISIONS
      },
      department_name: String
    },
    subject: {
      _subject_id: {
        type: Schema.Types.ObjectId,
        ref: constant.DEPARTMENT_SUBJECT
      },
      subject_name: String
    },
    course: {
      _course_id: {
        type: Schema.Types.ObjectId,
        ref: constant.COURSE
      },
      course_name: String
    },
    course_administrator_regular: {
      _staff_id: {
        type: Schema.Types.ObjectId,
        ref: constant.USER
      },
      name: {
        first: {
          type: String,
          trim: true,
        },
        middle: {
          type: String,
          trim: true,
        },
        last: {
          type: String,
          trim: true,
        },
        family: {
          type: String,
          trim: true,
        },
      },
    },
    course_administrator_interim: {
      _staff_id: {
        type: Schema.Types.ObjectId,
        ref: constant.USER
      },
      name: {
        first: {
          type: String,
          trim: true,
        },
        middle: {
          type: String,
          trim: true,
        },
        last: {
          type: String,
          trim: true,
        },
        family: {
          type: String,
          trim: true,
        },
      },
    },
    assigned_by: {
      _staff_id: {
        type: Schema.Types.ObjectId,
        ref: constant.USER
      },
      name: {
        first: {
          type: String,
          trim: true,
        },
        middle: {
          type: String,
          trim: true,
        },
        last: {
          type: String,
          trim: true,
        },
        family: {
          type: String,
          trim: true,
        },
      },
    },
    isDeleted: {
      type: Boolean,
      default: false,
    },

    isActive: {
      type: Boolean,
      default: true,
    },
  },
  { timestamps: true }
);
module.exports = mongoose.model(constant.COURSE_STAFF_ALLOCATION, schema);
