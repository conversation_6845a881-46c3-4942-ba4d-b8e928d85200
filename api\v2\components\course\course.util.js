const multer = require('multer');
const { uploadFile } = require('../../utility/file-upload');
const keys = require('../../utility/util_keys');
const { getS3SignedUrl } = require('../../services/aws.service');
const fileUpload = uploadFile.fields([
    {
        name: 'file',
        maxCount: 1,
    },
]);

const uploadcourseFile = (req, res, next) => {
    fileUpload(req, res, (err) => {
        if (err instanceof multer.MulterError) {
            return res.status(500).send(err || err.message);
        }
        if (err) {
            return res
                .status(500)
                .send(
                    response_function(
                        res,
                        500,
                        false,
                        'Something went wrong.Please change file format and upload',
                        err.toString(),
                    ),
                );
        }
        next();
    });
};

const getsignedUrl = async (url) => {
    const fileName = url.split('/').pop();
    const signedUrl = await getS3SignedUrl({
        bucket: keys.AWS_SECRET_BUCKET_NAME_V2,
        key: `courses/` + fileName,
    });
    return signedUrl;
};

module.exports = {
    uploadcourseFile,
    getsignedUrl,
};
