const { Joi } = require('../../common/middlewares/validation');
const { objectIdSchema, objectIdRQSchema } = require('../../../lib/utility/validationSchemas');

const createVariableSchema = Joi.object({
    query: Joi.object({
        programId: objectIdSchema.optional(),
        courseId: objectIdSchema.optional(),
        institutionCalendarId: objectIdSchema.optional(),
    }),
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().required(),
    }),
}).unknown(true);

const getVariableSchema = Joi.object({
    query: Joi.object({
        programId: objectIdSchema.optional(),
        courseId: objectIdSchema.optional(),
        institutionCalendarId: objectIdSchema.optional(),
        type: Joi.string().optional(),
    }),
}).unknown(true);

const updateVariableSchema = Joi.object({
    query: Joi.object({
        variableId: objectIdRQSchema,
    }),
    body: Joi.object({
        name: Joi.string().required(),
        type: Joi.string().required(),
    }),
}).unknown(true);

const deleteVariableSchema = Joi.object({
    query: Joi.object({
        variableId: objectIdRQSchema,
    }),
}).unknown(true);

module.exports = {
    createVariableSchema,
    getVariableSchema,
    updateVariableSchema,
    deleteVariableSchema,
};
