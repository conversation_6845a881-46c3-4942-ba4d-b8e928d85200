const express = require('express');
const route = express.Router();
const infrastructure = require('./infrastructure_controller');
const validator = require('./infrastructure_validator');
const { userPolicyAuthentication } = require('../../middleware/policy.middleware');

route.get(
    '/',
    [userPolicyAuthentication(['infrastructure_management:onsite:view'])],
    infrastructure.get,
);
route.get(
    '/single/:_id',
    [userPolicyAuthentication([])],
    validator.infrastructure_id,
    infrastructure.get_id,
);
route.post(
    '/',
    [
        userPolicyAuthentication([
            'infrastructure_management:onsite:settings:buildings_and_hospitals:add',
        ]),
    ],
    validator.infrastructure,
    infrastructure.insert,
);
route.put(
    '/edit_infrastructure/:_id',
    [
        userPolicyAuthentication([
            'infrastructure_management:onsite:settings:buildings_and_hospitals:edit',
        ]),
    ],
    validator.infrastructure_id,
    validator.infrastructure,
    infrastructure.update,
);
route.delete(
    '/delete_infrastructure/:_id',
    [
        userPolicyAuthentication([
            'infrastructure_management:onsite:settings:buildings_and_hospitals:delete',
        ]),
    ],
    validator.infrastructure_id,
    infrastructure.delete,
);
route.get(
    '/get_location',
    [
        userPolicyAuthentication([
            'infrastructure_management:onsite:settings:view',
            'infrastructure_management:onsite:settings:buildings_and_hospitals:view',
        ]),
    ],
    infrastructure.get_location,
);
// route.get('/get_infrastructure_dashboard', infrastructure.get_dashboard);

module.exports = route;
