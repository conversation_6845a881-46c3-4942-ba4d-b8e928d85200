const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    DIGI_SURVEY,
    COMPLETED,
    USER,
    DIGI_COURSE,
    INSTITUTION_CALENDAR,
    DS_PROGRAM_KEY,
    DS_COURSE_KEY,
    DS_INSTITUTION_KEY,
    DIGI_PROGRAM,
    DIGI_SURVEY_BANK,
    UPCOMING,
    INPROGRESS,
    CLOSED,
    DRAFT,
} = require('../utility/constants');
const digiSurveySchemas = new Schema(
    {
        _institution_id: {
            type: ObjectId,
        },
        institutionCalendarId: {
            type: ObjectId,
            ref: INSTITUTION_CALENDAR,
        },
        programId: {
            type: ObjectId,
            ref: DIGI_PROGRAM,
        },
        term: { type: String },
        curriculumName: { type: String },
        yearNo: {
            type: String,
        },
        levelNo: {
            type: String,
        },
        courseId: {
            type: ObjectId,
            ref: DIGI_COURSE,
        },
        createdBy: {
            type: ObjectId,
            ref: USER,
        },
        surveyBankId: { type: ObjectId, ref: DIGI_SURVEY_BANK },
        isTemplate: { type: Boolean, default: false }, //if false its personal survey else its template survey
        surveyType: { type: String },
        surveyName: { type: String },
        surveyDescription: { type: String },
        surveyVersionName: { type: String },
        surveyLevel: { type: String, enum: [DS_PROGRAM_KEY, DS_COURSE_KEY, DS_INSTITUTION_KEY] },
        noOfQuestion: { type: Number },
        learningOutcome: { type: Boolean },
        status: {
            type: String,
            default: UPCOMING,
            enum: [COMPLETED, UPCOMING, INPROGRESS, CLOSED, DRAFT],
        },
        closedSurveyReason: {
            reason: { type: String, default: '' },
            removeFromAllParticipants: { type: Boolean, default: false },
        },
        publishDate: { type: Date },
        expiryDate: { type: Date },
        questions: { type: Schema.Types.Mixed },
        isMandator: { type: Boolean, default: false },
        surveyUsers: [{ type: ObjectId, ref: USER }],
        outComeDetails: {
            //outcome based questions
            prefixSentence: { type: String },
            frameWorkDetails: [
                {
                    outcomeId: { type: ObjectId },
                    outcomeName: { type: String },
                    outcomeNo: { type: String },
                    domainId: { type: String },
                    frameWorkId: { type: String },
                    curriculumName: { type: String },
                },
            ],
        },
        mappedOutcomeDetails: [
            // normal question tagged with outcomes
            {
                frameWorkId: { type: String },
                curriculumName: { type: String },
                outcomeId: { type: ObjectId },
                domainId: { type: String },
                outcomeNo: { type: String },
                questionNo: { type: String },
                questionId: { type: String },
                isOutcomeQuestion: { type: Boolean },
            },
        ],
        mappedTagDetails: [
            //questions tagged with TAGS
            {
                questionNo: { type: String },
                tagId: { type: ObjectId },
                code: { type: String },
                name: { type: String },
                questionId: { type: String },
                isOutcomeQuestion: { type: Boolean },
            },
        ],
        userResponseSettings: {
            profilePhoto: {
                type: Boolean,
                default: false,
            },
            name: {
                type: Boolean,
                default: false,
            },
            email: {
                type: Boolean,
                default: false,
            },
            employeeId: {
                type: Boolean,
                default: false,
            },
        },
        surveyPublicUserDetails: {
            isPublicUserAllowed: { type: Boolean, default: false },
            maxResponse: { type: Number, default: 0 },
            url: { type: String },
            publicUserResponseSettings: {
                sendResponseCopy: {
                    type: Boolean,
                    default: false,
                },
                collectPersonalDetails: {
                    type: Boolean,
                    default: false,
                },
            },
        },
        // keys needed for FE to handle multi check in EDIT flow
        existingSelectedCourseId: { type: String },
        selectedParticipants: [
            {
                name: { type: String },
                type: { type: String },
                user_id: { type: String },
                userType: { type: String },
                gender: { type: String },
                participantId: { type: String },
                studentId: { type: String },
                constructedStudentDetails: [{ type: String }],
            },
        ],
        checkedParticipants: { type: Schema.Types.Mixed },
        //multi program and course handling keys
        multiSelectedProgramIds: [
            {
                programId: {
                    type: ObjectId,
                    ref: DIGI_PROGRAM,
                },
                term: { type: String },
                yearNo: {
                    type: String,
                },
                levelNo: {
                    type: String,
                },
                courseId: {
                    type: ObjectId,
                    ref: DIGI_COURSE,
                },
                rotationCount: { type: Number },
                studentIds: [
                    {
                        studentId: { type: ObjectId, ref: USER },
                        userType: { type: String },
                        studentGroup: { type: String },
                    },
                ],
            },
        ],
        isDeleted: {
            type: Boolean,
            default: false,
        },
        isActive: {
            type: Boolean,
            default: true,
        },
    },
    { timestamps: true },
);

module.exports = model(DIGI_SURVEY, digiSurveySchemas);
