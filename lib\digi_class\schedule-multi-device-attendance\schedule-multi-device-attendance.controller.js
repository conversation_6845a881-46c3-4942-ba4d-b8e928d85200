const ScheduleMultiDeviceAttendanceSchema = require('../../models/schedule_multi-device_attendance');
const courseScheduleSchema = require('../../models/course_schedule');
const userSchema = require('../../models/user');
const { convertToMongoObjectId, clone } = require('../../utility/common');
const { sendResponseWithRequest } = require('../../utility/common');
const {
    // RETAKE_ALL,
    // RETAKE_ABSENT,
    // BUZZER,
    // SURPRISE_QUIZ,
    COMPLETED,
    RUNNING,
    PRESENT,
    ABSENT,
    ONGOING,
    PRIMARY,
    PENDING,
    DC_STAFF,
} = require('../../utility/constants');
// const push = require('../../utility/notification_push');
const { timestampNow, getSignedURL } = require('../../utility/common_functions');
const { logger } = require('../../utility/util_keys');
const { v4: uuidv4 } = require('uuid');
const { generateAuthTokens } = require('../../utility/token.util');

exports.scheduleStart = async (req, res) => {
    try {
        const { _staff_id, _id, mode, secretCode } = req.body;
        const { _institution_id } = req.headers;
        const requestDatas = {
            scheduleId: _id,
            staffId: _staff_id,
            mode,
            secretCode,
        };
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleStart -> start');
        const scheduleQuery = {
            _id: convertToMongoObjectId(_id),
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleProject = {
            _id: 1,
            status: 1,
            'sessionDetail.attendance_mode': 1,
            'session.delivery_symbol': 1,
            'session.delivery_no': 1,
            'staffs._staff_id': 1,
            schedule_date: 1,
            socket_port: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .findOne(scheduleQuery, scheduleProject)
            .lean();
        console.timeEnd('courseScheduleData');
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        if (courseScheduleData.status === ONGOING && mode !== 'join')
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ALREADY_YOU_HAVE_RUNNING_SESSION'),
                null,
            );

        // let courseSchedule = courseScheduleResult.data;
        // // if (!isExpired(courseSchedule.schedule_date, courseSchedule.start))
        // //     return sendResponse(res, 200, false, 'start session on correct scheduled time', null);
        // const studentWithSchedule = [];
        // const scheduleId = courseSchedule._id;
        // const sessionId = courseSchedule.session._session_id;
        // if (courseSchedule.merge_status) {
        //     const scheduleIds = courseSchedule.merge_with.map((mw) =>
        //         convertToMongoObjectId(mw.schedule_id),
        //     );
        //     scheduleIds.push(convertToMongoObjectId(req.body._id));
        //     query._id = { $in: scheduleIds };
        // }

        const sessionDetail = courseScheduleData.sessionDetail;
        if (sessionDetail && mode !== 'join') {
            if (sessionDetail.attendance_mode === COMPLETED)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('ATTENDANCE_ALREADY_CLOSED'),
                    null,
                );
            if (sessionDetail.attendance_mode === ONGOING)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('ATTENDANCE_ALREADY_STARTED'),
                    null,
                );
        }
        const getSocketEventName = () => {
            const { _id } = courseScheduleData;
            if (courseScheduleData.session && courseScheduleData.session.delivery_symbol) {
                const { delivery_symbol, delivery_no } = courseScheduleData.session;
                return `${_id}-${delivery_symbol}-${delivery_no}`;
            }
            const delivery_symbol = 's';
            const delivery_no = '1';
            return `${_id}-${delivery_symbol}-${delivery_no}`;
        };
        // const otherSchedules = (await getJSON(courseScheduleSchema, query, {})).data;
        // const getStaffIds = () => {
        //     const mergedUserIds = [];
        //     otherSchedules.forEach((otherSchedule) => {
        //         const { students, _id, session } = otherSchedule;
        //         let sessionId;
        //         if (session) {
        //             sessionId = session._session_id;
        //         }
        //         students.forEach((student) => {
        //             if (!studentWithSchedule.find((sWS) => cs(sWS.userId) === cs(student._id)))
        //                 studentWithSchedule.push({
        //                     userId: student._id,
        //                     scheduleId: _id,
        //                     sessionId,
        //                     otherSchedule,
        //                 });
        //         });
        //         const studentIds = otherSchedule.students.map((student) => student._id);
        //         const staffIds = otherSchedule.staffs.map((staff) => staff._staff_id);
        //         mergedUserIds.push(studentIds.concat(staffIds));
        //     });
        //     // eslint-disable-next-line prefer-spread
        //     const users = [].concat.apply([], mergedUserIds);
        //     return [...new Set(users)];
        // };
        /*  if (!courseSchedule.zoomUuid && courseSchedule.mode === REMOTE) {
            await zoomMeetingUpdate(
                req.body._staff_id,
                courseSchedule._id,
                courseSchedule.start,
                courseSchedule.end,
            );
        } */
        // const staffDetails = await user.findOne({ _id: req.body._staff_id }, {});
        // const scheduleStudents = courseSchedule.students;
        if (mode !== 'join') {
            const socketEventName = getSocketEventName();
            // // courseSchedule.students = courseSchedule.students || [];
            // // let ids = courseScheduleData.staffs.map((scheduleElement) => scheduleElement._staff_id);
            // // ids = ids.map((id) => id.toString());
            // // ids = [...new Set(ids)];
            // // ids = ids.map((id) => convertToMongoObjectId(id));
            // const userResult = await base_control.get_list(
            //     userSchema,
            //     {
            //         _id: {
            //             $in: courseScheduleData.staffs.map(
            //                 (scheduleElement) => scheduleElement._staff_id,
            //             ),
            //         },
            //     },
            //     {
            //         name: 1,
            //         user_type: 1,
            //         user_id: 1,
            //         mobile: 1,
            //         biometric_data: 1,
            //         fcm_token: 1,
            //         web_fcm_token: 1,
            //         device_type: 1,
            //         socketEventId: 1,
            //     },
            // );
            // if (!userResult.status)
            //     return sendResponse(res, 200, false, req.t('STUDENTS_NOT_FOUND'), null);
            // const student_data = [];
            // const staffs = [];
            // const tokens = [];
            const session_uuid = uuidv4();
            // const socketIds = [];
            // userResult.data.forEach((element) => {
            //     let socketEventIds = element.socketEventId;
            //     if (socketEventIds) {
            //         socketEventIds = Object.values(socketEventIds);
            //         socketIds.push(socketEventIds);
            //     }
            //     if (element.user_type === 'student') {
            //         const studentData = scheduleStudents.find(
            //             (ele) => ele && ele._id.toString() === element._id.toString(),
            //         );
            //         student_data.push({
            //             _student_id: element._id,
            //             user_id: element.user_id,
            //             student_name: element.name,
            //             status: studentData ? studentData.status : 'pending',
            //         });
            //         const mergedSchedule = studentWithSchedule.find(
            //             (sWS) => cs(sWS.userId) === cs(element._id),
            //         );
            //         const groups = mergedSchedule.student_groups;
            //         let StudentGroup = '';
            //         if (groups) {
            //             groups.forEach((group) => {
            //                 if (group.students && group.students.length) {
            //                     const student = group.students.find(
            //                         (student) =>
            //                             student._student_id.toString() === element._id.toString(),
            //                     );
            //                     if (student) StudentGroup = group.group_name.slice(id.length - 5);
            //                 }
            //             });
            //         }
            //         tokens.push({
            //             device_type: element.device_type,
            //             token: element.fcm_token,
            //             web_fcm_token: element.web_fcm_token,
            //             _user_id: element._id,
            //             scheduleId: mergedSchedule ? mergedSchedule.scheduleId : scheduleId,
            //             sessionId: mergedSchedule ? mergedSchedule.sessionId : sessionId,
            //             schedule: mergedSchedule ? mergedSchedule.otherSchedule : undefined,
            //             StudentGroup,
            //         });
            //     } else {
            //         if (element._id.toString() === req.body._staff_id.toString()) {
            //             staffs.push({
            //                 _staff_id: element._id,
            //                 user_id: element.user_id,
            //                 staff_name: element.name,
            //                 status: 'present',
            //                 mode: 'auto',
            //                 time: timestampNow(),
            //             });
            //         } else {
            //             staffs.push({
            //                 _staff_id: element._id,
            //                 user_id: element.user_id,
            //                 staff_name: element.name,
            //                 status: 'pending',
            //             });
            //         }
            //     }
            // });
            const scheduleUpdateObject = {
                $set: {
                    // isLive: isLive || false,
                    status: ONGOING,
                    socket_port: socketEventName,
                    uuid: session_uuid,
                    'sessionDetail.attendance_mode': ONGOING,
                    'sessionDetail.retake': false,
                    'sessionDetail.start_time': timestampNow(),
                    'sessionDetail.startBy': convertToMongoObjectId(_staff_id),
                    'staffs.$[staffIndex].status': PRESENT,
                    'staffs.$[staffIndex].mode': 'auto',
                    'staffs.$[staffIndex].time': timestampNow(),
                },
            };
            const scheduleArrayFilter = {
                arrayFilters: [{ 'staffIndex._staff_id': _staff_id }],
            };
            const scheduleUpdate = await courseScheduleSchema.updateMany(
                {
                    $or: [
                        { _id: convertToMongoObjectId(_id) },
                        { 'merge_with.schedule_id': convertToMongoObjectId(_id) },
                    ],
                },
                scheduleUpdateObject,
                scheduleArrayFilter,
            );
            if (!scheduleUpdate)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_START_SESSION'),
                    null,
                );
            // Get Post to open in socket
            // const doc = await updateMany(courseScheduleSchema, query, objs);
            // await updateMany(
            //     courseScheduleSchema,
            //     {
            //         _id: query._id,
            //         'staffs._staff_id': convertToMongoObjectId(_staff_id),
            //     },
            //     { $set: { 'staffs.$.status': PRESENT } },
            // );
            // if (!doc.status)
            //     return sendResponse(res, 200, false, req.t('UNABLE_TO_START_SESSION'), null);
            // const students = student_data;
            // let present_count = 0;
            // let absent_count = 0;
            // let leave_count = 0;
            // students.forEach((element) => {
            //     if (element.status === 'present') present_count++;
            //     else if (element.status === 'absent') absent_count++;
            //     else if (
            //         element.status === LEAVE ||
            //         element.status === PERMISSION ||
            //         element.status === ONDUTY
            //     )
            //         leave_count++;
            // });

            // courseSchedule = (await getSchedule(req)).data;

            // switch (courseSchedule.remotePlatform) {
            //     case ZOOM:
            //         if (courseSchedule.mode === REMOTE && !courseSchedule.zoomDetail.zoomStartUrl) {
            //             const generateUrl = await createMeeting(courseSchedule._id);
            //             if (generateUrl.status && generateUrl.status === 1) {
            //                 courseSchedule.zoomDetail = { zoomStartUrl: generateUrl.start_url };
            //             }
            //         }
            //         break;
            //     case TEAMS:
            //         if (
            //             courseSchedule.mode === REMOTE &&
            //             !courseSchedule.teamsDetail.teamStartUrl
            //         ) {
            //             const generateUrl = await createTeamsMeeting(courseSchedule._id, _staff_id);
            //             if (generateUrl.status && generateUrl.status === 1) {
            //                 courseSchedule.teamsDetail = { teamsStartUrl: generateUrl.start_url };
            //             }
            //         }
            //         break;
            //     default:
            // }
            // const response = {
            //     _id: courseSchedule._id,
            //     status: courseSchedule.status,
            //     attendance_status: courseSchedule.sessionDetail
            //         ? courseSchedule.sessionDetail.attendance_mode
            //         : PENDING,
            //     count: students.length,
            //     present_count,
            //     absent_count,
            //     leave_count,
            //     students,
            //     staffs,
            //     session_uuid,
            //     remotePlatform: courseSchedule.remotePlatform,
            //     zoomDetail: {
            //         zoomStartUrl:
            //             courseSchedule.zoomDetail && courseSchedule.zoomDetail.zoomStartUrl
            //                 ? courseSchedule.zoomDetail.zoomStartUrl
            //                 : '',
            //     },
            //     teamsDetail: {
            //         teamsStartUrl:
            //             courseSchedule.teamsDetail && courseSchedule.teamsDetail.teamsStartUrl
            //                 ? courseSchedule.teamsDetail.teamsStartUrl
            //                 : '',
            //     },
            // };
            // if (tokens.length) {
            //     const { title, message } = makeTitleAndMessage(courseSchedule);
            //     const data = {
            //         _id: courseSchedule._id,
            //         sessionId: courseSchedule.session._session_id,
            //         courseId: courseSchedule._course_id,
            //         programId: courseSchedule._program_id,
            //         rotation: courseSchedule.rotation,
            //         rotation_count: courseSchedule.rotation_count,
            //         institutionCalendarId: courseSchedule._institution_calendar_id,
            //         yearNo: courseSchedule.year_no,
            //         levelNo: courseSchedule.level_no,
            //         term: courseSchedule.term,
            //         mergeStatus: courseSchedule.merge_status,
            //         mergeType: courseSchedule.type,
            //         clickAction: 'session_start',
            //         notificationType: 'session',
            //     };
            //     // fcm.firebase_push_notification(tokens, title, message, data)
            //     await push.notification_push(tokens, title, message, data);
            // }

            // if (tokens.length) {
            //     let userIds = tokens.map((token) => token._user_id);
            //     userIds = [...new Set(userIds)];
            //     const sendSocketData = [];
            //     const data = JSON.stringify({ notificationCount: 1, sessions: courseSchedule });
            //     for (const token of tokens) {
            //         const eventId = token._user_id;
            //         sendSocketData.push({ eventId, data });
            //     }
            //     if (sendSocketData && sendSocketData.length) axiosCall(sendSocketData);
            // }
            // //Socket with Event Mode Push
            // response.socket_port = socketEventName;
            logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleStart -> end');

            // Adding Record To Schedule Multi Device Attendances
            console.log(
                await ScheduleMultiDeviceAttendanceSchema.create({
                    _institution_id: convertToMongoObjectId(_institution_id),
                    scheduleId: convertToMongoObjectId(_id),
                    _staff_id: convertToMongoObjectId(_staff_id),
                    modeBy: PRIMARY,
                    status: RUNNING,
                    scheduleSecurityCode: secretCode,
                    scheduleDate: courseScheduleData.schedule_date,
                }),
            );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_ATTENDANCE_STARTED'),
                {
                    socket_port: socketEventName,
                },
            );
        }
        // const session_time = moment(timestampNow());
        // const start_time = moment(courseSchedule.session.start_time);
        // const session_times = moment(session_time.format('HH:mm:ss a'), 'HH:mm:ss a');
        // const start_times = moment(start_time.format('HH:mm:ss a'), 'HH:mm:ss a');
        // const duration = moment.duration(session_times.diff(start_times));
        // const min = parseInt(duration.asMinutes()) % 60;
        // const hours = parseInt(duration.asHours());
        // let late = false;
        // if (hours !== 0 || min > 10) late = true;
        if (sessionDetail.attendance_mode === 'pending')
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_STARTED'),
                null,
            );
        // const students = courseSchedule.students;
        // const staffs = courseSchedule.staffs;
        // const staffLoc = staffs.findIndex((i) => cs(i._staff_id) === cs(_staff_id));
        // staffs[staffLoc].status = 'present';
        // staffs[staffLoc].mode = 'auto';
        // staffs[staffLoc].time = timestampNow();
        // const objs = {
        //     $set: { staffs },
        // };
        // await updateMany(CourseSchedule, query, objs);
        const scheduleUpdateObject = {
            $set: {
                'staffs.$[staffIndex].status': PRESENT,
                'staffs.$[staffIndex].mode': 'auto',
                'staffs.$[staffIndex].time': timestampNow(),
            },
        };
        const scheduleArrayFilter = {
            arrayFilters: [{ 'staffIndex._staff_id': _staff_id }],
        };
        const scheduleUpdate = await courseScheduleSchema.updateMany(
            {
                $or: [
                    { _id: convertToMongoObjectId(_id) },
                    { 'merge_with.schedule_id': convertToMongoObjectId(_id) },
                ],
            },
            scheduleUpdateObject,
            scheduleArrayFilter,
        );
        if (!scheduleUpdate)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_START_SESSION'),
                null,
            );
        // let present_count = 0;
        // let absent_count = 0;
        // let leave_count = 0;
        // students.forEach((element) => {
        //     if (element.status === 'present') present_count++;
        //     else if (element.status === 'absent') absent_count++;
        //     else if (
        //         element.status === LEAVE ||
        //         element.status === PERMISSION ||
        //         element.status === ONDUTY
        //     )
        //         leave_count++;
        // });

        // const response = {
        //     is_late: late,
        //     status: courseSchedule.status,
        //     remotePlatform: courseSchedule.remotePlatform,
        //     attendance_status: sessionDetail ? sessionDetail.attendance_mode : PENDING,
        //     count: students.length,
        //     present_count,
        //     absent_count,
        //     leave_count,
        //     students,
        //     staffs,
        //     socket_port: courseSchedule.socket_port,
        //     zoomDetail: {
        //         zoomStartUrl:
        //             courseSchedule.zoomDetail && courseSchedule.zoomDetail.zoomStartUrl
        //                 ? courseSchedule.zoomDetail.zoomStartUrl
        //                 : '',
        //     },
        //     teamsDetail: {
        //         teamsStartUrl:
        //             courseSchedule.teamsDetail && courseSchedule.teamsDetail.teamsStartUrl
        //                 ? courseSchedule.teamsDetail.teamsStartUrl
        //                 : '',
        //     },
        // };
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleStart -> end');
        return sendResponseWithRequest(req, res, 200, true, 'Joined session successfully', {
            socket_port:
                courseScheduleData && courseScheduleData.socket_port
                    ? courseScheduleData.socket_port
                    : '',
        });
    } catch (error) {
        logger.error(
            'sessionController -> sessionStart -> %s error : %o',
            req.body._staff_id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.scheduleStartInTab = async (req, res) => {
    try {
        const { secretCode, scheduleDate } = req.body;
        const { _institution_id } = req.headers;
        const requestDatas = {
            secretCode,
            scheduleDate,
        };
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleStartInTab -> start');
        const scheduleDeviceQuery = {
            scheduleSecurityCode: secretCode,
            scheduleDate: new Date(scheduleDate),
            // status: RUNNING,
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleDeviceProject = {
            status: 1,
            _staff_id: 1,
            scheduleId: 1,
        };
        console.time('scheduleDeviceData');
        const scheduleDeviceData = await ScheduleMultiDeviceAttendanceSchema.findOne(
            scheduleDeviceQuery,
            scheduleDeviceProject,
        )
            .populate({ path: 'scheduleId', select: { scheduleEndDateAndTime: 1, socket_port: 1 } })
            .lean();
        console.timeEnd('scheduleDeviceData');
        if (!scheduleDeviceData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        if (scheduleDeviceData.status !== RUNNING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('ATTENDANCE_ALREADY_CLOSED'),
                null,
            );
        const tokens = await generateAuthTokens({ user_id: scheduleDeviceData._staff_id });
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleStartInTab -> end');
        return sendResponseWithRequest(req, res, 200, true, 'Joined session successfully', {
            ...scheduleDeviceData,
            ...tokens,
        });
    } catch (error) {
        logger.error(
            { error: error.stack },
            'sessionController -> sessionStart -> %s error : %o',
            req.body._staff_id,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.scheduleDetails = async (req, res) => {
    try {
        const { scheduleId } = req.params;
        const { operationBy } = req.query;
        const { _institution_id } = req.headers;
        const requestDatas = { scheduleId };
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleDetails -> start');
        const scheduleQuery = {
            _id: convertToMongoObjectId(scheduleId),
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleProject = {
            _id: 1,
            status: 1,
            'students.status': 1,
            'students._id': 1,
            'students.name': 1,
            ...(operationBy &&
                operationBy === DC_STAFF && {
                    'students.mode': 1,
                    'staffs.staff_name': 1,
                    'staffs._staff_id': 1,
                    'staffs.status': 1,
                    'staffs.mode': 1,
                }),
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .findOne(scheduleQuery, scheduleProject)
            .lean();
        console.timeEnd('courseScheduleData');
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_REPORT_PENDING'),
                null,
            );
        // Attaching Additional Datas in Students Array
        const studentIds = courseScheduleData.students.map((studentElement) => studentElement._id);
        const studentDatas = await userSchema.find(
            { _id: { $in: studentIds }, isActive: true },
            { user_id: 1, 'biometric_data.face': 1 },
        );
        const scheduleStudents = [];
        for (studentElement of courseScheduleData.students) {
            const studentDBData = studentDatas.find(
                (studentDataElement) =>
                    studentDataElement._id.toString() === studentElement._id.toString(),
            );
            if (studentDBData)
                scheduleStudents.push({
                    ...studentElement,
                    ...{
                        userId: studentDBData.user_id,
                        face:
                            studentDBData.biometric_data &&
                            studentDBData.biometric_data.face &&
                            studentDBData.biometric_data.face[0] &&
                            studentDBData.biometric_data.face[0]
                                ? await getSignedURL(studentDBData.biometric_data.face[0])
                                : '',
                    },
                });
            // else
            //     scheduleStudents.push({
            //         ...studentElement,
            //         ...{
            //             userId: '',
            //             face: '',
            //         },
            //     });
        }
        courseScheduleData.students = scheduleStudents;

        // Getting Attendance Changes from Multi Device flow
        if (courseScheduleData.status !== PENDING) {
            const scheduleDeviceQuery = {
                scheduleId: convertToMongoObjectId(scheduleId),
                status: RUNNING,
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
                isActive: true,
            };
            const scheduleDeviceProject = { studentIds: 1 };
            console.time('scheduleDeviceData');
            const scheduleDeviceData = await ScheduleMultiDeviceAttendanceSchema.findOne(
                scheduleDeviceQuery,
                scheduleDeviceProject,
            ).lean();
            console.timeEnd('scheduleDeviceData');
            if (scheduleDeviceData && scheduleDeviceData.studentIds) {
                for (studentIdElement of scheduleDeviceData.studentIds) {
                    const scheduleStudentIndex = courseScheduleData.students.findIndex(
                        (scheduleStudentElement) =>
                            scheduleStudentElement._id.toString() === studentIdElement.toString() &&
                            scheduleStudentElement.status === PENDING,
                    );
                    if (scheduleStudentIndex !== -1) {
                        courseScheduleData.students[scheduleStudentIndex].status = PRESENT;
                        courseScheduleData.students[scheduleStudentIndex].mode = 'auto';
                    }
                }
            }
        }
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleDetails -> end');
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('SESSION_REPORT'),
            courseScheduleData,
        );
    } catch (error) {
        logger.error(
            { error: error.stack },
            'sessionController -> sessionStart -> %s error : %o',
            req.body._staff_id,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.scheduleStudentAttendance = async (req, res) => {
    try {
        const { _id, _student_ids, _staff_ids } = req.body;
        const { operationBy } = req.query;
        const { _institution_id } = req.headers;
        const requestDatas = { _id, _student_ids };
        logger.info(requestDatas, 'scheduleMultiDeviceAttendance -> scheduleDetails -> start');
        const scheduleDeviceQuery = {
            scheduleId: convertToMongoObjectId(_id),
            status: RUNNING,
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleDeviceProject = {
            _id: 1,
            ...(operationBy && operationBy === DC_STAFF && { studentIds: 1 }),
        };
        console.time('scheduleDeviceData');
        const scheduleDeviceData = await ScheduleMultiDeviceAttendanceSchema.findOne(
            scheduleDeviceQuery,
            scheduleDeviceProject,
        ).lean();
        console.timeEnd('scheduleDeviceData');
        if (!scheduleDeviceData && (!operationBy || operationBy !== DC_STAFF))
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );

        // Pushing Student Attendance to Schedules
        if (operationBy && operationBy === DC_STAFF) {
            const scheduleQuery = {
                _id: convertToMongoObjectId(_id),
                _institution_id: convertToMongoObjectId(_institution_id),
                isDeleted: false,
                isActive: true,
            };
            const scheduleProject = {
                _id: 1,
                status: 1,
                students: 1,
                staffs: 1,
            };
            console.time('courseScheduleData');
            const courseScheduleData = await courseScheduleSchema
                .findOne(scheduleQuery, scheduleProject)
                .lean();
            console.timeEnd('courseScheduleData');
            if (!courseScheduleData)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('THERE_IS_NO_SESSION_FOUND'),
                    null,
                );
            if (courseScheduleData.status === PENDING)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('SESSION_NOT_YET_START'),
                    null,
                );
            const { students, staffs } = courseScheduleData;
            students.forEach((studentElement, index) => {
                if (
                    operationBy &&
                    operationBy === DC_STAFF &&
                    _student_ids &&
                    _student_ids.find(
                        (studentIdsElement) =>
                            studentIdsElement.toString() === studentElement._id.toString(),
                    )
                ) {
                    students[index].status =
                        students[index].status === 'present' ? 'absent' : 'present';
                    students[index].mode = 'manual';
                } else {
                    const status =
                        scheduleDeviceData &&
                        scheduleDeviceData.studentIds &&
                        scheduleDeviceData.studentIds.find(
                            (studentIdElement) =>
                                studentIdElement.toString() === studentElement._id.toString(),
                        )
                            ? PRESENT
                            : studentElement.status === PENDING
                            ? 'absent'
                            : studentElement.status;
                    if (status === PRESENT && !students[index].mode) students[index].mode = 'auto';
                    students[index].status = status;
                    students[index].primaryStatus =
                        students[index].primaryStatus === undefined
                            ? status
                            : students[index].primaryStatus === PENDING
                            ? status
                            : students[index].primaryStatus;
                }
            });
            staffs.forEach((staffElement, index) => {
                if (operationBy && operationBy === DC_STAFF && _staff_ids && _staff_ids.length) {
                    for (staffElement of _staff_ids) {
                        const staffIndex = staffs.findIndex(
                            (i) => i._staff_id.toString() === staffElement.toString(),
                        );
                        if (staffIndex >= 0) {
                            staffs[staffIndex].status =
                                staffs[staffIndex].status === 'present' ? 'absent' : 'present';
                            staffs[staffIndex].time = timestampNow();
                            staffs[staffIndex].mode = 'manual';
                        }
                    }
                } else {
                    const status = staffElement.status === PENDING ? 'absent' : staffElement.status;
                    staffs[index].status = status;
                }
            });
            const scheduleUpdateObject = {
                $set: {
                    students,
                    staffs,
                },
            };
            const scheduleUpdate = await courseScheduleSchema.updateMany(
                {
                    $or: [
                        { _id: convertToMongoObjectId(_id) },
                        { 'merge_with.schedule_id': convertToMongoObjectId(_id) },
                    ],
                },
                scheduleUpdateObject,
            );
            if (!scheduleUpdate)
                return sendResponseWithRequest(
                    req,
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_CHANGE_ATTENDANCE'),
                    null,
                );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
            );
        }
        const scheduleUpdateObject = {
            $push: {
                studentIds: _student_ids,
            },
        };
        const scheduleUpdate = await ScheduleMultiDeviceAttendanceSchema.updateOne(
            scheduleDeviceQuery,
            scheduleUpdateObject,
        );
        if (!scheduleUpdate)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('UNABLE_TO_CHANGE_ATTENDANCE'),
                null,
            );
        return sendResponseWithRequest(
            req,
            res,
            200,
            true,
            req.t('ATTENDANCE_MANUALLY_CHANGED_SUCCESSFULLY'),
        );
    } catch (error) {
        logger.error(
            'sessionController -> sessionAttendanceChange -> %s error : %o',
            req.body._id,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};

exports.scheduleCloseWithAttendance = async (req, res) => {
    const { scheduleId } = req.params;
    try {
        const { _institution_id } = req.headers;
        const requestDatas = { scheduleId };
        logger.info({ requestDatas }, 'scheduleMultiDeviceAttendance -> scheduleDetails -> start');
        const scheduleQuery = {
            _id: convertToMongoObjectId(scheduleId),
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleProject = {
            _id: 1,
            status: 1,
            students: 1,
            staffs: 1,
        };
        console.time('courseScheduleData');
        const courseScheduleData = await courseScheduleSchema
            .findOne(scheduleQuery, scheduleProject)
            .lean();
        console.timeEnd('courseScheduleData');
        if (!courseScheduleData)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('THERE_IS_NO_SESSION_FOUND'),
                null,
            );
        if (courseScheduleData.status === PENDING)
            return sendResponseWithRequest(
                req,
                res,
                200,
                false,
                req.t('SESSION_NOT_YET_START'),
                null,
            );
        const { students, staffs } = courseScheduleData;
        const scheduleDeviceQuery = {
            scheduleId: convertToMongoObjectId(scheduleId),
            status: RUNNING,
            _institution_id: convertToMongoObjectId(_institution_id),
            isDeleted: false,
            isActive: true,
        };
        const scheduleDeviceProject = { studentIds: 1 };
        console.time('scheduleDeviceData');
        const scheduleDeviceData = await ScheduleMultiDeviceAttendanceSchema.findOne(
            scheduleDeviceQuery,
            scheduleDeviceProject,
        ).lean();
        console.timeEnd('scheduleDeviceData');
        students.forEach((studentElement, index) => {
            const status =
                scheduleDeviceData &&
                scheduleDeviceData.studentIds &&
                scheduleDeviceData.studentIds.find(
                    (studentIdElement) =>
                        studentIdElement.toString() === studentElement._id.toString(),
                )
                    ? PRESENT
                    : studentElement.status === PENDING
                    ? 'absent'
                    : studentElement.status;
            if (status === PRESENT && !students[index].mode) students[index].mode = 'auto';
            students[index].status = status;
            students[index].primaryStatus =
                students[index].primaryStatus === undefined
                    ? status
                    : students[index].primaryStatus === PENDING
                    ? status
                    : students[index].primaryStatus;
        });
        staffs.forEach((staffElement, index) => {
            const status = staffElement.status === PENDING ? 'absent' : staffElement.status;
            staffs[index].status = status;
        });
        const scheduleUpdateObject = {
            $set: {
                'sessionDetail.attendance_mode': COMPLETED,
                'sessionDetail.stop_time': timestampNow(),
                students,
                staffs,
                status: COMPLETED,
            },
        };
        const scheduleUpdate = await courseScheduleSchema.updateMany(
            {
                $or: [
                    { _id: convertToMongoObjectId(scheduleId) },
                    { 'merge_with.schedule_id': convertToMongoObjectId(scheduleId) },
                ],
            },
            scheduleUpdateObject,
        );
        logger.info({ requestDatas }, 'scheduleMultiDeviceAttendance -> scheduleDetails -> end');
        if (scheduleUpdate) {
            console.log(
                await ScheduleMultiDeviceAttendanceSchema.updateOne(
                    {
                        scheduleId: convertToMongoObjectId(scheduleId),
                        status: RUNNING,
                    },
                    { $set: { status: COMPLETED } },
                ),
            );
            return sendResponseWithRequest(
                req,
                res,
                200,
                true,
                req.t('SESSION_COMPLETED_SUCCESSFULLY'),
                null,
            );
        }
        return sendResponseWithRequest(req, res, 200, false, req.t('UNABLE_TO_END_SESSION'), null);
    } catch (error) {
        logger.error(
            'scheduleMultiDeviceAttendance -> scheduleDetails -> %s error : %o',
            scheduleId,
            error.stack,
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('ERROR_CATCH'),
            error.toString(),
        );
    }
};
