const StudentService = require('./student.service');
const {
    getUserCourseLists,
} = require('../../../lib/digi_class/course_session/course_session_service');

const getStudentCourses = async ({
    query: { institutionCalendarId },
    headers: { user_id: userId },
}) => {
    const courses = await getUserCourseLists({ type: 'student', userId, institutionCalendarId });

    const formattedCourses = courses.map(
        ({
            _program_id,
            program_name,
            course_code,
            course_type,
            course_name,
            _id: courseId,
            year,
            level,
            rotation,
            rotationCount,
            term,
        }) => ({
            programId: _program_id,
            programName: program_name,
            courseName: course_name,
            courseType: course_type,
            courseCode: course_code,
            courseId,
            year,
            level,
            rotation,
            rotationCount,
            term,
        }),
    );

    return { statusCode: 200, data: formattedCourses };
};

const getStudentPortfolio = async ({
    query: {
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        rotation,
        rotationCount,
    },
    headers: { user_id: userId },
}) => {
    const portfolio = await StudentService.getStudentPortfolio({
        programId,
        courseId,
        institutionCalendarId,
        term,
        year,
        level,
        rotation,
        rotationCount,
        userId,
    });

    return { statusCode: 200, data: portfolio };
};

const updateStudentPortfolio = async ({
    query: { portfolioId, componentId },
    headers: { user_id: userId },
    body: { component },
}) => {
    const portfolio = await StudentService.updatePortfolio({
        portfolioId,
        componentId,
        component,
        userId,
    });

    return { statusCode: 200, data: portfolio };
};

const getGlobalRubric = async ({
    query: { portfolioId, componentId, childId, studentId },
    headers: { user_id: userId },
}) => {
    const rubric = await StudentService.getGlobalRubric({
        portfolioId,
        componentId,
        childId,
        studentId,
    });

    return { statusCode: 200, data: rubric };
};

module.exports = {
    getStudentCourses,
    getStudentPortfolio,
    updateStudentPortfolio,
    getGlobalRubric,
};
