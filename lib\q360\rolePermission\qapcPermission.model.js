const {
    Schema,
    model,
    Types: { ObjectId },
} = require('mongoose');
const {
    QAPC_PERMISSION,
    USER,
    GUEST_USER,
    TAG_LEVEL: { PROGRAM, COURSE, INSTITUTION },
    QAPC_FORM_SETTING_COURSES,
    QAPC_FORM_COURSES_GROUPS,
    QAPC_ACTION,
    QAPC_SUB_MODULE,
    ROLE,
    INSTITUTION_CALENDAR,
    DIGI_PROGRAM,
    DIGI_CURRICULUM,
    DIGI_COURSE,
    QAPC_ROLE,
    ALL,
    EVERY,
} = require('../../utility/constants');

const qapcPermissionSchema = new Schema(
    {
        userId: { type: ObjectId, ref: USER },
        guestUserId: { type: ObjectId, ref: GUEST_USER },
        _institution_id: { type: ObjectId, ref: INSTITUTION },
        qapcRoleIds: [{ type: ObjectId, ref: QAPC_ROLE }],
        permissions: {
            cfpc: [
                {
                    categoryFormCourseId: [{ type: ObjectId, ref: QAPC_FORM_SETTING_COURSES }],
                    selectType: { type: String } /*All selected row name */,
                    selectAll: { type: Boolean, default: false },
                    formCourseGroupId: [{ type: ObjectId, ref: QAPC_FORM_COURSES_GROUPS }],
                    selectedAcademic: { type: Boolean, default: false },
                    academicYear: {
                        type: String,
                        enum: [ALL, EVERY],
                    },
                    multiUser: { type: Boolean, default: false },
                    institutionCalendarIds: [{ type: ObjectId, ref: INSTITUTION_CALENDAR }],
                    assignType: { type: String } /*user or role*/,
                    subModuleId: { type: ObjectId, ref: QAPC_SUB_MODULE },
                    subModuleName: { type: String },
                    approverLevelIndex: { type: Number },
                    actionId: [{ type: ObjectId, ref: QAPC_ACTION }],
                    levelName: { type: String, enums: [PROGRAM, COURSE, INSTITUTION] },
                    qapcRoleId: { type: ObjectId, ref: QAPC_ROLE },
                    roleId: { type: ObjectId, ref: ROLE },
                    isDeleted: { type: Boolean, default: false },
                    uuid: { type: String },
                },
            ],
            pccf: [
                {
                    programId: { type: ObjectId, ref: DIGI_PROGRAM },
                    curriculamId: { type: ObjectId, ref: DIGI_CURRICULUM },
                    year: { type: String },
                    courseId: { type: ObjectId, ref: DIGI_COURSE },
                    multiUser: { type: Boolean, default: false },
                    categoryFormCourseId: [{ type: ObjectId, ref: QAPC_FORM_SETTING_COURSES }],
                    selectedAcademic: { type: Boolean, default: false },
                    selectType: { type: String },
                    selectAll: { type: Boolean, default: false },
                    formCourseGroupId: [{ type: ObjectId, ref: QAPC_FORM_COURSES_GROUPS }],
                    institutionCalendarIds: [{ type: ObjectId, ref: INSTITUTION_CALENDAR }],
                    assignType: { type: String } /*user or role*/,
                    subModuleId: { type: ObjectId, ref: QAPC_SUB_MODULE },
                    subModuleName: { type: String },
                    approverLevelIndex: { type: Number },
                    actionId: [{ type: ObjectId, ref: QAPC_ACTION }],
                    levelName: { type: String, enums: [PROGRAM, COURSE, INSTITUTION] },
                    qapcRoleId: { type: ObjectId, ref: QAPC_ROLE },
                    roleId: { type: ObjectId, ref: ROLE },
                    academicYear: {
                        type: String,
                        enum: [ALL, EVERY],
                    },
                    isDeleted: { type: Boolean, default: false },
                },
            ],
        },
    },
    {
        timestamps: true,
    },
);
module.exports = model(QAPC_PERMISSION, qapcPermissionSchema);
