// const base_control = require('../../base/base_controller');
// const common_files = require('../../utility/common');

const {
    allSessionOrderDatas,
    allCourseList,
    allStudentGroupYesterday,
} = require('../../../service/cache.service');
const { QUIZ } = require('../../utility/enums');
const {
    convertToMongoObjectId,
    clone,
    responseFunctionWithRequest,
    sendResponseWithRequest,
    query: commonQuery,
} = require('../../utility/common');
const {
    DS_DATA_RETRIEVED,
    SCHEDULE_TYPES: { REGULAR },
    STUDENT_SESSION_SURVEY,
    COMPLETED,
    ACTIVITIES,
    COURSE_SCHEDULE,
    QUESTION,
    MISSED,
    SUMMARY,
    SLO,
    CLO,
    USER,
    PROGRAM_REPORT_SETTINGS,
    DIGI_SESSION_ORDER,
} = require('../../utility/constants');
const { logger } = require('../../utility/util_keys');
const {
    studentGroupListNew,
    studentActivitySLOReport,
    getSurvey,
    getCourseSLOWithSessionOrderData,
    activityQuestionListWithActivityData,

    getSessionWiseSLO,
    getSessionSLOSurvey,
    courseYetToRate,
    selfEvaluationSummary,
    selfEvaluationSlo,
    selfEvaluationClo,
    courseCLOLists,
    selfEvaluationOutcomeSloWise,
    courseSessionWiseYetToRate,
    comparisonReportSummary,
    activityQuestionList,
    comparisonReportSlo,
    comparisonReportClo,
    getScheduleSessionWiseSLO,
    surveyRatingChanges,
} = require('./survey.service');
const { getDashboardCoursesList } = require('../dashboard/dashboard.service');
const activity = require('mongoose').model(ACTIVITIES);
const question = require('mongoose').model(QUESTION);

const { scheduleDateFormateChange } = require('../../utility/common_functions');
const surveyCollection = require('mongoose').model(STUDENT_SESSION_SURVEY);
const courseScheduleCollection = require('mongoose').model(COURSE_SCHEDULE);
const userCollection = require('mongoose').model(USER);
const programReportSettings = require('mongoose').model(PROGRAM_REPORT_SETTINGS);
const sessionOrder = require('mongoose').model(DIGI_SESSION_ORDER);

const { get, insert, update, get_list, bulk_write } = require('../../base/base_controller');

const {
    studentGroupList,
    getCourseSLO,
    getCourseCLO,
} = require('../../reports_analytics/reports_analytics_services');

exports.getSessionSurvey = async (req, res) => {
    try {
        const {
            params: {
                institutionCalendarId,
                programId,
                userId,
                courseId,
                sessionId,
                yearNo,
                levelNo,
                term,
            },
            query: { rotation, rotationCount, mode },
        } = req;
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            sessionId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
        };
        logger.info(
            requestedParams,
            'Survey -> getSessionSurveyDetails -> Course Session wise SLO Survey start',
        );
        let response = {
            /* requestedParams */
        };
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );

        let courseSessionOrder = await sessionOrder
            .findOne({ ...commonQuery, _course_id: convertToMongoObjectId(courseId) }, {})
            .lean();
        courseSessionOrder =
            courseSessionOrder && courseSessionOrder !== null
                ? courseSessionOrder.session_flow_data
                : [];

        // const courseSessionOrder = (await allSessionOrderDatas()).find(
        //     (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        // ).session_flow_data;
        const courseSessionData = courseSessionOrder.find(
            (sessionElement) => sessionElement._id.toString() === sessionId,
        );
        const userCourseSurveyData = await getSessionSLOSurvey(requestedParams);

        if (courseSessionData) {
            response = await getSessionWiseSLO({ courseSessionData, userCourseSurveyData, mode });
        }
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
        };
        logger.info('Survey -> getSessionSurveyDetails -> Course Session wise SLO Survey End');
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SESSION_WISE_SLO_SURVEY'),
                    response,
                ),
            );
    } catch (error) {
        logger.error(error, 'Survey -> getSessionSurveyDetails -> Session wise SLO Survey Error');
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.updateSessionSurvey = async (req, res) => {
    try {
        const {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            sessionId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            slos,
            feedBack,
        } = req.body;
        let { ratingValue } = req.body;
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            sessionId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            ratingValue,
            slos,
            feedBack,
        };
        logger.info(
            requestedParams,
            'Survey -> storeSessionSurvey -> Course Session wise SLO Survey Updating start',
        );
        if (!ratingValue) ratingValue = 5;
        const userCourseSurveyData = await getSessionSLOSurvey(requestedParams);

        if (!userCourseSurveyData.status) {
            const surveyCreate = await insert(surveyCollection, {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _user_id: convertToMongoObjectId(userId),
                _course_id: convertToMongoObjectId(courseId),
                _session_order_id: convertToMongoObjectId(sessionId),
                yearNo,
                levelNo,
                term,
                rotationCount,
                ratingValue,
                slos,
                feedBack,
            });
            if (!surveyCreate.status)
                return res
                    .status(410)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('UNABLE_TO_ADD_SURVEY'),
                            surveyCreate.data,
                        ),
                    );
        } else {
            for (sloElement of userCourseSurveyData.data.slos) {
                const userSlo = slos.find(
                    (sloRatingElement) =>
                        sloRatingElement._slo_id.toString() === sloElement._slo_id.toString(),
                );
                if (!userSlo) {
                    slos.push({
                        _slo_id: convertToMongoObjectId(sloElement._slo_id),
                        sloRating:
                            userCourseSurveyData.data.ratingValue !== ratingValue
                                ? ratingValue === 4 && userCourseSurveyData.data.ratingValue === 5
                                    ? parseInt(sloElement.sloRating) - 1
                                    : parseInt(sloElement.sloRating) + 1
                                : sloElement.sloRating,
                    });
                }
            }
            const surveyUpdate = {
                ratingValue,
                slos,
            };
            if (feedBack) surveyUpdate.feedBack = feedBack;
            const sloUpdates = await update(
                surveyCollection,
                userCourseSurveyData.data._id,
                surveyUpdate,
            );
            if (!sloUpdates.status)
                return res
                    .status(410)
                    .send(
                        responseFunctionWithRequest(
                            req,
                            410,
                            false,
                            req.t('UNABLE_TO_UPDATE_SURVEY'),
                            sloUpdates.data,
                        ),
                    );
        }
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SESSION_WISE_SLO_SURVEY_STORING'),
                    req.t('COURSE_SESSION_WISE_SLO_SURVEY_STORING'),
                ),
            );
    } catch (error) {
        logger.error(error, 'Survey -> storeSessionSurvey -> Session wise SLO Survey Error');
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.getScheduleSessionSurvey = async (req, res) => {
    try {
        const {
            params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term },
            query: { rotation, rotationCount, mode },
        } = req;
        let {
            query: { sessionIds },
        } = req;
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            sessionIds,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
        };
        if (!Array.isArray(sessionIds)) sessionIds = [sessionIds];
        logger.info(
            requestedParams,
            'Survey -> getSessionSurveyDetails -> Course Session wise SLO Survey start',
        );
        const response = {};
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );

        let courseSessionOrder = await sessionOrder
            .findOne({ ...commonQuery, _course_id: convertToMongoObjectId(courseId) }, {})
            .lean();
        courseSessionOrder =
            courseSessionOrder && courseSessionOrder !== null
                ? courseSessionOrder.session_flow_data
                : [];

        // const courseSessionOrder = (await allSessionOrderDatas()).find(
        //     (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        // ).session_flow_data;
        const courseSessionData = courseSessionOrder.filter((sessionElement) =>
            sessionIds.find(
                (sessionIdElement) => sessionIdElement.toString() === sessionElement._id.toString(),
            ),
        );
        console.time('userCourseSurveyDatas');
        const userCourseSurveyDatas = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _user_id: convertToMongoObjectId(userId),
                _course_id: convertToMongoObjectId(courseId),
                _session_order_id: {
                    $in: sessionIds.map((sessionElement) => convertToMongoObjectId(sessionElement)),
                },
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, slos: 1, feedBack: 1 },
        );
        console.timeEnd('userCourseSurveyDatas');
        userCourseSurveyDatas.data = userCourseSurveyDatas.status ? userCourseSurveyDatas.data : [];
        if (courseSessionData) {
            response.sessions = await getScheduleSessionWiseSLO({
                courseSessionData,
                userCourseSurveyDatas,
                mode,
            });
        }
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
        };
        logger.info('Survey -> getSessionSurveyDetails -> Course Session wise SLO Survey End');
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SESSION_WISE_SLO_SURVEY'),
                    response,
                ),
            );
    } catch (error) {
        logger.error(error, 'Survey -> getSessionSurveyDetails -> Session wise SLO Survey Error');
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.updateScheduleSessionSurvey = async (req, res) => {
    try {
        const {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            sessions,
        } = req.body;
        let { ratingValue } = req.body;
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            ratingValue,
            sessions,
        };
        if (!ratingValue) ratingValue = 5;
        logger.info(
            requestedParams,
            'Survey -> updateScheduleSessionSurvey -> Course Multi Session wise SLO Survey Updating start',
        );
        console.time('userCourseSurveyDatas');
        const userCourseSurveyDatas = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _user_id: convertToMongoObjectId(userId),
                _course_id: convertToMongoObjectId(courseId),
                _session_order_id: {
                    $in: sessions.map((sessionElement) =>
                        convertToMongoObjectId(sessionElement.sessionId),
                    ),
                },
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, slos: 1, feedBack: 1 },
        );
        console.timeEnd('userCourseSurveyDatas');
        userCourseSurveyDatas.data = userCourseSurveyDatas.status ? userCourseSurveyDatas.data : [];
        const bulkWriteData = [];
        for (sessionElement of sessions) {
            const userSessionElement = userCourseSurveyDatas.data.find(
                (surveyElement) =>
                    surveyElement._session_order_id.toString() ===
                    sessionElement.sessionId.toString(),
            );
            if (userSessionElement) {
                const surveyObject = {
                    ratingValue,
                    slos: sessionElement.slos,
                };
                if (sessionElement.feedBack) surveyObject.feedBack = sessionElement.feedBack;
                bulkWriteData.push({
                    updateOne: {
                        filter: {
                            _id: convertToMongoObjectId(userSessionElement._id),
                        },
                        update: {
                            $set: surveyObject,
                        },
                    },
                });
            } else {
                const surveyObject = {
                    slos: sessionElement.slos,
                };
                if (sessionElement.feedBack) surveyObject.feedBack = sessionElement.feedBack;
                bulkWriteData.push({
                    insertOne: {
                        document: {
                            isDeleted: false,
                            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                            _program_id: convertToMongoObjectId(programId),
                            _user_id: convertToMongoObjectId(userId),
                            _course_id: convertToMongoObjectId(courseId),
                            _session_order_id: convertToMongoObjectId(sessionElement.sessionId),
                            yearNo,
                            levelNo,
                            term,
                            rotationCount,
                            ratingValue,
                            slos: sessionElement.slos,
                            feedBack: sessionElement.feedBack,
                        },
                    },
                });
            }
        }
        const sloUpdates = await bulk_write(surveyCollection, bulkWriteData);
        if (!sloUpdates.status)
            return res
                .status(410)
                .send(
                    responseFunctionWithRequest(
                        req,
                        410,
                        false,
                        req.t('UNABLE_TO_UPDATE_SURVEY'),
                        sloUpdates.data,
                    ),
                );
        logger.info(
            'Survey -> updateScheduleSessionSurvey -> Multi Session wise SLO Survey Update End',
        );
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_MULTI_SESSION_WISE_SLO_SURVEY_UPDATED'),
                    req.t('COURSE_MULTI_SESSION_WISE_SLO_SURVEY_UPDATED'),
                ),
            );
    } catch (error) {
        logger.error(
            error,
            'Survey -> updateScheduleSessionSurvey -> Session wise SLO Survey Error',
        );
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.getCourses = async (req, res) => {
    try {
        const { type, userId, institutionCalendarId, rotation_count } = req.query;
        logger.info('dashboardController -> getCourses -> %s Course Module %s start', userId);
        const courseDatas = await getDashboardCoursesList(userId, type, institutionCalendarId);
        // const allSessOrderData = await allSessionOrderDatas();
        const courseIds = courseDatas.map((ele) => ele._id);

        const allSessOrderData = await sessionOrder
            .find({ ...commonQuery, _course_id: { $in: courseIds } }, {})
            .lean();

        const studentGroupDataCached = await allStudentGroupYesterday(
            institutionCalendarId,
            scheduleDateFormateChange(new Date()),
        );
        console.time('scheduleData');
        const courseScheduleDatas = await get_list(
            courseScheduleCollection,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                status: { $in: [COMPLETED, MISSED] },
                'students._id': convertToMongoObjectId(userId),
                type: REGULAR,
            },
            {
                isActive: 1,
                status: 1,
                term: 1,
                'students._id': 1,
                'session._session_id': 1,
            },
        );
        if (!courseScheduleDatas.status) courseScheduleDatas.data = [];
        console.timeEnd('scheduleData');
        console.time('activityData');
        const activityData = await activity
            .find(
                {
                    isDeleted: false,
                    _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                    quizType: QUIZ,
                    status: COMPLETED,
                    // studentCompletedQuiz: { $in: [convertToMongoObjectId(userId)] },
                    'students._studentId': convertToMongoObjectId(userId),
                },
                {},
            )
            .lean();
        let activityIds = [];
        if (activityData) {
            const filteredActivityData = activityData.filter((ele) =>
                courseIds.find(
                    (courseIdElement) => courseIdElement.toString() === ele.courseId.toString(),
                ),
            );
            activityIds = filteredActivityData.map((eleActivity) => eleActivity._id);
        }
        console.timeEnd('activityData');
        console.log(activityIds);
        console.time('questionData');
        const questQuery = {
            _activityId: { $in: activityIds },
            isDeleted: false,
        };
        const questionData = await question.find(questQuery, {}).lean();
        // return res.send(questionData);
        // const questionData = await allQuestions({});
        //status completed, userid
        console.timeEnd('questionData');
        console.time('surveyData');
        const surveyData = await get_list(
            surveyCollection,
            {
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _course_id: { $in: courseIds },
                _user_id: convertToMongoObjectId(userId),
                isActive: true,
                isDeleted: false,
            },
            {
                rotation: 1,
                isDeleted: 1,
                _institution_calendar_id: 1,
                _program_id: 1,
                _user_id: 1,
                _course_id: 1,
                _session_order_id: 1,
                yearNo: 1,
                levelNo: 1,
                term: 1,
                slos: 1,
            },
        );
        console.timeEnd('surveyData');
        console.time('ProgramReportSettings');
        const programReportSettingsData = await get_list(
            programReportSettings,
            { status: 'saved' },
            {
                selfEvaluationSurvey: 1,
                _program_id: 1,
                courses: 1,
                scalePoints: 1,
                rattingScaleForSurvey: 1,
            },
        );
        if (!programReportSettingsData.status) programReportSettingsData.data = [];
        console.timeEnd('ProgramReportSettings');

        if (courseDatas.length) {
            for (eleCourses of courseDatas) {
                const courseSloData = await getCourseSLOWithSessionOrderData(
                    eleCourses._course_id,
                    allSessOrderData,
                    courseScheduleDatas.data,
                    eleCourses.term,
                );
                const courseSessionOrder = allSessOrderData.find(
                    (courseSessionElement) =>
                        courseSessionElement._course_id.toString() ===
                        eleCourses._course_id.toString(),
                );
                const courseSloDatas = await getCourseSLO({ courseSessionOrder });
                const courseCLOData = await getCourseCLO(eleCourses._course_id);
                for (courseCLOElement of courseCLOData.data) {
                    for (CLOElement of courseCLOElement.clos) {
                        courseSloDatas.data.push({
                            _id: CLOElement._id,
                            no: CLOElement.no,
                            name: CLOElement.name,
                            delivery_symbol: 'clo',
                        });
                    }
                }
                const filteredStudentGroupData = studentGroupDataCached.filter(
                    (ele) =>
                        ele._institution_calendar_id.toString() ===
                            institutionCalendarId.toString() &&
                        ele.master._program_id.toString() === eleCourses._program_id.toString(),
                );
                //return res.send(studentGroupDataCached);
                const studentGroups = await studentGroupListNew(
                    filteredStudentGroupData,
                    eleCourses._course_id,
                    eleCourses.level,
                    eleCourses.term,
                    eleCourses.rotation_count,
                    userId,
                );
                const { activityList } = await activityQuestionListWithActivityData(
                    eleCourses._course_id,
                    eleCourses.term,
                    eleCourses.rotation_count,
                    activityData,
                    courseScheduleDatas.data,
                    questionData,
                );
                let studentDetails = studentGroups.studentWithOutGroupList.filter(
                    (eleStudent) => eleStudent._student_id.toString() === userId.toString(),
                );
                studentGroups.studentWithOutGroupList.filter(
                    (eleStudent) => eleStudent._student_id.toString() === userId.toString(),
                );
                studentDetails = clone(
                    await studentActivitySLOReport(
                        studentDetails,
                        activityList,
                        courseSloDatas.data,
                    ),
                );
                //student
                const studentData = studentGroups.studentWithOutGroupList.filter(
                    (eleStudent) => eleStudent._student_id.toString() === userId.toString(),
                );
                //return res.send(studentData);
                //eleCourses.students = studentData;
                eleCourses.studentQuiz = studentData[0]
                    ? studentData[0].studentActivities.map((ele) => {
                          return {
                              _id: ele._id,
                              name: ele.name,
                              mark: ele.mark,
                          };
                      })
                    : [];
                eleCourses.quizData = {
                    quizAttended: studentData[0] ? studentData[0].studentActivities.length : 0,
                    quizAssigned: studentData[0] ? studentData[0].studentActivities.length : 0,
                    overAllQuizPerformance:
                        studentData[0] && studentData[0].mark ? (studentData[0].mark / 100) * 5 : 0,
                };

                const surveyDetails = await getSurvey(
                    institutionCalendarId,
                    eleCourses._program_id,
                    eleCourses.level,
                    eleCourses.term,
                    eleCourses._course_id,
                    userId,
                    surveyData,
                );

                const sloSurvey = [];
                let totalSurveyRated = 0;
                if (surveyDetails.data.length) {
                    for (sloData of courseSloData.data) {
                        const surV = surveyDetails.data.filter(
                            (ele) =>
                                ele._session_order_id.toString() ===
                                sloData._session_order_id.toString(),
                        );
                        if (surV.length > 0) {
                            for (eleSurV of surV) {
                                const slo = eleSurV.slos.find(
                                    (ele) => ele._slo_id.toString() === sloData._id.toString(),
                                );
                                if (
                                    slo &&
                                    !sloSurvey.find(
                                        (sloSurveyElement) =>
                                            sloSurveyElement._id.toString() ===
                                            sloData._id.toString(),
                                    )
                                ) {
                                    sloSurvey.push({
                                        _id: sloData._id,
                                        no: sloData.no,
                                        name: sloData.name,
                                        delivery_symbol: sloData.delivery_symbol,
                                        s_no: sloData.s_no,
                                        slo_rating: slo.sloRating,
                                    });
                                    totalSurveyRated += slo.sloRating;
                                }
                            }
                        }
                    }
                }

                eleCourses.course_details = {
                    _id: eleCourses._id,
                    _institution_calendar_id: institutionCalendarId,
                    _program_id: eleCourses._program_id,
                    //"program_no":"BS0101",
                    program_name: eleCourses.program_name,
                    year: eleCourses.year,
                    level: eleCourses.level,
                    term: eleCourses.term,
                    rotation: eleCourses.rotation,
                    //"rotation_count":eleCourses.,
                    _course_id: eleCourses._course_id,
                    course_name: eleCourses.course_name,
                    course_no: eleCourses.course_number,
                    start_date: eleCourses.start_date,
                    end_date: eleCourses.end_date,
                };

                eleCourses.studentSurveyData = sloSurvey;

                //Program Report settings
                const settingsData = programReportSettingsData.data.find(
                    (eleProgramReportSettings) =>
                        eleProgramReportSettings._program_id.toString() ===
                            eleCourses._program_id.toString() &&
                        eleProgramReportSettings.selfEvaluationSurvey.isChecked === true &&
                        (eleProgramReportSettings.rattingScaleForSurvey === 'common'
                            ? eleProgramReportSettings.scalePoints.length !== 0
                            : eleProgramReportSettings.selfEvaluationSurvey.scalePoints.length !==
                              0),
                );
                if (!settingsData) eleCourses.selfEvaluationSurveyStatus = false;
                else {
                    const courseFind = settingsData.courses.find(
                        (courseElement) =>
                            courseElement.courseId.toString() ===
                                eleCourses._course_id.toString() &&
                            courseElement.year === eleCourses.year &&
                            courseElement.level === eleCourses.level &&
                            courseElement.term === eleCourses.term &&
                            (eleCourses.rotation_count && parseInt(eleCourses.rotation_count) !== 0
                                ? eleCourses.rotation_count === courseElement.rotationCount
                                : true),
                    );
                    if (courseFind) {
                        if (courseFind.selfEvaluationSurvey) {
                            eleCourses.selfEvaluationSurveyStatus = true;
                            eleCourses.surveyData = {
                                sloRated: sloSurvey.length,
                                sloEnabledForRating: courseSloData.data.length,
                                avgSESscore: sloSurvey.length
                                    ? totalSurveyRated / sloSurvey.length
                                    : sloSurvey.length,
                            };
                            eleCourses.yetToRate = courseSloData.data.length - sloSurvey.length;
                        } else eleCourses.selfEvaluationSurveyStatus = false;
                    } else {
                        eleCourses.selfEvaluationSurveyStatus = true;
                        eleCourses.surveyData = {
                            sloRated: sloSurvey.length,
                            sloEnabledForRating: courseSloData.data.length,
                            avgSESscore: sloSurvey.length
                                ? totalSurveyRated / sloSurvey.length
                                : sloSurvey.length,
                        };
                        eleCourses.yetToRate = courseSloData.data.length - sloSurvey.length;
                    }
                }
                //delete eleCourses._program_id;
                delete eleCourses.program_name;
                delete eleCourses.year;
                delete eleCourses.rotation;
                delete eleCourses._course_id;
                delete eleCourses.level;
                delete eleCourses.course_name;
                delete eleCourses.course_number;
                delete eleCourses.start_date;
                delete eleCourses.end_date;
            }
        }
        return sendResponseWithRequest(req, res, 200, true, DS_DATA_RETRIEVED, {
            courses: courseDatas,
        });
    } catch (error) {
        console.log(error);
        logger.error(
            'dashboardController -> getCourses -> %s Course Module error : %o',
            req.query.userId,
            { error },
        );
        return sendResponseWithRequest(
            req,
            res,
            500,
            false,
            req.t('INTERNAL_SERVER_ISSUE'),
            error.toString(),
        );
    }
};

exports.createCourseMockSurvey = async (req, res) => {
    try {
        const {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            feedBack,
        } = req.body;
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
            feedBack,
        };
        logger.info(
            requestedParams,
            'Survey -> createCourseMockSurvey -> Course Session wise SLO Survey Updating start',
        );
        const userCourseSurveyData = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _user_id: convertToMongoObjectId(userId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1 },
        );
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        ).session_flow_data;
        const courseSurveyMock = [];
        let count = 0;
        for (sessionElement of courseSessionOrder) {
            if (
                !(
                    userCourseSurveyData.status &&
                    userCourseSurveyData.data &&
                    userCourseSurveyData.data.find(
                        (dataElement) =>
                            dataElement._session_order_id.toString() ===
                            sessionElement._id.toString(),
                    )
                )
            ) {
                const slos = [];
                for (sloElement of sessionElement.slo) {
                    slos.push({
                        _slo_id: sloElement._id,
                        sloRating: count <= 5 ? Math.floor(Math.random() * 5 + 1) : null,
                    });
                    count = count <= 5 ? count + 1 : 0;
                }
                courseSurveyMock.push({
                    insertOne: {
                        document: {
                            isDeleted: false,
                            _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                            _program_id: convertToMongoObjectId(programId),
                            _user_id: convertToMongoObjectId(userId),
                            _course_id: convertToMongoObjectId(courseId),
                            _session_order_id: convertToMongoObjectId(sessionElement._id),
                            yearNo,
                            levelNo,
                            term,
                            rotationCount,
                            slos,
                        },
                    },
                });
            }
        }
        const surveyMock =
            courseSurveyMock.length !== 0
                ? await bulk_write(surveyCollection, courseSurveyMock)
                : null;
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SESSION_WISE_SLO_SURVEY_STORING'),
                    surveyMock,
                ),
            );
    } catch (error) {
        logger.error(error, 'Survey -> createCourseMockSurvey -> Session wise SLO Survey Error');
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.selfEvaluation = async (req, res) => {
    const {
        params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term, tab },
        query: { rotation, rotationCount },
    } = req;
    try {
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            tab,
            rotation,
            rotationCount,
        };
        logger.info(
            requestedParams,
            'Survey -> selfEvaluation -> Course Self Evaluation Survey %s start',
            tab,
        );
        const response = {};
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );

        let courseSessionOrder = await sessionOrder
            .findOne({ ...commonQuery, _course_id: convertToMongoObjectId(courseId) }, {})
            .lean();
        courseSessionOrder =
            courseSessionOrder && courseSessionOrder !== null
                ? courseSessionOrder.session_flow_data
                : [];

        // let courseSessionOrder = (await allSessionOrderDatas()).find(
        //     (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        // ).session_flow_data;
        console.time('courseSchedule');
        const courseScheduleDatas = await get_list(
            courseScheduleCollection,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                year_no: yearNo.toString(),
                level_no: levelNo.toString(),
                term,
                status: { $in: [COMPLETED, MISSED] },
                rotation_count: rotationCount,
                'students._id': convertToMongoObjectId(userId),
                type: REGULAR,
            },
            {
                isActive: 1,
                status: 1,
                program_name: 1,
                'students._id': 1,
                'session._session_id': 1,
            },
        );
        console.timeEnd('courseSchedule');
        if (!courseScheduleDatas.status) courseScheduleDatas.data = [];
        const completedSession = [
            ...new Set(
                courseScheduleDatas.data.map((scheduleElement) =>
                    scheduleElement.session._session_id.toString(),
                ),
            ),
        ];
        courseSessionOrder = courseSessionOrder.filter((sessionElement) =>
            completedSession.find(
                (scheduleElement) => scheduleElement.toString() === sessionElement._id.toString(),
            ),
        );
        // const studentIds = courseScheduleDatas.status
        //     ? [
        //           ...new Set(
        //               courseScheduleDatas.data
        //                   .map((scheduleElement) =>
        //                       scheduleElement.students
        //                           .map((studentElement) => studentElement._id.toString())
        //                           .flat(),
        //                   )
        //                   .flat(),
        //           ),
        //       ]
        //     : [];
        const { studentWithOutGroupList } = await studentGroupList(
            institutionCalendarId,
            programId,
            courseId,
            levelNo,
            term,
            rotationCount,
        );
        const studentIds = studentWithOutGroupList;
        console.time('surveyCollection');
        const courseSurveyData = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, _user_id: 1, slos: 1, ratingValue: 1 },
        );
        console.timeEnd('surveyCollection');
        console.time('ProgramReportSettings');
        const programReportSettingsData = await get(
            programReportSettings,
            { _program_id: convertToMongoObjectId(programId), status: 'saved' },
            { selfEvaluationSurvey: 1, _program_id: 1, courses: 1 },
        );
        const settingLimit =
            programReportSettingsData.status &&
            programReportSettingsData.data &&
            programReportSettingsData.data.selfEvaluationSurvey &&
            programReportSettingsData.data.selfEvaluationSurvey.limit &&
            programReportSettingsData.data.selfEvaluationSurvey.limit.to
                ? programReportSettingsData.data.selfEvaluationSurvey.limit.to
                : 5;
        console.timeEnd('ProgramReportSettings');
        courseSurveyData.data = courseSurveyData.status
            ? await surveyRatingChanges({ userSurveyData: courseSurveyData.data, settingLimit })
            : [];

        const userSurveyData = courseSurveyData.data.filter(
            (surveyElement) => surveyElement._user_id.toString() === userId,
        );
        const otherSurveyData = clone(courseSurveyData.data); /* courseSurveyData.data.filter(
            (surveyElement) => surveyElement._user_id.toString() !== userId,
        ); */
        // User Survey Yet to Rate
        const yetToRate = await courseYetToRate({ courseSessionOrder, userSurveyData });
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
            program_name:
                courseScheduleDatas.data &&
                courseScheduleDatas.data[0] &&
                courseScheduleDatas.data[0].program_name
                    ? courseScheduleDatas.data[0].program_name
                    : courseData.administration.program_name,
            yearNo,
            levelNo,
            term,
            rotationCount,
            yetToRate,
        };
        /* response.setting = [
            {
                name: 'targetBenchmark',
                shortName: '',
                maxValue: 4.3,
                colorCode: '',
                module: 'survey',
            },
            {
                name: 'criticalCutOff',
                shortName: '',
                maxValue: 3.4,
                colorCode: '',
                module: 'survey',
            },
        ]; */
        switch (tab) {
            case SUMMARY:
                response.sessionRating = await selfEvaluationSummary({
                    courseSessionOrder,
                    userSurveyData,
                    otherSurveyData,
                    studentIds,
                });
                break;
            case SLO:
                response.sloRating = await selfEvaluationSlo({
                    courseSessionOrder,
                    userSurveyData,
                    otherSurveyData,
                    studentIds,
                });
                break;
            case CLO:
                {
                    const { courseClo, frameWork } = await courseCLOLists({ courseData });
                    response.frameWork = frameWork;
                    response.cloRating = await selfEvaluationClo({
                        courseClo,
                        userSurveyData,
                        otherSurveyData,
                        studentIds,
                    });
                }
                break;
            case 'outcome':
                response.outcomeAnalysis = await selfEvaluationOutcomeSloWise({
                    courseSessionOrder,
                    userSurveyData,
                    settingLimit,
                });
                break;
            default:
                break;
        }
        logger.info(
            requestedParams,
            'Survey -> selfEvaluation -> Course Self Evaluation Survey %s End',
            tab,
        );
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SELF_EVALUATION_SURVEY'),
                    response,
                ),
            );
    } catch (error) {
        logger.error(
            error,
            'Survey -> selfEvaluation -> Course Self Evaluation Survey %s Error',
            tab,
        );
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.yetToRate = async (req, res) => {
    const {
        params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term },
        query: { rotation, rotationCount },
    } = req;
    try {
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            rotation,
            rotationCount,
        };
        logger.info(
            requestedParams,
            'Survey -> selfEvaluation -> Course Self Evaluation Survey yetToRate start',
        );
        const response = {};

        let courseSessionOrder = await sessionOrder
            .findOne({ ...commonQuery, _course_id: convertToMongoObjectId(courseId) }, {})
            .lean();
        courseSessionOrder =
            courseSessionOrder && courseSessionOrder !== null
                ? courseSessionOrder.session_flow_data
                : [];

        // let courseSessionOrder = (await allSessionOrderDatas()).find(
        //     (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        // ).session_flow_data;
        console.time('courseSchedule');
        const courseScheduleDatas = await get_list(
            courseScheduleCollection,
            {
                isDeleted: false,
                isActive: true,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                year_no: yearNo.toString(),
                level_no: levelNo.toString(),
                term,
                status: { $in: [COMPLETED, MISSED] },
                rotation_count: rotationCount,
                'students._id': convertToMongoObjectId(userId),
                type: REGULAR,
            },
            {
                isActive: 1,
                status: 1,
                'students._id': 1,
                'session._session_id': 1,
            },
        );
        console.timeEnd('courseSchedule');
        if (!courseScheduleDatas.status) courseScheduleDatas.data = [];
        const completedSession = [
            ...new Set(
                courseScheduleDatas.data.map((scheduleElement) =>
                    scheduleElement.session._session_id.toString(),
                ),
            ),
        ];
        courseSessionOrder = courseSessionOrder.filter((sessionElement) =>
            completedSession.find(
                (scheduleElement) => scheduleElement.toString() === sessionElement._id.toString(),
            ),
        );
        console.time('surveyCollection');
        const courseSurveyData = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                _user_id: convertToMongoObjectId(userId),
                rotationCount,
            },
            { _session_order_id: 1, _user_id: 1, slos: 1 },
        );
        console.timeEnd('surveyCollection');
        courseSurveyData.data = courseSurveyData.status ? courseSurveyData.data : [];
        // User Survey Session wise Yet to Rate
        const yetToRate = await courseSessionWiseYetToRate({
            courseSessionOrder,
            userSurveyData: courseSurveyData.data,
        });
        response.yetToRate = yetToRate;
        logger.info(
            requestedParams,
            'Survey -> selfEvaluation -> Course Self Evaluation Survey yetToRate End',
        );
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SELF_EVALUATION_SURVEY'),
                    response,
                ),
            );
    } catch (error) {
        logger.error(
            error,
            'Survey -> selfEvaluation -> Course Self Evaluation Survey yetToRate Error',
        );
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};

exports.comparisonReport = async (req, res) => {
    const {
        params: { institutionCalendarId, programId, userId, courseId, yearNo, levelNo, term, tab },
        query: { rotation, rotationCount },
    } = req;
    try {
        const requestedParams = {
            institutionCalendarId,
            programId,
            userId,
            courseId,
            yearNo,
            levelNo,
            term,
            tab,
            rotation,
            rotationCount,
        };
        logger.info(
            requestedParams,
            'Survey -> comparisonReport -> Course Comparison Report %s start',
            tab,
        );
        const response = {};
        console.time('userCollection');
        const userDetails = await get(
            userCollection,
            { _id: convertToMongoObjectId(userId) },
            { name: 1, user_id: 1 },
        );
        console.timeEnd('userCollection');
        if (userDetails) response.userDetail = userDetails;
        const courseData = (await allCourseList()).find(
            (courseElement) => courseElement._id.toString() === courseId,
        );
        const courseSessionOrder = (await allSessionOrderDatas()).find(
            (sessionOrderElement) => sessionOrderElement._course_id.toString() === courseId,
        ).session_flow_data;
        // console.time('courseSchedule');
        // const courseScheduleDatas = await get_list(
        //     courseScheduleCollection,
        //     {
        //         // isDeleted: false,
        //         _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
        //         _program_id: convertToMongoObjectId(programId),
        //         _course_id: convertToMongoObjectId(courseId),
        //         year_no: yearNo.toString(),
        //         level_no: levelNo.toString(),
        //         // term,
        //         // status: { $in: [COMPLETED, MISSED] },
        //         rotation_count: rotationCount,
        //     },
        //     {
        //         isActive: 1,
        //         status: 1,
        //         'students._id': 1,
        //     },
        // );
        // console.timeEnd('courseSchedule');

        console.time('surveyCollection');
        const courseSurveyData = await get_list(
            surveyCollection,
            {
                isDeleted: false,
                _institution_calendar_id: convertToMongoObjectId(institutionCalendarId),
                _program_id: convertToMongoObjectId(programId),
                _course_id: convertToMongoObjectId(courseId),
                yearNo,
                levelNo,
                term,
                rotationCount,
            },
            { _session_order_id: 1, _user_id: 1, slos: 1 },
        );
        console.timeEnd('surveyCollection');
        courseSurveyData.data = courseSurveyData.status ? courseSurveyData.data : [];
        const userSurveyData = courseSurveyData.data.filter(
            (surveyElement) => surveyElement._user_id.toString() === userId,
        );

        console.time('activityQuestionList');
        const activityList = await activityQuestionList({
            userId,
            courseId,
            term,
            rotationCount,
        });
        console.timeEnd('activityQuestionList');
        // response.activityList = activityList;

        // User Survey Yet to Rate
        const yetToRate = await courseYetToRate({ courseSessionOrder, userSurveyData });
        response.courseData = {
            courseName: courseData.course_name,
            courseCode: courseData.course_code,
            program_name: courseData.administration.program_name,
            yearNo,
            levelNo,
            term,
            rotationCount,
            yetToRate,
        };
        switch (tab) {
            case 'session':
                response.sessionRating = await comparisonReportSummary({
                    userId,
                    courseSessionOrder,
                    userSurveyData,
                    activityList: activityList.status ? activityList.data : [],
                });
                break;
            case SLO:
                response.sloRating = await comparisonReportSlo({
                    userId,
                    courseSessionOrder,
                    userSurveyData,
                    activityList: activityList.status ? activityList.data : [],
                });
                break;
            case CLO:
                {
                    const courseClo = await courseCLOLists({ courseData });
                    response.cloRating = await comparisonReportClo({
                        userId,
                        userSurveyData,
                        activityList: activityList.status ? activityList.data : [],
                        courseClo,
                    });
                }
                break;
            default:
                break;
        }
        logger.info('Survey -> comparisonReport -> Course Comparison Report %s End', tab);
        return res
            .status(200)
            .send(
                responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('COURSE_SELF_EVALUATION_SURVEY'),
                    response,
                ),
            );
    } catch (error) {
        logger.error(error, 'Survey -> comparisonReport -> Course Comparison Report %s Error', tab);
        return res
            .status(500)
            .send(
                responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    req.t('INTERNAL_SERVER_ISSUE'),
                    error.toString(),
                ),
            );
    }
};
