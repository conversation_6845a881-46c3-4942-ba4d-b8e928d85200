const Joi = require('joi');
const {
    objectIdSchema,
    objectIdRQSchema,
    stringSchema,
    numberSchema,
} = require('../../utility/validationSchemas');

exports.typeListValidator = Joi.object({ user_Id: objectIdRQSchema }).unknown(true);

exports.userViewNotificationValidator = Joi.object({
    userId: objectIdRQSchema,
    page: numberSchema,
    limit: numberSchema,
    announcementType: Joi.array().items(stringSchema),
    priorityType: Joi.array().items(stringSchema),
}).unknown(true);

exports.updateViewedNotificationValidator = Joi.object({
    userId: objectIdRQSchema,
    announcementId: Joi.array().items(objectIdRQSchema),
}).unknown(true);

exports.singleAnnouncementValidator = Joi.object({
    announcementId: objectIdRQSchema,
    userId: objectIdSchema,
}).unknown(true);

exports.userIdValidateInQuery = Joi.object({ userId: objectIdRQSchema }).unknown(true);
