let constant = require('../utility/constants');
var day_group = require('mongoose').model(constant.DAY_GROUP);
var institution = require('mongoose').model(constant.INSTITUTION);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const day_group_formate = require('./day_group_formate');
const ObjectId = require('mongodb').ObjectID;

exports.list = async (req, res) => {
    let query = { 'isDeleted': false };
    let project = { _id: 1, name: 1, days: 1, isDeleted: 1, isActive: 1 };
    let doc = await base_control.list(day_group, req.query.limit, req.query.pageNo, query, project);
    if (doc.status) {
        common_files.list_all_response(res, 200, true, "Day group List", doc.totalDoc, doc.totalPages, doc.currentPage, day_group_formate.day_group(doc.data));
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.list_id = async (req, res) => {
    let id = req.params.id;
    let query = { _id: id, 'isDeleted': false };
    let project = { _id: 1, name: 1, days: 1, isDeleted: 1, isActive: 1 };
    let doc = await base_control.get(day_group, query, project);
    if (doc.status) {
        let formated = day_group_formate.day_group_ID(doc.data);
        common_files.com_response(res, 200, true, "Day group List", formated);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.insert = async (req, res) => {
    let query = { 'name': req.body.name, 'isDeleted': false };
    let project = { _id: 1, name: 1, days: 1, isDeleted: 1, isActive: 1 };
    let docs = await base_control.get(day_group, query, project);
    if (!docs.status) {
        let doc = await base_control.insert(day_group, req.body);
        if (doc.status) {
            common_files.com_response(res, 201, true, "Day group Added successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    }
    else {
        common_files.com_response(res, 500, false, "Error duplicate values found ", "This content already present in DB");
    }
};

exports.update = async (req, res) => {
    let object_id = req.params.id;
    let doc = await base_control.update(day_group, object_id, req.body);
    if (doc.status) {
        common_files.com_response(res, 201, true, "Update successfully", doc.data);
    } else {
        common_files.com_response(res, 500, false, "Error", doc.data);
    }
};

exports.delete = async (req, res) => {
    let id = req.params.id;
 
        let doc = await base_control.delete(day_group, id);
        if (doc.status) {
            common_files.com_response(res, 201, true, "Deleted successfully", doc.data);
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
     
};

exports.list_values = async (req, res) => {
    let proj, query = { 'isDeleted': false };
    if (req.body.field != undefined) {
        if (req.body.field[0] != 'all') {
            proj = '{ _id: 1';
            req.body.field.forEach(element => {
                proj = proj + ', ' + element + ' : 1'
            });
            proj = proj + '}';
        } else { proj = {}; }


        let doc = await base_control.get_list(day_group, query, proj);
        if (doc.status) {
            common_files.com_response(res, 200, true, "Day group List", day_group_formate.day_group(doc.data));
        } else {
            common_files.com_response(res, 500, false, "Error", doc.data);
        }
    } else {
        common_files.com_response(res, 500, false, "Error parse field in body", "Error parse field in body");
    }
};