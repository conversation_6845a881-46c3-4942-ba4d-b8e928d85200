const express = require('express');
const route = express.Router();
const credit_hours_individual = require('./credit_hours_individual_controller');
const validater = require('./credit_hours_individual_validator');

//Validater need in all

route.get('/yearlevel/:id', credit_hours_individual.year_level);
route.get('/level/:id', credit_hours_individual.level);
route.post('/list', credit_hours_individual.list_values);
route.get('/:id', validater.credit_hours_individual_id, credit_hours_individual.list_id);
route.get('/', credit_hours_individual.list);
route.post('/', validater.credit_hours_individual, credit_hours_individual.insert);
route.put('/:id', validater.credit_hours_individual_id, validater.credit_hours_individual_update, credit_hours_individual.update);
route.delete('/:id', validater.credit_hours_individual_id, credit_hours_individual.delete);
route.get('/program/:id', validater.credit_hours_individual_id, credit_hours_individual.list_program);

module.exports = route;