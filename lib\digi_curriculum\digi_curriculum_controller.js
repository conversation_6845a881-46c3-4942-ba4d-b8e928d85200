const constant = require('../utility/constants');
// const institution = require('mongoose').model(constant.DIGI_INSTITUTE);
const institution = require('mongoose').model(constant.INSTITUTION);
const digi_curriculum = require('mongoose').model(constant.DIGI_CURRICULUM);
const program = require('mongoose').model(constant.DIGI_PROGRAM);
const digi_course = require('mongoose').model(constant.DIGI_COURSE);
const base_control = require('../base/base_controller');
const common_files = require('../utility/common');
const ObjectId = common_files.convertToMongoObjectId;

const { sendResult, sendErrorResponse } = require('../utility/common');
const { allCurriculumDatas, clearItem } = require('../../service/cache.service');

// Updating Curriculum Flat Caching Data
const updateCurriculumFlatCacheData = async () => {
    clearItem('allCurriculum');
    await allCurriculumDatas();
};

async function insert(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //check duplicate
        const curriculum_data = await base_control.get(
            digi_curriculum,
            {
                curriculum_name: req.body.curriculum_name,
                _program_id: ObjectId(req.body._program_id),
                isDeleted: false,
            },
            { _id: 1 },
        );
        if (curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DUPLICATE_CURRICULUM_NAME'),
                        [],
                    ),
                );

        const credit_arr = [];
        if (req.body.hour_as == 'total' && req.body.value_as == 'fixed_value') {
            if (req.body.credit[0].session_type == 'total' && req.body.credit[0].max > 0)
                credit_arr.push({
                    session_type: req.body.credit[0].session_type,
                    min: 0,
                    max: req.body.credit[0].max,
                });
            else
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('CHECK_CREDIT_HOURS_DATA'),
                            req.t('CHECK_CREDIT_HOURS_DATA'),
                        ),
                    );
        } else if (req.body.hour_as == 'total' && req.body.value_as == 'range_values') {
            if (
                req.body.credit[0].session_type == 'total' &&
                req.body.credit[0].min > 0 &&
                req.body.credit[0].max > 0
            )
                credit_arr.push({
                    session_type: req.body.credit[0].session_type,
                    min: req.body.credit[0].min,
                    max: req.body.credit[0].max,
                });
            else
                return res
                    .status(404)
                    .send(
                        common_files.responseFunctionWithRequest(
                            req,
                            404,
                            false,
                            req.t('CHECK_CREDIT_HOURS_DATA'),
                            req.t('CHECK_CREDIT_HOURS_DATA'),
                        ),
                    );
        } else if (req.body.hour_as == 'split_up' && req.body.value_as == 'fixed_value') {
            req.body.credit.forEach((element) => {
                credit_arr.push({ session_type: element.session_type, min: 0, max: element.max });
            });
            // credit_arr.push({ session_type: req.body.credit[0].session_type, min: 0, max: req.body.credit[0].max })
            // credit_arr.push({ session_type: req.body.credit[1].session_type, min: 0, max: req.body.credit[1].max })
            // credit_arr.push({ session_type: req.body.credit[2].session_type, min: 0, max: req.body.credit[2].max })
            // credit_arr.push({ session_type: req.body.credit[3].session_type, min: 0, max: req.body.credit[3].max })
        } else if (req.body.hour_as == 'split_up' && req.body.value_as == 'range_values') {
            req.body.credit.forEach((element) => {
                credit_arr.push({
                    session_type: element.session_type,
                    min: element.min,
                    max: element.max,
                });
            });
            // credit_arr.push({ session_type: req.body.credit[0].session_type, min: req.body.credit[0].min, max: req.body.credit[0].max })
            // credit_arr.push({ session_type: req.body.credit[1].session_type, min: req.body.credit[1].min, max: req.body.credit[1].max })
            // credit_arr.push({ session_type: req.body.credit[2].session_type, min: req.body.credit[2].min, max: req.body.credit[2].max })
            // credit_arr.push({ session_type: req.body.credit[3].session_type, min: req.body.credit[3].min, max: req.body.credit[3].max })
        }
        //return res.send(credit_arr)

        const obj = {
            _institution_id: req.headers._institution_id,
            _program_id: req.body._program_id,
            program_name: req.body.program_name,
            curriculum_name: req.body.curriculum_name,
            credit_hours: {
                hours_as: req.body.hour_as,
                values_as: req.body.value_as,
                credit: credit_arr,
            },
            _framework_id: req.body._framework_id,
            framework_name: req.body.framework_name,
            start_at: req.body.start_at,
            end_at: req.body.end_at,
            year_level: req.body.year_level,
            // is_pre_requisite: req.body.is_pre_requisite,
            // _pre_requisite_id: req.body._pre_requisite_id != "" ? req.body._pre_requisite_id : undefined,
            // _pre_requisite_name: req.body._pre_requisite_name != "" ? req.body._pre_requisite_name : undefined
        };
        const doc = await base_control.insert(digi_curriculum, obj);
        updateCurriculumFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('CURRICULUM_ADDED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ADD_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function update_curriculum(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //check duplicate
        const curriculum_data = await base_control.get(
            digi_curriculum,
            {
                _id: { $ne: ObjectId(req.params.id) },
                curriculum_name: req.body.curriculum_name,
                _program_id: ObjectId(req.body._program_id),
                isDeleted: false,
            },
            { _id: 1 },
        );
        if (curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('DUPLICATE_CURRICULUM_NAME'),
                        [],
                    ),
                );

        const obj = {
            curriculum_name: req.body.curriculum_name,
            credit_hours: {
                hours_as: req.body.hour_as,
                values_as: req.body.value_as,
                credit: req.body.credit,
            },
            _framework_id: req.body._framework_id,
            framework_name: req.body.framework_name,
            start_at: req.body.start_at,
            end_at: req.body.end_at,
            year_level: req.body.year_level,
            is_pre_requisite: req.body.is_pre_requisite,
            _pre_requisite_id: req.body._pre_requisite_id,
            _pre_requisite_name: req.body._pre_requisite_name,
        };
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(digi_curriculum, query, obj);
        updateCurriculumFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('CURRICULUM_UPDATED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_UPDATE_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}

async function list(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            isActive: true,
        };
        const doc = await base_control.get_list(digi_curriculum, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.response_function(res, 200, true, req.t('CURRICULUM_LIST'), doc.data),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function list_id(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params.id),
        };
        const doc = await base_control.get_list(digi_curriculum, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('CURRICULUM_DETAILS'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function get_curriculum_by_program_id(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _program_id: ObjectId(req.params.program_id), isDeleted: false };
        const doc = await base_control.get_list(digi_curriculum, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, false, 'Error', []));

        // Pre-Requisite Level get
        const curriculum_data = await base_control.get_list(
            digi_curriculum,
            {
                /* _program_id: ObjectId(req.params.program_id), _id: ObjectId(req.params.curriculum_id), */ isDeleted: false,
            },
            { _program_id: 1, year_level: 1 },
        );
        if (!curriculum_data.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('CURRICULUM_NOT_FOUND'),
                        req.t('CURRICULUM_NOT_FOUND'),
                    ),
                );

        //Assigned Course Count
        const course_query = {
            course_assigned_details: { $exists: true, $ne: [] },
            isDeleted: false,
            isActive: true,
        };
        const course_project = {
            _id: 1,
            _curriculum_id: 1,
            course_name: 1,
            course_code: 1,
            duration: 1,
            _program_id: 1,
            course_assigned_details: 1,
        };
        const course_doc = await base_control.get_list(digi_course, course_query, course_project);
        const curriculum_datas = [];
        let year_data = [];
        let level_data = [];
        let course_count = 0;
        let course_datas = [];
        doc.data.forEach((element) => {
            const pre_requisite_course_datas = [];
            year_data = [];
            course_datas = [];
            if (course_doc.status) {
                // course_datas = course_doc.data.filter(items =>
                // (((items._curriculum_id).toString() == (element._id).toString()) &&
                //     ((items._program_id).toString() == (element._program_id).toString())))
                for (course_element of course_doc.data) {
                    if (
                        course_element._program_id.toString() == element._program_id.toString() &&
                        course_element._curriculum_id.toString() == element._id.toString()
                    ) {
                        course_datas.push({
                            ...course_element.toObject(),
                            shared_with_others: false,
                        });
                    } else {
                        for (sub_element of course_element.course_assigned_details) {
                            if (
                                sub_element.course_shared_with.findIndex(
                                    (i) =>
                                        i._program_id.toString() ==
                                            element._program_id.toString() &&
                                        i._curriculum_id.toString() == element._id.toString(),
                                ) != -1
                            )
                                course_datas.push({
                                    ...course_element.toObject(),
                                    shared_with_others: true,
                                });
                        }
                    }
                }
            }
            // return res.send(course_datas)
            element.year_level.forEach((year_element) => {
                let current_loc = -1;
                if (year_element._pre_requisite_id) {
                    current_loc = curriculum_data.data.findIndex(
                        (i) => i._id.toString() == year_element._pre_requisite_id.toString(),
                    );
                    if (current_loc != -1 && course_doc.status)
                        for (course_element of course_doc.data) {
                            if (
                                course_element._curriculum_id.toString() ==
                                year_element._pre_requisite_id.toString()
                            ) {
                                pre_requisite_course_datas.push({
                                    ...course_element.toObject(),
                                    shared_with_others: false,
                                });
                            } else {
                                for (sub_element of course_element.course_assigned_details) {
                                    if (
                                        sub_element.course_shared_with.findIndex(
                                            (i) =>
                                                i._curriculum_id.toString() ==
                                                year_element._pre_requisite_id.toString(),
                                        ) != -1
                                    )
                                        pre_requisite_course_datas.push({
                                            ...course_element.toObject(),
                                            shared_with_others: true,
                                        });
                                }
                            }
                        }
                }
                level_data = [];
                year_element.levels.forEach((level_element) => {
                    course_count = 0;
                    if (current_loc == -1)
                        for (course_element of course_datas) {
                            if (
                                course_element.course_assigned_details.findIndex(
                                    (i) => i._level_id.toString() == level_element._id.toString(),
                                ) != -1
                            )
                                course_count++;
                            for (share_course_element of course_element.course_assigned_details) {
                                if (
                                    share_course_element.course_shared_with.findIndex(
                                        (i) =>
                                            i._level_id.toString() == level_element._id.toString(),
                                    ) != -1
                                )
                                    course_count++;
                            }
                        }
                    else {
                        console.log('Before Count ', course_count);
                        const year_loc = curriculum_data.data[current_loc].year_level.findIndex(
                            (i) => i.y_type == year_element.y_type,
                        );
                        if (year_loc != -1) {
                            const level_loc = curriculum_data.data[current_loc].year_level[
                                year_loc
                            ].levels.findIndex((i) => i.level_name == level_element.level_name);
                            if (level_loc != -1) {
                                const level_no =
                                    curriculum_data.data[current_loc].year_level[year_loc].levels[
                                        level_loc
                                    ];
                                for (course_element of pre_requisite_course_datas) {
                                    if (
                                        course_element.course_assigned_details.findIndex(
                                            (i) =>
                                                i._level_id.toString() == level_no._id.toString(),
                                        ) != -1
                                    )
                                        course_count++;
                                    for (share_course_element of course_element.course_assigned_details) {
                                        if (
                                            share_course_element.course_shared_with.findIndex(
                                                (i) =>
                                                    i._level_id.toString() ==
                                                    level_no._id.toString(),
                                            ) != -1
                                        )
                                            course_count++;
                                    }
                                }
                            }
                        }
                    }
                    level_data.push({
                        _id: level_element._id,
                        level_name: level_element.level_name,
                        start_week: level_element.start_week,
                        end_week: level_element.end_week,
                        course_count,
                    });
                    console.log(level_element.level_name, ' ', course_count);
                });
                const objs = { ...year_element.toObject() };
                objs.levels = level_data;
                year_data.push(objs);
            });
            const objs = { ...element.toObject() };
            objs.year_level = year_data;
            curriculum_datas.push(objs);
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('CURRICULUM_DETAILS'),
                    curriculum_datas,
                ),
            );
    } catch (error) {
        console.error(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function delete_curriculum(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const doc = await base_control.delete(digi_curriculum, ObjectId(req.params.id));
        updateCurriculumFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('CURRICULUM_DELETED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_DELETE_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function archive_curriculum(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const obj = { isActive: req.body.isActive };
        console.log(obj);
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(digi_curriculum, query, obj);
        updateCurriculumFlatCacheData();

        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        201,
                        true,
                        req.t('CURRICULUM_ARCHIVED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('UNABLE_TO_ARCHIVE_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function update_mapping_type(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const obj = { mapping_type: req.body.mapping_type };
        console.log(obj);
        const query = { _id: ObjectId(req.params.id) };
        const doc = await base_control.update(digi_curriculum, query, obj);
        updateCurriculumFlatCacheData();

        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('CURRICULUM_ARCHIVED_SUCCESSFULLY'),
                        doc.data,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    200,
                    false,
                    req.t('UNABLE_TO_ARCHIVE_CURRICULUM'),
                    [],
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function list_archived(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = { _institution_id: ObjectId(req.headers._institution_id), isActive: false };
        const doc = await base_control.get_list(digi_curriculum, query, {});
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('CURRICULUM_ARCHIVED_LIST'),
                    doc.data,
                ),
            );
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
async function update_level(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            _id: ObjectId(req.params.id),
        };

        objs = {
            $set: {
                'year_level.$[y].levels.$[l].level_name': req.body.level_name,
                'year_level.$[y].levels.$[l].start_week': req.body.start_week,
                'year_level.$[y].levels.$[l].end_week': req.body.end_week,
            },
        };
        filter = {
            arrayFilters: [{ 'y._id': req.params.year_id }, { 'l._id': req.params.level_id }],
        };
        console.log(objs, filter);
        const doc = await base_control.update_condition_array_filter(
            digi_curriculum,
            query,
            objs,
            filter,
        );
        updateCurriculumFlatCacheData();
        if (!doc.status)
            return res
                .status(200)
                .send(common_files.response_function(res, 200, false, 'Error', []));
        return res
            .status(200)
            .send(
                common_files.response_function(
                    res,
                    200,
                    true,
                    req.t('LEVEL_UPDATED_SUCCESSFULLY'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

/**
 * Returning Program list based on Type / Institution
 * @param {object} req
 * @param {object} res
 * @returns {Promise<Pre Requisite Program_List along with Curriculum datas>}
 */
async function get_pre_requisite(req, res) {
    try {
        const program_list = await base_control.get_list(
            program,
            {
                _institution_id: ObjectId(req.headers._institution_id),
                program_type: constant.PROGRAM_TYPE.PREREQUISITE,
                isDeleted: false,
            },
            {},
        );
        if (!program_list.status)
            return res
                .status(200)
                .send(common_files.responseFunctionWithRequest(req, 200, true, 'Program List', []));
        const program_ids = program_list.data.map((i) => ObjectId(i._id));
        const doc = await base_control.get_list(
            digi_curriculum,
            {
                _program_id: { $in: program_ids },
                _institution_id: ObjectId(req.headers._institution_id),
                isDeleted: false,
            },
            {},
        );
        const program_data = [];
        program_list.data.forEach((element) => {
            // let year_level = [];
            if (doc.status)
                doc.data.forEach((sub_element) => {
                    if (sub_element._program_id.toString() == element._id.toString())
                        program_data.push(sub_element);
                });
            // else
            //     program_data.push(element);
            // if (doc.data.findIndex(i => (i._program_id).toString() == (element._id).toString()) != -1) {
            //     year_level = doc.data[doc.data.findIndex(i => (i._program_id).toString() == (element._id).toString())].year_level;
            //     program_data.push({ ...element.toObject(), year_level });
            // }
        });
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    true,
                    req.t('PROGRAM_LIST'),
                    program_data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
async function filter_year_program_by_curriculum(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const query = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
            isActive: true,
        };
        const doc = await base_control.get_list(digi_curriculum, query, {
            _id: 1,
            start_at: 1,
            end_at: 1,
            _program_id: 1,
            program_name: 1,
            is_pre_requisite: 1,
        });
        if (!doc.status)
            return res
                .status(410)
                .send(common_files.response_function(res, 410, false, 'Error', []));

        let obj = {};
        const curriculum = [];
        for (data of doc.data) {
            obj = data._doc;
            obj.year_program = obj.end_at + 1 - obj.start_at;
            curriculum.push(obj);
        }
        const group_year_program = {};

        for (data of curriculum) {
            if (!group_year_program[data.year_program]) {
                group_year_program[data.year_program] = [];
                group_year_program[data.year_program].push(data);
            } else group_year_program[data.year_program].push(data);
        }

        return res.send(group_year_program);
    } catch (error) {
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}

async function upsert_standard_range_settings(req, res) {
    const { curriculum_id, setting_id, standard_range_setting } = req.body;
    digi_curriculum.findOne({ _id: curriculum_id }, function (err, curriculum) {
        if (err) sendErrorResponse(res, 500, serverError, err);
        const setting = curriculum.standard_range_settings.id(setting_id);
        if (setting) {
            setting.set(standard_range_setting);
            curriculum.save();
            sendResult(res, 200, curriculum);
        } else {
            curriculum.standard_range_settings.push(standard_range_setting);
            curriculum.save(function (err, insertResult) {
                if (err) sendErrorResponse(res, 500, serverError, err);
                sendResult(res, 200, insertResult);
            });
        }
    });
}
async function validate_data_check_curriculum(
    req,
    res,
    data,
    collection_datas,
    duplicate_in_excel,
) {
    const message = [];
    const obj = {};
    const { program_data, curriculum_data } = collection_datas;
    let pgm_data = '';

    //Excel Duplicate
    if (duplicate_in_excel) message.push(req.t('DUPLICATE_IN_EXCEL'));

    //Program Check
    const program_name_ind = program_data.data.findIndex(
        (ele) =>
            ele.name.toLowerCase() == data.Program_Name.trim().toLowerCase() &&
            ele.code.toLowerCase() == data.Program_Code.trim().toLowerCase(),
    );
    if (program_name_ind == -1) message.push(req.t('CHECK_PROGRAM_NAME'));
    else {
        obj.program_id = program_data.data[program_name_ind]._id;
        obj.program_name = program_data.data[program_name_ind].name;
        pgm_data = program_data.data[program_name_ind];

        //Curriculum Check
        if (curriculum_data.status) {
            const curriculum_ind = curriculum_data.data.findIndex(
                (ele) =>
                    ele.curriculum_name.toLowerCase() ==
                        data.Curriculum_Name.trim().toLowerCase() &&
                    ele._program_id.toString() ==
                        program_data.data[program_name_ind]._id.toString(),
            );
            if (curriculum_ind != -1) message.push(req.t('CHECK_CURRICULUM_NAME_ALREADY_EXIST'));
        }
        //Hours As and Set Value As
        const hours_as_arr = ['total', 'split_up'];
        const set_value_as_arr = ['fixed_value', 'range_of_values'];
        const hour_as_check = hours_as_arr.includes(data.Hour_As.trim().toLowerCase());
        const set_value_as_check = set_value_as_arr.includes(
            data.Set_Value_As.trim().toLowerCase(),
        );
        if (!hour_as_check) message.push(req.t('CHECK_HOUR_AS'));
        if (!set_value_as_check) message.push(req.t('CHECK_SET_VALUE_AS'));

        //Program Duration check
        if (!Number.isInteger(data.Program_Duration_Start_At))
            message.push(req.t('CHECK_PROGRAM_DURATION_START_AT_MUST_BE_INTEGER'));

        if (!Number.isInteger(data.Program_Duration_End_At))
            message.push(req.t('CHECK_PROGRAM_DURATION_END_AT_MUST_BE_INTEGER'));
    }
    return { message, data: obj, other_datas: { pgm_data } }; //Message for data check api   //Remaining datas for Import api
}
const checkDuplicateCurriculumInExcel = function (curriculum_excel_data, curriculum_name) {
    const curriculum_name_arr = curriculum_excel_data.map((ele) =>
        ele.Curriculum_Name.trim().toLowerCase(),
    );
    let flag = 0;
    const first_ind = curriculum_name_arr.indexOf(curriculum_name.trim().toLowerCase());
    const last_ind = curriculum_name_arr.lastIndexOf(curriculum_name.trim().toLowerCase());
    if (first_ind != last_ind) flag = 1;
    return flag;
};
const field_empty_validation = function (import_data, optional_field) {
    const validation_data = [];
    for (data of import_data) {
        const message = [];
        for (const key in data) {
            if (optional_field.includes(key)) continue;
            if (data[key].toString() == '') message.push(key + ' is required');
        }
        if (message.length > 0) validation_data.push({ data, message });
    }
    if (validation_data.length == 0) return { status: false, message: validation_data };
    return { status: true, message: validation_data };
};
async function data_check_curriculum(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );

        //Program List
        const query_program = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const program_data = await base_control.get_list(program, query_program, {});
        if (!program_data.status)
            return res
                .status(409)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        409,
                        false,
                        req.t('PROGRAM_NOT_FOUND'),
                        [],
                    ),
                );

        // Curriculum List
        const query_curriculum = {
            _institution_id: ObjectId(req.headers._institution_id),
            isDeleted: false,
        };
        const curriculum_data = await base_control.get_list(digi_curriculum, query_curriculum, {});
        // if (!curriculum_data.status)
        //     return res
        //         .status(409)
        //         .send(common_files.response_function(res, 409, false, 'Curriculum not found', []));

        const collection_datas = { program_data, curriculum_data };

        //Empty Validation
        const optional_field = [];
        const empty_validation_check = field_empty_validation(req.body.curriculum, optional_field);
        if (empty_validation_check.status)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('REQUIRED_FIELD_VALIDATION_FAILED'),
                        { invalid_data: empty_validation_check.message },
                    ),
                );

        const valid_data = [];
        const invalid_data = [];
        for (data of req.body.curriculum) {
            const excel_dup_status = checkDuplicateCurriculumInExcel(
                req.body.curriculum,
                data.Curriculum_Name,
            );
            const session_flow_duplicate_status = excel_dup_status == 1;
            if (session_flow_duplicate_status) {
                //Duplicate in Excel
                validation = await validate_data_check_curriculum(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = true),
                );
            } else {
                //No Duplicate in Excel
                validation = await validate_data_check_curriculum(
                    req,
                    res,
                    data,
                    collection_datas,
                    (duplicate_in_excel = false),
                );
            }
            if (validation.message.length > 0)
                invalid_data.push({ data, message: validation.message });
            else valid_data.push({ data, message: validation.message });

            //return res.send({ course_code_dup_status, course_name_dup_status })
        }
        const d = { valid_data, invalid_data };
        if (invalid_data.length > 0)
            return res
                .status(200)
                .send(
                    common_files.responseFunctionWithRequest(
                        req,
                        200,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        d,
                    ),
                );
        return res
            .status(200)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    200,
                    false,
                    req.t('DATA_CHECK_VALIDATION_SUCCESS'),
                    d,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(
                common_files.responseFunctionWithRequest(
                    req,
                    500,
                    false,
                    'Error Catch',
                    error.toString(),
                ),
            );
    }
}
const local_data_check_curriculum = async function (req, res) {
    const institution_check = await base_control.get(
        institution,
        { _id: ObjectId(req.headers._institution_id) },
        { _id: 1 },
    );
    if (!institution_check.status)
        return res
            .status(404)
            .send(
                common_files.response_function(
                    res,
                    404,
                    false,
                    req.t('INSTITUTION_NOT_FOUND'),
                    req.t('INSTITUTION_NOT_FOUND'),
                ),
            );

    const valid_data = [];
    const invalid_data = [];
    const import_data = [];
    let pgm = [];
    const collections_error_message = [];

    //Program List
    const query_program = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const program_data = await base_control.get_list(program, query_program, {});
    if (!program_data.status) collections_error_message.push(req.t('PROGRAM_NOT_FOUND'));

    // Curriculum List
    const query_curriculum = {
        _institution_id: ObjectId(req.headers._institution_id),
        isDeleted: false,
    };
    const curriculum_data = await base_control.get_list(digi_curriculum, query_curriculum, {});
    // if (!curriculum_data.status) collections_error_message.push('Curriculum not found');

    if (collections_error_message.length > 0)
        return { status: false, data: {}, message: req.t('COLLECTION_DATA_NOT_FOUND') };

    const collection_datas = { program_data, curriculum_data };
    for (data of req.body.curriculum) {
        const excel_dup_status = checkDuplicateCurriculumInExcel(
            req.body.curriculum,
            data.Curriculum_Name,
        );
        const curriculum_duplicate_status = excel_dup_status == 1;
        if (curriculum_duplicate_status) {
            //Duplicate in Excel
            validation = await validate_data_check_curriculum(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = true),
            );
        } else {
            //No Duplicate in Excel
            validation = await validate_data_check_curriculum(
                req,
                res,
                data,
                collection_datas,
                (duplicate_in_excel = false),
            );
        }
        if (validation.message.length > 0) invalid_data.push({ data, message: validation.message });
        else valid_data.push({ data, message: validation.message });

        import_data.push(validation.data);
        pgm = validation.other_datas.pgm_data;
    }
    const d = {
        valid_data,
        invalid_data,
        other_error_message: collections_error_message,
        import_data,
        other_datas: { pgm },
    };
    if (invalid_data.length > 0) return { status: false, data: d };
    return { status: true, data: d };
};
async function import_curriculum(req, res) {
    try {
        const institution_check = await base_control.get(
            institution,
            { _id: ObjectId(req.headers._institution_id) },
            { _id: 1 },
        );
        if (!institution_check.status)
            return res
                .status(404)
                .send(
                    common_files.response_function(
                        res,
                        404,
                        false,
                        req.t('INSTITUTION_NOT_FOUND'),
                        req.t('INSTITUTION_NOT_FOUND'),
                    ),
                );
        const data_check = await local_data_check_curriculum(req, res);
        if (!data_check.status)
            return res
                .status(410)
                .send(
                    common_files.response_function(
                        res,
                        500,
                        false,
                        req.t('DATA_CHECK_VALIDATION_FAILED'),
                        data_check.data,
                    ),
                );
        const pgm = data_check.data.other_datas.pgm;
        let obj = {};
        const curriculum_arr_obj = [];
        for (let i = 0; i < req.body.curriculum.length; i++) {
            obj = {
                insertOne: {
                    document: {
                        _institution_id: req.headers._institution_id,
                        _program_id: pgm._id,
                        program_name: pgm.name,
                        curriculum_name: req.body.curriculum[i].Curriculum_Name.trim(),
                        credit_hours: {
                            hours_as: req.body.curriculum[i].Hour_As.trim(),
                            values_as: req.body.curriculum[i].Set_Value_As.trim(),
                        },
                        start_at: req.body.curriculum[i].Program_Duration_Start_At,
                        end_at: req.body.curriculum[i].Program_Duration_End_At,
                    },
                },
            };
            curriculum_arr_obj.push(obj);
        }
        // return res.send(curriculum_arr_obj);
        const doc = await base_control.bulk_write(digi_curriculum, curriculum_arr_obj);
        updateCurriculumFlatCacheData();
        if (doc.status)
            return res
                .status(201)
                .send(
                    common_files.response_function(
                        res,
                        201,
                        true,
                        req.t('CURRICULUM_IMPORTED_SUCCESSFULLY'),
                        doc,
                    ),
                );
        return res
            .status(410)
            .send(
                common_files.response_function(
                    res,
                    410,
                    false,
                    req.t('UNABLE_TO_IMPORT_CURRICULUM'),
                    doc.data,
                ),
            );
    } catch (error) {
        console.log(error);
        return res
            .status(500)
            .send(common_files.response_function(res, 500, false, 'Error Catch', error.toString()));
    }
}
module.exports = {
    insert,
    update_curriculum,
    list,
    list_id,
    delete_curriculum,
    archive_curriculum,
    update_mapping_type,
    list_archived,
    update_level,
    get_curriculum_by_program_id,
    get_pre_requisite,
    filter_year_program_by_curriculum,
    upsert_standard_range_settings,
    import_curriculum,
    data_check_curriculum,
};
