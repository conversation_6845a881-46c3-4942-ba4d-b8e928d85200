const router = require('express').Router();
const catchAsync = require('../../utility/catch-async');
const {
    getCreateForm,
    createNewForm,
    updateCreateNewForm,
    singleFormList,
    formAttachment,
    getFormAttachment,
    updateFormStatus,
    settingTag,
    referenceDocumentAcademicYear,
    conclusionPhase,
    searchReferenceDocument,
    qapcLogUserList,
    referenceFormAttachment,
    getCategoryAndForm,
    formProgramList,
    createIncorporate,
    getIncorporateSection,
    getTermAndAttempt,
    getIncorporate,
    getTodoMissed,
    getCategoryList,
    getFormViewAttachment,
    searchDocumentList,
    createDuplicateForm,
    toDoCreatedFormList,
    publishedFormInitiator,
    selectedFormList,
    formInitiatedAttachments,
} = require('./formInitiator.controller');

//qapc log user list
router.get('/qapcLogUserList', catchAsync(qapcLogUserList));
router.get('/getCreateForm', catchAsync(getCreateForm));
router.get('/getFormViewAttachment', catchAsync(getFormViewAttachment));
//createNewForm
router.post('/createNewForm', catchAsync(createNewForm));
router.put('/updateCreateNewForm', catchAsync(updateCreateNewForm));
router.get('/singleFormList', catchAsync(singleFormList));
//step 2 form attachment
router.put('/formAttachment', catchAsync(formAttachment));
router.get('/getFormAttachment', catchAsync(getFormAttachment));
//update delete & archive
router.put('/updateFormStatus', catchAsync(updateFormStatus));
router.get('/settingTag', catchAsync(settingTag));
//reference Document
router.get('/referenceDocumentAcademicYear', catchAsync(referenceDocumentAcademicYear));
router.get('/conclusionPhase', catchAsync(conclusionPhase));
router.get('/searchReferenceDocument', catchAsync(searchReferenceDocument));
router.get('/searchDocumentList', catchAsync(searchDocumentList));
router.get('/referenceFormAttachment', catchAsync(referenceFormAttachment));
//incorporate sections
router.get('/getCategoryAndForm', catchAsync(getCategoryAndForm));
router.get('/getTermAndAttempt', catchAsync(getTermAndAttempt));
router.get('/formProgramList', catchAsync(formProgramList));
router.get('/getIncorporateSection', catchAsync(getIncorporateSection));
router.put('/createIncorporate', catchAsync(createIncorporate));
router.get('/getIncorporate', catchAsync(getIncorporate));
//missed todo list
router.get('/todoMissed', catchAsync(getTodoMissed));
//category list
router.get('/categoryList', catchAsync(getCategoryList));
//create duplicate
router.get('/createDuplicateForm', catchAsync(createDuplicateForm));
router.get('/toDoCreatedFormList', catchAsync(toDoCreatedFormList));
//HABA
router.get('/publishedFormInitiator', catchAsync(publishedFormInitiator));
router.get('/selectedFormList', catchAsync(selectedFormList));
router.get('/formInitiatedAttachments', catchAsync(formInitiatedAttachments));
module.exports = router;
